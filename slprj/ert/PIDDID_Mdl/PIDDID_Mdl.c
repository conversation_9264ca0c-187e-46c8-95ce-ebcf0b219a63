/*
 * File: PIDDID_Mdl.c
 *
 * Code generated for Simulink model 'PIDDID_Mdl'.
 *
 * Model version                  : 1.414
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:35:29 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "PIDDID_Mdl.h"
#include "StateMach_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "rtwtypes.h"
#include "RoofSys_CommDefines.h"
#include "RoofOper_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "irs_std_types.h"
#include "ICMon.h"
#include "PIDDID_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "UsgHist_ExpTypes.h"
#include "PIDDID_Mdl_types.h"
#include "PIDDID_Mdl_private.h"
#include "PIDDID_Exp.h"
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "UsgHist_Exp.h"
#include "BlockDet_Mdl_Exp.h"
#include "zero_crossing_types.h"

/* Named constants for Chart: '<S2>/AutoCycle' */
#define PIDDID_Mdl_IN_AutoCycle        ((uint8_T)1U)
#define PIDDID_Mdl_IN_GoToFullClose    ((uint8_T)1U)
#define PIDDID_Mdl_IN_GoToFullOpen     ((uint8_T)2U)
#define PIDDID_Mdl_IN_Init             ((uint8_T)2U)
#define PIDDID_Mdl_IN_NO_ACTIVE_CHILD  ((uint8_T)0U)
#define PIDDID_Mdl_IN_StopMovement     ((uint8_T)3U)
#define PIDDID_Mdl_IN_WaitAfterCloseDuringRoutine ((uint8_T)3U)
#define PIDDID_Mdl_IN_WaitAfterOpenDuringRoutine ((uint8_T)4U)

/* Named constants for Chart: '<S2>/ClearRF_Routine' */
#define PIDDID_Mdl_IN_Init_k           ((uint8_T)1U)
#define PIDDID_Mdl_IN_request_running  ((uint8_T)2U)
#define PIDDID_Mdl_IN_stopped          ((uint8_T)3U)

/* Named constants for Chart: '<S2>/Goto_Target_Pos' */
#define PIDDID_Mdl_IN_RequestRun       ((uint8_T)2U)

/* Named constants for Chart: '<S2>/HallOutput_Routine' */
#define PIDDID_Mdl_IN_DisableHallOutputCurrentPwrCycle ((uint8_T)1U)

/* Named constants for Chart: '<S2>/IO_TEST_ROUTINE' */
#define PIDDID_Mdl_IN_RequestRun_j     ((uint8_T)1U)
#define PIDDID_Mdl_IN_Step1            ((uint8_T)1U)
#define PIDDID_Mdl_IN_Step2            ((uint8_T)2U)
#define PIDDID_Mdl_IN_Step3            ((uint8_T)3U)
#define PIDDID_Mdl_IN_Step4            ((uint8_T)4U)
#define PIDDID_Mdl_IN_init             ((uint8_T)2U)

/* Named constants for Chart: '<S2>/OverridePWM_Routine' */
#define PIDDID_Mdl_IN_Initilization    ((uint8_T)1U)
#define PIDDID_Mdl_IN_RequestRunentry_ ((uint8_T)2U)

/* Named constants for Chart: '<S2>/Roof_Control_Routine' */
#define PIDDID_Mdl_IN_RequestRun_Closing ((uint8_T)2U)
#define PIDDID_Mdl_IN_RequestRun_Opening ((uint8_T)3U)

/* Named constants for Chart: '<S2>/Run_Motor_Continuous_Routine' */
#define PIDDID_Mdl_IN_RequestRun_CCW   ((uint8_T)2U)

MdlrefDW_PIDDID_Mdl_T PIDDID_Mdl_MdlrefDW;

/* Block signals (default storage) */
B_PIDDID_Mdl_c_T PIDDID_Mdl_B;

/* Block states (default storage) */
DW_PIDDID_Mdl_f_T PIDDID_Mdl_DW;

/* Previous zero-crossings (trigger) states */
ZCE_PIDDID_Mdl_T PIDDID_Mdl_PrevZCX;

/* System initialize for referenced model: 'PIDDID_Mdl' */
void PIDDID_Mdl_Init(void)
{
  /* InitializeConditions for UnitDelay: '<S16>/Unit Delay' */
  PIDDID_Mdl_DW.UnitDelay_DSTATE = true;

  /* SystemInitialize for Triggered SubSystem: '<S16>/NVMWriteForShutdown' */
  /* SystemInitialize for S-Function (NVMMan_enSaveAllNVMData): '<S25>/S-Function1' incorporates:
   *  Outport: '<S25>/isSoftStartParWriteComplete'
   */
  PIDDID_Mdl_B.stsGoToShtdwnReady = SCU_STATUS_ERROR;

  /* End of SystemInitialize for SubSystem: '<S16>/NVMWriteForShutdown' */
}

/* Disable for referenced model: 'PIDDID_Mdl' */
void PIDDID_Mdl_Disable(void)
{
  /* Disable for Enabled SubSystem: '<S2>/NVMClear' */
  if (PIDDID_Mdl_DW.NVMClear_MODE) {
    /* Disable for RelationalOperator: '<S15>/Equal' incorporates:
     *  Outport: '<S15>/NVMClearStatus'
     */
    PIDDID_Mdl_B.Equal = false;
    PIDDID_Mdl_DW.NVMClear_MODE = false;
  }

  /* End of Disable for SubSystem: '<S2>/NVMClear' */
}

/* Output and update for referenced model: 'PIDDID_Mdl' */
void PIDDID_Mdl(const StateMach_Mode_t
                *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, const
                ThermProt_MtrTempClass_t
                *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass,
                const boolean_T
                *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_isStartupRdy,
                const ThermProt_MosfetTempClass_t
                *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass,
                const MtrCtrl_MtrDir_t
                *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir, const
                uint8_T *rtu_SWC_OperLogLyrBus_MtrCtrlBus_DTC_MTR_Dir, const
                boolean_T
                rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_FeedbackStatus[2],
                const boolean_T
                *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_isMtrMoving, const
                PnlOp_ATS_t *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_stATS, const
                CtrlLog_RelearnMode_t
                *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode, const
                CtrlLog_tenTargetDirection
                *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const
                boolean_T
                *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isVBATMeasEnbPinStatus,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallOutPinStatus,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankAPinStatus,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankBPinStatus,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens1PinStatus,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens2PinStatus,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isINTPinStatus, const
                uint16_T *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_u12VBattInst,
                const int16_T *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TAmbTemp,
                const int16_T *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TMOSFETTemp,
                const uint16_T
                *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uHallPwrInst, const
                uint16_T *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1AVolt_mv,
                const uint16_T *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1BVolt_mv,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isMtrFebkCtrlHS1Status,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isHallSuppHS2Status, const
                boolean_T
                *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isSwtchSuppHS3Status,
                const uint8_T
                *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_u8GetPWMDutyCycleVal,
                const HallDeco_Dir_t
                *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_DMtrDir, const
                uint16_T *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd,
                const uint8_T *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1,
                const uint8_T *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HallMtrOptDir,
                const boolean_T
                *rtu_SWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault, const
                LearnAdap_Mode_t
                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode, const
                LearnAdap_Req_t
                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq, const
                uint16_T
                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop, const
                boolean_T
                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete,
                const boolean_T
                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearningAllowed,
                const uint16_T
                *rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_hStallPos, const
                BlockDet_Stall_dir
                *rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallDir, const
                int16_T *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_TEstMtrTemp,
                const uint16_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt,
                const VoltMon_VoltClass_t
                *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass, const
                boolean_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet,
                const boolean_T
                *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const
                uint16_T *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, const
                uint16_T
                *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls,
                const uint16_T
                *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn,
                const int16_T
                *rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp,
                const int16_T
                *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const
                boolean_T
                *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld,
                const uint16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_nCycle,
                const int16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MaxAmbTemp,
                const int16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MinAmbTemp,
                const uint16_T
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MaxBattVtg, const
                uint16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MinBattVtg,
                const uint32_T
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MotorOperTime, const
                uint16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_CycRenormCount,
                const uint8_T
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_LearnSuccessCount, const
                uint8_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_LeanrFailCount,
                const uint8_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_StallCount,
                const uint16_T
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalCount, const
                uint16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_SlideCycCount,
                const uint8_T
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ThermProtCount, const
                uint8_T
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReprogrammingCount,
                const uint16_T
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalPos, const
                CtrlLog_tenTargetDirection
                *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalDir, const
                uint16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalThres,
                const VehCom_Cmd_t *rtu_SWC_CommLyrBus_VehComBus_VehCom_stVehCmd,
                const uint8_T *rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn,
                int16_T *rty_PIDDIDBus_MtrMdlBus_MtrMdl_TEstMtrTemp,
                LearnAdap_Mode_t *rty_PIDDIDBus_LearnAdap_LearnAdap_stMode,
                boolean_T *rty_PIDDIDBus_LearnAdap_LearnAdap_isLearnComplete,
                boolean_T *rty_PIDDIDBus_LearnAdap_LearnAdap_isLearningAllowed,
                uint16_T *rty_PIDDIDBus_BlockDet_BlockDet_hStallPos,
                BlockDet_Stall_dir *rty_PIDDIDBus_BlockDet_BlockDet_stStallDir,
                int16_T *rty_PIDDIDBus_AmbTemp_AmbTempMon_TAmbTemp, boolean_T
                *rty_PIDDIDBus_AmbTemp_AmbTempMon_isAmbTempVld, boolean_T
                *rty_PIDDIDBus_PosMon_PosMon_isCurPosVld, uint16_T
                *rty_PIDDIDBus_PosMon_PosMon_hCurPos, VoltMon_VoltClass_t
                *rty_PIDDIDBus_VoltMon_VoltMon_stVoltClass, boolean_T
                *rty_PIDDIDBus_VoltMon_VoltMon_isFlctnDet, uint16_T
                *rty_PIDDIDBus_VoltMon_VoltMon_u12VBatt, int16_T
                *rty_PIDDIDBus_MOSFETTempBus_MOSFETTempMon_TMosTemp,
                ThermProt_MtrTempClass_t
                *rty_PIDDIDBus_ThermProt_ThermProt_stMtrTempClass, boolean_T
                *rty_PIDDIDBus_ThermProt_ThermProt_isStartupRdy,
                ThermProt_MosfetTempClass_t
                *rty_PIDDIDBus_ThermProt_ThermProt_stMosfetTempClass,
                PnlOp_ATS_t *rty_PIDDIDBus_PnlOp_PnlOp_stATS, boolean_T
                *rty_PIDDIDBus_PnlOp_PnlOp_isMtrMoving, MtrCtrl_MtrDir_t
                *rty_PIDDIDBus_MtrCtrl_IoHwAb_SetMtrDir, boolean_T
                rty_PIDDIDBus_MtrCtrl_MtrCtrl_FeedbackStatus[2],
                CtrlLog_tenTargetDirection
                *rty_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand,
                CtrlLog_RelearnMode_t
                *rty_PIDDIDBus_CtrlLog_CtrlLog_stRelearnMode, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_nCycle, int16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_MaxAmbTemp, int16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_MinAmbTemp, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_MaxBattVtg, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_MinBattVtg, uint32_T
                *rty_PIDDIDBus_UsgHist_UsgHist_MotorOperTime, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_CycRenormCount, uint8_T
                *rty_PIDDIDBus_UsgHist_UsgHist_LearnSuccessCount, uint8_T
                *rty_PIDDIDBus_UsgHist_UsgHist_LeanrFailCount, uint8_T
                *rty_PIDDIDBus_UsgHist_UsgHist_StallCount, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_ReversalCount, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_SlideCycCount, uint8_T
                *rty_PIDDIDBus_UsgHist_UsgHist_ThermProtCount, uint8_T
                *rty_PIDDIDBus_UsgHist_UsgHist_ReprogrammingCount, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_ReversalPos,
                CtrlLog_tenTargetDirection
                *rty_PIDDIDBus_UsgHist_UsgHist_ReversalDir, uint16_T
                *rty_PIDDIDBus_UsgHist_UsgHist_ReversalThres, HallDeco_Dir_t
                *rty_PIDDIDBus_HallDeco_HallDeco_DMtrDir, uint8_T
                *rty_PIDDIDBus_HallDeco_DTC_HALL_Hall1, uint16_T
                *rty_PIDDIDBus_HallDeco_HallDeco_rMtrSpd, boolean_T
                *rty_PIDDIDBus_HallDeco_isHallSupplyFault, uint16_T
                *rty_PIDDIDBus_ADCMon_ADCMon_u12VBattInst, int16_T
                *rty_PIDDIDBus_ADCMon_ADCMon_TAmbTemp, int16_T
                *rty_PIDDIDBus_ADCMon_ADCMon_TMOSFETTemp, uint16_T
                *rty_PIDDIDBus_ADCMon_ADCMon_uHallPwrInst, uint16_T
                *rty_PIDDIDBus_ADCMon_ADCMon_uM1AVolt_mv, uint16_T
                *rty_PIDDIDBus_ADCMon_ADCMon_uM1BVolt_mv, StateMach_Mode_t
                *rty_PIDDIDBus_StateMach_StateMach_stSysMode, boolean_T
                *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isVBATMeasEnbPinStatus, boolean_T
                *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallOutPinStatus, boolean_T
                *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankAPinStatus,
                boolean_T
                *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankBPinStatus,
                boolean_T *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens1PinStatus,
                boolean_T *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens2PinStatus,
                boolean_T *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isINTPinStatus,
                boolean_T *rty_PIDDIDBus_ExtDev_ExtDev_isMtrFebkCtrlHS1Status,
                boolean_T *rty_PIDDIDBus_ExtDev_ExtDev_isHallSuppHS2Status,
                boolean_T *rty_PIDDIDBus_ExtDev_ExtDev_isSwtchSuppHS3Status,
                uint8_T *rty_PIDDIDBus_ExtDev_ExtDev_u8GetPWMDutyCycleVal)
{
  int32_T q0;
  uint32_T qY;
  uint16_T c_x;
  uint16_T c_z;
  CtrlLog_RelearnMode_t tmp;
  boolean_T b_out_tmp;
  boolean_T guard1;
  boolean_T guard2;
  boolean_T rtb_LogicalOperator;

  /* Chart: '<S2>/Learning_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read10'
   *  DataStoreWrite: '<S2>/Data Store Write20'
   */
  PIDDID_Mdl_DW.VehCom_stVehCmd_prev = PIDDID_Mdl_DW.VehCom_stVehCmd_start;
  PIDDID_Mdl_DW.VehCom_stVehCmd_start =
    *rtu_SWC_CommLyrBus_VehComBus_VehCom_stVehCmd;
  if (PIDDID_Mdl_DW.is_active_c4_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.VehCom_stVehCmd_prev =
      *rtu_SWC_CommLyrBus_VehComBus_VehCom_stVehCmd;
    PIDDID_Mdl_DW.is_active_c4_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c4_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;

    /*  First initialization of the Routine */
    PIDDID_isRtnBusy = false;
  } else if (PIDDID_Mdl_DW.is_c4_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    PIDDID_isRtnBusy = false;
    if ((PIDDID_stDiagRtnCmd == VehCom_Cmd_t_Learn_C) || ((PIDDID_stDiagRtnCmd ==
          VehCom_Cmd_t_Learn_WOPreCond_C) &&
         (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode ==
          CtrlLog_RelearnMode_t_Idle_C))) {
      PIDDID_Mdl_DW.is_c4_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRun;
      PIDDID_isRtnBusy = true;
      PIDDID_stRoutineResult_LearnRtn =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }
  } else {
    /* case IN_RequestRun: */
    PIDDID_isRtnBusy = true;
    tmp = *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode;
    guard1 = false;
    if ((tmp == CtrlLog_RelearnMode_t_Idle_C) &&
        (*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete)) {
      PIDDID_stRoutineResult_LearnRtn =
        PIDDID_RoutineStatus_t_Routine_Successful_C;
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
      guard1 = true;
    } else if (PIDDID_stDiagRtnCmd == VehCom_Cmd_t_Stop_C) {
      PIDDID_stRoutineResult_LearnRtn =
        PIDDID_RoutineStatus_t_Routine_Successful_C;
      guard1 = true;
    } else if (((tmp == CtrlLog_RelearnMode_t_Idle_C) &&
                (!*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete))
               || ((PIDDID_Mdl_DW.VehCom_stVehCmd_prev !=
                    PIDDID_Mdl_DW.VehCom_stVehCmd_start) &&
                   (*rtu_SWC_CommLyrBus_VehComBus_VehCom_stVehCmd !=
                    VehCom_Cmd_t_None_C) &&
                   (*rtu_SWC_CommLyrBus_VehComBus_VehCom_stVehCmd !=
                    VehCom_Cmd_t_Invalid_C))) {
      PIDDID_stRoutineResult_LearnRtn =
        PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
      guard1 = true;
    }

    if (guard1) {
      PIDDID_Mdl_DW.is_c4_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;

      /*  First initialization of the Routine */
      PIDDID_isRtnBusy = false;
    }
  }

  /* End of Chart: '<S2>/Learning_Routine' */

  /* Chart: '<S2>/Roof_Control_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read7'
   *  DataStoreWrite: '<S2>/Data Store Write16'
   */
  if (PIDDID_Mdl_DW.temporalCounter_i1 < MAX_uint32_T) {
    PIDDID_Mdl_DW.temporalCounter_i1++;
  }

  if (PIDDID_Mdl_DW.is_active_c7_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c7_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c7_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;
    PIDDID_isRoofCtrlRtnBusy = false;
  } else {
    guard1 = false;
    guard2 = false;
    switch (PIDDID_Mdl_DW.is_c7_PIDDID_Mdl) {
     case PIDDID_Mdl_IN_Initilization:
      PIDDID_isRoofCtrlRtnBusy = false;
      b_out_tmp = ((*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete)
                   && (*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld));
      rtb_LogicalOperator = (b_out_tmp &&
        (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn < 99) &&
        (PIDDID_stDiagRtnCmd == VehCom_Cmd_t_ManOpn_C));
      if (rtb_LogicalOperator) {
        PIDDID_Mdl_DW.is_c7_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRun_Opening;
        PIDDID_Mdl_DW.temporalCounter_i1 = 0U;
        PIDDID_isRoofCtrlRtnBusy = true;
        PIDDID_stRoutineResult_RoofCtrlrtn =
          PIDDID_RoutineStatus_t_Routine_InProgress_C;
      } else {
        rtb_LogicalOperator = (b_out_tmp &&
          (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn > 1) &&
          (PIDDID_stDiagRtnCmd == VehCom_Cmd_t_ManCls_C));
        if (rtb_LogicalOperator) {
          PIDDID_Mdl_DW.is_c7_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRun_Closing;
          PIDDID_Mdl_DW.temporalCounter_i1 = 0U;
          PIDDID_isRoofCtrlRtnBusy = true;
          PIDDID_stRoutineResult_RoofCtrlrtn =
            PIDDID_RoutineStatus_t_Routine_InProgress_C;
        }
      }
      break;

     case PIDDID_Mdl_IN_RequestRun_Closing:
      PIDDID_isRoofCtrlRtnBusy = true;
      if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0) {
        c_z = MAX_uint16_T;
      } else {
        if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
          c_z = MAX_uint16_T;

          /* Divide by zero handler */
        } else {
          c_z = (uint16_T)(2000U / ((uint16_T)RoofSys_tSmpPIDDID_SC));
        }

        c_x = (uint16_T)(2000U - (uint16_T)((uint32_T)c_z * ((uint16_T)
          RoofSys_tSmpPIDDID_SC)));
        if ((c_x > 0) && (c_x >= (int32_T)((uint32_T)((uint16_T)
               RoofSys_tSmpPIDDID_SC) >> 1) + (((uint16_T)RoofSys_tSmpPIDDID_SC)
              & 1))) {
          c_z++;
        }
      }

      rtb_LogicalOperator = ((PIDDID_Mdl_DW.temporalCounter_i1 >= c_z) &&
        (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
         CtrlLog_tenTargetDirection_None_C) &&
        (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn <= 1));
      if (rtb_LogicalOperator) {
        PIDDID_stRoutineResult_RoofCtrlrtn =
          PIDDID_RoutineStatus_t_Routine_Successful_C;
        PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
        guard2 = true;
      } else if (PIDDID_stDiagRtnCmd == VehCom_Cmd_t_Stop_C) {
        PIDDID_stRoutineResult_RoofCtrlrtn =
          PIDDID_RoutineStatus_t_Routine_Successful_C;
        guard2 = true;
      } else {
        if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0) {
          c_z = MAX_uint16_T;
        } else {
          if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
            c_z = MAX_uint16_T;

            /* Divide by zero handler */
          } else {
            c_z = (uint16_T)(2000U / ((uint16_T)RoofSys_tSmpPIDDID_SC));
          }

          c_x = (uint16_T)(2000U - (uint16_T)((uint32_T)c_z * ((uint16_T)
            RoofSys_tSmpPIDDID_SC)));
          if ((c_x > 0) && (c_x >= (int32_T)((uint32_T)((uint16_T)
                 RoofSys_tSmpPIDDID_SC) >> 1) + (((uint16_T)
                 RoofSys_tSmpPIDDID_SC) & 1))) {
            c_z++;
          }
        }

        rtb_LogicalOperator = ((PIDDID_Mdl_DW.temporalCounter_i1 >= c_z) &&
          (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
           CtrlLog_tenTargetDirection_None_C) &&
          (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn < 99) &&
          (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn > 1));
        if (rtb_LogicalOperator) {
          PIDDID_stRoutineResult_RoofCtrlrtn =
            PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
          PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
          guard2 = true;
        }
      }
      break;

     default:
      /* case IN_RequestRun_Opening: */
      PIDDID_isRoofCtrlRtnBusy = true;
      if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0) {
        c_z = MAX_uint16_T;
      } else {
        if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
          c_z = MAX_uint16_T;

          /* Divide by zero handler */
        } else {
          c_z = (uint16_T)(2000U / ((uint16_T)RoofSys_tSmpPIDDID_SC));
        }

        c_x = (uint16_T)(2000U - (uint16_T)((uint32_T)c_z * ((uint16_T)
          RoofSys_tSmpPIDDID_SC)));
        if ((c_x > 0) && (c_x >= (int32_T)((uint32_T)((uint16_T)
               RoofSys_tSmpPIDDID_SC) >> 1) + (((uint16_T)RoofSys_tSmpPIDDID_SC)
              & 1))) {
          c_z++;
        }
      }

      rtb_LogicalOperator = ((PIDDID_Mdl_DW.temporalCounter_i1 >= c_z) &&
        (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
         CtrlLog_tenTargetDirection_None_C) &&
        (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn >= 99));
      if (rtb_LogicalOperator) {
        PIDDID_stRoutineResult_RoofCtrlrtn =
          PIDDID_RoutineStatus_t_Routine_Successful_C;
        PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
        guard1 = true;
      } else if (PIDDID_stDiagRtnCmd == VehCom_Cmd_t_Stop_C) {
        PIDDID_stRoutineResult_RoofCtrlrtn =
          PIDDID_RoutineStatus_t_Routine_Successful_C;
        guard1 = true;
      } else {
        if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0) {
          c_z = MAX_uint16_T;
        } else {
          if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
            c_z = MAX_uint16_T;

            /* Divide by zero handler */
          } else {
            c_z = (uint16_T)(2000U / ((uint16_T)RoofSys_tSmpPIDDID_SC));
          }

          c_x = (uint16_T)(2000U - (uint16_T)((uint32_T)c_z * ((uint16_T)
            RoofSys_tSmpPIDDID_SC)));
          if ((c_x > 0) && (c_x >= (int32_T)((uint32_T)((uint16_T)
                 RoofSys_tSmpPIDDID_SC) >> 1) + (((uint16_T)
                 RoofSys_tSmpPIDDID_SC) & 1))) {
            c_z++;
          }
        }

        rtb_LogicalOperator = ((PIDDID_Mdl_DW.temporalCounter_i1 >= c_z) &&
          (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
           CtrlLog_tenTargetDirection_None_C) &&
          (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn < 99) &&
          (*rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn > 1));
        if (rtb_LogicalOperator) {
          PIDDID_stRoutineResult_RoofCtrlrtn =
            PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
          PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
          guard1 = true;
        }
      }
      break;
    }

    if (guard2) {
      PIDDID_Mdl_DW.is_c7_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;
      PIDDID_isRoofCtrlRtnBusy = false;
    }

    if (guard1) {
      PIDDID_Mdl_DW.is_c7_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;
      PIDDID_isRoofCtrlRtnBusy = false;
    }
  }

  /* End of Chart: '<S2>/Roof_Control_Routine' */

  /* Logic: '<S2>/Logical Operator' */
  rtb_LogicalOperator =
    ((*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete) &&
     (*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld));

  /* Chart: '<S2>/Goto_Target_Pos' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read15'
   *  DataStoreRead: '<S2>/Data Store Read6'
   *  DataStoreRead: '<S2>/Data Store Read8'
   *  DataStoreRead: '<S2>/Data Store Read9'
   *  DataStoreWrite: '<S2>/Data Store Write18'
   */
  if (PIDDID_Mdl_DW.is_active_c6_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c6_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c6_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;

    /*  First initialization of the Routine */
    PIDDID_isRtnToPosBusy = false;
  } else if (PIDDID_Mdl_DW.is_c6_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    PIDDID_isRtnToPosBusy = false;
    if ((PIDDID_stDiagRtnCmd == VehCom_Cmd_t_AutoHallToPos_C) &&
        PIDDID_isGoToTrgtActv) {
      if (((*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos >= PIDDID_hDiagToPos
            - CfgParam_CAL.hMovPosErr) &&
           (*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos <= PIDDID_hDiagToPos
            + CfgParam_CAL.hMovPosErr)) || (!rtb_LogicalOperator)) {
        PIDDID_stRoutineResult_GotoTgtPos =
          PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
        PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;

        /*  First initialization of the Routine */
      } else {
        PIDDID_Mdl_DW.is_c6_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRun;
        PIDDID_isRtnToPosBusy = true;
        PIDDID_stRoutineResult_GotoTgtPos =
          PIDDID_RoutineStatus_t_Routine_InProgress_C;
      }
    }
  } else {
    /* case IN_RequestRun: */
    PIDDID_isRtnToPosBusy = true;
    guard1 = false;
    if ((*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
         CtrlLog_tenTargetDirection_None_C) &&
        (*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos >= PIDDID_hDiagToPos -
         CfgParam_CAL.hMovPosErr) &&
        (*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos <= PIDDID_hDiagToPos +
         CfgParam_CAL.hMovPosErr)) {
      PIDDID_stRoutineResult_GotoTgtPos =
        PIDDID_RoutineStatus_t_Routine_Successful_C;
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
      guard1 = true;
    } else if (!PIDDID_isGoToTrgtActv) {
      PIDDID_stRoutineResult_GotoTgtPos =
        PIDDID_RoutineStatus_t_Routine_Successful_C;
      guard1 = true;
    } else if (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
               CtrlLog_tenTargetDirection_None_C) {
      PIDDID_stRoutineResult_GotoTgtPos =
        PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
      guard1 = true;
    }

    if (guard1) {
      PIDDID_Mdl_DW.is_c6_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;

      /*  First initialization of the Routine */
      PIDDID_isRtnToPosBusy = false;
    }
  }

  /* End of Chart: '<S2>/Goto_Target_Pos' */

  /* Logic: '<S2>/Logical Operator1' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read12'
   *  DataStoreWrite: '<S2>/Data Store Write1'
   *  DataStoreWrite: '<S2>/Data Store Write16'
   *  DataStoreWrite: '<S2>/Data Store Write18'
   *  DataStoreWrite: '<S2>/Data Store Write20'
   */
  PIDDID_isOtherRtnBusy = (PIDDID_isRtnBusy || PIDDID_isRoofCtrlRtnBusy ||
    PIDDID_isRtnToPosBusy || PIDDID_isAutoCycleActive);

  /* Chart: '<S2>/Run_Motor_Continuous_Routine' incorporates:
   *  DataStoreWrite: '<S2>/Data Store Write13'
   */
  PIDDID_Mdl_DW.CtrlLog_stDirCommand_prev =
    PIDDID_Mdl_DW.CtrlLog_stDirCommand_start;
  PIDDID_Mdl_DW.CtrlLog_stDirCommand_start =
    *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand;
  if (PIDDID_Mdl_DW.is_active_c12_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.CtrlLog_stDirCommand_prev =
      *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand;
    PIDDID_Mdl_DW.is_active_c12_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c12_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;

    /*  First initialization of the Routine */
    PIDDID_isMtrCtrlRtnBusy = false;
  } else if (PIDDID_Mdl_DW.is_c12_PIDDID_Mdl == PIDDID_Mdl_IN_Initilization) {
    PIDDID_isMtrCtrlRtnBusy = false;
    if ((PIDDID_stDiagRtnCmd == VehCom_Cmd_t_ManOpnCont_C) ||
        (PIDDID_stDiagRtnCmd == VehCom_Cmd_t_ManClsCont_C)) {
      PIDDID_Mdl_DW.is_c12_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRun_CCW;
      PIDDID_isMtrCtrlRtnBusy = true;
      PIDDID_stRoutineResult_MtrCtrlrtn =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }
  } else {
    /* case IN_RequestRun_CCW: */
    PIDDID_isMtrCtrlRtnBusy = true;
    guard1 = false;
    if ((PIDDID_stDiagRtnCmd != VehCom_Cmd_t_ManOpnCont_C) &&
        (PIDDID_stDiagRtnCmd != VehCom_Cmd_t_ManClsCont_C)) {
      PIDDID_stRoutineResult_MtrCtrlrtn =
        PIDDID_RoutineStatus_t_Routine_Successful_C;
      guard1 = true;
    } else if ((PIDDID_Mdl_DW.CtrlLog_stDirCommand_prev !=
                PIDDID_Mdl_DW.CtrlLog_stDirCommand_start) &&
               (PIDDID_Mdl_DW.CtrlLog_stDirCommand_start ==
                CtrlLog_tenTargetDirection_None_C)) {
      PIDDID_stRoutineResult_MtrCtrlrtn =
        PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
      guard1 = true;
    }

    if (guard1) {
      PIDDID_Mdl_DW.is_c12_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;

      /*  First initialization of the Routine */
      PIDDID_isMtrCtrlRtnBusy = false;
    }
  }

  /* End of Chart: '<S2>/Run_Motor_Continuous_Routine' */

  /* Chart: '<S2>/AutoCycle' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read11'
   *  DataStoreRead: '<S2>/Data Store Read13'
   *  DataStoreRead: '<S2>/Data Store Read14'
   *  DataStoreWrite: '<S2>/Data Store Write2'
   */
  if (PIDDID_Mdl_DW.temporalCounter_i1_j < MAX_uint32_T) {
    PIDDID_Mdl_DW.temporalCounter_i1_j++;
  }

  if (PIDDID_Mdl_DW.is_active_c5_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c5_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c5_PIDDID_Mdl = PIDDID_Mdl_IN_Init;
    PIDDID_hDiagToPos = 0U;
  } else {
    guard1 = false;
    switch (PIDDID_Mdl_DW.is_c5_PIDDID_Mdl) {
     case PIDDID_Mdl_IN_AutoCycle:
      if (!PIDDID_isAutoCycleActive) {
        PIDDID_stRoutineResult_AutoCyc =
          PIDDID_RoutineStatus_t_Routine_Successful_C;
        guard1 = true;
      } else {
        rtb_LogicalOperator =
          ((*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
            CtrlLog_tenTargetDirection_None_C) && (!rtb_LogicalOperator));
        if (rtb_LogicalOperator) {
          PIDDID_stRoutineResult_AutoCyc =
            PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
          guard1 = true;
        } else {
          switch (PIDDID_Mdl_DW.is_AutoCycle) {
           case PIDDID_Mdl_IN_GoToFullClose:
            qY = (uint32_T)
              *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls +
              CfgParam_CAL.hMovPosErr;
            if (qY > 65535U) {
              qY = 65535U;
            }

            rtb_LogicalOperator =
              ((*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos <= (int32_T)qY) &&
               (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
                CtrlLog_tenTargetDirection_None_C));
            if (rtb_LogicalOperator) {
              PIDDID_Mdl_DW.is_AutoCycle =
                PIDDID_Mdl_IN_WaitAfterCloseDuringRoutine;
              PIDDID_Mdl_DW.temporalCounter_i1_j = 0U;
              PIDDID_stDiagRtnCmd = VehCom_Cmd_t_Stop_C;
            }
            break;

           case PIDDID_Mdl_IN_GoToFullOpen:
            q0 = *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn;
            qY = (uint32_T)q0 - /*MW:OvSatOk*/ CfgParam_CAL.hMovPosErr;
            if (qY > (uint32_T)q0) {
              qY = 0U;
            }

            rtb_LogicalOperator =
              ((*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos >= (int32_T)qY) &&
               (*rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand ==
                CtrlLog_tenTargetDirection_None_C));
            if (rtb_LogicalOperator) {
              PIDDID_Mdl_DW.is_AutoCycle =
                PIDDID_Mdl_IN_WaitAfterOpenDuringRoutine;
              PIDDID_Mdl_DW.temporalCounter_i1_j = 0U;
              PIDDID_stDiagRtnCmd = VehCom_Cmd_t_Stop_C;
            }
            break;

           case PIDDID_Mdl_IN_WaitAfterCloseDuringRoutine:
            if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0) {
              if (PIDDID_uAutoCyclePauseTime[1] == 0) {
                c_z = 0U;
              } else {
                c_z = MAX_uint16_T;
              }
            } else {
              if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
                c_z = MAX_uint16_T;

                /* Divide by zero handler */
              } else {
                c_z = (uint16_T)((uint32_T)PIDDID_uAutoCyclePauseTime[1] /
                                 ((uint16_T)RoofSys_tSmpPIDDID_SC));
              }

              c_x = (uint16_T)((uint32_T)PIDDID_uAutoCyclePauseTime[1] -
                               (uint16_T)((uint32_T)c_z * ((uint16_T)
                RoofSys_tSmpPIDDID_SC)));
              if ((c_x > 0) && (c_x >= (int32_T)((uint32_T)((uint16_T)
                     RoofSys_tSmpPIDDID_SC) >> 1) + (((uint16_T)
                     RoofSys_tSmpPIDDID_SC) & 1))) {
                c_z++;
              }
            }

            if (PIDDID_Mdl_DW.temporalCounter_i1_j >= c_z) {
              PIDDID_Mdl_DW.is_AutoCycle = PIDDID_Mdl_IN_GoToFullOpen;
              PIDDID_hDiagToPos =
                *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn;
              PIDDID_stDiagRtnCmd = VehCom_Cmd_t_AutoHallToPos_C;
            }
            break;

           default:
            /* case IN_WaitAfterOpenDuringRoutine: */
            if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0) {
              if (PIDDID_uAutoCyclePauseTime[0] == 0) {
                c_z = 0U;
              } else {
                c_z = MAX_uint16_T;
              }
            } else {
              if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
                c_z = MAX_uint16_T;

                /* Divide by zero handler */
              } else {
                c_z = (uint16_T)((uint32_T)PIDDID_uAutoCyclePauseTime[0] /
                                 ((uint16_T)RoofSys_tSmpPIDDID_SC));
              }

              c_x = (uint16_T)((uint32_T)PIDDID_uAutoCyclePauseTime[0] -
                               (uint16_T)((uint32_T)c_z * ((uint16_T)
                RoofSys_tSmpPIDDID_SC)));
              if ((c_x > 0) && (c_x >= (int32_T)((uint32_T)((uint16_T)
                     RoofSys_tSmpPIDDID_SC) >> 1) + (((uint16_T)
                     RoofSys_tSmpPIDDID_SC) & 1))) {
                c_z++;
              }
            }

            if (PIDDID_Mdl_DW.temporalCounter_i1_j >= c_z) {
              PIDDID_Mdl_DW.is_AutoCycle = PIDDID_Mdl_IN_GoToFullClose;
              PIDDID_hDiagToPos =
                *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls;
              PIDDID_stDiagRtnCmd = VehCom_Cmd_t_AutoHallToPos_C;
            }
            break;
          }
        }
      }
      break;

     case PIDDID_Mdl_IN_Init:
      if (PIDDID_isAutoCycleActive && rtb_LogicalOperator) {
        PIDDID_Mdl_DW.is_c5_PIDDID_Mdl = PIDDID_Mdl_IN_AutoCycle;
        PIDDID_stRoutineResult_AutoCyc =
          PIDDID_RoutineStatus_t_Routine_InProgress_C;
        PIDDID_Mdl_DW.is_AutoCycle = PIDDID_Mdl_IN_GoToFullOpen;
        PIDDID_hDiagToPos =
          *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn;
        PIDDID_stDiagRtnCmd = VehCom_Cmd_t_AutoHallToPos_C;
      }
      break;

     default:
      /* case IN_StopMovement: */
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
      PIDDID_Mdl_DW.is_c5_PIDDID_Mdl = PIDDID_Mdl_IN_Init;
      PIDDID_hDiagToPos = 0U;
      break;
    }

    if (guard1) {
      PIDDID_Mdl_DW.is_AutoCycle = PIDDID_Mdl_IN_NO_ACTIVE_CHILD;
      PIDDID_Mdl_DW.is_c5_PIDDID_Mdl = PIDDID_Mdl_IN_StopMovement;
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_Stop_C;
    }
  }

  /* End of Chart: '<S2>/AutoCycle' */

  /* Chart: '<S2>/ClearRF_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read2'
   */
  if (PIDDID_Mdl_DW.is_active_c1_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c1_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c1_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else {
    switch (PIDDID_Mdl_DW.is_c1_PIDDID_Mdl) {
     case PIDDID_Mdl_IN_Init_k:
      if (PIDDID_isReqUnlearn) {
        PIDDID_Mdl_DW.is_c1_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
        PIDDID_stRoutineResult_ClearRPRF =
          PIDDID_RoutineStatus_t_Routine_InProgress_C;
      }
      break;

     case PIDDID_Mdl_IN_request_running:
      if ((!*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete) &&
          (*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq ==
           LearnAdap_Req_t_NoReq_C)) {
        PIDDID_Mdl_DW.is_c1_PIDDID_Mdl = PIDDID_Mdl_IN_stopped;
        PIDDID_Mdl_B.isReqUnlearnOut = false;
        PIDDID_stRoutineResult_ClearRPRF =
          PIDDID_RoutineStatus_t_Routine_Successful_C;
      } else {
        PIDDID_stRoutineResult_ClearRPRF =
          PIDDID_RoutineStatus_t_Routine_InProgress_C;
      }
      break;

     default:
      /* case IN_stopped: */
      PIDDID_Mdl_B.isReqUnlearnOut = false;
      if (PIDDID_isReqUnlearn) {
        PIDDID_Mdl_DW.is_c1_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
        PIDDID_stRoutineResult_ClearRPRF =
          PIDDID_RoutineStatus_t_Routine_InProgress_C;
      }
      break;
    }
  }

  /* End of Chart: '<S2>/ClearRF_Routine' */

  /* DataStoreWrite: '<S2>/Data Store Write3' */
  PIDDID_isReqUnlearn = PIDDID_Mdl_B.isReqUnlearnOut;

  /* Chart: '<S2>/HallOutput_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read18'
   *  DataStoreWrite: '<S2>/Data Store Write8'
   */
  if (PIDDID_Mdl_DW.is_active_c18_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c18_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c18_PIDDID_Mdl = PIDDID_Mdl_IN_Init;
  } else if (PIDDID_Mdl_DW.is_c18_PIDDID_Mdl ==
             PIDDID_Mdl_IN_DisableHallOutputCurrentPwrCycle) {
    PIDDID_HallOutputDisabled = true;
  } else {
    /* case IN_Init: */
    rtb_LogicalOperator = (ICMntr_bReturnHallOutputEnabledStatus() &&
      (*rtu_SWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault));
    if (rtb_LogicalOperator) {
      PIDDID_Mdl_DW.is_c18_PIDDID_Mdl =
        PIDDID_Mdl_IN_DisableHallOutputCurrentPwrCycle;
      PIDDID_HallOutputDisabled = true;
    }
  }

  /* End of Chart: '<S2>/HallOutput_Routine' */

  /* Logic: '<S16>/AND' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read'
   *  Logic: '<S16>/NOT'
   *  UnitDelay: '<S16>/Unit Delay'
   */
  rtb_LogicalOperator = (PIDDID_isSyncToNVMEnable &&
    (!PIDDID_Mdl_DW.UnitDelay_DSTATE));

  /* Outputs for Triggered SubSystem: '<S16>/NVMWriteForShutdown' incorporates:
   *  TriggerPort: '<S25>/Trigger'
   */
  if (rtb_LogicalOperator && (PIDDID_Mdl_PrevZCX.NVMWriteForShutdown_Trig_ZCE !=
       POS_ZCSIG)) {
    /* S-Function (NVMMan_enSaveAllNVMData): '<S25>/S-Function1' incorporates:
     *  Constant: '<S25>/Full Save'
     */
    /* S-Function Block: <S25>/S-Function1 */
    PIDDID_Mdl_B.stsGoToShtdwnReady = NVMMan_enSaveAllNVMData(false);
  }

  PIDDID_Mdl_PrevZCX.NVMWriteForShutdown_Trig_ZCE = rtb_LogicalOperator;

  /* End of Outputs for SubSystem: '<S16>/NVMWriteForShutdown' */

  /* Chart: '<S2>/Sync_To_NVM' incorporates:
   *  DataStoreWrite: '<S2>/Data Store Write14'
   *  S-Function (NVMMan_enSaveAllNVMData): '<S25>/S-Function1'
   */
  PIDDID_stSyncToNvm = PIDDID_Mdl_B.stsGoToShtdwnReady;

  /* Chart: '<S2>/Suppress_CyclicRenorm_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read19'
   *  DataStoreWrite: '<S2>/Data Store Write15'
   */
  if (PIDDID_Mdl_DW.is_active_c13_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c13_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c13_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;
    PIDDID_isSuppressCyclicRenormRtnBusy = false;
  } else if (PIDDID_Mdl_DW.is_c13_PIDDID_Mdl == PIDDID_Mdl_IN_Initilization) {
    if (PIDDID_isCyclicRenormSuppress) {
      PIDDID_Mdl_DW.is_c13_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRunentry_;
      PIDDID_isSuppressCyclicRenormRtnBusy = true;
      PIDDID_stRoutineResult_SuppressCyclicRenorm =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    /* case IN_RequestRunentry_: */
  } else if (!PIDDID_isCyclicRenormSuppress) {
    PIDDID_stRoutineResult_SuppressCyclicRenorm =
      PIDDID_RoutineStatus_t_Routine_Successful_C;
    PIDDID_Mdl_DW.is_c13_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;
    PIDDID_isSuppressCyclicRenormRtnBusy = false;
  } else {
    PIDDID_isSuppressCyclicRenormRtnBusy = true;
    PIDDID_stRoutineResult_SuppressCyclicRenorm =
      PIDDID_RoutineStatus_t_Routine_InProgress_C;
  }

  /* End of Chart: '<S2>/Suppress_CyclicRenorm_Routine' */

  /* Chart: '<S2>/OverridePWM_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read20'
   *  DataStoreWrite: '<S2>/Data Store Write17'
   */
  if (PIDDID_Mdl_DW.is_active_c14_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c14_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c14_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;
    PIDDID_isOverridePWMRtnBusy = false;
  } else if (PIDDID_Mdl_DW.is_c14_PIDDID_Mdl == PIDDID_Mdl_IN_Initilization) {
    if (PIDDID_isOverridePWMEnable) {
      PIDDID_Mdl_DW.is_c14_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRunentry_;
      PIDDID_isOverridePWMRtnBusy = true;
      PIDDID_stOverridePWMRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    /* case IN_RequestRunentry_: */
  } else if (!PIDDID_isOverridePWMEnable) {
    PIDDID_stOverridePWMRoutineResult =
      PIDDID_RoutineStatus_t_Routine_Successful_C;
    PIDDID_Mdl_DW.is_c14_PIDDID_Mdl = PIDDID_Mdl_IN_Initilization;
    PIDDID_isOverridePWMRtnBusy = false;
  } else {
    PIDDID_isOverridePWMRtnBusy = true;
    PIDDID_stOverridePWMRoutineResult =
      PIDDID_RoutineStatus_t_Routine_InProgress_C;
  }

  /* End of Chart: '<S2>/OverridePWM_Routine' */

  /* Chart: '<S2>/Dummy_RP_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read21'
   */
  if (PIDDID_Mdl_DW.temporalCounter_i1_n < 7U) {
    PIDDID_Mdl_DW.temporalCounter_i1_n++;
  }

  PIDDID_Mdl_DW.PIDDID_isDummyRPRtnEna_prev =
    PIDDID_Mdl_DW.PIDDID_isDummyRPRtnEna_start;
  PIDDID_Mdl_DW.PIDDID_isDummyRPRtnEna_start = PIDDID_isDummyRPRtnEna;
  if (PIDDID_Mdl_DW.is_active_c17_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.PIDDID_isDummyRPRtnEna_prev = PIDDID_isDummyRPRtnEna;
    PIDDID_Mdl_DW.is_active_c17_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c17_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else if (PIDDID_Mdl_DW.is_c17_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    if ((PIDDID_Mdl_DW.PIDDID_isDummyRPRtnEna_prev !=
         PIDDID_Mdl_DW.PIDDID_isDummyRPRtnEna_start) &&
        PIDDID_Mdl_DW.PIDDID_isDummyRPRtnEna_start) {
      PIDDID_Mdl_DW.is_c17_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
      PIDDID_Mdl_DW.temporalCounter_i1_n = 0U;
      PIDDID_stDummyRPRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    /* case IN_request_running: */
  } else if ((*rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos == 0) &&
             (CfgParam_CAL.hPosTbl.hHardStopOpn ==
              *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop)) {
    PIDDID_stDummyRPRoutineResult = PIDDID_RoutineStatus_t_Routine_Successful_C;
    PIDDID_isDummyRPRtnEna = false;
    PIDDID_Mdl_DW.is_c17_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else if (PIDDID_Mdl_DW.temporalCounter_i1_n >= 5) {
    PIDDID_stDummyRPRoutineResult =
      PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
    PIDDID_isDummyRPRtnEna = false;
    PIDDID_Mdl_DW.is_c17_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  }

  /* End of Chart: '<S2>/Dummy_RP_Routine' */

  /* Chart: '<S2>/IO_TEST_ROUTINE' */
  if (PIDDID_Mdl_DW.temporalCounter_i1_a < MAX_uint32_T) {
    PIDDID_Mdl_DW.temporalCounter_i1_a++;
  }

  if (PIDDID_Mdl_DW.is_active_c8_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c8_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c8_PIDDID_Mdl = PIDDID_Mdl_IN_init;
    PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
  } else if (PIDDID_Mdl_DW.is_c8_PIDDID_Mdl == PIDDID_Mdl_IN_RequestRun_j) {
    /*  monitor HS2_OC bit in HS_OL_OC_OT_STAT Register */
    guard1 = false;
    if ((*rtu_SWC_OperLogLyrBus_MtrCtrlBus_DTC_MTR_Dir == 2) ||
        (*rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HallMtrOptDir == 2)) {
      PIDDID_stRoutineResult_IOTestRtn =
        PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
      PIDDID_stIOTestCmd = 0U;
      guard1 = true;
    } else if (PIDDID_stIOTestCmd == 0) {
      PIDDID_stRoutineResult_IOTestRtn =
        PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
      guard1 = true;
    } else {
      switch (PIDDID_Mdl_DW.is_RequestRun) {
       case PIDDID_Mdl_IN_Step1:
        if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
          q0 = MAX_int32_T;

          /* Divide by zero handler */
        } else {
          q0 = (int32_T)(1000U / ((uint16_T)RoofSys_tSmpPIDDID_SC));
        }

        if (PIDDID_Mdl_DW.temporalCounter_i1_a >= (uint32_T)q0) {
          PIDDID_Mdl_DW.is_RequestRun = PIDDID_Mdl_IN_Step2;
          PIDDID_Mdl_DW.temporalCounter_i1_a = 0U;
          PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
        }
        break;

       case PIDDID_Mdl_IN_Step2:
        if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
          q0 = MAX_int32_T;

          /* Divide by zero handler */
        } else {
          q0 = (int32_T)(1000U / ((uint16_T)RoofSys_tSmpPIDDID_SC));
        }

        if (PIDDID_Mdl_DW.temporalCounter_i1_a >= (uint32_T)q0) {
          PIDDID_Mdl_DW.is_RequestRun = PIDDID_Mdl_IN_Step3;
          PIDDID_Mdl_DW.temporalCounter_i1_a = 0U;
          PIDDID_stDiagRtnCmd = VehCom_Cmd_t_ManOpn_C;
        }
        break;

       case PIDDID_Mdl_IN_Step3:
        if (((uint16_T)RoofSys_tSmpPIDDID_SC) == 0U) {
          q0 = MAX_int32_T;

          /* Divide by zero handler */
        } else {
          q0 = (int32_T)(1000U / ((uint16_T)RoofSys_tSmpPIDDID_SC));
        }

        if (PIDDID_Mdl_DW.temporalCounter_i1_a >= (uint32_T)q0) {
          PIDDID_Mdl_DW.is_RequestRun = PIDDID_Mdl_IN_Step4;
          PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
        }
        break;

       default:
        /* case IN_Step4: */
        PIDDID_stRoutineResult_IOTestRtn =
          PIDDID_RoutineStatus_t_Routine_Successful_C;
        PIDDID_stIOTestCmd = 0U;
        PIDDID_Mdl_DW.is_RequestRun = PIDDID_Mdl_IN_NO_ACTIVE_CHILD;
        PIDDID_Mdl_DW.is_c8_PIDDID_Mdl = PIDDID_Mdl_IN_init;
        PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
        break;
      }
    }

    if (guard1) {
      PIDDID_Mdl_DW.is_RequestRun = PIDDID_Mdl_IN_NO_ACTIVE_CHILD;
      PIDDID_Mdl_DW.is_c8_PIDDID_Mdl = PIDDID_Mdl_IN_init;
      PIDDID_stDiagRtnCmd = VehCom_Cmd_t_None_C;
    }

    /* case IN_init: */
  } else if (PIDDID_stIOTestCmd == 1) {
    PIDDID_Mdl_DW.is_c8_PIDDID_Mdl = PIDDID_Mdl_IN_RequestRun_j;
    PIDDID_stRoutineResult_IOTestRtn =
      PIDDID_RoutineStatus_t_Routine_InProgress_C;
    PIDDID_Mdl_DW.is_RequestRun = PIDDID_Mdl_IN_Step1;
    PIDDID_Mdl_DW.temporalCounter_i1_a = 0U;
    PIDDID_stDiagRtnCmd = VehCom_Cmd_t_ManCls_C;
  }

  /* End of Chart: '<S2>/IO_TEST_ROUTINE' */

  /* Chart: '<S2>/Clear_History_Logs_Routine' */
  guard1 = false;
  guard2 = false;
  if (PIDDID_Mdl_DW.is_active_c3_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c3_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c3_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
    guard1 = true;
  } else {
    switch (PIDDID_Mdl_DW.is_c3_PIDDID_Mdl) {
     case PIDDID_Mdl_IN_Init_k:
      if (PIDDID_isClearHisRtnActive) {
        PIDDID_Mdl_DW.is_c3_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
        PIDDID_stClearRtnResult = PIDDID_RoutineStatus_t_Routine_InProgress_C;
        SysFltRctn_stLostRP_Reason = 0U;
        SysFltRctn_stLearnFail_Reason = 0U;
        UsgHist_Cycle_Counter_stallCnt.UsgHist_SlideCycCount = 0U;
        UsgHist_Cycle_Counter_stallCnt.UsgHist_CycRenormCount = 0U;
        UsgHist_History_loggers.UsgHist_MotorOperTime = 0U;
        UsgHist_History_loggers.UsgHist_ReversalCount = 0U;
        UsgHist_History_loggers.UsgHist_ReprogrammingCount = 0U;
        UsgHist_MinMax_historylog.UsgHist_MaxAmbTemp = 0;
        UsgHist_MinMax_historylog.UsgHist_MinAmbTemp = MAX_int16_T;
        UsgHist_MinMax_historylog.UsgHist_MaxBattVtg = 0U;
        UsgHist_MinMax_historylog.UsgHist_MinBattVtg = MAX_uint16_T;
        SysFltRctn_stLastStopReason = NoFault_C;
        UsgHist_Extend_Event.UsgHist_ThermProtCount = 0U;
        UsgHist_Extend_Event.UsgHist_LearnSuccessCount = 0U;
        UsgHist_Extend_Event.UsgHist_LeanrFailCount = 0U;
        BlockDet_hStallPos = 0U;
        BlockDet_stStallDir = BlockDet_Stall_dir_NoMov_C;
        UsgHist_Cycle_Counter_stallCnt.UsgHist_StallCount = 0U;
        guard2 = true;
      } else {
        guard1 = true;
      }
      break;

     case PIDDID_Mdl_IN_request_running:
      if (PIDDID_Mdl_DW.Delay1_DSTATE) {
        PIDDID_Mdl_DW.is_c3_PIDDID_Mdl = PIDDID_Mdl_IN_stopped;
        PIDDID_isClearHisRtnActive = false;
        guard1 = true;
      } else {
        guard2 = true;
      }
      break;

     default:
      /* case IN_stopped: */
      PIDDID_stClearRtnResult = PIDDID_RoutineStatus_t_Routine_Successful_C;
      PIDDID_Mdl_DW.is_c3_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
      guard1 = true;
      break;
    }
  }

  if (guard2) {
    /* Outputs for Enabled SubSystem: '<S2>/NVMClear' incorporates:
     *  EnablePort: '<S15>/Enable'
     */
    PIDDID_Mdl_DW.NVMClear_MODE = true;

    /* S-Function (NVMMan_vUpdateClearHistoryBlock): '<S15>/S-Function1' */
    /* S-Function Block: <S15>/S-Function1 */
    PIDDID_Mdl_B.SFunction1 = NVMMan_vUpdateClearHistoryBlock();

    /* RelationalOperator: '<S15>/Equal' incorporates:
     *  Constant: '<S15>/Constant'
     *  S-Function (NVMMan_vUpdateClearHistoryBlock): '<S15>/S-Function1'
     */
    PIDDID_Mdl_B.Equal = (PIDDID_Mdl_B.SFunction1 == SCU_STATUS_SUCCESS);

    /* End of Outputs for SubSystem: '<S2>/NVMClear' */
  }

  if (guard1) {
    /* Outputs for Enabled SubSystem: '<S2>/NVMClear' incorporates:
     *  EnablePort: '<S15>/Enable'
     */
    if (PIDDID_Mdl_DW.NVMClear_MODE) {
      /* Disable for RelationalOperator: '<S15>/Equal' incorporates:
       *  Outport: '<S15>/NVMClearStatus'
       */
      PIDDID_Mdl_B.Equal = false;
      PIDDID_Mdl_DW.NVMClear_MODE = false;
    }

    /* End of Outputs for SubSystem: '<S2>/NVMClear' */
  }

  /* End of Chart: '<S2>/Clear_History_Logs_Routine' */

  /* RelationalOperator: '<S16>/Equal' incorporates:
   *  Constant: '<S16>/Constant'
   *  S-Function (NVMMan_enSaveAllNVMData): '<S25>/S-Function1'
   *  UnitDelay: '<S16>/Unit Delay'
   */
  PIDDID_Mdl_DW.UnitDelay_DSTATE = (PIDDID_Mdl_B.stsGoToShtdwnReady ==
    SCU_STATUS_SUCCESS);

  /* Chart: '<S2>/SoftStart_Control_Routine' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read16'
   */
  if (PIDDID_Mdl_DW.is_active_c9_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c9_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c9_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else if (PIDDID_Mdl_DW.is_c9_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    if (PIDDID_isSoftStartEna) {
      PIDDID_Mdl_DW.is_c9_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
      PIDDID_stSoftStartRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    /* case IN_request_running: */
  } else if (!PIDDID_isSoftStartEna) {
    PIDDID_stSoftStartRoutineResult =
      PIDDID_RoutineStatus_t_Routine_Successful_C;
    PIDDID_Mdl_DW.is_c9_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  }

  /* End of Chart: '<S2>/SoftStart_Control_Routine' */

  /* Chart: '<S2>/SoftStop_Control' incorporates:
   *  DataStoreRead: '<S2>/Data Store Read17'
   */
  if (PIDDID_Mdl_DW.is_active_c10_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c10_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c10_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else if (PIDDID_Mdl_DW.is_c10_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    if (PIDDID_isSoftStopEna) {
      PIDDID_Mdl_DW.is_c10_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
      PIDDID_stSoftStopRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    /* case IN_request_running: */
  } else if (!PIDDID_isSoftStopEna) {
    PIDDID_stSoftStopRoutineResult = PIDDID_RoutineStatus_t_Routine_Successful_C;
    PIDDID_Mdl_DW.is_c10_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  }

  /* End of Chart: '<S2>/SoftStop_Control' */

  /* Chart: '<S2>/Control_Intermediate_Position_Routine' */
  if (PIDDID_Mdl_DW.is_active_c16_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c16_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c16_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else if (PIDDID_Mdl_DW.is_c16_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    if (PIDDID_isIntermediatePositionRtnEna) {
      PIDDID_Mdl_DW.is_c16_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
      PIDDID_stControlIntermediatePosRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    /* case IN_request_running: */
  } else if (!PIDDID_isIntermediatePositionRtnEna) {
    PIDDID_stControlIntermediatePosRoutineResult =
      PIDDID_RoutineStatus_t_Routine_Successful_C;
    PIDDID_Mdl_DW.is_c16_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else {
    PIDDID_stControlIntermediatePosRoutineResult =
      PIDDID_RoutineStatus_t_Routine_InProgress_C;
  }

  /* End of Chart: '<S2>/Control_Intermediate_Position_Routine' */

  /* Chart: '<S2>/Control_Open_Close_Position_Routine' */
  if (PIDDID_Mdl_DW.is_active_c15_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c15_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c15_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else if (PIDDID_Mdl_DW.is_c15_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    if (PIDDID_isOpenClosePositionRtnEna) {
      PIDDID_Mdl_DW.is_c15_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
      PIDDID_stOpenClosePosRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    /* case IN_request_running: */
  } else if (!PIDDID_isOpenClosePositionRtnEna) {
    PIDDID_stOpenClosePosRoutineResult =
      PIDDID_RoutineStatus_t_Routine_Successful_C;
    PIDDID_Mdl_DW.is_c15_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
  } else {
    PIDDID_stOpenClosePosRoutineResult =
      PIDDID_RoutineStatus_t_Routine_InProgress_C;
  }

  /* End of Chart: '<S2>/Control_Open_Close_Position_Routine' */

  /* Chart: '<S2>/Sync_To_NVM' */
  if (PIDDID_Mdl_DW.is_active_c11_PIDDID_Mdl == 0U) {
    PIDDID_Mdl_DW.is_active_c11_PIDDID_Mdl = 1U;
    PIDDID_Mdl_DW.is_c11_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
    PIDDID_isSyncToNVMEnable = false;
  } else if (PIDDID_Mdl_DW.is_c11_PIDDID_Mdl == PIDDID_Mdl_IN_Init_k) {
    if (PIDDID_isSyncToNVMEnable) {
      PIDDID_Mdl_DW.is_c11_PIDDID_Mdl = PIDDID_Mdl_IN_request_running;
      PIDDID_stNVMSyncRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }
  } else {
    /* case IN_request_running: */
    guard1 = false;
    if (PIDDID_stSyncToNvm == SCU_STATUS_SUCCESS) {
      PIDDID_stNVMSyncRoutineResult =
        PIDDID_RoutineStatus_t_Routine_Successful_C;
      guard1 = true;
    } else if (PIDDID_stSyncToNvm == SCU_STATUS_TIMEOUT) {
      PIDDID_stNVMSyncRoutineResult =
        PIDDID_RoutineStatus_t_Routine_Stopped_WO_Result_C;
      guard1 = true;
    } else {
      PIDDID_stNVMSyncRoutineResult =
        PIDDID_RoutineStatus_t_Routine_InProgress_C;
    }

    if (guard1) {
      PIDDID_Mdl_DW.is_c11_PIDDID_Mdl = PIDDID_Mdl_IN_Init_k;
      PIDDID_isSyncToNVMEnable = false;
    }
  }

  /* Outputs for Atomic SubSystem: '<Root>/Prepare_PIDDID_Bus' */
  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_MtrMdlBus_MtrMdl_TEstMtrTemp =
    *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_TEstMtrTemp;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_PosMon_PosMon_hCurPos =
    *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_VoltMon_VoltMon_stVoltClass =
    *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_VoltMon_VoltMon_isFlctnDet =
    *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_VoltMon_VoltMon_u12VBatt =
    *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_MOSFETTempBus_MOSFETTempMon_TMosTemp =
    *rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ThermProt_ThermProt_stMtrTempClass =
    *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ThermProt_ThermProt_isStartupRdy =
    *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_isStartupRdy;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ThermProt_ThermProt_stMosfetTempClass =
    *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_PnlOp_PnlOp_stATS = *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_stATS;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_PnlOp_PnlOp_isMtrMoving =
    *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_isMtrMoving;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_LearnAdap_LearnAdap_stMode =
    *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_MtrCtrl_IoHwAb_SetMtrDir =
    *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  rty_PIDDIDBus_MtrCtrl_MtrCtrl_FeedbackStatus[0] =
    rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_FeedbackStatus[0];
  rty_PIDDIDBus_MtrCtrl_MtrCtrl_FeedbackStatus[1] =
    rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_FeedbackStatus[1];

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand =
    *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_CtrlLog_CtrlLog_stRelearnMode =
    *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_nCycle =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_nCycle;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_MaxAmbTemp =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MaxAmbTemp;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_MinAmbTemp =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MinAmbTemp;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_MaxBattVtg =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MaxBattVtg;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_MinBattVtg =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MinBattVtg;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_MotorOperTime =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MotorOperTime;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_LearnAdap_LearnAdap_isLearnComplete =
    *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_CycRenormCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_CycRenormCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_LearnSuccessCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_LearnSuccessCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_LeanrFailCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_LeanrFailCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_StallCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_StallCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_SlideCycCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_SlideCycCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_ThermProtCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ThermProtCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_ReprogrammingCount =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReprogrammingCount;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalPos =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalPos;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalDir =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalDir;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_LearnAdap_LearnAdap_isLearningAllowed =
    *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearningAllowed;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalThres =
    *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalThres;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_HallDeco_HallDeco_DMtrDir =
    *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_DMtrDir;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_HallDeco_DTC_HALL_Hall1 =
    *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_HallDeco_HallDeco_rMtrSpd =
    *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_HallDeco_isHallSupplyFault =
    *rtu_SWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ADCMon_ADCMon_u12VBattInst =
    *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_u12VBattInst;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ADCMon_ADCMon_TAmbTemp =
    *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TAmbTemp;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ADCMon_ADCMon_TMOSFETTemp =
    *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TMOSFETTemp;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ADCMon_ADCMon_uHallPwrInst =
    *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uHallPwrInst;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ADCMon_ADCMon_uM1AVolt_mv =
    *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1AVolt_mv;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_BlockDet_BlockDet_hStallPos =
    *rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_hStallPos;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ADCMon_ADCMon_uM1BVolt_mv =
    *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1BVolt_mv;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_StateMach_StateMach_stSysMode =
    *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isVBATMeasEnbPinStatus =
    *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isVBATMeasEnbPinStatus;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallOutPinStatus =
    *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallOutPinStatus;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankAPinStatus =
    *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankAPinStatus;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankBPinStatus =
    *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankBPinStatus;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens1PinStatus =
    *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens1PinStatus;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens2PinStatus =
    *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens2PinStatus;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isINTPinStatus =
    *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isINTPinStatus;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ExtDev_ExtDev_isMtrFebkCtrlHS1Status =
    *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isMtrFebkCtrlHS1Status;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_BlockDet_BlockDet_stStallDir =
    *rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallDir;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ExtDev_ExtDev_isHallSuppHS2Status =
    *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isHallSuppHS2Status;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ExtDev_ExtDev_isSwtchSuppHS3Status =
    *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isSwtchSuppHS3Status;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_ExtDev_ExtDev_u8GetPWMDutyCycleVal =
    *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_u8GetPWMDutyCycleVal;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_AmbTemp_AmbTempMon_TAmbTemp =
    *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_AmbTemp_AmbTempMon_isAmbTempVld =
    *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld;

  /* SignalConversion generated from: '<S3>/PIDDIDBus' */
  *rty_PIDDIDBus_PosMon_PosMon_isCurPosVld =
    *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld;

  /* End of Outputs for SubSystem: '<Root>/Prepare_PIDDID_Bus' */

  /* Update for Delay: '<S2>/Delay1' */
  PIDDID_Mdl_DW.Delay1_DSTATE = PIDDID_Mdl_B.Equal;
}

/* Model initialize function */
void PIDDID_Mdl_initialize(const char_T **rt_errorStatus)
{
  RT_MODEL_PIDDID_Mdl_T *const PIDDID_Mdl_M = &(PIDDID_Mdl_MdlrefDW.rtm);

  /* Registration code */

  /* initialize error status */
  rtmSetErrorStatusPointer(PIDDID_Mdl_M, rt_errorStatus);
  PIDDID_Mdl_PrevZCX.NVMWriteForShutdown_Trig_ZCE = POS_ZCSIG;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
