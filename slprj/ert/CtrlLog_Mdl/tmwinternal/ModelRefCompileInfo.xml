<?xml version="1.0" encoding="UTF-8"?>
<MF0 version="1.1" packageUris="http://schema.mathworks.com/mf0/ci/19700101 http://schema.mathworks.com/mf0/sl_modelref_info/R2022b http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112 http://schema.mathworks.com/mf0/sltp_mm/R2022b_202203181029">
  <ModelRefInfoRepo.ModelRefInfoRoot type="ModelRefInfoRepo.ModelRefInfoRoot" uuid="fb681fe4-6530-48de-b434-9dc305ec7b81">
    <JITEngines>sXjRfP6dCvdYG3jnPrG01N</JITEngines>
    <JITEngines>slMSggQF0wwve6VmNhCCCFH</JITEngines>
    <calibrationData type="ModelRefInfoRepo.CalibrationData" uuid="9a074ee7-67c6-461f-b072-b382dbacaf67">
      <InternalData>[{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;}]</InternalData>
      <ModelName>CtrlLog_Mdl</ModelName>
      <RootIOData>[{&quot;Name&quot;:&quot;SWC_SigMonLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_StateMachLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_DataHndlLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_CommLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_DiagLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;ComLogBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;PnlOpBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_ObjDetLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_HwAbsLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;CtrlLogBus&quot;,&quot;Profile&quot;:&quot;&quot;}]</RootIOData>
    </calibrationData>
    <childModelRefInfo type="ModelRefInfoRepo.ChildModelRefInfo" uuid="83bcd45f-02d1-406a-9ff7-1d7197e43a55">
      <isSingleInstance>true</isSingleInstance>
      <modelName>CtrlLog_Mdl</modelName>
      <modelPath>CtrlLog_Mdl</modelPath>
    </childModelRefInfo>
    <compDerivCacheNeedsReset>false</compDerivCacheNeedsReset>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="953ad5d9-7a8a-4e66-8856-37424d9f9efa">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c25ba37c-7594-45f3-ad8f-7f18364ecbed">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="154cad73-d92d-4939-a134-358d35d59f3e">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="aacd5560-af1a-4167-88f6-d18fc1ba26c3">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6fef1a1f-47dd-4bec-b38a-715d08470acf">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="87074202-3cf6-4f74-90e1-6b69f75e7589">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e11497d4-d0bc-4143-a08f-53df6cf7e57a">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fc7e321b-ba0c-4162-b88e-1002783b08b6">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3691357b-8fd7-4aaf-a963-fca8247d9059">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f749c869-8df5-46ae-a58b-b7f4c77e41bc">
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="020f241b-3377-48ea-bcb0-8c2f777f2508">
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="dd6cb4a2-856c-47b2-9043-0541b70bf56e">
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3bf73e13-036d-49c7-ac39-5d8799717272">
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ed72be2a-903b-4dc5-9c33-e27cfc605831">
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="20e2566c-d502-4c37-8d32-65a06d955054">
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d9dc9297-bfce-45a6-87bd-d65d6d13f0c0">
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bbf72e8e-b66f-47bb-872c-1bc669737ce7">
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0fa7f892-37fd-417b-974c-de1b4afe8741">
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2f936913-1e7d-472e-9e2d-a2afd56c9706">
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f972aa3f-2203-4bfd-bd82-69b973f1c19b">
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cc6d0c39-1941-4642-947e-afda3c2fc0e6">
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ef82aea8-661e-435d-9646-a5589ed2b619">
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8d0bd612-61c8-40f9-a6ac-2817b529c6a7">
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="749ca603-fbb4-4dce-8981-6fef79cf0c5c">
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b9b3a00f-ae87-4c84-8572-8ee6a0829474">
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b7609188-c66f-441a-b61f-74f115243953">
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eeb248ce-608d-4ff7-8fc6-ff8f0d54296f">
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="68044ab1-9977-41f6-bb2c-357942fda46e">
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="47d753ed-1cbb-4830-a334-a18f3f564b40">
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4a2573bf-1813-4740-8155-f89c610134ad">
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fc6da329-4d62-497b-a5ab-734caba40d3c">
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c995ba2f-12aa-46a8-b903-77ba660c1e7c">
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6c30d19e-7a35-430f-8355-8c187dc363de">
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7b94c352-8b80-4548-9e17-bdb445cc4fdc">
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fd65ea79-d9cc-48a4-97e7-76df68a87bc4">
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b3d31a92-e9e6-4744-b036-1eaa490993b4">
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="596f9cde-bb19-4edf-ade7-c1059fca9963">
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5ff2da68-4f8a-4135-9aad-bc89b1cc8725">
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4e1e1d4a-065c-4235-8035-4b3a77b4a01c">
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7bf338b0-ac52-4ec9-b4c4-66a7ce822f39">
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="56502680-ed4a-46d1-b2b2-fc85d2438863">
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d4430bf6-8414-470d-a31c-df745a158dcd">
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="16bdaae5-4ab3-49cd-b1eb-7b3b075e8fc3">
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fd078ea7-4835-4b19-96f1-0bb152718c37">
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="70f17f6b-b0c6-4d07-9f2b-40c486f861b5">
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a59704f3-90bc-4c34-89e1-c416e5faa64e">
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1fca96a0-0de0-413f-9397-546449b03bce">
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0e797371-7d16-4c98-b98c-1ad01c29a4e2">
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1f09347d-d157-420b-a132-4a2adecd4a40">
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="714484f3-79c8-4f7d-84f3-f3ada1cea6fb">
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="67b6b715-a40c-457a-8dd4-ac0d6fc2b438">
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d9c282fc-4221-4d66-b89b-ec1f20785499">
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="86269d21-a56a-4433-a224-cf62b6ed7762">
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="961680d5-6016-4bab-8a4d-7a842cfdffd2">
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a480912e-9f6b-4e09-8104-080f67064bc1">
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a818e156-7959-402c-8e11-74b4c909016f">
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="803a2ec5-0008-44ce-a894-858019c367ea">
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a6ab3d3c-8846-40ba-a2ad-492f6c5284c1">
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="122b0c7b-e083-43ff-b4d2-03db5adc77da">
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a6165eed-9640-4a29-ae79-856702e2fdfe">
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0c3dc958-1abb-4d17-9735-38b0185465dd">
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e441a0e4-09eb-427e-97e2-9eeb5d91bfba">
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="33df20e4-d6b2-4df6-91dc-3601a9305d4e">
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cb76bd04-2522-4b22-b45d-e3eb781d91ca">
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="79ab31d2-f34c-4917-9230-4203ea2004d9">
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c4abb4b6-fbd2-43c5-8cdb-5f5ed3cd2dbe">
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b71838ec-ee83-4099-bedf-abd35c8ba8c0">
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8866e6ac-a2c2-4665-bb11-a27a8758254b">
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7eed9b22-f4a2-433b-af43-30490b3e2669">
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="432bedc6-f7bb-4bd2-aea0-95ac7083c2d8">
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1c85f3cd-733c-4bee-a878-de88f740c333">
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ce522b3f-7f07-42aa-8b67-6758b9659dcf">
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a74dcece-7376-47a8-877f-f33fea87ab41">
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ae19d129-5814-4c5b-863e-************">
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e5ce3fb2-6f3b-4cd7-b972-ef7c8deb77fd">
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8a443f4f-a209-4da9-b619-f6f73b238863">
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cb0bedd3-4eb2-4c6c-aa13-3a90f9eb963d">
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="65b601b3-a9cd-4f43-a996-5bd8d769b211">
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e00e2e5c-a63b-4152-8910-3edb31c54e30">
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5d2eed5e-bfe4-4e4c-8773-cf8654b6d765">
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9899a2e2-869b-4568-ba60-55c1e47c8dbf">
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a68d348a-253d-409f-9154-0dbd973bb488">
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1499f782-df54-42ad-9961-5c06dcb5fd47">
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="66e03d2a-7d16-48be-9ebf-f1f55f76a86a">
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="*************-4c39-855b-297d794b82a0">
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2c2ec2aa-0c8d-494f-8a4a-51eb61023f57">
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fd6a55dc-b655-4b57-b04c-10aea0f40390">
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0f9a1acc-ae70-48f4-b996-05bb13d6df55">
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f1a8ad9d-754d-41d7-99dd-3bbfb037d4f1">
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c2bdaa20-2504-4eaa-919c-df260cb65e7a">
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="10c407aa-f67b-4688-8f6c-59a98d30dc8a">
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f8eb62e7-c4a3-4f2f-8f13-f048fd45ef3d">
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f9e0449d-6227-430c-ac22-9739f96108d6">
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c2e82bea-278e-4680-be4e-6400c94455e7">
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="409fdf5f-3cc1-4966-9356-6420d17120f8">
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9ab6d690-0dde-4af7-a0f8-e3ce72a7eeea">
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="73d2de14-f115-4988-8827-2a54deb54509">
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0ee69af7-0ebe-4fda-8460-f6d906f6a749">
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d767956a-0d9e-4bd2-9885-0940d08f2fef">
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="38defc41-db12-4b47-ba51-2b595988b76d">
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="62513da6-8891-48a4-b801-ff47793b7e33">
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="767a7343-df4f-4b10-83c2-71fc353aec76">
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c9fb282a-fd6e-4295-a707-ccef5758ed3e">
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ae2828b9-e6cd-44d0-aa69-820735a1521d">
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1214dad1-f111-4e78-b3f8-b237966d90d4">
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="65dbf81c-de9e-4fb9-9ba7-c9fc6e21517a">
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0a929b9f-87d0-4e28-9ebe-1740fc42fd3c">
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6df9d4d9-b406-4075-927e-bdddeba0a4d1">
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1de8e48c-7551-41e0-a5da-9a814df41747">
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c13a75c4-f085-43ed-a425-0b52729774ed">
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="48fc3265-2dda-4deb-b481-b4a00209297e">
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="16499851-b4ec-4b8d-9ef8-f9faeb81160e">
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4fb44d2f-79e0-4927-9559-3b5522b4e2e0">
      <grAndCompPorts>112</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e4a59b15-3207-47a1-8347-548aab4efb0b">
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="30f00c6d-0ce8-45d1-9387-21e56f095caa">
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e71f5cef-e209-4327-9a11-11d2967f01b9">
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f7ec4194-6c4d-4c5d-b5d1-d61a98a1ba0b">
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="65ff7c70-596a-4d1e-b71a-a4afe246f075">
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="06fe1d83-8f96-4e3e-b1e5-81251b56fbcc">
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a937181d-0f62-40dd-8825-3623b5d08469">
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c47e7494-**************-1caf324cf743">
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6cb96e06-8a3d-455b-873f-97ff34013e56">
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f973f531-4570-452a-b560-d9a8a249683b">
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d54c30b6-fbcb-406b-ad2e-6d3a565d6108">
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7d651ba2-5c0a-41c9-9e39-db0cfc1f57bf">
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4a3bba5e-50c6-4887-b94a-0de8bf3db04e">
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bde9bc7a-f431-4c72-ae23-5a72474bd829">
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6c7f0411-7971-49c5-94a6-29d478480609">
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4a064aea-5e97-4df0-a476-0dfc43abe174">
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a5487377-721c-468f-ab92-78f41b845b07">
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9a256002-9281-4d11-ba9e-8899eef7c8c0">
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="49610fca-95ad-4084-a2c1-eb1171b53f0c">
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0f5860e2-9379-4dbc-bd39-43ec6d5205f6">
      <grAndCompPorts>132</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5cac03e6-189a-4883-9119-4085f0cb9421">
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c4b2d3c6-9728-4da0-bde0-58996b85f43d">
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="31d8c880-69f3-4c7e-89cf-e6b5166ef801">
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4a350185-f561-4d60-a2ec-b365d8774f8b">
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a7a77d64-ef18-4bf4-8a7c-bd94eafc413f">
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="52746360-1c56-498f-bcb2-3dc8d5c084b0">
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5aa7ce7b-0bdd-4b1f-aa05-d994f5209864">
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="64518c18-c021-4a61-af2e-083227623067">
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1c2caf1c-4b9b-4c40-aeff-87b06bf02472">
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d2c4b4fc-2581-43e9-8816-5dd0f83b096c">
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8111fca5-c4a3-4411-804d-dc3b2ef7be15">
      <grAndCompPorts>143</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b9dafef7-5e88-4bd0-8db6-3427c24448df">
      <grAndCompPorts>144</grAndCompPorts>
      <grAndCompPorts>9</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1418fb9e-02f3-4ed5-b6ec-7baded9f19d2">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compZcCacheNeedsReset>false</compZcCacheNeedsReset>
    <dataDictionary>CtrlLog_Mdl_Data.sldd</dataDictionary>
    <dataDictionarySet>CtrlLog_Mdl_Data.sldd</dataDictionarySet>
    <dataDictionarySetForDataTypeCheck>CtrlLog_Mdl_Data.sldd</dataDictionarySetForDataTypeCheck>
    <dataTransferInfos>AAFJTQAAAAAOAAAAOAAAAAYAAAAIAAAAAgAAAAAAAAAFAAAACAAAAAAAAAABAAAAAQAAAAAAAAAFAAQAAQAAAAEAAAAAAAAA</dataTransferInfos>
    <defaultsCMapping>{&quot;Inports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Outports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ParameterArguments&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;LocalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InternalData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedLocalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Constants&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;DataTransfers&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ModelData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InitializeTerminate&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Execution&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedUtility&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;}</defaultsCMapping>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="0b065679-1b02-4965-9fbd-00a61c16c341">
      <blockName>CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Unit Delay1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>7</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="567ded5b-351f-45da-9a44-a3f17a1345cd">
      <blockName>CtrlLog_Mdl/ControlLogic/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>15</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="8ff8e8d7-f097-4937-9b6e-85cb9614819f">
      <blockName>CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>4</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="7e905d7d-e543-4398-a120-f5204f40a2ea">
      <blockName>CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect
Increase/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>5</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="9e9ba7f3-6c81-4d0f-ba65-6b875de1e52e">
      <blockName>CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect
Increase1/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>5</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="31d054ef-ac08-469f-8710-e0923d6e63a2">
      <blockName>CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect
Increase2/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>5</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="c515ae04-af12-4a18-b055-55a1cd2cbcbb">
      <blockName>CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect
Increase3/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>5</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="38dfa9b7-118b-43fd-abcd-8ec40d9f2582">
      <blockName>CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Triggered
Subsystem/Detect
Change/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>3</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="e85f935e-323b-4f7c-8f7c-a140c6bf972b">
      <blockName>CtrlLog_Mdl/ControlLogic/Unit Delay2</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="41df9d6b-6c06-4b43-a830-78209a4d6bce">
      <blockName>CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/objectDetected/Detect
Increase/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="d834885d-5ccd-46e4-bfe8-cfeca2159207">
      <blockName>CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect
Increase4/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="6f0a76d4-92f9-4430-82f4-bfce77dd53d3">
      <blockName>CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect
Change/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="7c3de62e-6cf4-4049-a3a6-b305e5b4de42">
      <blockName>CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect
Increase/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="1cf34125-6bab-4ef3-bcfc-fc29cb33c9fe">
      <blockName>CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect
Increase1/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="d4b177c2-52e3-4787-9b0b-a025909c3fae">
      <blockName>CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect
Increase2/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="60ce8262-2dba-4037-b40b-bd902c770dcc">
      <blockName>CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Detect
Increase/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="e79d1c19-d72e-4adb-9328-67a33e013609">
      <blockName>CtrlLog_Mdl/ControlLogic/Unit Delay1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>16</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <fundamentalSampleTimePeriod>.2</fundamentalSampleTimePeriod>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="fad92867-01db-4a34-b5d4-0bf8b41eb961">
      <dataType>CfgParam_CALBus_t</dataType>
      <name>CfgParam_CAL</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="7476d19a-4cdd-4462-b9de-42529eb7c62e">
      <dataType>uint16</dataType>
      <name>PIDDID_hDiagToPos</name>
    </globalDSMInfo>
    <globalVariables>#ADCMonBus#ADCMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#APDetBus#APDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AmbTempMonBus#AmbTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#BlockDetBus#BlockDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CENTI_TO_MILI#CtrlLog_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#CfgParamBus#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CAL#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CALBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_MosfetLevels#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_hPosBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#ComLogBus#ComLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#Configuration#ModelConfig.sldd#</globalVariables>
    <globalVariables>#CtrlLogBus#CtrlLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#DIOCtrlBus#DIOCtrl_ExpData.sldd#</globalVariables>
    <globalVariables>#DiagComManBus#DiagComMan_ExpData.sldd#</globalVariables>
    <globalVariables>#ExtDevBus#ExtDev_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#HallDecoBus#HALLDeco_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#LearnAdapBus#LearnAdap_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MAX_PERCENTAGE#CtrlLog_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#MOSFETTempBus#MOSFETTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdlBus#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdl_CalcForTorqBus_t#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDIDBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_ADCMonBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_AmbTempMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_BlockDetBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_CtrlLogBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_DIOCtrlBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_ExtDevBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_HallDecoBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_LearnAdapBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MOSFETTempBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MtrCtrlBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MtrMdlBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_PnlOpBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_PosMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_StateMachBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_ThermProtBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_UsgHistBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_VoltMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_hDiagToPos#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#POS_REACHED#CtrlLog_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#PWMCtrlBus#PWMCtrl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PnlOpBus#PnlOp_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PosMonBus#PosMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RUN_FAIL#BlockDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefFieldBus#RefField_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefForceBus#RefForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RoofSys_tSmpCtrlLogic_SC#RoofSys_Data.sldd#</globalVariables>
    <globalVariables>#SWC_CommLyrBus#SWC_CommLyr.sldd#</globalVariables>
    <globalVariables>#SWC_DataHndlLyrBus#SWC_DataHndlLyr.sldd#</globalVariables>
    <globalVariables>#SWC_DiagLyrBus#SWC_DiagLyr.sldd#</globalVariables>
    <globalVariables>#SWC_HwAbsLyrBus#SWC_HwAbsLyr.sldd#</globalVariables>
    <globalVariables>#SWC_ObjDetLyrBus#SWC_ObjDetLyr.sldd#</globalVariables>
    <globalVariables>#SWC_SigMonLyrBus#SWC_SigMonLyr.sldd#</globalVariables>
    <globalVariables>#SWC_StateMachLyrBus#SWC_StateMachLyr.sldd#</globalVariables>
    <globalVariables>#StateMachBus#StateMach_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#SysFaultReacBus#SysFaultReac_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#TARGET_POS_INVALID#CtrlLog_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#ThresForceBus#TheshForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#UsgHistBus#UsgHis_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#VehComBus#VehCom_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#VoltMonBus#VoltMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#secondToMiliSeconds#CtrlLog_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#targetMovementBus#CtrlLog_Mdl_LocalData.sldd#</globalVariables>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9a7a9290-b431-496b-82b0-b2f367c5f0ce">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>34</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c9ece773-7ba9-4822-8961-e93694801235">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>35</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1489ae3b-6c31-4ea5-81ac-ac2912907aea">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>36</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7e622324-b749-4371-9414-4d039a555fa3">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>42</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ad90f585-823d-4052-83aa-c7a8695ab251">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>110</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f64efb60-3791-4877-a70a-7b852e1e8702">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>111</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f271aea9-02c7-4693-8ca5-7d5a280a704d">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>112</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6ca89202-85f1-481a-80fc-562664bbe3ed">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>113</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a3055afb-21cc-4c18-a163-a1f6a4880cbd">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>132</grAndCompPorts>
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>143</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a74b06a6-ed01-4c36-a073-48950854d4d4">
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>144</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompOutputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d9bef097-2024-4aed-82d5-2b6cb530bd67">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </grToCompOutputPortsMaps>
    <hasBwsAccessed>true</hasBwsAccessed>
    <hasBwsAccessedByAnyModel>true</hasBwsAccessedByAnyModel>
    <hasConstantOutput>false</hasConstantOutput>
    <hasModelWideEventTs>false</hasModelWideEventTs>
    <hasNonVirtualConstantTs>true</hasNonVirtualConstantTs>
    <hasStatesModifiedInOutputUpdate>true</hasStatesModifiedInOutputUpdate>
    <inlinedVariables>#CENTI_TO_MILI#CtrlLog_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#MAX_PERCENTAGE#CtrlLog_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#POS_REACHED#CtrlLog_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#RUN_FAIL#BlockDet_Mdl_ExpData.sldd#</inlinedVariables>
    <inlinedVariables>#TARGET_POS_INVALID#CtrlLog_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#secondToMiliSeconds#CtrlLog_Mdl_Data.sldd#</inlinedVariables>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="de654762-6091-494a-a8cb-e3e54fb6f4d4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d7298984-50a3-481b-af57-1c0f46b06f3d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="63d05a82-853d-4a63-9b00-73505db0fd71"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="691697ab-923c-48e0-92be-ab0292f379fe">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c476eb0f-37ff-480c-8185-e140c3fb6620"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a111fb07-007e-4a43-80bd-87989a3d8d4c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4a1fb526-f395-4b74-a335-40129f85c099">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f2acf6e2-2eb7-4f5c-a4a3-a7917d2f503c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="476867d2-4df6-49b2-8ef5-6c0abbd92beb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7373eb3d-9e99-431e-8eb6-9ee169d55932">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dff3c299-b995-4114-8d4c-50f4f761f377"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8d86f055-2cf1-4dce-b83b-0eedb8df2a3b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="76d5a78c-cfeb-46ef-941e-1cafe08903a2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ca7967b7-419f-4d70-a254-6bd582ea7f8a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0c72c822-70b4-479e-bc7f-55f9f2d0d87f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ceb81f07-f18a-4e7d-bf77-0b82fad1003e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="eaffaf6c-92b9-4fe3-8aec-001caddafc1c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c2b8429d-f0d4-46d1-a143-03f76e772217"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6d37bd24-6406-488b-9db3-b850d1a46c87">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0c5e4316-a5f7-45cd-86e3-a67527aa80c8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="00816e00-fff9-4b16-98d4-48e7f79ffb0b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1511f192-5cce-403b-877d-00ef7b28770d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c96f304a-9e40-43af-9e25-58699f1c6404"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="83c8c8e9-62f7-4b8d-bbe8-51276a0a2aa4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6d42f345-0428-4015-8abe-2105f33ec918">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e361d812-a007-4719-856d-a6cafa901f96"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fc6a7098-3771-41c1-875c-87a1c83178f7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7abade78-81a2-4c80-8192-ccc991ea9b9c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="572ccc24-f99a-4500-8b15-e7874670d982"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="54e60623-4366-4af6-819e-caebf2212aa3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="955f0af7-ac74-4c33-a438-2c68901493f8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2516ea28-6c46-4097-89c2-6c0d44451740"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="abffcfaa-1e54-4784-a065-4c702798f1c1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="92987916-8c5c-4e07-8553-ba823198c7a7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="abe9d35e-e25d-484d-b13d-37b179b55e4a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5c062606-8d71-409f-a686-dfe34a90c1ae"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8a9e6eb9-ab9a-4f73-bb90-95b569608b32">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="656ead66-f893-438e-abb9-9c43cf273398"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c09a0b15-d55a-4706-9e41-51a722ee592c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="aff7b681-0204-4975-bf26-1fb56c697884">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9a1ea627-b731-41dd-9414-84f09b6137db"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="96a5c637-2cc5-4ec1-858f-7ca0122bf72d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1f4734de-ba1d-44be-a6d6-b8d4f9eb05f8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="afd85d05-fa07-41c8-aa0b-a016849abb3b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4be458e6-81a4-49ba-872b-58d5f0a6cae5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="73a0656b-6f69-405a-955a-52f3b2f74744">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="18135239-de91-411f-bba9-0b1d3be4ccd0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9fa85707-b5ec-4239-accb-b22d4f477161"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="681e9650-69b7-4383-8b3f-425a193bbcff">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="19e92a20-72a0-474a-b28d-0e7db57686e2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f8169408-c0bc-4b66-b70a-09c78563daa7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2a19a802-497b-49c5-b6ec-0eedc0f24d7e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="abed498d-3507-489c-8fbf-7a603f97e9d5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0b8d6869-14b5-4c2a-a8f2-7743791b9907"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fb93a8d9-ea23-4018-8233-a94ce02a62bd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="41a153fe-d3c6-49b6-a60f-e54299814e69"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cf3b2911-c6f4-47bb-84d2-8e390c2605b8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b9679e2f-1cc9-46d7-81cc-384683c28bc6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8c31a36a-b16d-4081-86d4-698a30f1587f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2f963ef4-c96a-4477-afaa-191ee09e05bf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="932bf8e8-a947-49df-9afb-5b2fb21e9dc4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8edcf071-29bd-49e5-b9aa-c99c10abd891"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a63acd4a-3c1d-4b40-b30d-c983371bbf27"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bd505988-5e19-4de3-a60b-1e3192270a5b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c9e6c955-11c6-40fd-8a7a-ee4c88cd66b3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3f2c81f6-2afb-4d29-8f13-9c12a8b73505"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9f71ad2a-5a04-4d73-8743-d5216d231ff1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fcfcd468-5725-492f-a7c8-34bc3c5a7c34"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a077e43a-45fd-44f0-942c-d5527a03cf93"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cff59e4e-13d2-493d-8bc8-fa3c61c0bd36">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7812f422-5d8d-43d3-aadb-6bc1e905fc65"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d7af60d4-eade-42c5-9439-b3365cd1e27a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3654c244-c748-4df3-8929-bafe969fda9d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="49db125f-1fc7-4092-bb0d-55bd9b85fa57"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8ccf22ac-04ce-4095-a5b1-701814fb64fd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3b3d5ab9-a4cd-41cd-8870-8564452c80d2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9b36c147-2dec-4e2e-8e96-2f41cccabab4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c4fc8091-061a-44a5-a2ec-e3087a8dd172"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2502726c-60fd-446d-9580-5b0aad94973b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9bf88a5c-f0f7-49cb-93f6-4fb4bd6a93a2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7c0cd184-2f7b-465f-b604-d1de52b942f7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7c489b33-0626-4cde-bcd3-96ba6520131d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7cda3746-2507-444e-9e82-7b2ae71099f9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7dce95d0-ae70-4f42-8a65-9dba4d32daa3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="df2db98b-6054-4d24-bdee-3bedc6c2ff5a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="250c9dcb-b31b-4178-8958-f702d1245755"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="80e2c7fa-2ffa-4ba4-9bb4-2d7f66260e9b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d1415d84-a414-43cd-8136-7edadf6a027a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="574e0178-78b3-4b84-b667-5c95ad627bc7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0474acd2-1fcf-48cf-9133-f4a1eaf83e3c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="08af024b-4284-4c49-af8f-1e54bd2246e0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e98c472c-1d4d-43a1-92a7-821e9323fda0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="85b03b16-5819-43cd-a736-d587073d1aef"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e6e7a3f7-5d32-4bd8-a43e-d4bac8d777ce">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4db1e6b0-abb2-4110-af19-a3acd71f2e15"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="eab36938-7533-4969-8ee9-f8cc41457530"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="aae84578-0906-4584-a4f6-861896218b59">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dde85664-e710-4fff-a88b-4dab03ae19e4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c53682a7-59ea-442d-98b8-6d39a1e4e38d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="86bc324d-8935-4acd-9112-9a065d9f5918">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="01349545-f9ac-4d98-9b3e-0587a8788f35"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f765c28e-8ead-4bca-81eb-476b064ab32e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2a400dc2-7632-4aa9-a774-2af0d7625fdd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="04c9e0f7-2320-456a-82a9-95a99c55c2c6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ef1f89c5-5d3c-480f-b015-7b38c2f35731"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fe958548-9546-4ec5-abf6-dc65ae77b6e5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a2029613-be44-4d83-b8fd-ea20a637213e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="eca5eeed-88d5-4aa5-992b-13b2edd467d3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6b4c2291-dc35-463a-b718-bc92b63cf3ad">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="edf23161-7861-47d7-8938-ea1cb1ae0dc9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7bf65ff6-917d-4114-8737-0a95bc5d8563"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5a20b37c-56c1-4f3a-b197-a9976d949bc0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="50b0132c-9368-425f-86d5-a070ad2b3ef9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0ab91289-6dc7-4db0-98a5-5615b67e5b62"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ffcee67a-f306-4058-805c-26be70e030f9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>100.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="414fbd85-d863-45dc-a447-4f737ec0ceab"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c73190cf-5a7b-43eb-af30-8cb895aa5dfb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b2ed2962-41e0-4548-89d3-5471fbd3c4bc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d00a1794-9083-44d6-8a62-3f69216ec20d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="af9a8f0b-b85b-434e-829b-64a07ac56e06"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d3302088-78ce-4e34-9675-8bc3da755d6f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8d0c9654-a39a-4214-970d-1d5040290da6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="01c1f486-cc1e-4617-aa07-df6760f52d93"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5d4f7f35-84c7-4139-804a-11b725968783">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>1.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8aa0c2ef-464a-4416-a99b-8cff142fb84a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="367dd1d8-fe6b-4878-8e35-ea72869e9b54"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4f6d641b-6434-4ddc-9c09-df939606d153">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3145e68e-0d8c-4087-a696-22229d2f3ed5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d3b2aedc-0df3-4100-807a-edbe8407b833"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ba3dedbd-7d26-4a12-9125-6ccf5591f8cd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9cf78acc-897e-48e7-9ae1-cdf4b9cb4056"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6e03c715-ba4e-43fe-9c05-6b362a1a661e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1a92d74a-314a-48b1-9f83-524a3487eaf6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="255f42fc-9d8b-4eef-b8ca-2bc4f2d2bbe3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e3532de6-b392-40eb-acf5-a7780bb84efb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a2a57ad9-03dd-43c3-a8a9-153431936917">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cb300ef0-dfdd-4fc5-a8b6-dd673fd9e5dd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="11b59792-cc71-451b-a44a-dee178d9a414"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b9283216-8a10-41f9-8273-73c717608513">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="92eb3fd2-3f31-4d16-a4c5-54ec3dc88cfd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="97d98200-2285-4053-b5c0-6f336706e2cb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4b180eba-e1f6-4134-9e20-5a29cd12042e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9e55b2a4-0656-4619-bc13-bc59e81957c9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c26cc186-05c9-4773-a426-abea3666cfd9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="903b5cbd-bbd7-48c0-bf80-871aa757d29b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="66fb0ab9-f0e2-4bdc-a112-ed5ac3761c56"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="42120523-ebb1-4b7e-80c2-1ce157065b7c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7ad8602e-aeab-4c58-987f-7bb2fafe4550">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dfa55a67-4a7b-4b25-9636-a0e6d34ced19"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bfea1cfe-9dd5-465b-ba80-30974c979c56"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="86577b9f-d4c2-4cec-a358-c5e0fe4f87fd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="83c47a98-10f0-41f4-904a-34e5d83b77c6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="49fc4750-7d53-454c-a7d8-14f53ec76a94"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2ea5d35a-c8ea-427f-827d-67daad893654">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3284973f-c510-4398-9e92-1224a2068059"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f261fa3b-b0e8-48d9-bdba-e045a61e3790"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f6b3967a-769e-40a9-9434-4edfadf0d04f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8009d183-dff4-43ab-8762-ff546b3dfdc8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3fc179d5-74ef-4c42-88cd-8936e0458bd8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7138ed27-8be9-4138-9213-e0805c313e4d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="12856bdb-c6d2-4f7f-b821-fdf4787fb651"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="47d5739d-9a89-48c4-ab83-e1e3d27639e8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="beb40e6b-a79b-49cc-8156-951abba2e515">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2f9cc53b-615d-4f7d-a099-fa47fc208e8b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="10bee019-8239-4db3-a8ad-47cb81cc2843"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="33b7719b-3b8b-4ba6-8a26-f7826386e2d8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b77d643d-04ee-4e17-a402-7a09bb316ef0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6e999423-0ad0-4d8c-866d-0baeb0f4cb0d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6324a10e-0656-4568-b9c5-e500798bac65">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1a2e0fc7-00d9-4ef1-8776-881f134fdf32"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0297e0a6-10c5-4443-a62a-2983579742de"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f2a4c06e-f128-4d62-bba9-51f99b61867b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6f7b9758-7842-45c2-9c0f-707d7ffc54ce"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="39c24132-d527-4ad7-829a-ff68a56c53cb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="02ba4a7a-96b9-4eaa-9d3d-bc5e772d13c8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="97d4e7a6-201f-49db-8dcd-45e27ccbebab"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4d841235-8ab7-453f-a3ac-b793d0961b45"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0e3288ce-a9fa-402c-92cc-559c07eca466">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="876e4610-d95c-4120-9e35-1af0e11fafce"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fa7c573a-e20a-454e-ab60-a137041c6218"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0995d120-b4dc-4291-bf0b-6d8fb16ce464">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="62342a67-8b9f-4dc0-a781-3e35fd1fe2c8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1345cff9-cab1-49b8-b90a-4ab02b934ccc"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c2bf4789-a541-4eb4-a3dc-8110f18b45fe">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5e1d8180-4216-409e-b8d6-bc5ef4214037"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6d2ecc17-f1f8-42f5-98ce-49151b2a5010"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7db1b0b9-bcf1-496c-87b1-82e592b3a029">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="22522d75-cb68-49dc-95a6-86bd31162140"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8d512b28-990d-44da-af2e-6e387d549c79"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3ae5fecd-c2a7-4691-b467-1371f4867540">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b10d7200-353b-4ee0-8d2e-2ab2fb978282"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8e3449f4-e321-4740-aadc-c756983a894a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6008a802-4615-4b58-9a5f-18d3db26400c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d885ebbf-4d66-4d47-802e-d56d16d0b0b6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="41a1514b-530a-4a8b-9dbf-bb02e2b41264"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0bd6ec55-8d5a-4eb9-b363-743e0898caac">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5b58e195-66a8-4211-a2c1-052ecc59607f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fccba78b-c5f1-4364-8e3d-94010d5603aa"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bb3bb19d-c8d2-499c-96c5-78987a4ba796">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="40c2985c-f813-408e-9b73-9d0766505634"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5ca47fa2-e7f3-4e2f-bfea-40d4bb4c466a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ba4d1348-069f-4161-bf69-9623991d13fd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9bfe2b89-efc2-4795-bf32-e3350492a2b2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a3e8c3e1-fb88-497a-92b6-c41322b87c54"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c2847d46-7797-4591-a5d1-2c4a7fe32abd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a71e085f-8f23-4dd7-9aa1-d8d6f2f6f080"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4bb7262d-c1d9-4845-b976-ad2db5e4dcf9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4ae347bb-5d43-4f14-b07b-e6633aab97a5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d48f4c2f-8118-40db-9c90-3db90b026848"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1c011e13-a3af-434b-9d14-318ddc307a4b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ed6e868e-6a25-43ee-a938-7dc5572ce2cf">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7b31b1b0-6280-4381-a2dc-a6fb9b361ac9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ced78a2f-3d86-46fe-91d1-764b2a421b24"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c576d565-4e04-4a51-bd03-3bae15f40a5c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ce61eab2-16f8-42ac-9193-14e2d6b482a1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="34fce564-e690-4156-a7cd-d4bb8f6b0f23"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c2987e30-7796-42c8-8303-ac4f316924b2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fabc8ac3-bada-4863-8cec-b55b01442af9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c4ba6a14-909c-4f61-9078-fe9211077bb1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="54aaa0ec-0734-4776-9292-1fc8c92c7f80">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b994e7ee-415e-47ef-890d-3c44183ddb05"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="25932d1e-d427-4958-a2a7-2e6d848ae1ff"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="49278bc5-797b-4509-80f2-3f4db2d3f836">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ff247bd4-bbb6-446e-bdda-4dcdedc3b2f0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d4b27081-0915-4196-abbf-e29589e83307"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="be31684a-38ce-40ec-a4b5-7391b558689a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="112d5662-3933-4a3e-b5f5-4ce0808dc1cb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c618f527-902d-4a91-8c2f-a961aa3ca7cf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dbf683af-7564-4ebf-b9ed-701822f6fac6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3f2dfcd3-8ea7-450a-ab29-8c1a158a2e13"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bf3cd71f-70ad-4b0c-848e-0805758169c7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b38b88bd-ce52-4145-92d0-fd6e49fe7adb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>16777215.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="29b25a64-5384-478f-aebb-69e92b6fd786"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="93276d5c-3b28-482b-b8b8-44d2aa0461a5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a3b8349a-e530-43b5-aad1-10396486c6fc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3b214afa-d6d7-4178-9054-74b31e84da23"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f1710b42-a86c-450b-9eaa-a5d3bac28adf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b021dbbf-6aa0-4e4e-abc5-a61e91706615">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d356048a-47c9-47cb-9b34-1b775c605671"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e26ad815-6f59-44f3-b5a1-d1b7e2285f54"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6f64f513-77b9-42b9-a3da-b9cbd8ff58b4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="055198fe-2dac-49f9-8afc-0774b14bedca"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e5310a1d-4aa3-4660-8db4-fc2525be2d52"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1a777999-17ba-4d49-8339-8ada531b21bf">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2ab94474-b88a-434e-90ba-67b9b6502414"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="94ea9b09-04b2-4842-99ee-018087d32bfc"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2cc8ac6d-0cd6-4c23-b657-c1c4979e2c4f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f7b9f2fd-f65b-42a1-9b1f-7a36e658afcb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="65ea2a90-6de7-4f3b-bcbc-00918d508bd8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="904da182-c116-4f20-9231-6bcb66c5446a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e0d3f1bc-74a0-40de-969b-b6f742e6469d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="257c79b7-accb-4289-ad80-40624cb98961"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6c0c4f70-58b9-4a28-8518-e4084abd0c51">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="55f49b2f-4fb9-4176-952d-0a2777c63c53"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="213ce9b3-01b8-4ce7-8430-a7f6c1d5ff36"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="403a7328-b1a5-40db-95e3-23aa8ab089e5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9dc23403-d4e1-4872-8f78-d39f328e2851"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bf49a59c-fcfc-45d0-83f8-44de7e307267"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="49e68505-b9d9-42b8-a7f9-334f02505019">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5e246126-df76-4c49-89f3-60fe7ebb0df4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="aded5663-da25-4f81-bb56-e75d676c0929"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d33add3b-5371-4e85-b627-f475a85b1d3d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="31cf7edd-3e95-4341-801f-60e8b63c8bee"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="55ea2500-5a7e-4611-914f-2a56880df73a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c879dd58-a315-484c-a6eb-0cf9b2a8beac">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f808b348-91c6-4b37-b488-5b29c9d1bd76"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e4d1f6de-93fc-4c0d-96b0-69c67a38650d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d3a4dd60-a688-49c2-b42a-f246216196f2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2da38b92-ec7d-496e-9b9e-c7655f07d77e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="169ea6d0-836c-4c93-a9b6-cda53197385b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="248821ca-8b2a-48ca-be3f-f20bdddda07c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1767553d-c93a-44f5-8fc5-a5a6d4d93c79"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7b4f29a9-d26f-4e98-9604-fb7e57c95f5e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="965d80a2-76f7-4058-9416-35e97d017401">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="394552f9-6d0d-4ded-ae06-dad9d84a902a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e4dcd1e9-e708-4121-91cd-09aafee01b9a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="18766643-57a3-4cea-a32b-b1ef321bb394">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0a512600-5ab2-4337-b5d8-809c94a43c96"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b2920594-dbcd-4737-a131-2861faedc93e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5aac403b-45ec-4741-ab26-ad0cd1c51325">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8cdcbb63-ebd8-410a-93b4-b2649778796d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="695b8e4d-5282-4972-ba68-92bef569fb56"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e1343a80-ea0e-40ef-97bc-cdbfad6e6fc7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0c937598-7940-4fff-963f-39b11461f4a6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="46c16aea-9005-431a-ae0f-ba3bd8dd9596"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ca9434ef-1b72-4025-b029-d3853f0a4b94">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="924e12bf-56b6-44e6-9920-66053052e183"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="566720fe-8219-47b1-b0a5-badc4b0df0a9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="539015ac-3c75-4123-b62b-132ecfa3d757">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e20fdb91-cf6d-42fe-8f76-08e55d86e4eb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="45b3002b-decc-490e-b544-14a516f8fd08"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="70877194-382d-4ea8-95e6-88920a054ad9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="35bf2d2a-c537-4f03-a0f1-e57fd5b53d42"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="08b6098e-84d6-425b-9360-a00f2a302454"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fb705ed2-5557-408d-8783-c48e3040a9a8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e5de27d9-d8cf-45bd-859c-790f31581929"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="309ea883-a2c5-41b2-b164-6f22a94828dc"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="51424fb8-217f-4794-84fc-676db709a0d4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="69ffdb2f-09ea-4f16-bb21-c0f4062fe4bf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="903a176e-8398-4098-b882-ea4aee0d5536"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="455a8cdd-42b7-4576-8281-10c5258147a9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="196c32c0-6642-47c7-8ea9-58fb736cccca"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f863014e-d26c-4a26-93b8-46f3c4b9d848"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5bf7d640-248d-4bef-983d-fbe39abffb6f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ef446d58-982a-4296-bdef-37919eda99ad"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="20fe575e-35ed-44fd-91de-ba5f9ef305d9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b7406c66-3930-4396-8aef-312cbeffd6be">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="717bc0c6-1b06-4db5-b4a5-c1cd18582d05"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="853fd010-1d1c-4a7d-97f4-71f337e5cded"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0198af4f-b509-401e-a636-99aed98717b9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3a103f08-e593-4b7b-981a-0a58de52d3a5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="51f8c240-6bfb-4dd9-8b43-368e1aa2337f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c9c0d425-69c8-4ee9-a90d-de264e25b173">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="121c173a-d980-4044-891d-4b6a52392bbc"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e022b21a-60e3-4e66-8d13-3248906ce535"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d7354922-3b1c-4668-a949-eb914ddcd2ed">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f7770f4b-735a-43a6-9319-5979b2a47075"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="501b6ee7-2cf9-493c-9543-57d9d2f0414b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7626c248-ec8d-4b96-a881-988f224bfa7a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fe63aab3-55bf-42f8-a1e7-05054665eb28"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a104b6bf-a813-4f9a-8ef4-07edb64185a7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8506662b-94e2-486d-98af-d85268042089">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fc9afc22-52f2-47d3-bfbf-eb92df442cf4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="edf2c389-42b8-45fa-bb71-b8e6b591f142"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5c6ed392-52eb-4e14-8792-0eac91ef08fa">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4b7ec049-a534-4358-a14d-18da40a8581a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="67fe0ae0-5f64-475d-8f9b-1c75b2e31865"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f2f76fbb-5fbd-40e6-8a0c-28f93137dcd4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="81ae83b6-b23e-4bfd-96df-937158590acd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5b690578-f513-405c-993f-ea34ee88f453"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="eabe183f-d09c-4360-ad8c-6c4f54543bb5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="478db521-81d9-4b62-98f7-f3438564cb21"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="71a730b3-fcd4-43fb-857a-7bbd7d690b0f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="23c1f5ae-9274-4cb0-9d5d-db93b9f31be9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="35bce80c-d7de-44a3-ade9-e4250d323c20"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c1f13a65-7410-4a8b-936e-025c0778707c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6417e68a-3e25-4282-8d31-af133fd7f4fd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="70770fdb-e8c9-465a-85b2-64b8c609145a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f1da2369-c1d2-427d-86f4-054bb5bf7de8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0a095de5-2843-4efb-9004-c42baac4f248">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>7</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="077aa9e0-25de-4a20-8e9f-1718b3e9d203"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1a0660d2-52c7-42bd-aff3-5f9b77d981b6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bbf44b11-58f9-4ab9-aa70-37574956fb14">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d78a3b84-fe8f-4e38-96f8-9964623a333a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b32f2689-c437-413b-8acd-ad2905d07ecb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3416fbc0-8dd6-432e-a8b8-a154c170d891">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cf7304b8-9944-4fd5-9e35-a350be2fa1ea"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c577e8fb-4532-486e-ab52-2368eb3f2fd7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="04d44354-c359-43b5-9d11-c0154a21f28e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>1.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b499ec46-30b2-4d13-b027-d47105affbd8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7e49b675-71a5-48f9-8f1c-1ac077b5bf1c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4958938b-d630-4bbf-a72e-dccb0c7380e0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2fc87151-c254-485e-9039-5b45793b7452"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="149835be-baf5-493b-8135-b96c9adf926f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="316f5f93-64c6-4ab3-b6a2-115d7d07346c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7e9a4986-ca9e-46b9-b607-ef98fa24540c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4d72666c-6e76-4ee1-ac1a-1fb21b2cb036"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="faf28d30-8ec6-4499-8da4-7538f10a871e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c2fce880-6315-41f8-92c6-a61059f52dc2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d3bcd36c-673e-4fc9-b00a-1ba09c380520"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b472004f-c80b-432f-9c99-6304a83a3820">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b1af945f-68ad-448e-9177-5e95996180e5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7b82c059-aa00-46a4-9c24-76b11b57f3f3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2878452b-cbef-484f-9241-5934d6ca4852">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b06f9006-e859-4acc-b0a0-d7748e769e44"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="45fc29ef-0cb8-40d0-8da3-51d5089f169a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1404740d-2e7e-4010-85bf-ede393758de9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="decfa963-82a9-442a-af08-5e7597be6e63"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="89cfa113-43ae-4f3f-87e9-31fd4923c733"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="38a55d99-5042-493a-bf48-3793949c419a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d9d5329d-dcd4-4f19-ab78-f2d270ae6f04"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f4ad620f-bef8-4457-a6e7-266fa97dcc4a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c3f57a1f-1eb2-4223-a499-eb7dbe92f166">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="da7f81a8-eaba-4233-99e2-fdec5bb82907"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="036b141c-e4ee-4322-a7cd-21eec49b5d41"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="72b1a5e6-40e0-45b8-85d3-8f5f820d0131">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bd4e8743-b0c5-49e3-8997-2a0a92f02e98"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5112e4f1-15ae-429e-a8bc-8c09a2119b63"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2e880e2b-34a3-4cce-8c34-bb38b899b8da">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="39fe5404-e702-41cc-9e0f-3cb1f6219fc1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="986e025c-da60-4f3b-95a0-255d8e020ca6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="70fd7ae6-33d8-4d35-93f7-59b6ad24957a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1fe87629-8972-4460-80f0-1c29eb842031"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="718ba557-459e-43c8-a5b0-eb6e5ef1f50a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="db9f4b5d-5525-4156-bc63-3b2bbb1c4c78">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="238b25a9-23e2-440f-9ff9-5e6fc6fe1038"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f486d95d-32b6-4877-a6fb-522082b8a81e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d7fcfa2b-cdfe-4d8a-9591-1943f94da572">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e87745af-21ba-42ff-aebd-0b458680b6b0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="708ae3a3-8f8d-41d2-9093-762a19ce3bd5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4f980b60-d704-44ee-a292-5137a17722f8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="045ded48-1436-412e-b2bf-cf1c36a874b1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cea780ed-3c34-40e0-bec2-1d84066eaff8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="62922663-244e-42e8-8586-53818cde62ea">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="822dceff-d041-4cf9-97b9-c73036eb3a94"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4b5e573a-8570-4eb4-8a54-cafe237afb56"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4e5955e5-e966-4317-b16f-1f3d04c0cb00">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c7d1f0e6-e423-437f-aceb-ae94fa68421d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dc5bf0e4-c031-40ba-9c0d-ccdbbc781c02"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4a0605e8-d229-4ed2-9d45-5cc4471044a3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="73c84abb-43b7-4dde-a575-34fc869c80ae"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c28263eb-9a93-4813-aa07-b1b29e202510"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0b41e9d5-8026-4222-b94e-aa70e7a5bb6d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0875bc10-0d64-4d23-8d2a-8076ecfbfd56"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b5c2d182-2abe-41e7-9a61-8a82c1b2e232"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c63934da-6dbb-4831-86c5-236472edd5d9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c39cc8fa-07d2-4d95-bee4-9a802a5c0f7d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0798307a-1636-46b4-bc16-ab1930b593b6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b946bc2a-ba96-448e-bd39-d0fc674b2744">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9ff8c782-2c0d-4bcd-9317-dc06beed9c63"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ccd205af-02eb-4f69-8245-70492f4e671a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2f5b8c28-9786-43b3-99b5-30aa0e41d60e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="44fda65e-5797-4398-ae92-c8f1d8139e1e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d12ecd9b-060e-44fc-ab7e-38ad3770b7f5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bc2e5bfd-50ee-43ec-83d3-282078ae65dd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1c6cf6c6-6c4c-43b2-9f59-b1cb53f8c782"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e72da129-2058-42af-b7d3-cba2082606d9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0b2f13b5-3255-499b-bfac-8f655d5e245e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7a24a5c8-7395-4b12-aeef-b3a31dd42bd6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2fb75496-76e7-456c-8faf-37275ac6bef7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ef01998b-35f4-4826-93a3-179e9c3d3aa2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a2501e34-9d99-4bf8-af3e-dba00d8f4422"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c59eedcd-44cf-42de-bf87-199b83640faa"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="29e744d5-b8d4-4065-b2a0-f5f4f06ad51e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a97b7b65-e769-42b4-aa4a-b670caaa4592"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3ff8ca29-b415-4b30-bfbd-4195789182a7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dfa7c02c-942a-4588-a466-908975cb17c0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6a14f267-d6e1-4d7a-90d5-27b7a51d07b8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="750634c9-fe68-43aa-95ef-bf6ba12f759c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7e856c13-1acd-4627-b286-e96ce7dfe0e2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>8</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4e5b8b33-d064-4919-ae9e-b3e0cf3c73c6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1184dd1a-edef-450e-97c0-87076d57f2ce"/>
    </inports>
    <isAperiodicRootFcnCallSystem>true</isAperiodicRootFcnCallSystem>
    <isBdInSimModeForSimCodegenVariants>false</isBdInSimModeForSimCodegenVariants>
    <isInlineParamsOn>true</isInlineParamsOn>
    <isNumAllowedInstancesOne>true</isNumAllowedInstancesOne>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>false</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>false</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>false</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>false</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigOutportVirtualBus>false</isOrigOutportVirtualBus>
    <isPreCompSingleRate>false</isPreCompSingleRate>
    <isRefModelConstant>false</isRefModelConstant>
    <isRootFcnCallPortGroupsEmpty>true</isRootFcnCallPortGroupsEmpty>
    <loggingSaveFormat>2</loggingSaveFormat>
    <maxFreqHz>-1.0</maxFreqHz>
    <nonTunableVariables>rtw_collapsed_sub_expr_29</nonTunableVariables>
    <nonTunableVariables>rtw_collapsed_sub_expr_31</nonTunableVariables>
    <numDStateRecs>17</numDStateRecs>
    <numDataInputPorts>9</numDataInputPorts>
    <numLoggableDStateRecs>17</numLoggableDStateRecs>
    <numLoggableJacobianDStates>0</numLoggableJacobianDStates>
    <numModelWideEventTs>0</numModelWideEventTs>
    <numPortlessSimulinkFunctionPortGroups>0</numPortlessSimulinkFunctionPortGroups>
    <numRuntimeExportedRates>1</numRuntimeExportedRates>
    <numTs>1</numTs>
    <origInportBusType>SWC_SigMonLyrBus</origInportBusType>
    <origInportBusType>SWC_StateMachLyrBus</origInportBusType>
    <origInportBusType>SWC_DataHndlLyrBus</origInportBusType>
    <origInportBusType>SWC_CommLyrBus</origInportBusType>
    <origInportBusType>SWC_DiagLyrBus</origInportBusType>
    <origInportBusType>ComLogBus</origInportBusType>
    <origInportBusType>PnlOpBus</origInportBusType>
    <origInportBusType>SWC_ObjDetLyrBus</origInportBusType>
    <origInportBusType>SWC_HwAbsLyrBus</origInportBusType>
    <origOutportBusOutputAsStruct>true</origOutportBusOutputAsStruct>
    <origOutportBusType>CtrlLogBus</origOutportBusType>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="e8c66777-38d6-4606-9264-69743b5d4314">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>CtrlLogBus</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="6eb1a117-46bb-45e0-96a7-7f01a45e41db"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d57ef77b-68aa-4b01-a6b8-e8fac61404e8"/>
    </outports>
    <preCompAllowConstTsOnPorts>false</preCompAllowConstTsOnPorts>
    <preCompAllowPortBasedInTriggeredSS>true</preCompAllowPortBasedInTriggeredSS>
    <removeResetFunc>true</removeResetFunc>
    <runtimeNonFcnCallRateInfos type="ModelRefInfoRepo.RateInfo">
      <compiled>true</compiled>
      <isEmpty>true</isEmpty>
      <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
      <period>.2</period>
      <priority>40</priority>
      <rateIdx>0</rateIdx>
    </runtimeNonFcnCallRateInfos>
    <sampleTimeInheritanceRule>1</sampleTimeInheritanceRule>
    <solverStatusFlags>331</solverStatusFlags>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="086a27b4-9a7b-4108-a458-08cef1e97282">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="5cd93ccb-32a6-47cf-8022-5dc999c408b6">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_MovType_t</enumName>
      <labels>None_C</labels>
      <labels>Manual_C</labels>
      <labels>Automatic_C</labels>
      <labels>Learning_C</labels>
      <labels>AtsReversal_C</labels>
      <labels>RelaxOfMech_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="fa03b24b-5d55-42f2-a677-************">
      <defaultValue>None_C</defaultValue>
      <enumName>VehCom_Cmd_t</enumName>
      <labels>None_C</labels>
      <labels>Stop_C</labels>
      <labels>ManOpn_C</labels>
      <labels>ManCls_C</labels>
      <labels>AutoToPos_C</labels>
      <labels>AutoToPosStep_C</labels>
      <labels>StepOpn_C</labels>
      <labels>StepCls_C</labels>
      <labels>Learn_C</labels>
      <labels>Invalid_C</labels>
      <labels>Learn_WOPreCond_C</labels>
      <labels>AutoHallToPos_C</labels>
      <labels>ManOpnCont_C</labels>
      <labels>ManClsCont_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
      <values>10</values>
      <values>11</values>
      <values>12</values>
      <values>13</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="e679fed6-0205-4a20-a7a3-2a704de4af81">
      <defaultValue>NoStall_C</defaultValue>
      <enumName>BlockDet_Stall_t</enumName>
      <labels>NoStall_C</labels>
      <labels>IncStall_C</labels>
      <labels>DecStall_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="bdd1d227-cea8-42b8-b6a3-e9cfedd893f2">
      <defaultValue>Idle_C</defaultValue>
      <enumName>LearnAdap_Mode_t</enumName>
      <labels>Idle_C</labels>
      <labels>LearningPos_C</labels>
      <labels>AdaptionOpen_C</labels>
      <labels>AdaptionClose_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="4d0a04f7-eedf-4d99-b79f-7b5138928c78">
      <defaultValue>Init_C</defaultValue>
      <enumName>VoltMon_VoltClass_t</enumName>
      <labels>Init_C</labels>
      <labels>VoltClassA_C</labels>
      <labels>VoltClassB_C</labels>
      <labels>VoltClassC_C</labels>
      <labels>VoltClassD_C</labels>
      <labels>VoltClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="80472eca-3bb0-4d4b-8d17-42551f91e7e8">
      <defaultValue>AreaSlide_C</defaultValue>
      <enumName>PosMon_Area_t</enumName>
      <labels>AreaSlide_C</labels>
      <labels>AreaVent_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="48e17c78-a805-43ee-9980-69bdb72ca0f6">
      <defaultValue>PnlZero</defaultValue>
      <enumName>PosMon_PnlPosArea_t</enumName>
      <labels>PnlZero</labels>
      <labels>PnlFlushClose</labels>
      <labels>PnlVentArea</labels>
      <labels>PnlVentOpen</labels>
      <labels>PnlComfOpnArea</labels>
      <labels>PnlComfStop</labels>
      <labels>PnlSlideOpnArea</labels>
      <labels>PnlFullOpen</labels>
      <labels>PnlOpenHS</labels>
      <labels>PnlOutOfRng</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="bd5238ee-63be-4637-a8e7-7fd5f6122827">
      <defaultValue>Init_C</defaultValue>
      <enumName>StateMach_Mode_t</enumName>
      <labels>Init_C</labels>
      <labels>NvMInit_C</labels>
      <labels>Startup_C</labels>
      <labels>FullRun_C</labels>
      <labels>Standby_C</labels>
      <labels>Shtdwn_C</labels>
      <labels>FastShtdwn_C</labels>
      <labels>SystemReset_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>7</values>
      <values>8</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="3d25c6f9-8152-48e0-ac30-748890bdf354">
      <defaultValue>IRS_E_OK</defaultValue>
      <enumName>stdReturn_t</enumName>
      <labels>IRS_E_OK</labels>
      <labels>IRS_E_NOK</labels>
      <labels>IRS_E_PENDING</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="090cb60e-294b-4f71-b4a0-552b8486b90f">
      <defaultValue>Disabled_C</defaultValue>
      <enumName>CfgParam_TestMode_t</enumName>
      <labels>Disabled_C</labels>
      <labels>All_CW_C</labels>
      <labels>Sequential_C</labels>
      <labels>All_Stop_C</labels>
      <labels>All_CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="37166385-8c7e-450b-bf8e-5f825f836d92">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>SysFltDiag_LastStopReason_t</enumName>
      <labels>NoFault_C</labels>
      <labels>Debugger_C</labels>
      <labels>DiagReq_C</labels>
      <labels>PanicStp_C</labels>
      <labels>ThermPrtMtr_C</labels>
      <labels>ThermPrtMosfet_C</labels>
      <labels>RelaxMechComplt_C</labels>
      <labels>AtsRevComplt_C</labels>
      <labels>MovTimeout_C</labels>
      <labels>ClassEOV_C</labels>
      <labels>ClassEUV_C</labels>
      <labels>BattVltNonPlsbl_C</labels>
      <labels>AmbTmpNonPlsbl_C</labels>
      <labels>MosfetTempNonPlsbl_C</labels>
      <labels>SysIC_CommFlt_C</labels>
      <labels>LINCommFlt_C</labels>
      <labels>SysICFailSafe_C</labels>
      <labels>ChargePumpFlt_C</labels>
      <labels>HSSuppFlt_C</labels>
      <labels>VsIntFlt_C</labels>
      <labels>VsSuppFlt_C</labels>
      <labels>VCC1Flt_C</labels>
      <labels>RPInvalidFlt_C</labels>
      <labels>HallFlt_C</labels>
      <labels>SysICThermShdFlt_C</labels>
      <labels>MtrCtrlFlt_C</labels>
      <labels>HallSuppFlt_C</labels>
      <labels>UnderVotlage_C</labels>
      <labels>OverVoltage_C</labels>
      <labels>StallDurAtsRev_RlxMech_C</labels>
      <labels>OutsideEnvCond_C</labels>
      <labels>TargetPosRchd_C</labels>
      <labels>PrevReason_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>16</values>
      <values>17</values>
      <values>18</values>
      <values>19</values>
      <values>20</values>
      <values>21</values>
      <values>22</values>
      <values>23</values>
      <values>24</values>
      <values>25</values>
      <values>26</values>
      <values>27</values>
      <values>28</values>
      <values>29</values>
      <values>30</values>
      <values>31</values>
      <values>35</values>
      <values>37</values>
      <values>48</values>
      <values>49</values>
      <values>50</values>
      <values>51</values>
      <values>52</values>
      <values>255</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="129e236f-cb1f-4e4b-8a79-877367101c5a">
      <defaultValue>NoMov_C</defaultValue>
      <enumName>BlockDet_Stall_dir</enumName>
      <labels>NoMov_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>reserved</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="c4307049-960e-4ffc-a239-d8e1ce7da1f5">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MtrTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MtrTempClassA_C</labels>
      <labels>MtrTempClassB_C</labels>
      <labels>MtrTempClassC_C</labels>
      <labels>MtrTempClassD_C</labels>
      <labels>MtrTempClassE_C</labels>
      <labels>MtrTempClassF_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="2dc4f14c-a138-42a0-a21f-ca47ae6bd17d">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MosfetTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MosfetTempClassA_C</labels>
      <labels>MosfetTempClassB_C</labels>
      <labels>MosfetTempClassC_C</labels>
      <labels>MosfetTempClassD_C</labels>
      <labels>MosfetTempClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="4c3b8bef-ae71-40ab-b13a-fd4b731be00f">
      <defaultValue>ATSDeactivated_C</defaultValue>
      <enumName>PnlOp_ATS_t</enumName>
      <labels>ATSDeactivated_C</labels>
      <labels>ATSAvailable_C</labels>
      <labels>ATSActive_C</labels>
      <labels>ATSOverrideStage1_C</labels>
      <labels>ATSOverrideStage2_C</labels>
      <labels>ATSDisabled_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="dede3b00-c7e1-4a26-adbd-82797211d730">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>MtrCtrl_MtrDir_t</enumName>
      <labels>IDLE_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <labels>SECURE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="901732cc-39f6-4c81-b21c-906d93ceafaf">
      <defaultValue>Idle_C</defaultValue>
      <enumName>CtrlLog_RelearnMode_t</enumName>
      <labels>Idle_C</labels>
      <labels>ReqLearn_C</labels>
      <labels>LearningPos_C</labels>
      <labels>LearningRF_C</labels>
      <labels>Complete_C</labels>
      <labels>Interrupted_C</labels>
      <labels>Renorm_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="fd07663c-cc3a-46af-a6f8-280d15461db9">
      <defaultValue>DirNone_C</defaultValue>
      <enumName>HallDeco_Dir_t</enumName>
      <labels>DirNone_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="aa5d139c-141a-43ef-9364-a01d0621df65">
      <defaultValue>CmdNone_C</defaultValue>
      <enumName>PnlOp_MtrCmd_t</enumName>
      <labels>CmdNone_C</labels>
      <labels>CmdInc_C</labels>
      <labels>CmdDec_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="bd7c8b58-3699-45bc-a162-a09f43fce4d9">
      <defaultValue>RoofIdle_C</defaultValue>
      <enumName>PnlOp_Mode_t</enumName>
      <labels>RoofIdle_C</labels>
      <labels>Moving_C</labels>
      <labels>Intr_C</labels>
      <labels>ReachTarPos_C</labels>
      <labels>ReachStallPos_C</labels>
      <labels>Automatic_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="c93098f7-b294-4eea-aa9f-3101250c2762">
      <defaultValue>RevIdle_C</defaultValue>
      <enumName>PnlOp_Rev_t</enumName>
      <labels>RevIdle_C</labels>
      <labels>RevInProcATS_C</labels>
      <labels>RevInProcStall_C</labels>
      <labels>RevComplATS_C</labels>
      <labels>RevComplStall_C</labels>
      <labels>RevInhibted_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="c552d50c-be4f-40d8-9a22-6e5658d6b889">
      <defaultValue>NoReq_C</defaultValue>
      <enumName>LearnAdap_Req_t</enumName>
      <labels>NoReq_C</labels>
      <labels>ClrReq_C</labels>
      <labels>SaveReq_C</labels>
      <labels>InvalidReq_C</labels>
      <labels>InterruptReq_C</labels>
      <labels>ClrReqHardStopOpen_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="81c84946-ea06-4f38-9a8d-b79799f736bb">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>BlockDet_FltType_t</enumName>
      <labels>NoFault_C</labels>
      <labels>SamePosStallFault_C</labels>
      <labels>DoubleStallFault_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="9e408647-7c80-4fbc-987a-7dbc7f957563">
      <defaultValue>None_C</defaultValue>
      <enumName>APDet_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="0c1965a9-079d-403e-98c9-e073b4b0c38e">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>PWMCtrl_MtrDir_t</enumName>
      <labels>IDLE_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <labels>SECURE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="fbda0f2e-353e-4975-9bf9-bf20412109da">
      <defaultValue>None</defaultValue>
      <enumName>CtrlLog_tstcurrentKeyPosition</enumName>
      <labels>None</labels>
      <labels>BeforeFullClose</labels>
      <labels>FullClose</labels>
      <labels>ComfortArea</labels>
      <labels>ComfortPosition</labels>
      <labels>OpenArea</labels>
      <labels>FullOpen</labels>
      <labels>AfterFullOpen</labels>
      <labels>Invalid</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="3200a7c1-ccfe-47e2-8654-a33e70967a86">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoSyncParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>ManMov_C</labels>
      <labels>ManCls_C</labels>
      <labels>StepMov_C</labels>
      <labels>StepMovCls_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="35ffd6e7-4caf-4a38-8d0f-893bf7e31462">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoAppParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>StepMovCloseReq_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <timingAndTaskingRegistry>&lt;?xml version=&quot;1.0&quot;?&gt;
&lt;slexec_sto version=&quot;1.1&quot; packageUris=&quot;http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112&quot;&gt;
  &lt;sto.Registry type=&quot;sto.Registry&quot; uuid=&quot;41151f2f-7d92-4ca9-9195-d3c5f27c8228&quot;&gt;
    &lt;executionSpec&gt;Undetermined&lt;/executionSpec&gt;
    &lt;clockRegistry type=&quot;sto.ClockRegistry&quot; uuid=&quot;dcbc1329-eb14-4498-ac63-8e1e107c1e3d&quot;&gt;
      &lt;clocks type=&quot;sto.Timer&quot; uuid=&quot;d6682a02-7027-4c11-aee0-ae3baa3b2f3a&quot;&gt;
        &lt;clockTickConstraint&gt;PeriodicWithFixedResolution&lt;/clockTickConstraint&gt;
        &lt;computedFundamentalDiscretePeriod&gt;.2&lt;/computedFundamentalDiscretePeriod&gt;
        &lt;resolution&gt;.2&lt;/resolution&gt;
        &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;14668b01-f146-4e06-8f87-4f169e841806&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;cd807b0f-ebf7-4e78-aeb0-3fbbbd76b7a0&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;baseRate type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;25c9986a-67f5-4583-a04d-7dac011fd609&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;f791292d-295f-45ea-a038-c742b738f41f&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/baseRate&gt;
      &lt;/clocks&gt;
      &lt;clocks type=&quot;sto.Event&quot; uuid=&quot;93c3d4fd-14b3-4f7d-ba61-271a6914cef8&quot;&gt;
        &lt;eventType&gt;PARAMETER_CHANGE_EVENT&lt;/eventType&gt;
        &lt;cNum&gt;1&lt;/cNum&gt;
        &lt;clockType&gt;Event&lt;/clockType&gt;
        &lt;identifier&gt;ParameterChangeEvent&lt;/identifier&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;79a84f27-fa1b-442e-9dfb-25d2eaf26b28&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;2c74290a-6f73-4fb1-8496-3ed710f8ee32&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
      &lt;/clocks&gt;
      &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
    &lt;/clockRegistry&gt;
    &lt;taskRegistry type=&quot;sto.TaskRegistry&quot; uuid=&quot;310cbb20-a6ed-43c9-a7aa-c83163332857&quot;&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;8e69a570-c24d-4219-9d9f-4e6a698a3443&quot;&gt;
        &lt;isExplicit&gt;true&lt;/isExplicit&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;0ed610ae-bc66-4db6-934f-27573dc63c32&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;09009c88-2f27-46fc-aa52-b691aa4ced9c&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;schedulingClockId&gt;ParameterChangeEvent&lt;/schedulingClockId&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;ModelWideParameterChangeEvent&lt;/identifier&gt;
        &lt;priority&gt;-1&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;87230bf2-dddc-4a97-a54e-f5eb993c13f7&quot;&gt;
        &lt;isExecutable&gt;true&lt;/isExecutable&gt;
        &lt;orderIndex&gt;1&lt;/orderIndex&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;e9d37d86-907b-43e0-9f9e-45f77c2dc3ef&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;77195e37-5340-4747-83f2-23892c49bb79&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;_task0&lt;/identifier&gt;
        &lt;priority&gt;40&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;9750be91-0803-40b1-93c5-91dda50f7534&quot;&gt;
        &lt;taskIdentifier&gt;_task0&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;815ddb53-659a-488a-919a-3dd204114b41&quot;&gt;
        &lt;clockIdentifier&gt;ParameterChangeEvent&lt;/clockIdentifier&gt;
        &lt;taskIdentifier&gt;ModelWideParameterChangeEvent&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskPriorityDirection&gt;HighNumberLast&lt;/taskPriorityDirection&gt;
      &lt;taskingMode&gt;ClassicMultiTasking&lt;/taskingMode&gt;
    &lt;/taskRegistry&gt;
  &lt;/sto.Registry&gt;
&lt;/slexec_sto&gt;</timingAndTaskingRegistry>
    <triggerTsType>triggered</triggerTsType>
    <triggerType>3</triggerType>
    <usePortBasedSampleTime>true</usePortBasedSampleTime>
    <zeroCrossingInfo type="ModelRefInfoRepo.ZeroCrossingInfo" uuid="d27051e7-4dc8-4529-a4b6-88798da6b2b2">
      <direction>7</direction>
      <isDiscrete>true</isDiscrete>
      <name>Trig</name>
      <needsEventNotification>true</needsEventNotification>
      <tolerance>-1.0</tolerance>
      <type>1</type>
      <width>1</width>
    </zeroCrossingInfo>
    <zeroCrossingInfo type="ModelRefInfoRepo.ZeroCrossingInfo" uuid="3f13a4e1-8968-4c9f-9eb0-7c810880575b">
      <direction>7</direction>
      <isDiscrete>true</isDiscrete>
      <name>Trig</name>
      <needsEventNotification>true</needsEventNotification>
      <tolerance>-1.0</tolerance>
      <type>1</type>
      <width>1</width>
    </zeroCrossingInfo>
    <zeroInternalMemoryAtStartupUnchecked>true</zeroInternalMemoryAtStartupUnchecked>
    <FMUBlockMap type="ModelRefInfoRepo.FMUBlockInfo" uuid="87e724c2-06f0-4327-9c5b-336e2be6553f"/>
    <codeGenInfo type="ModelRefInfoRepo.CodeGenInformation" uuid="4f326134-6ead-4d94-8c90-1f62636daf44">
      <DWorkTypeName>MdlrefDW_CtrlLog_Mdl_T</DWorkTypeName>
    </codeGenInfo>
    <compiledVariantInfos type="ModelRefInfoRepo.CompiledVariantInfoMap" uuid="99ec994a-54ad-4b1b-a254-1dcb5d5c7f3f"/>
    <configSettingsForConsistencyChecks type="ModelRefInfoRepo.ConfigSettingsForConsistencyChecks" uuid="14cac4ab-6877-4554-976c-0807874798de">
      <consistentOutportInitialization>true</consistentOutportInitialization>
      <fixedStepSize>.2</fixedStepSize>
      <frameDiagnosticSetting>2</frameDiagnosticSetting>
      <hasHybridSampleTime>true</hasHybridSampleTime>
      <optimizedInitCode>true</optimizedInitCode>
      <signalLoggingSaveFormat>2</signalLoggingSaveFormat>
      <simSIMDOptimization>1</simSIMDOptimization>
      <solverName>FixedStepDiscrete</solverName>
      <solverType>SOLVER_TYPE_FIXEDSTEP</solverType>
      <hardwareSettings type="ModelRefInfoRepo.HardwareSettings" uuid="0b76fdb4-6406-4b99-9c77-7df8c85b5064">
        <prodBitPerChar>8</prodBitPerChar>
        <prodBitPerDouble>64</prodBitPerDouble>
        <prodBitPerFloat>32</prodBitPerFloat>
        <prodBitPerInt>32</prodBitPerInt>
        <prodBitPerLong>32</prodBitPerLong>
        <prodBitPerLongLong>64</prodBitPerLongLong>
        <prodBitPerPointer>32</prodBitPerPointer>
        <prodBitPerPtrDiffT>32</prodBitPerPtrDiffT>
        <prodBitPerShort>16</prodBitPerShort>
        <prodBitPerSizeT>32</prodBitPerSizeT>
        <prodEndianess>1</prodEndianess>
        <prodLargestAtomicFloat>1</prodLargestAtomicFloat>
        <prodLargestAtomicInteger>3</prodLargestAtomicInteger>
        <prodShiftRight>true</prodShiftRight>
        <prodWordSize>32</prodWordSize>
      </hardwareSettings>
    </configSettingsForConsistencyChecks>
    <controllableInputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="96818372-e3b4-45a7-af07-32364d003b28"/>
    <controllableOutputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="78258c49-0705-40da-b67e-a7800ec86e1f"/>
    <dataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="dec757f2-179d-46c0-a076-cf6f3c8d5b4c">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>113</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>140</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>142</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
      <compDataOutputPorts>0</compDataOutputPorts>
      <dataInputPorts>0</dataInputPorts>
      <dataInputPorts>1</dataInputPorts>
      <dataInputPorts>2</dataInputPorts>
      <dataInputPorts>3</dataInputPorts>
      <dataInputPorts>4</dataInputPorts>
      <dataInputPorts>5</dataInputPorts>
      <dataInputPorts>6</dataInputPorts>
      <dataInputPorts>7</dataInputPorts>
      <dataInputPorts>8</dataInputPorts>
      <dataOutputPorts>0</dataOutputPorts>
    </dataPortGroup>
    <expFcnUnconnectedDataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="44ccd764-349d-498f-81ee-46a501104f7f">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
    </expFcnUnconnectedDataPortGroup>
    <interfaceParameterInfo type="ModelRefInfoRepo.InterfaceParameterInfo" uuid="a2282f91-d262-4a9d-943b-949e7f17959d">
      <globalVariables type="ModelRefInfoRepo.BuiltinParameter" uuid="e1108396-54ca-4e80-b75c-4a75d44b226a">
        <datatypeID>5</datatypeID>
        <dimensions>1</dimensions>
        <dimensions>1</dimensions>
        <isUsed>true</isUsed>
        <netSVCE>true</netSVCE>
        <numDimensions>2</numDimensions>
        <parameterName>RoofSys_tSmpCtrlLogic_SC</parameterName>
      </globalVariables>
    </interfaceParameterInfo>
    <messageInfo type="ModelRefInfoRepo.MessageInformation" uuid="171feded-d1d4-440d-8baa-ae5c4001df83">
      <rootInportInfo type="ModelRefInfoRepo.MessageRootPortInfo" uuid="9fd4b77b-0972-4d79-b929-a5523c0b9e6a">
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="311ac4e1-a55b-48ac-bef5-b492b157b517">
          <portIdx>2</portIdx>
        </GrHierarchyMsgModeMap>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="cbf0d6d0-196b-4868-b192-7f53150faabd">
          <portIdx>6</portIdx>
        </GrHierarchyMsgModeMap>
      </rootInportInfo>
    </messageInfo>
    <methodInfo type="ModelRefInfoRepo.MethodExistenceInfo" uuid="5ff19747-294c-4347-8137-e8734709147a">
      <hasSystemInitializeMethod>true</hasSystemInitializeMethod>
      <hasSystemResetMethod>true</hasSystemResetMethod>
      <hasTerminateMethod>true</hasTerminateMethod>
      <hasUpdateMethod>true</hasUpdateMethod>
    </methodInfo>
    <periodicEventPortUnsupportedBlockInfo type="ModelRefInfoRepo.PeriodicEventPortUnsupportedBlockInfo" uuid="475c94bf-51c4-4f57-85b4-c1ad4b9d7d05"/>
    <portGroupsRequireSameRate type="ModelRefInfoRepo.PortGroupsRequireSameRate" uuid="af34a6f1-5c19-4396-9023-e300e641c4e6">
      <DSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="b5942d7c-aa16-433a-8f89-5d26a9710b97"/>
      <GlobalDSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="f4e562ab-0a69-4fdc-ba72-ee54a931d1d9"/>
      <mergedPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="d81e07fc-3965-4926-9d29-bcdc69388da0"/>
    </portGroupsRequireSameRate>
    <rateBasedMdlGlobalDSMRateSpec type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="7eae2ddd-9880-4eca-86c0-1d2ce1430618">
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="8cb03acf-688d-43bf-ae58-e7ef5353327c">
        <dSMName>CfgParam_CAL</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="be0b322c-49c6-4989-b118-612e89058436">
          <blockName>CtrlLog_Mdl/DataStoreRead2</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="f8840eec-b18a-44f2-bd1c-f5ba5890e792">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="d8f0ae96-cf84-4a4e-9e23-664cc8e4702c">
        <dSMName>PIDDID_hDiagToPos</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="09c2bb05-a1db-46e0-9bc0-166fc862fbb9">
          <blockName>CtrlLog_Mdl/Data Store Read</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="83478dcb-b420-461b-9f87-ba743a3621d9">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
    </rateBasedMdlGlobalDSMRateSpec>
    <rateSpecOfGlobalDSMAccessedByDescExpFcnMdlMap type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="985c2887-a226-49f8-b56b-7627bf598392"/>
    <rootBlockDiagramInterface type="ci.Model" uuid="dfbf280a-1690-4750-84b3-0901aa2fd8e9">
      <p_RootComponentInterface type="ci.ComponentInterface" uuid="18bd70dc-4f65-4f74-a8f1-93970f5a2cc6">
        <p_InputPorts type="ci.SignalInterface" uuid="79b772d1-1668-451f-bfa5-1ee913a463ff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="028604b7-9fa8-4873-a346-a3584adb68dc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="54c5ae70-4c12-4cb3-b30e-d03b01fc1e2c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="64ebccb5-932f-443a-83cf-76f8c77e9fa2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cdd36f58-4f9c-4dba-a4d9-793fadf72b2b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2dcafd62-**************-4fa78e494b92">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fa189c8c-85ae-412e-92ae-0254c729efe0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="512e0da2-1fb3-436f-a4df-b9b6619ea882">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9a12a049-3c3c-4cea-95cd-6ffb5d1c3c82">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1ada1f92-978e-49c3-98fc-116c19a39476">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cd8ef954-5981-4964-a12d-a2ff67d8e84a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2d22cb28-12b2-4342-99c4-28f452f5811d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="eb07709d-8e22-4802-b100-45a7e3c1976a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="14126c5a-a7fe-446f-8d79-96104cf598fe">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="80d82ce1-0c98-4e2b-8bc4-e2ffcd55867c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="13c5959f-fb92-42a7-95f3-8beca29984b4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="63477615-85e1-4649-985f-c977fdc1d419">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="165bc905-4f78-4d91-a66c-f7d81e194047">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_Area_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="78737594-f99a-4829-af06-531a307ffbb3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_PnlPosArea_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8df39256-95bf-4d98-bb97-16fa44cc2966">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5db141d8-2145-4cf9-adbf-1390f02096b8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b0581e63-d843-4943-ac12-d28ed77c81dd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="506e2f7e-b093-4030-b0b7-f02c0127c6ac">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f63c357d-7a01-45f7-a9a8-ecb32e5bcbc1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="eb4c1c97-b167-4727-8ce7-b80c2a651add">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7a290d49-7eba-4c22-bb8b-f6e01fcb7057">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a5588627-1adc-4d1f-a13d-fa08b7004d4c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="93948e11-3f3f-4c48-8f14-f224492ac862">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c07d9949-56f0-420f-bc8e-c10ac7d54690">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f10f2e5b-def8-4733-8ade-e9a45ea05c5e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0aeee06e-7db9-4a49-a65f-d296deb34843">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ab3bc7bf-b6ff-4dbc-9074-989089fd229d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8ae803b5-24f4-400c-8865-8f64f1f0387d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4364c827-fcd7-4aa7-9fe1-d1966f261542">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cda2f111-55cd-4b8f-b88e-b28048ecf625">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c14cbc8f-8c4f-4018-a992-5270915ba52c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>StateMach_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e1c26a03-7dfb-4ed3-b861-55b2b4c36f8e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SWC_DataHndlLyrBus</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e4df2e59-7204-4da3-af49-1fa45a0a853f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VehCom_Cmd_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c4aa3613-39e5-40ac-b9b4-780357a12717">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0197b5c5-2f7c-448f-ab12-8ac28f0ba32e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="af22c10e-cee4-4083-8e92-93d3b65c29b3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="18e73e2c-b7bf-4945-aace-1ebc26c355a9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bcbac4f0-3e6f-4ce2-8397-9abd0ddc98ac">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="02c617aa-83a6-497d-8213-95fb61305388">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="49c1b153-84b2-4943-8608-efcd7244dc55">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cb719104-4249-4230-be13-e59267e78e50">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="663c0677-2a18-4ae0-959a-b28c443222ca">
          <p_ComputedNumericDimensions>3.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e13767e3-5eac-4efa-bd49-b73c59ad3543">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SysFltDiag_LastStopReason_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1b8847fe-307c-4334-a1c4-ecc291eca925">
          <p_ComputedNumericDimensions>10.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="91398890-e577-4c36-ba5c-3c29753ecfbe">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="03698081-1a2a-477c-aa14-222e078462a1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4aab171e-06a0-498d-b9f5-d628f3c921cb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="73d31c7e-b501-400f-944c-9ab3f3793b51">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dfa2b0d9-ce43-4d72-8eff-0120d9722f62">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d86f8087-672d-4fbc-857b-d9e88218c5b3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>BlockDet_Stall_dir</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6462bd00-7202-4d42-b025-cfb5d133ba82">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6857b263-b7c4-4685-b256-96507911b669">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="322e970f-d1ab-467d-8297-92f2d1db1796">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="60dd4122-768a-4d15-8384-5b4791aac555">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9f5e7d43-7684-48dc-b7db-8531aeaff2b5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6709e7ce-7fd8-4dc9-b6c4-4a507e7d86cc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a8e94668-da6d-4c48-ad67-acaabe05dfff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="32c9bfcf-f42b-40c5-be55-3460ee890629">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="41b8d130-3499-4d58-99b8-22b71b4a3b7a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MtrTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6fc7584d-445b-4e9a-8168-f8abaeb0902e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f0f3b958-83a2-467c-b10c-b5c59c9e718d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MosfetTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="da6d8d84-e726-4b1d-9103-8f0b9935e2bb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_ATS_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d3526379-13a8-4cb3-9c0c-914e8d959c19">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="44e207ac-4770-4217-9ea2-58464bf23faa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>MtrCtrl_MtrDir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a72d9c71-9a66-42ff-a1c5-08905a3454fe">
          <p_ComputedNumericDimensions>2.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2d400038-9c21-494e-a661-2f1dcd32b393">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b6458dc6-1dfb-4a94-b125-d18629d7b4c7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_RelearnMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4da79974-9edd-41e8-b296-6a0778db3f21">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6c33d037-145a-471f-9639-3f76f95c3594">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0795bcd9-1eb5-444d-96b1-879df4748875">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8700db9e-ce08-4832-8ec3-3f42a592da0a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1519a239-1e9b-4ab5-9bd9-e7e45ee4eede">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="92e266ef-d64b-4972-8fc3-6a8b2ada79f8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint32</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b387df2f-b720-47c7-a81b-49d8fe8dfaf5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="16bf1f44-2266-44cd-8b00-f1ad1acd0ff9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d9ef935d-5112-4a5e-a9b7-7a2c06848ef8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="32a1bede-2391-4533-95de-7dc9d3bb3efe">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fe8df60e-1a96-4b07-bd3c-fce949f26b79">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fe98b038-dc19-49d3-ac44-97f321cf5af9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bc55dc9c-**************-e7e55eb18209">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="153d37a5-8fbf-4f15-86da-070c71587a54">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="15054fab-ba94-4c19-80b3-b402ffaab72c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1438dd53-c6c1-4efe-aae8-6bb42ebcc4aa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9e89c163-93d7-4ca8-91d0-63c7483ac72c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c3a946d6-dc18-4466-a987-ea049f5f2401">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>HallDeco_Dir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6f2a51c7-88d1-4931-ae64-af0932b9df5d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="61a5611b-3e56-40e8-ba93-ba8d6c7f015e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>rad/s</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bcb964a4-4d3d-456b-abae-73174cf76214">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="75d5c12b-ff8e-442c-b898-d19eb3028c02">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="93125e86-fdca-40e3-8e2e-087b5fabebad">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="864516f8-cc5f-4716-b758-f9496f205647">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ebeb5dcb-e6ac-4284-b458-c08ce3237238">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6361f79a-dd69-47f0-b9b4-c6e344811fb7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="45da46b5-3044-44df-8970-85bbbb523c80">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="30d68305-561d-4f54-ae05-8def95e67944">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>StateMach_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1bb8a69c-3f4e-4c57-9f92-b603eb4c9753">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4be88579-88c7-4290-a5b3-c44cd16e7316">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="749bf27e-0b14-4906-b752-da64a075c94d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c9cf8b6e-7014-4e9e-9209-745dd7c9b398">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="621c92fb-0d04-416f-a5a8-90974d913a4f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6d406620-5592-422d-868e-f5304f7b4e2c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a20ccdb6-c5a1-4ea2-86a5-245295b6310b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bda73c9d-5bfe-49a8-b77b-9ce04eea8cc7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="17dd7e8f-15be-447d-93a2-243d22271648">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8d9ca0ac-521d-4981-8642-228f2f7dde21">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3387064a-b574-47d1-8e25-5f46f3b35149">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="98a1233a-6ba6-45e8-9cff-a2012a0e7313">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ComLogBus</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fd499f72-ad9c-43ae-a5a5-8ea2aabb65a9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOpBus</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5c9d73aa-830f-4da9-ac50-5a44a9edf5c4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SWC_ObjDetLyrBus</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b46454da-0809-407c-90bf-8ee5fdca4747">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PWMCtrl_MtrDir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f0a9e355-fef8-44fc-8b51-45bcd6a8e887">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8bbbbfb9-3152-4ccf-bb8c-a737f9862a58">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c7ab3984-d425-4375-a114-f357f2600a60">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1f0702b6-c5bb-485f-ba77-9c97590bd0d9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="492deb32-c0ab-429a-9848-e98bdd7ceeaa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="545a28df-181b-4cae-a19d-5478fea3a13b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2c062265-3847-48e5-bb9b-a1db892544ba">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="833bf934-f004-48af-9d26-cf9f04c2f329">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="911165af-1f23-4db2-834e-648f3559d7c4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="98aaf1da-2771-41d5-a5cb-7fc8e4756135">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a3ed0b24-3e2b-472e-85f0-2e3246b0e428">
          <p_ComputedNumericDimensions>2.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0e7d10bd-d5b3-44b4-bf68-770bc97d3b21">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c3fe773b-1bc9-477f-b966-2ec9a11074ad">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7e7789c9-fb52-4ee9-b756-7a13da628ad4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="71526575-ab28-4297-882f-f35f24c00a97">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="37e45cb9-c6e9-48d1-bf1a-669d66c3f19e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ada79974-03ce-4352-ae2e-8d7ecb6398a9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d11e5b36-8170-46d2-b7ec-9be2c8c07a00">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9119eeaf-612b-4dba-8228-139b6dc5c7fc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="22851c2b-5228-4720-969a-c7c783328921">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2d7c54ea-5d6a-4f2a-8a2f-50111ad24aa2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c07ae20e-82d4-4d17-aa76-5dfd7855c57c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>HallDeco_Dir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e2dd1f38-b356-4f04-8086-ccd9a37be376">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fab345e1-ced8-4e17-9b38-7e22bab69e66">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>rad/s</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3780bf16-b26c-4172-aa6e-cddfe5188cd5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="418c1190-a57c-428c-b709-37643eaa9af9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5551ddee-fec8-4b3c-a5f5-6e8f8f816439">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cae04e96-107c-48d2-a6a7-380fad82d210">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="96436ae3-3ac1-4fdd-b384-8df149776eff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_Name>CtrlLog_Mdl</p_Name>
        <p_OutputPorts type="ci.SignalInterface" uuid="8aa2ed53-cf07-42ef-ac05-0baf236a55d8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLogBus</p_ComputedType>
        </p_OutputPorts>
        <p_Type>ROOT</p_Type>
      </p_RootComponentInterface>
    </rootBlockDiagramInterface>
    <simulinkFunctions type="ModelRefInfoRepo.SimulinkFunctions" uuid="696e847d-0fb3-429e-a900-649794942e35">
      <compSimulinkFunctionCatalog></compSimulinkFunctionCatalog>
    </simulinkFunctions>
    <sltpContext type="sltp.mm.core.Context" uuid="c310dc37-569e-433f-bf6f-cee7e67d5175">
      <globalData type="sltp.mm.core.GlobalData" uuid="cfb213e5-b51b-439f-93c6-532e3d117db5">
        <dataName>CfgParam_CAL</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fcba5ba0-0b68-4b24-9f9f-5f1ee01fc9d8">
        <dataName>PIDDID_hDiagToPos</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f481df61-b51a-4eb0-918b-54f9825eb2f0">
        <dataName>debugCounter</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="800fca2d-f0ae-45fe-a1fe-2d3aa09f2f3f">
        <dataName>debug_PosTable</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ce5d753d-342b-4765-8d49-0311655ea2fd">
        <dataName>portComLogBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a52f6f3c-537a-43a1-b5d4-e0721faa2592">
        <dataName>portComLogBus_ComLog_isLearnSwtich</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="30c27789-abc3-4284-ad5f-56791570856b">
        <dataName>portComLogBus_ComLog_isOverrideActv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7aced92c-4a79-4d12-a307-b3c113e0f7ac">
        <dataName>portComLogBus_ComLog_stActvMovCmd_movCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6443cb3e-9c0d-4f30-a24f-b942201cb763">
        <dataName>portComLogBus_ComLog_stActvMovCmd_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="574064dc-bf7d-48cf-93f1-a3dc4ffa9fac">
        <dataName>portComLogBus_ComLog_stActvMovCmd_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f229a025-9063-43d5-8258-58569dcec8cc">
        <dataName>portComLogBus_ComLog_stActvVldCmd_movCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="164b4eff-8ea1-4e19-9ada-7b6bd7ac61a9">
        <dataName>portComLogBus_ComLog_stActvVldCmd_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5983d73f-293d-4268-97af-661f9938422a">
        <dataName>portComLogBus_ComLog_stActvVldCmd_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e9fe8e42-0843-4a44-a0a5-25fca5345673">
        <dataName>portCtrlLogBus</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d0f76291-546a-44ec-a090-90acf1bc1a55">
        <dataName>portInport</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3972da20-10a1-44a6-8ff8-47316d50f3a7">
        <dataName>portInport1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f0b1c8ab-fbcc-424c-9a8e-4ce56a943ec2">
        <dataName>portPnlOpBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cef2f899-5fa6-492d-870f-ceabda2d5af3">
        <dataName>portPnlOpBus_PnlOp_hTrgtPosPtcd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f1cdeef2-13ce-4b61-933a-1d8dd314bb1a">
        <dataName>portPnlOpBus_PnlOp_isMtrMoving</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ea8406bb-e11e-4429-be8d-d9899212bd97">
        <dataName>portPnlOpBus_PnlOp_pMtrSpdCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e79aeaf2-1b5b-443c-ae29-e47ce875b419">
        <dataName>portPnlOpBus_PnlOp_stATS</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47d62033-fd75-4e74-acbc-57fadc3a7d83">
        <dataName>portPnlOpBus_PnlOp_stMtrCtrlCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d96efc9d-dc26-48e2-8980-7fe7c6360460">
        <dataName>portPnlOpBus_PnlOp_stNormalMov</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="87c731ca-d54d-4b6c-bc60-f4d34890479e">
        <dataName>portPnlOpBus_PnlOp_stReversal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="13bfc3cf-f225-4452-9d72-a7b42e044066">
        <dataName>portSWC_CommLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a9967254-8557-4401-9ab3-5a98bb7f1de9">
        <dataName>portSWC_CommLyrBus_VehComBus_DTC_VehSig_LossCommLIN</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c00e4555-3b5e-438a-b334-15ecabe30fa4">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ffa28ee3-85cc-4e06-aa60-f74296eb863c">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_bSleepReady</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="70700002-6a13-45a1-adb7-ae5a5f58009f">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_hToPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3231f260-a429-4cbb-9be0-c192efe79319">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_isAuthEnbl</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="27ee5ff5-8baf-4c88-b57c-6e805b448a0c">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_isVehSpdVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e836042d-2e6f-42bf-9841-9cc1e70e7136">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_percRelPosn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="36fc654a-ddb2-47fe-81fa-613d4db1f246">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_stVehCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b9cff841-8c40-4841-b542-a839eca02a0a">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_typeCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0b29d971-83c6-4bd2-ab72-455fbb24b028">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_u8AfterRun</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="879f0146-d8c5-444c-87ca-526e7c80e803">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_u8Mov2Posn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6b684723-3f62-4569-8111-fb095ff0d740">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_u8ReqManMovement</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2df7f2ce-48ed-4df0-b07f-6c8e3faf3098">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_u8StopMovment</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="84a4bd7c-2c23-4ff2-9bd4-3c07b679e704">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_vVehSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c7a4d2ec-20dd-4709-9c65-ce0d595717d4">
        <dataName>portSWC_DataHndlLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="282e59a4-f002-4ed9-8391-6f3b569f8e0f">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SCUInfo</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d2db4590-7c23-458d-866c-ce697846ebee">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SWVersion</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="feaa3d26-629e-4724-8fe4-da825fb04bf7">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isCALFileVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="259dcf1a-9d8a-47a1-8032-76929ec5b626">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isPosTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="651cb70b-72b6-46b3-8652-a6df7d324f38">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3c72e18e-e663-4125-848a-f9d57e39f966">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fde94ac5-fffc-46ce-b57b-f7f9d4e83066">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stTestMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6749f4a4-b78e-4cd0-9514-4cf9d5196406">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currByteOfRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="db2597d3-62e6-4d95-870e-08e2b2bded85">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currRFArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cdc2ec85-c539-4fb5-b0c0-9b9342a4e95e">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isInsideRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a1715e91-41a4-4902-8843-9cc4a39c046b">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3c35526e-43d7-49e1-abf6-fde19b7d5554">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a7a1c5f8-210a-471c-80c3-146c72db6fdb">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmBak</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4700ee2c-f882-4d8a-a70d-1aaac0fc9491">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmSync</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3c60a34f-17e6-4c7f-a441-1a003c6d713a">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8b0d316b-852b-45b0-89a1-854b465512c4">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="182c252a-14f3-45f0-b644-12cee4e6c5ab">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_stRFSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f3c69c5c-08e2-459d-8fe4-40c5e5cf148a">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="247eb1ee-1b84-4424-96f0-27519b6b7ad5">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fbc1130d-96a8-4c81-ad4e-7f1aa331cebe">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3665254e-16cc-4a42-861e-33f034614bfd">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_unlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9bd48639-9c03-4852-9ac7-3962d607ef19">
        <dataName>portSWC_DiagLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="85a3bf32-fb3e-4fb0-aada-afaf84afa54f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="37546570-3dd8-4bc1-9564-8c66975d240b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_TMOSFETTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6addd9db-5479-4124-8f16-a332e9162b9e">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_u12VBattInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a79f0029-e21f-424b-89be-092668b3990b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uHallPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="baf763b7-5d97-4901-a5c4-0fb56d2e8574">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uM1AVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="64283ff1-939a-4a99-a7ff-00db9ec10d98">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uM1BVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f846ff61-20b0-4ad5-b1b7-33b240ce05f4">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_APDet_FDifference</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8b02afd9-6829-47f8-975d-9f2ddeacc0bf">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_APDet_FTracking</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="88a16985-2b95-46a1-970f-d8e82273513d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_UsgHist_ATSRevReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5e4ff72f-ce09-4ae5-ba7e-3cd5b73f5a5b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_AmbTemp_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7308e70e-28b6-40c3-8383-f4489201fcf6">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_AmbTemp_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f8febf48-7cdd-4c32-981e-3356fdd8c038">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_BlockDet_BlockDet_hStallPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e7647952-6dac-4806-a651-a757c1570311">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_BlockDet_BlockDet_stStallDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="173bb704-4c3c-4dac-858d-46dc6fa904bb">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3d1f6178-ef51-467b-ad76-7ea08db3805b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_CtrlLog_CtrlLog_stRelearnMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="966b1d02-afc1-4a52-911f-aa0d48435cb9">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallOutPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8d030fff-7247-4b26-98a8-64cf980d59e4">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens1PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5aacb926-fca3-41c9-acea-5722fe2b0e14">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens2PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b5912ae2-1f2a-4d68-a4a6-610e31b437ad">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isINTPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="55931b23-ba37-4025-b059-20257836b2e0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankAPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0065fd36-acf8-406d-acac-8bb776d04fc0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankBPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d3758ac4-a235-4edd-ae7d-746be34dc369">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isVBATMeasEnbPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4fd8b1fa-a9bc-48d0-96fc-d3e16369d7f6">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="031aedf5-6e42-4198-bdf0-5c3f722ff5ae">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isHallSuppHS2Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1e2f8f84-c639-4263-a200-f00720deb210">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isMtrFebkCtrlHS1Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f2efc00b-ce8c-4bd6-98ad-7f4ca14b8cb6">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isSwtchSuppHS3Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8ca77a83-06b6-4171-8bea-6d9631939de0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_u8GetPWMDutyCycleVal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1e10fbb4-662a-4a5a-bee2-0dc188a3a504">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_DTC_HALL_Hall1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2842fe97-baab-4d03-94b7-768af9448c48">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_HallDeco_DMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cd54b68d-8e16-4bf5-a17f-78083a634c04">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_HallDeco_rMtrSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f8c4d7df-8acc-4e84-a1d9-b1880031ef36">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_isHallSupplyFault</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d14f9b41-d0fb-4969-beda-a679803e35ad">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_isLearnComplete</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0b70a32e-db50-4e66-9e84-40ffd87c12c7">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_isLearningAllowed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6dd7d8ee-72cb-4f3d-987d-eba61c69026a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_stMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1ec6d1dc-3b51-449f-b8da-50a8886f7b19">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5e6b1b4e-b688-4e25-8ec6-6b4ff18c2ce1">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrCtrl_IoHwAb_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="463a7650-4e37-46e4-adc4-00e1c4a66790">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrCtrl_MtrCtrl_FeedbackStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0a4f8584-1de1-400d-a63f-9de40cfa4e51">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrMdlBus_MtrMdl_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a88d8f1f-3303-4d28-9c94-3fa5fc6841e2">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PnlOp_PnlOp_isMtrMoving</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e3e2ad47-a170-44b7-911d-fccd2ec241db">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PnlOp_PnlOp_stATS</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e5d65cc2-f828-4535-8b94-d4c83e39255f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PosMon_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d0a35acf-41f3-4fa8-b517-f580fa6fc01d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PosMon_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="074121ef-0998-4552-bbeb-dbc63fa66a27">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_RefField_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="de8b53fb-1155-4c71-b5e0-b2917487531e">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_RefForce_RefForce_FRefForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="77c73a68-dbda-4e21-822a-701b2777f689">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_StateMach_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b8e54834-a296-4d1c-92aa-6e4431a8a38f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a60a4fbe-2de6-4510-aa72-e7214381e1e8">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_stMosfetTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2fecfe9b-e82a-4410-89f8-0fc489f9bb26">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_stMtrTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9a3075a7-8614-47a3-a0cb-b0a44b1632f4">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThresForce_ThresForce_FatsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="53343dcd-cff2-4792-a5dc-335f9a1d279a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_CycRenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bcd41ae0-7cd2-4652-b36b-d77126e95484">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_LeanrFailCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5a5d87fd-2aac-4be2-9668-3f41b0822cf3">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_LearnSuccessCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8e688814-5951-4730-a110-bd250bf900f5">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MaxAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="79234c05-5325-4943-bcc7-7f7403be7871">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MaxBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0fdf2893-3c4c-4758-8d1f-8cdc63be50ef">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MinAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3a90c21f-9de3-476c-b0a9-96ff5f95f395">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MinBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="90d98e2f-02d2-4749-8ba7-eb728733bbd6">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MotorOperTime</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="42eb2d77-fa5b-4eb2-93a7-e63ede490de5">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_RenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="afe066dc-d7b3-4948-948a-b3da6bc2772a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReprogrammingCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7d745ec7-3411-4aca-8dd5-ff3e218b08f7">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="39a265a6-ef5b-4964-8796-91b8fa31ca33">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ff204828-2152-47e7-ab25-429c4b6b7e80">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0f15e3b8-0dca-445a-b137-269f8154b580">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalThres</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bed45b03-7883-437c-819e-34e6be80dd61">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_SlideCycCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9dfa1efe-5615-40be-aa39-dd7f589d6268">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_StallCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f9db99b9-2e33-45c8-9594-545c1c368a71">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ThermProtCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="40817ed9-7586-4ae6-bf6a-bb168f325636">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1e49b10b-aa85-491b-b381-090267249ed3">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="21cbf320-b3fe-4168-bb21-2a13cf91da2f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2eaf68ba-0de6-4505-896c-466532173ce9">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01a14049-5756-4f2c-9331-90baef71ce93">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="72672480-e5bd-4542-9468-1212c3e1a4a9">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_DTCStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a428d055-c66b-4f95-8cc6-02cf025c21d2">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stLast10DTCs</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2f7d00b7-74e7-4e0d-90e1-e8e90dae7e88">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLasStpReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6c9dc539-513e-41a9-9a78-011cf44f08e5">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLearFailReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c3696994-53db-499b-8a6b-0bf4a60596c4">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLearnFailReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="aa8788e3-73d9-4c48-b568-3c18f874b5fd">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsSysFaultReaction</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47266fe5-f528-4cf9-b431-8fc6a9d52202">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsUnlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="69c41b65-aa55-4edd-b7c6-f7043f693b7f">
        <dataName>portSWC_HwAbsLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0bec3a5e-b06f-4fd4-a00f-fb4383a9fe52">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="037f8b13-58c4-4334-83de-dd5b660303a9">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_TMOSFETTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="799b921f-c1cf-48ca-beb4-70eb50139095">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_u12VBattInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3095a073-9bd5-4098-80c5-7c6b9cc402c0">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uHallPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ddeb9bd8-8a97-4a24-b7ca-53d388ad5d6a">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1AVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b47ff9eb-9c55-4547-a7d0-89ca78684deb">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1BVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01718e74-de4e-46cc-b976-5989bbc6cf84">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uSwchInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a741cab3-135c-445e-b6d4-9820d05c2a0b">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_PINReadStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="16bcc02d-25cf-41d4-bb7f-81abb041656b">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallOutPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="57784bf0-d44d-47c0-8ae1-6cc4b9f9295f">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens1PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="76e669f4-f6a5-439e-83b7-e3fc0d7ce6d1">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens2PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ca161e50-**************-db9eba8b6690">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isINTPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="72165332-22a5-48e3-b0b6-cb0a41f28fc9">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankAPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="626c4adc-f692-4bf9-a1fa-cdc147c89366">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankBPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4c64f3e3-**************-fac021aa13b7">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isVBATMeasEnbPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="314d5e89-ca64-4b67-9f17-5e1632b0e281">
        <dataName>portSWC_HwAbsLyrBus_DiagComManBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b51c486c-412d-40fd-be9d-8d80e101ce34">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isHallSuppHS2Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="50e8fbdd-c6b2-449e-a54a-9090e5ccba99">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isMtrFebkCtrlHS1Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7d0bbac6-bf49-4c1c-b4e8-b6932f831a40">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isSwtchSuppHS3Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="590cf255-1353-4b79-bce3-71335f5b23d7">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_u8GetPWMDutyCycleVal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3b4bbf4-3cd9-4f70-b68a-37805a97d629">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_Signal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d6994763-d75e-440e-a8a5-ceacdd4c74b9">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7f15fc3e-8dd2-4386-bc14-b75d9c2c2872">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_DTC_HallMtrOptDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5b864689-3749-46db-b8a5-fe096f86bf94">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_DMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="441e67b8-9f8e-4eb3-b9d6-05272f469d35">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_isMtrSpdVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1852a90f-a724-4351-91ff-736a4820b018">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="adbd3134-1c8d-4b4a-a64a-6e2d6c0602bc">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="262bcc97-fcad-4e36-8ee1-0ff8b5a2b564">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b0b35e77-71e8-4f10-a779-3d305a603760">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFaultDebd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="30df59b0-8095-44c7-812e-9d2b46f920a6">
        <dataName>portSWC_HwAbsLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1ee25ffe-5401-42dd-864f-81272b0c853e">
        <dataName>portSWC_HwAbsLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDutyCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="07352b60-dc21-44e6-b097-47d173a7ee00">
        <dataName>portSWC_ObjDetLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="16b5d877-6959-42f8-8f12-bf6924bd210b">
        <dataName>portSWC_SigMonLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4962e1f0-96b1-40ec-967b-096b8cdbdf42">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1f12aac2-611a-4084-87c7-6dc108eaca46">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0e9064ca-d9b3-42e2-a5df-8d87f5f91b98">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stAmbTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e07315f5-bf03-4360-910d-85fcf0796549">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stMCUTempNoCritFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b54a4ed0-3d2e-4381-b08c-6b8d02aa9364">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_IoHwAb_SetHallPwrOutM1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="10f7d88f-e4d1-491d-80cc-961d2235ed87">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_PosMon_isUpdateDisabled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1f02bc1a-7b66-46af-8a45-3392eb249b71">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="23188f3e-6480-4e52-96d5-be4150df8de8">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="96453238-d7d4-4e98-8672-3311566142d8">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="63fa8ae0-c3e6-4651-9a62-452a325f51cd">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_stMosTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6bec6351-573a-4691-9bdd-651d986d2344">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_curPanelArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="811116ab-02f3-43c3-b175-6d3406b090e7">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4abc740a-ecf1-472e-8566-68d0cc08471e">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f7e6ac23-a227-4e1b-a057-c3eb3ffb2f47">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hComfortMidStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="82887f8e-d479-4b4d-91d5-aec997dd40be">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="457c1c76-2643-413d-b9ef-1082bac3dd4e">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a9a721b6-cad7-46f0-a5fd-54d91a7d140c">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hNearClsSld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="338f86cf-ed32-402c-b67c-56717ab14675">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c876e718-740d-4a51-b4f9-ce8e51748836">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9bc6b647-327b-4917-ac0b-18e4223a69ec">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5bec56b8-a919-44f3-965f-e8949de73305">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="31874ba3-1da5-4c9e-8bdb-2a432e120f29">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isOutOfRange</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b47b6726-4ce1-4ef0-a8cd-8892b56a3623">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isRelearn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8410245e-742a-43b0-b514-6c5ed8503810">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cbfcda97-9ab7-443a-93a3-1001f24c190e">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a52e9c31-2eb9-4da1-85a2-a84aa54d1173">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_panelCurPosArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="96907cd8-40d9-4651-8632-df8743792299">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_relativePinionSize</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3bfc9b3-fbed-4764-94ef-faaed5a35c5f">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_stPosSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9d5a85b5-0a1d-43bb-9cf2-c677ab944792">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_isDebSwch</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="368889fe-4e0b-4162-bcb3-dd88cb9eee53">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stMultSwchFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ad3827c4-628f-4b3d-baeb-18a5ab56db5b">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a26c0891-aad6-4bba-b1a6-ea0c5dcfd607">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchStickyFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="722af730-6c1a-443d-ac1f-54f477c1e668">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchVltgFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a697021f-8d2b-4512-bb36-0d8f8acc5835">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_Pullup</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ad6181bc-1fd4-4dff-b4f9-29a8bfd9e44d">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="eee0264d-97dc-4f49-a98c-e0bf83d5a8b5">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="64d733a7-0b1d-4bc7-bfb8-eb18a51e2d1a">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="884fea2a-1b35-4ac2-8276-157d367c2aff">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isOverVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="736b1d3a-bfb4-46fc-8b49-db682ac4455e">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isPwrLost</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1593891f-2d4a-457f-a48b-069a7e22f858">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartUpRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="167c1304-ffb0-4b9e-aeaa-3e8e33b5c13a">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ca6732d9-3e74-485f-b7d2-137f078dc4a5">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isUnderVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c995a207-2277-407d-a5a1-ed2d6617cc5f">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isVoltDrop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ee31fb08-d36c-4b9d-ac87-e4ffb8846e21">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5fe26c34-aa64-4c57-bb41-2a985a8321a2">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="82d00818-c98a-4beb-8263-0baba5810d06">
        <dataName>portSWC_StateMachLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8abbdb98-b2c2-4963-b24b-0fe064cbc2dd">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isATSActive</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="73ce1225-cf69-42d4-adeb-765ac7c46d55">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isDeNormed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c453c1a8-614c-4c05-9ec9-cba165ef7d58">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isEmergMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="25016210-b561-433e-9332-cf4854ccfbc4">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isFactMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9f7331f3-b7c0-4975-a53b-f84295803f17">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isMfgMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e21b0483-afb0-4fcd-998b-1ca4c3734917">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isMtrStalled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="15fd2f32-66cf-4a5a-954d-a765e8608bd6">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isUnLearned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c3f7795a-a247-4780-9c79-701ebf7a733b">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stCurrMovStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="78db84e7-aaa7-40a3-ae40-57fc27367bea">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stEcuRstResp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="439aa297-1bcd-4677-b0ca-70eed125b755">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stErrMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cfd37f37-8fd9-4003-8355-1dd7901e417f">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stNewMovInh</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b1be931c-ffc9-46f2-b5e5-94bd3b78ec33">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stSCUOperMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9bb23d0c-3936-49c3-8f3f-5ccfee764652">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stShdnType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4b8b4a97-c8dc-4dbf-a810-47b8156aa6d1">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <priorityDirection>HighNumberLast</priorityDirection>
      <editorState type="sltp.mm.core.EditorState" uuid="ef1cfea2-484a-420d-ae76-3d2ab30b0402">
        <isSynchronized>true</isSynchronized>
        <panelState type="sltp.mm.core.EditorPanelState" uuid="0bb34eb9-3f07-403c-a299-ffd7020be893"/>
      </editorState>
      <rootTask type="sltp.mm.core.Task" uuid="d0d7a9bd-7250-4ca2-9bc6-fb3b0f8538f7">
        <context type="sltp.mm.core.Context" uuid="c310dc37-569e-433f-bf6f-cee7e67d5175"/>
        <explicit>false</explicit>
        <name>Default</name>
        <priority>-2147483648</priority>
        <subgraph type="sltp.mm.core.Graph" uuid="0e0d1936-fa46-4a0f-bcbf-5cba9801a15a">
          <tasks type="sltp.mm.core.Task" uuid="72c721da-1824-400e-a4c8-803151e680ec">
            <context type="sltp.mm.core.Context" uuid="c310dc37-569e-433f-bf6f-cee7e67d5175"/>
            <explicit>false</explicit>
            <id>1</id>
            <isTimed>true</isTimed>
            <name>D1</name>
            <priority>40</priority>
            <rates type="sltp.mm.core.Rate" uuid="e3782438-dacd-45ac-ab77-62ecaed37737">
              <annotation>D1</annotation>
              <color>-436207361</color>
              <identifier>ClassicPeriodicDiscrete0.20</identifier>
              <rateSpec type="sltp.mm.core.RateSpec">
                <period>.2</period>
              </rateSpec>
              <sti>0</sti>
            </rates>
          </tasks>
        </subgraph>
      </rootTask>
    </sltpContext>
    <stateWriterToOwnerMap type="ModelRefInfoRepo.StateWriterInfo" uuid="b0b94c3a-f238-448c-92b9-d6d6966b5d35"/>
    <stoClientDataRegistry type="sto.ClientDataRegistry" uuid="940f1bab-9a00-42b7-af3a-32c5f3e4d65d">
      <dataSets type="sto.ClientClockNamedDataSet" uuid="251e5b04-0a41-4a59-a30f-5f038156226c">
        <tag>sltpEvents</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="05acda7a-eb80-45aa-9018-8813259e70ef">
        <tag>sltpTaskGroups</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="8b947f14-b21f-4a56-bb04-3918beed63ad">
        <dSet type="ModelRefInfoRepo.SltpTaskData" uuid="935aacdf-616d-4596-889a-4e32958c5072"/>
        <tSet type="ModelRefInfoRepo.SltpTaskData" uuid="935aacdf-616d-4596-889a-4e32958c5072">
          <dataName>D1</dataName>
          <linkedSet type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="8b947f14-b21f-4a56-bb04-3918beed63ad"/>
          <id type="sto.TaskHierarchyElementId">
            <id>_task0</id>
          </id>
        </tSet>
        <tag>sltpTasks</tag>
      </dataSets>
    </stoClientDataRegistry>
    <varTsUIDMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="4d74cb94-f016-43e7-937b-8ca2ff4d34dd"/>
  </ModelRefInfoRepo.ModelRefInfoRoot>
</MF0>