/*
 * File: CfgParam_Mdl.c
 *
 * Code generated for Simulink model 'CfgParam_Mdl'.
 *
 * Model version                  : 1.238
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:29:52 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "CfgParam_Mdl.h"
#include "rtwtypes.h"
#include "StateMach_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "crc_driver.h"
#include "CfgParam_Mdl_private.h"
#include "CfgParam_Mdl_Exp.h"
#include "irs_std_types.h"
#include "CfgParam_Mdl_Version.h"
#include "zero_crossing_types.h"

MdlrefDW_CfgParam_Mdl_T CfgParam_Mdl_MdlrefDW;

/* Block signals (default storage) */
B_CfgParam_Mdl_c_T CfgParam_Mdl_B;

/* Block states (default storage) */
DW_CfgParam_Mdl_f_T CfgParam_Mdl_DW;

/* Previous zero-crossings (trigger) states */
ZCE_CfgParam_Mdl_T CfgParam_Mdl_PrevZCX;

/*
 * Output and update for enable system:
 *    '<S13>/NVMWrite'
 *    '<S14>/NVMWrite'
 */
void CfgParam_Mdl_NVMWrite(boolean_T rtu_Enable, uint32_T rtu_BlockId, uint8_T
  *rty_Status)
{
  /* Outputs for Enabled SubSystem: '<S13>/NVMWrite' incorporates:
   *  EnablePort: '<S18>/Enable'
   */
  if (rtu_Enable) {
    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S18>/S-Function' */
    /* S-Function Block: <S18>/S-Function */
    (*rty_Status) = NVMMan_enRequestNVMWrite_new(rtu_BlockId);
  }

  /* End of Outputs for SubSystem: '<S13>/NVMWrite' */
}

/* System initialize for referenced model: 'CfgParam_Mdl' */
void CfgParam_Mdl_Init(void)
{
  /* SystemInitialize for Triggered SubSystem: '<S5>/StartupCheck' */
  CfgParam_Mdl_PrevZCX.StartupCheck_Trig_ZCE = ZERO_ZCSIG;

  /* End of SystemInitialize for SubSystem: '<S5>/StartupCheck' */
}

/* Output and update for referenced model: 'CfgParam_Mdl' */
void CfgParam_Mdl(const StateMach_Mode_t
                  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode,
                  uint8_T *rty_CfgParamBus_CfgParam_stCALFileFlt, boolean_T
                  *rty_CfgParamBus_CfgParam_isCALFileVld, boolean_T
                  *rty_CfgParamBus_CfgParam_isStartupRdy, boolean_T
                  *rty_CfgParamBus_CfgParam_isPosTblVld, CfgParam_TestMode_t
                  *rty_CfgParamBus_CfgParam_stTestMode)
{
  uint32_T q0;
  uint32_T qY;
  uint32_T tmp;
  uint16_T rtb_Add2;
  uint16_T rtb_CalDataExt_CALChecksum;
  uint16_T rtb_CalData_CALChecksum;
  uint8_T rtb_CalData_CALVerMinor;
  uint8_T rtb_CfgParam_stCALFileFlt;
  boolean_T rtb_Compare;
  boolean_T rtb_DataStoreRead3;

  /* DataStoreRead: '<Root>/DataStoreRead2' */
  rtb_CfgParam_stCALFileFlt = CfgParam_CAL.CALVerMajor;
  rtb_CalData_CALVerMinor = CfgParam_CAL.CALVerMinor;
  rtb_CalData_CALChecksum = CfgParam_CAL.CALChecksum;

  /* DataStoreRead: '<Root>/DataStoreRead1' */
  rtb_CalDataExt_CALChecksum = CfgParam_CALExt.CALChecksum;

  /* DataStoreRead: '<Root>/Data Store Read3' */
  rtb_DataStoreRead3 = Config_isCALFileVld;

  /* RelationalOperator: '<S26>/Compare' incorporates:
   *  Constant: '<S26>/Constant'
   */
  rtb_Compare = (*rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode ==
                 StateMach_Mode_t_Startup_C);

  /* Outputs for Triggered SubSystem: '<S5>/StartupCheck' incorporates:
   *  TriggerPort: '<S28>/Trigger'
   */
  if (rtb_Compare && (CfgParam_Mdl_PrevZCX.StartupCheck_Trig_ZCE != POS_ZCSIG))
  {
    /* Sum: '<S28>/Add2' incorporates:
     *  Constant: '<S28>/Constant4'
     *  Gain: '<S28>/Gain2'
     */
    rtb_Add2 = (uint16_T)(100U * CfgParam_CAL_SWConst_C.CALVerMajor +
                          CfgParam_CAL_SWConst_C.CALVerMinor);

    /* Chart: '<S28>/ChecksumCalc' incorporates:
     *  Constant: '<S28>/Constant'
     */
    CRC_DRV_Configure(0U, (crc_user_config_t *)&rtCP_Constant_Value);
    q0 = sizeof(CfgParam_CALBus_t);
    qY = q0 - /*MW:OvSatOk*/ 2U;
    if (q0 - 2U > q0) {
      qY = 0U;
    }

    CRC_DRV_WriteData(0U, &CfgParam_CAL.CALVerMajor, qY);
    tmp = CRC_DRV_GetCrcResult(0U);
    if (tmp > 65535U) {
      tmp = 65535U;
    }

    CRC_DRV_Configure(0U, (crc_user_config_t *)&rtCP_Constant_Value);
    q0 = sizeof(CfgParam_CALExtBus_t);
    qY = q0 - /*MW:OvSatOk*/ 2U;
    if (q0 - 2U > q0) {
      qY = 0U;
    }

    CRC_DRV_WriteData(0U, &CfgParam_CALExt.CALExtMinorVersion, qY);
    q0 = CRC_DRV_GetCrcResult(0U);
    if (q0 > 65535U) {
      q0 = 65535U;
    }

    /* S-Function (NVMMan_bGetCRCBlockStatus): '<S28>/S-Function' incorporates:
     *  Constant: '<S28>/Constant7'
     */
    /* S-Function Block: <S28>/S-Function */
    CfgParam_Mdl_B.SFunction = NVMMan_bGetCRCBlockStatus(14U);

    /* S-Function (NVMMan_bGetCRCBlockStatus): '<S28>/S-Function1' incorporates:
     *  Constant: '<S28>/Constant6'
     */
    /* S-Function Block: <S28>/S-Function1 */
    CfgParam_Mdl_B.SFunction1 = NVMMan_bGetCRCBlockStatus(15U);

    /* Chart: '<S28>/updateCAL' incorporates:
     *  Chart: '<S28>/ChecksumCalc'
     *  Constant: '<S28>/Constant4'
     *  Constant: '<S28>/Constant5'
     *  Constant: '<S30>/Constant'
     *  DataStoreRead: '<Root>/DataStoreRead1'
     *  DataStoreRead: '<Root>/DataStoreRead2'
     *  Gain: '<S28>/Gain'
     *  Logic: '<S28>/Logical Operator1'
     *  Logic: '<S28>/Logical Operator15'
     *  Logic: '<S28>/Logical Operator4'
     *  Logic: '<S28>/NOT'
     *  Logic: '<S28>/NOT1'
     *  RelationalOperator: '<S28>/Relational Operator'
     *  RelationalOperator: '<S28>/Relational Operator13'
     *  RelationalOperator: '<S28>/Relational Operator2'
     *  RelationalOperator: '<S30>/Compare'
     *  Sum: '<S28>/Add'
     */
    CfgParam_Mdl_B.isWriteCALToNVM = false;
    if ((!CfgParam_Mdl_B.SFunction) || (!CfgParam_Mdl_B.SFunction1) ||
        ((rtb_CalDataExt_CALChecksum != (uint16_T)q0) || ((uint16_T)tmp !=
          rtb_CalData_CALChecksum)) || (rtb_CfgParam_stCALFileFlt != ((uint8_T)
          CfgParam_Cal_Ver_Major_C)) || ((uint16_T)(100U *
          rtb_CfgParam_stCALFileFlt + rtb_CalData_CALVerMinor) < rtb_Add2)) {
      /* Copy CalData into RAM */
      CfgParam_CAL = CfgParam_CAL_SWConst_C;

      /* Copy Extended CalData into RAM */
      CfgParam_CALExt = CfgParam_CALExt_SWConst_C;

      /* Set NVM write request */
      CfgParam_Mdl_B.isWriteCALToNVM = true;
    }

    /* End of Chart: '<S28>/updateCAL' */

    /* Switch: '<S28>/Switch' incorporates:
     *  DataStoreRead: '<Root>/Data Store Read2'
     *  Logic: '<S28>/Logical Operator2'
     *  Switch: '<S28>/Switch2'
     */
    if ((Config_stCALFileFlt != 0) || CfgParam_Mdl_B.isWriteCALToNVM) {
      /* Switch: '<S28>/Switch' */
      CfgParam_Mdl_B.stCALFileFltOut = Config_stCALFileFlt;

      /* Switch: '<S28>/Switch2' */
      CfgParam_Mdl_B.isCALFileVldOut = rtb_DataStoreRead3;
    } else {
      /* Switch: '<S28>/Switch' incorporates:
       *  Constant: '<S28>/Constant2'
       */
      CfgParam_Mdl_B.stCALFileFltOut = 1U;

      /* Switch: '<S28>/Switch2' incorporates:
       *  Constant: '<S28>/Constant3'
       */
      CfgParam_Mdl_B.isCALFileVldOut = ((boolean_T)VALID);
    }

    /* End of Switch: '<S28>/Switch' */
  }

  CfgParam_Mdl_PrevZCX.StartupCheck_Trig_ZCE = rtb_Compare;

  /* End of Outputs for SubSystem: '<S5>/StartupCheck' */

  /* Outputs for Enabled SubSystem: '<S14>/NVMWrite' */
  /* RelationalOperator: '<S21>/FixPt Relational Operator' incorporates:
   *  Constant: '<S14>/Constant3'
   *  RelationalOperator: '<S24>/Compare'
   *  UnitDelay: '<S21>/Delay Input1'
   *
   * Block description for '<S21>/Delay Input1':
   *
   *  Store in Global RAM
   */
  CfgParam_Mdl_NVMWrite(((int32_T)CfgParam_Mdl_B.isWriteCALToNVM > (int32_T)
    CfgParam_Mdl_DW.DelayInput1_DSTATE), 14U, &CfgParam_Mdl_B.isShdnRdy);

  /* End of Outputs for SubSystem: '<S14>/NVMWrite' */

  /* Outputs for Enabled SubSystem: '<S13>/NVMWrite' */
  /* RelationalOperator: '<S16>/FixPt Relational Operator' incorporates:
   *  Constant: '<S13>/Constant3'
   *  RelationalOperator: '<S19>/Compare'
   *  UnitDelay: '<S16>/Delay Input1'
   *
   * Block description for '<S16>/Delay Input1':
   *
   *  Store in Global RAM
   */
  CfgParam_Mdl_NVMWrite(((int32_T)CfgParam_Mdl_B.isWriteCALToNVM > (int32_T)
    CfgParam_Mdl_DW.DelayInput1_DSTATE_l), 15U, &CfgParam_Mdl_B.Status);

  /* End of Outputs for SubSystem: '<S13>/NVMWrite' */

  /* S-Function (sfix_bitop): '<S10>/Bitwise OR' */
  rtb_CfgParam_stCALFileFlt = (uint8_T)(CfgParam_Mdl_B.isShdnRdy |
    CfgParam_Mdl_B.Status);

  /* RelationalOperator: '<S10>/Relational Operator4' incorporates:
   *  Constant: '<S10>/Constant'
   */
  rtb_Compare = (rtb_CfgParam_stCALFileFlt == 0);

  /* Switch: '<S9>/Switch1' incorporates:
   *  Constant: '<S10>/Constant1'
   *  Constant: '<S9>/Failed'
   *  RelationalOperator: '<S10>/Relational Operator5'
   *  Switch: '<S9>/Switch3'
   *  Switch: '<S9>/Switch4'
   */
  if (rtb_CfgParam_stCALFileFlt != 0) {
    /* DataTypeConversion: '<Root>/Data Type Conversion1' incorporates:
     *  Constant: '<S9>/Constant2'
     */
    *rty_CfgParamBus_CfgParam_isCALFileVld = ((boolean_T)INVALID);
    rtb_CfgParam_stCALFileFlt = 2U;
  } else {
    if (rtb_Compare) {
      /* Switch: '<S9>/Switch4' incorporates:
       *  Constant: '<S9>/Constant3'
       *  DataTypeConversion: '<Root>/Data Type Conversion1'
       */
      *rty_CfgParamBus_CfgParam_isCALFileVld = ((boolean_T)VALID);
    } else {
      /* DataTypeConversion: '<Root>/Data Type Conversion1' incorporates:
       *  Switch: '<S9>/Switch4'
       */
      *rty_CfgParamBus_CfgParam_isCALFileVld = CfgParam_Mdl_B.isCALFileVldOut;
    }

    /* Switch: '<S9>/Switch2' incorporates:
     *  Constant: '<S9>/Pass'
     */
    if (rtb_Compare) {
      rtb_CfgParam_stCALFileFlt = 1U;
    } else {
      rtb_CfgParam_stCALFileFlt = CfgParam_Mdl_B.stCALFileFltOut;
    }

    /* End of Switch: '<S9>/Switch2' */
  }

  /* End of Switch: '<S9>/Switch1' */

  /* DataStoreWrite: '<Root>/Data Store Write' */
  Config_isCALFileVld = *rty_CfgParamBus_CfgParam_isCALFileVld;

  /* DataTypeConversion: '<Root>/Data Type Conversion2' incorporates:
   *  Constant: '<S11>/Constant'
   *  Logic: '<S9>/Logical Operator'
   *  Logic: '<S9>/Logical Operator10'
   *  Logic: '<S9>/Logical Operator8'
   *  Logic: '<S9>/Logical Operator9'
   *  RelationalOperator: '<S11>/Compare'
   */
  *rty_CfgParamBus_CfgParam_isStartupRdy = (((CfgParam_Mdl_B.stCALFileFltOut > 0)
    && (!CfgParam_Mdl_B.isWriteCALToNVM)) || (CfgParam_Mdl_B.isWriteCALToNVM &&
    (rtb_CfgParam_stCALFileFlt != 0)));

  /* DataStoreWrite: '<Root>/Data Store Write17' */
  Config_isStartupRdy = *rty_CfgParamBus_CfgParam_isStartupRdy;

  /* DataTypeConversion: '<Root>/Data Type Conversion5' incorporates:
   *  Gain: '<Root>/Gain3'
   */
  *rty_CfgParamBus_CfgParam_isPosTblVld = (rtb_DataStoreRead3 << 7 != 0);

  /* DataStoreWrite: '<Root>/Data Store Write18' */
  Config_isPosTblVld = *rty_CfgParamBus_CfgParam_isPosTblVld;

  /* DataTypeConversion: '<Root>/Data Type Conversion' */
  *rty_CfgParamBus_CfgParam_stCALFileFlt = rtb_CfgParam_stCALFileFlt;

  /* DataStoreWrite: '<Root>/Data Store Write19' */
  Config_stCALFileFlt = *rty_CfgParamBus_CfgParam_stCALFileFlt;

  /* DataTypeConversion: '<Root>/Data Type Conversion6' */
  *rty_CfgParamBus_CfgParam_stTestMode = CfgParam_TestMode_t_Disabled_C;

  /* Update for UnitDelay: '<S21>/Delay Input1' incorporates:
   *  RelationalOperator: '<S24>/Compare'
   *
   * Block description for '<S21>/Delay Input1':
   *
   *  Store in Global RAM
   */
  CfgParam_Mdl_DW.DelayInput1_DSTATE = CfgParam_Mdl_B.isWriteCALToNVM;

  /* Update for UnitDelay: '<S16>/Delay Input1' incorporates:
   *  RelationalOperator: '<S19>/Compare'
   *
   * Block description for '<S16>/Delay Input1':
   *
   *  Store in Global RAM
   */
  CfgParam_Mdl_DW.DelayInput1_DSTATE_l = CfgParam_Mdl_B.isWriteCALToNVM;
}

/* Model initialize function */
void CfgParam_Mdl_initialize(const char_T **rt_errorStatus)
{
  RT_MODEL_CfgParam_Mdl_T *const CfgParam_Mdl_M = &(CfgParam_Mdl_MdlrefDW.rtm);

  /* Registration code */

  /* initialize error status */
  rtmSetErrorStatusPointer(CfgParam_Mdl_M, rt_errorStatus);
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
