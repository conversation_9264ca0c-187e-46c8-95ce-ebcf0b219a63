var dataJson = {"arch":{"ispc":true,"isunix":false,"ismac":false},"build":"PosMon_Mdl","ref":true,"files":[{"name":"PosMon_Mdl.c","type":"source","group":"model","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\PosMon_Mdl","tag":"","groupDisplay":"Model files","code":"/*\r\n * File: PosMon_Mdl.c\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n *\r\n * Target selection: ert.tlc\r\n * Embedded hardware selection: NXP->Cortex-M0/M0+\r\n * Code generation objectives: Unspecified\r\n * Validation result: Not run\r\n */\r\n\r\n#include \"PosMon_Mdl.h\"\r\n#include \"rtwtypes.h\"\r\n#include \"VoltMon_ExpTypes.h\"\r\n#include \"PosMon_Mdl_types.h\"\r\n#include \"NvM_ExpTypes.h\"\r\n#include \"LearnAdp_ExpTypes.h\"\r\n#include \"CfgParam_Mdl_ExpTypes.h\"\r\n#include \"PosMon_ExpTypes.h\"\r\n#include \"PosMon_Mdl_private.h\"\r\n#include \"PosMon_Exp.h\"\r\n#include \"CfgParam_Mdl_Exp.h\"\r\n#include \"RoofSys.h\"\r\n\r\n/* Named constants for Chart: '<S4>/PositionState' */\r\n#define PosMon_Mdl_FALSE               (false)\r\n#define PosMon_Mdl_IN_GOING_INVALID    ((uint8_T)1U)\r\n#define PosMon_Mdl_IN_INVALID_OPERATION ((uint8_T)2U)\r\n#define PosMon_Mdl_IN_NORMAL_OPERATION ((uint8_T)3U)\r\n#define PosMon_Mdl_IN_NO_ACTIVE_CHILD  ((uint8_T)0U)\r\n#define PosMon_Mdl_IN_RESET            ((uint8_T)1U)\r\n#define PosMon_Mdl_IN_RESTORE_RP       ((uint8_T)2U)\r\n#define PosMon_Mdl_IN_VALID_AFTER_MOVING ((uint8_T)3U)\r\n#define PosMon_Mdl_IN_VALID_IDLE       ((uint8_T)4U)\r\n#define PosMon_Mdl_IN_VALID_MOVING     ((uint8_T)5U)\r\n#define PosMon_Mdl_IN_WRITE_NEWPOS     ((uint8_T)6U)\r\n#define PosMon_Mdl_IN_WRITING_INVALID  ((uint8_T)7U)\r\n#define PosMon_Mdl_RUN_FAIL            ((uint8_T)2U)\r\n#define PosMon_Mdl_WRITE_REQUEST_WAIT_TIME ((uint16_T)300U)\r\n#define PosMon_Mdl_WRITE_WAIT_TIME     ((uint16_T)3000U)\r\n\r\nMdlrefDW_PosMon_Mdl_T PosMon_Mdl_MdlrefDW;\r\n\r\n/* Block signals (default storage) */\r\nB_PosMon_Mdl_c_T PosMon_Mdl_B;\r\n\r\n/* Block states (default storage) */\r\nDW_PosMon_Mdl_f_T PosMon_Mdl_DW;\r\n\r\n/* Forward declaration for local functions */\r\nstatic void PosMon_Mdl_exit_internal_NORMAL_OPERATION(POSMON_STATEModeType\r\n  *rty_PanelState);\r\n\r\n/* Output and update for atomic system: '<S2>/CalculatePosition' */\r\nvoid PosMon_Mdl_CalculatePosition(const uint8_T *rtu_HallCountsCyclic, boolean_T\r\n  rtu_typeHallDir_P, int8_T rtu_U8One_C, int8_T rtu_Int8MinusOne_C, const\r\n  VoltMon_VoltClass_t *rtu_VoltMon_stVoltClass, int8_T rtu_ZeroU8_C, int16_T\r\n  rtu_hCurPos_IN, boolean_T *rty_isMotorMoving, int16_T *rty_localCurPos)\r\n{\r\n  int32_T tmp;\r\n  int16_T rtb_Switch;\r\n  int8_T rtb_Diff;\r\n  int8_T tmp_0;\r\n\r\n  /* Sum: '<S18>/Diff' incorporates:\r\n   *  UnitDelay: '<S18>/UD'\r\n   *\r\n   * Block description for '<S18>/Diff':\r\n   *\r\n   *  Add in CPU\r\n   *\r\n   * Block description for '<S18>/UD':\r\n   *\r\n   *  Store in Global RAM\r\n   */\r\n  rtb_Diff = (int8_T)((int8_T)*rtu_HallCountsCyclic - (int8_T)\r\n                      PosMon_Mdl_DW.UD_DSTATE);\r\n\r\n  /* Switch: '<S3>/Switch' incorporates:\r\n   *  Constant: '<S13>/Constant'\r\n   *  Constant: '<S14>/Constant'\r\n   *  Constant: '<S15>/Constant'\r\n   *  Constant: '<S16>/Constant'\r\n   *  Logic: '<S3>/OR'\r\n   *  MultiPortSwitch: '<S3>/MultiportSwitch'\r\n   *  Product: '<S3>/IncreaseOrDecrease'\r\n   *  RelationalOperator: '<S13>/Compare'\r\n   *  RelationalOperator: '<S14>/Compare'\r\n   *  RelationalOperator: '<S15>/Compare'\r\n   *  RelationalOperator: '<S16>/Compare'\r\n   */\r\n  if ((*rtu_VoltMon_stVoltClass == VoltMon_VoltClass_t_VoltClassA_C) ||\r\n      (*rtu_VoltMon_stVoltClass == VoltMon_VoltClass_t_VoltClassB_C) ||\r\n      (*rtu_VoltMon_stVoltClass == VoltMon_VoltClass_t_VoltClassC_C) ||\r\n      (*rtu_VoltMon_stVoltClass == VoltMon_VoltClass_t_VoltClassD_C)) {\r\n    /* MultiPortSwitch: '<S3>/MultiportSwitch' */\r\n    if (!rtu_typeHallDir_P) {\r\n      tmp_0 = rtu_U8One_C;\r\n    } else {\r\n      tmp_0 = rtu_Int8MinusOne_C;\r\n    }\r\n\r\n    rtb_Switch = (int16_T)(rtb_Diff * tmp_0);\r\n  } else {\r\n    rtb_Switch = rtu_ZeroU8_C;\r\n  }\r\n\r\n  /* End of Switch: '<S3>/Switch' */\r\n\r\n  /* Sum: '<S3>/AddToPreviousValue' */\r\n  tmp = rtb_Switch + rtu_hCurPos_IN;\r\n  if (tmp > 32767) {\r\n    tmp = 32767;\r\n  } else if (tmp < -32768) {\r\n    tmp = -32768;\r\n  }\r\n\r\n  *rty_localCurPos = (int16_T)tmp;\r\n\r\n  /* End of Sum: '<S3>/AddToPreviousValue' */\r\n\r\n  /* RelationalOperator: '<S17>/Compare' incorporates:\r\n   *  Constant: '<S17>/Constant'\r\n   */\r\n  *rty_isMotorMoving = (rtb_Diff != 0);\r\n\r\n  /* Update for UnitDelay: '<S18>/UD'\r\n   *\r\n   * Block description for '<S18>/UD':\r\n   *\r\n   *  Store in Global RAM\r\n   */\r\n  PosMon_Mdl_DW.UD_DSTATE = *rtu_HallCountsCyclic;\r\n}\r\n\r\n/* Function for Chart: '<S4>/PositionState' */\r\nstatic void PosMon_Mdl_exit_internal_NORMAL_OPERATION(POSMON_STATEModeType\r\n  *rty_PanelState)\r\n{\r\n  /* Chart: '<S4>/PositionState' */\r\n  *rty_PanelState = POSMON_STATEModeType_None;\r\n  PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_NO_ACTIVE_CHILD;\r\n}\r\n\r\n/* System initialize for atomic system: '<S2>/DetermineAndWriteState' */\r\nvoid PosMon_Mdl_DetermineAndWriteState_Init(POSMON_STATEModeType *rty_PanelState,\r\n  NVMMgmt_ReqCmdType *rty_NvmAccessState)\r\n{\r\n  /* SystemInitialize for Chart: '<S4>/PositionState' */\r\n  *rty_PanelState = POSMON_STATEModeType_None;\r\n  *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n}\r\n\r\n/* Output and update for atomic system: '<S2>/DetermineAndWriteState' */\r\nvoid PosMon_Mdl_DetermineAndWriteState(uint16_T rtu_tSmpPosMon_SC, boolean_T\r\n  rtu_PosMon_isCurPosVld, boolean_T rtu_HallSigFlt, boolean_T rtu_HallSigSc,\r\n  boolean_T rtu_DiagDenormReq, const uint8_T *rtu_stCALFileFlt, boolean_T\r\n  rtu_isRelearnIn, boolean_T rtu_isMotorMoving, int16_T rtu_localCurPos, const\r\n  LearnAdap_Req_t *rtu_stPosReq, stdReturn_t rtu_PosMonNVMstate, boolean_T\r\n  *rty_isCurPosVld, boolean_T *rty_isNVMPosVldOut, int16_T *rty_stPosSave,\r\n  boolean_T *rty_isRelearnOut, POSMON_STATEModeType *rty_PanelState, int16_T\r\n  *rty_RP_beforeReset, NVMMgmt_ReqCmdType *rty_NvmAccessState)\r\n{\r\n  int32_T tmp;\r\n  boolean_T guard1;\r\n\r\n  /* Chart: '<S4>/PositionState' */\r\n  if (PosMon_Mdl_DW.temporalCounter_i1 < MAX_uint32_T) {\r\n    PosMon_Mdl_DW.temporalCounter_i1++;\r\n  }\r\n\r\n  PosMon_Mdl_DW.stPosReq_prev = PosMon_Mdl_DW.stPosReq_start;\r\n  PosMon_Mdl_DW.stPosReq_start = *rtu_stPosReq;\r\n  if (PosMon_Mdl_DW.is_active_c3_PosMon_Mdl == 0U) {\r\n    PosMon_Mdl_DW.stPosReq_prev = *rtu_stPosReq;\r\n    PosMon_Mdl_DW.is_active_c3_PosMon_Mdl = 1U;\r\n\r\n    /*  Intialize local NVM state to value read from EEPROM   */\r\n    PosMon_Mdl_B.isNVMPosVldOut = rtu_PosMon_isCurPosVld;\r\n    PosMon_Mdl_B.PosMon_isRelearnOut = rtu_isRelearnIn;\r\n\r\n    /*  If valid position is found in EEPROM  */\r\n    if (rtu_PosMon_isCurPosVld) {\r\n      PosMon_Mdl_DW.is_c3_PosMon_Mdl = PosMon_Mdl_IN_NORMAL_OPERATION;\r\n\r\n      /*  Position is reported VALID to other components  */\r\n      PosMon_Mdl_B.isCurPosVld = true;\r\n      PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_VALID_IDLE;\r\n      *rty_PanelState = POSMON_STATEModeType_VALID_IDLE;\r\n      *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n    } else {\r\n      PosMon_Mdl_DW.is_c3_PosMon_Mdl = PosMon_Mdl_IN_INVALID_OPERATION;\r\n      *rty_PanelState = POSMON_STATEModeType_INVALID_OPERATION;\r\n      *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n      PosMon_Mdl_B.RP_beforeReset = 0;\r\n\r\n      /*  Relative position change is updated (actual position is not known!)\r\n         Postion = Relative position limited by 0 and 65535  */\r\n    }\r\n  } else {\r\n    guard1 = false;\r\n    switch (PosMon_Mdl_DW.is_c3_PosMon_Mdl) {\r\n     case PosMon_Mdl_IN_GOING_INVALID:\r\n      *rty_NvmAccessState = NVMMgmt_ReqCmdType_Erase;\r\n\r\n      /*  Possibly writing CALfile and Reference field so this could take a while  */\r\n      if ((rtu_PosMonNVMstate == IRS_E_OK) || (rtu_PosMonNVMstate == IRS_E_NOK))\r\n      {\r\n        PosMon_Mdl_DW.is_c3_PosMon_Mdl = PosMon_Mdl_IN_INVALID_OPERATION;\r\n        *rty_PanelState = POSMON_STATEModeType_INVALID_OPERATION;\r\n        *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n        PosMon_Mdl_B.RP_beforeReset = 0;\r\n\r\n        /*  Relative position change is updated (actual position is not known!)\r\n           Postion = Relative position limited by 0 and 65535  */\r\n      }\r\n      break;\r\n\r\n     case PosMon_Mdl_IN_INVALID_OPERATION:\r\n      *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n\r\n      /*   */\r\n      if ((*rtu_stPosReq == LearnAdap_Req_t_ClrReq_C) || (*rtu_stPosReq ==\r\n           LearnAdap_Req_t_ClrReqHardStopOpen_C)) {\r\n        PosMon_Mdl_DW.is_c3_PosMon_Mdl = PosMon_Mdl_IN_NORMAL_OPERATION;\r\n\r\n        /*  Position is reported VALID to other components  */\r\n        PosMon_Mdl_B.isCurPosVld = true;\r\n        PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_RESET;\r\n        *rty_PanelState = POSMON_STATEModeType_RESET;\r\n        PosMon_Mdl_B.PosMon_isRelearnOut = false;\r\n        PosMon_Mdl_B.RP_beforeReset = rtu_localCurPos;\r\n\r\n        /*  Reset value to hard stop position  */\r\n      }\r\n      break;\r\n\r\n     default:\r\n      /* case IN_NORMAL_OPERATION: */\r\n      /*  Sequence not ended correctly   */\r\n      if ((PosMon_Mdl_B.isNVMPosVldOut && rtu_PosMon_isCurPosVld &&\r\n           (*rtu_stPosReq == LearnAdap_Req_t_InvalidReq_C)) || (rtu_HallSigFlt ||\r\n           rtu_HallSigSc || rtu_DiagDenormReq) || (*rtu_stCALFileFlt ==\r\n           PosMon_Mdl_RUN_FAIL) || (rtu_PosMonNVMstate == IRS_E_NOK)) {\r\n        PosMon_Mdl_exit_internal_NORMAL_OPERATION(rty_PanelState);\r\n        PosMon_Mdl_B.isCurPosVld = PosMon_Mdl_FALSE;\r\n        PosMon_Mdl_DW.is_c3_PosMon_Mdl = PosMon_Mdl_IN_GOING_INVALID;\r\n        *rty_PanelState = POSMON_STATEModeType_GOING_INVALID;\r\n\r\n        /*  Write Invalid to NVM, movements can occur during invalid state\r\n           which are not stored in NVM   */\r\n        PosMon_Mdl_B.isNVMPosVldOut = PosMon_Mdl_FALSE;\r\n        *rty_NvmAccessState = NVMMgmt_ReqCmdType_Erase;\r\n        PosMon_Mdl_B.RP_beforeReset = 0;\r\n\r\n        /*  If NVM cannot be accessed POS mon wil get stuck here.\r\n           Endless saving to NVM untill reset  */\r\n\r\n        /*  Hall signal failure or Hall Signal Short circuit or Diag denorm request  */\r\n        /*  If Calibration data is updated  */\r\n        /*  Error occurs on NVM access  */\r\n        /*   */\r\n      } else if ((!PosMon_Mdl_B.isNVMPosVldOut) && rtu_PosMon_isCurPosVld &&\r\n                 (*rtu_stPosReq == LearnAdap_Req_t_InvalidReq_C)) {\r\n        PosMon_Mdl_exit_internal_NORMAL_OPERATION(rty_PanelState);\r\n        PosMon_Mdl_B.isCurPosVld = PosMon_Mdl_FALSE;\r\n        PosMon_Mdl_DW.is_c3_PosMon_Mdl = PosMon_Mdl_IN_INVALID_OPERATION;\r\n        *rty_PanelState = POSMON_STATEModeType_INVALID_OPERATION;\r\n        *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n        PosMon_Mdl_B.RP_beforeReset = 0;\r\n\r\n        /*  Relative position change is updated (actual position is not known!)\r\n           Postion = Relative position limited by 0 and 65535  */\r\n\r\n        /*  Reset for automatic learning  */\r\n      } else if (((PosMon_Mdl_DW.stPosReq_prev != PosMon_Mdl_DW.stPosReq_start) &&\r\n                  (PosMon_Mdl_DW.stPosReq_start == LearnAdap_Req_t_ClrReq_C)) ||\r\n                 ((PosMon_Mdl_DW.stPosReq_prev != PosMon_Mdl_DW.stPosReq_start) &&\r\n                  (PosMon_Mdl_DW.stPosReq_start ==\r\n                   LearnAdap_Req_t_ClrReqHardStopOpen_C))) {\r\n        PosMon_Mdl_exit_internal_NORMAL_OPERATION(rty_PanelState);\r\n        PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_RESET;\r\n        *rty_PanelState = POSMON_STATEModeType_RESET;\r\n        PosMon_Mdl_B.PosMon_isRelearnOut = false;\r\n        PosMon_Mdl_B.RP_beforeReset = rtu_localCurPos;\r\n\r\n        /*  Reset value to hard stop position  */\r\n      } else {\r\n        switch (PosMon_Mdl_DW.is_NORMAL_OPERATION) {\r\n         case PosMon_Mdl_IN_RESET:\r\n          /*  Directly go to erase NVM\r\n             before Relax of Mechanism  */\r\n          PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_WRITING_INVALID;\r\n          *rty_PanelState = POSMON_STATEModeType_WRITING_INVALID;\r\n\r\n          /*  If during movement power loss occurs the\r\n             position shall be unkown on wakeup   */\r\n          PosMon_Mdl_B.isNVMPosVldOut = PosMon_Mdl_FALSE;\r\n          *rty_NvmAccessState = NVMMgmt_ReqCmdType_Erase;\r\n          break;\r\n\r\n         case PosMon_Mdl_IN_RESTORE_RP:\r\n          PosMon_Mdl_B.RP_beforeReset = 0;\r\n          PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_WRITING_INVALID;\r\n          *rty_PanelState = POSMON_STATEModeType_WRITING_INVALID;\r\n\r\n          /*  If during movement power loss occurs the\r\n             position shall be unkown on wakeup   */\r\n          PosMon_Mdl_B.isNVMPosVldOut = PosMon_Mdl_FALSE;\r\n          *rty_NvmAccessState = NVMMgmt_ReqCmdType_Erase;\r\n          break;\r\n\r\n         case PosMon_Mdl_IN_VALID_AFTER_MOVING:\r\n          if (rtu_isMotorMoving) {\r\n            PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_VALID_MOVING;\r\n            *rty_PanelState = POSMON_STATEModeType_VALID_MOVING;\r\n\r\n            /*  Position = actual position  */\r\n            *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n          } else {\r\n            /*  Wait before write to\r\n               reduce NVM writes  */\r\n            if (rtu_tSmpPosMon_SC == 0U) {\r\n              tmp = MAX_int32_T;\r\n\r\n              /* Divide by zero handler */\r\n            } else {\r\n              tmp = (int32_T)((uint32_T)PosMon_Mdl_WRITE_WAIT_TIME /\r\n                              rtu_tSmpPosMon_SC);\r\n            }\r\n\r\n            if (PosMon_Mdl_DW.temporalCounter_i1 >= (uint32_T)tmp) {\r\n              guard1 = true;\r\n            } else {\r\n              if (rtu_tSmpPosMon_SC == 0U) {\r\n                tmp = MAX_int32_T;\r\n\r\n                /* Divide by zero handler */\r\n              } else {\r\n                tmp = (int32_T)((uint32_T)PosMon_Mdl_WRITE_REQUEST_WAIT_TIME /\r\n                                rtu_tSmpPosMon_SC);\r\n              }\r\n\r\n              if ((*rtu_stPosReq == LearnAdap_Req_t_SaveReq_C) &&\r\n                  (PosMon_Mdl_DW.temporalCounter_i1 >= (uint32_T)tmp)) {\r\n                guard1 = true;\r\n              } else if ((!rtu_PosMon_isCurPosVld) && (*rtu_stPosReq ==\r\n                          LearnAdap_Req_t_InterruptReq_C)) {\r\n                PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_RESTORE_RP;\r\n                *rty_PanelState = POSMON_STATEModeType_RESTORE_RP;\r\n              }\r\n            }\r\n          }\r\n          break;\r\n\r\n         case PosMon_Mdl_IN_VALID_IDLE:\r\n          *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n          if (rtu_isMotorMoving) {\r\n            PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_WRITING_INVALID;\r\n            *rty_PanelState = POSMON_STATEModeType_WRITING_INVALID;\r\n\r\n            /*  If during movement power loss occurs the\r\n               position shall be unkown on wakeup   */\r\n            PosMon_Mdl_B.isNVMPosVldOut = PosMon_Mdl_FALSE;\r\n            *rty_NvmAccessState = NVMMgmt_ReqCmdType_Erase;\r\n          } else if ((!rtu_PosMon_isCurPosVld) && (*rtu_stPosReq ==\r\n                      LearnAdap_Req_t_InterruptReq_C)) {\r\n            PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_RESTORE_RP;\r\n            *rty_PanelState = POSMON_STATEModeType_RESTORE_RP;\r\n          }\r\n          break;\r\n\r\n         case PosMon_Mdl_IN_VALID_MOVING:\r\n          *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n          if (!rtu_isMotorMoving) {\r\n            PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_VALID_AFTER_MOVING;\r\n            PosMon_Mdl_DW.temporalCounter_i1 = 0U;\r\n            *rty_PanelState = POSMON_STATEModeType_VALID_AFTER_MOVING;\r\n          }\r\n          break;\r\n\r\n         case PosMon_Mdl_IN_WRITE_NEWPOS:\r\n          *rty_NvmAccessState = NVMMgmt_ReqCmdType_Write;\r\n          if (rtu_PosMonNVMstate == IRS_E_OK) {\r\n            PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_VALID_IDLE;\r\n            *rty_PanelState = POSMON_STATEModeType_VALID_IDLE;\r\n            *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n          } else if (rtu_isMotorMoving) {\r\n            PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_WRITING_INVALID;\r\n            *rty_PanelState = POSMON_STATEModeType_WRITING_INVALID;\r\n\r\n            /*  If during movement power loss occurs the\r\n               position shall be unkown on wakeup   */\r\n            PosMon_Mdl_B.isNVMPosVldOut = PosMon_Mdl_FALSE;\r\n            *rty_NvmAccessState = NVMMgmt_ReqCmdType_Erase;\r\n          }\r\n          break;\r\n\r\n         default:\r\n          /* case IN_WRITING_INVALID: */\r\n          *rty_NvmAccessState = NVMMgmt_ReqCmdType_Erase;\r\n          if (rtu_PosMonNVMstate == IRS_E_OK) {\r\n            PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_VALID_MOVING;\r\n            *rty_PanelState = POSMON_STATEModeType_VALID_MOVING;\r\n\r\n            /*  Position = actual position  */\r\n            *rty_NvmAccessState = NVMMgmt_ReqCmdType_Void;\r\n          }\r\n          break;\r\n        }\r\n      }\r\n      break;\r\n    }\r\n\r\n    if (guard1) {\r\n      PosMon_Mdl_DW.is_NORMAL_OPERATION = PosMon_Mdl_IN_WRITE_NEWPOS;\r\n      *rty_PanelState = POSMON_STATEModeType_WRITE_NEWPOS;\r\n\r\n      /*  write newposition and position valid flag to NVM  */\r\n      PosMon_Mdl_B.isNVMPosVldOut = true;\r\n      *rty_NvmAccessState = NVMMgmt_ReqCmdType_Write;\r\n    }\r\n  }\r\n\r\n  /* End of Chart: '<S4>/PositionState' */\r\n\r\n  /* DataTypeConversion: '<S4>/Data Type Conversion1' */\r\n  *rty_isCurPosVld = PosMon_Mdl_B.isCurPosVld;\r\n\r\n  /* DataTypeConversion: '<S4>/Data Type Conversion2' */\r\n  *rty_isNVMPosVldOut = PosMon_Mdl_B.isNVMPosVldOut;\r\n\r\n  /* DataTypeConversion: '<S4>/Data Type Conversion3' */\r\n  *rty_isRelearnOut = PosMon_Mdl_B.PosMon_isRelearnOut;\r\n\r\n  /* SignalConversion generated from: '<S4>/RP_beforeReset' */\r\n  *rty_RP_beforeReset = PosMon_Mdl_B.RP_beforeReset;\r\n\r\n  /* SignalConversion generated from: '<S4>/stPosSave' */\r\n  *rty_stPosSave = PosMon_Mdl_B.RP_beforeReset;\r\n}\r\n\r\n/* Output and update for atomic system: '<S2>/PositionOutOfRange' */\r\nvoid PosMon_Mdl_PositionOutOfRange(int16_T rtu_localCurPos, const\r\n  CfgParam_hPosBus_t *rtu_hPosTabel, boolean_T rtu_isCurPosVld, const\r\n  LearnAdap_Mode_t *rtu_LearnAdap_stMode, const boolean_T *rtu_isStartupRdy_In,\r\n  uint8_T rtu_hOutOfRangeOffset_C, const boolean_T *rtu_isLearnComplete,\r\n  boolean_T *rty_isOutOfRange, boolean_T *rty_isStartupRdy)\r\n{\r\n  boolean_T rtb_LogicalOperator1;\r\n  boolean_T rtb_PosMon_isOutOfRange_b3;\r\n\r\n  /* SignalConversion generated from: '<S7>/isStartupRdy_In' */\r\n  *rty_isStartupRdy = *rtu_isStartupRdy_In;\r\n\r\n  /* Logic: '<S7>/Logical Operator1' */\r\n  rtb_LogicalOperator1 = (rtu_isCurPosVld && (*rty_isStartupRdy));\r\n\r\n  /* UnitDelay: '<S7>/Unit Delay' */\r\n  rtb_PosMon_isOutOfRange_b3 = PosMon_Mdl_DW.UnitDelay_DSTATE;\r\n\r\n  /* Switch: '<S7>/Switch' incorporates:\r\n   *  Logic: '<S7>/Logical Operator'\r\n   *  RelationalOperator: '<S7>/Relational Operator'\r\n   *  RelationalOperator: '<S7>/Relational Operator1'\r\n   *  Sum: '<S7>/Add1'\r\n   *  Sum: '<S7>/Subtract'\r\n   */\r\n  if (rtb_LogicalOperator1) {\r\n    rtb_PosMon_isOutOfRange_b3 = (((int16_T)((int16_T)\r\n      rtu_hPosTabel->hHardStopCls - rtu_hOutOfRangeOffset_C) > rtu_localCurPos) ||\r\n      (rtu_localCurPos > (int16_T)((uint32_T)rtu_hPosTabel->hHardStopOpn +\r\n      rtu_hOutOfRangeOffset_C)));\r\n  }\r\n\r\n  /* End of Switch: '<S7>/Switch' */\r\n\r\n  /* Logic: '<S7>/Logical Operator2' incorporates:\r\n   *  Constant: '<S26>/Constant'\r\n   *  Constant: '<S27>/Constant'\r\n   *  RelationalOperator: '<S26>/Compare'\r\n   *  RelationalOperator: '<S27>/Compare'\r\n   */\r\n  *rty_isOutOfRange = ((*rtu_LearnAdap_stMode != LearnAdap_Mode_t_AdaptionOpen_C)\r\n                       && (*rtu_LearnAdap_stMode !=\r\n    LearnAdap_Mode_t_AdaptionClose_C) && (*rtu_isLearnComplete) &&\r\n                       rtb_PosMon_isOutOfRange_b3);\r\n\r\n  /* Update for UnitDelay: '<S7>/Unit Delay' */\r\n  PosMon_Mdl_DW.UnitDelay_DSTATE = *rty_isOutOfRange;\r\n}\r\n\r\n/* Output and update for atomic system: '<S2>/ShutdownReadyDetermination' */\r\nvoid PosMon_Mdl_ShutdownReadyDetermination(POSMON_STATEModeType rtu_PanelState,\r\n  boolean_T *rty_isShdnRdy)\r\n{\r\n  /* Logic: '<S8>/Logical Operator' incorporates:\r\n   *  Constant: '<S29>/Constant'\r\n   *  Constant: '<S30>/Constant'\r\n   *  Constant: '<S31>/Constant'\r\n   *  RelationalOperator: '<S29>/Compare'\r\n   *  RelationalOperator: '<S30>/Compare'\r\n   *  RelationalOperator: '<S31>/Compare'\r\n   */\r\n  *rty_isShdnRdy = ((rtu_PanelState == POSMON_STATEModeType_VALID_IDLE) ||\r\n                    (rtu_PanelState == POSMON_STATEModeType_GOING_INVALID) ||\r\n                    (rtu_PanelState == POSMON_STATEModeType_INVALID_OPERATION));\r\n}\r\n\r\n/* Output and update for atomic system: '<S2>/UpdatePosition' */\r\nvoid PosMon_Mdl_UpdatePosition(POSMON_STATEModeType rtu_PanelState, int16_T\r\n  rtu_RP_beforeReset, uint16_T rtu_hPosTbl_hHardStopOpn, const LearnAdap_Req_t\r\n  *rtu_stPosReq, int16_T rtu_localCurPosIn, int16_T *rty_hCurPosSigned, uint16_T\r\n  *rty_hCurPosUnsigned)\r\n{\r\n  /* MultiPortSwitch: '<S9>/MultiportSwitch' incorporates:\r\n   *  Sum: '<S9>/Add'\r\n   */\r\n  switch (rtu_PanelState) {\r\n   case POSMON_STATEModeType_VALID_IDLE:\r\n    *rty_hCurPosSigned = rtu_localCurPosIn;\r\n    break;\r\n\r\n   case POSMON_STATEModeType_RESET:\r\n    /* Switch: '<S9>/Switch' incorporates:\r\n     *  Constant: '<S34>/Constant'\r\n     *  Constant: '<S9>/Constant'\r\n     *  DataTypeConversion: '<S9>/Data Type Conversion1'\r\n     *  RelationalOperator: '<S34>/Compare'\r\n     */\r\n    if (*rtu_stPosReq == LearnAdap_Req_t_ClrReqHardStopOpen_C) {\r\n      *rty_hCurPosSigned = (int16_T)rtu_hPosTbl_hHardStopOpn;\r\n    } else {\r\n      *rty_hCurPosSigned = 0;\r\n    }\r\n\r\n    /* End of Switch: '<S9>/Switch' */\r\n    break;\r\n\r\n   case POSMON_STATEModeType_RESTORE_RP:\r\n    *rty_hCurPosSigned = (int16_T)(rtu_RP_beforeReset + rtu_localCurPosIn);\r\n    break;\r\n\r\n   case POSMON_STATEModeType_WRITE_NEWPOS:\r\n    *rty_hCurPosSigned = rtu_localCurPosIn;\r\n    break;\r\n\r\n   default:\r\n    *rty_hCurPosSigned = rtu_localCurPosIn;\r\n    break;\r\n  }\r\n\r\n  /* End of MultiPortSwitch: '<S9>/MultiportSwitch' */\r\n\r\n  /* Saturate: '<S9>/Saturation' */\r\n  if (*rty_hCurPosSigned >= 0) {\r\n    /* DataTypeConversion: '<S9>/Data Type Conversion' */\r\n    *rty_hCurPosUnsigned = (uint16_T)*rty_hCurPosSigned;\r\n  } else {\r\n    /* DataTypeConversion: '<S9>/Data Type Conversion' */\r\n    *rty_hCurPosUnsigned = 0U;\r\n  }\r\n\r\n  /* End of Saturate: '<S9>/Saturation' */\r\n}\r\n\r\n/* System initialize for referenced model: 'PosMon_Mdl' */\r\nvoid PosMon_Mdl_Init(void)\r\n{\r\n  /* SystemInitialize for Atomic SubSystem: '<S2>/DetermineAndWriteState' */\r\n  PosMon_Mdl_DetermineAndWriteState_Init(&PosMon_Mdl_B.POSMON_STATE,\r\n    &PosMon_Mdl_B.NvmAccess);\r\n\r\n  /* End of SystemInitialize for SubSystem: '<S2>/DetermineAndWriteState' */\r\n}\r\n\r\n/* Output and update for referenced model: 'PosMon_Mdl' */\r\nvoid PosMon_Mdl(const LearnAdap_Mode_t\r\n                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode, const\r\n                LearnAdap_Req_t\r\n                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq, const\r\n                uint16_T\r\n                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop, const\r\n                uint16_T\r\n                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newSoftStop, const\r\n                boolean_T\r\n                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete,\r\n                const uint8_T\r\n                *rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt, const\r\n                boolean_T\r\n                *rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy, const\r\n                VoltMon_VoltClass_t *rtu_VoltMonBus_VoltMon_stVoltClass, const\r\n                uint8_T\r\n                *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic,\r\n                boolean_T *rty_PosMonBus_PosMon_isCurPosVld, boolean_T\r\n                *rty_PosMonBus_PosMon_isCurPosVldNvm, int16_T\r\n                *rty_PosMonBus_PosMon_hCurPosSigned, boolean_T\r\n                *rty_PosMonBus_PosMon_isRelearn, int16_T\r\n                *rty_PosMonBus_PosMon_stPosSave, uint16_T\r\n                *rty_PosMonBus_PosMon_hCurPos, PosMon_Area_t\r\n                *rty_PosMonBus_PosMon_curPanelArea, PosMon_PnlPosArea_t\r\n                *rty_PosMonBus_PosMon_panelCurPosArea, boolean_T\r\n                *rty_PosMonBus_PosMon_isShdnRdy, boolean_T\r\n                *rty_PosMonBus_PosMon_isOutOfRange, boolean_T\r\n                *rty_PosMonBus_PosMon_isStartupRdy, uint16_T\r\n                *rty_PosMonBus_PosMon_relativePinionSize, uint16_T\r\n                *rty_PosMonBus_PosMon_hPosTbl_hHardStopCls, uint16_T\r\n                *rty_PosMonBus_PosMon_hPosTbl_hSoftStopCls, uint16_T\r\n                *rty_PosMonBus_PosMon_hPosTbl_hNearClsSld, uint16_T\r\n                *rty_PosMonBus_PosMon_hPosTbl_hComfortMidStop, uint16_T\r\n                *rty_PosMonBus_PosMon_hPosTbl_hSoftStopOpn, uint16_T\r\n                *rty_PosMonBus_PosMon_hPosTbl_hHardStopOpn)\r\n{\r\n  CfgParam_hPosBus_t rtb_Switch;\r\n  CfgParam_hPosBus_t rtb_Switch2;\r\n  int32_T tmp;\r\n  int16_T localCurPos;\r\n  int16_T rtb_DataStoreRead2;\r\n  int16_T rtb_PosMon_hCurPosSigned;\r\n  int16_T rtb_RP_beforeReset;\r\n  int16_T rtb_RP_beforeReset_n;\r\n  uint16_T rtb_DataTypeConversion;\r\n  uint16_T rtb_PosMon_hCurPos;\r\n  uint16_T rtb_Switch1_hComfortMidStop;\r\n  uint16_T rtb_Switch1_hHardStopCls;\r\n  uint16_T rtb_Switch1_hNearClsSld;\r\n  uint16_T rtb_Switch1_hSoftStopCls;\r\n  uint16_T rtb_Switch1_hSoftStopOpn;\r\n  boolean_T Compare;\r\n  boolean_T rtb_LogicalOperator1;\r\n  boolean_T rtb_PosMon_isCurPosVld;\r\n  boolean_T rtb_PosMon_isRelearn;\r\n  boolean_T rtb_PosMon_isShdnRdy;\r\n\r\n  /* DataStoreRead: '<Root>/Data Store Read2' */\r\n  rtb_DataStoreRead2 = PosMon_RP_AND_VLDTY.PosMon_hCurPosSigned;\r\n\r\n  /* Outputs for Atomic SubSystem: '<S2>/CalculatePosition' */\r\n  /* DataStoreRead: '<Root>/DataStoreRead1' incorporates:\r\n   *  Constant: '<S2>/Constant1'\r\n   *  Constant: '<S2>/Constant4'\r\n   *  Constant: '<S2>/Constant5'\r\n   *  DataStoreRead: '<Root>/Data Store Read2'\r\n   */\r\n  PosMon_Mdl_CalculatePosition\r\n    (rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic,\r\n     CfgParam_CAL.typeHallDir, 1, -1, rtu_VoltMonBus_VoltMon_stVoltClass, 0,\r\n     PosMon_RP_AND_VLDTY.PosMon_hCurPosSigned, &Compare, &localCurPos);\r\n\r\n  /* End of Outputs for SubSystem: '<S2>/CalculatePosition' */\r\n\r\n  /* Outputs for Atomic SubSystem: '<S2>/DetermineAndWriteState' */\r\n  /* Constant: '<Root>/Constant11' incorporates:\r\n   *  Constant: '<Root>/Constant'\r\n   *  Constant: '<Root>/Constant1'\r\n   *  Constant: '<Root>/Constant2'\r\n   *  DataStoreRead: '<Root>/Data Store Read1'\r\n   *  DataStoreRead: '<Root>/Data Store Read3'\r\n   *  UnitDelay: '<S2>/Previous_NVM_State'\r\n   */\r\n  PosMon_Mdl_DetermineAndWriteState(((uint16_T)RoofSys_tSmpPosMon_SC),\r\n    PosMon_RP_AND_VLDTY.PosMon_isCurPosVld, false, false, false,\r\n    rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt, PosMon_isRelearn,\r\n    Compare, localCurPos, rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq,\r\n    PosMon_Mdl_DW.Previous_NVM_State_DSTATE, &rtb_PosMon_isCurPosVld,\r\n    &rtb_PosMon_isShdnRdy, &rtb_RP_beforeReset_n, &rtb_PosMon_isRelearn,\r\n    &PosMon_Mdl_B.POSMON_STATE, &rtb_RP_beforeReset, &PosMon_Mdl_B.NvmAccess);\r\n\r\n  /* End of Outputs for SubSystem: '<S2>/DetermineAndWriteState' */\r\n\r\n  /* DataStoreWrite: '<Root>/Data Store Write' */\r\n  PosMon_RP_AND_VLDTY.PosMon_isCurPosVld = rtb_PosMon_isCurPosVld;\r\n\r\n  /* DataStoreWrite: '<Root>/Data Store Write3' */\r\n  PosMon_isRelearn = rtb_PosMon_isRelearn;\r\n\r\n  /* Outputs for Enabled SubSystem: '<S6>/Subsystem' incorporates:\r\n   *  EnablePort: '<S25>/Enable'\r\n   */\r\n  /* RelationalOperator: '<S22>/Compare' incorporates:\r\n   *  Constant: '<S22>/Constant'\r\n   */\r\n  if (PosMon_Mdl_B.NvmAccess == NVMMgmt_ReqCmdType_Write) {\r\n    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S25>/S-Function' incorporates:\r\n     *  Constant: '<S6>/Constant3'\r\n     */\r\n    /* S-Function Block: <S25>/S-Function */\r\n    PosMon_Mdl_B.SFunction = NVMMan_enRequestNVMWrite_new(2U);\r\n  }\r\n\r\n  /* End of RelationalOperator: '<S22>/Compare' */\r\n  /* End of Outputs for SubSystem: '<S6>/Subsystem' */\r\n\r\n  /* Switch: '<S6>/Switch1' incorporates:\r\n   *  Constant: '<S23>/Constant'\r\n   *  Constant: '<S6>/Constant'\r\n   *  Constant: '<S6>/Constant2'\r\n   *  RelationalOperator: '<S23>/Compare'\r\n   *  UnitDelay: '<S2>/Previous_NVM_State'\r\n   */\r\n  if (PosMon_Mdl_B.SFunction != 0) {\r\n    PosMon_Mdl_DW.Previous_NVM_State_DSTATE = IRS_E_NOK;\r\n  } else {\r\n    PosMon_Mdl_DW.Previous_NVM_State_DSTATE = IRS_E_OK;\r\n  }\r\n\r\n  /* End of Switch: '<S6>/Switch1' */\r\n\r\n  /* DataStoreWrite: '<Root>/Data Store Write4' incorporates:\r\n   *  UnitDelay: '<S2>/Previous_NVM_State'\r\n   */\r\n  NVMAccessState = PosMon_Mdl_DW.Previous_NVM_State_DSTATE;\r\n\r\n  /* Outputs for Atomic SubSystem: '<S2>/UpdatePosition' */\r\n  /* DataStoreRead: '<Root>/DataStoreRead8' */\r\n  PosMon_Mdl_UpdatePosition(PosMon_Mdl_B.POSMON_STATE, rtb_RP_beforeReset,\r\n    CfgParam_CAL.hPosTbl.hHardStopOpn,\r\n    rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq, localCurPos,\r\n    &rtb_PosMon_hCurPosSigned, &rtb_PosMon_hCurPos);\r\n\r\n  /* End of Outputs for SubSystem: '<S2>/UpdatePosition' */\r\n\r\n  /* DataStoreWrite: '<Root>/Data Store Write5' */\r\n  PosMon_RP_AND_VLDTY.PosMon_hCurPosSigned = rtb_PosMon_hCurPosSigned;\r\n\r\n  /* DataStoreWrite: '<Root>/Data Store Write6' */\r\n  PosMon_isCurPosVldNvm = rtb_PosMon_isShdnRdy;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_isCurPosVld = rtb_PosMon_isCurPosVld;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_isCurPosVldNvm = rtb_PosMon_isShdnRdy;\r\n\r\n  /* RelationalOperator: '<S36>/Compare' incorporates:\r\n   *  Constant: '<S36>/Constant'\r\n   */\r\n  Compare = (*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop == 0);\r\n\r\n  /* Logic: '<S10>/Logical Operator1' */\r\n  rtb_LogicalOperator1 =\r\n    !*rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete;\r\n\r\n  /* RelationalOperator: '<S10>/Relational Operator' incorporates:\r\n   *  DataStoreRead: '<Root>/DataStoreRead2'\r\n   */\r\n  rtb_PosMon_isShdnRdy = (CfgParam_CAL.hPosTbl.hComfortMidStop !=\r\n    CfgParam_CAL.hPosTbl.hSoftStopOpn);\r\n\r\n  /* Switch: '<S10>/Switch' incorporates:\r\n   *  DataStoreRead: '<Root>/DataStoreRead2'\r\n   */\r\n  if (rtb_PosMon_isShdnRdy) {\r\n    rtb_DataTypeConversion = CfgParam_CAL.hPosTbl.hComfortMidStop;\r\n  } else {\r\n    rtb_DataTypeConversion =\r\n      *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newSoftStop;\r\n  }\r\n\r\n  /* End of Switch: '<S10>/Switch' */\r\n\r\n  /* BusAssignment: '<S10>/Bus Assignment' incorporates:\r\n   *  DataStoreRead: '<Root>/DataStoreRead2'\r\n   */\r\n  rtb_Switch = CfgParam_CAL.hPosTbl;\r\n  rtb_Switch.hComfortMidStop = rtb_DataTypeConversion;\r\n  rtb_Switch.hSoftStopOpn =\r\n    *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newSoftStop;\r\n  rtb_Switch.hHardStopOpn =\r\n    *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop;\r\n\r\n  /* Switch: '<S10>/Switch2' incorporates:\r\n   *  DataStoreRead: '<Root>/DataStoreRead2'\r\n   *  Logic: '<S10>/Logical Operator'\r\n   */\r\n  if (Compare || rtb_LogicalOperator1) {\r\n    rtb_Switch2 = CfgParam_CAL.hPosTbl;\r\n  } else {\r\n    rtb_Switch2 = rtb_Switch;\r\n  }\r\n\r\n  /* End of Switch: '<S10>/Switch2' */\r\n\r\n  /* Outputs for Atomic SubSystem: '<S2>/PositionOutOfRange' */\r\n  /* Constant: '<S2>/Constant8' incorporates:\r\n   *  UnitDelay: '<S7>/Unit Delay'\r\n   */\r\n  PosMon_Mdl_PositionOutOfRange(rtb_DataStoreRead2, &rtb_Switch2,\r\n    rtb_PosMon_isCurPosVld, rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode,\r\n    rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy, 80,\r\n    rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete,\r\n    &PosMon_Mdl_DW.UnitDelay_DSTATE, &rtb_PosMon_isShdnRdy);\r\n\r\n  /* End of Outputs for SubSystem: '<S2>/PositionOutOfRange' */\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_isStartupRdy = rtb_PosMon_isShdnRdy;\r\n\r\n  /* Sum: '<S11>/Subtract1' incorporates:\r\n   *  Saturate: '<S11>/Saturation'\r\n   */\r\n  rtb_DataStoreRead2 = (int16_T)((rtb_PosMon_hCurPosSigned -\r\n    rtb_Switch2.hSoftStopCls) >> 1);\r\n\r\n  /* Product: '<S11>/Divide' incorporates:\r\n   *  Constant: '<S11>/Constant9'\r\n   *  Product: '<S11>/Product'\r\n   *  Saturate: '<S11>/Saturation'\r\n   *  Sum: '<S11>/Subtract'\r\n   */\r\n  tmp = (uint16_T)(rtb_Switch2.hSoftStopOpn - rtb_Switch2.hSoftStopCls);\r\n  if (tmp == 0) {\r\n    tmp = MAX_int32_T;\r\n\r\n    /* Divide by zero handler */\r\n  } else {\r\n    if (rtb_DataStoreRead2 > 16384) {\r\n      /* Saturate: '<S11>/Saturation' */\r\n      rtb_DataStoreRead2 = 16384;\r\n    } else if (rtb_DataStoreRead2 < 0) {\r\n      /* Saturate: '<S11>/Saturation' */\r\n      rtb_DataStoreRead2 = 0;\r\n    }\r\n\r\n    tmp = rtb_DataStoreRead2 * 192 / tmp;\r\n  }\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  Constant: '<S11>/Constant10'\r\n   *  DataTypeConversion: '<S11>/Data Type Conversion'\r\n   *  Product: '<S11>/Divide'\r\n   *  Sum: '<S11>/Add'\r\n   */\r\n  *rty_PosMonBus_PosMon_relativePinionSize = (uint16_T)((uint16_T)((uint16_T)tmp\r\n    << 1) + 256U);\r\n\r\n  /* BusAssignment: '<S12>/Bus Assignment' incorporates:\r\n   *  DataStoreRead: '<Root>/Data Store Read5'\r\n   *  DataStoreRead: '<Root>/Data Store Read7'\r\n   */\r\n  rtb_Switch = rtb_Switch2;\r\n  rtb_Switch.hSoftStopCls = PIDDID_SoftClosePos;\r\n  rtb_Switch.hSoftStopOpn = PIDDID_SoftOpenPos;\r\n\r\n  /* Switch: '<S12>/Switch' incorporates:\r\n   *  DataStoreRead: '<Root>/Data Store Read4'\r\n   */\r\n  if (!PIDDID_isOpenClosePositionRtnEna) {\r\n    rtb_Switch = rtb_Switch2;\r\n  }\r\n\r\n  /* End of Switch: '<S12>/Switch' */\r\n\r\n  /* BusAssignment: '<S12>/Bus Assignment1' incorporates:\r\n   *  DataStoreRead: '<Root>/Data Store Read8'\r\n   *  Switch: '<S12>/Switch1'\r\n   */\r\n  rtb_Switch1_hHardStopCls = rtb_Switch.hHardStopCls;\r\n  rtb_Switch1_hSoftStopCls = rtb_Switch.hSoftStopCls;\r\n  rtb_Switch1_hNearClsSld = rtb_Switch.hNearClsSld;\r\n  rtb_Switch1_hSoftStopOpn = rtb_Switch.hSoftStopOpn;\r\n  rtb_DataTypeConversion = rtb_Switch.hHardStopOpn;\r\n  rtb_Switch1_hComfortMidStop = PIDDID_ControlIntermediatePos;\r\n\r\n  /* Switch: '<S12>/Switch1' incorporates:\r\n   *  BusAssignment: '<S12>/Bus Assignment1'\r\n   *  DataStoreRead: '<Root>/Data Store Read6'\r\n   */\r\n  if (!PIDDID_isIntermediatePositionRtnEna) {\r\n    rtb_Switch1_hHardStopCls = rtb_Switch.hHardStopCls;\r\n    rtb_Switch1_hSoftStopCls = rtb_Switch.hSoftStopCls;\r\n    rtb_Switch1_hNearClsSld = rtb_Switch.hNearClsSld;\r\n    rtb_Switch1_hComfortMidStop = rtb_Switch.hComfortMidStop;\r\n    rtb_Switch1_hSoftStopOpn = rtb_Switch.hSoftStopOpn;\r\n    rtb_DataTypeConversion = rtb_Switch.hHardStopOpn;\r\n  }\r\n\r\n  /* End of Switch: '<S12>/Switch1' */\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  Switch: '<S12>/Switch1'\r\n   */\r\n  *rty_PosMonBus_PosMon_hPosTbl_hHardStopCls = rtb_Switch1_hHardStopCls;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  Switch: '<S12>/Switch1'\r\n   */\r\n  *rty_PosMonBus_PosMon_hPosTbl_hSoftStopCls = rtb_Switch1_hSoftStopCls;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  Switch: '<S12>/Switch1'\r\n   */\r\n  *rty_PosMonBus_PosMon_hPosTbl_hNearClsSld = rtb_Switch1_hNearClsSld;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  Switch: '<S12>/Switch1'\r\n   */\r\n  *rty_PosMonBus_PosMon_hPosTbl_hComfortMidStop = rtb_Switch1_hComfortMidStop;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  Switch: '<S12>/Switch1'\r\n   */\r\n  *rty_PosMonBus_PosMon_hPosTbl_hSoftStopOpn = rtb_Switch1_hSoftStopOpn;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  Switch: '<S12>/Switch1'\r\n   */\r\n  *rty_PosMonBus_PosMon_hPosTbl_hHardStopOpn = rtb_DataTypeConversion;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_hCurPosSigned = rtb_PosMon_hCurPosSigned;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_isRelearn = rtb_PosMon_isRelearn;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_stPosSave = rtb_RP_beforeReset_n;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_hCurPos = rtb_PosMon_hCurPos;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  SignalConversion generated from: '<S2>/PosMon_curPanelArea'\r\n   */\r\n  *rty_PosMonBus_PosMon_curPanelArea =\r\n    PosMon_Mdl_ConstB.TmpBufferAtTmpGroundAtPosMon_curPanelAreaInport1Outport1;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  SignalConversion generated from: '<S2>/PosMon_panelCurPosArea'\r\n   */\r\n  *rty_PosMonBus_PosMon_panelCurPosArea =\r\n    PosMon_Mdl_ConstB.TmpBufferAtTmpGroundAtPosMon_panelCurPosAreaInport1Outport1;\r\n\r\n  /* Outputs for Atomic SubSystem: '<S2>/ShutdownReadyDetermination' */\r\n  PosMon_Mdl_ShutdownReadyDetermination(PosMon_Mdl_B.POSMON_STATE,\r\n    &rtb_PosMon_isShdnRdy);\r\n\r\n  /* End of Outputs for SubSystem: '<S2>/ShutdownReadyDetermination' */\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' */\r\n  *rty_PosMonBus_PosMon_isShdnRdy = rtb_PosMon_isShdnRdy;\r\n\r\n  /* SignalConversion generated from: '<Root>/PosMonBus' incorporates:\r\n   *  UnitDelay: '<S7>/Unit Delay'\r\n   */\r\n  *rty_PosMonBus_PosMon_isOutOfRange = PosMon_Mdl_DW.UnitDelay_DSTATE;\r\n}\r\n\r\n/* Model initialize function */\r\nvoid PosMon_Mdl_initialize(const char_T **rt_errorStatus)\r\n{\r\n  RT_MODEL_PosMon_Mdl_T *const PosMon_Mdl_M = &(PosMon_Mdl_MdlrefDW.rtm);\r\n\r\n  /* Registration code */\r\n\r\n  /* initialize error status */\r\n  rtmSetErrorStatusPointer(PosMon_Mdl_M, rt_errorStatus);\r\n}\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"PosMon_Mdl.h","type":"header","group":"model","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\PosMon_Mdl","tag":"","groupDisplay":"Model files","code":"/*\r\n * File: PosMon_Mdl.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n *\r\n * Target selection: ert.tlc\r\n * Embedded hardware selection: NXP->Cortex-M0/M0+\r\n * Code generation objectives: Unspecified\r\n * Validation result: Not run\r\n */\r\n\r\n#ifndef RTW_HEADER_PosMon_Mdl_h_\r\n#define RTW_HEADER_PosMon_Mdl_h_\r\n#ifndef PosMon_Mdl_COMMON_INCLUDES_\r\n#define PosMon_Mdl_COMMON_INCLUDES_\r\n#include \"rtwtypes.h\"\r\n#endif                                 /* PosMon_Mdl_COMMON_INCLUDES_ */\r\n\r\n#include \"VoltMon_ExpTypes.h\"\r\n#include \"PosMon_Mdl_types.h\"\r\n#include \"NvM_ExpTypes.h\"\r\n#include \"LearnAdp_ExpTypes.h\"\r\n#include \"CfgParam_Mdl_ExpTypes.h\"\r\n#include \"PosMon_ExpTypes.h\"\r\n#include \"ThermProt_ExpTypes.h\"\r\n#include \"StateMach_ExpTypes.h\"\r\n#include \"RoofSys_CommDefines.h\"\r\n#include \"BlockDet_ExpTypes.h\"\r\n#include \"RoofOper_ExpTypes.h\"\r\n#include \"RoofSys_ExpTypes.h\"\r\n#include \"CtrlLogic_ExpTypes.h\"\r\n#include \"CmdLogic_ExpTypes.h\"\r\n#include \"APDet_ExpTypes.h\"\r\n#include \"HallDeco_ExpTypes.h\"\r\n\r\n/* Includes for objects with custom storage classes */\r\n#include \"RoofSys.h\"\r\n#include \"CfgParam_Mdl_Exp.h\"\r\n#include \"PosMon_Exp.h\"\r\n\r\n/* user code (top of header file) */\r\n#include \"Stdint.h\"\r\n#include \"S32K118.h\"\r\n#include \"pins_driver.h\"\r\n#include \"TaskSch_Man.h\"\r\n#include \"pins_port_hw_access.h\"\r\n#include \"irs_lib.h\"\r\n#include \"irs_std_types.h\"\r\n\r\n/* Block signals for model 'PosMon_Mdl' */\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\ntypedef struct {\r\n  POSMON_STATEModeType POSMON_STATE;   /* '<S4>/PositionState' */\r\n  int16_T RP_beforeReset;              /* '<S4>/PositionState' */\r\n  uint8_T SFunction;                   /* '<S25>/S-Function' */\r\n  boolean_T isNVMPosVldOut;            /* '<S4>/PositionState' */\r\n  boolean_T isCurPosVld;               /* '<S4>/PositionState' */\r\n  boolean_T PosMon_isRelearnOut;       /* '<S4>/PositionState' */\r\n  NVMMgmt_ReqCmdType NvmAccess;        /* '<S4>/PositionState' */\r\n} B_PosMon_Mdl_c_T;\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n/* Block states (default storage) for model 'PosMon_Mdl' */\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\ntypedef struct {\r\n  stdReturn_t Previous_NVM_State_DSTATE;/* '<S2>/Previous_NVM_State' */\r\n  uint32_T temporalCounter_i1;         /* '<S4>/PositionState' */\r\n  uint8_T UD_DSTATE;                   /* '<S18>/UD' */\r\n  boolean_T UnitDelay_DSTATE;          /* '<S7>/Unit Delay' */\r\n  uint8_T is_c3_PosMon_Mdl;            /* '<S4>/PositionState' */\r\n  uint8_T is_NORMAL_OPERATION;         /* '<S4>/PositionState' */\r\n  uint8_T is_active_c3_PosMon_Mdl;     /* '<S4>/PositionState' */\r\n  LearnAdap_Req_t stPosReq_prev;       /* '<S4>/PositionState' */\r\n  LearnAdap_Req_t stPosReq_start;      /* '<S4>/PositionState' */\r\n} DW_PosMon_Mdl_f_T;\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n/* Invariant block signals for model 'PosMon_Mdl' */\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\ntypedef struct {\r\n  const PosMon_PnlPosArea_t\r\n    TmpBufferAtTmpGroundAtPosMon_panelCurPosAreaInport1Outport1;\r\n  const PosMon_Area_t TmpBufferAtTmpGroundAtPosMon_curPanelAreaInport1Outport1;\r\n} ConstB_PosMon_Mdl_h_T;\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\n/* Real-time Model Data Structure */\r\nstruct tag_RTM_PosMon_Mdl_T {\r\n  const char_T **errorStatus;\r\n};\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\ntypedef struct {\r\n  RT_MODEL_PosMon_Mdl_T rtm;\r\n} MdlrefDW_PosMon_Mdl_T;\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n/*\r\n * Exported States\r\n *\r\n * Note: Exported states are block states with an exported global\r\n * storage class designation.  Code generation will declare the memory for these\r\n * states and exports their symbols.\r\n *\r\n */\r\nextern stdReturn_t NVMAccessState; /* Simulink.Signal object 'NVMAccessState' */\r\nextern uint16_T PIDDID_ControlIntermediatePos;\r\n                    /* Simulink.Signal object 'PIDDID_ControlIntermediatePos' */\r\nextern uint16_T PIDDID_SoftClosePos;\r\n                              /* Simulink.Signal object 'PIDDID_SoftClosePos' */\r\nextern uint16_T PIDDID_SoftOpenPos;\r\n                               /* Simulink.Signal object 'PIDDID_SoftOpenPos' */\r\nextern boolean_T PosMon_isCurPosVldNvm;\r\n                               /* Simulink.Signal object 'PosMon_isCurPosVldNvm'\r\n                                * Current position validity NVM:\r\n                                  0 invalid\r\n                                  1: valid\r\n                                */\r\nextern boolean_T PosMon_isRelearn;\r\n                                 /* Simulink.Signal object 'PosMon_isRelearn' */\r\nextern void PosMon_Mdl_Init(void);\r\nextern void PosMon_Mdl(const LearnAdap_Mode_t\r\n  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode, const LearnAdap_Req_t\r\n  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq, const uint16_T\r\n  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop, const uint16_T\r\n  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newSoftStop, const boolean_T\r\n  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete, const uint8_T\r\n  *rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt, const boolean_T\r\n  *rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy, const\r\n  VoltMon_VoltClass_t *rtu_VoltMonBus_VoltMon_stVoltClass, const uint8_T\r\n  *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic, boolean_T\r\n  *rty_PosMonBus_PosMon_isCurPosVld, boolean_T\r\n  *rty_PosMonBus_PosMon_isCurPosVldNvm, int16_T\r\n  *rty_PosMonBus_PosMon_hCurPosSigned, boolean_T *rty_PosMonBus_PosMon_isRelearn,\r\n  int16_T *rty_PosMonBus_PosMon_stPosSave, uint16_T\r\n  *rty_PosMonBus_PosMon_hCurPos, PosMon_Area_t\r\n  *rty_PosMonBus_PosMon_curPanelArea, PosMon_PnlPosArea_t\r\n  *rty_PosMonBus_PosMon_panelCurPosArea, boolean_T\r\n  *rty_PosMonBus_PosMon_isShdnRdy, boolean_T *rty_PosMonBus_PosMon_isOutOfRange,\r\n  boolean_T *rty_PosMonBus_PosMon_isStartupRdy, uint16_T\r\n  *rty_PosMonBus_PosMon_relativePinionSize, uint16_T\r\n  *rty_PosMonBus_PosMon_hPosTbl_hHardStopCls, uint16_T\r\n  *rty_PosMonBus_PosMon_hPosTbl_hSoftStopCls, uint16_T\r\n  *rty_PosMonBus_PosMon_hPosTbl_hNearClsSld, uint16_T\r\n  *rty_PosMonBus_PosMon_hPosTbl_hComfortMidStop, uint16_T\r\n  *rty_PosMonBus_PosMon_hPosTbl_hSoftStopOpn, uint16_T\r\n  *rty_PosMonBus_PosMon_hPosTbl_hHardStopOpn);\r\n\r\n/* Model reference registration function */\r\nextern void PosMon_Mdl_initialize(const char_T **rt_errorStatus);\r\n\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\nextern void PosMon_Mdl_CalculatePosition(const uint8_T *rtu_HallCountsCyclic,\r\n  boolean_T rtu_typeHallDir_P, int8_T rtu_U8One_C, int8_T rtu_Int8MinusOne_C,\r\n  const VoltMon_VoltClass_t *rtu_VoltMon_stVoltClass, int8_T rtu_ZeroU8_C,\r\n  int16_T rtu_hCurPos_IN, boolean_T *rty_isMotorMoving, int16_T *rty_localCurPos);\r\nextern void PosMon_Mdl_DetermineAndWriteState_Init(POSMON_STATEModeType\r\n  *rty_PanelState, NVMMgmt_ReqCmdType *rty_NvmAccessState);\r\nextern void PosMon_Mdl_DetermineAndWriteState(uint16_T rtu_tSmpPosMon_SC,\r\n  boolean_T rtu_PosMon_isCurPosVld, boolean_T rtu_HallSigFlt, boolean_T\r\n  rtu_HallSigSc, boolean_T rtu_DiagDenormReq, const uint8_T *rtu_stCALFileFlt,\r\n  boolean_T rtu_isRelearnIn, boolean_T rtu_isMotorMoving, int16_T\r\n  rtu_localCurPos, const LearnAdap_Req_t *rtu_stPosReq, stdReturn_t\r\n  rtu_PosMonNVMstate, boolean_T *rty_isCurPosVld, boolean_T *rty_isNVMPosVldOut,\r\n  int16_T *rty_stPosSave, boolean_T *rty_isRelearnOut, POSMON_STATEModeType\r\n  *rty_PanelState, int16_T *rty_RP_beforeReset, NVMMgmt_ReqCmdType\r\n  *rty_NvmAccessState);\r\nextern void PosMon_Mdl_PositionOutOfRange(int16_T rtu_localCurPos, const\r\n  CfgParam_hPosBus_t *rtu_hPosTabel, boolean_T rtu_isCurPosVld, const\r\n  LearnAdap_Mode_t *rtu_LearnAdap_stMode, const boolean_T *rtu_isStartupRdy_In,\r\n  uint8_T rtu_hOutOfRangeOffset_C, const boolean_T *rtu_isLearnComplete,\r\n  boolean_T *rty_isOutOfRange, boolean_T *rty_isStartupRdy);\r\nextern void PosMon_Mdl_ShutdownReadyDetermination(POSMON_STATEModeType\r\n  rtu_PanelState, boolean_T *rty_isShdnRdy);\r\nextern void PosMon_Mdl_UpdatePosition(POSMON_STATEModeType rtu_PanelState,\r\n  int16_T rtu_RP_beforeReset, uint16_T rtu_hPosTbl_hHardStopOpn, const\r\n  LearnAdap_Req_t *rtu_stPosReq, int16_T rtu_localCurPosIn, int16_T\r\n  *rty_hCurPosSigned, uint16_T *rty_hCurPosUnsigned);\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n/* Exported data declaration */\r\n\r\n/* Declaration for custom storage class: ExportToFile */\r\nextern boolean_T PIDDID_isIntermediatePositionRtnEna;\r\nextern boolean_T PIDDID_isOpenClosePositionRtnEna;\r\n\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\nextern MdlrefDW_PosMon_Mdl_T PosMon_Mdl_MdlrefDW;\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_\r\n\r\n/* Block signals (default storage) */\r\nextern B_PosMon_Mdl_c_T PosMon_Mdl_B;\r\n\r\n/* Block states (default storage) */\r\nextern DW_PosMon_Mdl_f_T PosMon_Mdl_DW;\r\n\r\n#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/\r\n\r\n/*-\r\n * These blocks were eliminated from the model due to optimizations:\r\n *\r\n * Block '<S2>/Constant6' : Unused code path elimination\r\n * Block '<S2>/Constant7' : Unused code path elimination\r\n */\r\n\r\n/*-\r\n * The generated code includes comments that allow you to trace directly\r\n * back to the appropriate location in the model.  The basic format\r\n * is <system>/block_name, where system is the system number (uniquely\r\n * assigned by Simulink) and block_name is the name of the block.\r\n *\r\n * Use the MATLAB hilite_system command to trace the generated code back\r\n * to the model.  For example,\r\n *\r\n * hilite_system('<S3>')    - opens system 3\r\n * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3\r\n *\r\n * Here is the system hierarchy for this model\r\n *\r\n * '<Root>' : 'PosMon_Mdl'\r\n * '<S1>'   : 'PosMon_Mdl/ModelInfo'\r\n * '<S2>'   : 'PosMon_Mdl/PosMon'\r\n * '<S3>'   : 'PosMon_Mdl/PosMon/CalculatePosition'\r\n * '<S4>'   : 'PosMon_Mdl/PosMon/DetermineAndWriteState'\r\n * '<S5>'   : 'PosMon_Mdl/PosMon/DocBlock'\r\n * '<S6>'   : 'PosMon_Mdl/PosMon/NVM_Access'\r\n * '<S7>'   : 'PosMon_Mdl/PosMon/PositionOutOfRange'\r\n * '<S8>'   : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination'\r\n * '<S9>'   : 'PosMon_Mdl/PosMon/UpdatePosition'\r\n * '<S10>'  : 'PosMon_Mdl/PosMon/determinePositionTable'\r\n * '<S11>'  : 'PosMon_Mdl/PosMon/determineRelativePinionSize'\r\n * '<S12>'  : 'PosMon_Mdl/PosMon/overwritePositionPIDDID'\r\n * '<S13>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant'\r\n * '<S14>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant1'\r\n * '<S15>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant2'\r\n * '<S16>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant3'\r\n * '<S17>'  : 'PosMon_Mdl/PosMon/CalculatePosition/CompareToConstant'\r\n * '<S18>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Difference'\r\n * '<S19>'  : 'PosMon_Mdl/PosMon/CalculatePosition/DocBlock'\r\n * '<S20>'  : 'PosMon_Mdl/PosMon/DetermineAndWriteState/DocBlock'\r\n * '<S21>'  : 'PosMon_Mdl/PosMon/DetermineAndWriteState/PositionState'\r\n * '<S22>'  : 'PosMon_Mdl/PosMon/NVM_Access/Compare To Constant'\r\n * '<S23>'  : 'PosMon_Mdl/PosMon/NVM_Access/CompareToConstant4'\r\n * '<S24>'  : 'PosMon_Mdl/PosMon/NVM_Access/DocBlock1'\r\n * '<S25>'  : 'PosMon_Mdl/PosMon/NVM_Access/Subsystem'\r\n * '<S26>'  : 'PosMon_Mdl/PosMon/PositionOutOfRange/Compare To Constant'\r\n * '<S27>'  : 'PosMon_Mdl/PosMon/PositionOutOfRange/Compare To Constant1'\r\n * '<S28>'  : 'PosMon_Mdl/PosMon/PositionOutOfRange/DocBlock'\r\n * '<S29>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/Compare To Constant'\r\n * '<S30>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/Compare To Constant1'\r\n * '<S31>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/Compare To Constant2'\r\n * '<S32>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/DocBlock'\r\n * '<S33>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/DocBlock1'\r\n * '<S34>'  : 'PosMon_Mdl/PosMon/UpdatePosition/Compare To Constant'\r\n * '<S35>'  : 'PosMon_Mdl/PosMon/UpdatePosition/DocBlock'\r\n * '<S36>'  : 'PosMon_Mdl/PosMon/determinePositionTable/Compare To Zero2'\r\n * '<S37>'  : 'PosMon_Mdl/PosMon/determinePositionTable/DocBlock1'\r\n * '<S38>'  : 'PosMon_Mdl/PosMon/determineRelativePinionSize/DocBlock1'\r\n * '<S39>'  : 'PosMon_Mdl/PosMon/overwritePositionPIDDID/DocBlock1'\r\n */\r\n#endif                                 /* RTW_HEADER_PosMon_Mdl_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"PosMon_Mdl_private.h","type":"header","group":"model","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\PosMon_Mdl","tag":"","groupDisplay":"Model files","code":"/*\r\n * File: PosMon_Mdl_private.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n *\r\n * Target selection: ert.tlc\r\n * Embedded hardware selection: NXP->Cortex-M0/M0+\r\n * Code generation objectives: Unspecified\r\n * Validation result: Not run\r\n */\r\n\r\n#ifndef RTW_HEADER_PosMon_Mdl_private_h_\r\n#define RTW_HEADER_PosMon_Mdl_private_h_\r\n#include \"rtwtypes.h\"\r\n#include \"CfgParam_Mdl_ExpTypes.h\"\r\n#include \"PosMon_Mdl_types.h\"\r\n#include \"PosMon_ExpTypes.h\"\r\n#include \"LearnAdp_ExpTypes.h\"\r\n#include \"NvM_ExpTypes.h\"\r\n#include \"VoltMon_ExpTypes.h\"\r\n#include \"PosMon_Mdl.h\"\r\n#ifndef UCHAR_MAX\r\n#include <limits.h>\r\n#endif\r\n\r\n#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )\r\n#error Code was generated for compiler with different sized uchar/char. \\\r\nConsider adjusting Test hardware word size settings on the \\\r\nHardware Implementation pane to match your compiler word sizes as \\\r\ndefined in limits.h of the compiler. Alternatively, you can \\\r\nselect the Test hardware is the same as production hardware option and \\\r\nselect the Enable portable word sizes option on the Code Generation > \\\r\nVerification pane for ERT based targets, which will disable the \\\r\npreprocessor word size checks.\r\n#endif\r\n\r\n#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )\r\n#error Code was generated for compiler with different sized ushort/short. \\\r\nConsider adjusting Test hardware word size settings on the \\\r\nHardware Implementation pane to match your compiler word sizes as \\\r\ndefined in limits.h of the compiler. Alternatively, you can \\\r\nselect the Test hardware is the same as production hardware option and \\\r\nselect the Enable portable word sizes option on the Code Generation > \\\r\nVerification pane for ERT based targets, which will disable the \\\r\npreprocessor word size checks.\r\n#endif\r\n\r\n#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )\r\n#error Code was generated for compiler with different sized uint/int. \\\r\nConsider adjusting Test hardware word size settings on the \\\r\nHardware Implementation pane to match your compiler word sizes as \\\r\ndefined in limits.h of the compiler. Alternatively, you can \\\r\nselect the Test hardware is the same as production hardware option and \\\r\nselect the Enable portable word sizes option on the Code Generation > \\\r\nVerification pane for ERT based targets, which will disable the \\\r\npreprocessor word size checks.\r\n#endif\r\n\r\n#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )\r\n#error Code was generated for compiler with different sized ulong/long. \\\r\nConsider adjusting Test hardware word size settings on the \\\r\nHardware Implementation pane to match your compiler word sizes as \\\r\ndefined in limits.h of the compiler. Alternatively, you can \\\r\nselect the Test hardware is the same as production hardware option and \\\r\nselect the Enable portable word sizes option on the Code Generation > \\\r\nVerification pane for ERT based targets, which will disable the \\\r\npreprocessor word size checks.\r\n#endif\r\n\r\n/* Macros for accessing real-time model data structure */\r\n#ifndef rtmGetErrorStatus\r\n#define rtmGetErrorStatus(rtm)         (*((rtm)->errorStatus))\r\n#endif\r\n\r\n#ifndef rtmSetErrorStatus\r\n#define rtmSetErrorStatus(rtm, val)    (*((rtm)->errorStatus) = (val))\r\n#endif\r\n\r\n#ifndef rtmGetErrorStatusPointer\r\n#define rtmGetErrorStatusPointer(rtm)  (rtm)->errorStatus\r\n#endif\r\n\r\n#ifndef rtmSetErrorStatusPointer\r\n#define rtmSetErrorStatusPointer(rtm, val) ((rtm)->errorStatus = (val))\r\n#endif\r\n\r\n/* Invariant block signals (default storage) */\r\nextern const ConstB_PosMon_Mdl_h_T PosMon_Mdl_ConstB;\r\n\r\n#include \"peripherals_flash_1.h\"\r\n#include \"nvmman.h\"\r\n#include \"nvmman_conf.h\"\r\n#endif                                 /* RTW_HEADER_PosMon_Mdl_private_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"PosMon_Mdl_types.h","type":"header","group":"model","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\PosMon_Mdl","tag":"","groupDisplay":"Model files","code":"/*\r\n * File: PosMon_Mdl_types.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n *\r\n * Target selection: ert.tlc\r\n * Embedded hardware selection: NXP->Cortex-M0/M0+\r\n * Code generation objectives: Unspecified\r\n * Validation result: Not run\r\n */\r\n\r\n#ifndef RTW_HEADER_PosMon_Mdl_types_h_\r\n#define RTW_HEADER_PosMon_Mdl_types_h_\r\n#include \"VoltMon_ExpTypes.h\"\r\n#include \"NvM_ExpTypes.h\"\r\n#include \"LearnAdp_ExpTypes.h\"\r\n#include \"rtwtypes.h\"\r\n#include \"ThermProt_ExpTypes.h\"\r\n#include \"RoofSys_CommDefines.h\"\r\n#include \"RoofOper_ExpTypes.h\"\r\n#include \"RoofSys_ExpTypes.h\"\r\n#include \"CtrlLogic_ExpTypes.h\"\r\n#include \"CmdLogic_ExpTypes.h\"\r\n#include \"StateMach_ExpTypes.h\"\r\n#include \"BlockDet_ExpTypes.h\"\r\n#include \"APDet_ExpTypes.h\"\r\n#include \"RefForceBus.h\"\r\n#include \"MtrMdl_ExpTypes.h\"\r\n#include \"CfgParam_Mdl_ExpTypes.h\"\r\n#include \"HallDeco_ExpTypes.h\"\r\n#include \"PosMon_ExpTypes.h\"\r\n#include \"CfgParam_TThermThreshBus_t.h\"\r\n#include \"CfgParam_TThermThreshBus_MosfetLevels.h\"\r\n#ifndef DEFINED_TYPEDEF_FOR_POSMON_STATEModeType_\r\n#define DEFINED_TYPEDEF_FOR_POSMON_STATEModeType_\r\n\r\ntypedef enum {\r\n  POSMON_STATEModeType_None = 0,       /* Default value */\r\n  POSMON_STATEModeType_VALID_IDLE,\r\n  POSMON_STATEModeType_WRITING_INVALID,\r\n  POSMON_STATEModeType_VALID_MOVING,\r\n  POSMON_STATEModeType_VALID_AFTER_MOVING,\r\n  POSMON_STATEModeType_WRITE_NEWPOS,\r\n  POSMON_STATEModeType_RESTORE_RP,\r\n  POSMON_STATEModeType_INVALID_OPERATION,\r\n  POSMON_STATEModeType_RESET,\r\n  POSMON_STATEModeType_GOING_INVALID\r\n} POSMON_STATEModeType;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_ThermProtBus_\r\n#define DEFINED_TYPEDEF_FOR_ThermProtBus_\r\n\r\n/* Bus object for Thermal Protection signals */\r\ntypedef struct {\r\n  /* Motor Temperature Class Status(Rollo Motor) */\r\n  ThermProt_MtrTempClass_t ThermProt_stMtrTempClass;\r\n\r\n  /* Motor Temperature Standup ready(Rollo Motor) */\r\n  boolean_T ThermProt_isStartupRdy;\r\n\r\n  /* Mosfet Temperature Class Status(Rollo Motor) */\r\n  ThermProt_MosfetTempClass_t ThermProt_stMosfetTempClass;\r\n  boolean_T ThermProt_isSleepAllowed;\r\n} ThermProtBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_MtrCtrlBus_\r\n#define DEFINED_TYPEDEF_FOR_MtrCtrlBus_\r\n\r\n/* Bus object for Motor Control signals */\r\ntypedef struct {\r\n  /* Motor Direction Control signal (M1/M2)\r\n     RoofSys_MtrDir_t.IDLE_C: Idle\r\n     RoofSys_MtrDir_t.CW_C: CW\r\n     RoofSys_MtrDir_t.CCW_C: CCW\r\n     RoofSys_MtrDir_t.SECURE_C: Secure */\r\n  MtrCtrl_MtrDir_t IoHwAb_SetMtrDir;\r\n\r\n  /* Motor Direction DTC Status (Glass and Rollo)\r\n     0: Not Run\r\n     1: Run and Pass\r\n     2: Run and Fail */\r\n  uint8_T DTC_MTR_Dir;\r\n\r\n  /* Motor Duty Cycle Control signal (M1/M2)\r\n     Applied percentage value (0-100% = 0 - 100) */\r\n  int16_T IoHwAb_SetMtrDutyCycle;\r\n  uint16_T MtrCtrl_rMtrSpdTarget;\r\n  int16_T MtrCtrl_rClsLpFactor;\r\n  int16_T MtrCtrl_rOpnLpFactor;\r\n\r\n  /* MotorSpeed Error */\r\n  int32_T MtrCtrl_rMtrSpdError;\r\n\r\n  /* Motor debounced powered state */\r\n  MtrCtrl_MtrPwrd_t MtrCtrl_MtrPwrdState;\r\n  boolean_T MtrCtrl_FeedbackStatus[2];\r\n} MtrCtrlBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_PnlOpBus_\r\n#define DEFINED_TYPEDEF_FOR_PnlOpBus_\r\n\r\n/* Bus object for Roof Operation signals */\r\ntypedef struct {\r\n  /* Motor Control Command from Sunroof Operation (Glass Motor Command and Rollo Motor Command)\r\n     RoofOper_stMtrNoCmd_C      ----> 0: No Motor Command\r\n     RoofOper_stMtrIncCmd_C      ---->1: Increasing\r\n     RoofOper_stMtDecCmd_C      ----> 2: Decreasing */\r\n  PnlOp_MtrCmd_t PnlOp_stMtrCtrlCmd;\r\n\r\n  /* Sunroof Moving Status including reversal moving (Glass and Rollo)\r\n     0: Motor Not Moving\r\n     1: Motor Moving */\r\n  boolean_T PnlOp_isMtrMoving;\r\n\r\n  /* Sunroof Normal Moving Status (Glass and Rollo) */\r\n  PnlOp_Mode_t PnlOp_stNormalMov;\r\n\r\n  /* Sunroof Anti Pinch Reversal Status (glass and rollo) */\r\n  PnlOp_Rev_t PnlOp_stReversal;\r\n\r\n  /* ATS states from Sunroof Operation (Glass ATS state and Rollo ATS state)\r\n     '0: Deactivated'\r\n     '1: Available'\r\n     '2: Active'\r\n     '3: Override Stage 1'\r\n     '4: Override Stage 2'\r\n     '5: Disabled' */\r\n  PnlOp_ATS_t PnlOp_stATS;\r\n\r\n  /* Motor speed control command (Glass and Rollo) */\r\n  int16_T PnlOp_pMtrSpdCmd;\r\n\r\n  /* Motor target position */\r\n  uint16_T PnlOp_hTrgtPosPtcd;\r\n} PnlOpBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_SysFltDiag_LastStopReason_t_\r\n#define DEFINED_TYPEDEF_FOR_SysFltDiag_LastStopReason_t_\r\n\r\ntypedef uint8_T SysFltDiag_LastStopReason_t;\r\n\r\n/* enum SysFltDiag_LastStopReason_t */\r\n#define NoFault_C                      ((SysFltDiag_LastStopReason_t)0U) /* Default value */\r\n#define Debugger_C                     ((SysFltDiag_LastStopReason_t)1U)\r\n#define DiagReq_C                      ((SysFltDiag_LastStopReason_t)2U)\r\n#define PanicStp_C                     ((SysFltDiag_LastStopReason_t)3U)\r\n#define ThermPrtMtr_C                  ((SysFltDiag_LastStopReason_t)4U)\r\n#define ThermPrtMosfet_C               ((SysFltDiag_LastStopReason_t)5U)\r\n#define RelaxMechComplt_C              ((SysFltDiag_LastStopReason_t)6U)\r\n#define AtsRevComplt_C                 ((SysFltDiag_LastStopReason_t)7U)\r\n#define MovTimeout_C                   ((SysFltDiag_LastStopReason_t)8U)\r\n#define ClassEOV_C                     ((SysFltDiag_LastStopReason_t)16U)\r\n#define ClassEUV_C                     ((SysFltDiag_LastStopReason_t)17U)\r\n#define BattVltNonPlsbl_C              ((SysFltDiag_LastStopReason_t)18U)\r\n#define AmbTmpNonPlsbl_C               ((SysFltDiag_LastStopReason_t)19U)\r\n#define MosfetTempNonPlsbl_C           ((SysFltDiag_LastStopReason_t)20U)\r\n#define SysIC_CommFlt_C                ((SysFltDiag_LastStopReason_t)21U)\r\n#define LINCommFlt_C                   ((SysFltDiag_LastStopReason_t)22U)\r\n#define SysICFailSafe_C                ((SysFltDiag_LastStopReason_t)23U)\r\n#define ChargePumpFlt_C                ((SysFltDiag_LastStopReason_t)24U)\r\n#define HSSuppFlt_C                    ((SysFltDiag_LastStopReason_t)25U)\r\n#define VsIntFlt_C                     ((SysFltDiag_LastStopReason_t)26U)\r\n#define VsSuppFlt_C                    ((SysFltDiag_LastStopReason_t)27U)\r\n#define VCC1Flt_C                      ((SysFltDiag_LastStopReason_t)28U)\r\n#define RPInvalidFlt_C                 ((SysFltDiag_LastStopReason_t)29U)\r\n#define HallFlt_C                      ((SysFltDiag_LastStopReason_t)30U)\r\n#define SysICThermShdFlt_C             ((SysFltDiag_LastStopReason_t)31U)\r\n#define MtrCtrlFlt_C                   ((SysFltDiag_LastStopReason_t)35U)\r\n#define HallSuppFlt_C                  ((SysFltDiag_LastStopReason_t)37U)\r\n#define UnderVotlage_C                 ((SysFltDiag_LastStopReason_t)48U)\r\n#define OverVoltage_C                  ((SysFltDiag_LastStopReason_t)49U)\r\n#define StallDurAtsRev_RlxMech_C       ((SysFltDiag_LastStopReason_t)50U)\r\n#define OutsideEnvCond_C               ((SysFltDiag_LastStopReason_t)51U)\r\n#define TargetPosRchd_C                ((SysFltDiag_LastStopReason_t)52U)\r\n#define PrevReason_C                   (MAX_uint8_T)\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_CtrlLogBus_\r\n#define DEFINED_TYPEDEF_FOR_CtrlLogBus_\r\n\r\n/* Bus object for Control Logic signals */\r\ntypedef struct {\r\n  /* Reaction Command (Glass and Rollo) */\r\n  CtrlLog_MovType_t CtrlLog_stMovType;\r\n  int16_T CtrlLog_hReactTgtPos;\r\n\r\n  /* Control Logic Relearn Mode */\r\n  CtrlLog_RelearnMode_t CtrlLog_stRelearnMode;\r\n\r\n  /* CtrlLogic learning interruption reasons\r\n     Bit0(1/0): Learning request becomes inactive or not\r\n     Bit1(1/0): Block detected or not\r\n     Bit2(1/0): Maximum learning time is passed or not\r\n     Bit3(1/0): Position lost or not\r\n     Bit4(1/0): Is Emergency Mode Active or not\r\n     Bit5(1/0): Is Auth Disabled & Not in Factory Mode & Learn by Switch or not\r\n     Bit6(1/0): Is Power State not ON & Not in Factory Mode & Learn by Switch or not\r\n     Bit7(1/0): Is Fault Active or not */\r\n  SysFltDiag_LastStopReason_t CtrlLog_stLastStopReason;\r\n\r\n  /* MovIdle Status\r\n     When false: CtrlLogic is in the progress of executing single- or multi-step movements.\r\n     When true: CtrlLogic has completed all movement steps/initial value */\r\n  boolean_T CtrlLog_isMovIdle;\r\n\r\n  /* PnlMovIdle Status [glass,rollo]\r\n     When false: CtrlLogic is in the process of executing a panel movement.\r\n     When true: CtrlLogic is not executing a panel movement. */\r\n  boolean_T CtrlLog_isPnlMovIdle;\r\n  CtrlLog_tenTargetDirection CtrlLog_stDirCommand;\r\n} CtrlLogBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_ComLogBus_\r\n#define DEFINED_TYPEDEF_FOR_ComLogBus_\r\n\r\n/* Bus object for Command Logic signals */\r\ntypedef struct {\r\n  /* Active movement command (Glass and Rollo) */\r\n  VehCom_Cmd_t ComLog_stActvMovCmd;\r\n} ComLogBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_SWC_OperLogLyrBus_\r\n#define DEFINED_TYPEDEF_FOR_SWC_OperLogLyrBus_\r\n\r\ntypedef struct {\r\n  ThermProtBus ThermProtBus;\r\n  MtrCtrlBus MtrCtrlBus;\r\n  PnlOpBus PnlOpBus;\r\n  CtrlLogBus CtrlLogBus;\r\n  ComLogBus ComLogBus;\r\n} SWC_OperLogLyrBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_StateMachBus_\r\n#define DEFINED_TYPEDEF_FOR_StateMachBus_\r\n\r\n/* Bus object for State Machine signals\r\n   already Present. */\r\ntypedef struct {\r\n  /* Sunroof System Mode */\r\n  StateMach_Mode_t StateMach_stSysMode;\r\n} StateMachBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_SWC_StateMachLyrBus_\r\n#define DEFINED_TYPEDEF_FOR_SWC_StateMachLyrBus_\r\n\r\ntypedef struct {\r\n  StateMachBus StateMachBus;\r\n} SWC_StateMachLyrBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_LearnAdapBus_\r\n#define DEFINED_TYPEDEF_FOR_LearnAdapBus_\r\n\r\n/* Bus object for Learning and Adaption signals\r\n */\r\ntypedef struct {\r\n  /* Sunroof Learning and Adaption Status */\r\n  LearnAdap_Mode_t LearnAdap_stMode;\r\n\r\n  /* Learning Adaption Reference Field Request (Glass) */\r\n  LearnAdap_Req_t LearnAdap_stRefFieldReq;\r\n\r\n  /* Learning Adaption Postion Request (Glass) */\r\n  LearnAdap_Req_t LearnAdap_stPosReq;\r\n\r\n  /* LearnAdp learning/adaption interruption reasons\r\n     Bit0(1/0): Voltage is out of range or not\r\n     Bit1(1/0): Ambient temperature is out of range or not\r\n     Bit2(1/0): Vehicle speed is out of range or not\r\n     Bit3(1/0): Motor coil temperature is out of range or not\r\n     Bit4(1/0): Voltage fluctuation is detected or not\r\n     Bit5(1/0): Voltage delta is out of range or not\r\n     Bit6(1/0): Motor speed is low or not\r\n     Bit7(1/0): NVM error is detected or not */\r\n  uint8_T LearnAdap_stInt;\r\n  uint16_T LearnAdap_newHardStop;\r\n  uint16_T LearnAdap_newSoftStop;\r\n  boolean_T LearnAdap_isLearnComplete;\r\n  boolean_T LearnAdap_isLearningAllowed;\r\n  boolean_T LearnAdap_isInterrupted;\r\n} LearnAdapBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_BlockDetBus_\r\n#define DEFINED_TYPEDEF_FOR_BlockDetBus_\r\n\r\n/* Bus object for Block Detection signals */\r\ntypedef struct {\r\n  /* Motor Stall Type (Glass Motor and Rollo Motor) */\r\n  BlockDet_Stall_t BlockDet_stStallType;\r\n\r\n  /* Motor Stall Fault Status (Glass Motor and Rollo Motor)\r\n     0: Not Run\r\n     1: Run and Pass\r\n     2: Run and Fail */\r\n  uint8_T BlockDet_stStallFlt;\r\n\r\n  /* Motor Stall Fault Type (Glass Motor and Rollo Motor) */\r\n  BlockDet_FltType_t BlockDet_stFltType;\r\n\r\n  /* Motor Stall Position */\r\n  uint16_T BlockDet_hStallPos;\r\n\r\n  /* Motr Stall direction */\r\n  BlockDet_Stall_dir BlockDet_stStallDir;\r\n} BlockDetBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_APDetBus_\r\n#define DEFINED_TYPEDEF_FOR_APDetBus_\r\n\r\n/* Bus object for Anti Pinch Bus */\r\ntypedef struct {\r\n  /* Boolean to indicate if a pinch is detected at the moment */\r\n  boolean_T APDet_isAtsDetected;\r\n\r\n  /* Force difference (Rollo) */\r\n  int16_T APDet_FDifference;\r\n\r\n  /* This boolean indicates if the output of this component can be trusted.\r\n     It combines all the validity flags of the components in the model layer\r\n     into one. */\r\n  boolean_T APDet_isInputsAtsValid;\r\n\r\n  /* Tracking force (Rollo) */\r\n  int16_T APDet_FTracking;\r\n\r\n  /* ATS last reserval reason data (Rollo)\r\n     18 bytes data for each panel */\r\n  uint8_T UsgHist_ATSRevReason[18];\r\n  uint16_T APDet_ATSRevThreshold;\r\n  uint16_T APDet_ATSRevPosition;\r\n  APDet_tenTargetDirection APDet_ATSRevDir;\r\n  boolean_T APDet_isAtsEnabled;\r\n} APDetBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_ThresForceBus_\r\n#define DEFINED_TYPEDEF_FOR_ThresForceBus_\r\n\r\ntypedef struct {\r\n  /* ATS threshold force */\r\n  uint16_T ThresForce_FatsThreshold;\r\n} ThresForceBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_MtrMdlBus_\r\n#define DEFINED_TYPEDEF_FOR_MtrMdlBus_\r\n\r\n/* Bus object for Motor Model  signals */\r\ntypedef struct {\r\n  MtrMdl_CalcForTorqBus_t MtrMdl_stCalcForTorq;\r\n\r\n  /* Estimated Motor Temperature (Rollo) */\r\n  int16_T MtrMdl_TEstMtrTemp;\r\n\r\n  /* Estimated Case Temperature (Rollo) */\r\n  int16_T MtrMdl_TEstCaseTemp;\r\n  boolean_T MtrMdl_isMotorInStartUp;\r\n  boolean_T MtrMdl_isStartupRdy;\r\n\r\n  /* Calculated Force and Torque Validity (Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T MtrMdl_isCalcForTorqVld;\r\n\r\n  /* Estimated Motor Temperature Validity (Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T MtrMdl_isEstMtrTempVld;\r\n  int16_T MtrMdl_MbrakeTorque;\r\n} MtrMdlBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_SWC_ObjDetLyrBus_\r\n#define DEFINED_TYPEDEF_FOR_SWC_ObjDetLyrBus_\r\n\r\ntypedef struct {\r\n  LearnAdapBus LearnAdapBus;\r\n  BlockDetBus BlockDetBus;\r\n  APDetBus APDetBus;\r\n  ThresForceBus ThresForceBus;\r\n  RefForceBus RefForceBus;\r\n  MtrMdlBus MtrMdlBus;\r\n} SWC_ObjDetLyrBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_UsgHistBus_\r\n#define DEFINED_TYPEDEF_FOR_UsgHistBus_\r\n\r\n/* BUs object for Usage History */\r\ntypedef struct {\r\n  /* Cycle counts Rollo Slide\\Vent) */\r\n  uint16_T UsgHist_nCycle;\r\n\r\n  /* Usage History Shut Down Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  boolean_T UsgHist_isShdnRdy;\r\n\r\n  /* UsgHist Stored Unlearn Reason (Glass and Rollo)\r\n     Bit0(1/0): Unlearn due to panel position sensing ability lost (hall failure)\r\n     Bit1(1/0): Unlearn due to repeated blocks in the same position at a panel\r\n     Bit2(1/0): Unlearn due to reference field learning procedure started\r\n     Bit3(1/0): Unlearn due to unlearned by diagnostic command\r\n     Bit4(1/0): Unlearn due to CAL-file is invalid\r\n     Bit5(1/0): Unlearn due to Position is invalid\r\n     Bit6(1/0): Unlearn due to Reference Field is invalid\r\n     Bit7(1/0): Unlearn due to ECU reset via diagnostics during movement\r\n     Bit8(1/0): Unlearn due to double stall\r\n     Bit9(1/0): Unlearn due to position out of range\r\n     Bit10-15(1/0): Reserved */\r\n  uint16_T UsgHist_unlearnReason;\r\n\r\n  /* Usage History MotorTemp */\r\n  int16_T UsgHist_TEstMtrTemp;\r\n\r\n  /* Usage History caseTemp  */\r\n  int16_T UsgHist_TEstCaseTemp;\r\n\r\n  /* Usage History MotorTemp Validity */\r\n  boolean_T UsgHist_TEstMtrTempVld;\r\n\r\n  /* Usage History caseTemp Validity */\r\n  boolean_T UsgHistl_TEstCaseTempVld;\r\n\r\n  /* Usage History AmbTemp Validity */\r\n  boolean_T UsgHist_AmbTempVld;\r\n\r\n  /* Usage History MosfetTemp Validity */\r\n  boolean_T UsgHist_MOSFETTempVld;\r\n  int16_T UsgHist_MaxAmbTemp;\r\n  int16_T UsgHist_MinAmbTemp;\r\n  uint16_T UsgHist_MaxBattVtg;\r\n  uint16_T UsgHist_MinBattVtg;\r\n  uint32_T UsgHist_MotorOperTime;\r\n  uint16_T UsgHist_CycRenormCount;\r\n  uint8_T UsgHist_LearnSuccessCount;\r\n  uint8_T UsgHist_LeanrFailCount;\r\n  uint8_T UsgHist_StallCount;\r\n  uint16_T UsgHist_ReversalCount;\r\n  uint16_T UsgHist_SlideCycCount;\r\n  uint8_T UsgHist_ThermProtCount;\r\n  uint8_T UsgHist_ReprogrammingCount;\r\n\r\n  /* ATS Reversal position */\r\n  uint16_T UsgHist_ReversalPos;\r\n\r\n  /* ATS Reversal direction */\r\n  CtrlLog_tenTargetDirection UsgHist_ReversalDir;\r\n\r\n  /* ATS threshold force */\r\n  uint16_T UsgHist_ReversalThres;\r\n\r\n  /* Usage History Startup Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  boolean_T UsgHist_isStartupRdy;\r\n} UsgHistBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_stdReturn_t_\r\n#define DEFINED_TYPEDEF_FOR_stdReturn_t_\r\n\r\ntypedef enum {\r\n  IRS_E_OK = 0,                        /* Default value */\r\n  IRS_E_NOK,\r\n  IRS_E_PENDING\r\n} stdReturn_t;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_RefFieldBus_\r\n#define DEFINED_TYPEDEF_FOR_RefFieldBus_\r\n\r\n/* Bus object for RF detection signals */\r\ntypedef struct {\r\n  /* Set Startup Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  boolean_T RefField_isStartupRdy;\r\n\r\n  /* Config Shut Down Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  boolean_T RefField_isShdnRdy;\r\n\r\n  /* Reference force data saving status (Glass and Rollo)\r\n     stdReturn_t:\r\n\r\n     '0: OK'\r\n     '1: Not OK'\r\n     '2: Pending' */\r\n  stdReturn_t RefField_stRFSave;\r\n\r\n  /* Calibrated Position Table Validity NVM Status(Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T RefField_isRFTblVldNvm;\r\n\r\n  /* Calibrated Position Table Validity Status(Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T RefField_isRFTblVld;\r\n\r\n  /* Calibrated Position Table Validity NVM Status(Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T RefField_isRFTblVldNvmBak;\r\n\r\n  /* Calibrated Position Table Validity NVM Status(Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T RefField_isRFTblVldNvmSync;\r\n\r\n  /* Calibrated Position Table Validity NVM Status(Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T RefField_isInsideRF;\r\n  uint16_T RefField_currByteOfRF;\r\n  uint8_T RefField_currRFArea;\r\n} RefFieldBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_CfgParamBus_\r\n#define DEFINED_TYPEDEF_FOR_CfgParamBus_\r\n\r\n/* Bus object for COnfig signals */\r\ntypedef struct {\r\n  /* CAL File Fault Status\r\n     0: Not Run\r\n     1: Run and Pass\r\n     2: Run and Fail */\r\n  uint8_T CfgParam_stCALFileFlt;\r\n\r\n  /* CAL File Validity Status\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T CfgParam_isCALFileVld;\r\n\r\n  /* Config Startup Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  boolean_T CfgParam_isStartupRdy;\r\n\r\n  /* Reference Force Table Validity Status(Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T CfgParam_isPosTblVld;\r\n\r\n  /* PV test mode */\r\n  CfgParam_TestMode_t CfgParam_stTestMode;\r\n} CfgParamBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_SWC_DataHndlLyrBus_\r\n#define DEFINED_TYPEDEF_FOR_SWC_DataHndlLyrBus_\r\n\r\n/* BUs object for Usage History */\r\ntypedef struct {\r\n  /* Cycle counts(Glass Slide\\Vent and Rollo Slide\\Vent) */\r\n  UsgHistBus UsgHistBus;\r\n\r\n  /* Usage History Shut Down Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  RefFieldBus RefFieldBus;\r\n\r\n  /* UsgHist Stored Unlearn Reason (Glass and Rollo)\r\n     Bit0(1/0): Unlearn due to panel position sensing ability lost (hall failure)\r\n     Bit1(1/0): Unlearn due to repeated blocks in the same position at a panel\r\n     Bit2(1/0): Unlearn due to reference field learning procedure started\r\n     Bit3(1/0): Unlearn due to unlearned by diagnostic command\r\n     Bit4(1/0): Unlearn due to CAL-file is invalid\r\n     Bit5(1/0): Unlearn due to Position is invalid\r\n     Bit6(1/0): Unlearn due to Reference Field is invalid\r\n     Bit7(1/0): Unlearn due to ECU reset via diagnostics during movement\r\n     Bit8(1/0): Unlearn due to double stall\r\n     Bit9(1/0): Unlearn due to position out of range\r\n     Bit10-15(1/0): Reserved */\r\n  CfgParamBus CfgParamBus;\r\n} SWC_DataHndlLyrBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_VoltMonBus_\r\n#define DEFINED_TYPEDEF_FOR_VoltMonBus_\r\n\r\n/* Bus object for Voltage Monitoring Signals */\r\ntypedef struct {\r\n  /* 12v Battery voltage */\r\n  uint16_T VoltMon_u12VBatt;\r\n\r\n  /* 12v Battery voltage Validity Status\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T VoltMon_is12VBattVld;\r\n\r\n  /* Battery voltage class status */\r\n  VoltMon_VoltClass_t VoltMon_stVoltClass;\r\n\r\n  /* Battery voltage drop status\r\n     0: No voltage drop detected\r\n     1: Voltage drop detected */\r\n  boolean_T VoltMon_isVoltDrop;\r\n\r\n  /* Battery voltage fluctuation status\r\n     0: No voltage fluctuation detected\r\n     1: Voltage fluctuation detected */\r\n  boolean_T VoltMon_isFlctnDet;\r\n\r\n  /* Config Startup Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  boolean_T VoltMon_isStartUpRdy;\r\n\r\n  /* VBatt Voltage DTC Status\r\n     0: Not Run\r\n     1: Run and Pass\r\n     2: Run and Fail */\r\n  uint8_T DTC_VOLT_VBatt;\r\n\r\n  /* VPullup Voltage DTC Status\r\n     0: Not Run\r\n     1: Run and Pass\r\n     2: Run and Fail */\r\n  uint8_T DTC_VOLT_Pullup;\r\n\r\n  /* Battery voltage lost detection */\r\n  boolean_T VoltMon_isPwrLost;\r\n\r\n  /* VoltMon_isUnderVtgDTCFlgSet: if 1 = Set DTC ; 0 = clear DTC */\r\n  boolean_T VoltMon_isUnderVtgFlgSet;\r\n\r\n  /* VoltMon_isOverVtgDTCFlgSet: if 1 = Set DTC ; 0 = clear DTC */\r\n  boolean_T VoltMon_isOverVtgFlgSet;\r\n} VoltMonBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_PWMCtrlBus_\r\n#define DEFINED_TYPEDEF_FOR_PWMCtrlBus_\r\n\r\n/* Bus object for Pwm Control Input signals */\r\ntypedef struct {\r\n  /* Motor Direction Control signal (M1/M2)\r\n     RoofSys_MtrDir_t.IDLE_C: Idle\r\n     RoofSys_MtrDir_t.CW_C: CW\r\n     RoofSys_MtrDir_t.CCW_C: CCW\r\n     RoofSys_MtrDir_t.SECURE_C: Secure */\r\n  PWMCtrl_MtrDir_t PWMCtrl_SetMtrDir;\r\n\r\n  /* Motor Duty Cycle Control signal (M1/M2)\r\n     Applied percentage value (0-100% = 0 - 100) */\r\n  int16_T PWMCtrl_SetMtrDutyCycle;\r\n} PWMCtrlBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_DIOCtrlBus_\r\n#define DEFINED_TYPEDEF_FOR_DIOCtrlBus_\r\n\r\ntypedef struct {\r\n  boolean_T DIOCtrl_PINReadStatus;\r\n  boolean_T DIOCtrl_isVBATMeasEnbPinStatus;\r\n  boolean_T DIOCtrl_isHallOutPinStatus;\r\n  boolean_T DIOCtrl_isMtrFltrBlankAPinStatus;\r\n  boolean_T DIOCtrl_isMtrFltrBlankBPinStatus;\r\n  boolean_T DIOCtrl_isHallSens1PinStatus;\r\n  boolean_T DIOCtrl_isHallSens2PinStatus;\r\n  boolean_T DIOCtrl_isINTPinStatus;\r\n} DIOCtrlBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_ADCMonBus_\r\n#define DEFINED_TYPEDEF_FOR_ADCMonBus_\r\n\r\ntypedef struct {\r\n  uint16_T ADCMon_u12VBattInst;\r\n  uint16_T ADCMon_uSwchInst[2];\r\n  int16_T ADCMon_TAmbTemp;\r\n  int16_T ADCMon_TMOSFETTemp;\r\n  uint16_T ADCMon_uHallPwrInst;\r\n  uint16_T ADCMon_uM1AVolt_mv;\r\n  uint16_T ADCMon_uM1BVolt_mv;\r\n} ADCMonBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_DiagComManBus_\r\n#define DEFINED_TYPEDEF_FOR_DiagComManBus_\r\n\r\ntypedef struct {\r\n  boolean_T Dummy;\r\n} DiagComManBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_ExtDevBus_\r\n#define DEFINED_TYPEDEF_FOR_ExtDevBus_\r\n\r\ntypedef struct {\r\n  boolean_T ExtDev_isMtrFebkCtrlHS1Status;\r\n  boolean_T ExtDev_isHallSuppHS2Status;\r\n  boolean_T ExtDev_isSwtchSuppHS3Status;\r\n  uint8_T ExtDev_u8GetPWMDutyCycleVal;\r\n} ExtDevBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_HallDecoBus_\r\n#define DEFINED_TYPEDEF_FOR_HallDecoBus_\r\n\r\n/* Bus objects for HallDeco signals */\r\ntypedef struct {\r\n  /* Motor Direction (Glass and Rollo) - CW/CCW */\r\n  HallDeco_Dir_t HallDeco_DMtrDir;\r\n  uint8_T HallDeco_uiHallCountsCyclic;\r\n\r\n  /* Motor Speed (Glass and Rollo) */\r\n  uint16_T HallDeco_rMtrSpd;\r\n\r\n  /* Motor Speed Validity (Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T HallDeco_isMtrSpdVld;\r\n  uint8_T DTC_HALL_Hall1;\r\n  uint8_T DTC_HallMtrOptDir;\r\n  boolean_T isHallSupplyFaultDebd;\r\n  boolean_T isHallSupplyFault;\r\n} HallDecoBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_SWC_HwAbsLyrBus_\r\n#define DEFINED_TYPEDEF_FOR_SWC_HwAbsLyrBus_\r\n\r\ntypedef struct {\r\n  PWMCtrlBus PWMCtrlBus;\r\n  DIOCtrlBus DIOCtrlBus;\r\n  ADCMonBus ADCMonBus;\r\n  DiagComManBus DiagComManBus;\r\n  ExtDevBus ExtDevBus;\r\n  HallDecoBus HallDecoBus;\r\n} SWC_HwAbsLyrBus;\r\n\r\n#endif\r\n\r\n#ifndef DEFINED_TYPEDEF_FOR_PosMonBus_\r\n#define DEFINED_TYPEDEF_FOR_PosMonBus_\r\n\r\n/* Bus object for Position Monitoring signals */\r\ntypedef struct {\r\n  boolean_T PosMon_isCurPosVld;\r\n  boolean_T PosMon_isCurPosVldNvm;\r\n  int16_T PosMon_hCurPosSigned;\r\n  boolean_T PosMon_isRelearn;\r\n\r\n  /* Position saving status (Glass and Rollo)\r\n     stdReturn_t:\r\n\r\n     '0: OK'\r\n     '1: Not OK'\r\n     '2: Pending' */\r\n  int16_T PosMon_stPosSave;\r\n\r\n  /* Current Position (Glass and Rollo) */\r\n  uint16_T PosMon_hCurPos;\r\n\r\n  /* Current position area (Glass and Rollo) */\r\n  PosMon_Area_t PosMon_curPanelArea;\r\n\r\n  /* Current position area (Glass and Rollo) */\r\n  PosMon_PnlPosArea_t PosMon_panelCurPosArea;\r\n\r\n  /* 0: Not ready\r\n     1: Ready */\r\n  boolean_T PosMon_isShdnRdy;\r\n\r\n  /* Position out of range (Glass and Rollo)\r\n     0: Invalid\r\n     1: Valid */\r\n  boolean_T PosMon_isOutOfRange;\r\n\r\n  /* Position Monitoring Startup Ready Status\r\n     0: Not ready\r\n     1: Ready */\r\n  boolean_T PosMon_isStartupRdy;\r\n\r\n  /* Diameter of the helix pulley in relation to the full close diameter */\r\n  uint16_T PosMon_relativePinionSize;\r\n\r\n  /* Position table which includes the correct open positions */\r\n  CfgParam_hPosBus_t PosMon_hPosTbl;\r\n} PosMonBus;\r\n\r\n#endif\r\n\r\n/* Forward declaration for rtModel */\r\ntypedef struct tag_RTM_PosMon_Mdl_T RT_MODEL_PosMon_Mdl_T;\r\n\r\n#endif                                 /* RTW_HEADER_PosMon_Mdl_types_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"PosMon_Mdl_data.c","type":"source","group":"data","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\PosMon_Mdl","tag":"","groupDisplay":"Data files","code":"/*\r\n * File: PosMon_Mdl_data.c\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n *\r\n * Target selection: ert.tlc\r\n * Embedded hardware selection: NXP->Cortex-M0/M0+\r\n * Code generation objectives: Unspecified\r\n * Validation result: Not run\r\n */\r\n\r\n#include \"PosMon_Mdl_private.h\"\r\n\r\n/* Invariant block signals (default storage) */\r\nconst ConstB_PosMon_Mdl_h_T PosMon_Mdl_ConstB = {\r\n  PosMon_PnlPosArea_t_PnlZero,\r\n  AreaSlide_C\r\n};\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"APDet_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: APDet_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_APDet_ExpTypes_h_\r\n#define RTW_HEADER_APDet_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\ntypedef enum {\r\n  APDet_tenTargetDirection_None_C = 0, /* Default value */\r\n  APDet_tenTargetDirection_Open_C,\r\n  APDet_tenTargetDirection_Close_C,\r\n  APDet_tenTargetDirection_Stop_C\r\n} APDet_tenTargetDirection;\r\n\r\n#endif                                 /* RTW_HEADER_APDet_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"BlockDet_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: BlockDet_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_BlockDet_ExpTypes_h_\r\n#define RTW_HEADER_BlockDet_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Enumeration for faults available */\r\ntypedef uint8_T BlockDet_FltType_t;\r\n\r\n/* enum BlockDet_FltType_t */\r\n#define BlockDet_FltType_t_NoFault_C   ((BlockDet_FltType_t)0U)  /* Default value */\r\n#define BlockDet_FltType_t_SamePosStallFault_C ((BlockDet_FltType_t)1U)\r\n#define BlockDet_FltType_t_DoubleStallFault_C ((BlockDet_FltType_t)2U)\r\n\r\n/* Stall Type Flag */\r\ntypedef uint8_T BlockDet_Stall_dir;\r\n\r\n/* enum BlockDet_Stall_dir */\r\n#define BlockDet_Stall_dir_NoMov_C     ((BlockDet_Stall_dir)0U)  /* Default value */\r\n#define BlockDet_Stall_dir_Open_C      ((BlockDet_Stall_dir)1U)\r\n#define BlockDet_Stall_dir_Close_C     ((BlockDet_Stall_dir)2U)\r\n#define BlockDet_Stall_dir_reserved    ((BlockDet_Stall_dir)3U)\r\n\r\n/* Stall Type Flag */\r\ntypedef uint8_T BlockDet_Stall_t;\r\n\r\n/* enum BlockDet_Stall_t */\r\n#define BlockDet_Stall_t_NoStall_C     ((BlockDet_Stall_t)0U)    /* Default value */\r\n#define BlockDet_Stall_t_IncStall_C    ((BlockDet_Stall_t)1U)\r\n#define BlockDet_Stall_t_DecStall_C    ((BlockDet_Stall_t)2U)\r\n#endif                                 /* RTW_HEADER_BlockDet_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"CfgParam_Mdl_Exp.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: CfgParam_Mdl_Exp.h\r\n *\r\n * Code generated for Simulink model 'CfgParam_Mdl'.\r\n *\r\n * Model version                  : 1.238\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:29:52 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_CfgParam_Mdl_Exp_h_\r\n#define RTW_HEADER_CfgParam_Mdl_Exp_h_\r\n#include \"CfgParam_Mdl_ExpTypes.h\"\r\n#include \"rtwtypes.h\"\r\n\r\n/* Exported data declaration */\r\n/* Declaration for custom storage class: ExportToFile */\r\nextern CfgParam_CALBus_t CfgParam_CAL;\r\nextern CfgParam_CALExtBus_t CfgParam_CALExt;\r\n\r\n/* CAL file Extened\r\n\r\n   Init value\r\n   struct('isAdjBlockApply',uint16([0 0]),'adjustATS',[struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0)) struct('hMinPos',uint16(0),'hMaxPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdMinPos',uint8(0),'thresholdMaxPos',uint8(0))],'OtherCALData',uint8(zeros(1,2)),'CALChecksum',uint16(47478)) */\r\nextern CfgParam_CALExtBus_t CfgParam_CALExt_SWConst_C;\r\n\r\n/* struct(...\r\n   'CALExtMinorVersion',uint8(1),...\r\n   'ReservedByte1',uint8(0),...\r\n   'isAdjBlockApply',uint16([0]),...\r\n   'isThresholdAmbTempDep',uint16([0]),...\r\n   'isThresholdVoltDep',uint16([0]),...\r\n   'isThresholdVehSpdDep',uint16([0]),...\r\n   'adjustATS',...\r\n   [struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))...\r\n   struct('hEndPos',uint16(0),'hStartPos',uint16(0),'TAmbMin',uint8(0),'TAmbMax',uint8(0),'u12vBattMin',uint8(0),'u12vBattMax',uint8(0),'vVehSpdMin',uint8(0),'vVehSpdMax',uint8(0),'thresholdEndPos',int8(0),'thresholdStartPos',int8(0))],...\r\n   'CALChecksum',uint16(57872))\r\n */\r\nextern CfgParam_CALBus_t CfgParam_CAL_SWConst_C;\r\n\r\n/*\r\n   struct(...\r\n   'CALVerMajor', uint8(1),...\r\n   'CALVerMinor', uint8(0),...\r\n   'hPosTbl', struct('hHardStopCls', uint16(0),...\r\n   'hSoftStopCls', uint16(200),...\r\n   'hNearClsSld', uint16(300),...\r\n   'hComfortMidStop', uint16(23100),...\r\n   'hSoftStopOpn', uint16(23100),...\r\n   'hHardStopOpn', uint16(23500)),...\r\n   'typeMtrDir', boolean(0),...\r\n   'typeHallDir', boolean(1),...\r\n   'hMovPosErr', uint8(20),...\r\n   'hStallRevDist', uint8(250),...\r\n   'hRefForceStartPos', uint16([0 0 0 0]),...\r\n   'hRefForceEndPos', uint16([0 0 0 0]),...\r\n   'hRefForceNumEl', uint8([0 0 0 0]),...\r\n   'TAmbMaxLearnAdp', int8(65),...\r\n   'TAmbMinLearnAdp', int8(0),...\r\n   'vMaxVehSpdLearnAdp', uint8(3),...\r\n   'uMaxDeltaLearnAdp', uint8(100),...\r\n   'rMinMtrSpdLearnAdp', uint8(10),...\r\n   'AdpPercent', uint8(30),...\r\n   'tMaxLearn', uint8(200),...\r\n   'uMaxVoltClassA', uint8(160),...\r\n   'uMinVoltClassA', uint8(100),...\r\n   'uMaxVoltClassB', uint8(170),...\r\n   'uMinVoltClassB', uint8(90),...\r\n   'uMaxVoltClassC', uint8(190),...\r\n   'uMinVoltClassC', uint8(90),...\r\n   'uMaxVoltClassD', uint8(170),...\r\n   'uMinVoltClassD', uint8(0),...\r\n   'uMaxVoltClassE', uint8(255),...\r\n   'uMinVoltClassE', uint8(0),...\r\n   'uFlctnDet', uint8(90),...\r\n   'FTrkPosForceDir', uint8(2),...\r\n   'FTrkPosForceDirLT', uint8(2),...\r\n   'FTrkNegForceDir', uint8(2),...\r\n   'FTrkNegForceDirLT', uint8(2),...\r\n   'FMaxForceDifTrkAct', uint8(50),...\r\n   'FMaxInclAbove0ForceTrkAct', uint8(2),...\r\n   'FMaxInclBelow0ForceTrkAct', uint8(2),...\r\n   'MtrSpeedZoneRampOpen', uint8(10),...\r\n   'rudimentaryATSopen', uint16(2000),...\r\n   'rudimentaryATSclose', uint16(0),...\r\n   'baselineATS_slide_open', uint8(40),...\r\n   'baselineATS_slide_close', uint8(30),...\r\n   'mtrStartupThreshold', uint8(200),...\r\n   'hMtrStartupDist', uint8(250),...\r\n   'mfgATSThreshold', uint8(0),...\r\n   'mfgIncrAdp', uint8(0),...\r\n   'vVehSpdExitMfg', uint8(3),...\r\n   'vMinVehSpdRR', uint8(3),...\r\n   'FDifMtrSpike', uint8(25),...\r\n   'MtrSpeedZoneRampClose', uint8(10),...\r\n   'RRThreshold', uint16(20),...\r\n   'tDifMtrSpike', uint8(0),...\r\n   'tRRDeactive', uint8(250),...\r\n   'lowSpdATSThreshold', uint16(55),...\r\n   'highSpdATSThreshold_slide', uint16(75),...\r\n   'vVehSpdATSLow', uint8(6),...\r\n   'vVehSpdATSHigh', uint8(60),...\r\n   'overrideThreshold_stg1', uint8(200),...\r\n   'overrideThreshold_stg2', uint8(200),...\r\n   'hSlideRevClsDist', uint16(0),...\r\n   'hSlideRevOpnPos', uint16(0),...\r\n   'hSlideRevOpnDist', uint16(3900),...\r\n   'TThermProtThresholds', struct('MotorNoMove', uint8(160),...\r\n   'MotorATSAllowed', uint8(155),...\r\n   'MotorOngoingOpen', uint8(150),...\r\n   'MotorOnlyOpen', uint8(140),...\r\n   'MotorSleepInhibit', uint8(90),...\r\n   'MotorTempHystersis', uint8(20)),...\r\n   'TThermProtThresholds_MosfetLevels', struct('MosfetNoMove', uint8(200),...\r\n   'MosfetATSAllowed', uint8(180),...\r\n   'MosfetOngoingOpen', uint8(170),...\r\n   'MosfetOnlyOpen', uint8(160),...\r\n   'MosfetTempHystersis', uint8(20)),...\r\n   'TAmbCyclicRenorm', uint8(85),...\r\n   'nRPRelearnCycle', uint16(20),...\r\n   'tOverride', uint8(2),...\r\n   'tATSRevNoPos', uint8(100),...\r\n   'hDirChangeMtrStartupDist', uint8(25),...\r\n   'tEnterUnlearnModeMfg', uint8(10),...\r\n   'AmbTempUpperLimRun', uint8(185),...\r\n   'AmbTempLowerLimRun', uint8(0),...\r\n   'AmbTempUpperLimInit', uint8(160),...\r\n   'AmbTempLowerLimInit', uint8(5),...\r\n   'AmbTempSlopeLimit', uint8(20),...\r\n   'MosfetTempUpperLimRun', uint8(210),...\r\n   'MosfetTempLowerLimRun', uint8(0),...\r\n   'MosfetTempUpperLimInit', uint8(200),...\r\n   'MosfetTempLowerLimInit', uint8(5),...\r\n   'MosfetTempSlopeLimit', uint8(40),...\r\n   'MtrSpeedZone1Open', uint16(300),...\r\n   'MtrSpeedZone1Close', uint16(300),...\r\n   'MtrSpeedZone2Open', uint16(300),...\r\n   'MtrSpeedZone2Close', uint16(300),...\r\n   'SoftStartPoint1Perc', uint8(40),...\r\n   'SoftStartPoint2Perc', uint8(80),...\r\n   'SoftStartPoint1Time', uint16(40),...\r\n   'SoftStartPoint2Time', uint16(60),...\r\n   'SoftStopPointPerc', uint8(40),...\r\n   'SoftStartSoftStopEnable', uint8(3),...\r\n   'SoftStopPointTime', uint16(80),...\r\n   'TightenClothDis', uint16(600),...\r\n   'PARA_NOSYNC_Operation', CfgParam_NoSyncParam_t(1),...\r\n   'PARA_NOAPP_Operation', CfgParam_NoAppParam_t(0),...\r\n   'PARA_NOSYNC_Stepsize', uint16(500),...\r\n   'PARA_NOAPP_Stepsize', uint16(500),...\r\n   'PARA_NOSYNC_Steptime', uint16(50),...\r\n   'BlockDet_posSoftStallThreshold', uint16(500),...\r\n   'Voltage_Drop_Difference', uint16(30),...\r\n   'Voltage_Drop_Stabilization_Time', uint16(500),...\r\n   'Voltage_Drop_Time', uint16(100),...\r\n   'Voltage_Fluctuation_Stabilization_Time', uint16(1000),...\r\n   'Voltage_Fluctuation_Time', uint16(100),...\r\n   'VoltMon_uHys', uint16(200),...\r\n   'BlockDet_thresholdValueDetection', uint16([25000 1800]),...\r\n   'MtrSpeedZonePositionOpen', uint16(10000),...\r\n   'MtrSpeedZonePositionClose', uint16(10000),...\r\n   'CloseLoopSpeedPID_P', fi(0.2,0,16,8),...\r\n   'CloseLoopSpeedPID_I', fi(0.03,0,16,16),...\r\n   'hMaxPosSoftStopClose', uint16(0),...\r\n   'tMovingTimeOut', uint16(40),...\r\n   'CyclicRenormOpenCloseEnabling', uint16(3),...\r\n   'ReservedByte6', uint16(0),...\r\n   'ReservedByte7', uint16(0),...\r\n   'ReservedByte8', uint16(0),...\r\n   'ReservedByte9', uint16(0),...\r\n   'ReservedByte10', uint16(0),...\r\n   'ReservedByte11', uint16(0),...\r\n   'ReservedByte12', uint16(0),...\r\n   'ReservedByte13', uint16(0),...\r\n   'ReservedByte14', uint16(0),...\r\n   'CALChecksum', uint16(18105))\r\n */\r\nextern boolean_T Config_isCALFileVld;\r\n\r\n/* CAL File Validity Status\r\n   0: Invalid\r\n   1: Valid */\r\nextern boolean_T Config_isPosTblVld;\r\n\r\n/* Reference Force Table Validity Status( Rollo)\r\n   0: Invalid\r\n   1: Valid */\r\nextern boolean_T Config_isStartupRdy;\r\nextern uint8_T Config_stCALFileFlt;\r\n\r\n/* CAL File Fault Status\r\n   0: Not Run\r\n   1: Run and Pass\r\n   2: Run and Fail */\r\nextern Config_TestMode_t Config_stTestMode;\r\n\r\n/* PV test mode */\r\n#endif                                 /* RTW_HEADER_CfgParam_Mdl_Exp_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"CfgParam_Mdl_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: CfgParam_Mdl_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_CfgParam_Mdl_ExpTypes_h_\r\n#define RTW_HEADER_CfgParam_Mdl_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n#include \"CfgParam_TThermThreshBus_t.h\"\r\n#include \"CfgParam_TThermThreshBus_MosfetLevels.h\"\r\n#include \"RoofOper_ExpTypes.h\"\r\n\r\ntypedef enum {\r\n  ECUSWMode_t_Run_AppSW = 0,           /* Default value */\r\n  ECUSWMode_t_Run_BootSW\r\n} ECUSWMode_t;\r\n\r\n/* bus object for glass panel position table */\r\ntypedef struct {\r\n  /* Hard stop position close */\r\n  uint16_T hHardStopCls;\r\n\r\n  /* Soft stop close position (flush close) */\r\n  uint16_T hSoftStopCls;\r\n\r\n  /* Near close position (always close position), close movement cannot be interrupted untill soft stop close has been reached in soft stop direction */\r\n  uint16_T hNearClsSld;\r\n\r\n  /* Additional soft stop to be set in between full close and full open soft stop */\r\n  uint16_T hComfortMidStop;\r\n\r\n  /* Soft stop full open position */\r\n  uint16_T hSoftStopOpn;\r\n\r\n  /* Hard stop position open */\r\n  uint16_T hHardStopOpn;\r\n} CfgParam_hPosBus_t;\r\n\r\n/* ATS adjust block bus object  */\r\ntypedef struct {\r\n  /* End position the block is active */\r\n  uint16_T hEndPos;\r\n\r\n  /* Start position the block is active */\r\n  uint16_T hStartPos;\r\n\r\n  /* Minimum Ambient Temperature the block is active\r\n     (offset 40)\r\n     Temperature range: -40~215 degC */\r\n  uint8_T TAmbMin;\r\n\r\n  /* Maximum Ambient Temperature the block is active\r\n     (offset 40)\r\n     Temperature range: -40~215 degC */\r\n  uint8_T TAmbMax;\r\n\r\n  /* Minimum System Voltage the block is active\r\n     Voltage range: 0~25.5 volts */\r\n  uint8_T u12vBattMin;\r\n\r\n  /* Maximum System Voltage the block is active\r\n     Voltage range: 0~25.5 volts */\r\n  uint8_T u12vBattMax;\r\n\r\n  /* Minimum Vehicle Speed the block is active */\r\n  uint8_T vVehSpdMin;\r\n\r\n  /* Maximum Vehicle Speed the block is active */\r\n  uint8_T vVehSpdMax;\r\n\r\n  /* The threshold adjustment value at the end position the block is active */\r\n  int8_T thresholdEndPos;\r\n\r\n  /* The threshold adjustment value at the start position the block is active */\r\n  int8_T thresholdStartPos;\r\n} CfgParam_adjBlock_t;\r\n\r\ntypedef struct {\r\n  uint8_T ActiveDiagnosticVersion;\r\n  uint8_T ActiveDiagnosticVariant;\r\n  ECUSWMode_t ECUSWMode;\r\n} CfgParam_ActiveDiagInfoBus_t;\r\n\r\n/* CAL File Bus Object (172 bytes total)\r\n\r\n   Note: When adding new items to the bus check for any ReservedByte and use them if the datasize of new item fits in this location.\r\n */\r\ntypedef struct {\r\n  /* Major version CALfile, updated when CALfile layout is changed */\r\n  uint8_T CALVerMajor;\r\n\r\n  /* Minor version Calfile, updated when CALfile values change */\r\n  uint8_T CALVerMinor;\r\n\r\n  /* Panel calibrated position table with following fields per panel:\r\n     hHardStopCls - Hard stop position close\r\n     hSoftStopCls - Soft stop close position (flush close)\r\n     hNearCls - Near close position (always close position), close movement cannot be interrupted untill soft stop close has been reached in soft stop direction\r\n     hComfortMidStop - Additional soft stop to be set in between full close and full open soft stop\r\n     hSoftStopOpn - Soft stop full open position\r\n     hHardStopOpn - Hard stop position open */\r\n  CfgParam_hPosBus_t hPosTbl;\r\n\r\n  /* 0: Type1\r\n     Increasing: Motor Relay1 is high, Motor Relay2 is low\r\n     Decreasing: Motor Relay1 is low, Motor Relay2 is high\r\n     1: Type2\r\n     Increasing: Motor Relay1 is low, Motor Relay2 is high\r\n     Decreasing: Motor Relay1 is high, Motor Relay2 is low */\r\n  boolean_T typeMtrDir;\r\n\r\n  /* 0: Type1\r\n     CW: Hall 1 becomes to high, Hall 2 is low\r\n     CCW: Hall 1 becomes to high, Hall 2 is high\r\n     1: Type2\r\n     CW: Hall 1 becomes to high, Hall 2 is high\r\n     CCW: Hall 1 becomes to high, Hall 2 is low */\r\n  boolean_T typeHallDir;\r\n\r\n  /* Allowed deviation when stopping at a softstop position in hall pulses. The control logic will deactivate the motor when the panel is this amount of hall pulses prior the soft stop */\r\n  uint8_T hMovPosErr;\r\n\r\n  /* Amount of hall pulses the panel will reverse when a block has been detected to relax cable tension */\r\n  uint8_T hStallRevDist;\r\n\r\n  /* Start position of reference field block in hall pulses. Start position greater than end position means reference field is in closing direction, otherwise open direction. */\r\n  uint16_T hRefForceStartPos[4];\r\n\r\n  /* Stop position of reference field block in hall pulses. Start position greater than end position means reference field is in closing direction, otherwise open direction. */\r\n  uint16_T hRefForceEndPos[4];\r\n\r\n  /* Number of elements per reference field block. The reference field block will be divided in even sections. The distance does not have to be exactly dividable. When there are too many elements per amount of hall pulses (small field, many elements), learning of panel may fail. Total amount of elements per Panel (all four fields combined) cannot exceed 251 */\r\n  uint8_T hRefForceNumEl[4];\r\n\r\n  /* Maximum ambient temperature learning is still allowed */\r\n  int8_T TAmbMaxLearnAdp;\r\n\r\n  /* Minimum ambient temperature learning is still allowed */\r\n  int8_T TAmbMinLearnAdp;\r\n\r\n  /* Maximum vehicle speed learning is still allowed */\r\n  uint8_T vMaxVehSpdLearnAdp;\r\n\r\n  /* Maximum voltage difference during a movement through the reference field. (e.g. during movement the voltage drops slowly from 13 to 12v in case parameter 100) */\r\n  uint8_T uMaxDeltaLearnAdp;\r\n\r\n  /* Minimum motor speed which should be maintained during the learning movement (the speed needs to drop under the parameter speed for more than 100ms to abort learning) */\r\n  uint8_T rMinMtrSpdLearnAdp;\r\n\r\n  /* Percentage the force difference will be adapted when a movement has been completed with all adaption conditions valid for the whole movement */\r\n  uint8_T AdpPercent;\r\n\r\n  /* Maximum learning time before aborting learning\r\n   */\r\n  uint8_T tMaxLearn;\r\n\r\n  /* Upper limit voltage class (voltage class A is normal conditions, everything allowed)\r\n   */\r\n  uint8_T uMaxVoltClassA;\r\n\r\n  /* Lower limit voltage class (voltage class A is normal conditions, everything allowed) */\r\n  uint8_T uMinVoltClassA;\r\n\r\n  /* Upper limit voltage class (voltage class B conditions, no learning allowed) */\r\n  uint8_T uMaxVoltClassB;\r\n\r\n  /* Lower limit voltage class (voltage class B conditions, no learning allowed) */\r\n  uint8_T uMinVoltClassB;\r\n\r\n  /* Upper limit voltage class (voltage class C conditions, no new movement allowed) */\r\n  uint8_T uMaxVoltClassC;\r\n\r\n  /* Lower limit voltage class (voltage class C conditions, no new movement allowed) */\r\n  uint8_T uMinVoltClassC;\r\n\r\n  /* Upper limit voltage class (voltage class D conditions, no new movement allowed and ongoing movements stopped) */\r\n  uint8_T uMaxVoltClassD;\r\n\r\n  /* Lower limit voltage class (voltage class D conditions, no new movement allowed and ongoing movements stopped) */\r\n  uint8_T uMinVoltClassD;\r\n\r\n  /* Upper limit voltage class (voltage class E conditions, no new movement allowed and ongoing movements stopped) */\r\n  uint8_T uMaxVoltClassE;\r\n\r\n  /* Lower limit voltage class (voltage class E conditions, no new movement allowed and ongoing movements stopped) */\r\n  uint8_T uMinVoltClassE;\r\n\r\n  /* Voltage fluctuation threshold [cV] */\r\n  uint8_T uFlctnDet;\r\n\r\n  /* Force that will be added when tracking is active in positive direction and ambient temperature is 0 or above [dN] */\r\n  uint8_T FTrkPosForceDir;\r\n\r\n  /* Force that will be added when tracking is active in positive direction and ambient temperature is below 0 [dN] */\r\n  uint8_T FTrkPosForceDirLT;\r\n\r\n  /* Force that will be added when tracking is active in negative direction and ambient temperature is 0 or above [dN] */\r\n  uint8_T FTrkNegForceDir;\r\n\r\n  /* Force that will be added when tracking is active in negative direction and ambient temperature is below 0 [dN] */\r\n  uint8_T FTrkNegForceDirLT;\r\n\r\n  /* Maximum difference between expected force and actual force to activate tracking [N]\r\n   */\r\n  uint8_T FMaxForceDifTrkAct;\r\n\r\n  /* Maximum incline above 0 degrees force difference tracking is active [dN]\r\n   */\r\n  uint8_T FMaxInclAbove0ForceTrkAct;\r\n\r\n  /* Maximum incline below 0 degrees force difference tracking is active [dN]\r\n   */\r\n  uint8_T FMaxInclBelow0ForceTrkAct;\r\n\r\n  /* Ramp of motor speed change between zones in volt/10 per second in open direction */\r\n  uint8_T MtrSpeedZoneRampOpen;\r\n\r\n  /* Rudimentary ATS threshold (open) [N] */\r\n  uint16_T rudimentaryATSopen;\r\n\r\n  /* Rudimentary ATS threshold (close) [N] */\r\n  uint16_T rudimentaryATSclose;\r\n\r\n  /* Baseline ATS threshold\r\n     (Glass slide open/close, Glass vent open/close , Rollo slive open/close and Rollo vent open/close) */\r\n  uint8_T baselineATS_slide_open;\r\n\r\n  /* Baseline ATS threshold Rollo slide close [N] */\r\n  uint8_T baselineATS_slide_close;\r\n\r\n  /* Motor start-up ATS threshold [daN] */\r\n  uint8_T mtrStartupThreshold;\r\n\r\n  /* Amount of distance motor start up is active */\r\n  uint8_T hMtrStartupDist;\r\n\r\n  /* Manufacturing mode ATS threshold [N] */\r\n  uint8_T mfgATSThreshold;\r\n\r\n  /* Adaption percentage increment under manufacturing mode [%]\r\n   */\r\n  uint8_T mfgIncrAdp;\r\n\r\n  /* Vehicle speed at which manufacturing mode will be exited */\r\n  uint8_T vVehSpdExitMfg;\r\n\r\n  /* Minimum vehicle speed at which the rough road can be detected */\r\n  uint8_T vMinVehSpdRR;\r\n\r\n  /* Force difference to detect the spike of motor force [N] */\r\n  uint8_T FDifMtrSpike;\r\n\r\n  /* Ramp of motor speed change between zones in volt/10 per second in close direction */\r\n  uint8_T MtrSpeedZoneRampClose;\r\n\r\n  /* Rough road ATS threshold [N] */\r\n  uint16_T RRThreshold;\r\n\r\n  /* Time threshold to detect the spike of motor force [ms] */\r\n  uint8_T tDifMtrSpike;\r\n\r\n  /* Minimum duration time of no rough road detected [ms] */\r\n  uint8_T tRRDeactive;\r\n\r\n  /* Low Speed ATS threshold [N] */\r\n  uint16_T lowSpdATSThreshold;\r\n\r\n  /* High Speed ATS threshold Slide */\r\n  uint16_T highSpdATSThreshold_slide;\r\n\r\n  /* Low vehicle speed threshold of ATS [km/h] */\r\n  uint8_T vVehSpdATSLow;\r\n\r\n  /* High vehicle speed threshold of ATS [km/h] */\r\n  uint8_T vVehSpdATSHigh;\r\n\r\n  /* Override ATS threshold Stage 1. The amount of threshold increase when this override stage is active. */\r\n  uint8_T overrideThreshold_stg1;\r\n\r\n  /* Override ATS threshold Stage 2. The amount of threshold increase when this override stage is active. */\r\n  uint8_T overrideThreshold_stg2;\r\n\r\n  /* Distance of pinch reversal in close direction slide */\r\n  uint16_T hSlideRevClsDist;\r\n\r\n  /* Minimum position reversal in opening direction will end slide */\r\n  uint16_T hSlideRevOpnPos;\r\n\r\n  /* Distance of pinch reversal in opening direction */\r\n  uint16_T hSlideRevOpnDist;\r\n\r\n  /* Thermal protection thresholds (Rollo) */\r\n  CfgParam_TThermThreshBus_t TThermProtThresholds;\r\n\r\n  /* Thermal protection thresholds (Glass and Rollo) */\r\n  CfgParam_TThermThreshBus_MosfetLevels TThermProtThresholds_MosfetLevels;\r\n\r\n  /* Difference between current ambient temperature and temperature during last norming event at which a cyclic renorming needs to be triggered regardless of amount of cycles traveled since last renorm\r\n   */\r\n  uint8_T TAmbCyclicRenorm;\r\n\r\n  /* Number of cycles to be completed to do a relearning of the zero position (panel will run into hard stop and set hard stop position to zero) */\r\n  uint16_T nRPRelearnCycle;\r\n\r\n  /* Override ATS time threshold [sec]. The time that the next movement needs to be started after a pinch to stsrt an override movement */\r\n  uint8_T tOverride;\r\n\r\n  /* Amount of time a reversal will continue when position is lost */\r\n  uint8_T tATSRevNoPos;\r\n\r\n  /* Additional start up distance when the motor changes direction */\r\n  uint8_T hDirChangeMtrStartupDist;\r\n\r\n  /* Time untill relearning starts by pressing relearn switch when manufacturing mode is active */\r\n  uint8_T tEnterUnlearnModeMfg;\r\n\r\n  /* Maximum valid value of ambient temperature during runtime */\r\n  uint8_T AmbTempUpperLimRun;\r\n\r\n  /* Minimum valid value of ambient temperature during runtime */\r\n  uint8_T AmbTempLowerLimRun;\r\n\r\n  /* Maximum valid value of ambient temperature at startup */\r\n  uint8_T AmbTempUpperLimInit;\r\n\r\n  /* Minimum valid value of ambient temperature at startup */\r\n  uint8_T AmbTempLowerLimInit;\r\n\r\n  /* Maximum allowed deviation in temperature within one second */\r\n  uint8_T AmbTempSlopeLimit;\r\n\r\n  /* Maximum valid value of mosfet temperature during runtime */\r\n  uint8_T MosfetTempUpperLimRun;\r\n\r\n  /* Minimum valid value of mosfet temperature during runtime */\r\n  uint8_T MosfetTempLowerLimRun;\r\n\r\n  /* Maximum valid value of mosfet temperature at startup */\r\n  uint8_T MosfetTempUpperLimInit;\r\n\r\n  /* Minimum valid value of mosfet temperature at startup */\r\n  uint8_T MosfetTempLowerLimInit;\r\n\r\n  /* Maximum allowed deviation in temperature within one second */\r\n  uint8_T MosfetTempSlopeLimit;\r\n\r\n  /* Target speed automatic open movement */\r\n  uint16_T MtrSpeedZone1Open;\r\n\r\n  /* Target speed automatic close movement */\r\n  uint16_T MtrSpeedZone1Close;\r\n\r\n  /* Target speed manual open movement */\r\n  uint16_T MtrSpeedZone2Open;\r\n\r\n  /* Target speed manual close movement */\r\n  uint16_T MtrSpeedZone2Close;\r\n\r\n  /* Percentage of target speed at start of movement, initial speed */\r\n  uint8_T SoftStartPoint1Perc;\r\n\r\n  /* Pecentage of target speed reached at point 2 */\r\n  uint8_T SoftStartPoint2Perc;\r\n\r\n  /* Time stamp at which point 2 should be reached */\r\n  uint16_T SoftStartPoint1Time;\r\n\r\n  /* Time stamp after point 2 at which target speed should be reached */\r\n  uint16_T SoftStartPoint2Time;\r\n\r\n  /* Percentage of target speed at the end of the stop movement ramp */\r\n  uint8_T SoftStopPointPerc;\r\n\r\n  /* Enable or Disable soft start soft stop:\r\n     bit0=true enable soft start, false disable soft start\r\n     bit1 = true enable soft stop, false disable soft */\r\n  uint8_T SoftStartSoftStopEnable;\r\n\r\n  /* Delta time at which Low speed target to be reached  */\r\n  uint16_T SoftStopPointTime;\r\n\r\n  /* The distance to be traveled in opposite direction te retention the cloth */\r\n  uint16_T TightenClothDis;\r\n\r\n  /* Enumeration for behavior when reference position is invalid */\r\n  CfgParam_NoSyncParam_t PARA_NOSYNC_Operation;\r\n\r\n  /* Enumeration for behavior when reference field is invalid */\r\n  CfgParam_NoAppParam_t PARA_NOAPP_Operation;\r\n\r\n  /* Distance of stepwize movement when reference position is invalid */\r\n  uint16_T PARA_NOSYNC_Stepsize;\r\n\r\n  /* Distance of stepwize movement when reference field is invalid */\r\n  uint16_T PARA_NOAPP_Stepsize;\r\n\r\n  /* Rollo stepwise close time base operation completed */\r\n  uint16_T PARA_NOSYNC_Steptime;\r\n  uint16_T BlockDet_posSoftStallThreshold;\r\n\r\n  /* Voltage threshold to determine Voltage Drop Active */\r\n  uint16_T Voltage_Drop_Difference;\r\n\r\n  /* Time threshold to determine Voltage Drop Inactive */\r\n  uint16_T Voltage_Drop_Stabilization_Time;\r\n\r\n  /* Time threshold to determine Voltage Drop Active */\r\n  uint16_T Voltage_Drop_Time;\r\n\r\n  /* Time threshold to determine Voltage Fluctuation Inactive */\r\n  uint16_T Voltage_Fluctuation_Stabilization_Time;\r\n\r\n  /* Time threshold to determine Voltage Fluctuation Active */\r\n  uint16_T Voltage_Fluctuation_Time;\r\n\r\n  /* calibratable parameter for hysteresis with a typical value ±0.2 Volt for the transitions between the voltage ranges. */\r\n  uint16_T VoltMon_uHys;\r\n  uint16_T BlockDet_thresholdValueDetection[2];\r\n  uint16_T MtrSpeedZonePositionOpen;\r\n  uint16_T MtrSpeedZonePositionClose;\r\n  uint16_T CloseLoopSpeedPID_P;\r\n  uint16_T CloseLoopSpeedPID_I;\r\n  uint16_T hMaxPosSoftStopClose;\r\n  uint16_T tMovingTimeOut;\r\n  uint16_T CyclicRenormOpenCloseEnabling;\r\n  uint16_T ReservedByte6;\r\n  uint16_T ReservedByte7;\r\n  uint16_T ReservedByte8;\r\n  uint16_T ReservedByte9;\r\n  uint16_T ReservedByte10;\r\n  uint16_T ReservedByte11;\r\n  uint16_T ReservedByte12;\r\n  uint16_T ReservedByte13;\r\n  uint16_T ReservedByte14;\r\n\r\n  /* CAL file Checksum */\r\n  uint16_T CALChecksum;\r\n} CfgParam_CALBus_t;\r\n\r\n/* CAL File extended Bus Object (200 bytes total)\r\n\r\n\r\n */\r\ntypedef struct {\r\n  /* This variable to allow CRC manager to get start address of Ext CAL variable */\r\n  uint8_T CALExtMinorVersion;\r\n\r\n  /* This space is reserved to avoid padding by compiler and can be used later\r\n   */\r\n  uint8_T ReservedByte1;\r\n\r\n  /* ATS threshold adjustment block is applicable or not\r\n     (Glass or Rollo or None)\r\n     Each bit indicates each block applicable\r\n     Bit0~Bit15 represent adjustment block1 to block16\r\n     1: means applied\r\n     0: means not applied */\r\n  uint16_T isAdjBlockApply;\r\n\r\n  /* ATS temperature dependent threshold for corresponding threshold adjustment block is applicable or not\r\n     (Glass and Rollo or None)\r\n     Each bit indicates each block applicable\r\n     Bit0~Bit15 represent adjustment block1 to block16\r\n     1: means applied\r\n     0: means not applied */\r\n  uint16_T isThresholdAmbTempDep;\r\n\r\n  /* ATS voltage dependent threshold for corresponding threshold adjustment block is applicable or not\r\n     (Glass and Rollo or None)\r\n     Each bit indicates each block applicable\r\n     Bit0~Bit15 represent adjustment block1 to block16\r\n     1: means applied\r\n     0: means not applied */\r\n  uint16_T isThresholdVoltDep;\r\n\r\n  /* ATS verhicle speed dependent threshold for corresponding threshold adjustment block is applicable or not\r\n     (Glass and Rollo or None)\r\n     Each bit indicates each block applicable\r\n     Bit0~Bit15 represent adjustment block1 to block16\r\n     1: means applied\r\n     0: means not applied */\r\n  uint16_T isThresholdVehSpdDep;\r\n\r\n  /* Adjustment ATS threshold(16 blocks) */\r\n  CfgParam_adjBlock_t adjustATS[16];\r\n\r\n  /* CAL file Checksum */\r\n  uint16_T CALChecksum;\r\n} CfgParam_CALExtBus_t;\r\n\r\ntypedef struct {\r\n  uint8_T DDSPAckageRelAppSW[2];\r\n  uint8_T DDSPAckageRelBootSW[2];\r\n} CfgParam_DDSPackageRelBus_t;\r\n\r\ntypedef struct {\r\n  uint8_T HWYear;\r\n  uint8_T HWWeek;\r\n  uint8_T HWPatch;\r\n} CfgParam_HWVerInfoBus_t;\r\n\r\ntypedef struct {\r\n  uint8_T SWYear;\r\n  uint8_T SWWeek;\r\n  uint8_T SWPatch;\r\n} CfgParam_SWVerInfoBus_t;\r\n\r\n/* PV test mode type */\r\ntypedef uint8_T CfgParam_TestMode_t;\r\n\r\n/* enum CfgParam_TestMode_t */\r\n#define CfgParam_TestMode_t_Disabled_C ((CfgParam_TestMode_t)0U) /* Default value */\r\n#define CfgParam_TestMode_t_All_CW_C   ((CfgParam_TestMode_t)1U)\r\n#define CfgParam_TestMode_t_Sequential_C ((CfgParam_TestMode_t)2U)\r\n#define CfgParam_TestMode_t_All_Stop_C ((CfgParam_TestMode_t)3U)\r\n#define CfgParam_TestMode_t_All_CCW_C  ((CfgParam_TestMode_t)4U)\r\n\r\n/* PV test mode type */\r\ntypedef uint8_T Config_TestMode_t;\r\n\r\n/* enum Config_TestMode_t */\r\n#define Config_TestMode_t_Disabled_C   ((Config_TestMode_t)0U)   /* Default value */\r\n#define Config_TestMode_t_All_CW_C     ((Config_TestMode_t)1U)\r\n#define Config_TestMode_t_Sequential_C ((Config_TestMode_t)2U)\r\n#define Config_TestMode_t_All_Stop_C   ((Config_TestMode_t)3U)\r\n#define Config_TestMode_t_All_CCW_C    ((Config_TestMode_t)4U)\r\n#endif                                 /* RTW_HEADER_CfgParam_Mdl_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"CfgParam_TThermThreshBus_MosfetLevels.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: CfgParam_TThermThreshBus_MosfetLevels.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_CfgParam_TThermThreshBus_MosfetLevels_h_\r\n#define RTW_HEADER_CfgParam_TThermThreshBus_MosfetLevels_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Temporarily added. */\r\ntypedef struct {\r\n  /* When no move mosfet temperature exceeded, no movement is allowed. All movements will be stopped */\r\n  uint8_T MosfetNoMove;\r\n\r\n  /* When ATS Allowed mosfet temperature exceeded, only an ATS movement is allowed, all other movements will be stopped */\r\n  uint8_T MosfetATSAllowed;\r\n\r\n  /* When ongoing open mosfet temperature threshold has been exceeded, no new movement can be started, close movements will be stopped, open movements will not be stopped */\r\n  uint8_T MosfetOngoingOpen;\r\n\r\n  /* When only open mosfet temperature threshold is exceeded, only new open movements will be allowed, closing movements will not be stopped */\r\n  uint8_T MosfetOnlyOpen;\r\n\r\n  /* Amount of degrees mosfet temperature needs to be below the threshold to drop one temperature level */\r\n  uint8_T MosfetTempHystersis;\r\n} CfgParam_TThermThreshBus_MosfetLevels;\r\n\r\n#endif                 /* RTW_HEADER_CfgParam_TThermThreshBus_MosfetLevels_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"CfgParam_TThermThreshBus_t.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: CfgParam_TThermThreshBus_t.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_CfgParam_TThermThreshBus_t_h_\r\n#define RTW_HEADER_CfgParam_TThermThreshBus_t_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Temporarily added. */\r\ntypedef struct {\r\n  /* When no move motor temperature exceeded, no movement is allowed. All movements will be stopped\r\n   */\r\n  uint8_T MotorNoMove;\r\n\r\n  /* When ATS Allowed motor temperature exceeded, only an ATS movement is allowed, all other movements will be stopped */\r\n  uint8_T MotorATSAllowed;\r\n\r\n  /* When ongoing open threshold has been exceeded, no new movement can be started, close movements will be stopped, open movements will not be stopped */\r\n  uint8_T MotorOngoingOpen;\r\n\r\n  /* When only open motor temperature threshold is exceeded, only new open movements will be allowed, closing movements will not be stopped */\r\n  uint8_T MotorOnlyOpen;\r\n\r\n  /* When motor temperature is above this threshold, sleep will be inhibited. */\r\n  uint8_T MotorSleepInhibit;\r\n\r\n  /* Amount of degrees motor temperature needs to be below the threshold to drop one temperature leve */\r\n  uint8_T MotorTempHystersis;\r\n} CfgParam_TThermThreshBus_t;\r\n\r\n#endif                            /* RTW_HEADER_CfgParam_TThermThreshBus_t_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"CmdLogic_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: CmdLogic_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_CmdLogic_ExpTypes_h_\r\n#define RTW_HEADER_CmdLogic_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\ntypedef uint8_T VehCom_Cmd_t;\r\n\r\n/* enum VehCom_Cmd_t */\r\n#define VehCom_Cmd_t_None_C            ((VehCom_Cmd_t)0U)        /* Default value */\r\n#define VehCom_Cmd_t_Stop_C            ((VehCom_Cmd_t)1U)\r\n#define VehCom_Cmd_t_ManOpn_C          ((VehCom_Cmd_t)2U)\r\n#define VehCom_Cmd_t_ManCls_C          ((VehCom_Cmd_t)3U)\r\n#define VehCom_Cmd_t_AutoToPos_C       ((VehCom_Cmd_t)4U)\r\n#define VehCom_Cmd_t_AutoToPosStep_C   ((VehCom_Cmd_t)5U)\r\n#define VehCom_Cmd_t_StepOpn_C         ((VehCom_Cmd_t)6U)\r\n#define VehCom_Cmd_t_StepCls_C         ((VehCom_Cmd_t)7U)\r\n#define VehCom_Cmd_t_Learn_C           ((VehCom_Cmd_t)8U)\r\n#define VehCom_Cmd_t_Invalid_C         ((VehCom_Cmd_t)9U)\r\n#define VehCom_Cmd_t_Learn_WOPreCond_C ((VehCom_Cmd_t)10U)\r\n#define VehCom_Cmd_t_AutoHallToPos_C   ((VehCom_Cmd_t)11U)\r\n#define VehCom_Cmd_t_ManOpnCont_C      ((VehCom_Cmd_t)12U)\r\n#define VehCom_Cmd_t_ManClsCont_C      ((VehCom_Cmd_t)13U)\r\n#endif                                 /* RTW_HEADER_CmdLogic_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"CtrlLogic_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: CtrlLogic_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_CtrlLogic_ExpTypes_h_\r\n#define RTW_HEADER_CtrlLogic_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Control Logic Relearning Mode Type */\r\ntypedef uint8_T CtrlLog_RelearnMode_t;\r\n\r\n/* enum CtrlLog_RelearnMode_t */\r\n#define CtrlLog_RelearnMode_t_Idle_C   ((CtrlLog_RelearnMode_t)0U) /* Default value */\r\n#define CtrlLog_RelearnMode_t_ReqLearn_C ((CtrlLog_RelearnMode_t)1U)\r\n#define CtrlLog_RelearnMode_t_LearningPos_C ((CtrlLog_RelearnMode_t)2U)\r\n#define CtrlLog_RelearnMode_t_LearningRF_C ((CtrlLog_RelearnMode_t)3U)\r\n#define CtrlLog_RelearnMode_t_Complete_C ((CtrlLog_RelearnMode_t)4U)\r\n#define CtrlLog_RelearnMode_t_Interrupted_C ((CtrlLog_RelearnMode_t)5U)\r\n#define CtrlLog_RelearnMode_t_Renorm_C ((CtrlLog_RelearnMode_t)6U)\r\n\r\ntypedef enum {\r\n  CtrlLog_tenTargetDirection_None_C = 0,/* Default value */\r\n  CtrlLog_tenTargetDirection_Open_C,\r\n  CtrlLog_tenTargetDirection_Close_C,\r\n  CtrlLog_tenTargetDirection_Stop_C\r\n} CtrlLog_tenTargetDirection;\r\n\r\n#endif                                 /* RTW_HEADER_CtrlLogic_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"HallDeco_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: HallDeco_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_HallDeco_ExpTypes_h_\r\n#define RTW_HEADER_HallDeco_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Motor Direction Type */\r\ntypedef uint8_T HallDeco_Dir_t;\r\n\r\n/* enum HallDeco_Dir_t */\r\n#define HallDeco_Dir_t_DirNone_C       ((HallDeco_Dir_t)0U)      /* Default value */\r\n#define HallDeco_Dir_t_CW_C            ((HallDeco_Dir_t)1U)\r\n#define HallDeco_Dir_t_CCW_C           ((HallDeco_Dir_t)2U)\r\n#endif                                 /* RTW_HEADER_HallDeco_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"LearnAdp_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: LearnAdp_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_LearnAdp_ExpTypes_h_\r\n#define RTW_HEADER_LearnAdp_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* LearnAdaption Mode Type */\r\ntypedef uint8_T LearnAdap_Mode_t;\r\n\r\n/* enum LearnAdap_Mode_t */\r\n#define LearnAdap_Mode_t_Idle_C        ((LearnAdap_Mode_t)0U)    /* Default value */\r\n#define LearnAdap_Mode_t_LearningPos_C ((LearnAdap_Mode_t)1U)\r\n#define LearnAdap_Mode_t_AdaptionOpen_C ((LearnAdap_Mode_t)2U)\r\n#define LearnAdap_Mode_t_AdaptionClose_C ((LearnAdap_Mode_t)3U)\r\n\r\n/* LearnAdaption Request Type */\r\ntypedef uint8_T LearnAdap_Req_t;\r\n\r\n/* enum LearnAdap_Req_t */\r\n#define LearnAdap_Req_t_NoReq_C        ((LearnAdap_Req_t)0U)     /* Default value */\r\n#define LearnAdap_Req_t_ClrReq_C       ((LearnAdap_Req_t)1U)\r\n#define LearnAdap_Req_t_SaveReq_C      ((LearnAdap_Req_t)2U)\r\n#define LearnAdap_Req_t_InvalidReq_C   ((LearnAdap_Req_t)3U)\r\n#define LearnAdap_Req_t_InterruptReq_C ((LearnAdap_Req_t)4U)\r\n#define LearnAdap_Req_t_ClrReqHardStopOpen_C ((LearnAdap_Req_t)5U)\r\n#endif                                 /* RTW_HEADER_LearnAdp_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"MtrMdl_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: MtrMdl_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_MtrMdl_ExpTypes_h_\r\n#define RTW_HEADER_MtrMdl_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* bus object for Calculated Force and Torque\r\n\r\n */\r\ntypedef struct {\r\n  /* Calculated Force */\r\n  int16_T FcalcForce;\r\n\r\n  /* Calculated Torque(Newton-Metre) */\r\n  int16_T McalcTorque;\r\n} MtrMdl_CalcForTorqBus_t;\r\n\r\n#endif                                 /* RTW_HEADER_MtrMdl_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"NvM_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: NvM_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_NvM_ExpTypes_h_\r\n#define RTW_HEADER_NvM_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Request Command Type for Queue */\r\ntypedef uint8_T NVMMgmt_ReqCmdType;\r\n\r\n/* enum NVMMgmt_ReqCmdType */\r\n#define NVMMgmt_ReqCmdType_Void        ((NVMMgmt_ReqCmdType)0U)  /* Default value */\r\n#define NVMMgmt_ReqCmdType_Read        ((NVMMgmt_ReqCmdType)1U)\r\n#define NVMMgmt_ReqCmdType_Write       ((NVMMgmt_ReqCmdType)2U)\r\n#define NVMMgmt_ReqCmdType_Erase       ((NVMMgmt_ReqCmdType)3U)\r\n#define NVMMgmt_ReqCmdType_ReadSent    ((NVMMgmt_ReqCmdType)4U)\r\n#define NVMMgmt_ReqCmdType_WriteSent   ((NVMMgmt_ReqCmdType)5U)\r\n#define NVMMgmt_ReqCmdType_EraseSent   ((NVMMgmt_ReqCmdType)6U)\r\n#define NVMMgmt_ReqCmdType_ReadDone    ((NVMMgmt_ReqCmdType)7U)\r\n#define NVMMgmt_ReqCmdType_WriteDone   ((NVMMgmt_ReqCmdType)8U)\r\n#define NVMMgmt_ReqCmdType_EraseDone   ((NVMMgmt_ReqCmdType)9U)\r\n#define NVMMgmt_ReqCmdType_ReadError   ((NVMMgmt_ReqCmdType)10U)\r\n#define NVMMgmt_ReqCmdType_WriteError  ((NVMMgmt_ReqCmdType)11U)\r\n#define NVMMgmt_ReqCmdType_EraseError  ((NVMMgmt_ReqCmdType)12U)\r\n#endif                                 /* RTW_HEADER_NvM_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"PosMon_Exp.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: PosMon_Exp.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_PosMon_Exp_h_\r\n#define RTW_HEADER_PosMon_Exp_h_\r\n#include \"PosMon_ExpTypes.h\"\r\n\r\n/* Exported data declaration */\r\n/* Declaration for custom storage class: ExportToFile */\r\nextern PosMon_RP_AND_VLDTY_t PosMon_RP_AND_VLDTY;\r\n\r\n#endif                                 /* RTW_HEADER_PosMon_Exp_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"PosMon_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: PosMon_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_PosMon_ExpTypes_h_\r\n#define RTW_HEADER_PosMon_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\ntypedef uint8_T PosMon_Area_t;\r\n\r\n/* enum PosMon_Area_t */\r\n#define AreaSlide_C                    ((PosMon_Area_t)0U)       /* Default value */\r\n#define AreaVent_C                     ((PosMon_Area_t)1U)\r\n\r\n/* Panel Positions */\r\ntypedef uint8_T PosMon_PnlPosArea_t;\r\n\r\n/* enum PosMon_PnlPosArea_t */\r\n#define PosMon_PnlPosArea_t_PnlZero    ((PosMon_PnlPosArea_t)0U) /* Default value */\r\n#define PosMon_PnlPosArea_t_PnlFlushClose ((PosMon_PnlPosArea_t)1U)\r\n#define PosMon_PnlPosArea_t_PnlVentArea ((PosMon_PnlPosArea_t)2U)\r\n#define PosMon_PnlPosArea_t_PnlVentOpen ((PosMon_PnlPosArea_t)3U)\r\n#define PosMon_PnlPosArea_t_PnlComfOpnArea ((PosMon_PnlPosArea_t)4U)\r\n#define PosMon_PnlPosArea_t_PnlComfStop ((PosMon_PnlPosArea_t)5U)\r\n#define PosMon_PnlPosArea_t_PnlSlideOpnArea ((PosMon_PnlPosArea_t)6U)\r\n#define PosMon_PnlPosArea_t_PnlFullOpen ((PosMon_PnlPosArea_t)7U)\r\n#define PosMon_PnlPosArea_t_PnlOpenHS  ((PosMon_PnlPosArea_t)8U)\r\n#define PosMon_PnlPosArea_t_PnlOutOfRng ((PosMon_PnlPosArea_t)9U)\r\n\r\n/* Position and validity */\r\ntypedef struct {\r\n  /* current position of the rollo */\r\n  int16_T PosMon_hCurPosSigned;\r\n\r\n  /* Current position validity:\r\n     0 invalid\r\n     1: valid */\r\n  boolean_T PosMon_isCurPosVld;\r\n} PosMon_RP_AND_VLDTY_t;\r\n\r\n#endif                                 /* RTW_HEADER_PosMon_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"RefForceBus.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: RefForceBus.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_RefForceBus_h_\r\n#define RTW_HEADER_RefForceBus_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* bus object for roof model signals\r\n */\r\ntypedef struct {\r\n  /* Low Motor Speed Fault (Glass and Rollo)\r\n     0: Not Run\r\n     1: Run and Pass\r\n     2: Run and Fail */\r\n  int16_T RefForce_FreferenceForce;\r\n\r\n  /* Calculated Reference Force Validity (Glass and Rollo) */\r\n  boolean_T RefForce_isCalcRefForceVld;\r\n\r\n  /* Position Outside Reference Field flag */\r\n  boolean_T RefForce_isPosOutsideRefField;\r\n} RefForceBus;\r\n\r\n#endif                                 /* RTW_HEADER_RefForceBus_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"RoofOper_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: RoofOper_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_RoofOper_ExpTypes_h_\r\n#define RTW_HEADER_RoofOper_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Sunroof Moving Status Type */\r\ntypedef uint8_T CfgParam_NoAppParam_t;\r\n\r\n/* enum CfgParam_NoAppParam_t */\r\n#define CfgParam_NoAppParam_t_NoCmd_C  ((CfgParam_NoAppParam_t)0U) /* Default value */\r\n#define CfgParam_NoAppParam_t_StepMovCloseReq_C ((CfgParam_NoAppParam_t)1U)\r\n\r\n/* Sunroof Moving Status Type */\r\ntypedef uint8_T CfgParam_NoSyncParam_t;\r\n\r\n/* enum CfgParam_NoSyncParam_t */\r\n#define CfgParam_NoSyncParam_t_NoCmd_C ((CfgParam_NoSyncParam_t)0U) /* Default value */\r\n#define CfgParam_NoSyncParam_t_ManMov_C ((CfgParam_NoSyncParam_t)1U)\r\n#define CfgParam_NoSyncParam_t_ManCls_C ((CfgParam_NoSyncParam_t)2U)\r\n#define CfgParam_NoSyncParam_t_StepMov_C ((CfgParam_NoSyncParam_t)3U)\r\n#define CfgParam_NoSyncParam_t_StepMovCls_C ((CfgParam_NoSyncParam_t)4U)\r\n\r\n/* Sunroof ATS states type */\r\ntypedef uint8_T PnlOp_ATS_t;\r\n\r\n/* enum PnlOp_ATS_t */\r\n#define PnlOp_ATS_t_ATSDeactivated_C   ((PnlOp_ATS_t)0U)         /* Default value */\r\n#define PnlOp_ATS_t_ATSAvailable_C     ((PnlOp_ATS_t)1U)\r\n#define PnlOp_ATS_t_ATSActive_C        ((PnlOp_ATS_t)2U)\r\n#define PnlOp_ATS_t_ATSOverrideStage1_C ((PnlOp_ATS_t)3U)\r\n#define PnlOp_ATS_t_ATSOverrideStage2_C ((PnlOp_ATS_t)4U)\r\n#define PnlOp_ATS_t_ATSDisabled_C      ((PnlOp_ATS_t)5U)\r\n\r\n/* Sunroof Moving Status Type */\r\ntypedef uint8_T PnlOp_Mode_t;\r\n\r\n/* enum PnlOp_Mode_t */\r\n#define PnlOp_Mode_t_RoofIdle_C        ((PnlOp_Mode_t)0U)        /* Default value */\r\n#define PnlOp_Mode_t_Moving_C          ((PnlOp_Mode_t)1U)\r\n#define PnlOp_Mode_t_Intr_C            ((PnlOp_Mode_t)2U)\r\n#define PnlOp_Mode_t_ReachTarPos_C     ((PnlOp_Mode_t)3U)\r\n#define PnlOp_Mode_t_ReachStallPos_C   ((PnlOp_Mode_t)4U)\r\n#define PnlOp_Mode_t_Automatic_C       ((PnlOp_Mode_t)5U)\r\n\r\n/* Motor Control Command Status Type */\r\ntypedef uint8_T PnlOp_MtrCmd_t;\r\n\r\n/* enum PnlOp_MtrCmd_t */\r\n#define PnlOp_MtrCmd_t_CmdNone_C       ((PnlOp_MtrCmd_t)0U)      /* Default value */\r\n#define PnlOp_MtrCmd_t_CmdInc_C        ((PnlOp_MtrCmd_t)1U)\r\n#define PnlOp_MtrCmd_t_CmdDec_C        ((PnlOp_MtrCmd_t)2U)\r\n\r\n/* Sunroof Anti Pinch Reversal Status Type */\r\ntypedef uint8_T PnlOp_Rev_t;\r\n\r\n/* enum PnlOp_Rev_t */\r\n#define PnlOp_Rev_t_RevIdle_C          ((PnlOp_Rev_t)0U)         /* Default value */\r\n#define PnlOp_Rev_t_RevInProcATS_C     ((PnlOp_Rev_t)1U)\r\n#define PnlOp_Rev_t_RevInProcStall_C   ((PnlOp_Rev_t)2U)\r\n#define PnlOp_Rev_t_RevComplATS_C      ((PnlOp_Rev_t)3U)\r\n#define PnlOp_Rev_t_RevComplStall_C    ((PnlOp_Rev_t)4U)\r\n#define PnlOp_Rev_t_RevInhibted_C      ((PnlOp_Rev_t)5U)\r\n#endif                                 /* RTW_HEADER_RoofOper_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"RoofSys.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: RoofSys.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_RoofSys_h_\r\n#define RTW_HEADER_RoofSys_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Exported data define */\r\n/* Definition for custom storage class: Define */\r\n#define RoofSys_tSmpBlockDet_SC        5U                        /* BlockDet task Sample time\r\n                                                                  */\r\n#define RoofSys_tSmpCtrlLogic_SC       5U                        /* CtrlLogic task Sample time\r\n                                                                  */\r\n#define RoofSys_tSmpHallDeco_SC        5U                        /* HallDeco task Sample time\r\n                                                                  */\r\n#define RoofSys_tSmpMtrCtrl_SC         5U                        /* MtrCtrl task Sample time\r\n                                                                  */\r\n#define RoofSys_tSmpMtrMdl_SC          2U                        /* MtrMdl task Sample time\r\n                                                                  */\r\n#define RoofSys_tSmpPIDDID_SC          10U                       /* PIDDID task Sample time\r\n                                                                  */\r\n#define RoofSys_tSmpPnlOp_SC           5U                        /* Panel Operation Sample time\r\n                                                                  */\r\n#define RoofSys_tSmpPosMon_SC          5U                        /* PosMon task Sample time\r\n                                                                  */\r\n#endif                                 /* RTW_HEADER_RoofSys_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"RoofSys_CommDefines.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: RoofSys_CommDefines.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_RoofSys_CommDefines_h_\r\n#define RTW_HEADER_RoofSys_CommDefines_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Motor direction type  */\r\ntypedef uint8_T MtrCtrl_MtrDir_t;\r\n\r\n/* enum MtrCtrl_MtrDir_t */\r\n#define MtrCtrl_MtrDir_t_IDLE_C        ((MtrCtrl_MtrDir_t)0U)    /* Default value */\r\n#define MtrCtrl_MtrDir_t_CW_C          ((MtrCtrl_MtrDir_t)1U)\r\n#define MtrCtrl_MtrDir_t_CCW_C         ((MtrCtrl_MtrDir_t)2U)\r\n#define MtrCtrl_MtrDir_t_SECURE_C      ((MtrCtrl_MtrDir_t)3U)\r\n\r\n/* Motor direction type  */\r\ntypedef uint8_T MtrCtrl_MtrPwrd_t;\r\n\r\n/* enum MtrCtrl_MtrPwrd_t */\r\n#define MtrCtrl_MtrPwrd_t_IDLE_C       ((MtrCtrl_MtrPwrd_t)0U)   /* Default value */\r\n#define MtrCtrl_MtrPwrd_t_PWRD_C       ((MtrCtrl_MtrPwrd_t)1U)\r\n#define MtrCtrl_MtrPwrd_t_DEPWRD_C     ((MtrCtrl_MtrPwrd_t)2U)\r\n\r\n/* Motor direction type  */\r\ntypedef uint8_T PWMCtrl_MtrDir_t;\r\n\r\n/* enum PWMCtrl_MtrDir_t */\r\n#define PWMCtrl_MtrDir_t_IDLE_C        ((PWMCtrl_MtrDir_t)0U)    /* Default value */\r\n#define PWMCtrl_MtrDir_t_CW_C          ((PWMCtrl_MtrDir_t)1U)\r\n#define PWMCtrl_MtrDir_t_CCW_C         ((PWMCtrl_MtrDir_t)2U)\r\n#define PWMCtrl_MtrDir_t_SECURE_C      ((PWMCtrl_MtrDir_t)3U)\r\n#endif                                 /* RTW_HEADER_RoofSys_CommDefines_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"RoofSys_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: RoofSys_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_RoofSys_ExpTypes_h_\r\n#define RTW_HEADER_RoofSys_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Movement Type Definition */\r\ntypedef uint8_T CtrlLog_MovType_t;\r\n\r\n/* enum CtrlLog_MovType_t */\r\n#define CtrlLog_MovType_t_None_C       ((CtrlLog_MovType_t)0U)   /* Default value */\r\n#define CtrlLog_MovType_t_Manual_C     ((CtrlLog_MovType_t)1U)\r\n#define CtrlLog_MovType_t_Automatic_C  ((CtrlLog_MovType_t)2U)\r\n#define CtrlLog_MovType_t_Learning_C   ((CtrlLog_MovType_t)3U)\r\n#define CtrlLog_MovType_t_AtsReversal_C ((CtrlLog_MovType_t)4U)\r\n#define CtrlLog_MovType_t_RelaxOfMech_C ((CtrlLog_MovType_t)5U)\r\n#endif                                 /* RTW_HEADER_RoofSys_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"StateMach_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: StateMach_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_StateMach_ExpTypes_h_\r\n#define RTW_HEADER_StateMach_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Sunroof System Mode Type */\r\ntypedef uint8_T StateMach_Mode_t;\r\n\r\n/* enum StateMach_Mode_t */\r\n#define StateMach_Mode_t_Init_C        ((StateMach_Mode_t)0U)    /* Default value */\r\n#define StateMach_Mode_t_NvMInit_C     ((StateMach_Mode_t)1U)\r\n#define StateMach_Mode_t_Startup_C     ((StateMach_Mode_t)2U)\r\n#define StateMach_Mode_t_FullRun_C     ((StateMach_Mode_t)3U)\r\n#define StateMach_Mode_t_Standby_C     ((StateMach_Mode_t)4U)\r\n#define StateMach_Mode_t_Shtdwn_C      ((StateMach_Mode_t)5U)\r\n#define StateMach_Mode_t_FastShtdwn_C  ((StateMach_Mode_t)7U)\r\n#define StateMach_Mode_t_SystemReset_C ((StateMach_Mode_t)8U)\r\n#endif                                 /* RTW_HEADER_StateMach_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"ThermProt_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: ThermProt_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_ThermProt_ExpTypes_h_\r\n#define RTW_HEADER_ThermProt_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Mosfet Temperature Class Type Flag */\r\ntypedef uint8_T ThermProt_MosfetTempClass_t;\r\n\r\n/* enum ThermProt_MosfetTempClass_t */\r\n#define ThermProt_MosfetTempClass_t_Init_C ((ThermProt_MosfetTempClass_t)0U) /* Default value */\r\n#define ThermProt_MosfetTempClass_t_MosfetTempClassA_C ((ThermProt_MosfetTempClass_t)1U)\r\n#define ThermProt_MosfetTempClass_t_MosfetTempClassB_C ((ThermProt_MosfetTempClass_t)2U)\r\n#define ThermProt_MosfetTempClass_t_MosfetTempClassC_C ((ThermProt_MosfetTempClass_t)3U)\r\n#define ThermProt_MosfetTempClass_t_MosfetTempClassD_C ((ThermProt_MosfetTempClass_t)4U)\r\n#define ThermProt_MosfetTempClass_t_MosfetTempClassE_C ((ThermProt_MosfetTempClass_t)5U)\r\n\r\n/* Motor Temperature Class Type Flag */\r\ntypedef uint8_T ThermProt_MtrTempClass_t;\r\n\r\n/* enum ThermProt_MtrTempClass_t */\r\n#define ThermProt_MtrTempClass_t_Init_C ((ThermProt_MtrTempClass_t)0U) /* Default value */\r\n#define ThermProt_MtrTempClass_t_MtrTempClassA_C ((ThermProt_MtrTempClass_t)1U)\r\n#define ThermProt_MtrTempClass_t_MtrTempClassB_C ((ThermProt_MtrTempClass_t)2U)\r\n#define ThermProt_MtrTempClass_t_MtrTempClassC_C ((ThermProt_MtrTempClass_t)3U)\r\n#define ThermProt_MtrTempClass_t_MtrTempClassD_C ((ThermProt_MtrTempClass_t)4U)\r\n#define ThermProt_MtrTempClass_t_MtrTempClassE_C ((ThermProt_MtrTempClass_t)5U)\r\n#define ThermProt_MtrTempClass_t_MtrTempClassF_C ((ThermProt_MtrTempClass_t)6U)\r\n#endif                                 /* RTW_HEADER_ThermProt_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"VoltMon_ExpTypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: VoltMon_ExpTypes.h\r\n *\r\n * Code generated for Simulink model 'PosMon_Mdl'.\r\n *\r\n * Model version                  : 1.404\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:38:33 2025\r\n */\r\n\r\n#ifndef RTW_HEADER_VoltMon_ExpTypes_h_\r\n#define RTW_HEADER_VoltMon_ExpTypes_h_\r\n#include \"rtwtypes.h\"\r\n\r\n/* Voltage Class Type */\r\ntypedef uint8_T VoltMon_VoltClass_t;\r\n\r\n/* enum VoltMon_VoltClass_t */\r\n#define VoltMon_VoltClass_t_Init_C     ((VoltMon_VoltClass_t)0U) /* Default value */\r\n#define VoltMon_VoltClass_t_VoltClassA_C ((VoltMon_VoltClass_t)1U)\r\n#define VoltMon_VoltClass_t_VoltClassB_C ((VoltMon_VoltClass_t)2U)\r\n#define VoltMon_VoltClass_t_VoltClassC_C ((VoltMon_VoltClass_t)3U)\r\n#define VoltMon_VoltClass_t_VoltClassD_C ((VoltMon_VoltClass_t)4U)\r\n#define VoltMon_VoltClass_t_VoltClassE_C ((VoltMon_VoltClass_t)5U)\r\n#endif                                 /* RTW_HEADER_VoltMon_ExpTypes_h_ */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"rtwtypes.h","type":"header","group":"sharedutility","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\slprj\\ert\\_sharedutils","tag":"","groupDisplay":"Shared files","code":"/*\r\n * File: rtwtypes.h\r\n *\r\n * Code generated for Simulink model 'ADCMon_Mdl'.\r\n *\r\n * Model version                  : 1.183\r\n * Simulink Coder version         : 9.8 (R2022b) 13-May-2022\r\n * C/C++ source code generated on : Wed Jun  4 11:28:08 2025\r\n */\r\n\r\n#ifndef RTWTYPES_H\r\n#define RTWTYPES_H\r\n\r\n/* Logical type definitions */\r\n#if (!defined(__cplusplus))\r\n#ifndef false\r\n#define false                          (0U)\r\n#endif\r\n\r\n#ifndef true\r\n#define true                           (1U)\r\n#endif\r\n#endif\r\n\r\n/*=======================================================================*\r\n * Target hardware information\r\n *   Device type: NXP->Cortex-M0/M0+\r\n *   Number of bits:     char:   8    short:   16    int:  32\r\n *                       long:  32\r\n *                       native word size:  32\r\n *   Byte ordering: LittleEndian\r\n *   Signed integer division rounds to: Zero\r\n *   Shift right on a signed integer as arithmetic shift: on\r\n *=======================================================================*/\r\n\r\n/*=======================================================================*\r\n * Fixed width word size data types:                                     *\r\n *   int8_T, int16_T, int32_T     - signed 8, 16, or 32 bit integers     *\r\n *   uint8_T, uint16_T, uint32_T  - unsigned 8, 16, or 32 bit integers   *\r\n *=======================================================================*/\r\ntypedef signed char int8_T;\r\ntypedef unsigned char uint8_T;\r\ntypedef short int16_T;\r\ntypedef unsigned short uint16_T;\r\ntypedef int int32_T;\r\ntypedef unsigned int uint32_T;\r\n\r\n/*===========================================================================*\r\n * Generic type definitions: boolean_T, char_T, byte_T, int_T, uint_T,       *\r\n *                           ulong_T.                                        *\r\n *===========================================================================*/\r\ntypedef unsigned char boolean_T;\r\ntypedef int int_T;\r\ntypedef unsigned int uint_T;\r\ntypedef unsigned long ulong_T;\r\ntypedef char char_T;\r\ntypedef unsigned char uchar_T;\r\ntypedef char_T byte_T;\r\n\r\n/*=======================================================================*\r\n * Min and Max:                                                          *\r\n *   int8_T, int16_T, int32_T     - signed 8, 16, or 32 bit integers     *\r\n *   uint8_T, uint16_T, uint32_T  - unsigned 8, 16, or 32 bit integers   *\r\n *=======================================================================*/\r\n#define MAX_int8_T                     ((int8_T)(127))\r\n#define MIN_int8_T                     ((int8_T)(-128))\r\n#define MAX_uint8_T                    ((uint8_T)(255U))\r\n#define MAX_int16_T                    ((int16_T)(32767))\r\n#define MIN_int16_T                    ((int16_T)(-32768))\r\n#define MAX_uint16_T                   ((uint16_T)(65535U))\r\n#define MAX_int32_T                    ((int32_T)(2147483647))\r\n#define MIN_int32_T                    ((int32_T)(-2147483647-1))\r\n#define MAX_uint32_T                   ((uint32_T)(0xFFFFFFFFU))\r\n\r\n/* Block D-Work pointer type */\r\ntypedef void * pointer_T;\r\n\r\n#endif                                 /* RTWTYPES_H */\r\n\r\n/*\r\n * File trailer for generated code.\r\n *\r\n * [EOF]\r\n */\r\n"},{"name":"TaskSch_Man.c","type":"source","group":"legacy","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code","tag":"","groupDisplay":"Other files","code":"\r\n/*******************************************************************************\r\n*   File Name               :   TaskSch_Man.h\r\n*   Component Name          :   TaskScheduler\r\n*   UCom                    :   NXP S32K118 series\r\n*\r\n*   Create Date             :   2024-06-17\r\n*   Author                  :   M.H.KORTAS\r\n*   Corporation             :   Inalfa Roof Systems\r\n*\r\n*   Abstract Description    :   public header of example\r\n*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved\r\n*                               Unauthorized copying of this file, via any medium\r\n*                               is strictly prohibited\r\n*                               Proprietary and confidential\r\n*---Revision History------------------------------------------------------------\r\n*   Date        Author      Item     Description\r\n*   2024-06-17  M.H.KORTAS  [#26317]  Inital revision\r\n*******************************************************************************/\r\n/*******************************************************************************/\r\n/*    Debug Switch Section                                                     */\r\n/*******************************************************************************/\r\n//#define CPU_LOAD\r\n/*******************************************************************************\r\n*    Include File Section\r\n*******************************************************************************/\r\n#include \"TaskSch_Man.h\"\r\n#ifdef CPU_LOAD\r\n#include \"TimerHwAbs.h\"\r\n#endif\r\n/*******************************************************************************\r\n*    Local Macro Define Section\r\n*******************************************************************************/\r\n#define ZERO_COUNTER_VAL (0u)\r\n#define MAX_COUNTER_VAL (255u)\r\n#define INC_VAL   (1U)\r\n#define DEC_VAL   (1U)\r\n\r\n/*******************************************************************************\r\n*    Local Type Define Section\r\n*******************************************************************************/\r\n#ifdef CPU_LOAD\r\n#define MAX_TASK 35\r\nstatic uint16_t u16StartTimer[MAX_TASK];\r\nstatic uint16_t u16TaskTime[MAX_TASK];\r\nstatic uint16_t u16MaxTaskTime[MAX_TASK];\r\n#endif\r\n/*******************************************************************************\r\n*    Global Variable Define Section\r\n*******************************************************************************/\r\n\r\n\r\n/*******************************************************************************\r\n*    Local Variable Define Section\r\n*******************************************************************************/\r\nstatic uint8_t ST_u8_1msCntr = 0u;\r\n\r\n/******************************************************************************\r\n*    Local Function Prototype  Section\r\n******************************************************************************/\r\n\r\n/*******************************************************************************\r\n*    Local Function Define Section\r\n*******************************************************************************/\r\n\r\n/*******************************************************************************\r\n*    Global Function Define Section\r\n*******************************************************************************/\r\n\r\n\r\n/*----------------------------------------------------------------------------*/\r\n/** \r\n ** Function : TaskSch_u8Get1msCntr\r\n ** Function created to get and retunr the actual value of 1ms counter\r\n **\r\n ** @return    uint8_t\r\n **\r\n ** @param   None\r\n **/\r\n /*----------------------------------------------------------------------------*/\r\nuint8_t TaskSch_u8Get1msCntr(void)\r\n{\r\n    return (ST_u8_1msCntr);\r\n}\r\n\r\n/*----------------------------------------------------------------------------*/\r\n/** \r\n ** Function : TaskSch_vInc1msCntr\r\n ** Function created to increment the actual value of 1ms counter\r\n **\r\n ** @return  None\r\n **\r\n ** @param   None\r\n **/\r\n /*----------------------------------------------------------------------------*/\r\nvoid TaskSch_vInc1msCntr(void)\r\n{\r\n\tif (MAX_COUNTER_VAL != ST_u8_1msCntr){\r\n\t     ST_u8_1msCntr = ST_u8_1msCntr + INC_VAL;\r\n\t} else {\r\n\t\t/* do nothing */\r\n\t}\r\n}\r\n\r\n/*----------------------------------------------------------------------------*/\r\n/** \r\n ** Function : TaskSch_vDec1msCntr\r\n ** Function created to decrement the actual value of 1ms counter\r\n **\r\n ** @return  None  \r\n **\r\n ** @param   None\r\n **/\r\n /*----------------------------------------------------------------------------*/\r\nvoid TaskSch_vDec1msCntr(void)\r\n{\r\n\tif (ST_u8_1msCntr > ZERO_COUNTER_VAL){\r\n\t    ST_u8_1msCntr = ST_u8_1msCntr - DEC_VAL;\r\n\t} else {\r\n\t\t/* do nothing */\r\n\t}\r\n}\r\nvoid TaskSch_monitorTaskTimeStart(uint8_t u8task){\r\n#ifdef CPU_LOAD\r\n\tif(u8task < MAX_TASK)\r\n\t{\r\n\t\tu16StartTimer[u8task] = LPTMR_DRV_GetCounterValueByCount(INST_LPTMR_1);\r\n\t}\r\n#endif\r\n}\r\n\r\nvoid TaskSch_monitorTaskTimeEnd(uint8_t u8task){\r\n#ifdef CPU_LOAD\r\n\tif(u8task < MAX_TASK)\r\n\t{\r\n\t\tif(LPTMR_DRV_GetCounterValueByCount(INST_LPTMR_1) < u16StartTimer[u8task]){\r\n\t\t\tu16TaskTime[u8task] = LPTMR_DRV_GetCounterValueByCount(INST_LPTMR_1) + (UINT16_MAX - u16StartTimer[u8task]);\r\n\t\t}else{\r\n\t\t\tu16TaskTime[u8task] = LPTMR_DRV_GetCounterValueByCount(INST_LPTMR_1) - u16StartTimer[u8task];\r\n\t\t}\r\n\t}\r\n\r\n\tif(u16TaskTime[u8task] > u16MaxTaskTime[u8task]){\r\n\t\tu16MaxTaskTime[u8task] = u16TaskTime[u8task];\r\n\t}\r\n#endif\r\n}\r\n\r\nvoid TaskSch_vResetMaxValues(){\r\n#ifdef CPU_LOAD\r\n\tuint8_t u8Ctr;\r\n\tfor(u8Ctr = 0; u8Ctr < MAX_TASK; u8Ctr++){\r\n\t\tu16MaxTaskTime[u8Ctr] = 0;\r\n\t}\r\n#endif\r\n}\r\n"},{"name":"irs_lib.c","type":"source","group":"legacy","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\Common\\Code","tag":"","groupDisplay":"Other files","code":"/** @addtogroup  COMMON\r\n ** @{ */\r\n\r\n/** @file\r\n **\r\n ** Implementation of the IRS libs\r\n **/\r\n\r\n/*******************************************************************************\r\n*   File Name               :   irs_lib.c\r\n*   Component Name          :   COMMON\r\n*   UCom                    :   Freescale S12G series\r\n*\r\n*   Create Date             :   2017-06-15\r\n*   Author                  :   guanam\r\n*   Corporation             :   Inalfa Roof Systems\r\n*\r\n*   Abstract Description    :   Implementation of the IRS libs\r\n*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved\r\n*                               Unauthorized copying of this file, via any medium\r\n*                               is strictly prohibited\r\n*                               Proprietary and confidential\r\n*---Revision History------------------------------------------------------------\r\n*   Date        Author    Item      Description\r\n*   2021-06-17  guanam    [#13339]  Changed the len data type of DataSet function from uint8 to uint16 to fix the warning #69-D: integer conversion resulted in truncation\r\n*   2021-06-15  guanam    [#13339]  added DataSet function\r\n*   2020-01-23  guanam    [#nnnn]   change the return data type to fix the Matlab2019b code generation issue\r\n*   2018-02-03\tmourikma  [#3363]   added swapData\r\n*   2017-06-15  guanam    [#2554]   initial revision\r\n*******************************************************************************/\r\n\r\n/*******************************************************************************\r\n*   Debug Switch Section\r\n*******************************************************************************/\r\n\r\n/*******************************************************************************\r\n*   Include File Section\r\n*******************************************************************************/\r\n#include \"irs_lib.h\"\r\n\r\n/*******************************************************************************\r\n*   Local Macro Define Section\r\n*******************************************************************************/\r\n\r\n/*******************************************************************************\r\n*   Local Type Define Section\r\n*******************************************************************************/\r\n\r\n\r\n/*******************************************************************************\r\n*   Global Variable Define Section\r\n*******************************************************************************/\r\n\r\n\r\n/*******************************************************************************\r\n*   Local Variable Define Section\r\n*******************************************************************************/\r\n\r\n\r\n/******************************************************************************\r\n*   Local Function Prototype Section\r\n******************************************************************************/\r\n\r\n/*******************************************************************************\r\n*   Global Function Define Section\r\n*******************************************************************************/\r\nuint16 CRC16_CCITT(void *dataPtr,uint16 len)\r\n{\r\n\tuint8 x = 0;\r\n\tuint16 crc = (uint16)0xFFFF;\r\n\tuint16 index = 0;\r\n\tuint8 * dataPtrTemp=(uint8 *)dataPtr;\r\n\r\n\tindex = len;\r\n\tif(dataPtrTemp != (uint8 *)0x00)\r\n\t{\r\n\t\twhile (index--){\r\n\t\t\tx = crc >> 8 ^ *dataPtrTemp++;\r\n\t\t\tx ^= x>>4;\r\n\t\t\tcrc = (0xFFFF) &((crc << 8) ^ ((uint16)(x << 12)) ^ ((uint16)(x <<5)) ^ ((uint16)x));\r\n\t\t}\r\n\t}\r\n\telse\r\n\t{\r\n\t\tcrc = (uint16)0x00;\r\n\t}\r\n    return (uint16)crc;\r\n}\r\n\r\n/*----------------------------------------------------------------------------*/\r\nvoid DataCopy(void *dest, void *src,uint16 len)\r\n{\r\n   uint16 index = 0;\r\n   uint8 * destTemp=(uint8 *)dest;\r\n   uint8 * srcTemp=(uint8 *)src;\r\n\r\n    /* copy the data */\r\n   for(index = 0; index<len; index++)\r\n   {\r\n     destTemp[index] = srcTemp[index];\r\n   }\r\n}\r\n\r\n\r\n/*----------------------------------------------------------------------------*/\r\nvoid DataCopySelect(uint8 *dest,const uint8 *src,uint16 len, uint8 type)\r\n{\r\n   uint16 index = 0;\r\n   uint8 indexOffset = 0; //default only copy even index\r\n\r\n\tif((type == COPY_TYPE_ODD_FROM_SRC)||(type == COPY_TYPE_ODD_TO_DEST))\r\n\t{\r\n\t\t/* only copy odd index */\r\n\t\tindexOffset =1;\r\n\t}\r\n\t\r\n\tif((type == COPY_TYPE_ODD_FROM_SRC) ||(type == COPY_TYPE_EVEN_FROM_SRC))\r\n\t{\r\n\t   /* copy the data */\r\n\t   for(index = 0; index<len; index++)\r\n\t   {\r\n\t     dest[index] = src[index*2+indexOffset];\r\n\t   }\r\n\t}\r\n\telse if ((type == COPY_TYPE_ODD_TO_DEST) ||(type == COPY_TYPE_EVEN_TO_DEST)){\r\n\t\t/* copy the data */\r\n\t\tfor(index = 0; index<len; index++)\r\n\t\t{\r\n\t\t  dest[index*2+indexOffset] = src[index];\r\n\t\t}\r\n\t}\r\n}\r\n/*----------------------------------------------------------------------------*/\r\nvoid DataSwap(uint8 *arrayOne, uint8 *arrayTwo, uint16 len)\r\n{\r\n\tuint8 temp = 0;\r\n\tuint16 index = 0;\r\n\t\r\n\t/*Swap the data of the two arrays*/\r\n\tfor(index = 0; index<len; index=index+1)\r\n\t{\r\n\t\ttemp=arrayOne[index];\r\n\t\tarrayOne[index] = arrayTwo[index];\r\n\t\tarrayTwo[index] = temp;\r\n\t}\r\n}\r\n\r\n/*----------------------------------------------------------------------------*/\r\nvoid DataSet(uint8 *dataPtr, uint16 len, uint8 u8Value)\r\n{\r\n\tuint16 idx = 0;\r\n\r\n\t/* set the data */\r\n\tfor(idx = 0; idx < len; idx++)\r\n\t{\r\n\t\tdataPtr[idx] = u8Value;\r\n\t}\r\n}\r\n\r\n\r\n/**@} */\r\n"},{"name":"pins_driver.c","type":"source","group":"legacy","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\DriverLayer\\PINS\\Code","tag":"","groupDisplay":"Other files","code":"/*\r\n * Copyright (c) 2014 - 2015, Freescale Semiconductor, Inc.\r\n * Copyright 2016-2020 NXP\r\n * All rights reserved.\r\n *\r\n * NXP Confidential. This software is owned or controlled by NXP and may only be\r\n * used strictly in accordance with the applicable license terms. By expressly\r\n * accepting such terms or by downloading, installing, activating and/or otherwise\r\n * using the software, you are agreeing that you have read, and that you agree to\r\n * comply with and are bound by, such license terms. If you do not agree to be\r\n * bound by the applicable license terms, then you may not retain, install,\r\n * activate or otherwise use the software. The production use license in\r\n * Section 2.3 is expressly granted for this software.\r\n */\r\n/*!\r\n * @file pins_driver.c\r\n *\r\n * @page misra_violations MISRA-C:2012 violations\r\n *\r\n * @section [global]\r\n * Violates MISRA 2012 Advisory Rule 8.7, External could be made static.\r\n * Function is defined for usage by application code.\r\n *\r\n */\r\n\r\n#include \"device_registers.h\"\r\n#include \"pins_gpio_hw_access.h\"\r\n#if defined(FEATURE_PINS_DRIVER_USING_SIUL2)\r\n#include \"pins_siul2_hw_access.h\"\r\n#elif defined(FEATURE_PINS_DRIVER_USING_PORT)\r\n#include \"pins_port_hw_access.h\"\r\n#endif\r\n\r\n/*******************************************************************************\r\n * Definitions\r\n ******************************************************************************/\r\n\r\n/*******************************************************************************\r\n * Variables\r\n ******************************************************************************/\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_Init\r\n * Description   : This function configures the pins with the options provided\r\n * in the given structure.\r\n *\r\n * Implements    : PINS_DRV_Init_Activity\r\n *END**************************************************************************/\r\nstatus_t PINS_DRV_Init(uint32_t pinCount,\r\n                       const pin_settings_config_t config[])\r\n{\r\n    uint32_t i;\r\n    for (i = 0U; i < pinCount; i++)\r\n    {\r\n        PINS_Init(&config[i]);\r\n    }\r\n\r\n    return STATUS_SUCCESS;\r\n}\r\n\r\n#if defined(FEATURE_PINS_DRIVER_USING_PORT)\r\n#if FEATURE_PINS_HAS_PULL_SELECTION\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetPullSel\r\n * Description   : This function configures the internal resistor.\r\n *\r\n * Implements    : PINS_DRV_SetPullSel_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetPullSel(PORT_Type * const base,\r\n                         uint32_t pin,\r\n                         port_pull_config_t pullConfig)\r\n{\r\n    PINS_SetPullSel(base, pin, pullConfig);\r\n}\r\n\r\n#endif /* FEATURE_PINS_HAS_PULL_SELECTION */\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetMuxModeSel\r\n * Description   : This function configures the pin muxing.\r\n *\r\n * Implements    : PINS_DRV_SetMuxModeSel_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetMuxModeSel(PORT_Type * const base,\r\n                            uint32_t pin,\r\n                            port_mux_t mux)\r\n{\r\n    PINS_SetMuxModeSel(base, pin, mux);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetPinIntSel\r\n * Description   : This function configures the port pin interrupt/DMA request.\r\n *\r\n * Implements    : PINS_DRV_SetPinIntSel_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetPinIntSel(PORT_Type * const base,\r\n                           uint32_t pin,\r\n                           port_interrupt_config_t intConfig)\r\n{\r\n    PINS_SetPinIntSel(base, pin, intConfig);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetPinIntSel\r\n * Description   : This function gets the current port pin interrupt/DMA request configuration.\r\n *\r\n * Implements    : PINS_DRV_GetPinIntSel_Activity\r\n *END**************************************************************************/\r\nport_interrupt_config_t PINS_DRV_GetPinIntSel(const PORT_Type * const base,\r\n                                              uint32_t pin)\r\n{\r\n    return PINS_GetPinIntSel(base, pin);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ClearPinIntFlagCmd\r\n * Description   : This function clears the individual pin-interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_ClearPinIntFlagCmd_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ClearPinIntFlagCmd(PORT_Type * const base,\r\n                                 uint32_t pin)\r\n{\r\n    PINS_ClearPinIntFlagCmd(base, pin);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_EnableDigitalFilter\r\n * Description   : This function enables digital filter feature for digital pin muxing.\r\n *\r\n * Implements    : PINS_DRV_EnableDigitalFilter_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_EnableDigitalFilter(PORT_Type * const base,\r\n                                  uint32_t pin)\r\n{\r\n    PINS_EnableDigitalFilter(base, pin);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_DisableDigitalFilter\r\n * Description   : This function disables digital filter feature for digital\r\n * pin muxing.\r\n *\r\n * Implements    : PINS_DRV_DisableDigitalFilter_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_DisableDigitalFilter(PORT_Type * const base,\r\n                                   uint32_t pin)\r\n{\r\n    PINS_DisableDigitalFilter(base, pin);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ConfigDigitalFilter\r\n * Description   : This function configures digital filter for port with\r\n * given configuration.\r\n *\r\n * Implements    : PINS_DRV_ConfigDigitalFilter_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ConfigDigitalFilter(PORT_Type * const base,\r\n                                  const port_digital_filter_config_t * const config)\r\n{\r\n    PINS_ConfigDigitalFilter(base, config);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetPortIntFlag\r\n * Description   : This function reads the entire port interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_GetPortIntFlag_Activity\r\n *END**************************************************************************/\r\nuint32_t PINS_DRV_GetPortIntFlag(const PORT_Type * const base)\r\n{\r\n    return PINS_GetPortIntFlag(base);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ClearPortIntFlagCmd\r\n * Description   : This function clears the entire port interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_ClearPortIntFlagCmd_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ClearPortIntFlagCmd(PORT_Type * const base)\r\n{\r\n    PINS_ClearPortIntFlagCmd(base);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetPinsDirection\r\n * Description   : This function returns the current pins directions for a port. Pins\r\n * corresponding to bits with value of '1' are configured as output and\r\n * pins corresponding to bits with value of '0' are configured as input.\r\n *\r\n * Implements    : PINS_DRV_GetPinsDirection_Activity\r\n *END**************************************************************************/\r\npins_channel_type_t PINS_DRV_GetPinsDirection(const GPIO_Type * const base)\r\n{\r\n    return PINS_GPIO_GetPinsDirection(base);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetPinDirection\r\n * Description   : This function configures the direction for the given pin, with the\r\n * given value('1' for pin to be configured as output and '0' for pin to\r\n * be configured as input).\r\n *\r\n * Implements    : PINS_DRV_SetPinDirection_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetPinDirection(GPIO_Type * const base,\r\n                              pins_channel_type_t pin,\r\n                              pins_level_type_t direction)\r\n{\r\n    PINS_GPIO_SetPinDirection(base, pin, direction);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetPinsDirection\r\n * Description   : This function sets the direction configuration for all pins\r\n * in a port. Pins corresponding to bits with value of '1' will be configured as\r\n * output and pins corresponding to bits with value of '0' will be configured as\r\n * input.\r\n *\r\n * Implements    : PINS_DRV_SetPinsDirection_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetPinsDirection(GPIO_Type * const base,\r\n                               pins_channel_type_t pins)\r\n{\r\n    PINS_GPIO_SetPinsDirection(base, pins);\r\n}\r\n\r\n#if FEATURE_PORT_HAS_INPUT_DISABLE\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetPortInputDisable\r\n * Description   : This function sets the pins input state for a port.\r\n * Pins corresponding to bits with value of '1' will not be configured\r\n * as input and pins corresponding to bits with value of '0' will be configured\r\n * as input.\r\n *\r\n * Implements    : PINS_DRV_SetPortInputDisable_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetPortInputDisable(GPIO_Type * const base,\r\n                                  pins_channel_type_t pins)\r\n{\r\n    PINS_GPIO_SetPortInputDisable(base, pins);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetPortInputDisable\r\n * Description   : This function returns the current pins input state for a port. Pins\r\n * corresponding to bits with value of '1' are not configured as input and\r\n * pins corresponding to bits with value of '0' are configured as input.\r\n *\r\n * Implements    : PINS_DRV_GetPortInputDisable_Activity\r\n *END**************************************************************************/\r\npins_channel_type_t PINS_DRV_GetPortInputDisable(const GPIO_Type * const base)\r\n{\r\n    return PINS_GPIO_GetPortInputDisable(base);\r\n}\r\n#endif /* FEATURE_PORT_HAS_INPUT_DISABLE */\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetGlobalPinControl\r\n * Description   : This function quickly configures multiple pins within the one port for\r\n * the same peripheral function with the same pin configuration. Supports up to 16 pins with\r\n * the lower or upper half of pin registers at the same port.\r\n *\r\n * Implements    : PINS_DRV_SetGlobalPinControl_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetGlobalPinControl(PORT_Type * const base,\r\n                                  uint16_t pins,\r\n                                  uint16_t value,\r\n                                  port_global_control_pins_t halfPort)\r\n{\r\n    PINS_SetGlobalPinControl(base, pins, value, halfPort);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetGlobalIntControl\r\n * Description   : This function quickly configures multiple pins within the one port for\r\n * the same peripheral function with the same interrupt configuration. Supports up to 16 pins with\r\n * the lower or upper half of pin registers at the same port.\r\n *\r\n * Implements    : PINS_DRV_SetGlobalIntControl_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetGlobalIntControl(PORT_Type * const base,\r\n                                  uint16_t pins,\r\n                                  uint16_t value,\r\n                                  port_global_control_pins_t halfPort)\r\n{\r\n    PINS_SetGlobalIntControl(base, pins, value, halfPort);\r\n}\r\n\r\n#if FEATURE_PINS_HAS_OVER_CURRENT\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetOverCurPortIntFlag\r\n * Description   : This function reads the entire over current port interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_GetOverCurPortIntFlag_Activity\r\n *END**************************************************************************/\r\nuint32_t PINS_DRV_GetOverCurPortIntFlag(const PORT_Type * const base)\r\n{\r\n    return PINS_GetOverCurPortIntFlag(base);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ClearOverCurPortIntFlag\r\n * Description   : This function clears the entire over current port interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_ClearOverCurPortIntFlag_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ClearOverCurPortIntFlag(PORT_Type * const base)\r\n{\r\n    PINS_ClearOverCurPortIntFlag(base);\r\n}\r\n#endif /* FEATURE_PINS_HAS_OVER_CURRENT */\r\n\r\n#elif defined(FEATURE_PINS_DRIVER_USING_SIUL2)\r\n#if FEATURE_PINS_HAS_PULL_SELECTION\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetPullSel\r\n * Description   : This function configures the internal resistor.\r\n *\r\n * Implements    : PINS_DRV_SetPullSel_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetPullSel(PORT_Type * const base,\r\n                         uint16_t pin,\r\n                         port_pull_config_t pullConfig)\r\n{\r\n    PINS_SetPullSel(base, pin, pullConfig);\r\n}\r\n\r\n#endif /* FEATURE_PINS_HAS_PULL_SELECTION */\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetOutputBuffer\r\n * Description   : This function configures the output buffer.\r\n *\r\n * Implements    : PINS_DRV_SetOutputBuffer_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetOutputBuffer(PORT_Type * const base,\r\n                              uint16_t pin,\r\n                              bool enable,\r\n                              port_mux_t mux)\r\n{\r\n    PINS_SetOutputBuffer(base, pin, enable, mux);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetInputBuffer\r\n * Description   : This function configures the input buffer.\r\n *\r\n * Implements    : PINS_DRV_SetInputBuffer_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetInputBuffer(PORT_Type * const base,\r\n                             uint16_t pin,\r\n                             bool enable,\r\n                             uint32_t inputMuxReg,\r\n                             port_input_mux_t inputMux)\r\n{\r\n    PINS_SetInputBuffer(base, pin, enable, inputMuxReg, inputMux);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ConfigIntFilterClock\r\n * Description   : This function configures the interrupt filter clock prescaler.\r\n *\r\n * Implements    : PINS_DRV_ConfigIntFilterClock_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ConfigIntFilterClock(uint8_t prescaler)\r\n{\r\n    PINS_ConfigIntFilterClock(prescaler);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetExInt\r\n * Description   : This function configures the external interrupt.\r\n *\r\n * Implements    : PINS_DRV_SetExInt_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetExInt(siul2_interrupt_config_t intConfig)\r\n{\r\n    PINS_SetExInt(intConfig);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ClearPinExIntFlag\r\n * Description   : This function clears the individual pin external interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_ClearPinExIntFlag_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ClearPinExIntFlag(uint32_t eirqPinIdx)\r\n{\r\n    PINS_ClearPinExIntFlag(eirqPinIdx);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetPinExIntFlag\r\n * Description   : This function gets the individual pin external interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_GetPinExIntFlag_Activity\r\n *END**************************************************************************/\r\nbool PINS_DRV_GetPinExIntFlag(uint32_t eirqPinIdx)\r\n{\r\n    return PINS_GetPinExIntFlag(eirqPinIdx);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ClearExIntFlag\r\n * Description   : This function clears the entire external interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_ClearExIntFlag_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ClearExIntFlag(void)\r\n{\r\n    PINS_ClearExIntFlag();\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetExIntFlag\r\n * Description   : This function reads the entire external interrupt status flag.\r\n *\r\n * Implements    : PINS_DRV_GetExIntFlag_Activity\r\n *END**************************************************************************/\r\nuint32_t PINS_DRV_GetExIntFlag(void)\r\n{\r\n    return PINS_GetExIntFlag();\r\n}\r\n\r\n#endif /* FEATURE_PINS_DRIVER_USING_PORT */\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_WritePin\r\n * Description   : This function writes the given pin from a port, with the given value\r\n * ('0' represents LOW, '1' represents HIGH).\r\n *\r\n * Implements    : PINS_DRV_WritePin_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_WritePin(GPIO_Type * const base,\r\n                       pins_channel_type_t pin,\r\n                       pins_level_type_t value)\r\n{\r\n    PINS_GPIO_WritePin(base, pin, value);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_WritePins\r\n * Description   : This function writes all pins configured as output with the values given in\r\n * the parameter pins. '0' represents LOW, '1' represents HIGH.\r\n *\r\n * Implements    : PINS_DRV_WritePins_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_WritePins(GPIO_Type * const base,\r\n                        pins_channel_type_t pins)\r\n{\r\n    PINS_GPIO_WritePins(base, pins);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_GetPinsOutput\r\n * Description   : This function returns the current output that is written to a port. Only pins\r\n * that are configured as output will have meaningful values.\r\n *\r\n * Implements    : PINS_DRV_GetPinsOutput_Activity\r\n *END**************************************************************************/\r\npins_channel_type_t PINS_DRV_GetPinsOutput(const GPIO_Type * const base)\r\n{\r\n    return PINS_GPIO_GetPinsOutput(base);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_SetPins\r\n * Description   : This function configures output pins listed in parameter pins (bits that are\r\n * '1') to have a value of 'set' (HIGH). Pins corresponding to '0' will be\r\n * unaffected.\r\n *\r\n * Implements    : PINS_DRV_SetPins_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_SetPins(GPIO_Type * const base,\r\n                      pins_channel_type_t pins)\r\n{\r\n    PINS_GPIO_SetPins(base, pins);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ClearPins\r\n * Description   : This function configures output pins listed in parameter pins (bits that are\r\n * '1') to have a 'cleared' value (LOW). Pins corresponding to '0' will be\r\n * unaffected.\r\n *\r\n * Implements    : PINS_DRV_ClearPins_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_ClearPins(GPIO_Type * const base,\r\n                        pins_channel_type_t pins)\r\n{\r\n    PINS_GPIO_ClearPins(base, pins);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_TogglePins\r\n * Description   : This function toggles output pins listed in parameter pins (bits that are\r\n * '1'). Pins corresponding to '0' will be unaffected.\r\n *\r\n * Implements    : PINS_DRV_TogglePins_Activity\r\n *END**************************************************************************/\r\nvoid PINS_DRV_TogglePins(GPIO_Type * const base,\r\n                         pins_channel_type_t pins)\r\n{\r\n    PINS_GPIO_TogglePins(base, pins);\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_DRV_ReadPins\r\n * Description   : This function returns the current input values from a port. Only pins\r\n * configured as input will have meaningful values.\r\n *\r\n * Implements    : PINS_DRV_ReadPins_Activity\r\n *END**************************************************************************/\r\npins_channel_type_t PINS_DRV_ReadPins(const GPIO_Type * const base)\r\n{\r\n    return PINS_GPIO_ReadPins(base);\r\n}\r\n\r\n/******************************************************************************\r\n * EOF\r\n *****************************************************************************/\r\n"},{"name":"pins_port_hw_access.c","type":"source","group":"legacy","path":"C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\DriverLayer\\PINS\\Code","tag":"","groupDisplay":"Other files","code":"/*\r\n * Copyright 2017-2020 NXP\r\n * All rights reserved.\r\n *\r\n * NXP Confidential. This software is owned or controlled by NXP and may only be\r\n * used strictly in accordance with the applicable license terms. By expressly\r\n * accepting such terms or by downloading, installing, activating and/or otherwise\r\n * using the software, you are agreeing that you have read, and that you agree to\r\n * comply with and are bound by, such license terms. If you do not agree to be\r\n * bound by the applicable license terms, then you may not retain, install,\r\n * activate or otherwise use the software. The production use license in\r\n * Section 2.3 is expressly granted for this software.\r\n */\r\n\r\n#include \"pins_port_hw_access.h\"\r\n#include \"pins_gpio_hw_access.h\"\r\n\r\n/**\r\n * @page misra_violations MISRA-C:2012 violations\r\n *\r\n * @section [global]\r\n * Violates MISRA 2012 Advisory Rule 8.7, External could be made static.\r\n * Function is defined for usage by application code.\r\n *\r\n * @section [global]\r\n * Violates MISRA 2012 Advisory Rule 10.5, Impermissible cast; cannot cast from 'essentially Boolean'\r\n * to 'essentially unsigned'. This is required by the conversion of a bool into a bit.\r\n * Impermissible cast; cannot cast from 'essentially unsigned' to 'essentially enum<i>'.\r\n * This is required by the conversion of a bitfield of a register into a enum.\r\n *\r\n * @section [global]\r\n * Violates MISRA 2012 Advisory Rule 2.3, Global typedef not referenced.\r\n * The enumeration structure is used by user to enable or disable adc interleved channel.\r\n *\r\n * @section [global]\r\n * Violates MISRA 2012 Advisory Rule 11.4, Conversion between a pointer and\r\n * integer type.\r\n * The cast is required to initialize a pointer with an unsigned long define,\r\n * representing an address.\r\n *\r\n * @section [global]\r\n * Violates MISRA 2012 Required Rule 11.6, Cast from unsigned int to pointer.\r\n * The cast is required to initialize a pointer with an unsigned long define,\r\n * representing an address.\r\n *\r\n */\r\n\r\n#if FEATURE_SOC_PORT_COUNT > 0\r\n\r\n/*******************************************************************************\r\n * Code\r\n ******************************************************************************/\r\n\r\n#if FEATURE_PINS_HAS_ADC_INTERLEAVE_EN\r\n/*!\r\n * @brief ADC Interleave muxing selection\r\n */\r\ntypedef enum\r\n{\r\n    PIN_ADC_INTERLEAVE_DISABLE0    = 0xEu,   /*!< xxx0b ADC1_SE14 channel is connected to PTB15 */\r\n    PIN_ADC_INTERLEAVE_DISABLE1    = 0xDu,   /*!< xx0xb ADC1_SE15 channel is connected to PTB16 */\r\n    PIN_ADC_INTERLEAVE_DISABLE2    = 0xBu,   /*!< x0xxb ADC0_SE8  channel is connected to PTC0  */\r\n    PIN_ADC_INTERLEAVE_DISABLE3    = 0x7u,   /*!< 0xxxb ADC0_SE9  channel is connected to PTC1  */\r\n    PIN_ADC_INTERLEAVE_ENABLE0     = 0x1u,   /*!< xxx1b ADC1_SE14 channel is connected to PTB0  */\r\n    PIN_ADC_INTERLEAVE_ENABLE1     = 0x2u,   /*!< xx1xb ADC1_SE15 channel is connected to PTB1  */\r\n    PIN_ADC_INTERLEAVE_ENABLE2     = 0x4u,   /*!< x1xxb ADC0_SE8  channel is connected to PTB13 */\r\n    PIN_ADC_INTERLEAVE_ENABLE3     = 0x8u,   /*!< 1xxxb ADC0_SE9  channel is connected to PTB14 */\r\n    PIN_ADC_INTERLEAVE_INVALID     = 0xFFu   /*!< ADC interleave is invalid                     */\r\n} pin_adc_interleave_mux_t;\r\n\r\nstatic uint32_t PINS_GetAdcInterleaveVal(const PORT_Type * base,\r\n                                         const uint32_t pinPortIdx,\r\n                                         const uint32_t currentVal)\r\n{\r\n    uint32_t adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_INVALID;\r\n    /* calculate appropriate value to enable or disable in SIM_CHIPCTL[ADC_INTERLEAVE_EN] */\r\n    if ((uint32_t)base == (uint32_t)PORTB)\r\n    {\r\n        switch (pinPortIdx)\r\n        {\r\n            case 0:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_ENABLE0 | currentVal;\r\n                break;\r\n            case 1:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_ENABLE1 | currentVal;\r\n                break;\r\n            case 13:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_ENABLE2 | currentVal;\r\n                break;\r\n            case 14:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_ENABLE3 | currentVal;\r\n                break;\r\n            case 15:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_DISABLE0 & currentVal;\r\n                break;\r\n            case 16:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_DISABLE1 & currentVal;\r\n                break;\r\n            default:\r\n                /* invalid command */\r\n                break;\r\n        }\r\n    }\r\n    else if ((uint32_t)base == (uint32_t)PORTC)\r\n    {\r\n        switch (pinPortIdx)\r\n        {\r\n            case 0:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_DISABLE2 & currentVal;\r\n                break;\r\n            case 1:\r\n                adcInterleaveVal = (uint32_t)PIN_ADC_INTERLEAVE_DISABLE3 & currentVal;\r\n                break;\r\n            default:\r\n                /* invalid command */\r\n                break;\r\n        }\r\n    }\r\n    else\r\n    {\r\n        /* invalid command */\r\n    }\r\n    return adcInterleaveVal;\r\n}\r\n#endif /* FEATURE_PINS_HAS_ADC_INTERLEAVE_EN */\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_Init\r\n * Description   : This function configures the pins with the options provided\r\n * in the provided structure.\r\n *\r\n *END**************************************************************************/\r\nvoid PINS_Init(const pin_settings_config_t * config)\r\n{\r\n    DEV_ASSERT(config->base != NULL);\r\n    DEV_ASSERT((PORT_MUX_AS_GPIO != config->mux) || (config->gpioBase != NULL));\r\n    DEV_ASSERT(config->pinPortIdx < PORT_PCR_COUNT);\r\n    uint32_t regValue = config->base->PCR[config->pinPortIdx];\r\n    uint32_t directions;\r\n    uint32_t digitalFilters;\r\n    port_mux_t muxing;\r\n\r\n#if FEATURE_PINS_HAS_PULL_SELECTION\r\n    switch (config->pullConfig)\r\n    {\r\n        case PORT_INTERNAL_PULL_NOT_ENABLED:\r\n            {\r\n                regValue &= ~(PORT_PCR_PE_MASK);\r\n            }\r\n            break;\r\n        case PORT_INTERNAL_PULL_DOWN_ENABLED:\r\n            {\r\n                regValue &= ~(PORT_PCR_PS_MASK);\r\n                regValue |= PORT_PCR_PE(1U);\r\n            }\r\n            break;\r\n        case PORT_INTERNAL_PULL_UP_ENABLED:\r\n            {\r\n                regValue |= PORT_PCR_PE(1U);\r\n                regValue |= PORT_PCR_PS(1U);\r\n            }\r\n            break;\r\n        default:\r\n            /* invalid command */\r\n            DEV_ASSERT(false);\r\n            break;\r\n    }\r\n#endif /* FEATURE_PINS_HAS_PULL_SELECTION */\r\n#if FEATURE_PINS_HAS_OVER_CURRENT\r\n    switch (config->overCurConfig)\r\n    {\r\n        case PORT_OVER_CURRENT_DISABLED:\r\n            {\r\n                regValue &= ~(PORT_PCR_OCE_MASK);\r\n            }\r\n            break;\r\n        case PORT_OVER_CURRENT_INT_DISABLED:\r\n            {\r\n                regValue &= ~(PORT_PCR_OCIE_MASK);\r\n                regValue |= PORT_PCR_OCE_MASK;\r\n            }\r\n            break;\r\n        case PORT_OVER_CURRENT_INT_ENABLED:\r\n            {\r\n                regValue |= PORT_PCR_OCIE_MASK;\r\n                regValue |= PORT_PCR_OCE_MASK;\r\n            }\r\n            break;\r\n        default:\r\n            /* invalid command */\r\n            DEV_ASSERT(false);\r\n            break;\r\n    }\r\n    if (config->clearOCurFlag)\r\n    {\r\n        regValue &= ~(PORT_PCR_OCF_MASK);\r\n        regValue |= PORT_PCR_OCF(1U);\r\n    }\r\n#endif /* FEATURE_PINS_HAS_OVER_CURRENT */\r\n#if FEATURE_PINS_HAS_SLEW_RATE\r\n    regValue &= ~(PORT_PCR_SRE_MASK);\r\n    regValue |= PORT_PCR_SRE(config->rateSelect);\r\n#endif\r\n#if FEATURE_PORT_HAS_PASSIVE_FILTER\r\n    regValue &= ~(PORT_PCR_PFE_MASK);\r\n    regValue |= PORT_PCR_PFE(config->passiveFilter);\r\n#endif\r\n#if FEATURE_PINS_HAS_OPEN_DRAIN\r\n    regValue &= ~(PORT_PCR_ODE_MASK);\r\n    regValue |= PORT_PCR_ODE(config->openDrain);\r\n#endif\r\n#if FEATURE_PINS_HAS_DRIVE_STRENGTH\r\n    regValue &= ~(PORT_PCR_DSE_MASK);\r\n    regValue |= PORT_PCR_DSE(config->driveSelect);\r\n#endif\r\n    regValue &= ~(PORT_PCR_MUX_MASK);\r\n    muxing = config->mux;\r\n#if FEATURE_PINS_HAS_ADC_INTERLEAVE_EN\r\n    if (muxing == PORT_MUX_ADC_INTERLEAVE)\r\n    {\r\n        /* Get ADC Interleave from SIM and enable/disable desired bit */\r\n        uint32_t chipCtlReg = (SIM->CHIPCTL & SIM_CHIPCTL_ADC_INTERLEAVE_EN_MASK) >> SIM_CHIPCTL_ADC_INTERLEAVE_EN_SHIFT;\r\n        uint32_t interleaveVal = PINS_GetAdcInterleaveVal(config->base, config->pinPortIdx, chipCtlReg);\r\n        if (interleaveVal != (uint32_t)PIN_ADC_INTERLEAVE_INVALID)\r\n        {\r\n            SIM->CHIPCTL &= ~(SIM_CHIPCTL_ADC_INTERLEAVE_EN_MASK);\r\n            SIM->CHIPCTL |= SIM_CHIPCTL_ADC_INTERLEAVE_EN(interleaveVal);\r\n        }\r\n        /* return real muxing for pin */\r\n        muxing = PORT_PIN_DISABLED;\r\n    }\r\n#endif\r\n    regValue |= PORT_PCR_MUX(muxing);\r\n#if FEATURE_PORT_HAS_PIN_CONTROL_LOCK\r\n    regValue &= ~(PORT_PCR_LK_MASK);\r\n    regValue |= PORT_PCR_LK(config->pinLock);\r\n#endif\r\n    regValue &= ~(PORT_PCR_IRQC_MASK);\r\n    regValue |= PORT_PCR_IRQC(config->intConfig);\r\n    if (config->clearIntFlag)\r\n    {\r\n        regValue &= ~(PORT_PCR_ISF_MASK);\r\n        regValue |= PORT_PCR_ISF(1U);\r\n    }\r\n\r\n    config->base->PCR[config->pinPortIdx] = regValue;\r\n\r\n    /* Read current digital filter of port */\r\n    digitalFilters = (uint32_t)(config->base->DFER);\r\n    digitalFilters &= ~(1UL << (config->pinPortIdx));\r\n    digitalFilters |= (((uint32_t)(config->digitalFilter)) << (config->pinPortIdx));\r\n    /* Write to digital filter enable register */\r\n    config->base->DFER = digitalFilters;\r\n\r\n    /* If gpioBase address not null setup the direction of pin */\r\n    if (PORT_MUX_AS_GPIO == config->mux)\r\n    {\r\n        /* Read current direction */\r\n        directions = (uint32_t)(config->gpioBase->PDDR);\r\n        switch (config->direction)\r\n        {\r\n            case GPIO_INPUT_DIRECTION:\r\n                directions &= ~(1UL << config->pinPortIdx);\r\n                break;\r\n            case GPIO_OUTPUT_DIRECTION:\r\n                directions |= (1UL << config->pinPortIdx);\r\n                break;\r\n            case GPIO_UNSPECIFIED_DIRECTION:\r\n            /* pass-through */\r\n            default:\r\n                /* nothing to configure */\r\n                DEV_ASSERT(false);\r\n                break;\r\n        }\r\n\r\n        /* Configure initial value for output */\r\n        if (config->direction == GPIO_OUTPUT_DIRECTION)\r\n        {\r\n            PINS_GPIO_WritePin(config->gpioBase, config->pinPortIdx, config->initValue);\r\n        }\r\n\r\n        /* Configure direction */\r\n        config->gpioBase->PDDR = GPIO_PDDR_PDD(directions);\r\n    }\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_SetMuxModeSel\r\n * Description   : This function configures the pin muxing and support configuring\r\n * for the pins that have ADC interleaved channel as well.\r\n *\r\n *END**************************************************************************/\r\nvoid PINS_SetMuxModeSel(PORT_Type * const base,\r\n                        uint32_t pin,\r\n                        port_mux_t mux)\r\n{\r\n    DEV_ASSERT(pin < PORT_PCR_COUNT);\r\n    uint32_t regValue = base->PCR[pin];\r\n    port_mux_t muxing = mux;\r\n\r\n#if FEATURE_PINS_HAS_ADC_INTERLEAVE_EN\r\n    if (muxing == PORT_MUX_ADC_INTERLEAVE)\r\n    {\r\n        /* Get ADC Interleave from SIM and enable/disable desired bit */\r\n        uint32_t chipCtlReg = (SIM->CHIPCTL & SIM_CHIPCTL_ADC_INTERLEAVE_EN_MASK) >> SIM_CHIPCTL_ADC_INTERLEAVE_EN_SHIFT;\r\n        uint32_t interleaveVal = PINS_GetAdcInterleaveVal(base, pin, chipCtlReg);\r\n        if (interleaveVal != (uint32_t)PIN_ADC_INTERLEAVE_INVALID)\r\n        {\r\n            SIM->CHIPCTL &= ~(SIM_CHIPCTL_ADC_INTERLEAVE_EN_MASK);\r\n            SIM->CHIPCTL |= SIM_CHIPCTL_ADC_INTERLEAVE_EN(interleaveVal);\r\n        }\r\n        /* return real muxing for pin */\r\n        muxing = PORT_PIN_DISABLED;\r\n    }\r\n#endif\r\n    regValue &= ~(PORT_PCR_MUX_MASK);\r\n    regValue |= PORT_PCR_MUX(muxing);\r\n    base->PCR[pin] = regValue;\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_SetGlobalPinControl\r\n * Description   : Quickly configures multiple pins with the same pin configuration.\r\n *\r\n *END**************************************************************************/\r\nvoid PINS_SetGlobalPinControl(PORT_Type * const base,\r\n                              uint16_t pins,\r\n                              uint16_t value,\r\n                              port_global_control_pins_t halfPort)\r\n{\r\n    uint16_t mask = 0;\r\n    /* keep only available fields */\r\n    mask |= PORT_PCR_PS_MASK;\r\n    mask |= PORT_PCR_PE_MASK;\r\n#if FEATURE_PINS_HAS_SLEW_RATE\r\n    mask |= PORT_PCR_SRE_MASK;\r\n#endif /* FEATURE_PINS_HAS_OPEN_DRAIN */\r\n    mask |= PORT_PCR_PFE_MASK;\r\n#if FEATURE_PINS_HAS_OPEN_DRAIN\r\n    mask |= PORT_PCR_ODE_MASK;\r\n#endif /* FEATURE_PINS_HAS_OPEN_DRAIN */\r\n    mask |= PORT_PCR_DSE_MASK;\r\n    mask |= PORT_PCR_MUX_MASK;\r\n#if FEATURE_PINS_HAS_OVER_CURRENT\r\n    mask |= PORT_PCR_OCE_MASK;\r\n#endif /* FEATURE_PINS_HAS_OVER_CURRENT */\r\n    mask |= PORT_PCR_LK_MASK;\r\n    mask &= value;\r\n\r\n    switch (halfPort)\r\n    {\r\n        case PORT_GLOBAL_CONTROL_LOWER_HALF_PINS:\r\n            base->GPCLR = (((uint32_t)pins) << PORT_GPCLR_GPWE_SHIFT) | (uint32_t)mask;\r\n            break;\r\n        case PORT_GLOBAL_CONTROL_UPPER_HALF_PINS:\r\n            base->GPCHR = (((uint32_t)pins) << PORT_GPCLR_GPWE_SHIFT) | (uint32_t)mask;\r\n            break;\r\n        default:\r\n            /* nothing to configure */\r\n            DEV_ASSERT(false);\r\n            break;\r\n    }\r\n}\r\n\r\n/*FUNCTION**********************************************************************\r\n *\r\n * Function Name : PINS_SetGlobalIntControl\r\n * Description   : Quickly configures multiple pins with the same interrupt configuration.\r\n *\r\n *END**************************************************************************/\r\nvoid PINS_SetGlobalIntControl(PORT_Type * const base,\r\n                              uint16_t pins,\r\n                              uint16_t value,\r\n                              port_global_control_pins_t halfPort)\r\n{\r\n    uint32_t mask;\r\n    mask = (((uint32_t)value) << PORT_GICLR_GIWD_SHIFT) & PORT_PCR_IRQC_MASK;\r\n\r\n    switch (halfPort)\r\n    {\r\n        case PORT_GLOBAL_CONTROL_LOWER_HALF_PINS:\r\n            base->GICLR = ((uint32_t)pins) | mask;\r\n            break;\r\n        case PORT_GLOBAL_CONTROL_UPPER_HALF_PINS:\r\n            base->GICHR = ((uint32_t)pins) | mask;\r\n            break;\r\n        default:\r\n            /* nothing to configure */\r\n            DEV_ASSERT(false);\r\n            break;\r\n    }\r\n}\r\n\r\n#endif /* FEATURE_SOC_PORT_COUNT */\r\n/*******************************************************************************\r\n * EOF\r\n ******************************************************************************/\r\n"}],"coverage":[{"id":"SimulinkCoverage","name":"Simulink Coverage","files":[]},{"id":"Bullseye","name":"Bullseye Coverage","files":[]},{"id":"LDRA","name":"LDRA Testbed","files":[]}],"features":{"annotation":false,"coverage":true,"profiling":true,"tooltip":true,"coverageTooltip":false,"showJustificationLinks":false,"showProfilingInfo":false,"showTaskSummary":false}};