/*
 * File: UsgHis_Mdl.c
 *
 * Code generated for Simulink model 'UsgHis_Mdl'.
 *
 * Model version                  : 1.442
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:42:03 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "UsgHis_Mdl.h"
#include "rtwtypes.h"
#include "RoofSys_CommDefines.h"
#include "BlockDet_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "UsgHist_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "UsgHis_Mdl_private.h"
#include "zero_crossing_types.h"
#include "UsgHist_Exp.h"
#include "MtrMdl_Exp.h"
#include "IoSigIf_Exp.h"
#include "CfgParam_Mdl_Exp.h"

/* Named constants for Chart: '<S185>/Lib_CycleCounter' */
#define UsgHis_Mdl_IN_close            ((uint8_T)1U)
#define UsgHis_Mdl_IN_open             ((uint8_T)2U)
#define UsgHis_Mdl_MaxCycleCount_C     ((uint16_T)65530U)

MdlrefDW_UsgHis_Mdl_T UsgHis_Mdl_MdlrefDW;

/* Block signals (default storage) */
B_UsgHis_Mdl_c_T UsgHis_Mdl_B;

/* Block states (default storage) */
DW_UsgHis_Mdl_f_T UsgHis_Mdl_DW;

/* Previous zero-crossings (trigger) states */
ZCE_UsgHis_Mdl_T UsgHis_Mdl_PrevZCX;

/* Output and update for atomic system: '<S2>/CheckManufacturingConditions' */
void UsgHis_Mdl_CheckManufacturingConditions(uint16_T rtu_nCycleIn, uint16_T
  rtu_nCycleCntEEP, uint16_T *rty_UsgHist_nCycle)
{
  boolean_T rtb_FixPtRelationalOperator;

  /* RelationalOperator: '<S10>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S10>/Delay Input1'
   *
   * Block description for '<S10>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_FixPtRelationalOperator = (rtu_nCycleIn !=
    UsgHis_Mdl_DW.DelayInput1_DSTATE_gg);

  /* Switch: '<S3>/Switch1' incorporates:
   *  Constant: '<S13>/FixPt Constant'
   *  Sum: '<S13>/FixPt Sum1'
   */
  if (rtb_FixPtRelationalOperator) {
    *rty_UsgHist_nCycle = (uint16_T)(rtu_nCycleCntEEP + 1U);
  } else {
    *rty_UsgHist_nCycle = rtu_nCycleCntEEP;
  }

  /* End of Switch: '<S3>/Switch1' */

  /* DataTypeConversion: '<S14>/Data Type Conversion' incorporates:
   *  Constant: '<S3>/Constant3'
   */
  UsgHis_Mdl_B.EEP_USGHIST_CYCLECNT = 6U;

  /* Outputs for Triggered SubSystem: '<S14>/NVMWrite' incorporates:
   *  TriggerPort: '<S16>/Trigger'
   */
  if (rtb_FixPtRelationalOperator && (UsgHis_Mdl_PrevZCX.NVMWrite_Trig_ZCE !=
       POS_ZCSIG)) {
    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S16>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S16>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.isShdnRdy_p = NVMMan_enRequestNVMWrite_new
      (UsgHis_Mdl_B.EEP_USGHIST_CYCLECNT);
  }

  UsgHis_Mdl_PrevZCX.NVMWrite_Trig_ZCE = rtb_FixPtRelationalOperator;

  /* End of Outputs for SubSystem: '<S14>/NVMWrite' */

  /* Update for UnitDelay: '<S10>/Delay Input1'
   *
   * Block description for '<S10>/Delay Input1':
   *
   *  Store in Global RAM
   */
  UsgHis_Mdl_DW.DelayInput1_DSTATE_gg = rtu_nCycleIn;
}

/* Output and update for atomic system: '<S2>/ProduceDIDSignals' */
void UsgHis_Mdl_ProduceDIDSignals(boolean_T rtu_StateMach_isFactMode, const
  int16_T *rtu_AmbTempMon_TAmbTemp, const uint16_T *rtu_VoltMon_u12VBatt, const
  MtrCtrl_MtrPwrd_t *rtu_MtrCtrl_MtrPwrdState, const boolean_T
  *rtu_LearnAdap_isLearnComplete, const BlockDet_Stall_t
  *rtu_BlockDet_stStallType, uint8_T rtu_UsgHist_StallCountNvmRead, uint16_T
  rtu_UsgHist_SlideCycCountNvmRead, uint8_T rtu_UsgHist_LearnSuccessCountNvmRead,
  uint8_T rtu_UsgHist_LearnFailCountNvmRead, uint8_T
  rtu_UsgHist_ThermProtCountNvmRead, int16_T rtu_UsgHist_MinAmbTempNvmRead,
  int16_T rtu_UsgHist_MaxAmbTempNvmRead, uint16_T rtu_UsgHist_MinBattVtgNvmRead,
  uint16_T rtu_UsgHist_MaxBattVtgNvmRead, const boolean_T
  *rtu_LearnAdap_isInterrupted, uint16_T rtu_nCycOut, uint32_T
  rtu_UsgHist_MotorOperTime_In, uint16_T rtu_UsgHist_ReversalCountNvmRead,
  uint8_T rtu_UsgHist_ReprogrammingCount_In, const CtrlLog_MovType_t
  *rtu_stMovementType, const StateMach_Mode_t *rtu_StateMach_stSysMode, const
  ThermProt_MtrTempClass_t *rtu_stMtrTempClass, const
  ThermProt_MosfetTempClass_t *rtu_stMosfetTempClass, const uint16_T
  *rtu_hCurPos, const CtrlLog_tenTargetDirection *rtu_stDirCommand, const
  uint16_T *rtu_ThresForce_ForceThreshold, uint16_T rtu_UsgHist_ReversalThres_In,
  CtrlLog_tenTargetDirection rtu_UsgHist_ReversalDir_In, uint16_T
  rtu_UsgHist_ReversalPos_In, uint16_T rtu_LearnAdap_renormCycleCounter, int16_T
  *rty_UsgHist_MaxAmbTemp, int16_T *rty_UsgHist_MinAmbTemp, uint16_T
  *rty_UsgHist_MaxBattVtg, uint16_T *rty_UsgHist_MinBattVtg, uint32_T
  *rty_UsgHist_MotorOperTime, uint16_T *rty_UsgHist_CycRenormCount, uint8_T
  *rty_UsgHist_LearnSuccessCount, uint8_T *rty_UsgHist_LeanrFailCount, uint8_T
  *rty_UsgHist_StallCount, uint16_T *rty_UsgHist_ReversalCount, uint16_T
  *rty_UsgHist_SlideCycCount, uint8_T *rty_UsgHist_ThermProtCount, uint8_T
  *rty_UsgHist_ReprogrammingCount, uint16_T *rty_UsgHist_ReversalPos,
  CtrlLog_tenTargetDirection *rty_UsgHist_ReversalDir, uint16_T
  *rty_UsgHist_ReversalThres)
{
  int32_T tmp;
  uint32_T qY;
  uint32_T rtb_Divide;
  int16_T rtb_UsgHist_MaxAmbTemp_f;
  int16_T rtb_UsgHist_MinAmbTemp_n;
  int16_T tmp_0;
  uint16_T rtb_UsgHist_MaxBattVtg;
  uint16_T rtb_UsgHist_MinBattVtg;
  uint16_T rtb_UsgHist_ReversalPos;
  uint16_T rtb_UsgHist_ReversalThres;
  uint8_T rtb_Compare;
  boolean_T rtb_Compare_ch;
  boolean_T rtb_FixPtRelationalOperator;
  boolean_T rtb_FixPtRelationalOperator_b;
  boolean_T rtb_FixPtRelationalOperator_e;
  boolean_T rtb_FixPtRelationalOperator_p;
  boolean_T zcEvent;
  CtrlLog_tenTargetDirection rtb_UsgHist_ReversalDir;

  /* RelationalOperator: '<S28>/Compare' incorporates:
   *  Constant: '<S28>/Constant'
   */
  rtb_Compare_ch = (*rtu_stMovementType == CtrlLog_MovType_t_AtsReversal_C);

  /* RelationalOperator: '<S30>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S30>/Delay Input1'
   *
   * Block description for '<S30>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_FixPtRelationalOperator = ((int32_T)rtb_Compare_ch > (int32_T)
    UsgHis_Mdl_DW.DelayInput1_DSTATE_m);

  /* Outputs for Triggered SubSystem: '<S25>/ReversalDirection' incorporates:
   *  TriggerPort: '<S32>/Trigger'
   */
  if (rtb_FixPtRelationalOperator &&
      (UsgHis_Mdl_PrevZCX.ReversalDirection_Trig_ZCE != POS_ZCSIG)) {
    /* SignalConversion generated from: '<S32>/stDirCommand' */
    UsgHis_Mdl_B.stDirCommand = *rtu_stDirCommand;

    /* DataTypeConversion: '<S34>/Data Type Conversion' incorporates:
     *  Constant: '<S32>/Constant11'
     */
    UsgHis_Mdl_B.EEP_USGHIST_REVERSALEVENT_a = 45U;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S36>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S36>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_o =
      NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_REVERSALEVENT_a);
  }

  UsgHis_Mdl_PrevZCX.ReversalDirection_Trig_ZCE = rtb_FixPtRelationalOperator;

  /* End of Outputs for SubSystem: '<S25>/ReversalDirection' */

  /* Switch: '<S25>/Switch' incorporates:
   *  Constant: '<S29>/Constant'
   *  Logic: '<S25>/AND'
   *  RelationalOperator: '<S29>/Compare'
   */
  if (rtb_FixPtRelationalOperator && (*rtu_StateMach_stSysMode >
       StateMach_Mode_t_Startup_C)) {
    rtb_UsgHist_ReversalDir = UsgHis_Mdl_B.stDirCommand;
  } else {
    rtb_UsgHist_ReversalDir = rtu_UsgHist_ReversalDir_In;
  }

  /* End of Switch: '<S25>/Switch' */

  /* RelationalOperator: '<S38>/Compare' incorporates:
   *  Constant: '<S38>/Constant'
   */
  rtb_FixPtRelationalOperator = (*rtu_stMovementType ==
    CtrlLog_MovType_t_AtsReversal_C);

  /* RelationalOperator: '<S40>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S40>/Delay Input1'
   *
   * Block description for '<S40>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_FixPtRelationalOperator_b = ((int32_T)rtb_FixPtRelationalOperator >
    (int32_T)UsgHis_Mdl_DW.DelayInput1_DSTATE_d);

  /* Outputs for Triggered SubSystem: '<S26>/ReversalPosition' incorporates:
   *  TriggerPort: '<S42>/Trigger'
   */
  if (rtb_FixPtRelationalOperator_b &&
      (UsgHis_Mdl_PrevZCX.ReversalPosition_Trig_ZCE != POS_ZCSIG)) {
    /* SignalConversion generated from: '<S42>/UsgHist_RevNvmRead' */
    UsgHis_Mdl_B.UsgHist_RevNvmRead = *rtu_hCurPos;

    /* DataTypeConversion: '<S44>/Data Type Conversion' incorporates:
     *  Constant: '<S42>/Constant11'
     */
    UsgHis_Mdl_B.EEP_USGHIST_REVERSALEVENT_m = 45U;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S46>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S46>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_hp =
      NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_REVERSALEVENT_m);
  }

  UsgHis_Mdl_PrevZCX.ReversalPosition_Trig_ZCE = rtb_FixPtRelationalOperator_b;

  /* End of Outputs for SubSystem: '<S26>/ReversalPosition' */

  /* Switch: '<S26>/Switch' incorporates:
   *  Constant: '<S39>/Constant'
   *  Logic: '<S26>/AND'
   *  RelationalOperator: '<S39>/Compare'
   */
  if (rtb_FixPtRelationalOperator_b && (*rtu_StateMach_stSysMode >
       StateMach_Mode_t_Startup_C)) {
    rtb_UsgHist_ReversalPos = UsgHis_Mdl_B.UsgHist_RevNvmRead;
  } else {
    rtb_UsgHist_ReversalPos = rtu_UsgHist_ReversalPos_In;
  }

  /* End of Switch: '<S26>/Switch' */

  /* RelationalOperator: '<S48>/Compare' incorporates:
   *  Constant: '<S48>/Constant'
   */
  rtb_FixPtRelationalOperator_b = (*rtu_stMovementType ==
    CtrlLog_MovType_t_AtsReversal_C);

  /* RelationalOperator: '<S50>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S50>/Delay Input1'
   *
   * Block description for '<S50>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_FixPtRelationalOperator_p = ((int32_T)rtb_FixPtRelationalOperator_b >
    (int32_T)UsgHis_Mdl_DW.DelayInput1_DSTATE_h);

  /* Outputs for Triggered SubSystem: '<S27>/ReversalThreshold' incorporates:
   *  TriggerPort: '<S52>/Trigger'
   */
  if (rtb_FixPtRelationalOperator_p &&
      (UsgHis_Mdl_PrevZCX.ReversalThreshold_Trig_ZCE != POS_ZCSIG)) {
    /* SignalConversion generated from: '<S52>/UsgHist_RevthrNvmRead' */
    UsgHis_Mdl_B.UsgHist_RevthrNvmRead = *rtu_ThresForce_ForceThreshold;

    /* DataTypeConversion: '<S54>/Data Type Conversion' incorporates:
     *  Constant: '<S52>/Constant11'
     */
    UsgHis_Mdl_B.EEP_USGHIST_REVERSALEVENT = 45U;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S56>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S56>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_lh =
      NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_REVERSALEVENT);
  }

  UsgHis_Mdl_PrevZCX.ReversalThreshold_Trig_ZCE = rtb_FixPtRelationalOperator_p;

  /* End of Outputs for SubSystem: '<S27>/ReversalThreshold' */

  /* Switch: '<S27>/Switch' incorporates:
   *  Constant: '<S49>/Constant'
   *  Logic: '<S27>/AND'
   *  RelationalOperator: '<S49>/Compare'
   */
  if (rtb_FixPtRelationalOperator_p && (*rtu_StateMach_stSysMode >
       StateMach_Mode_t_Startup_C)) {
    rtb_UsgHist_ReversalThres = UsgHis_Mdl_B.UsgHist_RevthrNvmRead;
  } else {
    rtb_UsgHist_ReversalThres = rtu_UsgHist_ReversalThres_In;
  }

  /* End of Switch: '<S27>/Switch' */

  /* Logic: '<S5>/LogicalOperator1' incorporates:
   *  Constant: '<S115>/Constant'
   *  Logic: '<S5>/LogicalOperator2'
   *  Logic: '<S5>/LogicalOperator3'
   *  Logic: '<S5>/LogicalOperator4'
   *  RelationalOperator: '<S115>/Compare'
   */
  rtb_FixPtRelationalOperator_p = !rtu_StateMach_isFactMode;

  /* Outputs for Enabled SubSystem: '<S5>/History_Loggers' incorporates:
   *  EnablePort: '<S22>/Enable'
   */
  /* Outputs for Enabled SubSystem: '<S5>/Extend_Event' incorporates:
   *  EnablePort: '<S21>/Enable'
   */
  /* Outputs for Enabled SubSystem: '<S5>/Cycle_counters' incorporates:
   *  EnablePort: '<S19>/Enable'
   */
  if (rtb_FixPtRelationalOperator_p) {
    /* DataTypeConversion: '<S60>/Data Type Conversion' */
    *rty_UsgHist_CycRenormCount = rtu_LearnAdap_renormCycleCounter;

    /* RelationalOperator: '<S62>/FixPt Relational Operator' incorporates:
     *  UnitDelay: '<S62>/Delay Input1'
     *
     * Block description for '<S62>/Delay Input1':
     *
     *  Store in Global RAM
     */
    rtb_FixPtRelationalOperator_e = (*rty_UsgHist_CycRenormCount !=
      UsgHis_Mdl_DW.DelayInput1_DSTATE_g);

    /* Outputs for Triggered SubSystem: '<S60>/WriteCycRenormCounterVld' incorporates:
     *  TriggerPort: '<S64>/Trigger'
     */
    if (rtb_FixPtRelationalOperator_e &&
        (UsgHis_Mdl_PrevZCX.WriteCycRenormCounterVld_Trig_ZCE != POS_ZCSIG)) {
      /* DataTypeConversion: '<S64>/Data Type Conversion' incorporates:
       *  Constant: '<S60>/Constant11'
       */
      UsgHis_Mdl_B.DataTypeConversion = 19U;

      /* S-Function (NVMMan_enRequestNVMWrite_new): '<S66>/S_Func_NVMMan_enRequestNVMWrite_new' */
      /* S-Function Block: <S66>/S_Func_NVMMan_enRequestNVMWrite_new */
      UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_f =
        NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.DataTypeConversion);
    }

    UsgHis_Mdl_PrevZCX.WriteCycRenormCounterVld_Trig_ZCE =
      rtb_FixPtRelationalOperator_e;

    /* End of Outputs for SubSystem: '<S60>/WriteCycRenormCounterVld' */

    /* RelationalOperator: '<S69>/FixPt Relational Operator' incorporates:
     *  UnitDelay: '<S69>/Delay Input1'
     *
     * Block description for '<S69>/Delay Input1':
     *
     *  Store in Global RAM
     */
    rtb_FixPtRelationalOperator_e = (rtu_nCycOut >
      UsgHis_Mdl_DW.DelayInput1_DSTATE_j);

    /* Outputs for Enabled SubSystem: '<S59>/SlideCycCounter' incorporates:
     *  EnablePort: '<S71>/Enable'
     */
    if (rtb_FixPtRelationalOperator_e) {
      /* Sum: '<S71>/Add' incorporates:
       *  Constant: '<S71>/Constant'
       */
      qY = rtu_UsgHist_SlideCycCountNvmRead + 1U;
      if (qY > 65535U) {
        qY = 65535U;
      }

      /* Sum: '<S71>/Add' */
      UsgHis_Mdl_B.Add = (uint16_T)qY;

      /* DataTypeConversion: '<S73>/Data Type Conversion' incorporates:
       *  Constant: '<S71>/Constant11'
       */
      UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_a = 19U;

      /* S-Function (NVMMan_enRequestNVMWrite_new): '<S75>/S_Func_NVMMan_enRequestNVMWrite_new' */
      /* S-Function Block: <S75>/S_Func_NVMMan_enRequestNVMWrite_new */
      UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_h =
        NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_a);
    }

    /* End of Outputs for SubSystem: '<S59>/SlideCycCounter' */

    /* Switch: '<S59>/Switch4' incorporates:
     *  Constant: '<S68>/Constant'
     *  Logic: '<S59>/AND'
     *  RelationalOperator: '<S68>/Compare'
     */
    if ((*rtu_StateMach_stSysMode > StateMach_Mode_t_Startup_C) &&
        rtb_FixPtRelationalOperator_e) {
      *rty_UsgHist_SlideCycCount = UsgHis_Mdl_B.Add;
    } else {
      *rty_UsgHist_SlideCycCount = rtu_UsgHist_SlideCycCountNvmRead;
    }

    /* End of Switch: '<S59>/Switch4' */

    /* Update for UnitDelay: '<S62>/Delay Input1'
     *
     * Block description for '<S62>/Delay Input1':
     *
     *  Store in Global RAM
     */
    UsgHis_Mdl_DW.DelayInput1_DSTATE_g = *rty_UsgHist_CycRenormCount;

    /* Update for UnitDelay: '<S69>/Delay Input1'
     *
     * Block description for '<S69>/Delay Input1':
     *
     *  Store in Global RAM
     */
    UsgHis_Mdl_DW.DelayInput1_DSTATE_j = rtu_nCycOut;

    /* Logic: '<S77>/AND3' incorporates:
     *  Constant: '<S80>/Constant'
     *  RelationalOperator: '<S80>/Compare'
     */
    rtb_FixPtRelationalOperator_e = ((*rtu_LearnAdap_isInterrupted) &&
      (*rtu_StateMach_stSysMode > StateMach_Mode_t_Startup_C));

    /* Outputs for Triggered SubSystem: '<S77>/LeanrFailCounter' incorporates:
     *  TriggerPort: '<S82>/Trigger'
     */
    zcEvent = ((*rtu_LearnAdap_isInterrupted) &&
               (UsgHis_Mdl_PrevZCX.LeanrFailCounter_Trig_ZCE != POS_ZCSIG));
    if (zcEvent) {
      /* RelationalOperator: '<S83>/Compare' incorporates:
       *  Constant: '<S83>/Constant'
       */
      zcEvent = (rtu_UsgHist_LearnFailCountNvmRead == 255);

      /* Switch: '<S82>/Switch' */
      if (zcEvent) {
        /* Switch: '<S82>/Switch' */
        UsgHis_Mdl_B.Switch = rtu_UsgHist_LearnFailCountNvmRead;
      } else {
        /* Switch: '<S82>/Switch' incorporates:
         *  Constant: '<S82>/Constant'
         *  Sum: '<S82>/Add'
         */
        UsgHis_Mdl_B.Switch = (uint8_T)(rtu_UsgHist_LearnFailCountNvmRead + 1U);
      }

      /* End of Switch: '<S82>/Switch' */

      /* DataTypeConversion: '<S85>/Data Type Conversion' incorporates:
       *  Constant: '<S82>/Constant11'
       */
      UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_p = 22U;

      /* S-Function (NVMMan_enRequestNVMWrite_new): '<S87>/S_Func_NVMMan_enRequestNVMWrite_new' */
      /* S-Function Block: <S87>/S_Func_NVMMan_enRequestNVMWrite_new */
      UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_n =
        NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_p);
    }

    UsgHis_Mdl_PrevZCX.LeanrFailCounter_Trig_ZCE = *rtu_LearnAdap_isInterrupted;

    /* End of Outputs for SubSystem: '<S77>/LeanrFailCounter' */

    /* Switch: '<S77>/Switch3' */
    if (rtb_FixPtRelationalOperator_e) {
      *rty_UsgHist_LeanrFailCount = UsgHis_Mdl_B.Switch;
    } else {
      *rty_UsgHist_LeanrFailCount = rtu_UsgHist_LearnFailCountNvmRead;
    }

    /* End of Switch: '<S77>/Switch3' */

    /* Logic: '<S78>/AND' incorporates:
     *  Constant: '<S89>/Constant'
     *  RelationalOperator: '<S89>/Compare'
     */
    rtb_FixPtRelationalOperator_e = ((*rtu_LearnAdap_isLearnComplete) &&
      (*rtu_StateMach_stSysMode > StateMach_Mode_t_Startup_C));

    /* Outputs for Triggered SubSystem: '<S78>/LeanrSuccessCounter' incorporates:
     *  TriggerPort: '<S91>/Trigger'
     */
    zcEvent = ((*rtu_LearnAdap_isLearnComplete) &&
               (UsgHis_Mdl_PrevZCX.LeanrSuccessCounter_Trig_ZCE != POS_ZCSIG));
    if (zcEvent) {
      /* Sum: '<S91>/Add' incorporates:
       *  Constant: '<S91>/Constant'
       */
      tmp = (int32_T)(rtu_UsgHist_LearnSuccessCountNvmRead + 1U);
      if ((uint32_T)tmp > 255U) {
        tmp = 255;
      }

      /* Sum: '<S91>/Add' */
      UsgHis_Mdl_B.Add_c = (uint8_T)tmp;

      /* DataTypeConversion: '<S93>/Data Type Conversion' incorporates:
       *  Constant: '<S91>/Constant11'
       */
      UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_o = 22U;

      /* S-Function (NVMMan_enRequestNVMWrite_new): '<S95>/S_Func_NVMMan_enRequestNVMWrite_new' */
      /* S-Function Block: <S95>/S_Func_NVMMan_enRequestNVMWrite_new */
      UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_c =
        NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_o);
    }

    UsgHis_Mdl_PrevZCX.LeanrSuccessCounter_Trig_ZCE =
      *rtu_LearnAdap_isLearnComplete;

    /* End of Outputs for SubSystem: '<S78>/LeanrSuccessCounter' */

    /* Switch: '<S78>/Switch3' */
    if (rtb_FixPtRelationalOperator_e) {
      *rty_UsgHist_LearnSuccessCount = UsgHis_Mdl_B.Add_c;
    } else {
      *rty_UsgHist_LearnSuccessCount = rtu_UsgHist_LearnSuccessCountNvmRead;
    }

    /* End of Switch: '<S78>/Switch3' */

    /* Logic: '<S79>/Logical Operator' incorporates:
     *  Constant: '<S100>/Constant'
     *  Constant: '<S101>/Constant'
     *  Constant: '<S102>/Constant'
     *  Constant: '<S97>/Constant'
     *  Constant: '<S98>/Constant'
     *  Constant: '<S99>/Constant'
     *  Logic: '<S79>/OR'
     *  Logic: '<S79>/OR1'
     *  RelationalOperator: '<S100>/Compare'
     *  RelationalOperator: '<S101>/Compare'
     *  RelationalOperator: '<S102>/Compare'
     *  RelationalOperator: '<S97>/Compare'
     *  RelationalOperator: '<S98>/Compare'
     *  RelationalOperator: '<S99>/Compare'
     */
    rtb_FixPtRelationalOperator_e = ((*rtu_stMosfetTempClass ==
      ThermProt_MosfetTempClass_t_MosfetTempClassC_C) || (*rtu_stMosfetTempClass
      == ThermProt_MosfetTempClass_t_MosfetTempClassD_C) ||
      (*rtu_stMosfetTempClass == ThermProt_MosfetTempClass_t_MosfetTempClassE_C)
      || ((*rtu_stMtrTempClass == ThermProt_MtrTempClass_t_MtrTempClassD_C) || (*
      rtu_stMtrTempClass == ThermProt_MtrTempClass_t_MtrTempClassE_C) ||
          (*rtu_stMtrTempClass == ThermProt_MtrTempClass_t_MtrTempClassF_C)));

    /* Outputs for Triggered SubSystem: '<S79>/ThermalProtCounter' incorporates:
     *  TriggerPort: '<S106>/Trigger'
     */
    if (rtb_FixPtRelationalOperator_e &&
        (UsgHis_Mdl_PrevZCX.ThermalProtCounter_Trig_ZCE != POS_ZCSIG)) {
      /* Sum: '<S106>/Add' incorporates:
       *  Constant: '<S106>/Constant'
       */
      tmp = (int32_T)(rtu_UsgHist_ThermProtCountNvmRead + 1U);
      if ((uint32_T)tmp > 255U) {
        tmp = 255;
      }

      /* Sum: '<S106>/Add' */
      UsgHis_Mdl_B.UsgHist_ThermProtCount_g = (uint8_T)tmp;

      /* DataTypeConversion: '<S108>/Data Type Conversion' incorporates:
       *  Constant: '<S106>/Constant11'
       */
      UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_nl = 22U;

      /* S-Function (NVMMan_enRequestNVMWrite_new): '<S110>/S_Func_NVMMan_enRequestNVMWrite_new' */
      /* S-Function Block: <S110>/S_Func_NVMMan_enRequestNVMWrite_new */
      UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_a =
        NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_nl);
    }

    UsgHis_Mdl_PrevZCX.ThermalProtCounter_Trig_ZCE =
      rtb_FixPtRelationalOperator_e;

    /* End of Outputs for SubSystem: '<S79>/ThermalProtCounter' */

    /* Switch: '<S79>/Switch3' incorporates:
     *  Constant: '<S103>/Constant'
     *  Logic: '<S79>/AND'
     *  RelationalOperator: '<S103>/Compare'
     *  RelationalOperator: '<S104>/FixPt Relational Operator'
     *  UnitDelay: '<S104>/Delay Input1'
     *
     * Block description for '<S104>/Delay Input1':
     *
     *  Store in Global RAM
     */
    if (((int32_T)rtb_FixPtRelationalOperator_e > (int32_T)
         UsgHis_Mdl_DW.DelayInput1_DSTATE_gd) && (*rtu_StateMach_stSysMode >
         StateMach_Mode_t_Startup_C)) {
      *rty_UsgHist_ThermProtCount = UsgHis_Mdl_B.UsgHist_ThermProtCount_g;
    } else {
      *rty_UsgHist_ThermProtCount = rtu_UsgHist_ThermProtCountNvmRead;
    }

    /* End of Switch: '<S79>/Switch3' */

    /* Update for UnitDelay: '<S104>/Delay Input1'
     *
     * Block description for '<S104>/Delay Input1':
     *
     *  Store in Global RAM
     */
    UsgHis_Mdl_DW.DelayInput1_DSTATE_gd = rtb_FixPtRelationalOperator_e;

    /* Outputs for Enabled SubSystem: '<S112>/MotorOperTime' incorporates:
     *  EnablePort: '<S117>/Enable'
     */
    if (*rtu_MtrCtrl_MtrPwrdState == MtrCtrl_MtrPwrd_t_PWRD_C) {
      /* Sum: '<S117>/Add' incorporates:
       *  UnitDelay: '<S117>/Unit Delay'
       */
      qY = UsgHis_Mdl_DW.UnitDelay_DSTATE + /*MW:OvSatOk*/ 1U;
      if (UsgHis_Mdl_DW.UnitDelay_DSTATE + 1U < 1U) {
        qY = MAX_uint32_T;
      }

      /* Product: '<S117>/Divide' incorporates:
       *  Constant: '<S117>/Constant2'
       *  Sum: '<S117>/Add'
       */
      rtb_Divide = qY / 100U;

      /* RelationalOperator: '<S119>/FixPt Relational Operator' incorporates:
       *  UnitDelay: '<S119>/Delay Input1'
       *
       * Block description for '<S119>/Delay Input1':
       *
       *  Store in Global RAM
       */
      rtb_FixPtRelationalOperator_e = (rtb_Divide >
        UsgHis_Mdl_DW.DelayInput1_DSTATE);

      /* Outputs for Enabled SubSystem: '<S117>/MotorOperatingTime_sec' incorporates:
       *  EnablePort: '<S121>/Enable'
       */
      if (rtb_FixPtRelationalOperator_e) {
        /* Switch: '<S121>/Switch' incorporates:
         *  Constant: '<S121>/Constant1'
         *  Sum: '<S121>/Add1'
         */
        if (rtu_UsgHist_MotorOperTime_In + 1U > 16777215U) {
          /* Switch: '<S121>/Switch' incorporates:
           *  Constant: '<S121>/Constant3'
           */
          UsgHis_Mdl_B.UsgHist_MotorOperTime_c = 16777215U;
        } else {
          /* Switch: '<S121>/Switch' */
          UsgHis_Mdl_B.UsgHist_MotorOperTime_c = rtu_UsgHist_MotorOperTime_In +
            1U;
        }

        /* End of Switch: '<S121>/Switch' */

        /* DataTypeConversion: '<S122>/Data Type Conversion' incorporates:
         *  Constant: '<S121>/Constant11'
         */
        UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_l = 20U;

        /* S-Function (NVMMan_enRequestNVMWrite_new): '<S124>/S_Func_NVMMan_enRequestNVMWrite_new' */
        /* S-Function Block: <S124>/S_Func_NVMMan_enRequestNVMWrite_new */
        UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_lt =
          NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_l);
      }

      /* End of Outputs for SubSystem: '<S117>/MotorOperatingTime_sec' */

      /* Switch: '<S117>/Switch3' incorporates:
       *  Constant: '<S118>/Constant'
       *  Logic: '<S117>/AND'
       *  RelationalOperator: '<S118>/Compare'
       */
      if (rtb_FixPtRelationalOperator_e && (*rtu_StateMach_stSysMode >
           StateMach_Mode_t_Startup_C)) {
        /* Switch: '<S117>/Switch3' */
        UsgHis_Mdl_B.UsgHist_MotorOperTime_o =
          UsgHis_Mdl_B.UsgHist_MotorOperTime_c;
      } else {
        /* Switch: '<S117>/Switch3' */
        UsgHis_Mdl_B.UsgHist_MotorOperTime_o = rtu_UsgHist_MotorOperTime_In;
      }

      /* End of Switch: '<S117>/Switch3' */

      /* Update for UnitDelay: '<S117>/Unit Delay' incorporates:
       *  Sum: '<S117>/Add'
       */
      UsgHis_Mdl_DW.UnitDelay_DSTATE = qY;

      /* Update for UnitDelay: '<S119>/Delay Input1'
       *
       * Block description for '<S119>/Delay Input1':
       *
       *  Store in Global RAM
       */
      UsgHis_Mdl_DW.DelayInput1_DSTATE = rtb_Divide;

      /* Switch: '<S112>/Switch3' */
      *rty_UsgHist_MotorOperTime = UsgHis_Mdl_B.UsgHist_MotorOperTime_o;
    } else {
      /* Switch: '<S112>/Switch3' */
      *rty_UsgHist_MotorOperTime = rtu_UsgHist_MotorOperTime_In;
    }

    /* End of Outputs for SubSystem: '<S112>/MotorOperTime' */

    /* Logic: '<S114>/Logical Operator' incorporates:
     *  Constant: '<S114>/Constant3'
     *  Constant: '<S115>/Constant'
     *  RelationalOperator: '<S114>/Relational Operator3'
     *  RelationalOperator: '<S115>/Compare'
     *  RelationalOperator: '<S128>/FixPt Relational Operator'
     *  UnitDelay: '<S128>/Delay Input1'
     *
     * Block description for '<S128>/Delay Input1':
     *
     *  Store in Global RAM
     */
    rtb_FixPtRelationalOperator_e = ((*rtu_stMovementType !=
      UsgHis_Mdl_DW.DelayInput1_DSTATE_c) && (*rtu_stMovementType ==
      CtrlLog_MovType_t_AtsReversal_C));

    /* Outputs for Triggered SubSystem: '<S114>/ReversalCounter' incorporates:
     *  TriggerPort: '<S130>/Trigger'
     */
    if (rtb_FixPtRelationalOperator_e &&
        (UsgHis_Mdl_PrevZCX.ReversalCounter_Trig_ZCE != POS_ZCSIG)) {
      /* RelationalOperator: '<S131>/Compare' incorporates:
       *  Constant: '<S131>/Constant'
       */
      rtb_Compare = (uint8_T)(rtu_UsgHist_ReversalCountNvmRead == 65535);

      /* Switch: '<S130>/Switch' */
      if (rtb_Compare > 0) {
        /* Switch: '<S130>/Switch' */
        UsgHis_Mdl_B.UsgHist_ReversalCount_d = rtu_UsgHist_ReversalCountNvmRead;
      } else {
        /* Sum: '<S130>/Add' */
        rtb_Compare = (uint8_T)rtu_UsgHist_ReversalCountNvmRead;

        /* Switch: '<S130>/Switch' incorporates:
         *  Constant: '<S130>/Constant'
         *  Sum: '<S130>/Add'
         */
        UsgHis_Mdl_B.UsgHist_ReversalCount_d = (uint8_T)(rtb_Compare + 1U);
      }

      /* End of Switch: '<S130>/Switch' */

      /* DataTypeConversion: '<S133>/Data Type Conversion' incorporates:
       *  Constant: '<S130>/Constant11'
       */
      UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_h = 20U;

      /* S-Function (NVMMan_enRequestNVMWrite_new): '<S135>/S_Func_NVMMan_enRequestNVMWrite_new' */
      /* S-Function Block: <S135>/S_Func_NVMMan_enRequestNVMWrite_new */
      UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_b =
        NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_h);
    }

    UsgHis_Mdl_PrevZCX.ReversalCounter_Trig_ZCE = rtb_FixPtRelationalOperator_e;

    /* End of Outputs for SubSystem: '<S114>/ReversalCounter' */

    /* Switch: '<S114>/Switch3' incorporates:
     *  Constant: '<S127>/Constant'
     *  Logic: '<S114>/AND'
     *  RelationalOperator: '<S127>/Compare'
     */
    if (rtb_FixPtRelationalOperator_e && (*rtu_StateMach_stSysMode >
         StateMach_Mode_t_Startup_C)) {
      *rty_UsgHist_ReversalCount = UsgHis_Mdl_B.UsgHist_ReversalCount_d;
    } else {
      *rty_UsgHist_ReversalCount = rtu_UsgHist_ReversalCountNvmRead;
    }

    /* End of Switch: '<S114>/Switch3' */

    /* DataTypeConversion: '<S113>/Cast' */
    *rty_UsgHist_ReprogrammingCount = rtu_UsgHist_ReprogrammingCount_In;

    /* Update for UnitDelay: '<S128>/Delay Input1'
     *
     * Block description for '<S128>/Delay Input1':
     *
     *  Store in Global RAM
     */
    UsgHis_Mdl_DW.DelayInput1_DSTATE_c = *rtu_stMovementType;
  }

  /* End of Logic: '<S5>/LogicalOperator1' */
  /* End of Outputs for SubSystem: '<S5>/Cycle_counters' */
  /* End of Outputs for SubSystem: '<S5>/Extend_Event' */
  /* End of Outputs for SubSystem: '<S5>/History_Loggers' */

  /* Sum: '<S137>/Add' incorporates:
   *  Constant: '<S137>/Constant'
   *  Sum: '<S138>/Add'
   */
  rtb_UsgHist_MinAmbTemp_n = (int16_T)(*rtu_AmbTempMon_TAmbTemp + 50);

  /* RelationalOperator: '<S137>/GreaterThan' incorporates:
   *  Sum: '<S137>/Add'
   */
  rtb_FixPtRelationalOperator_e = (rtb_UsgHist_MinAmbTemp_n >
    rtu_UsgHist_MaxAmbTempNvmRead);

  /* Outputs for Enabled SubSystem: '<S137>/WriteAmbMaxTemp' incorporates:
   *  EnablePort: '<S143>/Enable'
   */
  if (rtb_FixPtRelationalOperator_e) {
    /* DataTypeConversion: '<S143>/Data Type Conversion' incorporates:
     *  Sum: '<S137>/Add'
     */
    UsgHis_Mdl_B.UsgHist_MaxAmbTemp = rtb_UsgHist_MinAmbTemp_n;

    /* DataTypeConversion: '<S145>/Data Type Conversion' incorporates:
     *  Constant: '<S143>/Constant11'
     */
    UsgHis_Mdl_B.EEP_USGHIST_MAXAMBTEMP = 21U;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S147>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S147>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_e =
      NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MAXAMBTEMP);
  }

  /* End of Outputs for SubSystem: '<S137>/WriteAmbMaxTemp' */

  /* Switch: '<S137>/Switch3' incorporates:
   *  Constant: '<S141>/Constant'
   *  Logic: '<S137>/AND'
   *  RelationalOperator: '<S141>/Compare'
   */
  if (rtb_FixPtRelationalOperator_e && (*rtu_StateMach_stSysMode >
       StateMach_Mode_t_Startup_C)) {
    rtb_UsgHist_MaxAmbTemp_f = UsgHis_Mdl_B.UsgHist_MaxAmbTemp;
  } else {
    rtb_UsgHist_MaxAmbTemp_f = rtu_UsgHist_MaxAmbTempNvmRead;
  }

  /* End of Switch: '<S137>/Switch3' */

  /* RelationalOperator: '<S138>/GreaterThan' */
  rtb_FixPtRelationalOperator_e = (rtb_UsgHist_MinAmbTemp_n <
    rtu_UsgHist_MinAmbTempNvmRead);

  /* Outputs for Enabled SubSystem: '<S138>/WriteAmbMinTemp' incorporates:
   *  EnablePort: '<S151>/Enable'
   */
  if (rtb_FixPtRelationalOperator_e) {
    /* DataTypeConversion: '<S151>/Data Type Conversion' */
    UsgHis_Mdl_B.UsgHist_MinAmbTemp = rtb_UsgHist_MinAmbTemp_n;

    /* DataTypeConversion: '<S153>/Data Type Conversion' incorporates:
     *  Constant: '<S151>/Constant11'
     */
    UsgHis_Mdl_B.EEP_USGHIST_MINAMBTEMP = 21U;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S155>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S155>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_p =
      NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINAMBTEMP);
  }

  /* End of Outputs for SubSystem: '<S138>/WriteAmbMinTemp' */

  /* Switch: '<S138>/Switch3' incorporates:
   *  Constant: '<S149>/Constant'
   *  Logic: '<S138>/AND'
   *  RelationalOperator: '<S149>/Compare'
   */
  if (rtb_FixPtRelationalOperator_e && (*rtu_StateMach_stSysMode >
       StateMach_Mode_t_Startup_C)) {
    rtb_UsgHist_MinAmbTemp_n = UsgHis_Mdl_B.UsgHist_MinAmbTemp;
  } else {
    rtb_UsgHist_MinAmbTemp_n = rtu_UsgHist_MinAmbTempNvmRead;
  }

  /* End of Switch: '<S138>/Switch3' */

  /* Sum: '<S139>/Subtract' */
  tmp = *rtu_VoltMon_u12VBatt - rtu_UsgHist_MaxBattVtgNvmRead;
  if (tmp > 32767) {
    tmp = 32767;
  } else if (tmp < -32768) {
    tmp = -32768;
  }

  /* RelationalOperator: '<S139>/GreaterThan' */
  rtb_FixPtRelationalOperator_e = (*rtu_VoltMon_u12VBatt >
    rtu_UsgHist_MaxBattVtgNvmRead);

  /* Abs: '<S139>/Abs' incorporates:
   *  Sum: '<S139>/Subtract'
   */
  if ((int16_T)tmp < 0) {
    tmp_0 = (int16_T)-(int16_T)tmp;
  } else {
    tmp_0 = (int16_T)tmp;
  }

  /* Logic: '<S139>/AND1' incorporates:
   *  Abs: '<S139>/Abs'
   *  Constant: '<S157>/Constant'
   *  RelationalOperator: '<S157>/Compare'
   */
  rtb_FixPtRelationalOperator_e = ((tmp_0 > 100) &&
    rtb_FixPtRelationalOperator_e);

  /* Outputs for Enabled SubSystem: '<S139>/WriteBattVtg' incorporates:
   *  EnablePort: '<S160>/Enable'
   */
  if (rtb_FixPtRelationalOperator_e) {
    /* DataTypeConversion: '<S160>/Data Type Conversion' */
    UsgHis_Mdl_B.UsgHist_MaxBattVtg = *rtu_VoltMon_u12VBatt;

    /* DataTypeConversion: '<S162>/Data Type Conversion' incorporates:
     *  Constant: '<S160>/Constant11'
     */
    UsgHis_Mdl_B.EEP_USGHIST_MAXBATTVTG = 21U;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S164>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S164>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_i =
      NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MAXBATTVTG);
  }

  /* End of Outputs for SubSystem: '<S139>/WriteBattVtg' */

  /* Switch: '<S139>/Switch3' incorporates:
   *  Constant: '<S158>/Constant'
   *  Logic: '<S139>/AND'
   *  RelationalOperator: '<S158>/Compare'
   */
  if (rtb_FixPtRelationalOperator_e && (*rtu_StateMach_stSysMode >
       StateMach_Mode_t_Startup_C)) {
    rtb_UsgHist_MaxBattVtg = UsgHis_Mdl_B.UsgHist_MaxBattVtg;
  } else {
    rtb_UsgHist_MaxBattVtg = rtu_UsgHist_MaxBattVtgNvmRead;
  }

  /* End of Switch: '<S139>/Switch3' */

  /* RelationalOperator: '<S140>/GreaterThan' */
  rtb_FixPtRelationalOperator_e = (*rtu_VoltMon_u12VBatt <
    rtu_UsgHist_MinBattVtgNvmRead);

  /* Sum: '<S140>/Subtract' */
  tmp = *rtu_VoltMon_u12VBatt - rtu_UsgHist_MinBattVtgNvmRead;
  if (tmp > 32767) {
    tmp = 32767;
  } else if (tmp < -32768) {
    tmp = -32768;
  }

  /* Abs: '<S140>/Abs' incorporates:
   *  Sum: '<S140>/Subtract'
   */
  if ((int16_T)tmp < 0) {
    tmp_0 = (int16_T)-(int16_T)tmp;
  } else {
    tmp_0 = (int16_T)tmp;
  }

  /* Outputs for Enabled SubSystem: '<S140>/BattVtgMinDet' incorporates:
   *  EnablePort: '<S166>/Enable'
   */
  /* Logic: '<S140>/AND1' incorporates:
   *  Abs: '<S140>/Abs'
   *  Constant: '<S167>/Constant'
   *  RelationalOperator: '<S167>/Compare'
   */
  if ((tmp_0 > 100) && rtb_FixPtRelationalOperator_e) {
    /* DataTypeConversion: '<S166>/Data Type Conversion' */
    UsgHis_Mdl_B.UsgHist_MinBattVtg = *rtu_VoltMon_u12VBatt;

    /* DataTypeConversion: '<S170>/Data Type Conversion' incorporates:
     *  Constant: '<S166>/Constant11'
     */
    UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_n = 21U;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S173>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S173>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new_l =
      NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG_n);
  }

  /* End of Logic: '<S140>/AND1' */
  /* End of Outputs for SubSystem: '<S140>/BattVtgMinDet' */

  /* Switch: '<S140>/Switch3' incorporates:
   *  Constant: '<S168>/Constant'
   *  Logic: '<S140>/AND'
   *  RelationalOperator: '<S168>/Compare'
   */
  if (rtb_FixPtRelationalOperator_e && (*rtu_StateMach_stSysMode >
       StateMach_Mode_t_Startup_C)) {
    rtb_UsgHist_MinBattVtg = UsgHis_Mdl_B.UsgHist_MinBattVtg;
  } else {
    rtb_UsgHist_MinBattVtg = rtu_UsgHist_MinBattVtgNvmRead;
  }

  /* End of Switch: '<S140>/Switch3' */

  /* Outputs for Enabled SubSystem: '<S5>/StallCounter' incorporates:
   *  EnablePort: '<S24>/Enable'
   */
  if (rtb_FixPtRelationalOperator_p) {
    /* Logic: '<S24>/Logical Operator1' incorporates:
     *  Constant: '<S175>/Constant'
     *  RelationalOperator: '<S175>/Compare'
     *  RelationalOperator: '<S177>/FixPt Relational Operator'
     *  UnitDelay: '<S177>/Delay Input1'
     *  UnitDelay: '<S24>/Unit Delay'
     *
     * Block description for '<S177>/Delay Input1':
     *
     *  Store in Global RAM
     */
    rtb_FixPtRelationalOperator_p = ((UsgHis_Mdl_DW.UnitDelay_DSTATE_o ==
      BlockDet_Stall_t_NoStall_C) && (*rtu_BlockDet_stStallType !=
      UsgHis_Mdl_DW.DelayInput1_DSTATE_eo));

    /* Outputs for Triggered SubSystem: '<S24>/StallCounter' incorporates:
     *  TriggerPort: '<S179>/Trigger'
     */
    if (rtb_FixPtRelationalOperator_p &&
        (UsgHis_Mdl_PrevZCX.StallCounter_Trig_ZCE != POS_ZCSIG)) {
      /* Sum: '<S179>/Add' incorporates:
       *  Constant: '<S179>/Constant'
       */
      tmp = (int32_T)(rtu_UsgHist_StallCountNvmRead + 1U);
      if ((uint32_T)tmp > 255U) {
        tmp = 255;
      }

      /* Sum: '<S179>/Add' */
      UsgHis_Mdl_B.UsgHist_StallCount_l = (uint8_T)tmp;

      /* DataTypeConversion: '<S181>/Data Type Conversion' incorporates:
       *  Constant: '<S179>/Constant11'
       */
      UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG = 19U;

      /* S-Function (NVMMan_enRequestNVMWrite_new): '<S183>/S_Func_NVMMan_enRequestNVMWrite_new' */
      /* S-Function Block: <S183>/S_Func_NVMMan_enRequestNVMWrite_new */
      UsgHis_Mdl_B.S_Func_NVMMan_enRequestNVMWrite_new =
        NVMMan_enRequestNVMWrite_new(UsgHis_Mdl_B.EEP_USGHIST_MINBATTVTG);
    }

    UsgHis_Mdl_PrevZCX.StallCounter_Trig_ZCE = rtb_FixPtRelationalOperator_p;

    /* End of Outputs for SubSystem: '<S24>/StallCounter' */

    /* Switch: '<S24>/Switch3' incorporates:
     *  Constant: '<S176>/Constant'
     *  Logic: '<S24>/AND'
     *  RelationalOperator: '<S176>/Compare'
     */
    if (rtb_FixPtRelationalOperator_p && (*rtu_StateMach_stSysMode >
         StateMach_Mode_t_Startup_C)) {
      *rty_UsgHist_StallCount = UsgHis_Mdl_B.UsgHist_StallCount_l;
    } else {
      *rty_UsgHist_StallCount = rtu_UsgHist_StallCountNvmRead;
    }

    /* End of Switch: '<S24>/Switch3' */

    /* Update for UnitDelay: '<S24>/Unit Delay' */
    UsgHis_Mdl_DW.UnitDelay_DSTATE_o = *rtu_BlockDet_stStallType;

    /* Update for UnitDelay: '<S177>/Delay Input1'
     *
     * Block description for '<S177>/Delay Input1':
     *
     *  Store in Global RAM
     */
    UsgHis_Mdl_DW.DelayInput1_DSTATE_eo = *rtu_BlockDet_stStallType;
  }

  /* End of Outputs for SubSystem: '<S5>/StallCounter' */

  /* SignalConversion generated from: '<S5>/UsgHist_MaxAmbTemp' */
  *rty_UsgHist_MaxAmbTemp = rtb_UsgHist_MaxAmbTemp_f;

  /* SignalConversion generated from: '<S5>/UsgHist_MaxBattVtg' */
  *rty_UsgHist_MaxBattVtg = rtb_UsgHist_MaxBattVtg;

  /* SignalConversion generated from: '<S5>/UsgHist_MinAmbTemp' */
  *rty_UsgHist_MinAmbTemp = rtb_UsgHist_MinAmbTemp_n;

  /* SignalConversion generated from: '<S5>/UsgHist_MinBattVtg' */
  *rty_UsgHist_MinBattVtg = rtb_UsgHist_MinBattVtg;

  /* SignalConversion generated from: '<S5>/UsgHist_ReversalDir' */
  *rty_UsgHist_ReversalDir = rtb_UsgHist_ReversalDir;

  /* SignalConversion generated from: '<S5>/UsgHist_ReversalPos' */
  *rty_UsgHist_ReversalPos = rtb_UsgHist_ReversalPos;

  /* SignalConversion generated from: '<S5>/UsgHist_ReversalThres' */
  *rty_UsgHist_ReversalThres = rtb_UsgHist_ReversalThres;

  /* Update for UnitDelay: '<S30>/Delay Input1'
   *
   * Block description for '<S30>/Delay Input1':
   *
   *  Store in Global RAM
   */
  UsgHis_Mdl_DW.DelayInput1_DSTATE_m = rtb_Compare_ch;

  /* Update for UnitDelay: '<S40>/Delay Input1'
   *
   * Block description for '<S40>/Delay Input1':
   *
   *  Store in Global RAM
   */
  UsgHis_Mdl_DW.DelayInput1_DSTATE_d = rtb_FixPtRelationalOperator;

  /* Update for UnitDelay: '<S50>/Delay Input1'
   *
   * Block description for '<S50>/Delay Input1':
   *
   *  Store in Global RAM
   */
  UsgHis_Mdl_DW.DelayInput1_DSTATE_h = rtb_FixPtRelationalOperator_b;
}

/* System initialize for atomic system: '<S185>/Lib_CycleCounter' */
void UsgHis_Mdl_Lib_CycleCounter_Init(uint16_T *rty_nCycle)
{
  *rty_nCycle = 0U;
}

/* Output and update for atomic system: '<S185>/Lib_CycleCounter' */
void UsgHis_Mdl_Lib_CycleCounter(const uint16_T *rtu_CurPos, uint8_T rtu_offSet,
  const uint16_T *rtu_Open, const uint16_T *rtu_Close, uint16_T rtu_nCycleIn,
  uint16_T *rty_nCycle, DW_Lib_CycleCounter_UsgHis_Mdl_T *localDW)
{
  int32_T tmp;

  /* Chart: '<S185>/Lib_CycleCounter' */
  if (localDW->is_active_c2_UsgHis_Mdl == 0U) {
    localDW->is_active_c2_UsgHis_Mdl = 1U;
    localDW->is_c2_UsgHis_Mdl = UsgHis_Mdl_IN_close;
  } else if (localDW->is_c2_UsgHis_Mdl == UsgHis_Mdl_IN_close) {
    if (*rtu_CurPos >= *rtu_Open - rtu_offSet) {
      localDW->is_c2_UsgHis_Mdl = UsgHis_Mdl_IN_open;
    }

    /* case IN_open: */
    /*  Check if within position range  */
  } else if (*rtu_CurPos <= *rtu_Close + rtu_offSet) {
    /*  If not at maximum value  */
    if (rtu_nCycleIn < UsgHis_Mdl_MaxCycleCount_C) {
      tmp = *rty_nCycle + 1;
      if (tmp > 65535) {
        tmp = 65535;
      }

      *rty_nCycle = (uint16_T)tmp;
    } else {
      /*  Do nothing  */
    }

    localDW->is_c2_UsgHis_Mdl = UsgHis_Mdl_IN_close;
  }

  /* End of Chart: '<S185>/Lib_CycleCounter' */
}

/* System initialize for atomic system: '<S2>/TravelCycleCounter' */
void UsgHis_Mdl_TravelCycleCounter_Init(uint16_T *rty_nCycleOut)
{
  /* SystemInitialize for Enabled SubSystem: '<S6>/CycleCounterRollo' */

  /* SystemInitialize for Chart: '<S185>/Lib_CycleCounter' */
  UsgHis_Mdl_Lib_CycleCounter_Init(rty_nCycleOut);

  /* End of SystemInitialize for SubSystem: '<S6>/CycleCounterRollo' */
}

/* Output and update for atomic system: '<S2>/TravelCycleCounter' */
void UsgHis_Mdl_TravelCycleCounter(const boolean_T *rtu_isLearnComplete, uint8_T
  rtu_hMovPosErr, const uint16_T *rtu_hCurPos, boolean_T
  rtu_StateMach_isFactMode, uint16_T rtu_nCycleIn, const uint16_T
  *rtu_hSoftStopOpn, const uint16_T *rtu_hSoftStopCls, uint16_T *rty_nCycleOut)
{
  /* Outputs for Enabled SubSystem: '<S6>/CycleCounterRollo' incorporates:
   *  EnablePort: '<S185>/Enable'
   */
  /* Logic: '<S6>/LogicalOperator1' incorporates:
   *  Logic: '<S6>/LogicalOperator2'
   */
  if ((!rtu_StateMach_isFactMode) && (*rtu_isLearnComplete)) {
    /* Chart: '<S185>/Lib_CycleCounter' */
    UsgHis_Mdl_Lib_CycleCounter(rtu_hCurPos, rtu_hMovPosErr, rtu_hSoftStopOpn,
      rtu_hSoftStopCls, rtu_nCycleIn, rty_nCycleOut,
      &UsgHis_Mdl_DW.sf_Lib_CycleCounter);
  }

  /* End of Logic: '<S6>/LogicalOperator1' */
  /* End of Outputs for SubSystem: '<S6>/CycleCounterRollo' */
}

/* Output and update for atomic system: '<S2>/UpdateNVM' */
void UsgHis_Mdl_UpdateNVM(const StateMach_Mode_t *rtu_StateMach_stSysMode, const
  uint16_T *rtu_SysFltRctn_stsUnlearnReason, uint16_T
  rtu_UsgHist_unlearnReasonIn, int16_T rtu_MtrMdl_TEstMtrTemp, int16_T
  rtu_MtrMdl_TEstCaseTemp, boolean_T rtu_MtrMdl_TEstMtrTempVld, const boolean_T *
  rtu_AmbTempMon_isAmbTempVld, const boolean_T *rtu_MOSFETTempMon_isAmbTempVld,
  const boolean_T *rtu_MtrMdl_isStartupRdy, boolean_T *rty_UsgHist_isShdnRdy,
  uint16_T *rty_UsgHist_unlearnReason, int16_T *rty_UsgHist_TEstMtrTemp, int16_T
  *rty_UsgHist_TEstCaseTemp, boolean_T *rty_UsgHist_TEstMtrTempVld, boolean_T
  *rty_UsgHistl_TEstCaseTempVld, boolean_T *rty_UsgHist_AmbTempVld, boolean_T
  *rty_UsgHist_MOSFETTempVld)
{
  int32_T i;
  uint16_T rtb_Switch1;
  uint8_T tmp[14];
  boolean_T rtb_Compare;
  boolean_T rtb_Compare_a;
  boolean_T rtb_FixPtRelationalOperator;

  /* RelationalOperator: '<S190>/Compare' incorporates:
   *  Constant: '<S190>/Constant'
   */
  rtb_Compare = (*rtu_StateMach_stSysMode == StateMach_Mode_t_Startup_C);

  /* Outputs for Enabled SubSystem: '<S7>/UpdateResetReason' incorporates:
   *  EnablePort: '<S196>/Enable'
   */
  /* Outputs for Enabled SubSystem: '<S7>/InvalidateUsagHistVariables' incorporates:
   *  EnablePort: '<S194>/Enable'
   */
  /* RelationalOperator: '<S191>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S191>/Delay Input1'
   *
   * Block description for '<S191>/Delay Input1':
   *
   *  Store in Global RAM
   */
  if ((int32_T)rtb_Compare < (int32_T)UsgHis_Mdl_DW.DelayInput1_DSTATE_k) {
    /* BusAssignment: '<S194>/Bus Assignment' incorporates:
     *  Constant: '<S194>/Constant'
     *  DataStoreWrite: '<S194>/Data Store Write'
     */
    UsgHist_CaseTemp.isVld = false;

    /* BusAssignment: '<S194>/Bus Assignment1' incorporates:
     *  Constant: '<S194>/Constant1'
     *  DataStoreWrite: '<S194>/Data Store Write1'
     */
    UsgHist_MtrTemp.isVld = false;

    /* DataStoreWrite: '<S196>/Data Store Write' incorporates:
     *  Constant: '<S196>/Constant2'
     *  DataStoreRead: '<S196>/Data Store Read'
     */
    tmp[0] = 0U;
    for (i = 0; i < 13; i++) {
      tmp[i + 1] = UsgHist_resetReason[i];
    }

    for (i = 0; i < 14; i++) {
      UsgHist_resetReason[i] = tmp[i];
    }

    /* End of DataStoreWrite: '<S196>/Data Store Write' */
  }

  /* End of RelationalOperator: '<S191>/FixPt Relational Operator' */
  /* End of Outputs for SubSystem: '<S7>/InvalidateUsagHistVariables' */
  /* End of Outputs for SubSystem: '<S7>/UpdateResetReason' */

  /* Outputs for Enabled SubSystem: '<S7>/UpdateUsagHistVariables' incorporates:
   *  EnablePort: '<S197>/Enable'
   */
  /* RelationalOperator: '<S189>/Compare' incorporates:
   *  Constant: '<S189>/Constant'
   */
  if (*rtu_StateMach_stSysMode == StateMach_Mode_t_Standby_C) {
    /* BusCreator: '<S197>/Bus Creator' incorporates:
     *  DataStoreRead: '<S197>/Data Store Read'
     *  DataStoreRead: '<S197>/Data Store Read1'
     *  DataStoreWrite: '<S197>/Data Store Write'
     */
    UsgHist_CaseTemp.TTemp = MtrMdl_TEstCaseTemp;
    UsgHist_CaseTemp.isVld = MtrMdl_isEstCaseTempVld;

    /* BusCreator: '<S197>/Bus Creator1' incorporates:
     *  DataStoreRead: '<S197>/Data Store Read2'
     *  DataStoreRead: '<S197>/Data Store Read3'
     *  DataStoreWrite: '<S197>/Data Store Write1'
     */
    UsgHist_MtrTemp.TTemp = MtrMdl_TEstMtrTemp;
    UsgHist_MtrTemp.isVld = MtrMdl_isEstMtrTempVld;

    /* DataStoreWrite: '<S197>/Data Store Write3' incorporates:
     *  DataStoreRead: '<S197>/Data Store Read10'
     *  DataTypeConversion: '<S205>/Data Type Conversion3'
     */
    UsgHist_stHallState = (uint8_T)IoHwAb_GetHallInput;
  }

  /* End of RelationalOperator: '<S189>/Compare' */
  /* End of Outputs for SubSystem: '<S7>/UpdateUsagHistVariables' */

  /* DataTypeConversion: '<S198>/Data Type Conversion' incorporates:
   *  Constant: '<S7>/Constant6'
   */
  UsgHis_Mdl_B.EEP_USGHIST_UNLEARN_REASON = 5U;

  /* RelationalOperator: '<S200>/Compare' incorporates:
   *  Constant: '<S200>/Constant'
   */
  rtb_Compare_a = (rtu_UsgHist_unlearnReasonIn == 65535);

  /* RelationalOperator: '<S201>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S201>/Delay Input1'
   *
   * Block description for '<S201>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_FixPtRelationalOperator = (*rtu_SysFltRctn_stsUnlearnReason !=
    UsgHis_Mdl_DW.DelayInput1_DSTATE_e);

  /* Logic: '<S195>/Logical Operator' */
  rtb_FixPtRelationalOperator = (rtb_Compare_a || rtb_FixPtRelationalOperator);

  /* Outputs for Enabled SubSystem: '<S198>/NVMWrite' incorporates:
   *  EnablePort: '<S208>/Enable'
   */
  if (rtb_FixPtRelationalOperator) {
    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S208>/S_Func_NVMMan_enRequestNVMWrite_new' */
    /* S-Function Block: <S208>/S_Func_NVMMan_enRequestNVMWrite_new */
    UsgHis_Mdl_B.isShdnRdy = NVMMan_enRequestNVMWrite_new
      (UsgHis_Mdl_B.EEP_USGHIST_UNLEARN_REASON);
  }

  /* End of Outputs for SubSystem: '<S198>/NVMWrite' */

  /* Logic: '<S7>/Logical Operator' */
  *rty_UsgHist_isShdnRdy = false;

  /* Switch: '<S195>/Switch' incorporates:
   *  Switch: '<S195>/Switch1'
   */
  if (rtb_Compare_a) {
    *rty_UsgHist_unlearnReason = 0U;
  } else {
    if (rtb_FixPtRelationalOperator) {
      /* Switch: '<S195>/Switch1' */
      rtb_Switch1 = *rtu_SysFltRctn_stsUnlearnReason;
    } else {
      /* Switch: '<S195>/Switch1' */
      rtb_Switch1 = rtu_UsgHist_unlearnReasonIn;
    }

    *rty_UsgHist_unlearnReason = rtb_Switch1;
  }

  /* End of Switch: '<S195>/Switch' */

  /* Switch: '<S7>/Switch' incorporates:
   *  DataStoreRead: '<S7>/Data Store Read5'
   *  DataStoreRead: '<S7>/Data Store Read7'
   *  DataStoreRead: '<S7>/Data Store Read8'
   *  DataStoreRead: '<S7>/Data Store Read9'
   *  Switch: '<S7>/Switch1'
   *  Switch: '<S7>/Switch2'
   *  Switch: '<S7>/Switch3'
   */
  if (*rtu_MtrMdl_isStartupRdy) {
    *rty_UsgHist_TEstMtrTemp = rtu_MtrMdl_TEstMtrTemp;
    *rty_UsgHist_TEstCaseTemp = rtu_MtrMdl_TEstCaseTemp;
    *rty_UsgHist_TEstMtrTempVld = rtu_MtrMdl_TEstMtrTempVld;
    *rty_UsgHistl_TEstCaseTempVld = rtu_MtrMdl_TEstMtrTempVld;
  } else {
    *rty_UsgHist_TEstMtrTemp = UsgHist_MtrTemp.TTemp;
    *rty_UsgHist_TEstCaseTemp = UsgHist_CaseTemp.TTemp;
    *rty_UsgHist_TEstMtrTempVld = UsgHist_MtrTemp.isVld;
    *rty_UsgHistl_TEstCaseTempVld = UsgHist_CaseTemp.isVld;
  }

  /* End of Switch: '<S7>/Switch' */

  /* SignalConversion generated from: '<S7>/Data Type Conversion' */
  *rty_UsgHist_AmbTempVld = *rtu_AmbTempMon_isAmbTempVld;

  /* SignalConversion generated from: '<S7>/Data Type Conversion2' */
  *rty_UsgHist_MOSFETTempVld = *rtu_MOSFETTempMon_isAmbTempVld;

  /* Update for UnitDelay: '<S191>/Delay Input1'
   *
   * Block description for '<S191>/Delay Input1':
   *
   *  Store in Global RAM
   */
  UsgHis_Mdl_DW.DelayInput1_DSTATE_k = rtb_Compare;

  /* Update for UnitDelay: '<S201>/Delay Input1'
   *
   * Block description for '<S201>/Delay Input1':
   *
   *  Store in Global RAM
   */
  UsgHis_Mdl_DW.DelayInput1_DSTATE_e = *rtu_SysFltRctn_stsUnlearnReason;
}

/* System initialize for referenced model: 'UsgHis_Mdl' */
void UsgHis_Mdl_Init(void)
{
  /* SystemInitialize for Atomic SubSystem: '<S2>/TravelCycleCounter' */
  UsgHis_Mdl_TravelCycleCounter_Init(&UsgHis_Mdl_B.nCycle);

  /* End of SystemInitialize for SubSystem: '<S2>/TravelCycleCounter' */
}

/* Output and update for referenced model: 'UsgHis_Mdl' */
void UsgHis_Mdl(const uint16_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt,
                const uint16_T *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos,
                const uint16_T
                *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls,
                const uint16_T
                *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn,
                const boolean_T
                *rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld,
                const int16_T
                *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const
                boolean_T
                *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld,
                const StateMach_Mode_t
                *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, const
                uint16_T
                *rtu_SWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsUnlearnReason,
                const ThermProt_MtrTempClass_t
                *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass,
                const ThermProt_MosfetTempClass_t
                *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass,
                const MtrCtrl_MtrPwrd_t
                *rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_MtrPwrdState, const
                CtrlLog_MovType_t
                *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovType, const
                CtrlLog_tenTargetDirection
                *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const
                boolean_T
                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete,
                const boolean_T
                *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isInterrupted,
                const BlockDet_Stall_t
                *rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallType, const
                uint16_T
                *rtu_SWC_ObjDetLyrBus_ThresForceBus_ThresForce_FatsThreshold,
                const boolean_T
                *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isStartupRdy, uint16_T
                *rty_UsgHistBus_UsgHist_nCycle, boolean_T
                *rty_UsgHistBus_UsgHist_isShdnRdy, uint16_T
                *rty_UsgHistBus_UsgHist_unlearnReason, int16_T
                *rty_UsgHistBus_UsgHist_TEstMtrTemp, int16_T
                *rty_UsgHistBus_UsgHist_TEstCaseTemp, boolean_T
                *rty_UsgHistBus_UsgHist_TEstMtrTempVld, boolean_T
                *rty_UsgHistBus_UsgHistl_TEstCaseTempVld, boolean_T
                *rty_UsgHistBus_UsgHist_AmbTempVld, boolean_T
                *rty_UsgHistBus_UsgHist_MOSFETTempVld, int16_T
                *rty_UsgHistBus_UsgHist_MaxAmbTemp, int16_T
                *rty_UsgHistBus_UsgHist_MinAmbTemp, uint16_T
                *rty_UsgHistBus_UsgHist_MaxBattVtg, uint16_T
                *rty_UsgHistBus_UsgHist_MinBattVtg, uint32_T
                *rty_UsgHistBus_UsgHist_MotorOperTime, uint16_T
                *rty_UsgHistBus_UsgHist_CycRenormCount, uint8_T
                *rty_UsgHistBus_UsgHist_LearnSuccessCount, uint8_T
                *rty_UsgHistBus_UsgHist_LeanrFailCount, uint8_T
                *rty_UsgHistBus_UsgHist_StallCount, uint16_T
                *rty_UsgHistBus_UsgHist_ReversalCount, uint16_T
                *rty_UsgHistBus_UsgHist_SlideCycCount, uint8_T
                *rty_UsgHistBus_UsgHist_ThermProtCount, uint8_T
                *rty_UsgHistBus_UsgHist_ReprogrammingCount, uint16_T
                *rty_UsgHistBus_UsgHist_ReversalPos, CtrlLog_tenTargetDirection *
                rty_UsgHistBus_UsgHist_ReversalDir, uint16_T
                *rty_UsgHistBus_UsgHist_ReversalThres, boolean_T
                *rty_UsgHistBus_UsgHist_isStartupRdy)
{
  int16_T rtb_TmpSignalConversionAtUsgHist_MaxAmbTempInport1;
  int16_T rtb_TmpSignalConversionAtUsgHist_MinAmbTempInport1;
  int16_T rtb_UsgHist_TEstCaseTemp;
  int16_T rtb_UsgHist_TEstMtrTemp;
  uint16_T rtb_TmpSignalConversionAtUsgHist_MaxBattVtgInport1;
  uint16_T rtb_TmpSignalConversionAtUsgHist_MinBattVtgInport1;
  uint16_T rtb_TmpSignalConversionAtUsgHist_ReversalPosInport1;
  uint16_T rtb_TmpSignalConversionAtUsgHist_ReversalThresInport1;
  uint16_T rtb_UsgHist_nCycle;
  uint16_T rtb_UsgHist_unlearnReason;
  boolean_T rtb_UsgHist_TEstMtrTempVld;
  boolean_T rtb_UsgHist_isShdnRdy;
  boolean_T rtb_UsgHistl_TEstCaseTempVld;
  CtrlLog_tenTargetDirection rtb_TmpSignalConversionAtUsgHist_ReversalDirInport1;

  /* Outputs for Atomic SubSystem: '<S2>/UpdateNVM' */

  /* DataStoreRead: '<Root>/Data Store Read' incorporates:
   *  DataStoreRead: '<Root>/Data Store Read2'
   *  DataStoreRead: '<Root>/Data Store Read3'
   *  DataStoreRead: '<Root>/Data Store Read4'
   */
  UsgHis_Mdl_UpdateNVM(rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode,
                       rtu_SWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsUnlearnReason,
                       UsgHist_unlearnReason, MtrMdl_TEstMtrTemp,
                       MtrMdl_TEstCaseTemp, MtrMdl_isEstMtrTempVld,
                       rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld,
                       rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld,
                       rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isStartupRdy,
                       &rtb_UsgHist_isShdnRdy, &rtb_UsgHist_unlearnReason,
                       &rtb_UsgHist_TEstMtrTemp, &rtb_UsgHist_TEstCaseTemp,
                       &rtb_UsgHist_TEstMtrTempVld,
                       &rtb_UsgHistl_TEstCaseTempVld, &UsgHist_AmbTempVld,
                       &UsgHist_MOSFETTempVld);

  /* End of Outputs for SubSystem: '<S2>/UpdateNVM' */

  /* DataStoreWrite: '<Root>/Data Store Write' */
  UsgHist_CaseTemp.TTemp = rtb_UsgHist_TEstCaseTemp;

  /* DataStoreWrite: '<Root>/Data Store Write1' */
  UsgHist_MtrTemp.isVld = rtb_UsgHist_TEstMtrTempVld;

  /* Outputs for Atomic SubSystem: '<S2>/TravelCycleCounter' */

  /* DataStoreRead: '<Root>/Data Store Read6' incorporates:
   *  DataStoreRead: '<Root>/Data Store Read1'
   *  DataStoreRead: '<Root>/Data Store Read12'
   */
  UsgHis_Mdl_TravelCycleCounter
    (rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete,
     CfgParam_CAL.hMovPosErr, rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos,
     PIDDID_isFactoryMode, UsgHist_nCycle,
     rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn,
     rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls,
     &UsgHis_Mdl_B.nCycle);

  /* End of Outputs for SubSystem: '<S2>/TravelCycleCounter' */

  /* Outputs for Atomic SubSystem: '<S2>/ProduceDIDSignals' */

  /* DataStoreRead: '<Root>/Data Store Read12' incorporates:
   *  DataStoreRead: '<Root>/Data Store Read10'
   *  DataStoreRead: '<Root>/Data Store Read11'
   *  DataStoreRead: '<Root>/Data Store Read5'
   *  DataStoreRead: '<Root>/Data Store Read7'
   *  DataStoreRead: '<Root>/Data Store Read8'
   *  DataStoreRead: '<Root>/Data Store Read9'
   *  SignalConversion generated from: '<S2>/ProduceDIDSignals'
   * */
  UsgHis_Mdl_ProduceDIDSignals(PIDDID_isFactoryMode,
    rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp,
    rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt,
    rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_MtrPwrdState,
    rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete,
    rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallType,
    UsgHist_Cycle_Counter_stallCnt.UsgHist_StallCount,
    UsgHist_Cycle_Counter_stallCnt.UsgHist_SlideCycCount,
    UsgHist_Extend_Event.UsgHist_LearnSuccessCount,
    UsgHist_Extend_Event.UsgHist_LeanrFailCount,
    UsgHist_Extend_Event.UsgHist_ThermProtCount,
    UsgHist_MinMax_historylog.UsgHist_MinAmbTemp,
    UsgHist_MinMax_historylog.UsgHist_MaxAmbTemp,
    UsgHist_MinMax_historylog.UsgHist_MinBattVtg,
    UsgHist_MinMax_historylog.UsgHist_MaxBattVtg,
    rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isInterrupted,
    UsgHis_Mdl_B.nCycle, UsgHist_History_loggers.UsgHist_MotorOperTime,
    UsgHist_History_loggers.UsgHist_ReversalCount,
    UsgHist_History_loggers.UsgHist_ReprogrammingCount,
    rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovType,
    rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode,
    rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass,
    rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass,
    rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos,
    rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand,
    rtu_SWC_ObjDetLyrBus_ThresForceBus_ThresForce_FatsThreshold,
    UsgHist_Reversal_Event.UsgHist_ReversalThres,
    UsgHist_Reversal_Event.UsgHist_ReversalDir,
    UsgHist_Reversal_Event.UsgHist_ReversalPos, LearnAdap_renormCycleCounter,
    &rtb_TmpSignalConversionAtUsgHist_MaxAmbTempInport1,
    &rtb_TmpSignalConversionAtUsgHist_MinAmbTempInport1,
    &rtb_TmpSignalConversionAtUsgHist_MaxBattVtgInport1,
    &rtb_TmpSignalConversionAtUsgHist_MinBattVtgInport1,
    &UsgHis_Mdl_B.UsgHist_MotorOperTime, &UsgHis_Mdl_B.UsgHist_CycRenormCount,
    &UsgHis_Mdl_B.UsgHist_LearnSuccessCount,
    &UsgHis_Mdl_B.UsgHist_LeanrFailCount, &UsgHis_Mdl_B.UsgHist_StallCount,
    &UsgHis_Mdl_B.UsgHist_ReversalCount, &UsgHis_Mdl_B.UsgHist_SlideCycCount,
    &UsgHis_Mdl_B.UsgHist_ThermProtCount, &UsgHis_Mdl_B.Cast,
    &rtb_TmpSignalConversionAtUsgHist_ReversalPosInport1,
    &rtb_TmpSignalConversionAtUsgHist_ReversalDirInport1,
    &rtb_TmpSignalConversionAtUsgHist_ReversalThresInport1);

  /* End of Outputs for SubSystem: '<S2>/ProduceDIDSignals' */

  /* BusCreator: '<Root>/Bus Creator5' incorporates:
   *  DataStoreWrite: '<Root>/Data Store Write10'
   */
  UsgHist_Reversal_Event.UsgHist_ReversalPos =
    rtb_TmpSignalConversionAtUsgHist_ReversalPosInport1;
  UsgHist_Reversal_Event.UsgHist_ReversalDir =
    rtb_TmpSignalConversionAtUsgHist_ReversalDirInport1;
  UsgHist_Reversal_Event.UsgHist_ReversalThres =
    rtb_TmpSignalConversionAtUsgHist_ReversalThresInport1;
  UsgHist_Reversal_Event.reserved = 0U;

  /* DataStoreWrite: '<Root>/Data Store Write2' */
  UsgHist_MtrTemp.TTemp = rtb_UsgHist_TEstMtrTemp;

  /* DataStoreWrite: '<Root>/Data Store Write3' */
  UsgHist_CaseTemp.isVld = rtb_UsgHistl_TEstCaseTempVld;

  /* DataStoreWrite: '<Root>/Data Store Write34' */
  UsgHist_unlearnReason = rtb_UsgHist_unlearnReason;

  /* Outputs for Atomic SubSystem: '<S2>/CheckManufacturingConditions' */

  /* DataStoreRead: '<Root>/Data Store Read13' */
  UsgHis_Mdl_CheckManufacturingConditions(UsgHis_Mdl_B.nCycle, UsgHist_nCycle,
    &rtb_UsgHist_nCycle);

  /* End of Outputs for SubSystem: '<S2>/CheckManufacturingConditions' */

  /* DataStoreWrite: '<Root>/Data Store Write37' */
  UsgHist_nCycle = rtb_UsgHist_nCycle;

  /* BusCreator: '<Root>/Bus Creator2' incorporates:
   *  DataStoreWrite: '<Root>/Data Store Write6'
   */
  UsgHist_Extend_Event.UsgHist_ThermProtCount =
    UsgHis_Mdl_B.UsgHist_ThermProtCount;
  UsgHist_Extend_Event.UsgHist_LearnSuccessCount =
    UsgHis_Mdl_B.UsgHist_LearnSuccessCount;
  UsgHist_Extend_Event.UsgHist_LeanrFailCount =
    UsgHis_Mdl_B.UsgHist_LeanrFailCount;

  /* BusCreator: '<Root>/Bus Creator4' incorporates:
   *  DataStoreWrite: '<Root>/Data Store Write7'
   */
  UsgHist_Cycle_Counter_stallCnt.UsgHist_CycRenormCount =
    UsgHis_Mdl_B.UsgHist_CycRenormCount;
  UsgHist_Cycle_Counter_stallCnt.UsgHist_SlideCycCount =
    UsgHis_Mdl_B.UsgHist_SlideCycCount;
  UsgHist_Cycle_Counter_stallCnt.UsgHist_StallCount =
    UsgHis_Mdl_B.UsgHist_StallCount;

  /* BusCreator: '<Root>/Bus Creator3' incorporates:
   *  DataStoreWrite: '<Root>/Data Store Write8'
   */
  UsgHist_History_loggers.UsgHist_ReprogrammingCount = UsgHis_Mdl_B.Cast;
  UsgHist_History_loggers.UsgHist_ReversalCount =
    UsgHis_Mdl_B.UsgHist_ReversalCount;
  UsgHist_History_loggers.UsgHist_MotorOperTime =
    UsgHis_Mdl_B.UsgHist_MotorOperTime;

  /* BusCreator: '<Root>/Bus Creator1' incorporates:
   *  DataStoreWrite: '<Root>/Data Store Write9'
   */
  UsgHist_MinMax_historylog.UsgHist_MinAmbTemp =
    rtb_TmpSignalConversionAtUsgHist_MinAmbTempInport1;
  UsgHist_MinMax_historylog.UsgHist_MaxAmbTemp =
    rtb_TmpSignalConversionAtUsgHist_MaxAmbTempInport1;
  UsgHist_MinMax_historylog.UsgHist_MinBattVtg =
    rtb_TmpSignalConversionAtUsgHist_MinBattVtgInport1;
  UsgHist_MinMax_historylog.UsgHist_MaxBattVtg =
    rtb_TmpSignalConversionAtUsgHist_MaxBattVtgInport1;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_nCycle = rtb_UsgHist_nCycle;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_isShdnRdy = rtb_UsgHist_isShdnRdy;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_MinAmbTemp =
    rtb_TmpSignalConversionAtUsgHist_MinAmbTempInport1;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_MaxBattVtg =
    rtb_TmpSignalConversionAtUsgHist_MaxBattVtgInport1;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_MinBattVtg =
    rtb_TmpSignalConversionAtUsgHist_MinBattVtgInport1;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_MotorOperTime = UsgHis_Mdl_B.UsgHist_MotorOperTime;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_CycRenormCount = UsgHis_Mdl_B.UsgHist_CycRenormCount;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_LearnSuccessCount =
    UsgHis_Mdl_B.UsgHist_LearnSuccessCount;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_LeanrFailCount = UsgHis_Mdl_B.UsgHist_LeanrFailCount;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_StallCount = UsgHis_Mdl_B.UsgHist_StallCount;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_ReversalCount = UsgHis_Mdl_B.UsgHist_ReversalCount;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_SlideCycCount = UsgHis_Mdl_B.UsgHist_SlideCycCount;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_unlearnReason = rtb_UsgHist_unlearnReason;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_ThermProtCount = UsgHis_Mdl_B.UsgHist_ThermProtCount;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_ReprogrammingCount = UsgHis_Mdl_B.Cast;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_ReversalPos =
    rtb_TmpSignalConversionAtUsgHist_ReversalPosInport1;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_ReversalDir =
    rtb_TmpSignalConversionAtUsgHist_ReversalDirInport1;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_ReversalThres =
    rtb_TmpSignalConversionAtUsgHist_ReversalThresInport1;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' incorporates:
   *  Constant: '<Root>/Constant1'
   */
  *rty_UsgHistBus_UsgHist_isStartupRdy = true;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_TEstMtrTemp = rtb_UsgHist_TEstMtrTemp;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_TEstCaseTemp = rtb_UsgHist_TEstCaseTemp;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_TEstMtrTempVld = rtb_UsgHist_TEstMtrTempVld;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHistl_TEstCaseTempVld = rtb_UsgHistl_TEstCaseTempVld;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_AmbTempVld = UsgHist_AmbTempVld;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_MOSFETTempVld = UsgHist_MOSFETTempVld;

  /* SignalConversion generated from: '<Root>/UsgHistBus ' */
  *rty_UsgHistBus_UsgHist_MaxAmbTemp =
    rtb_TmpSignalConversionAtUsgHist_MaxAmbTempInport1;
}

/* Model initialize function */
void UsgHis_Mdl_initialize(const char_T **rt_errorStatus)
{
  RT_MODEL_UsgHis_Mdl_T *const UsgHis_Mdl_M = &(UsgHis_Mdl_MdlrefDW.rtm);

  /* Registration code */

  /* initialize error status */
  rtmSetErrorStatusPointer(UsgHis_Mdl_M, rt_errorStatus);
  UsgHis_Mdl_PrevZCX.NVMWrite_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.ReversalDirection_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.ReversalPosition_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.ReversalThreshold_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.WriteCycRenormCounterVld_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.LeanrFailCounter_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.LeanrSuccessCounter_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.ThermalProtCounter_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.ReversalCounter_Trig_ZCE = POS_ZCSIG;
  UsgHis_Mdl_PrevZCX.StallCounter_Trig_ZCE = POS_ZCSIG;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
