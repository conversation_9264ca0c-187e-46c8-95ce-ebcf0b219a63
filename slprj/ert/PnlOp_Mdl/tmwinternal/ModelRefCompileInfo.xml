<?xml version="1.0" encoding="UTF-8"?>
<MF0 version="1.1" packageUris="http://schema.mathworks.com/mf0/ci/19700101 http://schema.mathworks.com/mf0/sl_modelref_info/R2022b http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112 http://schema.mathworks.com/mf0/sltp_mm/R2022b_202203181029">
  <ModelRefInfoRepo.ModelRefInfoRoot type="ModelRefInfoRepo.ModelRefInfoRoot" uuid="eebc3ddf-f18f-4d1d-8550-10781ab5d546">
    <JITEngines>sksZIt22GzW3FHthhnubSOF</JITEngines>
    <JITEngines>ssBZMwT9ctjPAp9guTmhOoD</JITEngines>
    <calibrationData type="ModelRefInfoRepo.CalibrationData" uuid="4e3ed76d-daf9-428d-9413-968c4963f657">
      <InternalData>[{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;}]</InternalData>
      <ModelName>PnlOp_Mdl</ModelName>
      <RootIOData>[{&quot;Name&quot;:&quot;SWC_ObjDetLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_SigMonLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_StateMachLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_DataHndlLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;CtrlLogBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_HwAbsLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;PnlOpBus&quot;,&quot;Profile&quot;:&quot;&quot;}]</RootIOData>
    </calibrationData>
    <childModelRefInfo type="ModelRefInfoRepo.ChildModelRefInfo" uuid="4690abac-fef1-4fdc-96e9-738e17fa1359">
      <isSingleInstance>true</isSingleInstance>
      <modelName>PnlOp_Mdl</modelName>
      <modelPath>PnlOp_Mdl</modelPath>
    </childModelRefInfo>
    <compDerivCacheNeedsReset>false</compDerivCacheNeedsReset>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f3072654-c18d-4394-a034-f64750f934ba">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c418af01-9af6-4abe-b5ca-8fccd5ea52ef">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7d68d119-a8de-4c4b-a131-9713650ccbf9">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0c568fed-04c4-411d-8718-093c198e0cb5">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f9c3f36b-3a27-416f-a434-823439d2d14e">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="daf11dd0-8bf5-4552-84a1-b395126f96a4">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bbc6c3f5-b1ee-4488-8b1b-27aee516d271">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="33f12587-70e2-431e-902a-3e626ce48111">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b91133eb-66b8-411b-8ecb-5faa37549b1e">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="22d94f18-bb0a-4179-8c6f-e497ab68417d">
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="76fbba99-dfe1-4c95-bb3e-e2cf15e1ab4d">
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="386f0e25-52df-49ea-8b1b-770827b092e9">
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6e0a228d-85d9-47ca-9b94-78fc69faef82">
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a71ded35-03a5-420b-ad2a-6fbb7a5abc84">
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eebef459-7b0c-4e97-9ffa-9cda74f284c9">
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2e6711ec-8466-45ae-a5b3-a7290957bfcd">
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a4ad4423-8122-4002-b9ec-19cdbaf8f44a">
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="af704019-885c-4ef2-bb5e-99679c733686">
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5749953f-da9f-4015-95e2-52d1d00dff07">
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="333d2fb9-f2d6-464e-b070-7cdc1160a3f4">
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eebfd458-01e5-42f4-8882-08a145de58ab">
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="51a4f082-3cb3-41e7-a4d7-9c13aae87267">
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="437ce5c1-171e-4a64-bb25-07ca9f9f51fb">
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b45ad5d1-fa8d-4cfa-b553-e7d9fc0c3590">
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="27fbb5fb-96fe-4188-8e9c-9fcfbebd2894">
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="edbae034-1c49-4378-a493-9c4731e66711">
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f48766b9-b52d-4f79-8bdf-665fd2e16b46">
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0025b831-8ceb-4f7f-afb2-f7140720ced4">
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0cf7c855-4733-416a-900a-52eae17842ab">
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3b9830d0-35d9-49eb-8fa3-b6274a1eccfd">
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9260d25a-ef9c-40af-906b-dba0e3e47bda">
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="64397121-69c1-4a4d-802d-27e87fccdcd4">
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fed42505-bf45-43fc-9fc0-44a11f707dc3">
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9652cee8-3df1-42fe-9fb0-8bdb40da2b88">
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ba4f71dd-d13f-400c-a979-b7d0c6458d29">
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="64ac7d73-e1db-48c5-a5b2-90a8f31c1cd7">
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8e963080-acf2-43ae-859d-aff3136e7c08">
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="acc50a3c-7b2e-48d8-9d6d-717d54074e8a">
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eb5e60cc-ce07-4d3e-9ce0-cb5b4138e53d">
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b41d6146-22f6-44d8-82d9-7e23adb74b1c">
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7e7e9eba-5be8-4def-bf3d-831a6431459f">
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c02abe3d-3b66-43f4-80cb-e1dfd1391436">
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a32e8d34-5282-4f19-bd1a-7b2015023066">
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="96d7cf28-c50c-4636-a8a6-8f885bef9ee8">
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3f303ed6-345b-4b82-aa5f-90654ac30efc">
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6209237e-7a8f-412e-bf02-5a8b12f66947">
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5097f027-7d7c-4521-ac4f-428c95297858">
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cd672ad8-0431-4a32-8cb5-162dcbc84f33">
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3324e7b6-8826-4c88-af13-d00d69119069">
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="484ce12f-5450-46d2-a21c-d9f788aa3cad">
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5ecc2c3a-fbe4-4b5e-8361-b096f9b7f349">
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3aff7855-6926-4dab-a839-1d297dd13994">
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e195293d-7280-4070-acf1-3b8107701486">
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="75dc4d35-d86d-4d59-b0c2-b81a8fbfcd84">
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fd068eb5-8e35-4aac-a284-6fd9bd6d0669">
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e2b1ed41-f823-476f-85d7-fabd6c4971f2">
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5b6bcd8d-8a0f-4015-a1ed-b9e4301eaa9e">
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="41f39a72-fcd7-4963-8020-c52940860d39">
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="056e7ef7-5281-4fb2-84f7-31c7c65592c1">
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7d4af3ce-9064-4295-8b3b-3fe4d065fb47">
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="59b6b788-a9b5-48e2-9c27-c757aa51268a">
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f375f3af-e92b-47d6-9916-a067dad9d82b">
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c447d3d6-a65b-4f1a-b6d3-ffb2ab9e206c">
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7501d0db-bbcd-4e07-8bbc-357d1e6226d4">
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6de4f163-c0b2-4b87-9fcd-f58873db008f">
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="25ff7c0d-be3b-4c18-a40a-31a204b583d0">
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="647b650f-19e3-4050-80b6-dcfd80a258d8">
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e2cc975e-81d6-4b55-967d-a2452a021228">
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="45b318f8-ca91-4871-92d3-032d838b1a19">
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="98eebab8-e179-4731-a384-2adcd53e59cd">
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="108cb01e-b81c-4206-95a7-c309ebaf39d9">
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c3fcf2ed-7775-4c1d-b406-4535ad73ee7e">
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c4de4686-39b5-4a55-9ac1-9f176a843c4e">
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4f975f3c-3071-4524-92f8-6fd2297bb62b">
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8efcbcdd-8167-4f54-8dac-1784dabede08">
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ec451f13-2830-496b-990e-faa0c86c70b4">
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7e3f8534-d785-41c9-bcb4-b4c420f1c97b">
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c570e506-084f-4709-8d44-479b90e04975">
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5d3b3a1a-2223-46d6-9cc5-53465ed2669b">
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fabd7ded-ae6a-4a15-b7d9-5ec90319a125">
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="52649f08-814b-48e5-bbc3-85862179ce02">
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c742c89c-8560-4245-a542-0645a02c88c4">
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d8f7f309-69c5-4ee6-b398-9f55d77bc891">
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7f3d0a7b-b67d-4373-8324-03afe7aab4bb">
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0b2beb27-e4c4-4294-9c7b-1b5a82409635">
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fb74354c-3e29-4a5d-a9cd-a5e2b9fe9357">
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ae7de022-174d-4cb5-8847-d3468cbac462">
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4996530a-ec87-4c19-871d-edfbe6f8bdf2">
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0870b73a-e883-4498-a1d8-326b78257995">
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d6f1c721-4beb-4f78-87b3-77103a1abd23">
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ffed5509-d996-4d5a-93c8-10de015c979f">
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="236db5c2-33dc-4c1e-830f-e85be21ba2a7">
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="38a3c0d9-a91e-4801-bd25-184de72cecf0">
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5501a197-e1d1-46c2-83ae-0bb3909d01c4">
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f42069a7-1f5b-4d07-850d-3c126c894bd5">
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f6093569-3632-4be8-931b-7e64bfcbc56d">
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="dbd23bfc-fca1-46cf-b3b1-195e5f897409">
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9fb5a547-7c80-49ab-b831-d79b381d0dc7">
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f757f6f1-fde1-463d-bab3-99e8b2b26535">
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0b61bf6a-139e-496b-a8e6-9a83a6f43bd3">
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bb769bb9-6d7a-41c8-bb17-79224584daad">
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="042d1c5c-d67a-4479-926b-242d093631e8">
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b1203a25-3d22-417e-a425-1a4ea29ecaa3">
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d739ffde-75af-4156-8c1d-6d237af0714c">
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0d9ed9cf-5663-4555-93c0-60b60d28d8ba">
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f47fed9b-6aa8-4676-82e9-69916e9da486">
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="55259cd8-06b3-413c-a8af-41cc5a458249">
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="45108773-cd6a-4073-8f16-a2686697cb6b">
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="83ad0f88-1fb4-4589-a629-b665b42460f6">
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3a00742b-7f7e-4e92-b136-75a822685bb3">
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="de802cbb-28ae-4962-a35b-056954e2a671">
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fe2f053e-36e9-432f-8537-d0e414150e06">
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e35f2ae6-f699-4150-a7fd-d86159afb516">
      <grAndCompPorts>112</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="14f0a1f5-fc72-45e8-ac28-74da9765fb54">
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="da9d4f8b-ffb3-47e0-931a-bb4fe1bac18c">
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c2364244-cfdb-4bf4-b5bc-3f3ad13e2be6">
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f65dd276-5a6c-4c5a-adc0-3a6afa959b38">
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4e2ff790-cc0b-4a1f-a8f9-53551112fe78">
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="05832331-d20f-48cb-8afc-e20b2c5e9ce1">
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c4c2f135-05ab-4954-a326-3412dc38ed38">
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="feda00b5-5f11-4b3c-ae0e-9563706d0779">
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="02b0f25f-2747-4d99-8b4a-80581646eed2">
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c2c872fc-d40f-4247-bd38-0402a6c3c311">
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2b3f403e-d391-4662-a252-3629f6bd810e">
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e82b5f85-cf5a-4708-a57c-99c0f9939873">
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bdfa3a0b-dd96-4864-b8a4-41249755703b">
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="82c2997d-d0a1-4d57-9bc4-781cc0bd8c85">
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8c6ee906-9a6a-4a1f-b367-60b6c8ff4f60">
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9cd209c4-2d06-480c-a26a-4faf7b1eb9e9">
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a9fd449d-3b05-4bad-b436-66010b649a83">
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="243b6b8c-b133-4638-b38a-515529d644fe">
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="65d67e00-b93e-4a73-8387-37282251e707">
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="89040264-24c0-4a2c-b11a-f3dd62cd6050">
      <grAndCompPorts>132</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7e9c4767-f37f-42f7-bc19-5235bf82b8cb">
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7f196025-58f2-4e36-a143-f4312be36506">
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4eb6fde9-3eac-46df-938e-30f33c84e753">
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ef982bbc-762f-4b72-9089-e2ce5be50b7b">
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7aacf193-e0e9-4581-aa8f-7bb872b06050">
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="911c4fe3-5443-466f-b10d-17d2f5a86ce3">
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2133306a-b61b-4e1a-8e93-da9cfe6d2f7d">
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1c48c59a-cf54-4a49-8840-e1f8f371d5b9">
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="82069f7c-8f2f-4dbb-9913-0574d9e861ec">
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="95a0504e-2c6b-48e2-8ce6-562d7302f427">
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bccd940b-a8e5-4c5f-8b73-c64c54e2a288">
      <grAndCompPorts>143</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2d1f213f-75c7-4210-bc86-e239558fb254">
      <grAndCompPorts>144</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="dfa8057a-b57c-4b97-908d-048a6b623dc6">
      <grAndCompPorts>145</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8810fa6b-3582-45e0-8b4e-e8db2356c793">
      <grAndCompPorts>146</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="650cc309-ae11-4e61-b6f7-a0b1b4101e69">
      <grAndCompPorts>147</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="73ec5f5f-59d7-427c-8848-223eeaf96cff">
      <grAndCompPorts>148</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="159ef754-5ab7-4641-88c1-a0e6bc7e84db">
      <grAndCompPorts>149</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5f3f7a61-a57b-4294-8ca9-ae30aa447147">
      <grAndCompPorts>150</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ac26244d-0914-414a-9221-84f7053397ce">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="24cb679e-4753-40bc-b6c4-d0bf69b6b086">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0c96030a-64e7-4cc7-9192-8d2012ff4142">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="121d67fd-f656-41fc-9c0b-b5068a1e406b">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4ed5808c-136e-4587-b8e5-27cccda63d52">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="dfdad571-bc32-44d0-94d9-ce939c7e5ad7">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cdb1be4f-dbfc-4154-b076-0a0816e11690">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compZcCacheNeedsReset>false</compZcCacheNeedsReset>
    <dataDictionary>PnlOp_Mdl_Data.sldd</dataDictionary>
    <dataDictionarySet>PnlOp_Mdl_Data.sldd</dataDictionarySet>
    <dataDictionarySetForDataTypeCheck>PnlOp_Mdl_Data.sldd</dataDictionarySetForDataTypeCheck>
    <dataTransferInfos>AAFJTQAAAAAOAAAAOAAAAAYAAAAIAAAAAgAAAAAAAAAFAAAACAAAAAAAAAABAAAAAQAAAAAAAAAFAAQAAQAAAAEAAAAAAAAA</dataTransferInfos>
    <defaultsCMapping>{&quot;Inports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Outports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ParameterArguments&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;LocalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InternalData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedLocalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Constants&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;DataTransfers&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ModelData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InitializeTerminate&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Execution&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedUtility&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;}</defaultsCMapping>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="cf79bf04-3ada-4b69-b4de-0835e6b52a5e">
      <blockName>PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/ClosedLoopPIDController/integrator/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>76</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="edeb2cdb-1a8d-4c15-83d3-83c8d8dcbbff">
      <blockName>PnlOp_Mdl/PnlOp_Mdl/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>54</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="699961d1-a78a-4e8d-8723-bd54802d37e7">
      <blockName>PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/Unit Delay1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>54</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <fundamentalSampleTimePeriod>.2</fundamentalSampleTimePeriod>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="e2f21473-6d15-4482-ae97-7bfafb098ea7">
      <dataType>CfgParam_CALBus_t</dataType>
      <name>CfgParam_CAL</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="1adc47c4-325d-4028-a5c6-f035e464b91d">
      <dataType>uint16</dataType>
      <name>PIDDID_SoftStartPoint1Time</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="9c652f19-295c-4fa6-8cb3-773b7421bade">
      <dataType>uint16</dataType>
      <name>PIDDID_SoftStartPoint2Time</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="de929241-2857-4e63-95ab-e47775cdead0">
      <dataType>uint16</dataType>
      <name>PIDDID_SoftStopPoint1Time</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="c3b628d9-76d6-4d37-a423-86446cb9f4cc">
      <dataType>uint8</dataType>
      <name>PIDDID_MtrDutyCycle</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="177c4cdc-bb20-42b1-96ca-c27e3ee78c13">
      <dataType>uint8</dataType>
      <name>PIDDID_SoftStartPoint1Perc</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="38982cdb-7cea-4447-a7bf-168b43d79105">
      <dataType>uint8</dataType>
      <name>PIDDID_SoftStartPoint2Perc</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="905a94e9-b4ae-42f9-8b87-4e77c33da23b">
      <dataType>uint8</dataType>
      <name>PIDDID_SoftStopPoint1Perc</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="704c682c-1e72-452a-a4ce-49a8f2160063">
      <dataType>boolean</dataType>
      <name>PIDDID_isFactoryMode</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="53b0c945-d7c3-4f75-bd6d-ca4de3e1289f">
      <dataType>boolean</dataType>
      <name>PIDDID_isOverridePWMRtnBusy</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="fd8ca5fc-7f06-4068-934e-0257f41ca28b">
      <dataType>boolean</dataType>
      <name>PIDDID_isSoftStartEna</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="4ecbf0e2-629b-428c-ad7c-7b09dc2d5ac8">
      <dataType>boolean</dataType>
      <name>PIDDID_isSoftStopEna</name>
    </globalDSMInfo>
    <globalVariables>#ADCMonBus#ADCMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#APDetBus#APDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AmbTempMonBus#AmbTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#BlockDetBus#BlockDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParamBus#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CAL#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CALBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_MosfetLevels#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_hPosBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#Configuration#ModelConfig.sldd#</globalVariables>
    <globalVariables>#CtrlLogBus#CtrlLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#DIOCtrlBus#DIOCtrl_ExpData.sldd#</globalVariables>
    <globalVariables>#DiagComManBus#DiagComMan_ExpData.sldd#</globalVariables>
    <globalVariables>#ExtDevBus#ExtDev_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#HallDecoBus#HALLDeco_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#LearnAdapBus#LearnAdap_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MOSFETTempBus#MOSFETTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdlBus#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdl_CalcForTorqBus_t#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_MtrDutyCycle#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_SoftStartPoint1Perc#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_SoftStartPoint1Time#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_SoftStartPoint2Perc#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_SoftStartPoint2Time#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_SoftStopPoint1Perc#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_SoftStopPoint1Time#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_isFactoryMode#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_isOverridePWMRtnBusy#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_isSoftStartEna#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_isSoftStopEna#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PWMCtrlBus#PWMCtrl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PnlOpBus#PnlOp_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PosMonBus#PosMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefFieldBus#RefField_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefForceBus#RefForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RoofSys_tSmpPnlOp_SC#RoofSys_Data.sldd#</globalVariables>
    <globalVariables>#SWC_DataHndlLyrBus#SWC_DataHndlLyr.sldd#</globalVariables>
    <globalVariables>#SWC_HwAbsLyrBus#SWC_HwAbsLyr.sldd#</globalVariables>
    <globalVariables>#SWC_ObjDetLyrBus#SWC_ObjDetLyr.sldd#</globalVariables>
    <globalVariables>#SWC_SigMonLyrBus#SWC_SigMonLyr.sldd#</globalVariables>
    <globalVariables>#SWC_StateMachLyrBus#SWC_StateMachLyr.sldd#</globalVariables>
    <globalVariables>#StateMachBus#StateMach_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#ThresForceBus#TheshForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#UsgHistBus#UsgHis_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#VoltMonBus#VoltMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#fixdt_MaxMotorSpeed_C#PnlOp_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#fixdt_MinMotorSpeed_C#PnlOp_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#u16_maxVoltageAllowed#PnlOp_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#u8_MaxPercentage_C#PnlOp_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#u8_MtrSpeedZoneRampOffset_C#PnlOp_Mdl_LocalData.sldd#</globalVariables>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4e2a8c18-5635-402c-b906-d881c010d47e">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>35</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ea1bf636-9a4c-4413-9daf-6d88f9e5ab57">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>70</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0a3f6a8e-b9bf-4631-b2c2-dc5f62ac1cdd">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>71</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="298840e7-3ada-4794-a520-1ebd5dd60a8b">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>112</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b764ffa5-2b1e-4f3a-8a56-21770d4fc238">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>119</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="304fa298-842d-4087-b1d5-2565440b0770">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>132</grAndCompPorts>
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>143</grAndCompPorts>
      <grAndCompPorts>144</grAndCompPorts>
      <grAndCompPorts>145</grAndCompPorts>
      <grAndCompPorts>146</grAndCompPorts>
      <grAndCompPorts>147</grAndCompPorts>
      <grAndCompPorts>148</grAndCompPorts>
      <grAndCompPorts>149</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ecbfc499-2e28-4fae-b657-9c570d810671">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>150</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompOutputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="09810803-f2c6-4807-93a5-c41c003b3ca7">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </grToCompOutputPortsMaps>
    <hasBwsAccessed>true</hasBwsAccessed>
    <hasBwsAccessedByAnyModel>true</hasBwsAccessedByAnyModel>
    <hasConstantOutput>false</hasConstantOutput>
    <hasModelWideEventTs>false</hasModelWideEventTs>
    <hasNonVirtualConstantTs>true</hasNonVirtualConstantTs>
    <hasStatesModifiedInOutputUpdate>true</hasStatesModifiedInOutputUpdate>
    <inlinedVariables>#fixdt_MaxMotorSpeed_C#PnlOp_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#fixdt_MinMotorSpeed_C#PnlOp_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#u16_maxVoltageAllowed#PnlOp_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#u8_MaxPercentage_C#PnlOp_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#u8_MtrSpeedZoneRampOffset_C#PnlOp_Mdl_LocalData.sldd#</inlinedVariables>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="757145b4-a72c-4930-8bef-9c167044ccad">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dffcd55c-c2f8-4ee6-98e5-a976e039055a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8005b05d-0a02-4e2d-ae95-fb5c3373d737"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="63967d0b-6963-4a9b-a783-b4acb530e7a1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ecf3aeaf-3a33-4f2d-9493-a2539948fd42"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9ae47702-4309-4e31-9d59-83abbf63b9fa"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="48b023f0-1132-4f1c-9788-25736e931f41">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ddfcfc8b-5847-4baa-b14e-d32d9728ab31"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5c83243f-9651-4919-bf21-83d896807c6d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0d134d5c-ec17-4837-ac5d-5880a56e01c7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="58de8f8b-2bfc-484c-89d1-558e19654d21"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="99e464c3-2b73-4b0f-9ec6-199bc4896fc1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f86cea58-2845-46f1-b589-ccef5022ad6b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="103760c1-5fe8-4313-be4a-71236d7f89dc"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="aa08d77f-48ef-40e2-b1ed-645b175b6ef8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="aa67251c-e69d-4085-abe9-03925bfad1f8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dc8a4c83-4ca0-4896-adc5-665808ba8f86"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8ccfca74-28b3-42ab-880b-750c54c40fe0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="75e26f10-4cea-4eff-9e04-54f7f30fa7ec">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9e0bcc91-7cfb-4894-999f-6a97f6028cb2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="467b0d44-9acc-444c-8eb0-9f3adf35269e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4052f913-99cb-4d0a-b562-a01f0fc75bbb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="50fb3c61-df24-45d6-9f88-fea13e54b004"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c4f31854-842c-448a-b94d-5bc02be791e7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="edd4b5a7-8b5d-400b-aa7f-5d931dd70fb5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ba4ec118-1d38-4bea-8100-9640a82f0db3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8e79f998-30ec-4538-9ef7-06c986ab840d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="73a34fa9-7006-4611-be7b-321cfeca147d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="aa22110d-8510-45be-80e7-04ffc924da67"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a1f49732-fd90-4c24-bdc6-a07970fa1de3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d4f0ee7d-b148-4a88-a42c-021022954a47">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3358bd1e-5d7a-4de6-ac89-c5b23237e0eb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="665bc9b1-1d6e-45cb-a0cc-b5f559f14d16"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d7b341c1-347d-4d9d-9274-8a50bdb1dd0e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e65bf69e-6f09-48b1-b1fe-a7320aac0e56"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="580b08e5-ccb2-4492-8dae-84b268097387"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="049f4ed0-1f49-4f1c-9df8-786991caea58">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="78e1ff60-56db-4539-aa9c-4a231635872a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c313c76e-0a8d-4107-8022-2c0942d77343"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5f60fa92-3f71-4dc9-b36a-212a88d773a7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bf043797-2c46-4476-a601-80f46b4dbaf5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ad54854b-4c1a-496b-b0ec-0a36f2e05235"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ee86b25e-123d-449b-b521-aba9058e1ab4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7488f5fe-4284-496f-9407-22865e28be9c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1d1e1f6a-619d-48fd-8f89-7c2547163266"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="64591e0f-8282-49ff-89ee-c746fbbe7758">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bf09d33f-4c0f-47d9-a9fa-b128b059674f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f047b180-05f9-45bb-837a-6733b7d6b2f1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f74d5cc3-9777-4311-a209-b4459a1d5268">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a3bfb7e3-c66c-4e0c-acc7-4947f2e08f64"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fc86c04c-fb8a-458c-b8a9-c8834d58b52b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="46df5225-c1a0-456e-9d94-2557966044ba">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2ac8a1c8-4d06-4328-9e81-fe30a1e032c8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2d3cd224-64a4-4211-a61b-68769612ac11"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f0e4026c-5968-4ff5-a4b0-f87dbd29abb6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3dac159a-85d4-423e-a308-14485e25384f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b382ad90-cac0-47ee-a9bc-459dbccf742d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4c5f6b00-25d0-4051-989f-fa472552876c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="baa2d7df-3758-456a-81f6-2eff9571fae4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d9684867-40bf-4e90-b16c-719470af4653"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6a4c43df-b91d-4964-9312-5795d8fdaaea">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="74d4a5be-9ae0-42a2-9dd2-daaf0063a0f1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6fe85b07-fcf7-4e81-a0eb-765d6319cea6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f0e33845-ebc3-448a-83ac-89b85401a929">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b8a9a570-de09-4052-91eb-7022d0b455ff"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9fb58e32-4e00-4c9e-9e24-4846fb3ca713"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="36642662-5e2c-4fb8-9a7b-e2c8922cbac1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="db914de4-43ba-4ce8-b939-9d3b6f1b5a56"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bf16af25-a663-4c5c-934f-0d830c101bc9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7084b6e1-033f-4345-bd8f-cfe24a519f0e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="69fb7f90-7a4b-4485-89e4-5e35265d5dc4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="68a24688-0c25-4a18-8106-e95d883ee1cf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3072aa4d-b39f-40cb-9522-da60511f301b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>32767.0</designMax>
      <designMin>-32768.0</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="10c53665-5f49-443f-aa4b-a4aba6a835a0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="43a81fe8-8b0d-4877-9e53-ea9e75b1934c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dd096553-5510-4101-89a0-56667471f53d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="49047549-e054-4cd9-98bf-06268d8d2100"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ddab9998-5884-4176-9d54-9c540f79ef34"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4dd0e3e7-ff28-4a63-89cb-866d0a713ace">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d7c97d40-cae8-4923-ba8c-8d3eae1c24b9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="60ca8913-4f94-4b17-9906-d34dc9706945"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2ed4e67c-81a3-4793-8c43-7bf46ac14f8d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>32712.0</designMax>
      <designMin>-32712.0</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="142a32bd-d1e0-4965-8ca4-dbdecafa89c5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1815910e-67b7-4b25-a3f0-f1f8185155b9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="313fb54d-5b81-45c2-89ac-d45340c61100">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>32712.0</designMax>
      <designMin>-32712.0</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="51b39c62-4e48-4e54-9112-a0d88a36b419"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8bfd96c9-5ca9-4170-ac59-3337444e17a3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8a7e97f4-12fa-4cd2-a25e-c5cd215ed005">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7bf63611-afe3-4036-bb08-dea14034307e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6d4f3fca-93d1-4709-8966-d2e10c7c3240"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="08916f09-ae43-46c9-bc59-e1b06d0acdc4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="587c85df-1e54-4d99-a542-606d43708112"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0eef1942-17ba-4974-97bc-9845934dd5c0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ab6917c6-134b-4c17-8eeb-ae20416aa8d7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7c9641a8-30d3-464c-a714-e6e283799e7d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6c2b8d53-f7d9-4f04-83df-5223948b7607"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="366f4bc6-24fa-42b1-a44c-2b55a843e91c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8cb350ec-b528-45e6-b849-ee0ca10457a1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="980c4213-164d-4234-967d-41ccdb13b0af"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="335aa511-965b-4cb5-a885-d2a2906b942f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="68bf572a-3f48-418f-8e96-aae3d98b2d7f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9a427367-17f0-4cb6-be34-3418cd55233d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1c4c620c-0463-4c08-aab0-74bf9b0d4ed4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d4ab93b9-3b39-4bd6-b2c4-89c35f31f0ef"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="45c5a310-d37a-4706-89f2-eb532cc5487d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e941e884-7b47-4351-a569-31ec220567d4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="afb2f973-6809-4f6f-9545-3e69a439e44a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="39c8c860-7c64-4e62-a581-e9bc1c92cf0e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="066d5f61-672f-411e-bd25-58e8c6338260">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8d2e4fae-5f6e-4296-9679-85b2ea75548f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e9f8c9d6-5adc-482c-961c-29d2674c007a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="81e0095c-4df1-480e-b244-ccdc3224d3b4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1b294dce-fdb3-4970-9340-ee7949f72e3b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="df08e07c-c53e-48ae-b7b6-80701595bd8d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f73515ba-1807-4709-ba27-b89d438aa444">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="66ec08f3-96ea-4a98-a2e1-533572cf71cf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="031a7e89-9a94-4431-b1dc-ac17b709f523"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e4d0cebf-2d23-41d3-a593-3c5642edcaa9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9fedd88f-a78a-4b93-bb84-1b2c9ed7a9d1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="38300dd1-f9a3-477f-8ac6-38dfa76222b0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="46d78995-5a8c-4593-81bb-bf8a5a16f9ee">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b2c79a50-7266-464f-b1ba-782fed2b08ce"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3e368c34-cc55-439d-b4e4-cce6277dcfd0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="90b386e2-e893-4849-b386-01d3500de273">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="06b2733b-15ab-46e6-abb9-bbed3a6b5b9e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a77cdcd4-1ef1-449c-a8e6-7958f0ec4b2f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cd59b5b2-d17b-4021-a855-603e44434c92">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="45e306e5-1367-4862-a06f-e00c0fd04aa8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7efe4883-acbf-4419-9035-f10548ae1f0b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="919afa5b-596f-44f6-ba8e-d51e9453e77b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9ded0e08-eab7-484c-b4cd-0b24bbc92bb3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1c22b448-36a0-4d47-9b1f-b5c4bd9d89a6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="48afb5cc-021f-467f-ab9e-5fd877e43ae6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8e307577-1522-4806-84e2-9de5abd3b18f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="64ea93d4-faf5-4dd9-96b6-52a76c25a32f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8ad1b8ad-11d0-4782-848d-80b2e3e83cb7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d9aca922-db29-4ab8-96e7-d6a34c030ec1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b903d57c-6bf6-4b10-9c37-ab25564440c0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="30b83ae6-61cc-4b3b-b450-a9f5aafda7b3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c6887f13-e8f5-42b2-b339-25953d808738"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6924d883-47d4-4787-ae1d-00e03811f3fc"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6c0a73d8-746c-4c3c-809a-b1c5285cdda5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b1e2d1ec-60cf-4579-a654-fbe257df8a44"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="75428132-397f-4d13-a1f8-2ebb3736ca9e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d11d6bf1-884c-4872-9315-9f9f1dffe1b8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="caec2d87-0900-4ac2-bcb8-9f17582e0e5d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="70082eb2-8597-4b9d-b454-874a0dba12a5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e3f64ff1-20b7-4964-acf1-d43da1c38cd3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="68bcd3e6-f264-4946-aa20-9c574cf1dc86"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="aef4f034-945b-4fd9-9ddb-07b0265c61ec"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="679cae77-04de-4b59-aad1-e7131adfe898">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c3ef5ed1-65ab-4796-8f03-136582ea42af"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="05747b42-1681-4d82-a92b-80295ad5d516"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="30759509-65de-459e-a6ad-24007dfe9b79">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="035ff667-9d87-4a9b-b020-438811bb30ac"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d5387c8f-73e2-4dc4-8a2a-39bf3969713a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="585fcb1a-3aa2-4d0d-896d-3450e2cab195">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="db7dfd46-6451-4f89-8fb7-f3854c9eb772"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="71238ba5-1297-4a23-835b-b8df10182c9d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="73f8ad0d-e875-4917-a173-8a6abfbc6bfe">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b516e04d-7e96-4278-bdc1-5ab4c5b23ec4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5fcef8e3-cd64-48fa-97de-046392ae0493"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2cb67d36-1be9-4fdd-82c0-9273b0ccc90f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c0f51d5e-d1d5-440a-a71b-0f305d5d73b1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="599b30f0-b6c2-4ed3-af7d-0f2f1a4b9d7a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="096d8484-b02f-481b-abee-38558b7a8a3f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="81b10d6f-013a-450a-8188-7582d5c840a0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1a724087-bf86-48b1-a63f-b33426553243"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="435f9e6a-9811-4668-9ffc-d269ece878f2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8a2af0b2-3093-47c9-b9bd-5876fb7c03b8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="de256a94-644f-44ee-9766-4b47ec34014d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b96c0d10-a2dc-488a-a7a8-fb2dc3e17225">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="33144f2e-6aaa-411f-8415-6426f484e9e6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ff4cc257-cb17-43ae-9567-7758d47f51a8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="963ea8b4-5c81-494a-820a-0a7fdb989938">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2d773de5-51db-4c4f-bdbd-873a13e575fe"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dcb0ece9-914d-4f16-99f5-03cfc26306f2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d262ac84-a01f-4e79-83aa-3954e2bee57a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="55b1352b-0f14-4194-a958-ea25b7a6f359"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8cea42a4-ec1d-4ee2-bc9e-5d7bd1e0b4b4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="85c6f7ed-8d07-4d90-952b-df4b73010385">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4aa7e2f4-4b40-4b4c-a565-05e260fd519e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="97d8ef54-bb86-4245-b4a3-40cfc3cc4fe0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3c01a44c-3658-4e26-9ff0-9e8e2565f002">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0fcd0908-6c01-4b26-b31f-bd2c879cb3a2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b8a5b0a9-2a75-40ac-b298-888ee58a2904"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="781d5d2c-652a-4645-8f74-9e9e5dbd5b12">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="816a4748-0485-487d-bd26-db27f748ee0a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c849903d-c7cd-4917-9291-128bc9cb9adb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bd0d35fd-2c48-4d8a-bb71-aef86b2a35b8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="619ee816-f1a8-400b-b8af-ca855a430ce3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="43775c18-649f-458c-a13b-7bcda0860133"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9e364de8-534b-46e1-8fa7-3acb5c600ead">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="690e0431-9845-4922-86ad-dfbb21ae69ca"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="00b58262-6003-4717-ae6b-af6c98306252"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2351f8a1-6e7e-451f-848d-fccbec8042ef">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="47de22ef-dbcc-4bbf-8af5-a131a1eb1990"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a12c1da5-16d3-4f72-ad1a-05d6eed8492d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f471ae07-63ff-47b5-a104-d96508d4d17e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e177c25e-4ab0-4de5-a398-a598444c5078"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="82b60736-49b5-4ea6-b512-826da493281c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="47c18000-5d8c-43e3-9018-10b4b77e7a40">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f97f0671-b035-4b29-8eb8-2605fb98d254"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c9f8ab83-053f-4c69-b1f7-533d2407d069"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dbdde0c4-6ae1-4d5b-93d1-caf4766cb685">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a47a57b8-dec7-4615-a804-2b81358e7cbe"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f714c256-25c6-4fbd-9a85-3e9ec45a05eb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e7d797ea-91ce-43fb-97c1-5318c2f20628">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="29d3c1b1-b47d-49b6-ad76-4bbad7e06a8a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b85c5cab-478e-4a35-9c23-a5a7d54dd330"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6f4d0b49-08ab-4f1e-b133-538123af10e8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c32cddbf-759b-4d8e-a033-80d0081971ce"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ccce7def-eed4-4d62-bfc1-924803fa6e01"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="00935271-1a9e-41b1-9a8b-8eaf3cf083cc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="44420edb-36a2-44cb-8886-bb8373c8a868"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7bfd7608-1c28-4bb3-8e65-bd8a24df75c4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2ee95663-9155-4195-88a1-3e47d496fbc6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0422ff3b-fb93-49f0-97fe-7f7ababef02d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fcd9a3b0-9c2e-4fab-9784-cf5b7adda4fb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4ee18275-e57f-47fb-a544-aa07c07ebead">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4f13361b-d50a-4450-adb4-171deb2a9d4b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d4a86e4a-e139-42ea-81e9-55f8bf01d624"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="300803b8-a4cf-4dae-9c8e-3baeb78c2152">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="016f8818-c25c-4fb6-bfdb-18f6d962dc71"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1e8190c0-7332-4aad-aa7d-eca8ad531960"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f2c89e36-dc40-4151-a2f4-ff31ebfa7dc9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="02d93b9b-9f2c-47b0-bb8b-25ed12e96396"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bae42107-e291-4f89-a2c7-239fcb1dd274"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b44c9487-bb7b-4a67-a7bf-667fe162d1a0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d9278924-388b-429b-858f-2bd718d1190d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="38c542f1-ec42-409d-a061-dead35d1cc5f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="98623f69-7ec2-4216-8d10-6632805e6908">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8e12b97a-e189-4f02-acce-df1c3ff63173"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f190ed2a-56e3-40e7-8a45-289cf6218e5c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5b986222-56f9-4718-a7c4-96c9e4805a0a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="aaf9d917-4b03-4bb9-9cf4-1b42f52ea6f4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fdcfc011-f4e2-44eb-9da2-cec596fd134d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="48d9ccca-9e76-4ae1-a6c7-57bbafec1856">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="05667638-0ada-471e-ac35-e2af7e84acac"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="91648a12-656e-4c2f-b7ee-e42bdd40bf15"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e2f0326f-2c88-4070-8746-f82dee14c8f6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8805b5f5-9410-4a38-9d8b-81045d03fd74"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c62d4c32-cb3b-40a6-be30-f051ee06cc9b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f9990725-b389-45fd-ad4c-a7d6836195f1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="36aa9161-a6db-4a00-ba0e-f9aae3e3f62f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8c0f07c9-291c-4fd6-9114-1df2e630ad8c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ba673fbd-82c1-4e20-bd5c-7b70ea9438ff">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="69a1bc21-9c6b-4cde-90fd-b016f4291cd4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f682c7e6-ce9f-4e40-9669-984e0fffb680"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3d50b0a1-5dbb-43f1-ab73-b8285c75a650">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3f220a9d-9ca1-4d14-8b78-a4045c48ef92"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d887c455-9cad-4f96-956d-29bc3edc2b52"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cba02e22-4bde-4ed9-8394-5825235f2925">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2b03f300-81c4-4c91-8074-805c76e8219d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8d15e7f3-e92b-42d1-ba95-bc0a7c260e4b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a703abe5-6f68-4cd9-a5d2-dcdb92cef927">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>16777215.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f73047ec-3126-442b-9142-d242325ef1e1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="17de6a0d-657f-489e-8874-8c9778716e16"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f7aebaf8-786f-4c50-95a5-0e2b5feaf9ba">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7c8d5b98-5589-47f1-aee9-4278fa9d1671"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="90eb36c9-f178-4f15-8298-c22b58c61b43"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a28213d2-886e-43e7-85a2-55dc656e0c7d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1135a273-a607-4492-ac92-3cf1cd799e59"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7b1d9263-d9c0-4c7d-a8c8-920ea4b20059"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a58df130-3edd-41bc-887a-fc60ddb258f4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="60a809fa-06d1-4f8a-a7e8-7c952a5cb81b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="35bcddfe-142e-44b8-b3ee-360168ca1d9b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ecc9b573-4177-4ad5-b8df-e406c3d2dad1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d866d372-7fc8-4f75-8347-9f35b84a4682"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1066f975-aed5-4205-9a06-fd9ecd89b554"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="78b63505-191d-4d8c-b15f-8cfc5bd25707">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7d1b74ec-8730-4bb1-bd27-8df3611620f3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a9248e65-7625-4643-81a5-bbfe751123aa"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="81e832e0-40db-4a54-bb12-a3453a67c007">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f925ffaa-744b-4471-b87c-5896e27a9399"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="af556bee-a786-4ae0-9c28-991cf2029c67"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6ebe0e25-5f68-44b1-a69f-4e75045ea3d9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="00435a2d-d0e4-4615-aa74-d0e2faa1474e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e2159dd5-b616-4639-b6ea-74f731f72dc6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5351523d-de3e-4c8e-8bec-ac6e6c3c8eee">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d02ef1c3-9e38-4958-8107-daac67b7500c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="745b73a0-1f60-4222-b227-cfaca5b20bf0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="da986cd3-373d-4c87-b469-1b7e01f54b04">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cce630a1-7e66-4de2-802e-c0dce8b615f5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="42fe5d21-4ba3-4e4d-9eae-e5b5e3942594"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4790f827-9bee-45b3-83c2-203e4601a84d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="95e0f4d2-decf-48a6-a9f3-ec16c91e477a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f4681df9-9ec6-4cd2-b7d0-bfe667c2756f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="848a2e96-de65-4a5f-b90b-f5ad32abe401">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8f90d0e2-38ee-4601-a1b5-47ba3059c7c2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1a580c21-4dad-4015-8926-0dfaada056b2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a2f8c3cf-644b-46e5-9713-f44c56b29dca">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="65764f60-3461-49e2-be39-77db904e3ba0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="23a0f7c6-e146-4dea-a8a8-dba553dc6343"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="519870f1-8547-4715-968f-17f7aa758002">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a3bc4527-0ed1-4f1c-80ea-20bca2ad21e5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b9df9499-f7c9-454a-a9d2-540712dbe0e3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ba3eb10b-36a2-4654-93d7-93a06050f9e2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="37bb74df-d138-4fa4-90d4-5311137babb8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4e5b0946-d0d0-43b9-9639-516e13b6e433"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7dc4f598-74c0-4da7-b1fa-4c8131315bcf">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fa809377-a9f3-4d74-a8ec-9c6df22b05f5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5c8719a7-8daf-4e58-9c75-29ad0a5b49cf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c13b921c-0ac1-495f-aa1d-b26b0a795d89">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="39c13044-92a6-4419-8fa8-39e1700e9cd8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="232c10a3-ebbe-4887-b4a4-b810c5bbbc62"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="64d96162-9074-45ae-8e64-7a49995f65d1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f48b4816-3540-4fac-b335-441159c7b1c2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2154f07e-8410-4b8a-a653-291a12a156f8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7dbc287b-15f2-4c43-92e6-c6dc73d49158">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="99f1d1ff-4511-4f52-9c7b-c8fde77cb032"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e2bac9da-f590-48c8-93d5-a9bd20b29b89"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1907aaf5-73c8-4682-8425-7b71d9735dd6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e51c5578-a26e-44d6-b2dd-f9acd56fbf67"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a8bea0d6-d9f3-467a-bcca-e76ca3e9d860"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bafb566f-51af-452d-baf3-53fafa8be661">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f8ba1d22-babc-41f0-9533-733eb52b781a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="18e2da58-7069-4e28-8b95-40969adc1735"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="93c9a8ae-a5b9-48b8-9bf2-35fdd0666ae0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0ca09c18-a2e4-46d5-b95e-611d045b4815"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f986f845-b619-44ac-a692-669f1022bdbd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="41692cbe-1445-4364-8a1e-ca17ec605f40">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1abf3b77-d671-48e4-aba1-9465a36f682d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="afb76a04-01ea-4130-9383-ffb82d9596ec"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d3d8c4ac-a566-41b7-8129-c00f9fa7c3fa">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="680f53c1-7804-49ea-a18c-5b582d0fd81a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1d612577-b332-46a4-b780-c768b1590777"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fd83511f-015f-4683-b007-a5945aed99c6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="97fac1c1-ca41-44c1-ab13-990343159c85"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="07131d6d-067b-4d56-8dc2-c1252f059331"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c7e667bd-3e63-4ad8-b510-42f8836ddf1b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c411add9-36a5-463a-8239-1437179e93f1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d599a612-d62c-49d5-8d61-3fb4e9862455"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f47c8a42-f531-4dca-acd4-e662d7e979b4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fffae734-c812-4818-8d22-ed06ce53838b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="12930c97-14f0-434e-b5b1-35b7720c9b68"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c69de9fb-3bb6-46d1-95a1-ad4742bc4aff">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7c84c343-59f4-47c7-8bf3-4a28bfb8c647"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3bfb4f4f-2494-4bc3-82f8-4e849aed4dde"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ba8a5b2e-f486-409c-84e3-44085928a145">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a5fa6e22-d165-4c56-8515-c5fd83e3d69a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a116edb1-d2c7-4e3c-a3ed-5b5625e16be8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1906627b-6934-4b71-9dda-b6f01c246aab">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e8468137-04f8-4b3b-916f-b39a5361e6e6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b27fac8a-f247-48d5-b4ae-a45f4be27534"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8e85cded-3320-4729-b075-f03dddc0bf29">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d84e3d14-73c3-4b14-b107-3617c445fb9e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ac2be7ca-9007-42cf-b2bd-22569eb85846"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2ab94070-09ad-4cf0-8d86-d70c8ed5ed45">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a348903e-d422-44f3-b190-4507b6e8ec11"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3bf7a695-161a-4340-9162-761d3686a976"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="336aa9bb-17ae-4d8e-aa92-1efee1678322">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a69992f9-4f4a-4d0f-96da-6c90eb4974b7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d70a034e-b0fc-42f7-a1ca-12ef2272f070"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="493fd829-0929-42ab-bb01-d2f797541935">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1164b948-fc8e-452f-9813-18c91a0c60d8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="60461e90-53cf-482d-bfc1-5c1dca903c40"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ac2b2cfb-e43c-40c7-a345-3d7889ae6708">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="805c5679-b055-4769-ac42-7587475dd80f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8f744050-e690-4fa0-8d62-8e486ad5d589"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="efdab3a5-2022-49f1-905b-e5590ecdaaf9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a53efe48-696b-4458-a0f5-b66c731f6b77"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="17a82188-3d54-4d83-9ead-ac7062f22336"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2bce2e25-6cbc-4798-9325-46cd6be6c4be">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b23a5173-7dd1-4778-9254-db0b26c23100"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7ce7c1f5-4df9-4bb3-8ef7-2985d6ff0cc6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="83460118-726f-451f-b12e-52111e6b1528">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>1.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b33a9f00-bab2-4071-8884-eae1b0df2c6e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f32170b0-3cba-470a-a641-2cc9959fbc60"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="81ec21c4-b411-4690-8f03-2112c7dba0c5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fc3a7dbc-5cd1-46bc-bc00-4789b581af94"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a140a169-3c61-4ecb-81ee-091a3ca83aea"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="59deeec2-8c69-4375-8a87-654ac4e28b4f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e8c37e4e-4816-49a2-b11a-d5ad9ae2457c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7b5c7449-477d-470c-86f8-07ef1a2b5260"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7720999d-02c9-4b5b-99db-dc9989d85a1f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c04b3dca-29b6-459c-bab2-c736e8590565"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c27a23e2-bad7-4859-bd7d-d227ecb3028e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="38a4666c-7707-4a1f-b8df-7a2743f5f14a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5f816ddb-3e5a-4623-9c76-3be457295f8b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="085c13f2-dd1d-4deb-8320-9e6bc43a4952"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="34160b04-648d-4afa-887a-c665c2de7a27">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7a5623ab-0eb9-476b-83e5-ee852792acd2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="301e84c8-988a-4d5c-ad79-27a45b2f4427"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9a3585cd-f1d0-4527-a2b8-3ba241d62cbc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="38534f04-246b-4aab-b8af-7c9912ef9405"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="14b6c58a-**************-28785fcace7c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3ce52150-3c40-4dc3-a4d7-678560ee8246">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d308a864-ac46-43b3-b6bb-2f562c341bb3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c96a59c4-f29a-4f1b-a406-8eb19d71e022"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="58de52e8-4f55-4ff2-b483-c36d107e3e23">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="86f26a1b-17fb-4635-a341-54a392038600"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="*************-4b7a-9103-228af6cfeae4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="01e3dec7-0f5c-43d6-b539-a0afb0c5a50f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a41562e8-e1b3-4ad2-9246-2f79f8b6177c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="780a6aae-0965-468f-a4db-431e7289bf27"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="915979c9-18c4-49d1-ab61-f4b27f72fb8f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="18c0d750-2281-4c11-bd51-d585d7ba5211"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5bfd3b30-23f6-48cd-9a3c-29b47e80a184"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7d1997d3-056c-4d83-9bca-76a45f5d2bc2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b04c6194-bf92-477b-80dd-c8ad0c531001"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c592a7e5-89ee-4645-9651-f58aee1d577a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="57b08c6b-0f1c-46cf-8556-57573a230161">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="94399ef5-396d-4d52-b03c-b5556a09b470"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="095b6cd7-5ac0-4b11-b65e-45fd40b9ac49"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="90b001fa-2c21-4395-804b-492192c34dc6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f3f7f57f-9ba3-455e-9796-3d3dc3ebeca4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="94621805-995f-4585-a23c-e2bea6dbdbd1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a7453d34-5111-4c94-a0e9-31bdd3bde667">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c32f9299-85bb-4be9-8389-e2ee317c962e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6fb15ac8-14f9-4331-9c03-6782245720d2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f16d5f95-1c03-4bc3-9807-ff18fa184b95">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8b19c7ff-a265-491c-8b52-************"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9b66f5bd-3f86-4804-9b66-ec060251c006"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3d3ea50c-c185-435f-99f9-f7270f090fe8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="75d4caf3-4ad6-4b5a-91d1-32f92983bf0b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="db8f2467-60c1-463d-8bf3-19527d8e19e2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="23428c9e-8d3a-4156-b483-262b4239d684">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8b0ea652-62a7-4b9c-bc46-1d704162ae71"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2fcbd01a-4092-4e79-9710-7bcd697a7aa4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c82ecee3-52ba-4a45-a804-3b44f27f4546">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="88230d0d-ec10-40fa-bd13-954fae28c763"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d1b39c49-a202-45e5-8878-e873f9ed9a93"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cd2a94e3-380d-4f60-af65-aeb1783fd721">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="da1dc2d2-fe54-4984-b4b7-1a791f246802"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9e284e1a-8715-4381-b851-f5523b5712aa"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="123304b8-1944-4138-928b-dd0309de6ef8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="72758141-57a1-4883-a10a-0df331f28cc0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7efd6ff4-bb7b-4bf8-81f7-68cb6e89f679"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="aa8aa41f-2da6-463e-bcf2-9cda84dca964">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dbf99f07-53b9-4f01-9298-0df8286d8023"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d5f2b1e7-b353-47c4-bc1d-2653e650a8a5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d9507854-5392-4866-aab7-81c6528fee90">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="431bccfe-790f-4a7a-97eb-99a2f6165906"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cd6ece87-3ffa-405d-b5bd-5a798d50713c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c7cb91bf-bb02-48c3-abc1-e92580aded0b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e77b7f0b-a0b8-4969-b6f3-c691fde97a4b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ae374f6e-3018-4869-bf39-690f4f48039d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d742d990-f807-40bf-8f74-44dc9c8dc0b5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3dd894e2-6fc4-4a9d-86a0-4bb07296c9a9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="58b51415-3620-4c91-b9b0-902a6eab5426"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="48981be9-cc89-4081-8883-7b589532f71d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="55b0447d-07fc-4253-a79e-cbeb21737360"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3ce33509-50c8-4d9f-ad65-2e133e2166a3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="68bbf542-3920-4cbb-8faa-c076a7ba14c2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6be3eb58-25a2-4f6b-9999-3e834e032695"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5c1fd3f4-a91f-4e4f-b3dc-43f854f4036c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9c2a9aa5-6a23-45de-b2d6-6eb2f5a72f91">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="85414a5d-ff89-416f-a3f0-13265e1db4a0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b1f5588a-12e7-45da-b5e8-1bd9ba0443bc"/>
    </inports>
    <isAperiodicRootFcnCallSystem>true</isAperiodicRootFcnCallSystem>
    <isBdInSimModeForSimCodegenVariants>false</isBdInSimModeForSimCodegenVariants>
    <isInlineParamsOn>true</isInlineParamsOn>
    <isNumAllowedInstancesOne>true</isNumAllowedInstancesOne>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigOutportVirtualBus>true</isOrigOutportVirtualBus>
    <isPreCompSingleRate>false</isPreCompSingleRate>
    <isRefModelConstant>false</isRefModelConstant>
    <isRootFcnCallPortGroupsEmpty>true</isRootFcnCallPortGroupsEmpty>
    <loggingSaveFormat>2</loggingSaveFormat>
    <maxFreqHz>-1.0</maxFreqHz>
    <numDStateRecs>3</numDStateRecs>
    <numDataInputPorts>6</numDataInputPorts>
    <numLoggableDStateRecs>3</numLoggableDStateRecs>
    <numLoggableJacobianDStates>0</numLoggableJacobianDStates>
    <numModelWideEventTs>0</numModelWideEventTs>
    <numPortlessSimulinkFunctionPortGroups>0</numPortlessSimulinkFunctionPortGroups>
    <numRuntimeExportedRates>1</numRuntimeExportedRates>
    <numTs>1</numTs>
    <origInportBusType>SWC_ObjDetLyrBus</origInportBusType>
    <origInportBusType>SWC_SigMonLyrBus</origInportBusType>
    <origInportBusType>SWC_StateMachLyrBus</origInportBusType>
    <origInportBusType>SWC_DataHndlLyrBus</origInportBusType>
    <origInportBusType>CtrlLogBus</origInportBusType>
    <origInportBusType>SWC_HwAbsLyrBus</origInportBusType>
    <origOutportBusOutputAsStruct>false</origOutportBusOutputAsStruct>
    <origOutportBusType>PnlOpBus</origOutportBusType>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="cdaf114d-f2d0-462b-a77b-ae0e2ba7147e">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>PnlOp_stMtrCtrlCmd</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="37bf9dfa-6489-4c7a-9cce-4619beb316a9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2ca7fa66-9af3-442b-a98e-a8d0c8e9d83c"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="26bb2444-e8a1-4f9b-a8bf-c735f7d5d9dc">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>PnlOp_isMtrMoving</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="798cb383-7afc-4b38-9d40-6e265bcb5bc7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="abb37ec0-acb4-44d7-95ac-c6763ad0b2b4"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="2b58b362-cf79-4b6c-b6f8-4c53e145d9e3">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>PnlOp_stNormalMov</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="c69f4831-1acf-4600-a343-30a5fecb2cc8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ffa5c60c-3121-47d3-b761-ca980819a0fb"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="dbbb0cb1-d9a8-4627-8489-3f2f052b13d7">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>PnlOp_stReversal</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="af6b6bbf-2aff-4d7e-8c45-eccc6181f191"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8f3f8ce9-1aca-4c23-a1ba-348bf3408be4"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="4b63d0aa-ec5b-46a7-9d9c-82e99bbd7f6a">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>PnlOp_stATS</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="902710c9-5dc8-4ff8-a6f8-9264278484b8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="16573a16-d653-4e04-ad41-a421c3653a94"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="f1a4f30c-4612-402d-9d67-c81db3096172">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="fb3ed6e5-1a24-4c76-977f-e5effee34bfd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ecf638f6-c999-472c-bc2d-2c37b0de71cc"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="9601fa61-2975-4a0b-9981-3635decd193e">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>PnlOp_hTrgtPosPtcd</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="f7808a03-b767-4e4c-9650-9fb4962c3418"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dfcb2808-0907-49cc-8c50-0bbef740f20e"/>
    </outports>
    <preCompAllowConstTsOnPorts>false</preCompAllowConstTsOnPorts>
    <preCompAllowPortBasedInTriggeredSS>true</preCompAllowPortBasedInTriggeredSS>
    <removeResetFunc>true</removeResetFunc>
    <runtimeNonFcnCallRateInfos type="ModelRefInfoRepo.RateInfo">
      <compiled>true</compiled>
      <isEmpty>true</isEmpty>
      <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
      <period>.2</period>
      <priority>40</priority>
      <rateIdx>0</rateIdx>
    </runtimeNonFcnCallRateInfos>
    <sampleTimeInheritanceRule>1</sampleTimeInheritanceRule>
    <solverStatusFlags>331</solverStatusFlags>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="95f3c5a3-793a-4c39-bef5-c8027266f717">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="e42d38dc-8e27-4cba-a180-9817498f804c">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_MovType_t</enumName>
      <labels>None_C</labels>
      <labels>Manual_C</labels>
      <labels>Automatic_C</labels>
      <labels>Learning_C</labels>
      <labels>AtsReversal_C</labels>
      <labels>RelaxOfMech_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="ed717aa1-2de7-419d-8b77-89c4d8680db7">
      <defaultValue>Idle_C</defaultValue>
      <enumName>CtrlLog_RelearnMode_t</enumName>
      <labels>Idle_C</labels>
      <labels>ReqLearn_C</labels>
      <labels>LearningPos_C</labels>
      <labels>LearningRF_C</labels>
      <labels>Complete_C</labels>
      <labels>Interrupted_C</labels>
      <labels>Renorm_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="663784a6-b37c-4678-b43c-5b687b08bb67">
      <defaultValue>Idle_C</defaultValue>
      <enumName>LearnAdap_Mode_t</enumName>
      <labels>Idle_C</labels>
      <labels>LearningPos_C</labels>
      <labels>AdaptionOpen_C</labels>
      <labels>AdaptionClose_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="4759b28f-d46f-41c2-910d-db2478d735d9">
      <defaultValue>NoReq_C</defaultValue>
      <enumName>LearnAdap_Req_t</enumName>
      <labels>NoReq_C</labels>
      <labels>ClrReq_C</labels>
      <labels>SaveReq_C</labels>
      <labels>InvalidReq_C</labels>
      <labels>InterruptReq_C</labels>
      <labels>ClrReqHardStopOpen_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="01155147-f7c6-46d0-92fe-2b2f27db8e45">
      <defaultValue>NoStall_C</defaultValue>
      <enumName>BlockDet_Stall_t</enumName>
      <labels>NoStall_C</labels>
      <labels>IncStall_C</labels>
      <labels>DecStall_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="db875ded-7de1-4a12-a5a4-c0531b5f0c32">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>BlockDet_FltType_t</enumName>
      <labels>NoFault_C</labels>
      <labels>SamePosStallFault_C</labels>
      <labels>DoubleStallFault_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="0f56fa86-694b-421c-8e0b-7130f7b92548">
      <defaultValue>NoMov_C</defaultValue>
      <enumName>BlockDet_Stall_dir</enumName>
      <labels>NoMov_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>reserved</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="f9eb335f-80e2-45bd-823b-c546fa8769ab">
      <defaultValue>None_C</defaultValue>
      <enumName>APDet_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="e8f42726-bdab-4ef5-a6ed-016ef810f4fc">
      <defaultValue>Init_C</defaultValue>
      <enumName>VoltMon_VoltClass_t</enumName>
      <labels>Init_C</labels>
      <labels>VoltClassA_C</labels>
      <labels>VoltClassB_C</labels>
      <labels>VoltClassC_C</labels>
      <labels>VoltClassD_C</labels>
      <labels>VoltClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="239f6ae9-c2e1-4cd9-86d3-64bf42611625">
      <defaultValue>AreaSlide_C</defaultValue>
      <enumName>PosMon_Area_t</enumName>
      <labels>AreaSlide_C</labels>
      <labels>AreaVent_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="4d7bc6d2-c4c8-4d88-b06e-08c007ffbe34">
      <defaultValue>PnlZero</defaultValue>
      <enumName>PosMon_PnlPosArea_t</enumName>
      <labels>PnlZero</labels>
      <labels>PnlFlushClose</labels>
      <labels>PnlVentArea</labels>
      <labels>PnlVentOpen</labels>
      <labels>PnlComfOpnArea</labels>
      <labels>PnlComfStop</labels>
      <labels>PnlSlideOpnArea</labels>
      <labels>PnlFullOpen</labels>
      <labels>PnlOpenHS</labels>
      <labels>PnlOutOfRng</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="25e56671-9a97-4a60-9e5e-b4fe190bb21e">
      <defaultValue>Init_C</defaultValue>
      <enumName>StateMach_Mode_t</enumName>
      <labels>Init_C</labels>
      <labels>NvMInit_C</labels>
      <labels>Startup_C</labels>
      <labels>FullRun_C</labels>
      <labels>Standby_C</labels>
      <labels>Shtdwn_C</labels>
      <labels>FastShtdwn_C</labels>
      <labels>SystemReset_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>7</values>
      <values>8</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="6ea18b69-ed10-41c3-9234-324f52584219">
      <defaultValue>IRS_E_OK</defaultValue>
      <enumName>stdReturn_t</enumName>
      <labels>IRS_E_OK</labels>
      <labels>IRS_E_NOK</labels>
      <labels>IRS_E_PENDING</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="93848491-80bb-47ec-ba2f-21bb3f15fb69">
      <defaultValue>Disabled_C</defaultValue>
      <enumName>CfgParam_TestMode_t</enumName>
      <labels>Disabled_C</labels>
      <labels>All_CW_C</labels>
      <labels>Sequential_C</labels>
      <labels>All_Stop_C</labels>
      <labels>All_CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="7aafb29f-**************-28d35b48d58b">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>SysFltDiag_LastStopReason_t</enumName>
      <labels>NoFault_C</labels>
      <labels>Debugger_C</labels>
      <labels>DiagReq_C</labels>
      <labels>PanicStp_C</labels>
      <labels>ThermPrtMtr_C</labels>
      <labels>ThermPrtMosfet_C</labels>
      <labels>RelaxMechComplt_C</labels>
      <labels>AtsRevComplt_C</labels>
      <labels>MovTimeout_C</labels>
      <labels>ClassEOV_C</labels>
      <labels>ClassEUV_C</labels>
      <labels>BattVltNonPlsbl_C</labels>
      <labels>AmbTmpNonPlsbl_C</labels>
      <labels>MosfetTempNonPlsbl_C</labels>
      <labels>SysIC_CommFlt_C</labels>
      <labels>LINCommFlt_C</labels>
      <labels>SysICFailSafe_C</labels>
      <labels>ChargePumpFlt_C</labels>
      <labels>HSSuppFlt_C</labels>
      <labels>VsIntFlt_C</labels>
      <labels>VsSuppFlt_C</labels>
      <labels>VCC1Flt_C</labels>
      <labels>RPInvalidFlt_C</labels>
      <labels>HallFlt_C</labels>
      <labels>SysICThermShdFlt_C</labels>
      <labels>MtrCtrlFlt_C</labels>
      <labels>HallSuppFlt_C</labels>
      <labels>UnderVotlage_C</labels>
      <labels>OverVoltage_C</labels>
      <labels>StallDurAtsRev_RlxMech_C</labels>
      <labels>OutsideEnvCond_C</labels>
      <labels>TargetPosRchd_C</labels>
      <labels>PrevReason_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>16</values>
      <values>17</values>
      <values>18</values>
      <values>19</values>
      <values>20</values>
      <values>21</values>
      <values>22</values>
      <values>23</values>
      <values>24</values>
      <values>25</values>
      <values>26</values>
      <values>27</values>
      <values>28</values>
      <values>29</values>
      <values>30</values>
      <values>31</values>
      <values>35</values>
      <values>37</values>
      <values>48</values>
      <values>49</values>
      <values>50</values>
      <values>51</values>
      <values>52</values>
      <values>255</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="ce191588-2f44-460f-8704-6c7b40640ca8">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>PWMCtrl_MtrDir_t</enumName>
      <labels>IDLE_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <labels>SECURE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="4a2033ce-6d90-41f4-902a-ccbee891f586">
      <defaultValue>DirNone_C</defaultValue>
      <enumName>HallDeco_Dir_t</enumName>
      <labels>DirNone_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="b2c0d899-4519-495a-8c96-2bbb65917b10">
      <defaultValue>CmdNone_C</defaultValue>
      <enumName>PnlOp_MtrCmd_t</enumName>
      <labels>CmdNone_C</labels>
      <labels>CmdInc_C</labels>
      <labels>CmdDec_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="e7555d8b-d437-499e-ab2f-d9600d511538">
      <defaultValue>RoofIdle_C</defaultValue>
      <enumName>PnlOp_Mode_t</enumName>
      <labels>RoofIdle_C</labels>
      <labels>Moving_C</labels>
      <labels>Intr_C</labels>
      <labels>ReachTarPos_C</labels>
      <labels>ReachStallPos_C</labels>
      <labels>Automatic_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="570ca45d-8b3e-4d84-a099-be25b63dabd2">
      <defaultValue>RevIdle_C</defaultValue>
      <enumName>PnlOp_Rev_t</enumName>
      <labels>RevIdle_C</labels>
      <labels>RevInProcATS_C</labels>
      <labels>RevInProcStall_C</labels>
      <labels>RevComplATS_C</labels>
      <labels>RevComplStall_C</labels>
      <labels>RevInhibted_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="fe619478-4a0e-4ac9-b4aa-7070266b6cfb">
      <defaultValue>ATSDeactivated_C</defaultValue>
      <enumName>PnlOp_ATS_t</enumName>
      <labels>ATSDeactivated_C</labels>
      <labels>ATSAvailable_C</labels>
      <labels>ATSActive_C</labels>
      <labels>ATSOverrideStage1_C</labels>
      <labels>ATSOverrideStage2_C</labels>
      <labels>ATSDisabled_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="2fda4f98-62a5-486b-8f6f-2f3dc0180e49">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoSyncParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>ManMov_C</labels>
      <labels>ManCls_C</labels>
      <labels>StepMov_C</labels>
      <labels>StepMovCls_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="1c71fdcb-83f4-4b2f-8ddb-cdde51a55807">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoAppParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>StepMovCloseReq_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <timingAndTaskingRegistry>&lt;?xml version=&quot;1.0&quot;?&gt;
&lt;slexec_sto version=&quot;1.1&quot; packageUris=&quot;http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112&quot;&gt;
  &lt;sto.Registry type=&quot;sto.Registry&quot; uuid=&quot;573985c2-7af4-411a-a825-4e774001d785&quot;&gt;
    &lt;executionSpec&gt;Undetermined&lt;/executionSpec&gt;
    &lt;clockRegistry type=&quot;sto.ClockRegistry&quot; uuid=&quot;b3e4dcd9-6bcc-49b0-ab1e-abf84ff9825e&quot;&gt;
      &lt;clocks type=&quot;sto.Timer&quot; uuid=&quot;858d21ce-0cae-49a7-9ae3-016d8fa0d9ec&quot;&gt;
        &lt;clockTickConstraint&gt;PeriodicWithFixedResolution&lt;/clockTickConstraint&gt;
        &lt;computedFundamentalDiscretePeriod&gt;.2&lt;/computedFundamentalDiscretePeriod&gt;
        &lt;resolution&gt;.2&lt;/resolution&gt;
        &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;596ec736-b4cb-40be-8380-8edfe96f7689&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;c8c674d5-463a-4c30-bfc5-e61924bbaa08&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;baseRate type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;83c4fa71-749c-49ab-9b99-252077251b48&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;fbd1fd3e-4d09-481e-8514-4dc043e37686&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/baseRate&gt;
      &lt;/clocks&gt;
      &lt;clocks type=&quot;sto.Event&quot; uuid=&quot;a0be1369-855d-4179-b0ed-7141f77b835a&quot;&gt;
        &lt;eventType&gt;PARAMETER_CHANGE_EVENT&lt;/eventType&gt;
        &lt;cNum&gt;1&lt;/cNum&gt;
        &lt;clockType&gt;Event&lt;/clockType&gt;
        &lt;identifier&gt;ParameterChangeEvent&lt;/identifier&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;fdcfc89d-7c5c-4a5c-94b9-aa9e2503baf4&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;e8b779e0-1493-4a5e-8dd2-292b9898dac4&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
      &lt;/clocks&gt;
      &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
    &lt;/clockRegistry&gt;
    &lt;taskRegistry type=&quot;sto.TaskRegistry&quot; uuid=&quot;60ae068a-df4e-4589-a7ef-ac4bf9b17747&quot;&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;d58dc595-d67b-45bc-bea3-b74f1be15119&quot;&gt;
        &lt;isExplicit&gt;true&lt;/isExplicit&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;4b03d193-4188-426d-a4b7-6ff8d2b29816&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;31858b84-4b3a-4082-b774-455bb3753949&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;schedulingClockId&gt;ParameterChangeEvent&lt;/schedulingClockId&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;ModelWideParameterChangeEvent&lt;/identifier&gt;
        &lt;priority&gt;-1&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;0335e958-1ad2-4968-9982-eabedb1aba85&quot;&gt;
        &lt;isExecutable&gt;true&lt;/isExecutable&gt;
        &lt;orderIndex&gt;1&lt;/orderIndex&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;93363a4f-3fec-44e2-add3-cfd54f7ec532&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;93e5fbb5-7775-44bd-a2fc-fb6db144b84b&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;_task0&lt;/identifier&gt;
        &lt;priority&gt;40&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;0ee40086-9cb8-43c4-9306-eabfa2dfb925&quot;&gt;
        &lt;taskIdentifier&gt;_task0&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;50a71af5-7cf8-435e-9131-27a70fe3eb92&quot;&gt;
        &lt;clockIdentifier&gt;ParameterChangeEvent&lt;/clockIdentifier&gt;
        &lt;taskIdentifier&gt;ModelWideParameterChangeEvent&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskPriorityDirection&gt;HighNumberLast&lt;/taskPriorityDirection&gt;
      &lt;taskingMode&gt;ClassicMultiTasking&lt;/taskingMode&gt;
    &lt;/taskRegistry&gt;
  &lt;/sto.Registry&gt;
&lt;/slexec_sto&gt;</timingAndTaskingRegistry>
    <triggerTsType>triggered</triggerTsType>
    <triggerType>3</triggerType>
    <usePortBasedSampleTime>true</usePortBasedSampleTime>
    <zeroInternalMemoryAtStartupUnchecked>true</zeroInternalMemoryAtStartupUnchecked>
    <FMUBlockMap type="ModelRefInfoRepo.FMUBlockInfo" uuid="4bb39212-c762-4f14-960b-7a7f9b428faf"/>
    <codeGenInfo type="ModelRefInfoRepo.CodeGenInformation" uuid="5bcdb273-e7d0-4710-89f0-3dab34945894">
      <DWorkTypeName>MdlrefDW_PnlOp_Mdl_T</DWorkTypeName>
    </codeGenInfo>
    <compiledVariantInfos type="ModelRefInfoRepo.CompiledVariantInfoMap" uuid="617ba0c3-9433-4c40-bb69-d0b5641f71c5"/>
    <configSettingsForConsistencyChecks type="ModelRefInfoRepo.ConfigSettingsForConsistencyChecks" uuid="2427fe95-af47-4368-8b88-d6d502c51a35">
      <consistentOutportInitialization>true</consistentOutportInitialization>
      <fixedStepSize>.2</fixedStepSize>
      <frameDiagnosticSetting>2</frameDiagnosticSetting>
      <hasHybridSampleTime>true</hasHybridSampleTime>
      <optimizedInitCode>true</optimizedInitCode>
      <signalLoggingSaveFormat>2</signalLoggingSaveFormat>
      <simSIMDOptimization>1</simSIMDOptimization>
      <solverName>FixedStepDiscrete</solverName>
      <solverType>SOLVER_TYPE_FIXEDSTEP</solverType>
      <hardwareSettings type="ModelRefInfoRepo.HardwareSettings" uuid="bc0ae3b1-957e-481f-85f1-c22bb0f8ab32">
        <prodBitPerChar>8</prodBitPerChar>
        <prodBitPerDouble>64</prodBitPerDouble>
        <prodBitPerFloat>32</prodBitPerFloat>
        <prodBitPerInt>32</prodBitPerInt>
        <prodBitPerLong>32</prodBitPerLong>
        <prodBitPerLongLong>64</prodBitPerLongLong>
        <prodBitPerPointer>32</prodBitPerPointer>
        <prodBitPerPtrDiffT>32</prodBitPerPtrDiffT>
        <prodBitPerShort>16</prodBitPerShort>
        <prodBitPerSizeT>32</prodBitPerSizeT>
        <prodEndianess>1</prodEndianess>
        <prodLargestAtomicFloat>1</prodLargestAtomicFloat>
        <prodLargestAtomicInteger>3</prodLargestAtomicInteger>
        <prodShiftRight>true</prodShiftRight>
        <prodWordSize>32</prodWordSize>
      </hardwareSettings>
    </configSettingsForConsistencyChecks>
    <controllableInputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="ad069ad9-032f-4c31-ace3-fcd6faef0e33"/>
    <controllableOutputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="a9f0d4d6-7419-4801-af29-7c1b584dc327"/>
    <dataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="80b9bc85-ba79-4b07-9c9d-d404f0d9644d">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>113</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>140</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>142</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
      <compDataInputPorts>144</compDataInputPorts>
      <compDataInputPorts>145</compDataInputPorts>
      <compDataInputPorts>146</compDataInputPorts>
      <compDataInputPorts>147</compDataInputPorts>
      <compDataInputPorts>148</compDataInputPorts>
      <compDataInputPorts>149</compDataInputPorts>
      <compDataOutputPorts>0</compDataOutputPorts>
      <compDataOutputPorts>1</compDataOutputPorts>
      <compDataOutputPorts>2</compDataOutputPorts>
      <compDataOutputPorts>3</compDataOutputPorts>
      <compDataOutputPorts>4</compDataOutputPorts>
      <compDataOutputPorts>5</compDataOutputPorts>
      <compDataOutputPorts>6</compDataOutputPorts>
      <dataInputPorts>0</dataInputPorts>
      <dataInputPorts>1</dataInputPorts>
      <dataInputPorts>2</dataInputPorts>
      <dataInputPorts>3</dataInputPorts>
      <dataInputPorts>4</dataInputPorts>
      <dataInputPorts>5</dataInputPorts>
      <dataOutputPorts>0</dataOutputPorts>
    </dataPortGroup>
    <expFcnUnconnectedDataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="2d7422b6-7948-4245-8b7f-ae91a3076886">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>140</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>142</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
      <compDataInputPorts>145</compDataInputPorts>
      <compDataInputPorts>146</compDataInputPorts>
      <compDataInputPorts>147</compDataInputPorts>
      <compDataInputPorts>148</compDataInputPorts>
      <compDataInputPorts>149</compDataInputPorts>
    </expFcnUnconnectedDataPortGroup>
    <interfaceParameterInfo type="ModelRefInfoRepo.InterfaceParameterInfo" uuid="16bc23d3-1073-4f79-9992-f36997532024">
      <globalVariables type="ModelRefInfoRepo.BuiltinParameter" uuid="6703dd47-30ee-42b3-a3a5-f2f6d7ca0a93">
        <datatypeID>5</datatypeID>
        <dimensions>1</dimensions>
        <dimensions>1</dimensions>
        <isUsed>true</isUsed>
        <netSVCE>true</netSVCE>
        <numDimensions>2</numDimensions>
        <parameterName>RoofSys_tSmpPnlOp_SC</parameterName>
      </globalVariables>
    </interfaceParameterInfo>
    <messageInfo type="ModelRefInfoRepo.MessageInformation" uuid="e2c9accf-1444-4245-ba46-8b0a5075a0d6">
      <rootInportInfo type="ModelRefInfoRepo.MessageRootPortInfo" uuid="91593a69-d4ff-493a-bd1a-4a19236c3e80">
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="8263c851-1aff-48ab-8eed-de74f3bfce05">
          <portIdx>2</portIdx>
        </GrHierarchyMsgModeMap>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="6a6c6303-ba97-469b-ac77-ab4a1455c3c5">
          <portIdx>3</portIdx>
        </GrHierarchyMsgModeMap>
      </rootInportInfo>
    </messageInfo>
    <methodInfo type="ModelRefInfoRepo.MethodExistenceInfo" uuid="ab759a54-b56b-4933-b38b-b34e62c3ea38">
      <hasSystemInitializeMethod>true</hasSystemInitializeMethod>
      <hasSystemResetMethod>true</hasSystemResetMethod>
      <hasTerminateMethod>true</hasTerminateMethod>
      <hasUpdateMethod>true</hasUpdateMethod>
    </methodInfo>
    <periodicEventPortUnsupportedBlockInfo type="ModelRefInfoRepo.PeriodicEventPortUnsupportedBlockInfo" uuid="e8357a89-bcb7-44bf-810a-0f0488815deb"/>
    <portGroupsRequireSameRate type="ModelRefInfoRepo.PortGroupsRequireSameRate" uuid="a63d1be3-821e-4a77-a3c4-3b61e0332d58">
      <DSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="50817595-a2d4-444c-99b3-022280c5230d"/>
      <GlobalDSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="07821ced-e140-4cec-89ea-75d5d9560e4b"/>
      <mergedPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="8c096c2b-58de-4e3e-b660-88ce157a7538"/>
    </portGroupsRequireSameRate>
    <rateBasedMdlGlobalDSMRateSpec type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="c83d0c9e-c944-4c33-b593-24ec6a077400">
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="ca24965f-a83c-451a-b648-4fdc39fcec77">
        <dSMName>CfgParam_CAL</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="b6ee5da4-1042-4c4b-8a97-467b3b59e5b2">
          <blockName>PnlOp_Mdl/Data Store Read5</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="aedac239-182e-47bf-8b34-b00a5a14f280">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="4a87e09b-7202-4e3a-a13f-88247ffed9cb">
        <dSMName>PIDDID_MtrDutyCycle</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="a4d3792d-f8b2-46e0-8800-5f0f4981b7f6">
          <blockName>PnlOp_Mdl/Data Store Read11</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="7f59fd09-8679-4c0f-8a95-ccfe709cb4b4">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="90772d1e-34a3-4491-ae42-c4997b17dc87">
        <dSMName>PIDDID_SoftStartPoint1Perc</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="f4b24170-12f0-4cd9-8afb-35b9cc04588f">
          <blockName>PnlOp_Mdl/Data Store Read3</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="96d07d9b-d1ea-43d8-bba2-737e1e45b74c">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="332028c3-ec6e-422a-b90b-a346b2ccb663">
        <dSMName>PIDDID_SoftStartPoint1Time</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="fe3d93f7-d6e5-49f4-a7d5-dfd829087ec9">
          <blockName>PnlOp_Mdl/Data Store Read4</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="f8ab6a07-c0fe-48f9-ae73-a7db30d7e788">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="562afda1-92ab-4dd6-a406-923d03a827dd">
        <dSMName>PIDDID_SoftStartPoint2Perc</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="b7818a71-b2d6-4b91-a90b-d3902fdf297a">
          <blockName>PnlOp_Mdl/Data Store Read6</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="23864f06-ed9b-4c82-bbe9-d348fb38b5a6">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="a3e0d6e1-99b8-4d1d-a10c-63274fa712b6">
        <dSMName>PIDDID_SoftStartPoint2Time</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="11017c92-4719-4337-ad16-ef1eacf8e59b">
          <blockName>PnlOp_Mdl/Data Store Read7</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="63b05b74-f539-46e3-b700-8a70a2458651">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="61f00fe0-2dbe-4d0d-97eb-05b42d72a8cc">
        <dSMName>PIDDID_SoftStopPoint1Perc</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="147cd60c-ff0d-45c7-badf-c691bf02a822">
          <blockName>PnlOp_Mdl/Data Store Read8</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="45b2c525-558b-4f54-9d2b-60d3c14166ae">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="de20ab62-fc1e-4395-bc16-7c5ad77edc72">
        <dSMName>PIDDID_SoftStopPoint1Time</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="bade0772-9f6d-46c3-8c79-0b5b6caf391b">
          <blockName>PnlOp_Mdl/Data Store Read9</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="145d4a4c-a5ab-492f-8802-569ef471cc08">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="e72acb38-f026-43f9-98e3-54ef6b478cfa">
        <dSMName>PIDDID_isFactoryMode</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="9759fc43-0709-4dfb-ae2c-e8132ad03b59">
          <blockName>PnlOp_Mdl/Data Store Read1</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="4262b50d-79fc-4a1e-bf21-47c4190b6909">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="249ec3b2-ae10-4ab5-b7f9-0fd7002f1e77">
        <dSMName>PIDDID_isOverridePWMRtnBusy</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="08d2e9fa-25b7-4569-acf3-5416dc2b602f">
          <blockName>PnlOp_Mdl/Data Store Read10</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="cf061796-0d71-41cf-ba32-6bec32fc086e">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="9987f8d2-6a89-498a-b9a3-a63bb083bfcb">
        <dSMName>PIDDID_isSoftStartEna</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="30dc435e-3440-4a04-8d3d-ff74d8a628d7">
          <blockName>PnlOp_Mdl/Data Store Read</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="842b43b4-0d62-4afb-ad75-3b7ba6c9bea9">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="8001f752-8945-4979-a744-63ebcd799d3e">
        <dSMName>PIDDID_isSoftStopEna</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="fff5e275-2600-4c48-81fb-69d3f6d75afd">
          <blockName>PnlOp_Mdl/Data Store Read2</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="4fe1248d-e31f-453e-b0df-770069c37456">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
    </rateBasedMdlGlobalDSMRateSpec>
    <rateSpecOfGlobalDSMAccessedByDescExpFcnMdlMap type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="973eb68d-7283-4709-85c0-753614eeccfa"/>
    <rootBlockDiagramInterface type="ci.Model" uuid="764d446b-07f3-4861-9333-6f33c64cdc8b">
      <p_RootComponentInterface type="ci.ComponentInterface" uuid="183c6a11-a198-4f57-ad58-c39025454d0e">
        <p_InputPorts type="ci.SignalInterface" uuid="cb5a9f29-69c9-47ef-aa5e-e4ddab88ae4a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a03906a7-a3eb-47b8-a4ec-229116d52f5b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Req_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ce208107-c2b2-40e6-a007-6159be4e66e1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Req_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a2f4e760-f375-48fd-8ac1-06b125ff3f60">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="587e9c97-663b-4f83-b21d-d99bed14aa36">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ff7e2b71-1dab-4228-a681-0b71d1eff6b4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="be0dd11b-f2f5-418a-81cc-59332c177177">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="85cadd37-8af4-44b3-be51-676a7b6dd9d4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5f771fa1-cb8a-4c00-b826-8ba5a59ad593">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="71938a31-87ac-4c22-a853-94bff0694726">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>BlockDet_Stall_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="785fad59-229d-4b26-aaa5-c3723d7966a3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c7326330-0f7c-4c3b-8864-f338bc75bbde">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>BlockDet_FltType_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dd5e7e13-4846-4996-ae05-45af694b39ef">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="73a24c58-9a1a-4f78-b914-a4fd1c721ecc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>BlockDet_Stall_dir</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="41808562-77bb-4d2c-a466-a8803e9a5c85">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0693535f-358c-40ce-bd27-edb7249bcf96">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7eae7dfa-7790-4452-86ec-8edba9bd0399">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0055dc99-9651-4dea-b758-0b33aa8f9cf6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1d5ca21c-2d1c-41d8-be4c-f3df4df8aea9">
          <p_ComputedNumericDimensions>18.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4eb32a9d-8054-4151-b348-e18b34be7175">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b0525d8c-dd05-4a41-baca-a69062eaa387">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0ec5f501-0bb6-47bf-b736-3e08550fc9d0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>APDet_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="57babeca-c0f8-49d1-9f3b-03bd963f01b9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dd75f277-ff75-48f0-b0d1-482662e97f36">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="63ff805e-7aa7-4ddd-b8cd-202ddaa5bc12">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="681833c3-1bf4-48f2-a4bd-81e7a1cdc0e2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="83364a1d-1110-484d-8c44-be1d4f622695">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9ee55902-bdca-41b0-8c5d-eae83fa99dd2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f35d5b31-9ab1-40e2-828e-60d0d9e1f2cb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c8367df4-dc75-4a52-ab12-e05044917a9c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="eaaba5ac-5881-40a7-a0cd-9b3c9346a1e1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3e1a2e26-a861-4e40-bfc5-62bc7f32a0dd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e28b209b-db79-4768-8764-3b5b83d9cb59">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="495428a8-089d-4e5b-8369-43f7d416ba4a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c0703081-a23a-42d2-9d31-3add52c964b2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="185d954b-5162-4dd8-ab4d-93aa38fea24b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3553eb48-3d8f-481f-bf7e-1f342ca37259">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="040f5c78-1db7-4195-8228-64ca53bb204c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="96e41585-e47f-4c89-a346-c86a9971ea61">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7eb48acb-1438-44ec-953f-d5e71f688c84">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3b00277e-04d0-4df7-b9d8-a4f2afa1e9b3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="64474700-b3d2-4422-8bf9-97c6cf37f990">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fbff6d79-51fd-41d6-b02b-3ae9ab8cc99f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5b5ed303-e910-448e-bbb1-e62c7ad2aeef">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d2cdb0e2-e7b4-4367-b2d6-feb949c7efbd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2e8b8709-8b2a-41e2-846d-2ad1d7445f33">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2f93f9aa-fc2f-42fb-a43a-d1d45ce4d18c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e76ed266-8db3-4be3-9d43-442680c3ff51">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8463db30-7d1e-4f2f-b1d9-a2782302e9d6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="83172a3b-0bc9-41da-8a3e-7676a87f2af8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2ac7e3d8-2342-4a1e-8f08-fc5033f5cfd1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4f2f0c9e-32c0-4a89-a0e3-a8e5e0349aa0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="73fd6a2a-ff52-4511-8c4b-b100e81003fd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6bd89526-786e-4e09-8a2a-8fd0f86a7932">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_Area_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="46299b17-1048-458d-842f-b19bf0e5790f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_PnlPosArea_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d72b4f51-3b90-42b9-ad74-a64f997690ba">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f7972449-07e6-4c27-afe8-b9fa0beace0d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dcc705a6-8bcb-4f67-ae53-2d411c43096f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="64fda5c9-498d-4d34-a961-6956d05abfad">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="87e71f9e-0ec7-4f8b-a127-4e1014902528">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="be59d7af-9187-44fe-aa6f-057971521808">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6044a89c-4c0a-4e95-96f6-9fa05d017691">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="91da50a5-b4cd-4a2b-a14b-59c607701c6b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2afbc129-699f-4d06-8367-9e6dbda205aa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e6e04836-f2b0-4706-bfb4-8eceb4687b6a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="da17240b-056a-4a69-969d-011f8dd8f4be">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b7fbb8e5-b713-48fe-8704-f98d2b5579ab">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="07cfe8fb-e28b-476e-8626-a3c88bbbfab5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f7cd9f1a-7f7e-4b6c-bf1a-bae3c723ac3d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="42098017-1c3b-4d3c-bfe0-0ed74d6ff5a8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9be426dd-5631-437a-ae7f-296f0717da9d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ae563e1e-c572-4f73-9254-fe8939a9fcc7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>StateMach_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d49b274d-0ae9-452c-aa6b-6109d33bbf72">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a36caf5d-ca02-4944-8591-5f32c561df53">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d7772299-41a1-4d85-a108-66d0997dbb03">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3642cad9-425b-42ad-9c98-370affe2ffa0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f339f3fa-292b-4494-987b-aff77f1afee0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5f0f1343-bfc0-4200-9467-c1aa219e0827">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="059d367d-312e-4f97-b4e8-5aebab7bceb6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3451b17e-aa16-486a-80ed-19c1493e8bc4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c991658c-02bc-4430-8c5b-35e9795b240a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0fd2d792-98e6-4098-83ea-239367707bcb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2a85895d-d184-4c49-9162-823029a9c101">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="84fbd8f3-3ccc-4fa4-8bdf-9f3b865eb5f2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6227f3eb-1272-43f7-8907-e63408b70a66">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f8665915-6574-41d6-8a8c-************">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint32</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b340d6f3-c903-4d90-a580-ed1f7e20ff5c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="043fe330-617e-4fc1-bab1-b8b282cbbbfe">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a6cb9263-a444-45ba-b1b2-69d9f132ad8d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4c1e264f-0062-4a69-b0b6-2dbe2e0d5926">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="59897d08-4973-42c7-81fc-4db6c5640921">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8f0dce58-4a4c-49cc-9153-c33030b6fb16">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="298ccf87-e63b-45f8-919c-0fdfd8598d2c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fc82e1a0-9344-486d-bd0e-eee9a3709655">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2bc249c0-35d1-43a7-b7b1-1a2886604aea">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d1b02086-81ca-4311-9098-ad8eb665d452">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="661b7abb-c898-4072-a2e2-9a9189fa0c4c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d9cee433-aee5-44e9-85b2-3e760fe67ff6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c29a36ab-edda-4abe-b76c-e98866f3eac2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1a72b2a0-a0e9-4d73-a153-b7ecad9ff6c6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="429df11c-0240-4319-bec0-36dcd4a2a6f1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>stdReturn_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="84ca368c-64c0-4554-b18a-24175f2b134a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6e12ca82-7631-4664-90c4-8d4a490270d1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="68faa84a-1fc3-477e-a9c1-6855b5d39f42">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c59c12b7-174e-416b-92be-32b6586b28c2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9ba3218d-2c48-4183-a832-6e525c88a974">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c2ec6265-ac05-42e2-8675-6a5571169391">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="19a18ae8-9739-4681-ad3e-9a0cc324c186">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a30670dc-64b8-4336-a958-c6b4d416c651">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2bfb7ec5-e794-4693-926b-11c73f4e376c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b9d3aa3e-6cfa-42a3-b337-0ae3c300926c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ade541a7-9aae-43a8-9939-59990a91499d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="653807eb-11a0-41e7-a873-6472170bbef6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CfgParam_TestMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="393892d8-c6e5-43b6-9d20-cc5005d010c2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_MovType_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="24483ff5-b40a-44fd-90f3-b0ea48be83d2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fa6d90d1-69d1-4692-8cf4-ec6deccc52a9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_RelearnMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0c05d5d4-d25a-466c-9190-8edbf6a80a77">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SysFltDiag_LastStopReason_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9a631fa3-0b93-4c44-9399-c654f74e2c8a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1455e965-04a6-49dc-9f82-f90d1803e4af">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="18fb441a-8cf2-44cb-9b88-5ff618ae1e6c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7038eb1d-420a-41f8-bb39-6290d3daee5c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PWMCtrl_MtrDir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="63860e52-7bf6-4b88-ad8f-4a441e1d5efb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4108a924-cb23-463c-bf0a-4d1d6f219123">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="41825580-14e6-4806-bb6d-178f53b5fd15">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d8a4cdda-3c45-4b05-9417-39b54d7f98bc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b86bfee1-6757-4750-a841-6dd2520b0b7e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="48a9242b-e5f6-4839-a406-ffa05ce98de6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8020c13d-a8cb-45ac-8f97-76f45be35239">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e114252e-65a9-4fc7-b768-f7f028cab8d4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dd2e1deb-**************-6dca38214a29">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fcde2361-4db3-412e-b4ff-97d1d5d4c1d7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0d311d66-0345-48cc-9b7b-6559cbbc973c">
          <p_ComputedNumericDimensions>2.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="56d96df2-81fd-464e-ad3f-4c781393c02f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="eb56a886-5fc7-410e-bc95-c97f7b54eb1c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a82e47f2-5aea-4466-a648-a45f2a5b3431">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9273f97e-f52d-46e5-8db1-1cbbc96ccc00">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d321a19f-7a11-4f0c-a6d2-f0854f6fcf5a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dea565ec-7cb3-4c9c-836f-3864afb18467">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d4ede83d-4d30-4ebd-a5b3-c5c55d4e9b27">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="57a691c0-c96e-4f00-becd-a2a2c15364b6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="615bb7e1-ef39-44ae-83e7-92d2089bec79">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9290f9a6-3e0e-480f-a445-73e68edb060a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b2773be4-2288-4b0c-9550-0206a7157fef">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>HallDeco_Dir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="44ad2c5b-d21f-4dc8-8fe5-16706b503ef7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="33415100-5be2-4e6c-af76-9076adb0704e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>rad/s</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="93fce006-4769-4304-a482-db7c4e8f0396">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bf84c531-2428-423f-a3a0-509e0d176ac6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8f683782-7093-45e6-8b37-a8227d59bae7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1efb95cb-ee7f-4c6c-9af7-3688d7308b22">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8dd96a79-500e-49f6-b0a1-651ffae479af">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_Name>PnlOp_Mdl</p_Name>
        <p_OutputPorts type="ci.SignalInterface" uuid="c850afaa-198b-45bb-9459-e081f51baa28">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_MtrCmd_t</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="03588d99-83dd-49b4-ab05-4016e4dae8df">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="027c359b-97bd-4a19-9228-3e1b87a8d7e8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Mode_t</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="b6c5e839-f49e-41cb-9804-59ed0593058f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Rev_t</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="1978f93b-11a1-4e85-b5ac-e53c858bcf6d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_ATS_t</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="7258f585-315f-4fb3-97ef-91cbc7f52cd7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="681d5da0-b22e-4502-8315-80f43db19d83">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>%</p_ComputedUnit>
        </p_OutputPorts>
        <p_Type>ROOT</p_Type>
      </p_RootComponentInterface>
    </rootBlockDiagramInterface>
    <simulinkFunctions type="ModelRefInfoRepo.SimulinkFunctions" uuid="313dbc06-0311-4280-8049-666447fb4a22">
      <compSimulinkFunctionCatalog></compSimulinkFunctionCatalog>
    </simulinkFunctions>
    <sltpContext type="sltp.mm.core.Context" uuid="26d13ff8-c651-4442-846b-cdd83e5cf026">
      <globalData type="sltp.mm.core.GlobalData" uuid="5e4a5cd7-fc7b-44cd-b4ea-43e74999a4fb">
        <dataName>CfgParam_CAL</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4388c9e9-192d-417d-8add-7cf3eac0c908">
        <dataName>PIDDID_MtrDutyCycle</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3ce4c595-6967-41c7-9135-2103a2f5bbd9">
        <dataName>PIDDID_SoftStartPoint1Perc</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5ddc334b-12ff-4c27-8b78-d0a7b2e51bfd">
        <dataName>PIDDID_SoftStartPoint1Time</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="04f0f02c-8c24-424b-b89c-e1ab35c95cb4">
        <dataName>PIDDID_SoftStartPoint2Perc</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ae02d7c3-4ef3-46a0-b471-ac119a911dc2">
        <dataName>PIDDID_SoftStartPoint2Time</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c07df74e-2ca9-444b-ba14-6291619e0d21">
        <dataName>PIDDID_SoftStopPoint1Perc</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="91676ba9-904a-484d-bec7-0f953673d326">
        <dataName>PIDDID_SoftStopPoint1Time</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9c53ed12-27fb-49f1-b61a-0ed7809d24ee">
        <dataName>PIDDID_isFactoryMode</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="be0765ca-095a-4d90-8164-6ebde991dbc6">
        <dataName>PIDDID_isOverridePWMRtnBusy</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7f270e83-741e-493f-a357-d62232475150">
        <dataName>PIDDID_isSoftStartEna</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="853234ef-2192-489a-8f42-8b2336fc6f02">
        <dataName>PIDDID_isSoftStopEna</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7a4b0319-0e84-40e9-b4bc-2fa7c964648a">
        <dataName>portCtrlLogBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="452ea812-9eb7-4f3e-ac31-31e0a39ffd4c">
        <dataName>portCtrlLogBus_CtrlLog_hReactTgtPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="901a4607-4be3-46ad-a783-0febadeb363e">
        <dataName>portCtrlLogBus_CtrlLog_isMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e79c37b3-0672-4226-a327-f2bc85934715">
        <dataName>portCtrlLogBus_CtrlLog_isPnlMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bef6199f-fd61-4153-96ab-c6b6d062d36d">
        <dataName>portCtrlLogBus_CtrlLog_stDirCommand</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3a41c50c-1281-4490-870d-2a2c588301eb">
        <dataName>portCtrlLogBus_CtrlLog_stLastStopReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b9677a35-ac27-4481-a7da-fe5dc19b898f">
        <dataName>portCtrlLogBus_CtrlLog_stLearnInt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ba00a778-dbc9-43a2-b06e-79f1ff15ee79">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movReact</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fa0d3d41-7c28-4e2c-b64c-282832813bc6">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f1053de3-5386-4dfa-aec0-e395bf0d9171">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6b79874d-bfe8-48d2-8336-5ec5148e686c">
        <dataName>portCtrlLogBus_CtrlLog_stMovType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7d319c96-9cf4-4c7b-96a0-8e853757cd48">
        <dataName>portCtrlLogBus_CtrlLog_stRelearnMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ec88f118-0791-4415-9c7d-8324938a0c58">
        <dataName>portInport</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="17125ce1-49ba-415e-b031-c56538c232a5">
        <dataName>portPnlOpBus</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b6eea70a-64c0-4a37-b6e7-c5f530a6d7e4">
        <dataName>portPnlOpBus_PnlOp_hTrgtPosPtcd</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0e41ba3d-8ebd-4a2b-8e4e-cf395983bf2f">
        <dataName>portPnlOpBus_PnlOp_isMtrMoving</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6d226a16-0a76-4d61-bd92-6acf0aa6ff80">
        <dataName>portPnlOpBus_PnlOp_pMtrSpdCmd</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="522c5a97-7d3b-412e-a8bb-f3c722023e21">
        <dataName>portPnlOpBus_PnlOp_stATS</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ad868ee4-1203-4661-8453-01603daef95a">
        <dataName>portPnlOpBus_PnlOp_stMtrCtrlCmd</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d74793bf-c701-481a-a5de-733b3b220daf">
        <dataName>portPnlOpBus_PnlOp_stNormalMov</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0eb4cdb7-a1cd-4d89-ab99-7c804d405a01">
        <dataName>portPnlOpBus_PnlOp_stReversal</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="90291d6a-8fbc-4aef-b0c0-861e6138f57d">
        <dataName>portSWC_DataHndlLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e6804ee6-c21e-462d-a16e-1a9b9e8136e2">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SCUInfo</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9ae63b20-3711-44b1-a7a2-f2ad21a2e1fb">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SWVersion</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4f567d9e-1985-4814-a470-dce9d51e3430">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isCALFileVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6fa2d6b0-d9a3-4d1e-bfc2-4df4a9b7f149">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isPosTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="aa75c63b-090f-4589-a1b4-53a85945ebd5">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="820fa8f8-289d-4c4e-a23e-be8c90d112bb">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b03da30f-6d2c-4d86-9742-ffd1f5f9b5bb">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stTestMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="99bd5dc1-6dd9-4079-aa23-5d0312eeb044">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currByteOfRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ca948c51-ba49-4908-9b86-d915c677b729">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currRFArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a74b08c8-201e-4850-bbde-edb5638177f4">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isInsideRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3492033-10d2-4d82-88d0-3dfa2bb6f811">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="505c8fff-a79a-49e4-9669-69db1d76894b">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f2690517-af69-4860-978b-a528a7d16099">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmBak</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="*************-4581-a8fc-605dc9d715b0">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmSync</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="67ac9ced-e001-43b5-9626-bb1d57ae19bf">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8310a237-8b14-4e89-8276-473f7a45a874">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="70af54de-de34-43b2-be11-5cd327f23876">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_stRFSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="05d8f591-99f7-4b09-b6e3-5a8be598d01e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_AmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7071398e-ffe5-45dc-8aff-54dc180d4586">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_CycRenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3f10f070-2ed7-4add-9b03-1ceca1036d9b">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LeanrFailCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6130a093-319e-4085-bfaf-65f09096fcbc">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LearnSuccessCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="eb022944-fb93-4d8d-8d90-7bfc2581138b">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MOSFETTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a6957dd2-0eb3-4766-888f-9ff1db6cdf86">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f626daba-4e53-4508-8a4b-0364d3efefe0">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6e943163-86fe-4e02-9506-ec7c61659e88">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a65c9f39-46a9-4bf3-88c2-4e80b810b02f">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f94da60e-5650-4b37-ac6f-b1c01adfe489">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MotorOperTime</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ad7a71a5-bc88-4dfe-9461-9fbf88cfd0ea">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_RenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a285d7bf-e6ef-4f13-a62a-70c061991bdf">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReprogrammingCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1bc95321-ab5e-41c8-b5ab-c7ac0265922e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b3c03828-237c-4835-9d5a-2758b7bb26a3">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f25e2189-aa38-422b-ac76-108446bc7a0c">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3a0ee246-72d5-4960-82af-cb457fb9f69e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalThres</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b9903f26-b650-43d2-a99e-84d26db5ecc2">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_SlideCycCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="53490b25-1f9a-4994-9cca-9e6fca59b545">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_StallCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c9f35e7b-4fa2-4d29-a4d7-a0cd24089c19">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstCaseTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="802ac4e4-0b47-4de2-b2ca-526a78628b5e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cf96bc1d-07ad-4f16-9cb8-2bba969d8ffe">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7ea99f25-07f0-4575-a0bc-f83feff212a1">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ThermProtCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="87570422-e6be-456e-abb2-4fe7d9865efc">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c4647064-bc7c-4920-b4f3-0f100e358dab">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b449de8a-5b39-4ac0-993c-0b9920edd1cc">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="10afce4d-02db-4c6a-a7b1-e1be2b777312">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_unlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8fb64714-0986-4748-8aab-e3425dbe2ee1">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHistl_TEstCaseTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3384bb76-bc9d-4fdc-b276-afb8ed4ff880">
        <dataName>portSWC_HwAbsLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9f7a9a6b-0f1e-4c40-ba6c-a06d9467336b">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="*************-4bae-800f-03b002920b98">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_TMOSFETTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c4c6be89-e9ff-4157-a4fa-5537e4a74c87">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_u12VBattInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="36700250-a0c6-4764-a40b-a294a851bf98">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uHallPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c00f2611-4473-4b26-82ed-9b38c4dfc814">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1AVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="16941ca4-c2ff-4a46-9ed1-9123020b8660">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1BVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="08838513-dd01-4187-b12c-e3c6cead042d">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uSwchInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4b4ab2f4-2e5c-4fa3-bbb1-67ee17d011be">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_PINReadStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7f33da22-6b9c-4159-ba91-1f2bdb5e962e">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallOutPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c122222a-7df4-460e-a80f-542ccbe57eee">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens1PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6f3d2e52-029a-4e0a-94b8-4605ccf90e9d">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens2PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b3af97a3-82dd-4870-860a-00b269e31f15">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isINTPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="11ef40a6-e846-4522-9547-125f4411b224">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankAPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="04d7db31-b5b2-4101-a991-1c944fb968ee">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankBPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="72d00ec8-a469-4a37-9c61-43b5da4bf152">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isVBATMeasEnbPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6c5a5404-150a-4c9e-89d3-9ee80b40d550">
        <dataName>portSWC_HwAbsLyrBus_DiagComManBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f6114765-a0ee-4142-b5af-c9b3882c77b7">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isHallSuppHS2Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="343cd3c3-3897-4b2d-8890-dd3b37f864c1">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isMtrFebkCtrlHS1Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a068687f-84c4-4b40-b79f-71c05b4b79fd">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isSwtchSuppHS3Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fb5fed86-c7f6-4ce6-a7c3-cd1c6fea2391">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_u8GetPWMDutyCycleVal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="011f8e82-8c2b-4e45-8685-58d1229380e3">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="92b23a9b-fa89-48b3-a5b4-ec7b6b8a4cfb">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_DTC_HallMtrOptDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f9c9e34b-7efd-4709-9630-5bc561c4bd93">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_DMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b7df07a7-43ef-440b-a8b3-fc1f9002fe78">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_isMtrSpdVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="188eca88-c169-4946-95db-52d206643d4f">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d0f16fc4-a80e-4758-8953-a36d8b0464d4">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="62711de4-ebe5-4ded-b659-10824760cc41">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8a9660a8-06d0-4c06-b598-42edf8d1b286">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFaultDebd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="105d9d24-d281-4cd9-89ab-2cd24415a81f">
        <dataName>portSWC_HwAbsLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1e2b8131-d675-4675-847e-0649be658b76">
        <dataName>portSWC_HwAbsLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDutyCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="214bb9b5-8951-4d84-92fb-e7e9034ccc83">
        <dataName>portSWC_ObjDetLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d20893cd-28c8-4adc-97c9-74da71fa3d41">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_ATSRevDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3b5c9aaa-0898-4596-a0e5-f67a7f2e8eb4">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_ATSRevPosition</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e1dd46df-724f-4bc4-bf80-b92189772446">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_ATSRevThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bd1c8b12-5d4d-4d04-a0d6-23ef4bf6d95a">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_FDifference</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b71035e3-1434-4d12-9c3f-1581c4c3af45">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_FTracking</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e37e410e-f313-46a3-a221-6ed91e4937d6">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_isAtsDetected</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="de0d68e7-7495-46fc-a23f-1800dc0b5455">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_isAtsEnabled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ccf26198-7972-4b9a-a60a-f80377149db9">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_APDet_isInputsAtsValid</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="35450638-caff-4cf6-8e0a-e7edbfc39d89">
        <dataName>portSWC_ObjDetLyrBus_APDetBus_UsgHist_ATSRevReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="920d8f63-a998-497c-9e85-52c9ad11ad66">
        <dataName>portSWC_ObjDetLyrBus_BlockDetBus_BlockDet_hStallPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="25ff6a02-5aef-43d6-ad0d-42b048a637e3">
        <dataName>portSWC_ObjDetLyrBus_BlockDetBus_BlockDet_stFltType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a34907da-96e2-4a95-a60d-0017909b03fb">
        <dataName>portSWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="285c4058-1356-4fd4-89cd-08e7b9cad109">
        <dataName>portSWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3db46fbb-a268-4233-9ded-79c7e1afcb9b">
        <dataName>portSWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5ed0a498-498a-4bce-8e10-ea683ee7b8ca">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isInterrupted</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ce409342-47e6-4b2c-a9f0-f4fa47cf53cd">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3ca1cdb3-578b-4143-b8b1-51c8cf48e092">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearningAllowed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dd2c1f89-0088-49e2-b099-1edbb9f52463">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d57afbaa-f786-4a03-abc7-b0a159a779f7">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newSoftStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="672e56d2-f71f-4a10-88bf-b1d3a0f00f84">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stInt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3e2923b-1e8f-4ca0-ab28-ee03932fbd45">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fc8dae83-3857-4a4c-9330-3a3e280c21fc">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="21b6381b-43d5-4bd4-8154-71c3779c6c3c">
        <dataName>portSWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stRefFieldReq</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="09c211ac-99a1-4918-9393-0ee556532fca">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_MFilteredTorque</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="42a262d2-0c83-4bf5-a545-9acc371b9e98">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_MbrakeTorque</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a542759e-84c5-4666-9ad6-bad75a47026c">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_TEstCaseTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d9fec1fb-38f1-4280-b1b8-d51fe986c4c6">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c394e2df-e75b-479a-9ee0-588eeec59d46">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isCalcForTorqVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0c444ff2-7fdd-4ca9-bce0-63c3793822ab">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isEstCaseTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d331ccd8-8e0c-4b08-9bbc-542f37c0e812">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isEstMtrTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4a163642-0188-45c4-9e97-ad2f490e99fa">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isMotorInStartUp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="086b7f99-b3d1-4da5-b766-d81693606840">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1fd1f18d-1f2e-4b0f-8ba2-5dc42e003197">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_stCalcForTorq_FcalcForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dc9c8627-ce16-47ef-a07d-2b1d65ac9b68">
        <dataName>portSWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_stCalcForTorq_McalcTorque</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d154efa1-772b-4a31-965e-a53d33a74761">
        <dataName>portSWC_ObjDetLyrBus_RefForceBus_RefForce_FRefForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e6ad6338-d009-46b4-af22-237c1d89d085">
        <dataName>portSWC_ObjDetLyrBus_RefForceBus_RefForce_FreferenceForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2c9a76f1-d356-44e4-888a-************">
        <dataName>portSWC_ObjDetLyrBus_RefForceBus_RefForce_hPositionOfForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dd554252-ff4d-4b7f-922f-0bd53fe9b561">
        <dataName>portSWC_ObjDetLyrBus_RefForceBus_RefForce_isCalcRefForceVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="efc1e1ec-6d14-4f3f-a167-857a0d6e9838">
        <dataName>portSWC_ObjDetLyrBus_RefForceBus_RefForce_isPosOutsideRefField</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c8a8d99e-6ddb-47f2-a80c-6f052c5096a4">
        <dataName>portSWC_ObjDetLyrBus_ThresForceBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e3aefe04-482d-41a0-8716-d811c43dbec0">
        <dataName>portSWC_ObjDetLyrBus_ThresForceBus_ThresForce_FatsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a00db8b8-05fb-4d84-8585-a1165addfa97">
        <dataName>portSWC_ObjDetLyrBus_ThresForceBus_ThresForce_adapAtsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="92f50131-7f87-4550-9c99-1564815d778f">
        <dataName>portSWC_ObjDetLyrBus_ThresForceBus_ThresForce_isAdapDisabled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47e4c4cd-90ab-42b5-851a-406bfed8ca34">
        <dataName>portSWC_SigMonLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f5e286c1-162d-4fc2-b7f7-b24c2970acd8">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3b4d9f3a-0d13-419b-95bf-d1d604bf7cbf">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="20cab8aa-60d6-4a13-af57-41d0514daaeb">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stAmbTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="36af198a-4e1e-4227-9cee-3fb76f70e5b7">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stMCUTempNoCritFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="04cf5005-3423-477e-b5f8-10c321223d4d">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_IoHwAb_SetHallPwrOutM1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f842d5c4-d2bb-4e37-a6e7-df072f901eed">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_PosMon_isUpdateDisabled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f058c792-51a3-4198-bb0f-d935729cddce">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3419e6e-dcbe-4685-a067-4960db3f436d">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6ba40480-b338-42d6-8eac-308c81db6aa8">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8af6ca17-e160-4d7a-a603-6f390ca3b4bb">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_stMosTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9b1418dd-73ad-4bc6-ac5a-2171ce0b8238">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_curPanelArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ab545fa6-1f1d-41ef-b333-44885c829426">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1ec27013-98c7-40b9-ad76-f2a2966fd92a">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4295484c-f6da-4407-a9c9-d63afffab558">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hComfortMidStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8c803530-0534-4b9f-9eb4-7fc5c9b49ba0">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1f8fd824-d6b4-4062-929b-dde518ffa6e9">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="37cd18be-3840-43ec-9da5-db475f929b3a">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hNearClsSld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4ff40d76-1c99-4610-b60d-775d8ac46010">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="87c772fb-e62b-4673-b2f8-9c334dffb302">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e1568325-5026-4b99-afe5-865390ceb129">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="923c21b0-c400-471f-b150-a72ed5fe6786">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="21968d93-9da4-41d8-b560-00c0bfa1c9fb">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isOutOfRange</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="765cb9a9-9857-409c-b163-a8e92d28c076">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isRelearn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dc631dd4-397c-4a4f-b4f0-5b5526441f63">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8596236f-d4fd-407e-9521-238450dbdc5d">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b5966976-2a07-42be-9aae-12daa83d5307">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_panelCurPosArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3e5b6333-60fb-48d5-beb9-8f83aefd1ed2">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_relativePinionSize</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3bd9709f-ca1d-4215-83e7-be79357fecdf">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_stPosSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d7a2b93b-715d-43d3-a3fb-cc4c557eb964">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_isDebSwch</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2e9cbe3f-0d8a-40af-9b51-6f96aebd5f80">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stMultSwchFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="48fb57eb-ba1d-4ac8-8de3-46b81adc5711">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2807e051-b68e-4a87-b71f-09ab2f7b227f">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchStickyFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="73fe4cd0-745c-46e5-a7e5-42a582c287f3">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchVltgFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f46d3053-972b-4a32-87ef-51a8d14dc804">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_Pullup</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="38b7e9ca-6b89-4818-a42c-d5e8db6bff03">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="17f60110-70ba-47b4-a50a-77d7be69c677">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="*************-4e5f-97da-1868ee1cf481">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="eafa7bf1-e578-4a37-a271-8737f4ce8c66">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isOverVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8f2757bd-5687-4459-b1af-cb9e1e477ef1">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isPwrLost</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0cba1134-4e7a-4091-b974-2fdd00b1a4a9">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartUpRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d557ac26-bbda-4808-825c-c224f6361931">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f2fcf065-4040-4fc6-841e-8ed02b38b3f1">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isUnderVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2b0d4023-3db9-4a7f-bc26-a1080c45f6b7">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isVoltDrop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3421b9f6-3ca4-40d1-80de-05dc36361d81">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b4f462b2-070d-45bc-ac2f-e36cc6a08539">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9c6b224b-81a4-4151-8c27-de1774b6976b">
        <dataName>portSWC_StateMachLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="02ee9519-61dc-49be-ab20-392e81baeb65">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isATSActive</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6e958e01-f487-4a0d-ae1f-730a4ddd9c65">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isDeNormed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d9a2b842-0423-422e-b099-b8f4335e23bd">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isEmergMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7f291035-26a0-43ff-addc-9def3e468c92">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isFactMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="45060bf3-371b-4813-9d48-b66287a5ee7c">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isMfgMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3bd0644a-9706-4d8c-87cd-b236edeb987a">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isMtrStalled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="aa3f4630-6be1-4300-96c0-ca27150991a6">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isUnLearned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dbce69b5-64f7-4dee-bc74-a12bb208e92d">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stCurrMovStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="151a6807-f26a-4104-a336-e93e88d58a4d">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stEcuRstResp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ea45e6bf-5df9-431d-a5cc-b2ea5eb8d705">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stErrMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="41dbb0cf-b39d-4cec-8af2-49fc254a1e5c">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stNewMovInh</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c6b13d5e-0be4-4982-807d-db1917a72aca">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stSCUOperMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f630002f-4c08-408c-94bf-9484d5714181">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stShdnType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="07a366b4-1991-4501-9ce2-01533b08bc81">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ee51664d-41cc-4b12-911f-37e9700f038e">
        <dataName>speedPID_I</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a9018de7-3831-47bf-9b7d-e02792c89c24">
        <dataName>speedPID_P</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <priorityDirection>HighNumberLast</priorityDirection>
      <editorState type="sltp.mm.core.EditorState" uuid="ef1cfea2-484a-420d-ae76-3d2ab30b0402">
        <isSynchronized>true</isSynchronized>
        <panelState type="sltp.mm.core.EditorPanelState" uuid="0bb34eb9-3f07-403c-a299-ffd7020be893"/>
      </editorState>
      <rootTask type="sltp.mm.core.Task" uuid="3f5f5e2f-c92e-4928-bf70-5854b7d89fe6">
        <context type="sltp.mm.core.Context" uuid="26d13ff8-c651-4442-846b-cdd83e5cf026"/>
        <explicit>false</explicit>
        <name>Default</name>
        <priority>-2147483648</priority>
        <subgraph type="sltp.mm.core.Graph" uuid="b88c2219-1bc0-4f56-a43b-d7a391b4929b">
          <tasks type="sltp.mm.core.Task" uuid="0e32183c-0a6d-4dbf-a50e-2f01b947ed5e">
            <context type="sltp.mm.core.Context" uuid="26d13ff8-c651-4442-846b-cdd83e5cf026"/>
            <explicit>false</explicit>
            <id>1</id>
            <isTimed>true</isTimed>
            <name>D1</name>
            <priority>40</priority>
            <rates type="sltp.mm.core.Rate" uuid="fa7412f5-1f62-4b70-9573-bedc74651a6b">
              <annotation>D1</annotation>
              <color>-436207361</color>
              <identifier>ClassicPeriodicDiscrete0.20</identifier>
              <rateSpec type="sltp.mm.core.RateSpec">
                <period>.2</period>
              </rateSpec>
              <sti>0</sti>
            </rates>
          </tasks>
        </subgraph>
      </rootTask>
    </sltpContext>
    <stateWriterToOwnerMap type="ModelRefInfoRepo.StateWriterInfo" uuid="a9ff0ff7-1888-4e32-8597-f9734f707d7a"/>
    <stoClientDataRegistry type="sto.ClientDataRegistry" uuid="e35b7dfa-7cc2-4759-91c2-96809caa4a97">
      <dataSets type="sto.ClientClockNamedDataSet" uuid="7c81127f-9800-4574-b5ca-db8f4f01dd35">
        <tag>sltpEvents</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="4a1202a6-07e9-44e3-8604-3f0b82ace67b">
        <tag>sltpTaskGroups</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="96d260b1-ffff-4afb-9c7c-ec3d74ba7f2d">
        <dSet type="ModelRefInfoRepo.SltpTaskData" uuid="850e1676-7587-442d-beba-21129c92db69"/>
        <tSet type="ModelRefInfoRepo.SltpTaskData" uuid="850e1676-7587-442d-beba-21129c92db69">
          <dataName>D1</dataName>
          <linkedSet type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="96d260b1-ffff-4afb-9c7c-ec3d74ba7f2d"/>
          <id type="sto.TaskHierarchyElementId">
            <id>_task0</id>
          </id>
        </tSet>
        <tag>sltpTasks</tag>
      </dataSets>
    </stoClientDataRegistry>
    <varTsUIDMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="61f1dced-6547-4f4f-b0ba-9a14132e9253"/>
  </ModelRefInfoRepo.ModelRefInfoRoot>
</MF0>