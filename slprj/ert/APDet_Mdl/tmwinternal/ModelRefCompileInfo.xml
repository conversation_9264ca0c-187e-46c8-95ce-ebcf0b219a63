<?xml version="1.0" encoding="UTF-8"?>
<MF0 version="1.1" packageUris="http://schema.mathworks.com/mf0/ci/19700101 http://schema.mathworks.com/mf0/sl_modelref_info/R2022b http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112 http://schema.mathworks.com/mf0/sltp_mm/R2022b_202203181029">
  <ModelRefInfoRepo.ModelRefInfoRoot type="ModelRefInfoRepo.ModelRefInfoRoot" uuid="00bbacc2-5d23-499e-a9fb-3cadb026d350">
    <JITEngines>s64lSo4eY0MPNuxdrSnYazE</JITEngines>
    <JITEngines>sJXmtpgjAuAg7pMVlS1MPaH</JITEngines>
    <calibrationData type="ModelRefInfoRepo.CalibrationData" uuid="f5edf8b5-a230-460a-bfb4-9ad949712083">
      <InternalData>[{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;}]</InternalData>
      <ModelName>APDet_Mdl</ModelName>
      <RootIOData>[{&quot;Name&quot;:&quot;MtrMdlBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_DataHndlLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_OperLogLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_SigMonLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;RefForceBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;ThresForceBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;LearnAdapBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;APDetBus&quot;,&quot;Profile&quot;:&quot;&quot;}]</RootIOData>
    </calibrationData>
    <childModelRefInfo type="ModelRefInfoRepo.ChildModelRefInfo" uuid="bca7576d-16f4-48c9-894b-35510bbbb1d8">
      <isSingleInstance>true</isSingleInstance>
      <modelName>APDet_Mdl</modelName>
      <modelPath>APDet_Mdl</modelPath>
    </childModelRefInfo>
    <compDerivCacheNeedsReset>false</compDerivCacheNeedsReset>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="aa15f018-38f7-42eb-bcfc-580fced6fb3f">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cb674c20-1861-46cf-94d3-b8be6391955a">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a2a22f08-d3f2-4ce7-bb0a-fc6afd3257fe">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9b30f86e-fb7d-45a2-a80e-7cf2da2e9df9">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="06d1ccda-d0a9-4b34-a39b-477d85515240">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3392bffd-90a6-41a1-be0a-cbd49629eb5b">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="498990a7-7208-4f04-a719-a474a7f87324">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="025fc2dd-54e4-4615-9263-130c9bf088bc">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e3280707-ab95-42b6-b299-ef5132517937">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="aa9b761c-899a-4b1c-92bd-d103a0bae4a7">
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4ea66fdf-716d-47a0-ac21-a9b475ae8779">
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="aebd09fb-a4ef-416a-9b54-7d581b1bf6d7">
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8fc4459e-97ae-44f0-9256-bcc968c8f6e1">
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="024e7ca1-0931-47db-9e7b-c8d68a0d1f77">
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b8e23827-b08b-401e-ad83-0fcc687b6600">
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a04ceee2-20c6-4e76-bf38-bb92fe63fbe4">
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="205a87c9-69c8-4920-9f32-731bd9cc198f">
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="10ed792b-4379-44be-a4ea-d67eec0fffa1">
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="528d5eca-7c8c-4ec4-8a69-93dc9c073e47">
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="84aa595d-6905-4da4-8ce1-e78a49092d8b">
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="953d7180-57b1-4b09-ba41-b3be581b3c42">
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cf34e04a-ad26-4a4a-a8b2-158e013b2aec">
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2f5ba222-21f8-4cf1-828a-5836ae3fdf8c">
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="883b08ea-eaea-448d-9033-8dcccd82de5e">
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f297acd2-4aa2-4b61-8b19-099779d35945">
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="29d73acb-3b85-4997-b62f-7866ae89b1d6">
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="97190b33-559a-4fac-80c2-1f9630ab2638">
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e4502387-5b75-4dc2-85a1-40a31239281c">
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8dbfcba0-3c04-4fdc-bc6f-ee1fb1b5315f">
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8b0a4591-c489-47f0-b268-0c0dd410bcb1">
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e4911492-47c3-4bef-a02a-f1334618690b">
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eee36ea2-fd54-492e-8d11-ec5e3eb6420f">
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ac7730d6-98d6-49bd-b77e-81356319a25c">
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b38e950a-0ff3-49ef-893c-e3d282bc9be9">
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f4175e35-4acc-45d3-972f-4d5713e591e8">
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ad1a7b36-0f15-4bc6-a50c-e2e18b2accc4">
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f2313791-3795-452c-a483-ff108d18fe1a">
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a41ed407-3deb-4174-920f-bfe6eb2d7cea">
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cd3940e6-ff2e-44ce-9c9e-f573d17314e2">
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fb6cc82e-4837-4690-8cce-a32b78123841">
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a8f3b00f-8eda-4881-abec-270eaa7315c1">
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e0225306-3612-420f-af86-9a6c37d4912c">
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2a8ccb0b-4b76-499a-9218-b2a6d901b035">
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3e5c374f-de2a-4ba5-9d55-96cf044290cf">
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="93dd8451-a88c-4d0a-a898-953013e03ebb">
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fc21b48f-cb26-48cc-961a-4f33e6be8f4c">
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="66cc86f4-963f-4fb1-aa8e-3ca538f68644">
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d6bb71db-0856-4b44-9c83-e52dd193fcd1">
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9cd590e4-bb5d-4cc0-a16e-c090326f82cc">
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fd07f067-d612-4a73-8c83-0e9883f512d9">
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9206cf64-74ec-4082-8a09-7276f1199e31">
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ec62c2f1-c164-4193-8bad-f1b47d3b80ec">
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b91b14fd-76aa-4892-88be-60c624fab361">
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8a607202-253f-4e37-ab9b-9f784fb5ca02">
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2d2c781c-005a-419c-a4c0-1258daf2b745">
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3e2a8b47-5180-472b-933d-b5aeae978f8c">
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="419f4b54-6b74-43fc-8cc4-30cbbffccb38">
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2945f373-ca45-470c-8ca0-202f5fc41930">
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="79a4b774-a2a5-45f1-84dd-1abd4addcc62">
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="03d4c45a-da0b-4eae-a66d-8ad6038c0cc2">
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6c73f001-62b1-48ea-b8db-fe8f64f652d5">
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e072ee71-eb50-4037-814f-ea1eedba45ec">
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="62b25e61-b9e1-43ca-a47c-7efe76cfbe91">
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b52324d6-dc72-40a5-acea-8176b303c216">
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="20726d5e-7bbd-4733-9151-7c3ed4965e9b">
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="850a6e75-f4d2-4d17-8cf7-efdd8adaa57e">
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b8cb1640-db92-49ad-8de9-4ba2755b1530">
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8dd8e7a2-655f-41c3-8418-b4be124efa36">
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0b7a89ed-450f-4d44-bcb9-84794c859ba7">
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="268058a0-51c1-46eb-aeef-4ec32f0f6409">
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7f46cd6d-8243-4fba-9d8a-1a0034ea792f">
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ae2b322b-60ad-419a-9add-eb5037537b98">
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="80ab191d-0da5-487b-bb83-4c63adddb6c7">
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="600f2e17-5a19-4052-9674-3374f2bf9d7f">
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="45e573f0-7d1f-4969-bd88-55d308ed29a9">
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4513ab70-39ce-4e1d-a049-43f1c63b91fc">
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="91eaba93-7451-40cd-81bb-70282009bfb1">
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8ad05f1c-3479-4306-ba5b-2de498f475d0">
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="107031ac-740a-4050-9486-fd6e4473d0b8">
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9b415ee4-49a8-4fdd-a04c-a0f2d309c298">
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7f7a4001-d5c9-4003-aa62-b07f2c2aa17a">
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fe7361fc-715d-4f8a-addb-82fe72d0f420">
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a04d653c-9c98-4c8d-8e8b-2ebb69d8275c">
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9924a3d4-2760-4b2a-ac31-db9792a0174a">
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d8a615c1-3f25-4ae8-9f86-154cbeccd57c">
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e1cf1d61-e2f6-4fc1-8cf0-f993369edab2">
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b2c30b1f-306d-417b-a6d0-0c2972e6d5c9">
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e4155021-ebd9-446a-9b04-104618e7435f">
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="87a25683-d3d9-4975-8bb6-8471f99c2166">
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f2af07d8-a946-4842-b90f-52e1a0ebdddb">
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="30994175-66c7-4792-b1d5-baaadd29d269">
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="48062216-d115-4902-aaf8-5614833db4ac">
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f6f96a54-e091-47a4-855b-8b25a86c00a4">
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6f50a0e8-8aad-40a6-ac80-ead8317eb2de">
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f93a2b21-33b5-4712-805d-9095011298a0">
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="527185ab-8aaf-4515-9291-33049f2af98a">
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9f69e737-5f21-40c5-a492-43abe2fa5e9d">
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="433f47e3-86e5-4700-905f-428db5d32565">
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f1110a91-fd8e-4788-9df8-3a6f4af08d3a">
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1b63aeca-b79f-4a01-bc5b-35238bce50e3">
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="003ddc4a-0f1c-4e8b-8acf-91c8da95a4ed">
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b43d1f59-8360-43dd-a816-74cce13d3058">
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="056c5d97-db53-419e-b435-c338350b1039">
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c3fc0527-39dc-4ea3-bbf4-5fdc35de5a2b">
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="086a2374-965a-4b9a-adc9-0f510d892934">
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b9001094-32b9-4ff1-b504-9bc8aa643a42">
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="385c932c-4237-477d-85bf-2a142705017d">
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f2561bca-a7e0-4b5d-b2e4-bb4eb242e329">
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2103fac7-90b7-4fb9-a83f-f3d32495b3cc">
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d157aeea-62ad-4384-b4eb-6201d888538d">
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="908adf88-058c-46bb-aca9-34c148d21bd8">
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eb056391-3d5e-4a8d-88af-462872378feb">
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="05038f2a-2ec6-498a-9058-ae6654878fcf">
      <grAndCompPorts>112</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="30d00d18-da1f-47f2-99c8-d6c5d56e4be2">
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2f76b5bd-1122-44d0-8378-30ec95d8b4b8">
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="30dbe4bf-17cf-4052-8834-395c96b1d8cd">
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="40030e28-b95f-48ab-96e9-0db875a19ac3">
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4bb5be0a-2d52-4de1-87ac-09364a5de7ab">
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ac282079-3711-4f6e-bcee-277b4e76ab06">
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="db904927-a48e-40ca-a2e5-1d8f8754c090">
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fb534138-0678-4b42-853e-80d4a564a118">
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6eeeddb5-f275-4bd3-9ae0-05a44b861a85">
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="89d0839f-79a6-4a34-b43b-cec79c04f34e">
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4ff7b0dd-80ca-407c-b4bb-4f22e69839b6">
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ade22cfc-9e49-43b7-b1a3-c8729fd796b4">
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="dadf4843-8365-44f6-a111-816722e9f463">
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="15f50b46-9c72-4c17-a4c5-9f3b60606bb1">
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6c3427bb-4fc7-4f57-95dd-270793d06449">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0a58f149-fa4d-4ce0-ac33-8dcb4c83fe06">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d2ed6f68-3de1-4df7-a9a7-32a05e15b6af">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a38ada93-206c-4921-a625-644deb5fbc03">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="78b67ba3-2aea-4ae2-afb1-8c7dd2bcdabb">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3b4d257f-6978-47d2-af16-97bde2f28a54">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="84cad33e-4809-41a6-b3ce-b07547259b18">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="572b48ef-3f40-4f5d-96e7-1aa1d2effa81">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b0197f2f-bec0-4361-bace-3e89829aee29">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compZcCacheNeedsReset>false</compZcCacheNeedsReset>
    <dataDictionary>APDet_Mdl_Data.sldd</dataDictionary>
    <dataDictionarySet>APDet_Mdl_Data.sldd</dataDictionarySet>
    <dataDictionarySetForDataTypeCheck>APDet_Mdl_Data.sldd</dataDictionarySetForDataTypeCheck>
    <dataTransferInfos>AAFJTQAAAAAOAAAAOAAAAAYAAAAIAAAAAgAAAAAAAAAFAAAACAAAAAAAAAABAAAAAQAAAAAAAAAFAAQAAQAAAAEAAAAAAAAA</dataTransferInfos>
    <defaultsCMapping>{&quot;Inports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Outports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ParameterArguments&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;LocalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InternalData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedLocalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Constants&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;DataTransfers&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ModelData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InitializeTerminate&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Execution&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedUtility&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;}</defaultsCMapping>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="103a7a19-173e-4d56-afb5-3b7504e69a93">
      <blockName>APDet_Mdl/AntiPinch/determineTracking/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>4</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="b32c3c12-9d7f-44d4-a97f-b8a8503a6b8a">
      <blockName>APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive/determineInclineInRange/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>4</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="e31d21a1-6699-465d-a864-184e1d5fac20">
      <blockName>APDet_Mdl/AntiPinch/equalTheForce/getSignalsFromBus/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>4</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="3691fdee-9bfb-4c7f-a2e1-d65089ca69fd">
      <blockName>APDet_Mdl/AntiPinch/determineTracking/distanceTraveled/Unit Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>5</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="c387b797-a44c-461c-8dc7-6aecffb0677e">
      <blockName>APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive/determineInclineInRange/Tapped Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>2</dataType>
      <label>X</label>
      <width>14</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="57f6cf8b-a4a8-4e99-830b-47b0e1344077">
      <blockName>APDet_Mdl/AntiPinch/determineTracking/distanceTraveled/Tapped Delay</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>3</dataType>
      <label>X</label>
      <width>14</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="93deacb5-e9c4-430b-841f-e5586b43ade6">
      <blockName>APDet_Mdl/AntiPinch/equalTheForce/determineForceEqualizing/Detect
Change/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <discStateRecordInfo type="ModelRefInfoRepo.StateRecordInfo" uuid="20b6791c-ba44-45ca-9519-45eae9235ef7">
      <blockName>APDet_Mdl/AntiPinch/equalTheForce/determineForceEqualizing/Detect
Decrease1/Delay Input1</blockName>
      <crossMdlRef>true</crossMdlRef>
      <dataType>8</dataType>
      <label>DSTATE</label>
      <width>1</width>
    </discStateRecordInfo>
    <fundamentalSampleTimePeriod>.2</fundamentalSampleTimePeriod>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="c7216c71-6c49-4fc4-9086-efaf7d97a1d9">
      <dataType>CfgParam_CALBus_t</dataType>
      <name>CfgParam_CAL</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="360be757-5e28-46d5-a33b-196ebeab4e75">
      <dataType>MtrMdl_CalcForTorqBus_t</dataType>
      <name>MtrMdl_stCalcForTorq</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="0b57b83b-93c7-4e08-84ea-efe659f51685">
      <dataType>int16</dataType>
      <name>AntiPinch_FDifference</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="613d9e60-6b04-45e7-b271-da110e4eda99">
      <dataType>int16</dataType>
      <name>AntiPinch_FTracking</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="a75184ec-dcc5-4d9c-a58f-7463e34b55fb">
      <dataType>boolean</dataType>
      <name>AntiPinch_isAtsDetected</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="52367f96-45d2-4d83-8163-6aff524cb57b">
      <dataType>boolean</dataType>
      <name>PIDDID_isATSEnabled</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="927578dd-87f4-4ac1-a55b-7cbcac58f2b4">
      <dataType>boolean</dataType>
      <name>PIDDID_isFactoryMode</name>
    </globalDSMInfo>
    <globalVariables>#APDetBus#APDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AmbTempMonBus#AmbTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AntiPinch_FDifference#APDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AntiPinch_FTracking#APDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AntiPinch_isAtsDetected#APDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParamBus#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CAL#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CALBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_MosfetLevels#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_hPosBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#ComLogBus#ComLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#Configuration#ModelConfig.sldd#</globalVariables>
    <globalVariables>#CtrlLogBus#CtrlLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#LearnAdapBus#LearnAdap_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MOSFETTempBus#MOSFETTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrCtrlBus#MtrCtrl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdlBus#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdl_CalcForTorqBus_t#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdl_stCalcForTorq#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#NewtonDividedByCentiNewton#APDet_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#PIDDID_isATSEnabled#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_isFactoryMode#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PnlOpBus#PnlOp_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PosMonBus#PosMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefFieldBus#RefField_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefForceBus#RefForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#SWC_DataHndlLyrBus#SWC_DataHndlLyr.sldd#</globalVariables>
    <globalVariables>#SWC_OperLogLyrBus#SWC_OperLogLyr.sldd#</globalVariables>
    <globalVariables>#SWC_SigMonLyrBus#SWC_SigMonLyr.sldd#</globalVariables>
    <globalVariables>#ThermProtBus#ThermProt_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#ThresForceBus#TheshForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#UsgHistBus#UsgHis_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#UsgHist_ATSRevReason_C#APDet_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#VoltMonBus#VoltMon_Mdl_ExpData.sldd#</globalVariables>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="191150f0-297e-49ed-9d38-60ea68e2a5b9">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="253c5979-ef74-472d-b1ee-6b0d7c7e706e">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>49</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="362e2918-1582-400a-8a0f-ddfd92395f6d">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>77</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e86a096e-b108-45f0-a4b9-1c201d54d461">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>112</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="65572615-b78d-462f-a42b-093cfd4845b1">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>115</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ee6b7ed9-cc37-473e-b469-b6be04ba2f78">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>116</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b1333abc-3e72-4264-8a4a-573675fe2384">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>125</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="616d2011-c332-48fa-9496-d5fac586dfe0">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>126</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompOutputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ec3f4fc0-7789-407c-94d8-7e70142e881d">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </grToCompOutputPortsMaps>
    <hasBwsAccessed>true</hasBwsAccessed>
    <hasBwsAccessedByAnyModel>true</hasBwsAccessedByAnyModel>
    <hasConstantOutput>false</hasConstantOutput>
    <hasModelWideEventTs>false</hasModelWideEventTs>
    <hasNonVirtualConstantTs>true</hasNonVirtualConstantTs>
    <hasStatesModifiedInOutputUpdate>true</hasStatesModifiedInOutputUpdate>
    <inlinedVariables>#NewtonDividedByCentiNewton#APDet_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#UsgHist_ATSRevReason_C#APDet_Mdl_Data.sldd#</inlinedVariables>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d7dd59a8-ca08-4548-9b3e-ba068e4d9db5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>32712.0</designMax>
      <designMin>-32712.0</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0971c402-5fb7-49f1-9186-e0ed69ef3a0f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4a47a66d-d316-454b-9a49-f3128e785ffc"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="01596baf-5610-4827-87ae-7f6f92c331a2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>32712.0</designMax>
      <designMin>-32712.0</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="853cffea-6328-46fb-93c0-e3b44b001a54"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d9592a30-fd10-467b-99fa-e884e5044107"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="948fb89f-14d9-45b9-927b-ccaaf0676c80">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="67820fd3-0c9e-4a89-883f-33ba1aadd91c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b033389f-8c3d-4c95-b134-5c2b231c528e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="41f7115a-6aa1-4be4-86e6-7ef82386219b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3f5e546a-5a81-4a12-989d-cc50465965e6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="95f5d674-0a41-4db1-b5e8-dbe7a9a53eff"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="89d797e7-df4b-4f40-aef2-32fd7171fd3d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a33cdae0-7ce5-41bf-8347-e42b22aeb7f3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="47e7f46a-49b5-4a33-a608-5ff03db1f249"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="745b1968-602a-40c3-bbaf-6b5f61431432">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="560efd6d-f4a3-4919-ba17-9e6e01b58429"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ea9d4e8c-3199-4523-a3a5-90f971b8db1a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a48bca4b-292b-4c88-add1-68e645e16026">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="06f142ba-ed27-4c6b-91ca-a506d88e7c67"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="af20d56d-1e05-489d-b44b-307247a0d335"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="83ae6348-6bbb-4b03-bd49-4b51e80aa145">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f7a19bb8-dd72-40f9-b57b-c990fc584458"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="103b83a8-f5a7-4f9a-9b9c-294fc000427d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="be292b1f-7b8f-4b34-bcff-c5dfe969b472">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a8a44838-dd81-4e41-871f-a8c577133d4f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="96b5ed13-e2d7-47f6-8e56-1283108af74a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3f479069-f162-4373-9e85-0d846fdd5e78">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1c006d0b-d55c-4a64-a760-5b2bcd3ab3d5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6dac2793-9c21-4e90-b39e-0473ded8321b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="130b002f-db44-4f20-a49d-ae28232c4964">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="da9b8180-285c-4413-adcd-348bc33532c6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f45f281f-9a80-4262-ab30-05ccf4c205a0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="14db090c-6bcc-4dca-bb37-1d21812a0d53">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e8c54ed2-e506-46ce-af0e-6e1560058fad"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="40c9780b-3cf3-42f3-abca-59934cd5d6a8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="011df87e-28d9-4dd3-8d97-9e9465c84932">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b999a566-f979-49e0-863a-8cece1c8033b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7e9e86cb-6b26-4de9-8872-e8e308a49172"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1c23b2a1-665d-404d-b2b7-a7cd124d7589">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8595b02e-3bcf-4043-ad23-c3512b64f2f9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c38b59e6-c105-43d5-8d0f-f48c82e60bf8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9db483d2-9962-4b57-bb49-ff60c28de2a4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5f49f96b-2c00-4d11-88f8-cb267ad79ca0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="611f51bf-598a-41d7-b5df-251b7dd91254"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="27fef232-90c1-4c25-b649-a645a0edf197">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5aa6a876-ada3-4ab3-955f-66b4327935a7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="95dddd08-02c1-4257-8e2f-64655af9b19e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ca0fc4e0-7a47-4d71-bd4f-425c3dda28da">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3b538cff-0028-43fc-a2e1-6c3b2ed8b56d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="30f8f450-eb7b-44f7-a707-edc5d31cec38"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3e563d7d-edf7-446c-a3b8-209b5d43fa1f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="31310caa-afeb-40c1-8d09-4ad9b8c9cd50"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="83e961fc-c95a-4f05-bb7c-abe346611b99"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0b74335f-556e-451b-9526-fad5b1d2f5d8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0f757977-a3ab-437e-a22f-9a07d414dc29"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="13677658-fbc4-4760-bf4c-8dcde69a5b79"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0e5838ed-fd13-4f41-9813-8263474053a6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7dc00792-a2d8-498d-8932-a829e74d0136"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9e97a1a5-4830-47dc-943a-be3d8a469d7e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e8f1e03c-c29c-4f09-b49f-81f7991d806c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="371ca058-e023-4df1-98c1-27bdc2daab0c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="*************-4c5f-8beb-47f1416fb70d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6185c08e-bddf-46a3-8769-f9289609f472">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="296ce820-f9b3-4c7f-8851-f9f53fac8071"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6e59b352-6c48-49bb-87d6-b15039ffd055"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d036a7b9-ca3c-4212-86b0-86fa7407fe11">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>16777215.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="47e9372d-8658-4d96-a77c-42f6189e7839"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5bfa781a-080f-4b85-970e-183c3bdc070d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b96f344a-e326-4c90-9e09-b19e8d338ae3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d199c02d-1030-4db3-a917-d9bfb103706e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e4cdfeaf-4d5f-446f-8917-bd9cc7a5e81a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="38849217-df3b-401c-953f-57ab405d8304">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a8a0c328-fb44-4880-b7e3-f3590e92c53f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d6f97ee0-a1f9-452c-a871-c446ac16297b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="425c26fa-6ef0-4e55-b129-f9398746763f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d9cc9d29-cf71-4d84-a7f8-7f1792e0dd21"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e2d2d813-47b9-4193-953a-d038092151ab"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="17c8595a-6da0-4845-9ab3-1a26f0cfa5d7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="086bd29f-df9d-447d-8e14-************"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d568020a-9891-4517-994c-feeb17e3d6a4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6d2a0460-1d3a-47d4-be30-e348a37605fb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="30119fea-1fc8-4c31-9c44-a38bc7533802"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4b520cf7-cfc3-4342-8401-482b28f08a3b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1693d675-f4fd-449d-ac35-827fbae12014">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9ba7ffc4-2322-4088-bd0e-e86a37a70652"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="223a5e80-9225-47a0-af09-9dca4b068388"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fb306fdb-f0b4-4830-8bb9-4da05fff5563">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="df7d28ec-f533-4fed-abb4-b6acf4808636"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c7505226-5611-4cac-9b8e-63272ce8590a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e8906e7b-0c2f-4b4e-add5-1a1e28daba8b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1c8a8910-5c11-409e-9877-b562dd4053da"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a8e21c13-1318-4de3-a61d-b5a669968e92"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5050bb4e-980a-4e70-839d-f1a60f58de40">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ba8f3305-3b4b-41fb-b89a-ad4d0978717c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7b68dac2-89b8-4529-beda-585dd5e87a22"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e291f99f-56a0-44f6-9cbc-20088fd6e9eb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ddd887b3-b48a-4420-b07c-72b3735bcdb6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="02da5f32-e1b3-4a3e-bd6a-0a3a19131b3c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="59fcae81-e918-44e8-bd5d-024f0f488084">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="064f189c-78b5-4148-a0d3-cd3fff2d0209"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f14ca8d5-f541-424b-a1cb-03a51f172bc2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b2085129-b245-42d6-9c5e-62f59dc9efc9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5f85d31f-f281-425f-a0e5-9d09c8767267"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="651e7efe-42eb-4709-adba-86db5062ea2e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="31c1345e-5937-4cc4-bdc3-5583f4571e48">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fda67b61-**************-c1c319ca6e84"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5ad96abd-d8d2-44d6-8c68-55b137beda7e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b3ee2fe7-997c-4043-8136-ee2a528f99de">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3e0440c3-f442-440b-963f-074db4f77fa1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="85bc1e9d-f934-4eea-b050-8795c8cf608f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ba143722-b425-46e4-948e-d9a87341b029">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b5380148-2b7a-471c-b9ec-85d73ed8bc74"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="49d2434c-5c27-4e4a-9b14-72990a67fa2e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7e732416-d369-4a0e-8233-6df548d5e266">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="124ab103-f95c-44c7-93d9-9d59b3bcf48a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ebe5a339-1a63-48b8-9f02-0e423cc1ab4d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5fd8c067-b18a-4bf9-99dc-37950af24f14">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="74945fcc-3eb9-4f8e-aa1b-f327d5a6e6e9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="294130d7-0837-492a-8939-3d369838d5c0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="000273c3-710b-440d-bdec-2bc1ccb7d4ef">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b645dabc-44b6-4667-af62-fe76d22cbb11"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="36853430-5a15-4cc0-981f-7136d26f33fd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3a256ac8-d4dd-4c26-8a18-bd27e645bd16">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="44d65af4-1ee6-4d3e-af31-184e722111c9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="64f2b0c1-cc56-488d-b125-fe6f1ae1988d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="30aa201f-4f41-4800-bcaa-c76030623a40">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f0faf0b4-19f4-4852-8ed3-0dff60064860"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0a089701-23ea-412b-aa6d-c6d67a1a15e8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2ee0a094-2cac-41e4-a3e0-b9e3a0be96f0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8810febe-3657-4ca2-96fb-a5d3fbf1ad04"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="eca8af76-ecd6-430c-9834-fab3721acebe"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ce9109e3-91ff-430a-88ad-915e2a4e23d7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3534d8f7-e70d-41d7-ae87-49a7f2172557"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="95f3e9bc-cbd7-432c-a2b7-27725efdd9f9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="75f850bc-afcf-4d5e-ab56-0d5e48035890">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0677e594-921f-40d2-a91f-e5a935fbd651"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5a8fdf53-f07a-4d00-9758-d49691baa14b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f9d6666a-0188-4aa4-8df8-046c03cc91a5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8186acfd-c1ec-4a7d-9cfa-7b170c1bb559"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="404d92d8-6200-46ec-9b33-6f4a99abe86c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6f09b868-f908-4d48-9506-d023f963b4f4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1fbff233-b3c5-4f55-b622-de3e350de607"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d30c49a0-95c1-459a-b050-be36f7b1965f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4c45ee27-c0ac-4997-b08f-8cf3a2d12aaa">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7656161d-80b7-460a-8422-b57a0e5b3c6f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a9c71282-5b4f-485d-86b5-ad93fc032281"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f43ac2ec-7994-4b2c-aa73-c31da42b80f1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c5a7978b-254e-4bc3-b66a-9b022fba27b8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dc0c022a-2b81-4ba6-a284-95917b9d0af0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="42eac467-bccb-428c-ba24-775c5268e6c5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4ce07bd3-c9f5-4136-b137-61bbb7a71ec6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2af99185-9d26-438e-8e2f-f59c7b9bdba3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="925123cb-7f00-4ce6-ac9c-f5b9a2dcd4cb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="48077f7e-614a-40f4-a6ca-e9fe0393ec6e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fdc8362e-92bb-4fac-bfac-bd3bdcc3ce27"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="671beb5e-e224-4c53-8bee-e15e7562f950">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fb497e30-c7b7-4e7c-8137-3e851a9fec46"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="03b9d81c-7725-402b-97e6-0f24c510c7f5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="adcd083e-fb52-4d7d-9975-81a7128032ad">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fec2628d-9360-45b8-ac04-7be34ebcf2be"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8f607e03-1be6-4232-8c36-2c0aeeef25cd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7784aaf2-9ab8-4ddd-bc68-128143f310d3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0d947973-424e-4e98-a70a-eb857dba894a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dc14542b-d29e-49ca-b743-566d80e8185f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9f21deca-d5f9-4ea0-ae3a-4b06cbd6beb6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1444e3d2-8b77-4e53-bbcd-cb5ea1d669a5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ed9c3376-be60-4dc3-aac9-6480f1910158"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="*************-4242-bfc7-fee89f93f9e5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="90d4bd41-84fa-4f54-ad52-fab2e98dd001"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fe8d63fd-fe8d-4a26-95e7-b4172596a466"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="20cdc6b6-0c5b-4410-95a0-6fb84a1ee7b4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="60e655f9-9fba-4be7-a85e-6a6141b2d470"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ac8e05ea-614c-4d93-b285-fff89650ff12"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a04c900a-0d05-4d2c-9c1e-6e8d35fe1d4c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="aae73bb0-2c90-478f-99d9-b1c5fe1297d8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b7f5ed34-0b94-4ce9-9fb6-522932eac2da"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="52b18dd2-9d4c-444c-aeef-014c38159de2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bf7c6124-2b45-46ad-aa2d-285d540293ff"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="248b4389-cce8-4ec8-b6b8-5edc7ca8c596"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f1364f39-ea7d-4dd6-a9f0-784b648a29d3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="95eebe4b-85b3-4f2e-8e27-41b47d24219b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="26aa9d1e-fc39-4a27-9e73-d4123f971ad2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="804c5f6f-e1d5-406e-99be-3d28d144a694">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5d31fabe-18ff-4abb-975c-abb99a523709"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="060c07db-ec92-4822-a08d-8bfbb2e736ee"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d5d12db5-241a-410a-abff-94cf7d8c380e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e3106e2b-9516-4be6-bdfd-fef290c3bc9c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="188c833d-2c4c-416e-b9f1-bca125b25b3a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="abfcf340-f034-4f3c-8487-b26f967d7613">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="35cf17ce-58be-42e8-b7e7-48d4aa115f1a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cddbf648-a745-42d7-8311-5d61cffa94a6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5abae359-5def-4ed2-ab72-700b9ce12af3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2cbcbe58-55ef-4002-8704-07f7913b2b2f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="deb1d01a-c0d2-4e03-a825-9e8aad1e2887"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fa7ff6c5-a558-40a2-9f06-8a78bfef1475">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ce79f686-c80c-4959-929c-a1f275ce3223"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8e463f85-cbb7-4690-9926-b806f90110e2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="448d0234-1209-46a2-a00f-e7fe2e097095">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="48d3c7dc-afc8-46f3-a322-d4d5b53a4ddf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="251c3287-95d2-4147-b676-0c7502bab6f9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1321fa49-df91-473a-890e-367dee98b775">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0a9271bc-a7c6-41ff-81c2-71df318eeddb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7045d450-74d4-4509-b5a4-e4d5f18bf4a0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="42384f6e-2f88-4fa5-9fd0-0ae3b195a6d7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0c2219d6-b855-48a6-aba2-051e13850f66"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="37731f75-8615-4390-bc6f-55e8806999b8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3d9ca470-ad6e-49f1-8ce2-0fbfa9944fd1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fe1f9805-eb26-4f5c-a091-768c3d323fc2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="102efa83-57d5-4e89-9b05-7193f14236b1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="df95f5dc-5e9a-4f65-9051-61dce7d766db">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="086a4583-f579-4534-acc6-272ccc906ea8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9ce7fd0c-56c8-43d0-9c00-e5775a852aa5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9b8a0aa2-939c-42ca-a955-c06969b2e47c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9601552a-d5c2-4ec4-8262-1b42e3f33c55"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8292049a-f690-44a8-b84d-deabc9ae337e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="91439081-31e2-472f-92ca-6da0cecab125">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4870b30a-3cff-4bb9-a982-ba705548e274"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f75d3f29-e7b8-4ac7-a4c9-ddd5083e869a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c4f25aed-4109-477f-b8ed-e1902833fe47">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="75e7d63c-82a3-40de-a713-b14e3d23dd10"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="758e52c9-42ef-4fc5-9e9e-05536a2aa8ad"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="16b61ed1-61d7-4be2-bedb-1012361f685e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="70f07440-7691-4525-825f-30134a5d2edb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0977ab19-fc44-4ae1-8077-a7b3190820ce"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="082af42e-75be-4d5d-8980-7e5ad6b1d0ad">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5e446c33-9935-44a1-b06a-2d4b0af4dc60"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8b159fd1-e3f8-4427-90e8-5ef455d478f3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4762e4a4-9f5f-4184-b534-18845a068a8c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2fe72999-1fef-4eb7-bb1c-4008285207ec"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="374656ce-5585-4273-9dd4-447376a2078d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a9662624-6e4b-4adc-970a-e6f98c4cea00">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="494042aa-77ae-42b2-abcb-c6b4c9d7212d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="875df5e7-49ff-47dd-8907-3dfb10b87e2f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f23c88e0-8c24-44d2-aac9-a51557f2d82b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ac3154e9-a4b0-4f49-ba35-32f864c4696b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="63f5d9a3-c691-48ef-8ab1-8cfded23e04a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0cea1c3e-b686-4796-afb5-d73ba3d0104e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d05132cb-b5c0-4c65-8f0b-6203ed30ccd4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e7cf65e1-ecb3-49ee-aad3-3803f449bda9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9b5fded5-8b39-4ab3-ad94-fb68f88c8e96">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6cfbb844-74ae-4c2b-868e-139418d2cbc7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="72672d39-a490-4679-9950-8c1863ea7590"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2c9a12c6-0eb3-4c10-ae4b-402da5ba3590">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="24ef59a7-3c97-4cb4-b949-110a1628408b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ae682040-b2ee-4990-b570-bdb1470dc702"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="36a7766c-75e2-48c1-85ee-70ed7a75f144">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bb335962-e510-4229-b119-481f86f4a43f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fab2d488-fda7-470f-be35-bfc54741d9a8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="468f4f7a-6c7b-41dc-bae5-0f0411da7f90">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="559d7c71-6edb-4615-8a26-609cef3e3c0e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="094306e4-e741-4c4b-9fbf-9fd5b843b561"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c43c204e-1612-4359-b26f-cfec0595139c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3ad3bafc-b060-42fa-9fdf-95adf4df3534"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d616cf92-f417-4aaa-933a-e4ed5cdf7d4d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a69350a5-37fa-4a34-bcde-c6c1cdb2919a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="70ca238a-8336-49a2-a07c-b08dcb3924be"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8e39a9fe-4c38-4d8a-a4b9-eb2e6203dc30"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0fd6cd2a-104d-4ab2-9279-c83a9b1d2f57">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="43b70c7d-8bb6-4e72-9256-3bc99d5d990b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="63696202-2a52-4494-b044-6b569d839ab8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f3932436-5ecf-4ea4-bf35-167fb867c3d2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ed059f2e-227e-4095-83e9-75bbe8692c56"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a37ec7d8-c28a-47aa-b7a9-244b69e13f05"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9a001a3b-4fdd-4513-aecc-2dccbca26fd9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5469564f-4ba0-4b96-916a-d058acf33078"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6951bbd6-404d-416b-8027-fbda44fe20b9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="419b9639-8c8f-43a8-a1e5-5b7d0b67f455">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fd4360b4-6fc6-4da2-9fec-ec1cef7a37c3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6b26bfdf-2dbb-4506-8187-bc7dc75e3258"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="945a2d55-1f55-491b-bc3b-806cd7dd4fd3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="16afac16-a1fa-4ace-996c-39c45edd443c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="17ee2f53-2007-4656-a5f5-fa34a9744b55"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="63cd3d4e-1d4e-47cd-a7ad-96c0b4fa8827">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0e469905-8d07-4ac0-abd4-39ebc12d76f0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="480d5ccd-b09c-491c-8111-e653b9e7b7ef"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a21a1bcf-6b5f-4f48-817d-277376797a4b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="26c86fc9-f5c3-4f97-81c5-92e81bf6d22b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ece118d5-0041-4962-8276-ca9894c9952f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dd25cfc6-59f0-4dcc-bfc3-ae6a90201880">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b3debd20-36fa-4753-993f-386e334a612a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e4f829df-acba-4bca-bb89-706ce17cfe7c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8993849d-5550-4600-ad90-0a6082ae7ef4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3fbf1052-498d-42a5-bb2e-d145d0ecb846"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="64d5c43d-6039-4455-b98c-2b1c7a0f3c5c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c6ac183a-bae9-48e4-83c5-af9a36e79200">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="21932f83-a69a-43a7-8670-e2f062b61ac9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="39a6b3a1-4b97-4107-8a23-13c668e1fc17"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0411217e-12e8-43e9-bf65-4b833e31006a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fc48b18b-537c-466d-b595-e84f4d47187d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4b2b8d8e-791b-400e-92c9-6f77c6217c29"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f6e2c9c1-0e37-452b-96c0-6389e5b97170">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d3ed2ea5-e424-44bf-ad79-01b57d9c3925"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4b4e8f28-004f-4c5d-9ac6-58ba5ac7c37e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f9d1a41a-633e-422e-9f90-2f7362a16ce5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e5185c58-32b7-44e8-80e7-1a5bc5ef8fc1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="51aa04ac-4433-4bcd-899e-8af74c174c88"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0df918d2-d328-4650-8498-b704b14ae8ba">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1ed49c12-edcb-4f10-b535-1eca0af292e4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bb7cefc5-cc0c-4aa4-a290-9be721ac0d49"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="085effff-6bed-445b-92a9-dbc0b7f58a7c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="544c0d2d-0ef8-4508-ab2c-7a27495d8382"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="75a0e75a-8771-47e4-9d7f-2903149d68e1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bfe666e1-1267-4471-aa7c-9a4201f6462f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9a59a733-8659-4770-b11d-ff08eaf89eda"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e9bea64b-319c-4e4e-8834-94ac93a7fcbc"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="93f87a7e-83c0-4dcc-989e-544afe2c4b9c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9b6e6812-bac0-405d-972f-96c941bfb898"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7c3004bf-7fab-471e-b87e-f11984d54b6a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="13589a89-ac5b-457e-b2b8-fb1590f34a1d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ac7c6fd5-9ddf-431f-b66e-bf9df1f7c510"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1cbada2d-357f-4769-9461-9fbd34cd9c42"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e3229aaa-367f-438a-9558-c2d25e0ebd94">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a7a1de36-473e-4f3c-b55c-6f859740a961"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3f580027-8897-432d-879f-ff554174e296"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="83d01198-067f-4144-8d07-02f8fb3ca5ff">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="998d01e5-c9f1-4679-8576-e2958e143faf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="52f8d2af-c495-4956-99ca-aa52e7f9a6d4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="995411ea-d5c9-43ac-871a-cd47f30080f2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2e75d9de-41cf-4721-95e9-fc76dad3edf3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="528bc3f3-4fd1-4151-8b43-347143dc1d47"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="084caeae-f6fa-457d-8e16-c3a701cac369">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f4f88177-2474-4395-b537-b675cb37f4a1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fbf44021-5cf2-4006-90bb-6f36139d0824"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4b819447-406a-49ca-8b46-fa1451356cd3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c2f25f10-0fa1-4e90-a42d-2759343204d8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="06e5d81b-72cc-4b99-bf74-1533fd50ec1c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fd96ac04-d8a1-474d-9563-c18c701a55ae">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cb0d0bbb-db9f-4c66-8986-120ff05e5406"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f17ac0e2-f87d-4275-88c0-f81e910bf172"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="121c616d-6424-49d2-96ae-469fcdb77b6e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d3bcd1ea-2e25-4940-8c8c-30fa01f68b37"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="35fb087c-bfde-4fbc-9be3-0d355f727919"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6f7020d5-727f-4419-97be-1b46236785dd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="87de0ae4-84cd-47ff-b6d9-8750d5b96e20"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1388aeed-9f56-44da-a6bc-a5426f466ed2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e2395854-db27-4d72-84a7-cdb70f02f8ff">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9419beef-dff7-423c-befc-35e8301dca60"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="683df398-f2a1-49a7-810b-5ec17ff27885"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="720861bd-568d-457d-a2cd-f2b232ebc6f5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>32767.0</designMax>
      <designMin>-32768.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="156a0a25-29a7-4ec0-b288-1063b40d44ea"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4570ef61-b9a2-486e-96f1-d38f2c257746"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7d019a43-580e-4a49-8174-0b8f379b7657">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b6337e4d-9524-4e77-9eed-e2f76da10561"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6da96d02-f2c1-4274-9777-0c74d5601b29"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a52cec68-5e32-4871-990c-96c761af68e7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5a010c72-56bc-4594-8a99-d16c20ca0ee8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="aaf988dd-3389-4708-82f6-f4ac071fef9e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="62fe625e-5669-498c-8649-01c462737373">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c88240d6-7df2-4f9b-b736-4e3ef6d293fc"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e106ddec-c95d-482b-86eb-0d0a8e905aab"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f43c1882-f55c-4986-94b8-b901b636affe">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2fea4565-6e5c-4f9c-bf6f-383d69dad2d4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a03f585d-060b-4d99-8e3e-9f4619a6b1ea"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e21c8315-3958-49d4-80f1-6cbbc8093c97">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4996cf9b-36ab-4229-a837-4df699fa0923"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c258d998-bf50-4850-9d1e-249ee411c970"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cbd7b829-d4ef-40c5-a8e0-1f1c4277b104">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="441c065e-336b-4d70-8e43-b583d0a35380"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e423508f-573a-48db-84f1-e164ad12037d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1d06da4f-f183-4b01-9392-54ff3bfd0270">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f7cfe821-8230-4237-bfeb-2d62b0a13a43"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a99b9f16-162f-48e7-9b6e-8c3f2a486e41"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="afe57352-abc6-4599-8b7f-dbe8d2cc51f0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d2a45eca-6f48-45e4-a4d6-29469492cd7d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2cf96527-3107-4517-82aa-c4d7497b7d9d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="69bd5e62-9c04-4547-bd99-0fc807b4bd50">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e9061324-0503-40c2-b33a-f65ffc7424c0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e2bb57f7-412a-4d45-98a4-5e8c17566e72"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a764645e-8f36-4bd2-b377-dc61ad57aa72">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="98845792-4d3e-469a-93c6-29c60f2550af"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ca17567c-2007-44d4-86bd-42ca7bd21774"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5451d87b-bf51-43b7-85e3-0cf086e0015e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="70993302-7ddb-492c-badb-6d9f37b07340"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="61597fbe-edcb-446b-90cd-13d1045e95b6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4a7ef81f-ac6b-4d4b-8f09-db8cab70e596">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3a1e9ff7-75aa-4506-b6e2-6c22040d39d3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4f0ae8b9-d3cf-4f08-8476-082f18ca1336"/>
    </inports>
    <isAperiodicRootFcnCallSystem>true</isAperiodicRootFcnCallSystem>
    <isBdInSimModeForSimCodegenVariants>false</isBdInSimModeForSimCodegenVariants>
    <isInlineParamsOn>true</isInlineParamsOn>
    <isNumAllowedInstancesOne>true</isNumAllowedInstancesOne>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigOutportVirtualBus>true</isOrigOutportVirtualBus>
    <isPreCompSingleRate>false</isPreCompSingleRate>
    <isRefModelConstant>false</isRefModelConstant>
    <isRootFcnCallPortGroupsEmpty>true</isRootFcnCallPortGroupsEmpty>
    <loggingSaveFormat>2</loggingSaveFormat>
    <maxFreqHz>-1.0</maxFreqHz>
    <numDStateRecs>8</numDStateRecs>
    <numDataInputPorts>7</numDataInputPorts>
    <numLoggableDStateRecs>8</numLoggableDStateRecs>
    <numLoggableJacobianDStates>0</numLoggableJacobianDStates>
    <numModelWideEventTs>0</numModelWideEventTs>
    <numPortlessSimulinkFunctionPortGroups>0</numPortlessSimulinkFunctionPortGroups>
    <numRuntimeExportedRates>1</numRuntimeExportedRates>
    <numTs>1</numTs>
    <origInportBusType>MtrMdlBus</origInportBusType>
    <origInportBusType>SWC_DataHndlLyrBus</origInportBusType>
    <origInportBusType>SWC_OperLogLyrBus</origInportBusType>
    <origInportBusType>SWC_SigMonLyrBus</origInportBusType>
    <origInportBusType>RefForceBus</origInportBusType>
    <origInportBusType>ThresForceBus</origInportBusType>
    <origInportBusType>LearnAdapBus</origInportBusType>
    <origOutportBusOutputAsStruct>false</origOutportBusOutputAsStruct>
    <origOutportBusType>APDetBus</origOutportBusType>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="6e50f96c-65b6-4cff-9c06-1f66641d4089">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="670c0b2b-3f1a-45c0-bf2e-e52c6352e604"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="63264a1e-0992-4515-88de-ed078af58309"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="5e5179d5-d131-4965-966f-0bb358192397">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="ceccd6a0-1635-446a-85fe-484c7fcf93d1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="79bddba9-829a-4d8c-abb9-ceb641a21952"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="502091a1-7172-4d72-9b0f-c8acb4c8b7ba">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="c411e50b-c8e9-4c36-bcc6-420c42676170"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dc33e51e-f0a3-4331-bfd1-6b14a8fe62c0"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="40b3c6e6-3551-4fda-975f-6e633d95534e">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="41ec6e39-3a3c-4add-bee1-189b54524c5d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a1bf062b-4ad6-4255-9a2c-85c930ca4285"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="74cc5743-ff90-41af-82aa-2e6d6e4709c8">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="1b0443a6-24f8-4cdf-9d38-a2d8d787985e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a6e04cb9-0155-45f3-b35f-033a0abe236d"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="486d9015-001d-412b-a798-e305271f7416">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="6cc9e923-0503-483c-a385-4fedec3d5bff"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="09360b8e-283b-4b9a-86ca-25f713b065e6"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="6110db7c-baa5-483f-bb73-784e3b802b13">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="152947c5-58e5-46a9-810f-4c051136a696"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="73937f5c-4ce2-4a99-8cf0-27f3326d9e5f"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="4e958a45-32ba-4b49-9039-5a8a72bc2331">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="d15cc5b6-694e-4444-9790-e0884e8c70f7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7e8e20aa-b338-4373-ac51-ae747a50b09e"/>
    </outports>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="d7450304-d454-4c53-b20d-5b7904bf9ad2">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="50f0d7c3-917c-49d9-98f4-76f679df62a6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b9a71a8f-29bd-471e-8d27-f2a19b9b53e6"/>
    </outports>
    <preCompAllowConstTsOnPorts>false</preCompAllowConstTsOnPorts>
    <preCompAllowPortBasedInTriggeredSS>true</preCompAllowPortBasedInTriggeredSS>
    <removeResetFunc>true</removeResetFunc>
    <runtimeNonFcnCallRateInfos type="ModelRefInfoRepo.RateInfo">
      <compiled>true</compiled>
      <isEmpty>true</isEmpty>
      <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
      <period>.2</period>
      <priority>40</priority>
      <rateIdx>0</rateIdx>
    </runtimeNonFcnCallRateInfos>
    <sampleTimeInheritanceRule>1</sampleTimeInheritanceRule>
    <solverStatusFlags>331</solverStatusFlags>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="77bbe823-2a18-47dd-a215-5085ee3d5733">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="8e40bea5-949a-4607-b6d2-8c58f94c567d">
      <defaultValue>IRS_E_OK</defaultValue>
      <enumName>stdReturn_t</enumName>
      <labels>IRS_E_OK</labels>
      <labels>IRS_E_NOK</labels>
      <labels>IRS_E_PENDING</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="1623f19c-08bf-4cbf-a22c-0b502931915f">
      <defaultValue>Disabled_C</defaultValue>
      <enumName>CfgParam_TestMode_t</enumName>
      <labels>Disabled_C</labels>
      <labels>All_CW_C</labels>
      <labels>Sequential_C</labels>
      <labels>All_Stop_C</labels>
      <labels>All_CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="babb3ef0-be35-48b5-b782-acaa753f6fab">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MtrTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MtrTempClassA_C</labels>
      <labels>MtrTempClassB_C</labels>
      <labels>MtrTempClassC_C</labels>
      <labels>MtrTempClassD_C</labels>
      <labels>MtrTempClassE_C</labels>
      <labels>MtrTempClassF_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="b8503fa1-e1ed-4e9b-a853-61aa8fe300ab">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MosfetTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MosfetTempClassA_C</labels>
      <labels>MosfetTempClassB_C</labels>
      <labels>MosfetTempClassC_C</labels>
      <labels>MosfetTempClassD_C</labels>
      <labels>MosfetTempClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="ed12d166-49e0-421a-a855-c34932d64cc4">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>MtrCtrl_MtrDir_t</enumName>
      <labels>IDLE_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <labels>SECURE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="3d65727a-08db-4ff3-8285-d2b614aa3713">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>MtrCtrl_MtrPwrd_t</enumName>
      <labels>IDLE_C</labels>
      <labels>PWRD_C</labels>
      <labels>DEPWRD_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="23e8b11b-ecc9-457b-8452-a4383b0a3d47">
      <defaultValue>CmdNone_C</defaultValue>
      <enumName>PnlOp_MtrCmd_t</enumName>
      <labels>CmdNone_C</labels>
      <labels>CmdInc_C</labels>
      <labels>CmdDec_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="eed4c89d-9576-42c6-b74b-5bf11c49366d">
      <defaultValue>RoofIdle_C</defaultValue>
      <enumName>PnlOp_Mode_t</enumName>
      <labels>RoofIdle_C</labels>
      <labels>Moving_C</labels>
      <labels>Intr_C</labels>
      <labels>ReachTarPos_C</labels>
      <labels>ReachStallPos_C</labels>
      <labels>Automatic_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="772f6389-9fc0-44bb-a56f-1dac6b207d88">
      <defaultValue>RevIdle_C</defaultValue>
      <enumName>PnlOp_Rev_t</enumName>
      <labels>RevIdle_C</labels>
      <labels>RevInProcATS_C</labels>
      <labels>RevInProcStall_C</labels>
      <labels>RevComplATS_C</labels>
      <labels>RevComplStall_C</labels>
      <labels>RevInhibted_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="9be2998a-8af1-4824-b5d4-09e971ccd752">
      <defaultValue>ATSDeactivated_C</defaultValue>
      <enumName>PnlOp_ATS_t</enumName>
      <labels>ATSDeactivated_C</labels>
      <labels>ATSAvailable_C</labels>
      <labels>ATSActive_C</labels>
      <labels>ATSOverrideStage1_C</labels>
      <labels>ATSOverrideStage2_C</labels>
      <labels>ATSDisabled_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="98d39566-a2d2-4b10-95fc-2ece5c7020b9">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_MovType_t</enumName>
      <labels>None_C</labels>
      <labels>Manual_C</labels>
      <labels>Automatic_C</labels>
      <labels>Learning_C</labels>
      <labels>AtsReversal_C</labels>
      <labels>RelaxOfMech_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="4181ec5a-e68b-4a51-89b3-8e8eb0dcdf31">
      <defaultValue>Idle_C</defaultValue>
      <enumName>CtrlLog_RelearnMode_t</enumName>
      <labels>Idle_C</labels>
      <labels>ReqLearn_C</labels>
      <labels>LearningPos_C</labels>
      <labels>LearningRF_C</labels>
      <labels>Complete_C</labels>
      <labels>Interrupted_C</labels>
      <labels>Renorm_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="1b114800-e1f5-4bd3-93d5-5a66c0f7cd79">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>SysFltDiag_LastStopReason_t</enumName>
      <labels>NoFault_C</labels>
      <labels>Debugger_C</labels>
      <labels>DiagReq_C</labels>
      <labels>PanicStp_C</labels>
      <labels>ThermPrtMtr_C</labels>
      <labels>ThermPrtMosfet_C</labels>
      <labels>RelaxMechComplt_C</labels>
      <labels>AtsRevComplt_C</labels>
      <labels>MovTimeout_C</labels>
      <labels>ClassEOV_C</labels>
      <labels>ClassEUV_C</labels>
      <labels>BattVltNonPlsbl_C</labels>
      <labels>AmbTmpNonPlsbl_C</labels>
      <labels>MosfetTempNonPlsbl_C</labels>
      <labels>SysIC_CommFlt_C</labels>
      <labels>LINCommFlt_C</labels>
      <labels>SysICFailSafe_C</labels>
      <labels>ChargePumpFlt_C</labels>
      <labels>HSSuppFlt_C</labels>
      <labels>VsIntFlt_C</labels>
      <labels>VsSuppFlt_C</labels>
      <labels>VCC1Flt_C</labels>
      <labels>RPInvalidFlt_C</labels>
      <labels>HallFlt_C</labels>
      <labels>SysICThermShdFlt_C</labels>
      <labels>MtrCtrlFlt_C</labels>
      <labels>HallSuppFlt_C</labels>
      <labels>UnderVotlage_C</labels>
      <labels>OverVoltage_C</labels>
      <labels>StallDurAtsRev_RlxMech_C</labels>
      <labels>OutsideEnvCond_C</labels>
      <labels>TargetPosRchd_C</labels>
      <labels>PrevReason_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>16</values>
      <values>17</values>
      <values>18</values>
      <values>19</values>
      <values>20</values>
      <values>21</values>
      <values>22</values>
      <values>23</values>
      <values>24</values>
      <values>25</values>
      <values>26</values>
      <values>27</values>
      <values>28</values>
      <values>29</values>
      <values>30</values>
      <values>31</values>
      <values>35</values>
      <values>37</values>
      <values>48</values>
      <values>49</values>
      <values>50</values>
      <values>51</values>
      <values>52</values>
      <values>255</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="8152151a-2554-4008-b385-8ab1821ced3b">
      <defaultValue>None_C</defaultValue>
      <enumName>VehCom_Cmd_t</enumName>
      <labels>None_C</labels>
      <labels>Stop_C</labels>
      <labels>ManOpn_C</labels>
      <labels>ManCls_C</labels>
      <labels>AutoToPos_C</labels>
      <labels>AutoToPosStep_C</labels>
      <labels>StepOpn_C</labels>
      <labels>StepCls_C</labels>
      <labels>Learn_C</labels>
      <labels>Invalid_C</labels>
      <labels>Learn_WOPreCond_C</labels>
      <labels>AutoHallToPos_C</labels>
      <labels>ManOpnCont_C</labels>
      <labels>ManClsCont_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
      <values>10</values>
      <values>11</values>
      <values>12</values>
      <values>13</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="ef87a5e0-a90d-4670-89a8-f7aa8144cd02">
      <defaultValue>Init_C</defaultValue>
      <enumName>VoltMon_VoltClass_t</enumName>
      <labels>Init_C</labels>
      <labels>VoltClassA_C</labels>
      <labels>VoltClassB_C</labels>
      <labels>VoltClassC_C</labels>
      <labels>VoltClassD_C</labels>
      <labels>VoltClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="f09ee28a-3824-4a7a-85a8-9bff1a542298">
      <defaultValue>AreaSlide_C</defaultValue>
      <enumName>PosMon_Area_t</enumName>
      <labels>AreaSlide_C</labels>
      <labels>AreaVent_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="9a88daff-1760-4fa8-9c9b-fb91936784a5">
      <defaultValue>PnlZero</defaultValue>
      <enumName>PosMon_PnlPosArea_t</enumName>
      <labels>PnlZero</labels>
      <labels>PnlFlushClose</labels>
      <labels>PnlVentArea</labels>
      <labels>PnlVentOpen</labels>
      <labels>PnlComfOpnArea</labels>
      <labels>PnlComfStop</labels>
      <labels>PnlSlideOpnArea</labels>
      <labels>PnlFullOpen</labels>
      <labels>PnlOpenHS</labels>
      <labels>PnlOutOfRng</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="d7de8628-632e-4826-b288-8595603b3dca">
      <defaultValue>Idle_C</defaultValue>
      <enumName>LearnAdap_Mode_t</enumName>
      <labels>Idle_C</labels>
      <labels>LearningPos_C</labels>
      <labels>AdaptionOpen_C</labels>
      <labels>AdaptionClose_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="88061ac8-de63-4168-8af3-fb6c2a0cbd78">
      <defaultValue>NoReq_C</defaultValue>
      <enumName>LearnAdap_Req_t</enumName>
      <labels>NoReq_C</labels>
      <labels>ClrReq_C</labels>
      <labels>SaveReq_C</labels>
      <labels>InvalidReq_C</labels>
      <labels>InterruptReq_C</labels>
      <labels>ClrReqHardStopOpen_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="a4d31fe9-3275-46a7-9d5e-375267213a5d">
      <defaultValue>None_C</defaultValue>
      <enumName>APDet_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="35cc98fe-ce35-4a3f-9aa8-786bbef55404">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoSyncParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>ManMov_C</labels>
      <labels>ManCls_C</labels>
      <labels>StepMov_C</labels>
      <labels>StepMovCls_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="3b59d96b-bf65-4837-8afa-82130066a2cb">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoAppParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>StepMovCloseReq_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <timingAndTaskingRegistry>&lt;?xml version=&quot;1.0&quot;?&gt;
&lt;slexec_sto version=&quot;1.1&quot; packageUris=&quot;http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112&quot;&gt;
  &lt;sto.Registry type=&quot;sto.Registry&quot; uuid=&quot;463f3813-9134-4e12-b1c7-27eca4386a6d&quot;&gt;
    &lt;executionSpec&gt;Undetermined&lt;/executionSpec&gt;
    &lt;clockRegistry type=&quot;sto.ClockRegistry&quot; uuid=&quot;eb626fb2-bd75-4860-b245-63b8f28293cb&quot;&gt;
      &lt;clocks type=&quot;sto.Timer&quot; uuid=&quot;462a5e99-c781-4581-9b31-57bfcecff643&quot;&gt;
        &lt;clockTickConstraint&gt;PeriodicWithFixedResolution&lt;/clockTickConstraint&gt;
        &lt;computedFundamentalDiscretePeriod&gt;.2&lt;/computedFundamentalDiscretePeriod&gt;
        &lt;resolution&gt;.2&lt;/resolution&gt;
        &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;0a54a517-49a6-4e81-a2a5-ba528ab2e424&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;e1730b6f-4c7b-42b8-9f5e-a0c8a9dda6fb&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;baseRate type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;0196f7dc-7cb6-4c1a-a0dc-775a8b2e1b28&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;0a01666e-5372-4f93-838c-2ddce9b37338&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/baseRate&gt;
      &lt;/clocks&gt;
      &lt;clocks type=&quot;sto.Event&quot; uuid=&quot;5422896d-b59c-44e4-b61e-d6627079394e&quot;&gt;
        &lt;eventType&gt;PARAMETER_CHANGE_EVENT&lt;/eventType&gt;
        &lt;cNum&gt;1&lt;/cNum&gt;
        &lt;clockType&gt;Event&lt;/clockType&gt;
        &lt;identifier&gt;ParameterChangeEvent&lt;/identifier&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;d9b4cf35-482c-49f8-9288-ace53136d7bc&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;10af232a-0f2b-4b15-9878-b4d90dbd431e&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
      &lt;/clocks&gt;
      &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
    &lt;/clockRegistry&gt;
    &lt;taskRegistry type=&quot;sto.TaskRegistry&quot; uuid=&quot;15261103-faa9-4169-b956-9ed9b2d8fd65&quot;&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;5586ca1d-a708-4c68-9e37-c9df74e1b5db&quot;&gt;
        &lt;isExplicit&gt;true&lt;/isExplicit&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;e1773132-8874-4765-b476-574e68f342f6&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;406eb9f8-35a4-4a4b-b8d8-39b45c77d542&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;schedulingClockId&gt;ParameterChangeEvent&lt;/schedulingClockId&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;ModelWideParameterChangeEvent&lt;/identifier&gt;
        &lt;priority&gt;-1&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;3d7da709-e1d0-4ed2-b378-420e88492d84&quot;&gt;
        &lt;isExecutable&gt;true&lt;/isExecutable&gt;
        &lt;orderIndex&gt;1&lt;/orderIndex&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;079f92bc-09db-458d-8e15-6fa400f4d80c&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;9a04e200-fa67-46bb-bdf1-e59c08b3c386&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;_task0&lt;/identifier&gt;
        &lt;priority&gt;40&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;aafd9cc1-87bf-4f6e-a61b-c77ed750fda6&quot;&gt;
        &lt;taskIdentifier&gt;_task0&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;a24414b2-44f4-4b69-a0b6-2734532f64ca&quot;&gt;
        &lt;clockIdentifier&gt;ParameterChangeEvent&lt;/clockIdentifier&gt;
        &lt;taskIdentifier&gt;ModelWideParameterChangeEvent&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskPriorityDirection&gt;HighNumberLast&lt;/taskPriorityDirection&gt;
      &lt;taskingMode&gt;ClassicMultiTasking&lt;/taskingMode&gt;
    &lt;/taskRegistry&gt;
  &lt;/sto.Registry&gt;
&lt;/slexec_sto&gt;</timingAndTaskingRegistry>
    <triggerTsType>triggered</triggerTsType>
    <triggerType>3</triggerType>
    <usePortBasedSampleTime>true</usePortBasedSampleTime>
    <zeroCrossingInfo type="ModelRefInfoRepo.ZeroCrossingInfo" uuid="beb270b8-b30c-4d32-9f91-15ff3acb0640">
      <direction>7</direction>
      <isDiscrete>true</isDiscrete>
      <name>Trig</name>
      <needsEventNotification>true</needsEventNotification>
      <tolerance>-1.0</tolerance>
      <type>1</type>
      <width>1</width>
    </zeroCrossingInfo>
    <zeroInternalMemoryAtStartupUnchecked>true</zeroInternalMemoryAtStartupUnchecked>
    <FMUBlockMap type="ModelRefInfoRepo.FMUBlockInfo" uuid="58897b10-3aa4-4d0c-b958-db61a685b565"/>
    <codeGenInfo type="ModelRefInfoRepo.CodeGenInformation" uuid="550efdc8-4d7c-48d8-a0de-65a98352eede">
      <DWorkTypeName>MdlrefDW_APDet_Mdl_T</DWorkTypeName>
    </codeGenInfo>
    <compiledVariantInfos type="ModelRefInfoRepo.CompiledVariantInfoMap" uuid="0eea3ec8-8cbc-4f86-b258-a6ff9dfe700f"/>
    <configSettingsForConsistencyChecks type="ModelRefInfoRepo.ConfigSettingsForConsistencyChecks" uuid="5175357e-c2c3-4517-ada2-d5b634d042ad">
      <consistentOutportInitialization>true</consistentOutportInitialization>
      <fixedStepSize>.2</fixedStepSize>
      <frameDiagnosticSetting>2</frameDiagnosticSetting>
      <hasHybridSampleTime>true</hasHybridSampleTime>
      <optimizedInitCode>true</optimizedInitCode>
      <signalLoggingSaveFormat>2</signalLoggingSaveFormat>
      <simSIMDOptimization>1</simSIMDOptimization>
      <solverName>FixedStepDiscrete</solverName>
      <solverType>SOLVER_TYPE_FIXEDSTEP</solverType>
      <hardwareSettings type="ModelRefInfoRepo.HardwareSettings" uuid="4d50ca03-e701-4d87-bf3c-661c6961daca">
        <prodBitPerChar>8</prodBitPerChar>
        <prodBitPerDouble>64</prodBitPerDouble>
        <prodBitPerFloat>32</prodBitPerFloat>
        <prodBitPerInt>32</prodBitPerInt>
        <prodBitPerLong>32</prodBitPerLong>
        <prodBitPerLongLong>64</prodBitPerLongLong>
        <prodBitPerPointer>32</prodBitPerPointer>
        <prodBitPerPtrDiffT>32</prodBitPerPtrDiffT>
        <prodBitPerShort>16</prodBitPerShort>
        <prodBitPerSizeT>32</prodBitPerSizeT>
        <prodEndianess>1</prodEndianess>
        <prodLargestAtomicFloat>1</prodLargestAtomicFloat>
        <prodLargestAtomicInteger>3</prodLargestAtomicInteger>
        <prodShiftRight>true</prodShiftRight>
        <prodWordSize>32</prodWordSize>
      </hardwareSettings>
    </configSettingsForConsistencyChecks>
    <controllableInputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="c3ca1675-135d-4334-86a7-7800eca770c1"/>
    <controllableOutputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="fafd683b-0bd7-4b45-bd68-9a2209be19ca"/>
    <dataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="10f4cc87-31ac-4556-bb47-64bc77888ace">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>113</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataOutputPorts>0</compDataOutputPorts>
      <compDataOutputPorts>1</compDataOutputPorts>
      <compDataOutputPorts>2</compDataOutputPorts>
      <compDataOutputPorts>3</compDataOutputPorts>
      <compDataOutputPorts>4</compDataOutputPorts>
      <compDataOutputPorts>5</compDataOutputPorts>
      <compDataOutputPorts>6</compDataOutputPorts>
      <compDataOutputPorts>7</compDataOutputPorts>
      <compDataOutputPorts>8</compDataOutputPorts>
      <dataInputPorts>0</dataInputPorts>
      <dataInputPorts>1</dataInputPorts>
      <dataInputPorts>2</dataInputPorts>
      <dataInputPorts>3</dataInputPorts>
      <dataInputPorts>4</dataInputPorts>
      <dataInputPorts>5</dataInputPorts>
      <dataInputPorts>6</dataInputPorts>
      <dataOutputPorts>0</dataOutputPorts>
    </dataPortGroup>
    <expFcnUnconnectedDataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="40c71da3-2798-4d43-9385-85c2eb3c7685">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
    </expFcnUnconnectedDataPortGroup>
    <interfaceParameterInfo type="ModelRefInfoRepo.InterfaceParameterInfo" uuid="0cdcd50f-17cd-4ed4-8316-ef9a221df290"/>
    <messageInfo type="ModelRefInfoRepo.MessageInformation" uuid="81320460-a79d-4fa1-8cd2-0f59a20e5c41"/>
    <methodInfo type="ModelRefInfoRepo.MethodExistenceInfo" uuid="a1a5ae4e-d627-4835-a2c0-3a0997f7a62d">
      <hasSystemInitializeMethod>true</hasSystemInitializeMethod>
      <hasSystemResetMethod>true</hasSystemResetMethod>
      <hasTerminateMethod>true</hasTerminateMethod>
      <hasUpdateMethod>true</hasUpdateMethod>
    </methodInfo>
    <periodicEventPortUnsupportedBlockInfo type="ModelRefInfoRepo.PeriodicEventPortUnsupportedBlockInfo" uuid="9c2400f1-4c12-460d-86ed-87fa28ac2a64"/>
    <portGroupsRequireSameRate type="ModelRefInfoRepo.PortGroupsRequireSameRate" uuid="e5ad9a7b-4ee3-4483-93fd-274af20b9d0b">
      <DSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="5c39b2fb-4629-4faf-a8f3-1a030ee9618d"/>
      <GlobalDSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="695fcab6-e4df-49d7-8a8d-cf2a1f3822da"/>
      <mergedPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="933c50be-6eca-49f6-ab84-ea70d8f9dfbc"/>
    </portGroupsRequireSameRate>
    <rateBasedMdlGlobalDSMRateSpec type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="1d5f02c1-cd42-4937-8912-dd77222c2ecd">
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="b6fcd6e3-3130-4bc8-b038-2bb507685e30">
        <dSMName>AntiPinch_FDifference</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="1a375270-1c54-4e10-b72d-93667b5a3c6b">
          <blockName>APDet_Mdl/Data Store Write3</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="1e2d2537-d466-4f97-a088-b7a4e63ee3c1">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="efcbde3e-dd7b-4933-88a2-3c7b55bff9dd">
        <dSMName>AntiPinch_FTracking</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="af121dc0-269e-4c17-b27c-c514deb306f3">
          <blockName>APDet_Mdl/Data Store Write4</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="8ac00789-9b54-40cf-831f-967b1befbce5">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="fa33e5fe-0529-40ab-acc2-407a10333f98">
        <dSMName>AntiPinch_isAtsDetected</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="ae53e051-2f6f-41bc-a52d-8a7a47f28239">
          <blockName>APDet_Mdl/Data Store Write2</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="a291f65c-7356-4386-acd6-1d4131d3d72f">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="bf101def-0487-4242-976b-d24c068410eb">
        <dSMName>CfgParam_CAL</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="e90c0baf-f3fe-4f52-9941-867928c17640">
          <blockName>APDet_Mdl/Data Store Read17</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="ec983a37-ee02-467b-a6a7-d630044e430e">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="42f88fb2-8369-414e-b1ea-6e987ae3753c">
        <dSMName>MtrMdl_stCalcForTorq</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="9ff69e71-b120-49db-90ba-3d49ebacfbc4">
          <blockName>APDet_Mdl/Data Store Read3</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="f201932f-8da3-4b00-8478-bee6e7047b75">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="4ae7374a-207f-4e38-9189-e5a84be73670">
        <dSMName>PIDDID_isATSEnabled</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="62ede404-ec6b-4977-9c81-e06ce7ca3e0a">
          <blockName>APDet_Mdl/Data Store Read1</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="cd7bd57d-b523-46f7-a517-68981a785ef9">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="e0cc2a01-f742-456a-a8dc-d87bee6b4d67">
        <dSMName>PIDDID_isFactoryMode</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="d10a71d8-3a8d-43ef-8929-9f74fbbb85cd">
          <blockName>APDet_Mdl/Data Store Read2</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="e6127538-675e-4712-b7b3-134ff6b329c7">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
    </rateBasedMdlGlobalDSMRateSpec>
    <rateSpecOfGlobalDSMAccessedByDescExpFcnMdlMap type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="4361fa39-d238-41d2-ac32-adce216a7f3c"/>
    <rootBlockDiagramInterface type="ci.Model" uuid="d2ebb02b-7c22-42d5-acac-0c9cdfe574e9">
      <p_RootComponentInterface type="ci.ComponentInterface" uuid="2ed296bd-887d-4cd5-a146-fb4c9997f78c">
        <p_InputPorts type="ci.SignalInterface" uuid="da25eb55-85db-4f66-83ab-a266a9548b64">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0d055dfe-ad26-494a-b5a4-c30bfbf1b880">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="46cbd6fd-3f2a-4a16-b31f-9dccd7b4bb98">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="68f178d1-ca62-4379-9c34-0c40d48bef1a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a0f78688-f8ef-4af1-b763-037f5268750e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0340b8bc-ee92-4719-b673-31f1577b4c42">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="75847d4a-5398-4a17-8050-0565f0044ea5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1b45ab55-9a31-42dc-a61b-c07c247a88f4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a3fb9ade-6ee9-4d04-b39a-926f40fc734f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="580a4479-3262-42d3-95fc-18a2b13f6c7e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dad957b8-9636-4c24-a656-38ffedf16973">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8be1923b-a3da-446d-a345-ae2d43023f44">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ec0cdbf6-f518-418f-94c7-3a3c4ad89ab3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="94a480cb-0e31-424e-9c7b-cb55bcb66392">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2bbfb9a3-a2d9-4097-aa0a-adb5228dc521">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="38e21446-bd91-4ea1-a7e3-1afe104a4bd1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="87ea7ce2-92a4-4afe-8a66-16f9b3e3c0f6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ae1793d9-963f-4a68-b19d-b4cec2a9e28f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a9caf841-2ec2-4674-8b42-baa1ed79be5d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f8463103-8f21-4246-b2cc-365c5f8d3a47">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="214d934e-4a6f-4b83-993d-644e957fc4a4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7c162a95-b794-46d0-b6bf-f6423577d568">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7bccee93-d59b-43c2-b43c-09c5de148f65">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint32</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="057b8994-fe6f-41ef-ae66-d5536384479e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2c21a9a5-6f64-4702-80ff-8e421811f381">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="96fa36be-d12f-4281-995a-d9e86c02b8aa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="235f0fa0-ca6f-4614-84c8-070a3fad5fdb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1c313ea1-7654-4a8c-96a0-f3d87224dfe5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="74133980-a35e-41a3-96d2-418e710329c2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="18ed9d6e-928a-4f27-a848-88a79173517a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="58954a7d-81ec-44c5-9b37-445129ce25cf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="130be89f-7c1f-4d65-996a-c422dbaa6404">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="237c1196-5cf5-4d16-b0b6-044f979c7e1f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fd433f35-903b-4d7f-b50d-9fcfde34d553">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5d095e8a-3632-4aa2-afbf-dd3c91f205ff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ea875232-b0ea-4809-90de-e80ace986020">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="89a3bcc7-d354-4df1-bb70-aa51c3aa0e2c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4188d877-ea53-4d7e-98e5-f1f69666780e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>stdReturn_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7f7bf933-7faf-4386-b3a3-09b55ca4ed56">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a4ea23af-54f4-4c17-87a8-e16bf3db9118">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ec7f28eb-bd39-46d3-8a7d-8abc8746d43b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1b423775-189d-45b2-91fb-e807c62a3343">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2002c648-5a4f-4403-a3b5-a08bb198e736">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4360a178-2624-445f-b392-2b571947a917">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="43085137-ce0b-4dd5-a9d8-967df6ff97b5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e1345fd2-637d-445c-82f9-13eded6fa64e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4516f1e4-226c-4e3b-937c-2064afdf8231">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fec79d56-60ab-4077-876c-cbdef2f0e1c0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4897d108-9e66-453b-abf4-f162b750e033">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="96fb634f-d66c-476c-9318-4e1d8c643287">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CfgParam_TestMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bd47b92f-8dfa-449d-8c98-294417ea10cd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MtrTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="adf38f36-7b6e-4ffe-861e-663e98c899f0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8fe8e643-33a2-4dd3-9f42-b4cb74cb193e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MosfetTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1302dfef-9a98-49c8-bb60-2952515e5cfb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="105c2d87-38b3-48fe-805b-a72c88b594fa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>MtrCtrl_MtrDir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5969561a-9f32-415d-a36c-07f0b206f61a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="557dd701-c43e-4795-95de-563754dd1202">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fbb1b5b4-b8ad-42fc-a0df-82f881b8ed2f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c89d15a6-d8e1-4b5c-b265-856bafef0912">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En12</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="90485fa9-c444-4bc7-8a92-02424d40d6fd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En12</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a138fb90-0a6a-4b53-932d-a6329a016eca">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix32_En21</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="15f91e9a-7b7f-459a-818e-db026ea8c917">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>MtrCtrl_MtrPwrd_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="61089b09-07a1-47c3-8fc0-5c920773c748">
          <p_ComputedNumericDimensions>2.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b1f982d9-effc-464e-8ea7-0116dcbcbbc9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_MtrCmd_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="21e141d7-0df6-438a-ace3-4d58cbca5a82">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e35c0c8a-6c24-4760-8160-a5c428391048">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5f0a16f8-2796-4d79-b6a4-343086352b5f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Rev_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="284febd9-659f-4177-8d8a-46e4e1b18bad">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_ATS_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dea3d769-4c0a-44dc-b2e7-6636d5656702">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="09340735-421c-4e41-9514-89fda6e00306">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>%</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e5b9ac13-5117-4c99-9a6b-e29db9857be4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_MovType_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f2bd4eb1-3cad-4759-8b0a-d24ad3cc97bf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="*************-466b-a2e4-14da6dc59ce2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_RelearnMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="068612c1-2089-4b71-a9d6-4976b6aaa719">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SysFltDiag_LastStopReason_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="38d3170b-c88e-40de-bf1f-d0f5c2effe37">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5b87172b-81ef-4b8b-b7b6-e351aacb8c30">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="148b2964-db0f-47cb-9120-38bd1a93acaf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3a82400d-1111-467d-b0fd-296370138ee7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VehCom_Cmd_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="11c29144-5872-414c-8080-909206bfa858">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dd7103c6-5e84-4117-9d5c-d678e180bdae">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d2cf041b-32c1-4cd9-9494-115236fb6af6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="20ef07ec-e41d-4070-bbd9-cc91fe9c3725">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3de58714-56e1-4216-b007-a38556bcbaae">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ea7af0ba-1ac9-47cc-a4cc-fdc503a64263">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8c82aea1-a0fc-4ad0-b092-d6e42e3c643d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="73352825-6e39-40d9-9898-288bd371439c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e67630c8-1d13-4dad-9830-0906f4174d73">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="43581bed-75a8-4984-a334-f975c8294026">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e57ddd3d-a895-4a0b-adff-08355d4d1353">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8af41b11-74d9-402d-9f73-b87590718367">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ed71da7b-9319-4534-bae0-80fd6ad2ead0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c9f1a1b3-927e-4f2c-b397-97d93d294623">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e17abc52-fef6-4128-8b4b-3bf145dbe08f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4698a5c0-a3a5-47cc-b773-6f7dc8e23a3c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="457c982c-5b03-47de-adf8-0a53677176ed">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8678764c-9508-483c-a70c-57e8cddf4d61">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_Area_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="06059aef-0b71-492e-ae38-82df6ff4470e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_PnlPosArea_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a5e55cae-4433-4bea-88b2-2669656350f0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="023735d9-3f5b-467c-8651-99c3b1ad81c0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c9d07067-dd18-43ca-94b7-630d228400d2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e003d957-eb59-4304-ac83-d93ac3d6cabd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8be09fd3-61a2-4bf0-8253-7ee32e4ca37f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="19a25206-32c7-475f-b6c5-a1192aa099a3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8509e855-5f47-4341-b9fd-4fa4381eeb4a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d9d55338-25f3-4cdc-a9de-5d085ff029be">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="caf10b8f-dcb2-4690-9acc-039318ae8b3e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3adf01fc-ebf9-42e4-9ed6-3d102b68150a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="303afaa2-d189-44a0-bae5-0acb9c92492a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b0e02edc-0b6d-409f-ab41-504c9842c53a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="850555bf-559c-45d9-802c-a06eae5f22f6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bdd6e244-37f6-41ff-8a4c-44947cb633a7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="571791d7-712f-49f6-843f-ba4c9e452eba">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c8d1f495-266c-41e4-86e6-198bd6501cea">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bb95f705-77d0-4e02-a866-5995014aba2a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4d1cae40-852e-44c8-9350-625cd3fa4e60">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b755b086-8e53-4fd6-a95c-20d89a0bfb00">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5626c404-4368-4315-a4c9-6614bc217cd0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="73081957-d952-48e9-a5ea-0b7f2ef9741a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="96ca01cd-1dd6-48a0-b6bf-b6a17658ac46">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Req_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d968d594-895a-43f2-98a1-4960a65c1658">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Req_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="178c0663-1727-4849-bb21-5844c3306504">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="68b8c061-6382-4b1a-8761-fe7a59576780">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="df60a140-a1a3-45f0-9e53-65720f482433">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="889dcf78-99fc-4d68-b42f-46134afae69d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c84eb38b-d115-4fc2-805a-3c48df84a734">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="07414421-cf38-4937-b8c0-eb6bf44b6c7d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_Name>APDet_Mdl</p_Name>
        <p_OutputPorts type="ci.SignalInterface" uuid="298b3d4c-cb49-4864-9b00-ec5c7bb40db7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="e321971f-24b9-4dfb-aca8-8c0f6fdca91a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="8c7d3156-2074-4ce3-a481-32e94409e52a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="65d02c8a-07cf-4ab7-adb5-98d40f00b240">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="0c23a0cb-d283-437b-aa0a-e38aaac753cb">
          <p_ComputedNumericDimensions>18.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="61cd8f62-eddf-43f2-9729-ad2d5b74191f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="120d130f-c971-401f-84c1-48cf47fe2572">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="c1835d07-445c-4c2b-96c6-95cc14da607a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>APDet_tenTargetDirection</p_ComputedType>
        </p_OutputPorts>
        <p_OutputPorts type="ci.SignalInterface" uuid="dbc0720d-f3e5-4abb-aedb-699fe58cf4da">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_OutputPorts>
        <p_Type>ROOT</p_Type>
      </p_RootComponentInterface>
    </rootBlockDiagramInterface>
    <simulinkFunctions type="ModelRefInfoRepo.SimulinkFunctions" uuid="321eafe2-7372-40ff-b1d3-4432d28ba596">
      <compSimulinkFunctionCatalog></compSimulinkFunctionCatalog>
    </simulinkFunctions>
    <sltpContext type="sltp.mm.core.Context" uuid="c1b4d841-ef2c-4100-805f-172d53e9188f">
      <globalData type="sltp.mm.core.GlobalData" uuid="97a2c68f-986e-4903-b1d7-bcd744d41bef">
        <dataName>AntiPinch_FDifference</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b8f52ebf-aef9-4771-8b48-d6072475bfe5">
        <dataName>AntiPinch_FTracking</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0b1033ac-c232-496d-be78-2ffaf6163abe">
        <dataName>AntiPinch_isAtsDetected</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fa1ae529-bfd3-40ce-86a3-5971407b97fb">
        <dataName>CfgParam_CAL</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="40284a7a-f6ff-4e87-940b-6dc3d3a412a3">
        <dataName>MtrMdl_stCalcForTorq</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0c03d5fd-3595-4831-8aa4-73c84c37f65f">
        <dataName>NewtonDividedByDeciNewton</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ba1dc520-0781-43ba-87f1-c73e2877eef4">
        <dataName>PIDDID_isATSEnabled</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="503a9e9d-eefd-400f-ad61-c154027b7934">
        <dataName>PIDDID_isFactoryMode</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7711c59a-4d16-41b8-91e1-917199264b29">
        <dataName>StateMach_isATSEnabled</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="385876cb-ba67-4d2e-a294-856dceb0f73c">
        <dataName>portAPDetBus</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f2dc26cb-120e-4954-8e22-c21c5192d2c6">
        <dataName>portAPDetBus_APDet_ATSRevDir</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47a81dfd-955c-4a2d-bca0-c6f5e61ae242">
        <dataName>portAPDetBus_APDet_ATSRevPosition</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f9b1cf37-8c2e-4a4f-8da4-8a32ce966717">
        <dataName>portAPDetBus_APDet_ATSRevThreshold</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="45664d39-cd08-466a-a319-0e91537e0fca">
        <dataName>portAPDetBus_APDet_FDifference</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="eec251b5-2973-42aa-ae39-67ec719df7a2">
        <dataName>portAPDetBus_APDet_FTracking</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7b01ac15-f88b-4a29-bbb5-5e7fc56fa573">
        <dataName>portAPDetBus_APDet_isAtsDetected</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9ccbc924-0d6f-4627-9479-50a8fdd05bb9">
        <dataName>portAPDetBus_APDet_isAtsEnabled</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0be39fae-88ba-4fa4-a481-abfd5cc1f4b5">
        <dataName>portAPDetBus_APDet_isInputsAtsValid</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f885dcec-2975-4bf7-bcc7-2c35b8f8323a">
        <dataName>portAPDetBus_UsgHist_ATSRevReason</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c4fc1c4c-6782-4d7c-add4-268543747d5f">
        <dataName>portBlockDetBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="613bc92d-f78e-4507-ae25-bb4c3846eeee">
        <dataName>portBlockDetBus_BlockDet_stFltType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="76f839db-394f-41a7-ada4-a63d65afc0b3">
        <dataName>portBlockDetBus_BlockDet_stStallFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3b76f90f-287a-435f-951a-9afca9130a41">
        <dataName>portBlockDetBus_BlockDet_stStallType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="786fae8a-054b-4dee-bd58-a0d44823f3ca">
        <dataName>portInport</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="13dc0497-916e-48da-a809-ac450877b618">
        <dataName>portInport1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5813acf2-2b6a-4e0f-910e-dc0ac419d290">
        <dataName>portLearnAdapBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e4fe1284-8457-4ece-8561-8f822fda48e4">
        <dataName>portLearnAdapBus_LearnAdap_isInterrupted</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6c2b21c5-7458-4c88-ba08-65dfba46b37b">
        <dataName>portLearnAdapBus_LearnAdap_isLearnComplete</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="760b8f7f-97aa-4862-b3ca-661dae818d45">
        <dataName>portLearnAdapBus_LearnAdap_isLearningAllowed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dae085f2-9423-4eec-af72-484c4232aec8">
        <dataName>portLearnAdapBus_LearnAdap_newHardStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cc0bb7c8-4229-4e64-b710-543ca0868faf">
        <dataName>portLearnAdapBus_LearnAdap_newSoftStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7d5259d3-a2a7-49a4-946f-22a4f1bcec81">
        <dataName>portLearnAdapBus_LearnAdap_stInt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6a0ad9b7-c410-4d8b-8f3c-2f85056bc4b8">
        <dataName>portLearnAdapBus_LearnAdap_stMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="982dea35-8d14-4d1c-9579-0706bbadf88a">
        <dataName>portLearnAdapBus_LearnAdap_stPosReq</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9b6723aa-2526-48a2-9bf3-0991614364bd">
        <dataName>portLearnAdapBus_LearnAdap_stRefFieldReq</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c65eadd4-238e-4599-8c8d-320a447114a4">
        <dataName>portMtrMdlBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4cbba2b6-96be-468b-a202-ca0263fd10c5">
        <dataName>portMtrMdlBus_MtrMdl_MFilteredTorque</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="eba1853d-9624-4fbb-98e6-f1f2022ac79f">
        <dataName>portMtrMdlBus_MtrMdl_MbrakeTorque</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="21412538-ada0-4bcc-ac29-f760c45e9ac0">
        <dataName>portMtrMdlBus_MtrMdl_TEstCaseTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="40884754-ce14-4ed2-90f7-28c58181b713">
        <dataName>portMtrMdlBus_MtrMdl_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="419e4281-54b2-4f10-b166-40bbcdd244a6">
        <dataName>portMtrMdlBus_MtrMdl_isCalcForTorqVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9866d5ff-8663-4d95-abda-bb218437446a">
        <dataName>portMtrMdlBus_MtrMdl_isEstCaseTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2ec19664-a785-4576-b28e-de594c7b02ff">
        <dataName>portMtrMdlBus_MtrMdl_isEstMtrTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cff1ed8c-cf48-430f-96eb-a25056fb93fb">
        <dataName>portMtrMdlBus_MtrMdl_isMotorInStartUp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fe3be39d-9747-45cb-8621-9b737ddef8f6">
        <dataName>portMtrMdlBus_MtrMdl_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="304411c8-3b24-49b9-aca2-8e58e3b3d048">
        <dataName>portMtrMdlBus_MtrMdl_stCalcForTorq_FcalcForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7f7c6bf5-d8d6-420e-9c81-ff9e70c58c4d">
        <dataName>portMtrMdlBus_MtrMdl_stCalcForTorq_McalcTorque</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="44c5db45-9c01-4692-99ba-d22b06463975">
        <dataName>portRefForceBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d55b61b5-0782-47c1-9240-6940a5846606">
        <dataName>portRefForceBus_RefForce_FreferenceForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3d37a2a2-9e94-45f1-885e-09674d160428">
        <dataName>portRefForceBus_RefForce_isCalcRefForceVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="48a15a17-ff01-454d-b82d-655c18f309e7">
        <dataName>portRefForceBus_RefForce_isPosOutsideRefField</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d6cd01a1-6618-47a5-a06e-48bb3d305c04">
        <dataName>portRoofMdlBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="41a982e0-aa91-4826-8702-f7da1ebe15b6">
        <dataName>portRoofMdlBus_StateMachBus_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0259719b-c45f-4694-8506-72aacd9717b2">
        <dataName>portSWC_AntiPinchBusIn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1336a5f4-2d0b-4c9c-8abe-ca670509b6fe">
        <dataName>portSWC_DataHndlLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="70c6cf18-bb51-442b-8fb3-68e3ca0a26c9">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SCUInfo</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9171bc5c-2e0d-4c2f-be6e-91bf30d6de00">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SWVersion</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="15ae74ad-3049-4c4e-9856-3e4a8547c542">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isCALFileVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5c9e7791-ef04-440e-b5e4-ded38c4dfcc0">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isPosTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1174c177-d9de-463f-b15c-d33eda779147">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="961c2e3f-a365-4afe-a224-4321c36d1548">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="17d9ce7a-76d9-4fc5-8f3c-9545217349c3">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stTestMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6e39f55b-8edc-4fe3-a100-bcad4f1f770b">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currByteOfRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b576cbdd-2607-4619-bf13-7a1bb313dfd1">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currRFArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1fc7cb00-8606-4e0a-94f6-798f7b8a8472">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isInsideRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9f8105dd-f56d-4682-95a5-1ea6ce056f36">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="402a554d-05e4-4dec-8bc3-d3368dde45db">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ced6b70f-89ec-48b9-b8dc-a77fc3170d12">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmBak</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6fa1948f-ff20-46f7-a1fd-3ee634d3954b">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmSync</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0d70e793-5d02-4d58-b811-098d21076875">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="201bf5cc-3098-4d82-b1a9-9dbf5edf5290">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d48f137e-0deb-4c4f-8ee0-897d06492e1f">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_stRFSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="46efbc4c-8fa0-447c-ba9a-62bc34e2a1c4">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_AmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="771bec6f-9880-4a32-a2b5-7be41c9d1b1d">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_CycRenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="16ecb8d0-06f0-41d4-ab6f-bfec1ade4f27">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LeanrFailCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e2436cc9-cf00-42eb-b382-b0bcdc6086ac">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LearnSuccessCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5990b15d-17ad-438a-9903-dad9d181453e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MOSFETTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bcb5f786-03c6-47a5-b45d-d2603b67d59f">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8b4c4e0e-66e3-4b20-ae6f-d80d7cefb800">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5ef1af52-9f8b-48c3-8aa7-8a772c0a9b3a">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c41079e9-e035-44f3-be94-aa885c87cc6a">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="90638f65-d01f-48e9-ac37-db354f4576a1">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MotorOperTime</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="29cc735e-bcb2-4d75-86b7-21b361b41852">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_RenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="86f43cb6-7a04-4d08-911a-1119c3ed91e0">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReprogrammingCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="61863b51-bd01-4392-8820-63c82eef26bb">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b6c4133c-a723-49b4-9905-7f0bd2a307eb">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ba3ce73c-a638-4129-b0f0-3bd6f17297ee">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2d37bb31-13a4-48f8-97a0-7c5f067e5cd0">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalThres</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="706c7e6d-c025-4a56-a8ff-8101863a9175">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_SlideCycCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4256f3af-2342-4af2-a40a-6954610d2926">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_StallCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c774b6af-5882-46cf-acb2-fd2fe04b3f85">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstCaseTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dfe215a6-d5c1-4c9c-a300-25ce68182958">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dbd35e5f-eec4-4a33-b71b-12c842e27da4">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fa28a7a5-00fd-48a1-8a00-bcfa2540d10e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ThermProtCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47918932-dbc7-4058-b63b-8f2d8eb3cc4e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="55c73183-b932-4d7c-bb8a-ee9f9e1762a0">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="27de8360-2045-4c9e-aacf-d3f7ce5cbb50">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4c73f5e2-26c4-47f8-b1a3-4a02af29d452">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_unlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ac3cdb74-8a01-41d2-80b0-9459cb53d259">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHistl_TEstCaseTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8660fabe-ef2c-4fd6-8082-0b40c9a4d87f">
        <dataName>portSWC_OperLogLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ffe047bc-d739-4890-90e6-8a16b0e6cc7e">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_isLearnSwtich</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="60008e10-61e4-4fac-b87a-9f6fdc24b6ea">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_isOverrideActv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fec1b238-cbb0-4cb9-81f7-45749715eb92">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7b88b1ae-ba97-4d4c-ae8b-da1c2c8265a6">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd_movCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="36e40e8b-c814-429e-9e59-e735a42d24a8">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="68f9def4-1b21-453d-a102-2cd706e2ce63">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bb36487f-3525-45da-9cd5-8a7696f6fe9f">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_stActvVldCmd_movCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1d99bdab-926f-43de-9e47-ee08214b3921">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_stActvVldCmd_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ffb38dcd-0ab2-4d94-9b06-8ecc2dd9a1e0">
        <dataName>portSWC_OperLogLyrBus_ComLogBus_ComLog_stActvVldCmd_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e3363d8a-9ff2-4315-8e23-767fc1bfc94f">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_hReactTgtPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d2668e4e-d9ce-43ac-bd92-fe6602978f58">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_isMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3944e225-1a31-423e-b2be-c0a30e7a60ae">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_isPnlMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01d7bdb8-50df-4eb0-bd9e-4ef954a0394b">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9d1374e6-af0e-46e1-9065-aad2262f77f5">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stLastStopReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4f9d4422-1e71-41f4-88b5-bd43c1b6c13c">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stLearnInt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b2ec3f49-4827-4fe0-9d5c-598bee1e58e7">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovReact_movReact</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="232071db-**************-955b0cf92a6c">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovReact_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="eaae13a7-00d7-42a4-9d1f-2834a810acae">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovReact_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e408c607-43e0-45d0-9789-2514083f3c8a">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="127b0d4b-33ec-4c7a-bce2-8e8b5d19b88e">
        <dataName>portSWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="16d51253-dc49-4b21-93e6-58c9d6651812">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_DTC_MTR_Dir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0c4399a2-271c-4fd5-a397-b16374295987">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1a96a47a-71a9-4e43-bb79-c3a1d07b8468">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDutyCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6df6932c-8934-4dae-b26b-b22639cbcb4d">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_FeedbackStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dd0c69fb-691b-4c07-811b-0fce5b46007e">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_MtrPwrdState</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d15fb1eb-c1ce-4b2a-82da-b1c153dcf2c2">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_rClsLpFactor</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c5213758-caa3-4445-8c6c-1efad3b195a1">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_rMtrSpdError</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ba029ba3-ae54-43d9-9086-510a61c59588">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_rMtrSpdTarget</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2703cca8-5c9f-4e74-81e3-d90fac084b08">
        <dataName>portSWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_rOpnLpFactor</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3f0fead4-686b-45a7-9e1f-3aa3396289f9">
        <dataName>portSWC_OperLogLyrBus_PnlOpBus_PnlOp_hTrgtPosPtcd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0c6d61ca-3ee9-4ba1-b463-19fe511ae8c4">
        <dataName>portSWC_OperLogLyrBus_PnlOpBus_PnlOp_isMtrMoving</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2a35feb8-279d-455a-afdf-4f07b36c0ede">
        <dataName>portSWC_OperLogLyrBus_PnlOpBus_PnlOp_pMtrSpdCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6b7bd6fa-9748-4293-bc39-23e27b69f4e2">
        <dataName>portSWC_OperLogLyrBus_PnlOpBus_PnlOp_stATS</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3111dcd8-cdca-482e-83c6-0099d3d308b9">
        <dataName>portSWC_OperLogLyrBus_PnlOpBus_PnlOp_stMtrCtrlCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ef452265-8832-4dc6-a763-cb4a5e72a322">
        <dataName>portSWC_OperLogLyrBus_PnlOpBus_PnlOp_stNormalMov</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="feb6ab4f-a196-4330-ab7d-c720798a89fe">
        <dataName>portSWC_OperLogLyrBus_PnlOpBus_PnlOp_stReversal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="43928053-07f5-4243-a258-c69eca3af32b">
        <dataName>portSWC_OperLogLyrBus_ThermProtBus_ThermProt_isSleepAllowed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c7bdf0e6-3585-4c4b-94f5-99cd9013230d">
        <dataName>portSWC_OperLogLyrBus_ThermProtBus_ThermProt_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b0d64ce9-b4d7-4b86-a5be-0ff3e7ec5ba9">
        <dataName>portSWC_OperLogLyrBus_ThermProtBus_ThermProt_stFltType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9d150daf-3695-4f13-93a6-bdfa25a8fd88">
        <dataName>portSWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6f59645f-9ed1-480a-b295-4de145e248c3">
        <dataName>portSWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f861f830-dbbe-4f6e-9a79-38315d732db1">
        <dataName>portSWC_OperLogLyrBus_ThermProtBus_ThermProt_stOverHeatFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="18e95f0b-29df-445e-8984-9dbf2b6acf86">
        <dataName>portSWC_SigMonLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3f793931-f1ca-44df-971a-9148371946dd">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="60f28a0e-24a0-421d-86a3-59604a2003e9">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="694d0490-1216-4cef-83a4-2cfc50ed26f7">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stAmbTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="323f5542-1e6a-4408-9ee2-543dd463e69a">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stMCUTempNoCritFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3e3e851f-782d-436c-ac93-491722acaed1">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_IoHwAb_SetHallPwrOutM1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8606bc78-e284-4bf5-a030-ca094fcbc3da">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_PosMon_isUpdateDisabled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4130315b-3179-46d4-b91b-de6bb7012514">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="08539085-872c-4391-b370-638715cfbaba">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="daa70db6-d66d-4cc7-b4c6-7970d166a932">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a652566f-e0b1-4868-bc7b-f3dd99a654dd">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_stMosTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5432750c-edb6-479c-8b8d-f171990b65f5">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_curPanelArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0d592115-24b9-4951-ad52-e44087ffdcb7">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3b55fdaa-10c3-43fc-beac-8beb7aa764dd">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bcd70d76-c9bf-4533-8742-e80408801b37">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hComfortMidStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dca55d3a-ce4b-4cb5-8a32-0d774c888839">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="87fab8ab-eecc-46d3-b62a-b10b8171d5b0">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ba9ddebf-0ad6-48cd-ad2d-cd03b878d884">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hNearClsSld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1996001e-b19a-4775-a300-9e4f8aa92ba9">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dbe9bfdb-7597-4696-a0c7-d087b11742cf">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c414d984-70f6-4198-b0da-7a068a882e9c">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47d6a777-a303-4a33-acf5-5da9fa49ee4b">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1fd01258-1284-4096-af4a-00fc6bff17d9">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isOutOfRange</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="737baf23-b9f9-4dbc-a950-553f75404dd2">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isRelearn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="869ce324-70d4-404b-93aa-3f75f9b6b1e0">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ec39f1ef-2942-4d2a-ae9f-b87f500fc8f9">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="*************-4d25-a648-5ff5a1eaf8da">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_panelCurPosArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="13523913-f14f-4e1c-a3f9-fdc080ee6606">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_relativePinionSize</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4d277143-19e4-46e8-99af-8947fe5216ac">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_stPosSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b47ee604-a8ae-4a8d-ba0f-acf3feae3d20">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_isDebSwch</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="de884731-5aa0-45af-9225-d87a618f1434">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stMultSwchFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="077371d2-d3f5-4f85-8483-26b4d9af3d84">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="90f21be0-006a-41c9-a200-80d66d3e6dd7">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchStickyFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d3382307-4bb8-4890-9ab3-d8dc3f1d59c2">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchVltgFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e5b19127-1ebb-4f70-af8e-3e062750a993">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_Pullup</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="931c2356-019b-4b19-9e0e-a4c61bcc0e8b">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="80e16608-e630-4a9a-acae-d7601b28c8dc">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b743d560-7890-475b-974f-de7c86a437df">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b01f3fd9-3ae8-47f2-b093-a6af10542932">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isOverVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="db2334dc-e376-4476-99cd-b7156235f1b3">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isPwrLost</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7af26e89-2b45-491a-8587-47e9bdff4020">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartUpRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="12006916-7848-4e4d-bb81-6952c39a91f8">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6dcf461a-e424-466f-bcc6-2cf13dbe39e0">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isUnderVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9ebd86fd-c325-4af7-beb1-4fe037cc918f">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isVoltDrop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ade98520-700a-479a-b1b2-f2e522f24cf3">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d1c7d68e-3eb9-43e2-89f1-d7191f603c6e">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="115e4306-f3cd-457f-add8-da76b2318ed0">
        <dataName>portSWC_StateMachLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bffa2cb3-94dd-416b-b8e8-517b1b147412">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="939558e5-a763-4c67-ba04-0b65016dec37">
        <dataName>portThresForceBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8ddc8304-c17a-4e0b-92ca-e4776de9c980">
        <dataName>portThresForceBus_ThresForce_FatsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4dec93d6-88f3-4060-89ca-39c68a57ad50">
        <dataName>portVehMdlBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f5e345bd-5d48-4719-b8a6-30c2835133d4">
        <dataName>portVehMdlBus_StateMachBus_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e4016650-6d01-4e75-a8ef-89391f6a8783">
        <dataName>portVehMdlBus_VehMdl_FatsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fbe82526-0192-4294-8948-c9079edb4dc2">
        <dataName>portVehMdlBus_VehMdl_adapAtsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="076146bc-e874-4e06-9aa6-de59b49a21a1">
        <dataName>portVehMdlBus_VehMdl_isAdapDisabled</dataName>
        <type>RootInport</type>
      </globalData>
      <priorityDirection>HighNumberLast</priorityDirection>
      <editorState type="sltp.mm.core.EditorState" uuid="ef1cfea2-484a-420d-ae76-3d2ab30b0402">
        <isSynchronized>true</isSynchronized>
        <panelState type="sltp.mm.core.EditorPanelState" uuid="0bb34eb9-3f07-403c-a299-ffd7020be893"/>
      </editorState>
      <rootTask type="sltp.mm.core.Task" uuid="68839f8f-f54f-4897-98df-1f32044a1852">
        <context type="sltp.mm.core.Context" uuid="c1b4d841-ef2c-4100-805f-172d53e9188f"/>
        <explicit>false</explicit>
        <name>Default</name>
        <priority>-2147483648</priority>
        <subgraph type="sltp.mm.core.Graph" uuid="07f90314-65ed-413d-b0e4-532bfa2c2e93">
          <tasks type="sltp.mm.core.Task" uuid="e2991d88-2762-48a0-9353-eda8f6ef7386">
            <context type="sltp.mm.core.Context" uuid="c1b4d841-ef2c-4100-805f-172d53e9188f"/>
            <explicit>false</explicit>
            <id>1</id>
            <isTimed>true</isTimed>
            <name>D1</name>
            <priority>40</priority>
            <rates type="sltp.mm.core.Rate" uuid="d0d0da07-1de0-4b15-9c23-a122da60e9b6">
              <annotation>D1</annotation>
              <color>-436207361</color>
              <identifier>ClassicPeriodicDiscrete0.20</identifier>
              <rateSpec type="sltp.mm.core.RateSpec">
                <period>.2</period>
              </rateSpec>
              <sti>0</sti>
            </rates>
          </tasks>
        </subgraph>
      </rootTask>
    </sltpContext>
    <stateWriterToOwnerMap type="ModelRefInfoRepo.StateWriterInfo" uuid="dcc1b5d0-9c28-481c-8f9c-002fe0bd329c"/>
    <stoClientDataRegistry type="sto.ClientDataRegistry" uuid="2a442bc3-b5d4-496a-87fe-2f095cf043f1">
      <dataSets type="sto.ClientClockNamedDataSet" uuid="af956768-74de-4d6e-95ea-04c79cb31e50">
        <tag>sltpEvents</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="d423501b-b3d2-48d0-9a02-585bd52e6237">
        <tag>sltpTaskGroups</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="ad0c43d4-6300-4a42-917c-12b8ba922549">
        <dSet type="ModelRefInfoRepo.SltpTaskData" uuid="75b8fbf6-074f-4d6b-9eac-04bd7c5095d3"/>
        <tSet type="ModelRefInfoRepo.SltpTaskData" uuid="75b8fbf6-074f-4d6b-9eac-04bd7c5095d3">
          <dataName>D1</dataName>
          <linkedSet type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="ad0c43d4-6300-4a42-917c-12b8ba922549"/>
          <id type="sto.TaskHierarchyElementId">
            <id>_task0</id>
          </id>
        </tSet>
        <tag>sltpTasks</tag>
      </dataSets>
    </stoClientDataRegistry>
    <varTsUIDMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="c9241a38-47e2-4ad6-973b-fda58dba31b7"/>
  </ModelRefInfoRepo.ModelRefInfoRoot>
</MF0>