<?xml version="1.0" encoding="UTF-8"?>
<MF0 version="1.1" packageUris="http://schema.mathworks.com/mf0/ci/19700101 http://schema.mathworks.com/mf0/sl_modelref_info/R2022b http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112 http://schema.mathworks.com/mf0/sltp_mm/R2022b_202203181029">
  <ModelRefInfoRepo.ModelRefInfoRoot type="ModelRefInfoRepo.ModelRefInfoRoot" uuid="6ec526be-0b11-4ea0-bca4-c20eccf6cf94">
    <JITEngines>sLJV0t5e7BwNN6xnq2tx2mC</JITEngines>
    <calibrationData type="ModelRefInfoRepo.CalibrationData" uuid="dbdd8f81-9b44-42a3-a614-501e529f337a">
      <InternalData>[{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;&quot;,&quot;Profile&quot;:&quot;&quot;}]</InternalData>
      <ModelName>MtrCtrl_Mdl</ModelName>
      <RootIOData>[{&quot;Name&quot;:&quot;SWC_HwAbsLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_SigMonLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_DiagLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;SWC_DataHndlLyrBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;PnlOpBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;CtrlLogBus&quot;,&quot;Profile&quot;:&quot;&quot;},{&quot;Name&quot;:&quot;MtrCtrlBus&quot;,&quot;Profile&quot;:&quot;&quot;}]</RootIOData>
    </calibrationData>
    <childModelRefInfo type="ModelRefInfoRepo.ChildModelRefInfo" uuid="9d1b430a-78e0-4d25-b40c-26f8d8e56414">
      <isSingleInstance>true</isSingleInstance>
      <modelName>MtrCtrl_Mdl</modelName>
      <modelPath>MtrCtrl_Mdl</modelPath>
    </childModelRefInfo>
    <compDerivCacheNeedsReset>false</compDerivCacheNeedsReset>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e7e7384a-5a8b-4471-a991-56391f740278">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="91ca1731-f6df-425f-a9cf-e2d4f8c0be64">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="15e1d106-96d5-4464-b10f-168c2ed06095">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fcfc77bc-b138-4f7e-b6f4-7752ab24efbc">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b9b89449-81bc-44a9-9700-a46c6dc94ef1">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="92b3f17c-c74a-428d-ae2d-eb97df8daa54">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="62f10a74-888b-4812-afe8-78a072fe76d2">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1da467f4-40c1-42d9-bb26-163ef006696a">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c29152e3-a7a0-44c0-bc3f-37929186273d">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6aa9ca81-3621-4a6a-aa7a-1601f5bc5dd1">
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1cd1f984-1b40-43c8-b340-ce688a5b204d">
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a7646686-3eeb-4846-96cd-a91871a5c97b">
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2320635e-342d-4c38-bc5c-2d7fdfc1b20c">
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="88662db8-2972-4c6c-af0b-f6eb7d8e9eb3">
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b1f6cf4d-e07e-4ecf-b09d-7bb6bc6dc952">
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="61add80e-c905-4b73-9148-a6c577bbfc24">
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="efc9cf56-b39c-47af-a83a-68ab85a1a574">
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3c42fad1-5288-4d75-bd5e-0a0cb3556038">
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2b2c6cc5-c8e7-4cb5-8287-ba8fc6afb084">
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3c21bbf4-55da-4d2c-9b05-de14133cebcf">
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c72394a0-cdfe-4a75-90bf-fd49afdb3e57">
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="56698049-f9f6-48a8-841e-5820d64f8a0b">
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3367ca07-211d-4565-a445-79da633d5fd3">
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="efcc079c-f9c4-4f0b-97af-1de93b32aeff">
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ae229dfb-e794-49fe-9784-a6efba64e4df">
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="207b8c63-7e38-4f08-8581-d34bd120b847">
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="691e9f65-9b2c-4895-b123-882089d768c9">
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="826f7af9-a936-45ea-9b8c-b6f94d0558e6">
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b20b63e2-c2dd-4f09-a0c1-a22bb8c73025">
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6e524ce7-36ee-4859-83ff-f9a568339ae5">
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="941abfed-3de1-4513-ae2c-abcbfffd5a5e">
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="be28f73b-8661-4144-b0ee-9153c48ba7ca">
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2821f6c9-201e-4122-8c9d-c15a0773592a">
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="45280a39-8eb5-4104-a1ed-7114b67e598f">
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="faa42057-3850-4c85-87d0-324c599a8b76">
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fd3f02f0-9669-4493-aafa-55ec32efeacd">
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="58b35d0a-bad2-47d2-9e88-b1a64dd320f3">
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d7651249-da61-47b7-853d-906c2d12dd9d">
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="13b33ffa-3f67-49f5-a322-089c513d5d86">
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="69bd44a5-4c9c-4978-8c5c-87ee40cddfdc">
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c5b46964-7a1c-4c32-9777-2ae7202445d3">
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d24696a4-65db-45eb-9f49-b1742da15989">
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="102d0ef8-0780-4e1f-ad60-e4471ea28927">
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0e9f8a8c-db87-43b5-99cc-23646ce23d16">
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ec08f2d7-55ed-4d6e-967d-d91d8c06e78c">
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="07eb0407-cbd9-4596-a3bc-1d12e9449c32">
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="be571ce1-c5a1-48c0-837c-47208bebeb76">
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6d073b0a-f22b-4dae-a741-92286f3cc3e2">
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="473534c0-3f34-4af8-a71c-dd3f4c75d7ca">
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8af9a81b-c1c2-41db-a493-ab8b7a86cc9d">
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="36ab9010-3982-4f41-8eb0-10890eb2e899">
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4bbda393-8b40-4667-8819-8b7170ff5239">
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a4c080f6-6199-489e-b2e3-66712be40e2d">
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="efa31cc9-9da7-4dbd-9271-33cd0711de06">
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="88170207-01c5-440d-8191-24ac8736d92f">
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1c1b0672-241c-4215-8c36-20aa827d4acc">
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="49d326e9-591a-4c89-a6f8-ee32d0582f7e">
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5115af48-2fc2-400c-a68d-167b3f737cc6">
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f8cea922-bc53-4863-80e1-d80ab997285c">
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cc0dcbe5-5e4c-4830-8b02-e8f77581a032">
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c58a2397-fb77-4a7b-acd4-80f164df19a0">
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ec164b98-af33-45f4-97fa-022c01d0dd1c">
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="454e2350-ecf1-4eb8-88a9-8b42673d5a49">
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="72c59043-66e3-4f51-bbe3-60f3834ce83c">
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5f33d080-904e-4c54-b43b-e1f460413eb9">
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7cb9befe-ee7f-4103-8d2d-7ae8c2461914">
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b70fa340-4176-4319-9a8e-efe78a424573">
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1ffd32bd-c2cf-4a58-984b-bc20af8fd5e9">
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="78c6152d-9f3d-41b6-916a-f650c851eccb">
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3c157102-5519-41e0-9bbb-fb6993dc62e0">
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9f0ef359-a97d-4d05-91d4-0f0bad0fdd48">
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="04c4008a-5aca-4626-a47e-6a97bbaffe67">
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="403695f4-0b63-49ac-89cc-84c83359f9c3">
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="589712fa-9bfa-492c-9944-df2d3fd23eff">
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d41eee31-4c98-4900-8677-fcf7d7b1e31b">
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="882a37c9-8cba-461b-893e-fcc53bf41866">
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="016e872b-de45-40c7-b4a5-0dcc06fda14e">
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="105ad3dc-6c61-4fd5-832e-6d8774120836">
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="49bf3469-1c55-4200-a827-4d0bef441ea6">
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4927fa02-a480-4e57-9b87-720d7926d9dd">
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="88ec1f63-5734-4f47-b6b2-667fe301e6f2">
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="72b35373-6545-4327-a690-378516950c38">
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d95077ef-4fd7-40e9-9be8-c27775005cc0">
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fe288bff-8206-4633-822c-ca50156aba3f">
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="657a8202-fa60-40bb-b0ab-acfe045735eb">
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c5928dc4-ce66-4041-9e18-3aadba1c1f0d">
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9f69fb4f-f1b1-46e9-9893-58f5a3bd0ff7">
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="858412fe-0cb6-4a66-8af6-5bf73f78ffa2">
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="deadc968-f140-487f-b914-6ebfa436b534">
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="beddbd0e-cb33-4697-ac23-4532842927d4">
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="61a74eff-2cb9-4b22-8243-0d37f292a9d8">
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f7a807ba-0cd5-41a1-b8ac-f49c311101e2">
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="64ef3aa7-29b6-4feb-a214-112530d9b86c">
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9c9f9fd6-eba6-44c1-a260-249ddae4a90b">
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5ca9a54d-3531-4d5a-852c-a437f9c60606">
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e023d260-ced0-49e9-8927-d68f6ff7b56a">
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d3d8f193-7149-49ff-b1e9-7de2feadaa27">
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b6379abd-faf6-401d-a132-c3596a2adf12">
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="da029035-70bc-4d8d-b57b-73e4fd9ae61d">
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="395cdc79-912a-4d05-81c5-4eb7e12544fa">
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b19095a7-d22d-4b7e-ad34-cf7db4730124">
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="89bb13f4-dc48-4115-b493-570d9cda9522">
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e03e86e3-c7d5-4957-9752-7b98d41665dd">
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d7c66dfe-8e64-442b-b646-08e3c4fad132">
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7c612777-e8ec-4707-9ee1-ecd16ac1d995">
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bddd81db-91b4-4d53-b0e4-59207b55edd6">
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d574bd8d-d09e-40bc-89cd-aea9342e2133">
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cc2e6266-203c-4d3c-aaf6-84a8d99c5c36">
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="*************-4032-8444-4b8cd454f3ea">
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4c354fad-851a-45ab-a852-374703b9f373">
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d9c22f84-5666-401a-8cb5-c06c7e4df9cd">
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9fa81964-2dd6-4c69-bd7e-3a8c93ef452c">
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5866fe5a-82e6-4054-bc38-be6f44fd7dc9">
      <grAndCompPorts>112</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="da44a6a3-5c14-46ca-947a-bafb72180719">
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3ad30c23-42e3-4458-bd9d-b61172d7b764">
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="22a800fb-51d9-40a5-a6a2-04d2a86d261a">
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bc238b1c-d0e3-4d7b-89bb-a3802f21fefb">
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7c89e7ac-f800-4f9e-bd28-2f51c39bf9b1">
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8267e35a-9b5a-4c92-a5b9-4abfb1f656e2">
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c8741dca-1d73-4cab-967b-923374bfd98a">
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9505413c-e567-440b-b99c-08a6e0e44920">
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4307c0c7-677f-47e6-b892-5472a731bd12">
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="472908ef-6dc1-434b-8b0a-b946959ca0e4">
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0ec19c0b-72d1-498f-a387-e950144ff263">
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fe545803-fd33-4939-b741-c27cdb4ce7eb">
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a330b2b3-1205-4d20-9bd8-643a17a36435">
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="36a4b643-bbca-41b4-8482-c7b8dbad8d15">
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="710dc197-0489-44e5-aa56-4a34dc96bffc">
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9f1df55c-2101-4393-b973-9e5c8ea79f2b">
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="58ab9dd6-f164-4107-8d0e-a269b8ab4480">
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8b67e4cc-2de6-4fd0-9dba-3a330145f58d">
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="faf8edc1-ac15-4e94-8c42-ab6323975025">
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ea933f03-8234-4af1-bcee-9c03a4703d78">
      <grAndCompPorts>132</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="82e93289-8e81-45d2-a8e5-fa123aeb0e1e">
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d7dc204a-231e-46e9-8585-0aa31598699f">
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3b68a826-3054-467a-9524-b2bbca3f6b49">
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="97f0fb14-18c8-4792-9c97-ba0ce0fd587c">
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="45a5b364-6055-4042-b041-2c12efd16394">
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="12f174e7-ee75-4edc-bd2e-394033c56fff">
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eb83ed4e-e141-4b58-b7db-5645ad1a484c">
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1507dfb8-3030-44c3-ba08-76f828a89a5e">
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3675f284-e09f-4e4d-86b8-129d9367f976">
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bd387896-4b67-4ef2-b99f-be119c510907">
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="028dd88c-3ccb-48d9-9b68-5f7233d72bb8">
      <grAndCompPorts>143</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ea6402e3-c0f9-4f2a-93d6-3ad2508302d5">
      <grAndCompPorts>144</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="13df5417-3ce7-4190-b3db-10fd002d8d5a">
      <grAndCompPorts>145</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="19951f86-eaa1-41ce-8c1f-ece32f9b8175">
      <grAndCompPorts>146</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f6975fe9-f5dc-4989-87c0-189f5e5eec06">
      <grAndCompPorts>147</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c1e62ff6-200f-40d4-852e-7fa4b9971af9">
      <grAndCompPorts>148</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="492a0cb7-bf43-4705-abf6-ee1d2f68057d">
      <grAndCompPorts>149</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="842b048e-c473-4b2d-8114-2fedd9165beb">
      <grAndCompPorts>150</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8bca14e0-5a45-4c32-9371-ce643630e8db">
      <grAndCompPorts>151</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9eb22dff-0c85-4485-991e-20dbfe462233">
      <grAndCompPorts>152</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7891d413-5cdd-49a6-801f-5fb00136a77e">
      <grAndCompPorts>153</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a181a380-19e2-4c7e-9051-e3751cc16715">
      <grAndCompPorts>154</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1c8242ee-a4de-4d26-b632-0258ac09fefb">
      <grAndCompPorts>155</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="10a6538d-ee1b-4b29-9bee-56e742677074">
      <grAndCompPorts>156</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="96f8bb72-d770-45b2-8f74-3d8a9648f9a0">
      <grAndCompPorts>157</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="757e44d4-7cf5-4e7b-b341-c86df0e71a95">
      <grAndCompPorts>158</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a62882ab-b7c4-43bd-ae0b-745f6b081ac0">
      <grAndCompPorts>159</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="96565763-91c9-4230-b11f-e1c5a23f5a49">
      <grAndCompPorts>160</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1d025ca1-f68f-4bf8-999a-86324f1a14e1">
      <grAndCompPorts>161</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="13fc2089-ca0f-4b69-b1c2-86c35288c5e1">
      <grAndCompPorts>162</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="457be094-b9e7-40c2-84ea-7ffad310124f">
      <grAndCompPorts>163</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="166ac5ab-057d-4e04-94b5-6b3d06d6d6ef">
      <grAndCompPorts>164</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d19f014b-61e1-4fe0-acd1-129f4cc44bac">
      <grAndCompPorts>165</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5183b51c-242b-443c-8211-5d636e72f4ae">
      <grAndCompPorts>166</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="02989e31-a40d-44a1-ba67-8ca163797214">
      <grAndCompPorts>167</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f4d97686-471d-49fd-bdcb-a33fde5865fb">
      <grAndCompPorts>168</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="aafe588b-338d-400b-ace5-1d41237ff2df">
      <grAndCompPorts>169</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a5530da8-092b-41c9-9d4b-6a3d17a70e56">
      <grAndCompPorts>170</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7c1b772f-e0a8-4ef1-b5ef-32c20145a499">
      <grAndCompPorts>171</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="aba137c6-33eb-4927-8205-e094fb7b5454">
      <grAndCompPorts>172</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fb765916-93f6-4207-9de6-4bf2ae3ff788">
      <grAndCompPorts>173</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4916d61c-5cc8-4096-893e-8e50b906a9e9">
      <grAndCompPorts>174</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="46578454-b639-496c-a238-8fbb8f4e0f7b">
      <grAndCompPorts>175</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="28aaac73-a880-4fa9-bfd5-532718f285b9">
      <grAndCompPorts>176</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="87e811a0-0e5d-4164-bdc0-ee7d2ae38230">
      <grAndCompPorts>177</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0f0041b5-968f-42b5-b791-4adc91d51929">
      <grAndCompPorts>178</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="56fa378d-6946-4b6f-9050-398afe11f2e3">
      <grAndCompPorts>179</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1f541c88-8e1b-4edf-8c7b-c85434b272df">
      <grAndCompPorts>180</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6b8a3749-1b78-450f-8c74-8e4e60f44680">
      <grAndCompPorts>181</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ad64e6ea-6649-4fb5-9ba1-45e1f4488979">
      <grAndCompPorts>182</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="342e6d2d-c356-44dd-982b-4b4a599850c8">
      <grAndCompPorts>183</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="603686e8-246c-4754-a2a0-0924349a86e7">
      <grAndCompPorts>184</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0a57e165-5bc2-489e-8610-eb6ae11865b1">
      <grAndCompPorts>185</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8e5fa1f7-7f96-4bae-b4ef-d54a7757d300">
      <grAndCompPorts>186</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4fb65bde-f4d4-4fcf-bee6-45cd5ba13364">
      <grAndCompPorts>187</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="20002cb5-779a-4c17-849e-7315c2cfa40d">
      <grAndCompPorts>188</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="187c3cb6-e235-4ffd-8c12-71edc6cbfe6e">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compZcCacheNeedsReset>false</compZcCacheNeedsReset>
    <dataDictionary>MtrCtrl_Mdl_Data.sldd</dataDictionary>
    <dataDictionarySet>MtrCtrl_Mdl_Data.sldd</dataDictionarySet>
    <dataDictionarySetForDataTypeCheck>MtrCtrl_Mdl_Data.sldd</dataDictionarySetForDataTypeCheck>
    <dataTransferInfos>AAFJTQAAAAAOAAAAOAAAAAYAAAAIAAAAAgAAAAAAAAAFAAAACAAAAAAAAAABAAAAAQAAAAAAAAAFAAQAAQAAAAEAAAAAAAAA</dataTransferInfos>
    <defaultsCMapping>{&quot;Inports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Outports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ParameterArguments&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;LocalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InternalData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedLocalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Constants&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;DataTransfers&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ModelData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InitializeTerminate&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Execution&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedUtility&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;}</defaultsCMapping>
    <fundamentalSampleTimePeriod>.2</fundamentalSampleTimePeriod>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="4e48d23d-3748-4fbd-8c51-4c77aec3d98e">
      <dataType>CfgParam_CALBus_t</dataType>
      <name>CfgParam_CAL</name>
    </globalDSMInfo>
    <globalVariables>#ADCMonBus#ADCMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AmbTempMonBus#AmbTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParamBus#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CAL#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CALBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_MosfetLevels#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_hPosBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#Configuration#ModelConfig.sldd#</globalVariables>
    <globalVariables>#CtrlLogBus#CtrlLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#DIOCtrlBus#DIOCtrl_ExpData.sldd#</globalVariables>
    <globalVariables>#DTC_stDTC_Failed_C#MtrCtrl_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#DTC_stDTC_NotRun_C#MtrCtrl_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#DTC_stDTC_Passed_C#MtrCtrl_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#DiagComManBus#DiagComMan_ExpData.sldd#</globalVariables>
    <globalVariables>#ExtDevBus#ExtDev_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#HallDecoBus#HALLDeco_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MOSFETTempBus#MOSFETTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrCtrlBus#MtrCtrl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrCtrl_nMtrDTCMaturityTime#MtrCtrl_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#MtrCtrl_nMtrMinActnVolt#MtrCtrl_Mdl_LocalData.sldd#</globalVariables>
    <globalVariables>#PIDDIDBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_ADCMonBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_AmbTempMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_BlockDetBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_CtrlLogBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_DIOCtrlBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_ExtDevBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_HallDecoBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_LearnAdapBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MOSFETTempBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MtrCtrlBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MtrMdlBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_PnlOpBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_PosMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_StateMachBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_ThermProtBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_UsgHistBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_VoltMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PWMCtrlBus#PWMCtrl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PnlOpBus#PnlOp_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PosMonBus#PosMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefFieldBus#RefField_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RoofSys_tSmpMtrCtrl_SC#RoofSys_Data.sldd#</globalVariables>
    <globalVariables>#SWC_DataHndlLyrBus#SWC_DataHndlLyr.sldd#</globalVariables>
    <globalVariables>#SWC_DiagLyrBus#SWC_DiagLyr.sldd#</globalVariables>
    <globalVariables>#SWC_HwAbsLyrBus#SWC_HwAbsLyr.sldd#</globalVariables>
    <globalVariables>#SWC_SigMonLyrBus#SWC_SigMonLyr.sldd#</globalVariables>
    <globalVariables>#SysFaultReacBus#SysFaultReac_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#UsgHistBus#UsgHis_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#VoltMonBus#VoltMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#nVoltFltSmpls_C#MtrCtrl_Mdl_LocalData.sldd#</globalVariables>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c1d982d2-9d99-4710-b97b-5e752f693841">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>29</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7c88cea0-4a37-4f60-8dd8-8e43a245c770">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>64</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="67c3d603-74dd-4731-9495-bc10913b45b1">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>112</grAndCompPorts>
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>132</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ba764da1-7177-4ead-8c2a-b274cd0cbf8b">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>143</grAndCompPorts>
      <grAndCompPorts>144</grAndCompPorts>
      <grAndCompPorts>145</grAndCompPorts>
      <grAndCompPorts>146</grAndCompPorts>
      <grAndCompPorts>147</grAndCompPorts>
      <grAndCompPorts>148</grAndCompPorts>
      <grAndCompPorts>149</grAndCompPorts>
      <grAndCompPorts>150</grAndCompPorts>
      <grAndCompPorts>151</grAndCompPorts>
      <grAndCompPorts>152</grAndCompPorts>
      <grAndCompPorts>153</grAndCompPorts>
      <grAndCompPorts>154</grAndCompPorts>
      <grAndCompPorts>155</grAndCompPorts>
      <grAndCompPorts>156</grAndCompPorts>
      <grAndCompPorts>157</grAndCompPorts>
      <grAndCompPorts>158</grAndCompPorts>
      <grAndCompPorts>159</grAndCompPorts>
      <grAndCompPorts>160</grAndCompPorts>
      <grAndCompPorts>161</grAndCompPorts>
      <grAndCompPorts>162</grAndCompPorts>
      <grAndCompPorts>163</grAndCompPorts>
      <grAndCompPorts>164</grAndCompPorts>
      <grAndCompPorts>165</grAndCompPorts>
      <grAndCompPorts>166</grAndCompPorts>
      <grAndCompPorts>167</grAndCompPorts>
      <grAndCompPorts>168</grAndCompPorts>
      <grAndCompPorts>169</grAndCompPorts>
      <grAndCompPorts>170</grAndCompPorts>
      <grAndCompPorts>171</grAndCompPorts>
      <grAndCompPorts>172</grAndCompPorts>
      <grAndCompPorts>173</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="88ee135e-1147-4459-83cc-3a05b2b5cffd">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>174</grAndCompPorts>
      <grAndCompPorts>175</grAndCompPorts>
      <grAndCompPorts>176</grAndCompPorts>
      <grAndCompPorts>177</grAndCompPorts>
      <grAndCompPorts>178</grAndCompPorts>
      <grAndCompPorts>179</grAndCompPorts>
      <grAndCompPorts>180</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1f5a0d24-c76a-4dc9-9c94-************">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>181</grAndCompPorts>
      <grAndCompPorts>182</grAndCompPorts>
      <grAndCompPorts>183</grAndCompPorts>
      <grAndCompPorts>184</grAndCompPorts>
      <grAndCompPorts>185</grAndCompPorts>
      <grAndCompPorts>186</grAndCompPorts>
      <grAndCompPorts>187</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="87a5fd4d-ce4c-4d68-8084-4ab8ca703cbf">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>188</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompOutputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fa6d1763-9bc9-4aee-acb7-506c0f1499e7">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </grToCompOutputPortsMaps>
    <hasBwsAccessed>true</hasBwsAccessed>
    <hasBwsAccessedByAnyModel>true</hasBwsAccessedByAnyModel>
    <hasConstantOutput>false</hasConstantOutput>
    <hasModelWideEventTs>false</hasModelWideEventTs>
    <hasNonVirtualConstantTs>true</hasNonVirtualConstantTs>
    <hasStateInsideForEachSubsystem>true</hasStateInsideForEachSubsystem>
    <hasStatesModifiedInOutputUpdate>true</hasStatesModifiedInOutputUpdate>
    <inlinedVariables>#MtrCtrl_nMtrDTCMaturityTime#MtrCtrl_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#MtrCtrl_nMtrMinActnVolt#MtrCtrl_Mdl_LocalData.sldd#</inlinedVariables>
    <inlinedVariables>#nVoltFltSmpls_C#MtrCtrl_Mdl_LocalData.sldd#</inlinedVariables>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c8221c49-5de6-46fa-9b1a-23677aa689e3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="84065ca0-e969-4ee8-ba48-ce9940f265a6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cebcd341-755e-45a3-be97-b354dfbfc034"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6e6da84b-7b30-4a74-aa27-1bf44037e3f4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0c469916-f8d1-4cdc-bd90-1c19932c0852"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e4b4bbf2-c178-453c-9d4e-54b35e022cfd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7d666ad8-0bda-492e-ba35-d591a6b367a3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>1.0</designMax>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fd45e48e-8983-473d-852e-6887940813fd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9e7aca0a-a8a6-40df-bbcc-7f224895e147"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3c9e3888-023f-4796-bc2e-80514a61e919">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="61e4947c-16db-4e35-82b3-949c60f4afa5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="421a7722-f9d8-4e28-9d36-ecdecef3e281"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="59a95049-e6a4-4fea-9e15-1ec821feee08">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5430fab7-f552-488c-8e55-9ecc9dcb32b2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="198267e7-9d4e-4ee9-8004-c80f87e99306"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4d9d6dde-6685-473a-9b63-15a3168f1319">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4570871a-5b5c-4ac7-825a-bd3c02481e4a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dfe4b6b2-deb3-421e-9c24-************"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6c962f50-bfdb-4533-b8aa-6d2e429df615">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ace3d5f8-e93c-4e54-9ae6-27b5ee4168ea"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fbcae2aa-39d5-4b77-ba33-701c3a2febcf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="52b86273-fe65-4e05-a83a-2e441bf7ff1e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2950e407-a044-468f-8081-0c9ca168960e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3b228ae8-a1a8-4e08-b43f-7f7ef9c9f115"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="23402432-8b1e-4733-ae3f-df2fc79e7b94">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="305077eb-711f-45af-aa41-6f07ac6d14e5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f5cd5508-736d-41ff-a388-ea8c1e3c846a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e4f8dc3b-cc49-4462-84a6-0e6d0dacbc21">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="14084a92-83ad-4c84-8e95-dcb0542ef763"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="704362b8-40f0-414e-af9e-027af55a62e8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7b2ab69f-f8eb-4565-a690-9a0cababab10">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="801a801e-286f-4bf2-b20e-c17265fbe606"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6969ad01-245e-44c8-b66b-69632fb9af97"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a1d7ccb1-7f62-491f-b6ce-677d5aed48a8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a664dd5b-a341-44e9-8024-6ff077548255"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="10c7faa6-61c9-4ee2-8ebf-0141aec04e35"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6f625a2d-54d1-4917-8c96-4bf627faae46">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6dcf1890-ff9f-4bad-908a-cadbf892ae11"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a8afae51-0238-416a-ac63-3e98c8f4771d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cce95878-a650-4408-969c-3e4bde576217">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7227d5b4-4c40-46f1-93fb-fab5a4eabb8a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b6d4607e-b056-4b57-b58f-0019275ad373"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="72681bb6-3f0f-4a3f-8ecf-55530cbdafe7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e85fd86b-b8d7-4c43-8729-3d254b021549"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="76eafa70-93b4-4b52-8a8d-c12bc92f174c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="849de9c7-2dc1-4f71-b286-a20879cd67a0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b0c9c0f6-ff6e-4750-9265-ec78e104329d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8e7b1c80-3df0-477e-adc4-ddbbf0f0c5d5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e8c44d79-18ca-4604-be68-9ee6e825a0e7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="01021098-3991-48a2-ada9-bb8f7aa526e1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9eab096e-6825-4cd4-aa6d-96c4c5238eda"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="98387c8e-014f-47f1-81c1-c8412c3fc561">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="421dc8ec-205e-4dc4-9c7f-6ae9fe844fd9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="744daaaa-230b-4330-ae49-96570bf462f9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="622446fc-012e-429a-9c98-664abfa95f37">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8b0e199f-e139-4a8f-8cb8-252c32e36c0a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="54fa86d1-e838-411b-9d0d-f4ac99d81f64"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="80296f75-2bbb-4e72-8499-d9c4736a8ff6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fd9821fb-dd47-4d40-9aec-caf936b8af15"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="886b1b8f-9162-4336-a9db-af44aed53e5e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3a2e1792-bd7b-4d56-a538-d7492d54044b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1ceda1de-6e30-4409-8ec7-de19d335ec1c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9c7514e3-2040-4b6b-8b2b-a8518555c1b3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f954eafa-b9d9-48e1-937c-d914f284ad4b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6dd9cf6d-8e41-40e2-9ddf-284ee5c8947e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="38c44728-32b5-40a3-b9d6-a64eaae50004"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1d948f39-e6b5-4285-abfb-b506ed49a5b8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3b07b107-e45d-484f-84fd-b4873c72644a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f4e72e02-bbfb-4aad-add9-4960daaf43ba"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6ce65067-4740-4f98-bfce-6ad8736accc1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="310db107-b082-4ebd-b792-f15dfac1cba4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e622819d-368c-4d70-a6d6-ebba1241bb17"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9b6deacf-44dd-423e-ae8d-5e7152716065">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6cc1de5f-8e37-460c-9403-fa8e3d29b209"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fe9a47fb-d7dc-41d5-82a3-ce662e302004"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="42cf0308-9339-411d-8d1b-409a5ef9e703">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8d95cc67-0404-4609-8373-a1ad2719bbfa"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a13dd1c7-81d9-4fe1-b0c1-a7e5dc1656c2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2770a1e0-be64-4463-bdd0-5cb97c29d937">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2f25bea5-77c6-4c9f-896b-a9357d541ecb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1c147f91-47e0-4207-bfa3-73448d38d709"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3b5f19ef-d5f7-403f-8845-9d5b5f115946">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6f21c0a2-411c-4254-b971-b5fe76a9fe44"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="052107dc-7a74-4b29-97c7-d6638e45155c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a9bd15ac-1605-40fc-9bb9-cc74762145c3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3e6b3fa7-cb3a-42b3-af6d-9ec13fe43ff5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="35abaf74-999d-402e-908c-63d5dcfda0bb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="69c16fbb-5803-4752-a0e8-4102105305ac">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="39328d4e-e4ec-48ad-befe-a0d83878c734"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1ecb98b2-b59e-4fe1-b95c-3879099faf04"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="525b7cba-8f79-4b05-aa3c-48fcc3d4627d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cdca1ce2-e515-4452-bb69-8f4df513a3b8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a0293c6f-8cc4-469f-8b4f-0e17b2cb4cf4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3011d453-8f0b-4338-b4b6-f85da95480bc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bb584db8-b80b-4d05-8f53-fa278373cb8e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b0d99f0e-cd96-4a7d-b415-dfbdb0320b8a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="79744d4a-cd48-4f86-aa33-d8195a24ee94">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b4017ff7-aec8-4b4d-b901-66a1b5a4acb5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="426e4293-fefc-43de-ac14-1495eb6a3f16"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="497bac14-e68b-4e78-bfa5-45c3d4d9041b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="792748d8-935a-4f36-8434-886fa3db7ccf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a2868fa6-e23f-491a-a881-833a1bc32a15"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e251c287-303d-45a7-ac92-9ba8167b724f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a2032753-bff8-4a61-9906-41ce9bc3c522"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5868678d-7f31-4b74-88dc-c0eeaa726402"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8b40e293-ae04-4499-8069-e0175954af99">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6985be18-9d02-418b-b57b-0d9f9dff41b0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f0379b5c-da6f-4bf5-86e3-ceeeda3547bf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="91c8b611-8483-4ee2-9458-9780388d08d5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="76305624-570f-4bf7-ba5d-d21e67abe118"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="680c9ae9-5948-47fb-aefb-1511a8729a9b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8883e30c-349e-43d7-83b3-961d0c4997e1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3d8ffa48-bd3e-41bb-8039-73c2a6d22547"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="78eaa815-4ad7-40c2-8632-7d624ec84188"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cc8b9e1d-ade0-4eb0-addb-7c739dc11be9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="abec474e-1e8d-489b-9167-d8d204e8be3c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6b520180-8845-4189-bd19-bc2137a44a32"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d74ce64b-1fc8-4673-bc6e-50378776387c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0320915c-65f8-4346-9e83-8b219ea7ac31"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cd5bf839-dff2-40f7-81b1-e8606c393344"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b8f8ce1b-39c0-428f-841b-5ada7a4e1916">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9695ad9b-8fcb-4493-9cfd-c797941898c7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="68e7ce4a-66ca-43c7-a3d6-3009d528444e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8bfa6945-3641-42b6-8e7f-174331b058c2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e87f6fff-d2f7-45bb-9f60-378bdc247a8f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dd2c3038-03f7-48cd-885b-83b5723bacd8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="99c9de2c-9e7c-4f84-b382-7ad8e4fd4863">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cb8fdeae-22d2-451e-bf4b-5bfe3b3e71cc"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a859f7a7-fa84-4d3a-869a-825981242bf9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e91e117d-d194-437e-8824-dd1343669d07">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0745134c-d7ae-497c-8d73-ffe8c56ba552"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d4e1094d-4ba0-4218-86ed-b20690a36edd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="79c0d7d5-646c-4fa9-b371-f473ed863ade">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bf26f12f-5866-4be9-ae3c-499c654f4627"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="365968e2-80a8-4568-8a2e-fa316db2744f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="878e3bca-bf36-4c9b-a634-3391f02dc65a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="85cac425-ae9f-42d9-b15c-d33f382bc69a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c29bf2a9-f0b5-4220-a931-28552fa85751"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bc3c29b7-dd8a-4f45-98a9-a6cac3168b7e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a48c03c5-84ee-4360-9c86-9920593e3bb8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1e355528-02e6-40d6-a17d-ef618ee114d7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="03e62097-e134-4d79-94d5-3c155a2b8201">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b1416435-878f-4c5b-bf51-e31fc0be3e84"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="23c8c0db-1e05-4405-b443-ccdef63385b7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0c126d48-38d6-456c-a582-aef5793ee1bf">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="21ed5149-a350-46f2-ac63-cc2ae7b4df21"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="25a3f62b-b3d6-41e0-bc14-66e223d1663b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="986f862a-b8d8-4613-910c-c724ab1c4f99">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d77945b4-9fca-49e3-8d3d-811cf08f5116"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="83f0468d-d819-47e3-b62a-f6aceab0bfba"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e5964ec0-d91f-4c6b-a813-a670342ea597">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="737e6210-e9af-4913-b7c4-9a968bda4315"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2612634f-ee30-4638-b6cb-0968b7670754"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fd80286d-253b-4c18-abb1-8425161118fe">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e892caa0-08a1-4a0b-a7ed-ea7f4ed972a2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="802227a8-00ae-40dd-aef3-f3d63201eea2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d955bb34-24c8-4ece-a393-b7ed94e7c866">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e259484b-b1ba-459a-a0e3-df07ce894b18"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e94da1bf-8b25-4307-840c-577e6a70ccdb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="eaf82d1f-bec3-4aab-99e2-fdcf4dff9eba">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9f32f80a-bc4c-4935-a54b-d12b7f9f7304"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fddce8b7-46bc-420c-b425-48f648042eb7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9b9536ce-8368-4cc3-a288-20f575183bfb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="86de7187-25bc-4ab5-80dd-b3de982c7b96"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7d54949d-c643-47eb-9303-23717b58aed9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9a8d47de-b427-497b-a632-535bcb97cfe4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0f34a5ac-348c-4d55-b435-cafe1a5fc512"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="337db035-f9bb-4f30-933a-a9849581032f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="47b1f96a-1904-4f70-98e2-7b5f0a3dea84">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2fb6bddf-fa22-46f7-9efd-11b02853e856"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8c02d393-79d0-4955-ab8a-231e7c1a8f61"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0e166f00-f870-4893-82b8-60399c2c5b9d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e164d486-9d6b-4a00-97d0-f8296d31f044"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="42123d18-c7e5-44c9-90ba-20be047234d8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4fe55aee-6670-47cd-aa84-818c5e4d33ce">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b19b12ce-277f-4abb-b58a-3a591c2161cb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d4bf3f3b-cf8e-4ab8-9407-5c18251f5c24"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c93ba925-ddb8-4569-8b94-12030d55f940">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b6cd2cea-fd48-4b76-8106-c4168bc11ff0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c6234cf8-fa40-4a77-bc87-033bc9bcc3ad"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3d8c01b7-5405-46a0-a895-cbd943865ad8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="06a83919-a7fa-4657-a863-d7a16defd14c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bf964727-cc62-4fc1-9226-6e74d3193d7c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="205be16a-8904-4a77-a990-e5330d951d55">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d01c4063-951f-4f57-a25d-68d2164527cf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8ea1f90c-a7c9-4f42-a1a3-2e94acd842c9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="37070658-edbb-42e0-83c0-b94f4c3f44e1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e949c8ef-0a70-4b1b-a958-69f20a01f1a8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="959242e7-d496-4a94-afd0-8aea324cf8bf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7b234cd3-0d45-4279-820e-f2c931cfc8b3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2960ab44-6c1e-45f5-8c2f-04cb8bdf60a4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="daa802be-08f8-4fa7-8dc9-643d576833e7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="622a4b72-560c-4133-ac2f-62f5b6d3d140">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="421581ba-445c-4d54-b5d6-129046ffba97"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d426ae24-8baf-468a-b0fe-6934afcb5d4d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6b2ac778-4758-4928-b41a-b2c5cb07b04c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="42574ce2-f94b-4c00-bc04-c197fbc8c0ca"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="42ffa2d5-8930-4ee3-b262-ee9f7db8c724"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ef27ee5c-d8ec-48ce-8ada-ac00c255f260">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="90f7f2b9-7b42-414f-a9fc-c806aef74c04"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9625c9f2-47c9-4930-b465-72880fd8f1de"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8a014972-4796-4ec4-a0d8-ce0387f54136">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8c2d6666-b7db-43e7-b088-b79691c91136"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ac1c3f27-4ae9-4721-a20e-730bfb86cec0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6e450608-a159-40af-a6fc-1a012e523d89">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b325930c-0332-4f2e-a035-8566058fdef6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c287ce15-fe1a-4f52-990b-191a24998a0b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3ff46d63-aff1-47c2-8ca6-bbf91da3363a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ee254461-ae32-4983-9b63-c7a7e68f52b0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a1a6b9d1-5924-4ff2-972f-ba401d1b7778"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4fe064d4-5f76-47a8-b7d1-4f8b56ff0123">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="204ab023-6319-44b6-bece-17ac179dc790"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4bc6b018-8367-42cb-be8a-b3db659a63df"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="142f3fe9-c0cf-48f3-9e58-2c28e56534c2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dccb446b-0426-4f29-b3a4-5c12dc09b3bd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f09105c3-2d3b-425f-a0f2-01de292a373d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="22e810af-ece2-47e5-b5b5-27a9901684fe">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9189ca07-0d78-4857-810c-7117f39fcaff"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9f4e0766-29c8-49c9-8062-bd3843713201"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="66442381-726b-4993-a5e0-abf5b5a1f684">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5c04a53b-0986-4344-a46c-1e7a678a28d4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="20e28804-461e-451f-9a4e-4c9fd49e46fd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5c1d1a4a-d43e-4219-b7c6-1a0e4aa37183">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="db264272-64c0-418c-9265-c5123f40de95"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6557e9ab-2219-42f6-87c6-52503dbd3c59"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="13bc70dd-9649-4a0c-a9c0-41170e6d734d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9856bcc3-36a8-4545-9ec9-8abfb864d4e4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7dc451ee-a75e-4804-99bf-163123f8aab3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b8822327-06c9-4c32-b2ff-9b54618b8989">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9b90e2c5-f682-46ec-855f-501f78470bea"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cb9a5bd8-1683-482d-8b92-38ad6dcdca62"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a645edbb-c1bc-4ca4-bdad-f4a2d4048f1c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7ffff641-4cda-48ae-bb9f-13e2b98f6616"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="47b6bd17-00d9-401e-9d08-e0b33436aeaf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1b3d5e09-fe3b-4edb-b2d1-9df386c96769">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0451c87f-a435-4cad-8b63-3c3d8de51fe8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f0a18079-8997-4b5d-8971-73b97bf888ce"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="07d70aec-ebba-4510-a7ed-6fb57e003c62">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0f736a74-f3a8-4342-a0ff-24ebd9d434d3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8741095f-3f12-4705-a648-e3f8cdb9b25a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="aa96418a-1561-4caa-a63c-1a393d1c1269">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b67ce7eb-4caf-43b7-adcc-7e87e9a14a29"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d138d07a-8068-44d2-93b1-ea08104e02d0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="482ef77f-0ebb-4bd5-8527-51f2a9bc11b4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b1015950-e41f-453c-aaf8-e813f0a9068d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="245f909f-9993-4261-b7c1-b2622b3f3873"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="17575da3-5c21-46cd-a776-c11610adbb92">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="54543183-48a2-4f38-86e7-9e35c3a12c04"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bb097770-ee77-4051-90f2-7b0e83862296"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c2d536ee-0c06-4002-88b9-86e3d45c7c90">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0c8554cc-93c8-4e21-9eda-7642eee8566d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8bdf96e0-b4c6-4634-a474-fc81326afed9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0d17ae9e-087e-4aff-ab1e-c3fbe0c0a578">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="baadcc1f-251a-4a07-bcbf-7845d0a611a8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bef8e5a9-35be-476c-a3a0-863042d5b5b2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a06aa850-cef1-4627-b417-923507309f96">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9f6072ab-c594-4729-b294-ca7f8697a97d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5da16a2d-cb6f-45f1-8afb-7302d00557a3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dbe48d19-6ad9-4e82-a3bf-5a1c0751e0b7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="02401450-d438-49ec-8f74-13f8ee3381c1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="34f7816c-de08-4ed7-9e40-3b806f8d61f0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="70990eca-62c1-4cbe-aafe-40cfe5a4e9b6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="504a774c-730c-48a1-a644-1c47a12142a0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="02aa261a-3a47-4d53-9f77-dee763375b84"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5da56ad0-ae0c-415b-989e-3fcce4025d08">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e8de77fc-a703-48d9-b68a-80f2a0ff400e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9a4a1311-f891-4956-ad4b-e94e2aa2b6f7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="76b42eaa-f475-4542-ad59-910b6ab93318">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6886b42c-5ac0-49ad-b803-89f6978556cd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="65fff217-e1a7-4a9a-a9f5-777f9c46f803"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b5e65449-ca47-41d6-99ca-22c4710bc15e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="05b0bbb5-6e5d-448b-9b39-9ee3f0d905a1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f0852626-efbd-4d9f-9398-2e31e2de3265"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="54d34856-2087-4dbe-9423-094d6299b463">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fd61fe6b-c878-4956-9178-cf9282de0445"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2b335fe6-1e1d-46f3-a8c6-e64c5c29fc52"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9e425658-df6f-40fa-b505-23a0078ce9b0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4aa8c382-af07-43dd-832b-7f79cb2fa15c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="497f4add-f823-4032-84e3-5a8298e777e9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9216731b-810a-4d9a-864d-267564d27101">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e35fbe89-8345-4156-9de8-641c04507a3b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="94e424c0-ab81-4daa-b9a2-525cbd3e8bba"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a4a6da3f-b229-46d9-b9aa-bc1bb2d006c4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="90bd5d56-3aaf-4ffe-b61e-c53a93a62dbd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d64a9e02-6d13-4e3e-a123-892a91213b6c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="20c27ee6-fafa-4343-bb1f-da00073419e9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c901017a-bad2-4100-b3bb-1838ef03d362"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="94aabdcf-ef01-4efe-856a-cc0437696002"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="05897e0e-a7fc-4572-bbc7-3bf72dd5a076">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ded01746-7602-4d12-a4de-c3d214064a5a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3f46ea9f-6028-4f7f-b62e-dff19bc526de"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a99ccbb8-5da2-4a96-9743-599d230e50b8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="833a9de8-5856-42bd-bb91-4622f43a54e8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="93144f37-66fd-403f-92f7-4ff10f8fde79"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6b20ad12-121f-4a43-a7ff-e2600d3db1c3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="876c1775-25eb-4094-9e57-16b378eadaa5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="174fd8db-5fc4-4442-9705-fcd979a9432a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9366a0b0-e195-4e57-a017-1bc5f21396dc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>16777215.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5fbd6814-389a-421e-bc0a-25065beb65be"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="308584f9-785e-41d2-95f5-7cb7b21bd3d7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0563e864-8d40-403e-9272-df4675b5a25a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="997e8117-9219-4d72-a295-18daccae1c1d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="237ea3e4-6e98-4303-8b78-d397bc7d2dff"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3c345d72-3541-422a-ac9b-9b35a6462ec3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e028db62-c532-4454-be9a-8dd667660980"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2ef25862-12db-427e-b0d3-a424b7b89864"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ff0fae08-7ef7-4f07-b449-c182832b10e2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="81d77776-2cf4-4dfd-9648-1069a033dc22"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c05c4b93-0d81-4664-8914-5eb190fb6d7f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9e81cf53-639c-482c-acef-218f26541435">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="16e79842-e0cf-4469-846c-fc41e24a868a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f2752495-117c-423a-bf7b-a95280f2eed9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f5532fb7-f183-40a7-8965-c2da1b533ada">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="82f86235-bbba-4d79-a8f5-2c2ccf56d86e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cc1fceb5-10cd-4ca1-93cc-dc981e69f815"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4ec06e67-f57b-41a2-afa9-c71549dc1a4b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="214752c5-7c75-438e-806a-04f03ecb037e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0b3b1b1c-3362-475a-a7c9-4bb8cf5ad553"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dc87c5a5-270b-4a23-8c0e-ac5933cebfc7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="98733d6c-55eb-480a-899d-8c878339f6da"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3240a143-b24d-4c62-a2f0-50f3572da83b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0a3af34e-19c4-468f-9b25-d7d65e3b5cfc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0ec4ab97-7a23-4d90-81f7-1eb29769812f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3c2421d9-25f5-4d29-9266-048eacbd9f5b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="57e0ae73-97d1-41fa-862e-c191d07cd5bc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5553400b-22b9-4343-997d-94a88c8e4f88"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cdfc2382-3a6f-4895-a1d0-e84087ecc947"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1ebbe7bb-93fd-47b4-8621-978ec75ec16d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="08be6dc0-484d-400e-9789-0db23e3d38ae"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1c8c0bfb-9383-4a6e-a069-49017dbff44f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d1fd33c0-e91b-491f-a455-faf6dcf9d9df">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ab951bf0-b7d4-4712-a7a1-ff90e35018a1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="06d8dd34-c9ec-4a31-bd06-b801d0bf933b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a07e78de-a5d9-43ab-b49f-f02641c3fd69">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="de0ff6b2-acab-4190-a424-2de6080d629c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="efcb0aa5-8e17-408c-a32d-bf715661b719"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="305e74ff-fb45-4cb2-8efa-eaf6692ea69a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d2a259bd-416f-49ce-98c8-ca10acd5c30a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4604d3c6-46b0-4fb5-8ed2-59bb991b44d8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4ace601e-b552-471d-81b0-************">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f54376e6-7e80-4eb4-8ba7-7c92b0fe3cdd"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ccb421c8-40aa-40ce-8dd3-8a00572b63b7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3b9a81fe-625d-4412-9b0c-2da4b382c2b6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="548688ed-9697-4376-8aee-235ee4887d2d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d0a57180-86c2-4bcb-8229-9db0c98df716"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c9b1cd1e-5129-410f-ae1d-b800bf1cb026">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a8ad747f-7c11-4f77-845f-603a75309619"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c5c0b0b8-b5d9-40a6-870d-8c7051355e0f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6c207736-a5cd-4f20-a7ba-ea1f30707c3e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3a1106bf-d197-489c-88ec-a524c4d2001b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8ae8c478-79b2-4b66-b3fe-9397783ff30e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6430747a-4bc2-4d2a-8fad-0c1ea0c46ec6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6791c06c-b48b-4f87-8013-1274e32ce3e0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a38ee6b6-db23-46de-a628-c9f7b0ec17e4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6ac1c22c-1c14-46bc-8921-8884f9a92faa">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cc04e27d-0e5b-46d3-9ac8-051dc10d1222"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="426affb4-573b-46b3-afc2-e9e3eafa094b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c00de23d-1d5d-4335-aeb1-efb69773dc83">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ac104b6f-3a1c-416a-93d2-835aad788f03"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5a7aab9e-368c-4d8b-9b89-deff0a2c76db"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c551e8dc-f642-499c-bea0-4cb429392ada">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="722a0bf1-ecdc-4307-95aa-225b14968d41"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="42048446-f089-413a-9cbb-90bfbc10ded4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4fd8ba61-7649-4199-93f8-21a28a6fab5d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="38d0d6eb-0fa2-479d-ba10-10c460eb96b0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4296feb5-59b8-4b1e-ae5d-e5a1201241e2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="49707557-3a38-4311-a59b-981808dfdfc6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ed5265b9-90e3-48e9-9025-2af819dcc223"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d25bab31-5cfe-424b-a234-fb435bcc2f63"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b07a6294-24bc-4962-9327-1e733679f983">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e31f66c9-7f8f-4a4d-9da4-97804a641caf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6ffa3ab8-1657-4fc8-b34d-168c8047a0b4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0f6debff-6a09-416b-abbe-9af639b754e9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ff061019-da8a-4478-93af-8b18c0635d9c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="81be195a-8ba0-49a4-8d73-8eea9db80ab3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="10792463-05ac-489e-911c-f0b6d00faec2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e8179af0-438c-47e5-91a6-ac21c7e5766e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="904fc009-51f0-4685-9be8-5e44239e248a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7dff494d-5ad8-4afb-a31d-2794fc038af0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="54b03c23-36a8-4548-a19d-a6ebde1276c6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e5f83c12-b542-4a6f-83ba-2a180109e75b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ad77943d-4af6-4ce0-af08-9629334d585a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="676163fe-7435-4813-b3ad-33df8ecb79fc"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="888e64b8-9878-4cc7-a3ae-67e4318363d8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cfff757d-d1e2-4d61-936c-c7e640071a4d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9725ccd8-9bf6-440a-8ca6-32148e009984"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dff604fb-19ba-4fbd-85ae-02fe092d93e6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="675a774c-8db8-4ad1-8b70-b6f9e39645f6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2a514810-fcd4-4b2b-b689-051fce51ec94"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3eb966e6-628a-40c2-b0b1-9e56bac4b722"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="283c544d-f930-4213-9e76-7e9104698d1e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="513a9e47-5285-4371-9fab-0673f664a944"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="645ff6ec-747a-438c-8372-4d925a480ff7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e346f3a3-e077-46d2-9c97-883b9650bc26">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b8f01d73-8edc-41d9-b18d-e4b50e7b5302"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7664434e-bf2a-48e6-ba7f-63ea7bce18cb"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4a5f7ccc-522a-46ad-a156-7f23c93527cb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2009b557-b9f2-4ef8-91ff-9b632385f2ae"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="92385d1a-1f61-4187-963d-00e1ac3a39dd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fa61cc3c-e2c5-4c6b-a95d-9614518cfc73">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3c8b5598-62b9-44e2-8b36-d59fc3c1c64a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6786d1db-bf1d-4b74-9310-69e34f8a5b9a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b6d14c48-1f12-4e99-978b-ceea16cfcaef">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a8ea13a4-4a4b-4afa-a022-c10d6208c175"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9e91769c-9a9d-4f35-b656-fafe670217e6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c7d06765-8067-451c-b38a-2e328fd4267b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="11476e14-03d1-4287-a89e-0442d850c3e7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d15f9221-0633-4259-aa95-f5259aae4740"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="781c7fca-e686-492b-b3fc-79308fbf4e14">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="81409752-3d13-4bcf-a8e3-f16930ff7915"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1ab3b937-e8e5-4957-b0e4-3ca9bef7b0f0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="23e9b1b6-1d1f-485a-8c06-ea4932d7a28c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a0ba2ec1-23ad-43a7-9158-b077bcc4ca04"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="662c8bc1-a06f-4658-b42e-9ac353cd3f71"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="60c0e2c2-83b1-42fb-911c-72f365dce077">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2f723475-28fb-4cf0-bec1-8531e85b2144"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c239de6b-238f-44ee-8d3b-cacd81af4d1f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1245a3b4-dd07-45cf-b3b5-3a8001c69439">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e1529907-a1ed-4876-83e1-f9bbc23c0039"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1d0ac90f-8e4c-42ba-b906-42fe263bbc5c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b7ed2552-af45-48c3-93bc-0baf537794fe">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fcb42d95-1639-408f-8467-aaac25e1f8a1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a059d98a-7545-471e-9bac-60b12ee7355f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f26e69c6-a3f0-42f9-9d46-83061b37a272">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d0331ae6-314b-4de2-adf8-a04d15e63113"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6f9f9c59-e031-40b8-9d1d-7981bf7b2ce5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7edf7b63-c63f-4a84-a875-7439d798792d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="32712dd7-4ed4-47bf-a0ed-b02add447cd8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2c4d637c-0be8-40a2-add9-a1cda538ab04"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b70dbc00-10ac-4b55-a4bf-801506da55c1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5506a75b-6558-4eda-98f0-56d5247ff140"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="431440fe-8b51-477f-ab5d-543a4c79db68"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9e0270ef-8894-4103-a090-ecd8133d5612">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="60b3c90f-c465-4658-92a3-5090900dd023"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1533b6e3-61a7-4ab2-9aed-0738282a8905"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0745f9a1-fc5a-4574-bb1f-2614281ce87c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1fe5295d-a36a-42e6-b0f5-f70cb2de98b3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3f6164ac-7892-4f41-b39e-12f1cdb375bf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b6ea2604-8f2a-4e78-bc75-d892f23e7f92">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>16777215.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1932a18b-ed4c-4a4f-b137-646061643cc1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="81763fc7-eb9a-41b7-ba6f-b2728843df0b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0da8d4ba-e82a-49f5-be72-489cda1078ed">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7b248949-d2a3-4766-abf6-9768ed547eb4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="df3c5cd6-b501-4a6c-94b1-61711c16626c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cc0e5ff3-01f3-4bc2-b04b-f567c679001c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4118ca9d-9d75-4f4c-b326-17d9fdce823a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9257e3e3-8f1b-42a4-a3df-1831e5e22e0d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="647e60eb-7f82-459b-9b32-fd5f01c3706a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d2b6ca13-82b1-40fe-a1b8-402a6b10fc86"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2966b463-1387-4769-996f-eeea98c7f429"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f27f8824-ec9f-447d-b8fb-b450f6c75d84">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="367e152c-d3b8-4d50-a58d-5824a1f14031"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2545c109-432d-47de-86b7-4dda1774c6ec"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f32313d0-d174-40e3-a238-ad65e37ac813">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8acb30d7-2d40-4687-9391-47a49bdfd8f5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c32f5d76-c95b-4876-92e9-3931022f7ade"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="44aad4c2-4294-4fbc-a661-19cac1d36d27">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dae932aa-74f3-4493-a536-75c4f9dcadc2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fbfe905f-290f-40fb-85e2-8fb90b7db932"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="89ded87b-ceb6-4044-9b0a-cc36837fe62d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="412aee64-596d-478b-a000-9a70fc9adfe8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f8305bb2-a621-43c0-a79a-d747be06ce6b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5a41bb49-49d1-4b14-b818-c5e3f33a5858">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f559d8f7-5863-4f86-8b9e-680f294a12e7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4bc12e42-9b34-4e9d-8697-085e7f2c5eee"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0fe03de6-2cbf-424f-a719-39309cbbe2c1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d2d1e31d-e799-48a3-af4d-25c64604f8c6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a85abd0b-d5e3-4502-94ec-cbd503578678"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="31b5d1e5-f755-45bf-ba26-7d321af615e8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5a1b0998-7df1-45a2-ae3b-62596b974d75"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="34ec8a1c-96e9-4a8e-9acb-4b786b409e2f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="665cc56c-6d57-4dfa-967f-3aca08191997">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ec8b13d2-0cb9-46f3-8937-8ec93cddda0a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b759d4aa-377e-4b4e-90da-040be89a4f46"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="52731882-847b-4869-a100-3d89ccf04fa4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="03f64f68-e2e8-4c7f-88ab-cad816e2ccdb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="02d7db7d-c362-4041-8b19-9ef9eb7ef70f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a8980b28-0572-46eb-a4a9-5b1672af6d28">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="de5cd090-35f5-4f02-a9c8-b5714510cc1f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f7f3b7bc-3b8f-4d05-b226-fe096a09f4d5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6f086d56-e9a1-4b44-bc2e-a2897c3f6bdd">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d7d0750b-db0e-44bb-97a0-44324f0ec2dc"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9062a08f-9d84-45d2-ae8e-395449a4703b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="aa38385a-625d-4d48-9598-8d8588fd3572">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ed445be5-7970-42d4-ba21-25b01862c760"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5e3f0d5d-60ad-4c94-be06-840c54d4a583"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7e07f093-3405-4222-85ee-9ec94dfb9139">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9c40c80b-b657-4da5-b1c2-72dd1c623c6a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e4724cb6-7a40-4535-8d78-f836b5a609e7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dd94a764-dc4b-4049-9a6e-6b3048c4f01b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e91f5ace-1f07-458e-bfe5-6803cd2464dc"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="595f14af-dfd1-49bf-b785-72124769faad"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="49e53f66-4a43-475c-a4e1-2c186e815117">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b2c159dc-cf13-4496-a2bc-6706d77539c1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c52cf79e-9a1b-435c-874c-2a1e2f11fa95"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f87f232a-0cc4-4328-a1f1-e040b3b3c0bc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0a56b913-c170-4fbc-b79f-3f84f4d8968a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e9b6b694-ccaa-41c7-8fe9-514d6bcb2ae8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="82a7ba8d-d07c-4299-9944-bb58a3089965">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ed306261-b5cd-40da-9331-b3e28394401a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fff28f2d-a71d-4ac2-89c3-db7d752f3483"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7cff17c0-732d-406d-b228-67bc3157b8c5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3d447e81-509a-4e06-bdcb-f38b77a7453f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="78155309-35e6-4b28-b438-63a3dc4791f5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="63b024b3-9043-4b13-ac10-dcc644efa7ce">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f681ea33-228a-4c8c-b1bc-5b9e577f6456"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="541e0ffc-ba8d-4f03-b696-44ec8c2527db"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="491d8b58-847f-4d9c-9c2b-af0d43298094">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="37d0bbe9-2994-4189-836e-b86462f33948"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="36e57cd8-32ad-4200-830d-45f42a3ac725"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3e86e7f1-2319-4c42-921b-3766cb9d0b77">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dc7478cf-9f26-4955-a8c4-7eab48280e0d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a148d67b-0fce-45dd-a2da-2caca55956e8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="53569b82-dccd-4e32-bf79-0a776768ed73">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="87c6a8e9-bf81-46b9-9d64-3ec9682b47da"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9b2a7e7e-ed74-4507-a232-0d36d8cace28"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3f8045a8-6b1c-44c6-84dd-328fcc9e1ddb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c730e534-7420-44cc-aa6d-d618aaf37b5a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1b064fc6-9221-47bd-bf32-d2b4a42ac243"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="342198e5-e205-4889-88d0-eace07d52c34">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="921f0288-98cb-46c5-bffa-3f65162ddca0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e85b512c-24b8-40f6-82f6-ebd401e887a0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="03715c33-7d15-4ecd-961c-919885c8038b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="197f9c9a-35c6-4923-8987-4311bc84573a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dc05110c-d432-4a58-9f5a-0928221cb81e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6559e3c2-6c32-46cf-aef3-05ad6e6b4f34">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e743d9ec-bd72-44d7-ab6c-53c40cd9879a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a1e85390-af37-449e-ab3d-704f9ff665fe"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="53662129-7ec7-4e67-a61a-9dae02b81b13">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f314c70b-c9a2-4b58-a1b8-2e32938add95"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ae8d1119-fe9b-4ce4-8c0c-0011f85523c5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="dcdd64ca-8deb-45af-82b6-ec09c2d36651">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b100446e-4f33-456f-8a1f-76aaf5b7c583"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="25412b66-aabd-4def-8417-badd4c9a6ba4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="58fc5116-e39e-47dd-b1e8-e74b92be82a4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1fb080e6-6688-4a96-b8b3-cd2736490f61"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9a14c512-2cc1-43d9-8ad4-eda713a96454"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c0a26e82-626b-4383-922d-d06129edf288">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1dd00e65-b631-488d-8537-c7f46a06a01e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a007fbe8-789c-4a3f-9e42-e0a1a4475c55"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bdfb9f35-da8d-49a7-964c-518139dc4a53">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="05d47539-42fd-42cb-a92d-5e2c555a6340"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="126f323b-7632-475e-b42d-72aaba4fe5b0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9f167733-5915-4677-9e0f-3b992cbe8526">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="683b7a6c-1fb7-438b-a40c-8573c19f1aa6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e177299b-70a4-4b28-952a-1bef5a66e89d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1063e500-a8ab-47a1-b0e0-af647249f269">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dc5290c1-2d00-4e88-bdec-dc10202d6116"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c7514649-0587-486d-9abc-40809d51957d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="27b9cf86-8087-4059-9b71-707b57c0d3fb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8f922495-4383-444c-a9d3-16f90e0a91e1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a71e1b79-17af-42a3-a09c-45694bfcff1a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cd670a1d-3de0-4ea0-b3ae-40335c97169d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8e8452c1-eb43-4e0f-96ed-6a7fca685e6e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="11217f60-a941-449b-ae9a-631f073e8326"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="871c0c67-02d2-439b-aa65-631ae04b8690">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d8280b30-d278-4d08-9c3a-fa9a54abafc5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="46dcd912-39c2-4c96-8c75-299f90df3f67"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fa4fe092-1dc7-4924-b4c7-5dba7c1d3ef1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fd75e862-8367-40db-8cdb-79125b7a7ce6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="67cc5121-d3c9-4df6-a787-d943257b95be"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1b546936-f945-44c5-aef1-cf1a51117b57">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9af09c1d-9cb7-40e3-9b4a-abb5c0178707"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4ffad39c-37b2-403f-bd6b-452f5a5085d5"/>
    </inports>
    <isAperiodicRootFcnCallSystem>true</isAperiodicRootFcnCallSystem>
    <isBdInSimModeForSimCodegenVariants>false</isBdInSimModeForSimCodegenVariants>
    <isInlineParamsOn>true</isInlineParamsOn>
    <isNumAllowedInstancesOne>true</isNumAllowedInstancesOne>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigOutportVirtualBus>false</isOrigOutportVirtualBus>
    <isPreCompSingleRate>false</isPreCompSingleRate>
    <isRefModelConstant>false</isRefModelConstant>
    <isRootFcnCallPortGroupsEmpty>true</isRootFcnCallPortGroupsEmpty>
    <loggingSaveFormat>2</loggingSaveFormat>
    <maxFreqHz>-1.0</maxFreqHz>
    <nonTunableVariables>rtw_collapsed_sub_expr_15</nonTunableVariables>
    <nonTunableVariables>rtw_collapsed_sub_expr_17</nonTunableVariables>
    <nonTunableVariables>rtw_collapsed_sub_expr_24</nonTunableVariables>
    <nonTunableVariables>rtw_collapsed_sub_expr_26</nonTunableVariables>
    <numDStateRecs>7</numDStateRecs>
    <numDataInputPorts>6</numDataInputPorts>
    <numLoggableDStateRecs>6</numLoggableDStateRecs>
    <numLoggableJacobianDStates>0</numLoggableJacobianDStates>
    <numModelWideEventTs>0</numModelWideEventTs>
    <numPortlessSimulinkFunctionPortGroups>0</numPortlessSimulinkFunctionPortGroups>
    <numRuntimeExportedRates>1</numRuntimeExportedRates>
    <numTs>1</numTs>
    <origInportBusType>SWC_HwAbsLyrBus</origInportBusType>
    <origInportBusType>SWC_SigMonLyrBus</origInportBusType>
    <origInportBusType>SWC_DiagLyrBus</origInportBusType>
    <origInportBusType>SWC_DataHndlLyrBus</origInportBusType>
    <origInportBusType>PnlOpBus</origInportBusType>
    <origInportBusType>CtrlLogBus</origInportBusType>
    <origOutportBusOutputAsStruct>true</origOutportBusOutputAsStruct>
    <origOutportBusType>MtrCtrlBus</origOutportBusType>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="5b9b5c3b-588a-40c5-91f8-1b0f946f9c60">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="5572b2e3-c576-42c9-97a2-8420c7deab80"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8eabcead-e5a9-4ffc-ac69-09522dcd1703"/>
    </outports>
    <preCompAllowConstTsOnPorts>false</preCompAllowConstTsOnPorts>
    <preCompAllowPortBasedInTriggeredSS>true</preCompAllowPortBasedInTriggeredSS>
    <removeResetFunc>true</removeResetFunc>
    <runtimeNonFcnCallRateInfos type="ModelRefInfoRepo.RateInfo">
      <compiled>true</compiled>
      <isEmpty>true</isEmpty>
      <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
      <period>.2</period>
      <priority>40</priority>
      <rateIdx>0</rateIdx>
    </runtimeNonFcnCallRateInfos>
    <sampleTimeInheritanceRule>1</sampleTimeInheritanceRule>
    <solverStatusFlags>331</solverStatusFlags>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="a8bbb98f-4f2a-4191-8b3d-40d94e77553f">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="34e1d0e1-c11f-4c1a-a7db-f69b12aa1160">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>MtrCtrl_MtrDir_t</enumName>
      <labels>IDLE_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <labels>SECURE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="05d38239-ad98-4323-b09c-dfbb6916c83f">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>MtrCtrl_MtrPwrd_t</enumName>
      <labels>IDLE_C</labels>
      <labels>PWRD_C</labels>
      <labels>DEPWRD_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="66e099cd-95f3-4dc5-848c-4e1a6d571630">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>PWMCtrl_MtrDir_t</enumName>
      <labels>IDLE_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <labels>SECURE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="07089747-bb96-4f12-bc0f-3063af533017">
      <defaultValue>DirNone_C</defaultValue>
      <enumName>HallDeco_Dir_t</enumName>
      <labels>DirNone_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="eb264815-d95f-486b-9383-628605c55a78">
      <defaultValue>Init_C</defaultValue>
      <enumName>VoltMon_VoltClass_t</enumName>
      <labels>Init_C</labels>
      <labels>VoltClassA_C</labels>
      <labels>VoltClassB_C</labels>
      <labels>VoltClassC_C</labels>
      <labels>VoltClassD_C</labels>
      <labels>VoltClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="92b75824-e5cc-4ca0-b4d6-0b70b0090761">
      <defaultValue>AreaSlide_C</defaultValue>
      <enumName>PosMon_Area_t</enumName>
      <labels>AreaSlide_C</labels>
      <labels>AreaVent_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="8d804e70-456a-4136-a4b6-a25644831757">
      <defaultValue>PnlZero</defaultValue>
      <enumName>PosMon_PnlPosArea_t</enumName>
      <labels>PnlZero</labels>
      <labels>PnlFlushClose</labels>
      <labels>PnlVentArea</labels>
      <labels>PnlVentOpen</labels>
      <labels>PnlComfOpnArea</labels>
      <labels>PnlComfStop</labels>
      <labels>PnlSlideOpnArea</labels>
      <labels>PnlFullOpen</labels>
      <labels>PnlOpenHS</labels>
      <labels>PnlOutOfRng</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="5c67e377-c435-4dab-a6ab-f4aa14ff6fb1">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>SysFltDiag_LastStopReason_t</enumName>
      <labels>NoFault_C</labels>
      <labels>Debugger_C</labels>
      <labels>DiagReq_C</labels>
      <labels>PanicStp_C</labels>
      <labels>ThermPrtMtr_C</labels>
      <labels>ThermPrtMosfet_C</labels>
      <labels>RelaxMechComplt_C</labels>
      <labels>AtsRevComplt_C</labels>
      <labels>MovTimeout_C</labels>
      <labels>ClassEOV_C</labels>
      <labels>ClassEUV_C</labels>
      <labels>BattVltNonPlsbl_C</labels>
      <labels>AmbTmpNonPlsbl_C</labels>
      <labels>MosfetTempNonPlsbl_C</labels>
      <labels>SysIC_CommFlt_C</labels>
      <labels>LINCommFlt_C</labels>
      <labels>SysICFailSafe_C</labels>
      <labels>ChargePumpFlt_C</labels>
      <labels>HSSuppFlt_C</labels>
      <labels>VsIntFlt_C</labels>
      <labels>VsSuppFlt_C</labels>
      <labels>VCC1Flt_C</labels>
      <labels>RPInvalidFlt_C</labels>
      <labels>HallFlt_C</labels>
      <labels>SysICThermShdFlt_C</labels>
      <labels>MtrCtrlFlt_C</labels>
      <labels>HallSuppFlt_C</labels>
      <labels>UnderVotlage_C</labels>
      <labels>OverVoltage_C</labels>
      <labels>StallDurAtsRev_RlxMech_C</labels>
      <labels>OutsideEnvCond_C</labels>
      <labels>TargetPosRchd_C</labels>
      <labels>PrevReason_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>16</values>
      <values>17</values>
      <values>18</values>
      <values>19</values>
      <values>20</values>
      <values>21</values>
      <values>22</values>
      <values>23</values>
      <values>24</values>
      <values>25</values>
      <values>26</values>
      <values>27</values>
      <values>28</values>
      <values>29</values>
      <values>30</values>
      <values>31</values>
      <values>35</values>
      <values>37</values>
      <values>48</values>
      <values>49</values>
      <values>50</values>
      <values>51</values>
      <values>52</values>
      <values>255</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="477ad1cd-0c35-4487-8f0e-f11875a36e5e">
      <defaultValue>Idle_C</defaultValue>
      <enumName>LearnAdap_Mode_t</enumName>
      <labels>Idle_C</labels>
      <labels>LearningPos_C</labels>
      <labels>AdaptionOpen_C</labels>
      <labels>AdaptionClose_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="27a8a1d3-43ca-40bb-a10e-595bba50b977">
      <defaultValue>NoMov_C</defaultValue>
      <enumName>BlockDet_Stall_dir</enumName>
      <labels>NoMov_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>reserved</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="66bb8735-1a15-4f9f-8a35-fe210a7d6351">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MtrTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MtrTempClassA_C</labels>
      <labels>MtrTempClassB_C</labels>
      <labels>MtrTempClassC_C</labels>
      <labels>MtrTempClassD_C</labels>
      <labels>MtrTempClassE_C</labels>
      <labels>MtrTempClassF_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="f3a4b253-3fb9-4933-a96c-db4508540d63">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MosfetTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MosfetTempClassA_C</labels>
      <labels>MosfetTempClassB_C</labels>
      <labels>MosfetTempClassC_C</labels>
      <labels>MosfetTempClassD_C</labels>
      <labels>MosfetTempClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="c3bc7f1a-25cb-458f-8c8b-4cea05cfa3e5">
      <defaultValue>ATSDeactivated_C</defaultValue>
      <enumName>PnlOp_ATS_t</enumName>
      <labels>ATSDeactivated_C</labels>
      <labels>ATSAvailable_C</labels>
      <labels>ATSActive_C</labels>
      <labels>ATSOverrideStage1_C</labels>
      <labels>ATSOverrideStage2_C</labels>
      <labels>ATSDisabled_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="95f833ed-3555-440a-9a03-1f23b404a17b">
      <defaultValue>Idle_C</defaultValue>
      <enumName>CtrlLog_RelearnMode_t</enumName>
      <labels>Idle_C</labels>
      <labels>ReqLearn_C</labels>
      <labels>LearningPos_C</labels>
      <labels>LearningRF_C</labels>
      <labels>Complete_C</labels>
      <labels>Interrupted_C</labels>
      <labels>Renorm_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="127a8fb7-d2d4-4675-ab28-23ea7c7c2db2">
      <defaultValue>Init_C</defaultValue>
      <enumName>StateMach_Mode_t</enumName>
      <labels>Init_C</labels>
      <labels>NvMInit_C</labels>
      <labels>Startup_C</labels>
      <labels>FullRun_C</labels>
      <labels>Standby_C</labels>
      <labels>Shtdwn_C</labels>
      <labels>FastShtdwn_C</labels>
      <labels>SystemReset_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>7</values>
      <values>8</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="cfc5662d-b81a-46e5-8d2d-78fc64b3517a">
      <defaultValue>IRS_E_OK</defaultValue>
      <enumName>stdReturn_t</enumName>
      <labels>IRS_E_OK</labels>
      <labels>IRS_E_NOK</labels>
      <labels>IRS_E_PENDING</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="e142e878-892e-4e9f-9796-b1e6daad0203">
      <defaultValue>Disabled_C</defaultValue>
      <enumName>CfgParam_TestMode_t</enumName>
      <labels>Disabled_C</labels>
      <labels>All_CW_C</labels>
      <labels>Sequential_C</labels>
      <labels>All_Stop_C</labels>
      <labels>All_CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="16a209df-27c0-4d2e-8b98-6bebe79a2db8">
      <defaultValue>CmdNone_C</defaultValue>
      <enumName>PnlOp_MtrCmd_t</enumName>
      <labels>CmdNone_C</labels>
      <labels>CmdInc_C</labels>
      <labels>CmdDec_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="eacba781-944d-4783-93bc-001f9c5ce452">
      <defaultValue>RoofIdle_C</defaultValue>
      <enumName>PnlOp_Mode_t</enumName>
      <labels>RoofIdle_C</labels>
      <labels>Moving_C</labels>
      <labels>Intr_C</labels>
      <labels>ReachTarPos_C</labels>
      <labels>ReachStallPos_C</labels>
      <labels>Automatic_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="39ca324c-8e66-418a-aada-69da7ff35bdc">
      <defaultValue>RevIdle_C</defaultValue>
      <enumName>PnlOp_Rev_t</enumName>
      <labels>RevIdle_C</labels>
      <labels>RevInProcATS_C</labels>
      <labels>RevInProcStall_C</labels>
      <labels>RevComplATS_C</labels>
      <labels>RevComplStall_C</labels>
      <labels>RevInhibted_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="c7a7d202-421a-4ae5-ac3d-a390a32fd6a0">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_MovType_t</enumName>
      <labels>None_C</labels>
      <labels>Manual_C</labels>
      <labels>Automatic_C</labels>
      <labels>Learning_C</labels>
      <labels>AtsReversal_C</labels>
      <labels>RelaxOfMech_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="5ad62199-09c0-4ac4-b690-d2836837798c">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoSyncParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>ManMov_C</labels>
      <labels>ManCls_C</labels>
      <labels>StepMov_C</labels>
      <labels>StepMovCls_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="dc8f5ded-c097-4b8b-9303-18db589dc8ca">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoAppParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>StepMovCloseReq_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <timingAndTaskingRegistry>&lt;?xml version=&quot;1.0&quot;?&gt;
&lt;slexec_sto version=&quot;1.1&quot; packageUris=&quot;http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112&quot;&gt;
  &lt;sto.Registry type=&quot;sto.Registry&quot; uuid=&quot;ecf79c5a-9e00-42dc-afb1-e9e9da95930c&quot;&gt;
    &lt;executionSpec&gt;Undetermined&lt;/executionSpec&gt;
    &lt;clockRegistry type=&quot;sto.ClockRegistry&quot; uuid=&quot;518dd90d-f6f6-4742-9467-d7eb93856775&quot;&gt;
      &lt;clocks type=&quot;sto.Timer&quot; uuid=&quot;ac7c49c5-4658-4da7-9e08-9a5e702e7872&quot;&gt;
        &lt;clockTickConstraint&gt;PeriodicWithFixedResolution&lt;/clockTickConstraint&gt;
        &lt;computedFundamentalDiscretePeriod&gt;.2&lt;/computedFundamentalDiscretePeriod&gt;
        &lt;resolution&gt;.2&lt;/resolution&gt;
        &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;603a9188-2f96-4cfe-aec3-b27923e38a0d&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;bf03a0ed-d457-4e5a-821b-58b33d40dc6d&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;baseRate type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;53e691c0-96ca-4128-9881-8e654dfb9b0e&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;47cb81ab-a1d9-44d3-bf64-9cec9e08d12e&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/baseRate&gt;
      &lt;/clocks&gt;
      &lt;clocks type=&quot;sto.Event&quot; uuid=&quot;3142b1d4-daf0-4ec2-af04-7dd9b3b35758&quot;&gt;
        &lt;eventType&gt;PARAMETER_CHANGE_EVENT&lt;/eventType&gt;
        &lt;cNum&gt;1&lt;/cNum&gt;
        &lt;clockType&gt;Event&lt;/clockType&gt;
        &lt;identifier&gt;ParameterChangeEvent&lt;/identifier&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;f66587c6-fca9-4428-b0e7-1ace9d691a34&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;53c0bf47-c60f-4806-8fe3-c2b8479d2d60&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
      &lt;/clocks&gt;
      &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
    &lt;/clockRegistry&gt;
    &lt;taskRegistry type=&quot;sto.TaskRegistry&quot; uuid=&quot;3b8e94a8-67d3-4a8a-982d-61131db3da23&quot;&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;6488b622-a823-4972-b473-dd8c9ae14962&quot;&gt;
        &lt;isExplicit&gt;true&lt;/isExplicit&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;f8a04bdc-18e4-4812-827d-40dbd01d7d73&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;a277a85d-1ddd-4c41-ae23-b80fa332b101&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;schedulingClockId&gt;ParameterChangeEvent&lt;/schedulingClockId&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;ModelWideParameterChangeEvent&lt;/identifier&gt;
        &lt;priority&gt;-1&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;a9ce21c2-5c6b-46db-beaa-c0d85f825d65&quot;&gt;
        &lt;isExecutable&gt;true&lt;/isExecutable&gt;
        &lt;orderIndex&gt;1&lt;/orderIndex&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;23d1417b-5542-4929-b0ec-0653d68f0ae8&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;cbb816e0-8d91-4f8f-8ea6-3c3a6bc02ba5&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;_task0&lt;/identifier&gt;
        &lt;priority&gt;40&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;fd5ce34c-**************-b7fa81d1a1cb&quot;&gt;
        &lt;taskIdentifier&gt;_task0&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;3265418c-af0f-4c29-8c93-c4b4c10088fc&quot;&gt;
        &lt;clockIdentifier&gt;ParameterChangeEvent&lt;/clockIdentifier&gt;
        &lt;taskIdentifier&gt;ModelWideParameterChangeEvent&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskPriorityDirection&gt;HighNumberLast&lt;/taskPriorityDirection&gt;
      &lt;taskingMode&gt;ClassicMultiTasking&lt;/taskingMode&gt;
    &lt;/taskRegistry&gt;
  &lt;/sto.Registry&gt;
&lt;/slexec_sto&gt;</timingAndTaskingRegistry>
    <triggerTsType>triggered</triggerTsType>
    <triggerType>3</triggerType>
    <usePortBasedSampleTime>true</usePortBasedSampleTime>
    <zeroInternalMemoryAtStartupUnchecked>true</zeroInternalMemoryAtStartupUnchecked>
    <FMUBlockMap type="ModelRefInfoRepo.FMUBlockInfo" uuid="ee4f4352-f470-49da-b3d1-0f7e36e736b1"/>
    <codeGenInfo type="ModelRefInfoRepo.CodeGenInformation" uuid="189ccb68-2645-40de-ae1a-e304e37c1a2d">
      <DWorkTypeName>MdlrefDW_MtrCtrl_Mdl_T</DWorkTypeName>
    </codeGenInfo>
    <compiledVariantInfos type="ModelRefInfoRepo.CompiledVariantInfoMap" uuid="6f6e6296-aa28-4d32-bb88-167bda145ca0"/>
    <configSettingsForConsistencyChecks type="ModelRefInfoRepo.ConfigSettingsForConsistencyChecks" uuid="c48b8033-839d-42de-9fd1-894a6d982003">
      <consistentOutportInitialization>true</consistentOutportInitialization>
      <fixedStepSize>.2</fixedStepSize>
      <frameDiagnosticSetting>2</frameDiagnosticSetting>
      <hasHybridSampleTime>true</hasHybridSampleTime>
      <optimizedInitCode>true</optimizedInitCode>
      <signalLoggingSaveFormat>2</signalLoggingSaveFormat>
      <simSIMDOptimization>1</simSIMDOptimization>
      <solverName>FixedStepDiscrete</solverName>
      <solverType>SOLVER_TYPE_FIXEDSTEP</solverType>
      <hardwareSettings type="ModelRefInfoRepo.HardwareSettings" uuid="8372103b-8cea-4447-a4a1-85b139f21358">
        <prodBitPerChar>8</prodBitPerChar>
        <prodBitPerDouble>64</prodBitPerDouble>
        <prodBitPerFloat>32</prodBitPerFloat>
        <prodBitPerInt>32</prodBitPerInt>
        <prodBitPerLong>32</prodBitPerLong>
        <prodBitPerLongLong>64</prodBitPerLongLong>
        <prodBitPerPointer>32</prodBitPerPointer>
        <prodBitPerPtrDiffT>32</prodBitPerPtrDiffT>
        <prodBitPerShort>16</prodBitPerShort>
        <prodBitPerSizeT>32</prodBitPerSizeT>
        <prodEndianess>1</prodEndianess>
        <prodLargestAtomicFloat>1</prodLargestAtomicFloat>
        <prodLargestAtomicInteger>3</prodLargestAtomicInteger>
        <prodShiftRight>true</prodShiftRight>
        <prodWordSize>32</prodWordSize>
      </hardwareSettings>
    </configSettingsForConsistencyChecks>
    <controllableInputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="ce45442e-a7de-4fdd-a630-577a4d59c2c3"/>
    <controllableOutputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="b4618cad-514e-4f8b-a8ec-998d6eda2033"/>
    <dataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="3d08254f-34f9-4dfe-adbc-9a647f162fbf">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>113</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>140</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>142</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
      <compDataInputPorts>144</compDataInputPorts>
      <compDataInputPorts>145</compDataInputPorts>
      <compDataInputPorts>146</compDataInputPorts>
      <compDataInputPorts>147</compDataInputPorts>
      <compDataInputPorts>148</compDataInputPorts>
      <compDataInputPorts>149</compDataInputPorts>
      <compDataInputPorts>150</compDataInputPorts>
      <compDataInputPorts>151</compDataInputPorts>
      <compDataInputPorts>152</compDataInputPorts>
      <compDataInputPorts>153</compDataInputPorts>
      <compDataInputPorts>154</compDataInputPorts>
      <compDataInputPorts>155</compDataInputPorts>
      <compDataInputPorts>156</compDataInputPorts>
      <compDataInputPorts>157</compDataInputPorts>
      <compDataInputPorts>158</compDataInputPorts>
      <compDataInputPorts>159</compDataInputPorts>
      <compDataInputPorts>160</compDataInputPorts>
      <compDataInputPorts>161</compDataInputPorts>
      <compDataInputPorts>162</compDataInputPorts>
      <compDataInputPorts>163</compDataInputPorts>
      <compDataInputPorts>164</compDataInputPorts>
      <compDataInputPorts>165</compDataInputPorts>
      <compDataInputPorts>166</compDataInputPorts>
      <compDataInputPorts>167</compDataInputPorts>
      <compDataInputPorts>168</compDataInputPorts>
      <compDataInputPorts>169</compDataInputPorts>
      <compDataInputPorts>170</compDataInputPorts>
      <compDataInputPorts>171</compDataInputPorts>
      <compDataInputPorts>172</compDataInputPorts>
      <compDataInputPorts>173</compDataInputPorts>
      <compDataInputPorts>174</compDataInputPorts>
      <compDataInputPorts>175</compDataInputPorts>
      <compDataInputPorts>176</compDataInputPorts>
      <compDataInputPorts>177</compDataInputPorts>
      <compDataInputPorts>178</compDataInputPorts>
      <compDataInputPorts>179</compDataInputPorts>
      <compDataInputPorts>180</compDataInputPorts>
      <compDataInputPorts>181</compDataInputPorts>
      <compDataInputPorts>182</compDataInputPorts>
      <compDataInputPorts>183</compDataInputPorts>
      <compDataInputPorts>184</compDataInputPorts>
      <compDataInputPorts>185</compDataInputPorts>
      <compDataInputPorts>186</compDataInputPorts>
      <compDataInputPorts>187</compDataInputPorts>
      <compDataOutputPorts>0</compDataOutputPorts>
      <dataInputPorts>0</dataInputPorts>
      <dataInputPorts>1</dataInputPorts>
      <dataInputPorts>2</dataInputPorts>
      <dataInputPorts>3</dataInputPorts>
      <dataInputPorts>4</dataInputPorts>
      <dataInputPorts>5</dataInputPorts>
      <dataOutputPorts>0</dataOutputPorts>
    </dataPortGroup>
    <expFcnUnconnectedDataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="11ea38ef-5db5-4d06-8350-63ac1c93ea63">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>113</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>140</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>142</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
      <compDataInputPorts>144</compDataInputPorts>
      <compDataInputPorts>145</compDataInputPorts>
      <compDataInputPorts>146</compDataInputPorts>
      <compDataInputPorts>147</compDataInputPorts>
      <compDataInputPorts>148</compDataInputPorts>
      <compDataInputPorts>149</compDataInputPorts>
      <compDataInputPorts>150</compDataInputPorts>
      <compDataInputPorts>151</compDataInputPorts>
      <compDataInputPorts>152</compDataInputPorts>
      <compDataInputPorts>153</compDataInputPorts>
      <compDataInputPorts>154</compDataInputPorts>
      <compDataInputPorts>155</compDataInputPorts>
      <compDataInputPorts>156</compDataInputPorts>
      <compDataInputPorts>157</compDataInputPorts>
      <compDataInputPorts>158</compDataInputPorts>
      <compDataInputPorts>159</compDataInputPorts>
      <compDataInputPorts>160</compDataInputPorts>
      <compDataInputPorts>161</compDataInputPorts>
      <compDataInputPorts>162</compDataInputPorts>
      <compDataInputPorts>163</compDataInputPorts>
      <compDataInputPorts>164</compDataInputPorts>
      <compDataInputPorts>165</compDataInputPorts>
      <compDataInputPorts>166</compDataInputPorts>
      <compDataInputPorts>167</compDataInputPorts>
      <compDataInputPorts>168</compDataInputPorts>
      <compDataInputPorts>169</compDataInputPorts>
      <compDataInputPorts>170</compDataInputPorts>
      <compDataInputPorts>171</compDataInputPorts>
      <compDataInputPorts>172</compDataInputPorts>
      <compDataInputPorts>173</compDataInputPorts>
      <compDataInputPorts>174</compDataInputPorts>
      <compDataInputPorts>175</compDataInputPorts>
      <compDataInputPorts>176</compDataInputPorts>
      <compDataInputPorts>177</compDataInputPorts>
      <compDataInputPorts>178</compDataInputPorts>
      <compDataInputPorts>180</compDataInputPorts>
      <compDataInputPorts>181</compDataInputPorts>
      <compDataInputPorts>182</compDataInputPorts>
      <compDataInputPorts>183</compDataInputPorts>
      <compDataInputPorts>184</compDataInputPorts>
      <compDataInputPorts>185</compDataInputPorts>
      <compDataInputPorts>186</compDataInputPorts>
    </expFcnUnconnectedDataPortGroup>
    <interfaceParameterInfo type="ModelRefInfoRepo.InterfaceParameterInfo" uuid="5024bd1b-6d18-483b-8756-d613fc9c1550">
      <globalVariables type="ModelRefInfoRepo.BuiltinParameter" uuid="4986fa3a-e977-4e71-ae73-c220b10f1c5a">
        <datatypeID>5</datatypeID>
        <dimensions>1</dimensions>
        <dimensions>1</dimensions>
        <isUsed>true</isUsed>
        <netSVCE>true</netSVCE>
        <numDimensions>2</numDimensions>
        <parameterName>RoofSys_tSmpMtrCtrl_SC</parameterName>
      </globalVariables>
      <globalVariables type="ModelRefInfoRepo.BuiltinParameter" uuid="22236c7f-6b63-4939-b8a6-f551e7986f0a">
        <datatypeID>3</datatypeID>
        <dimensions>1</dimensions>
        <dimensions>1</dimensions>
        <isUsed>true</isUsed>
        <netSVCE>true</netSVCE>
        <numDimensions>2</numDimensions>
        <parameterName>DTC_stDTC_Failed_C</parameterName>
      </globalVariables>
      <globalVariables type="ModelRefInfoRepo.BuiltinParameter" uuid="2a82111e-7bb1-4cdb-99b1-402ba456aaa7">
        <datatypeID>3</datatypeID>
        <dimensions>1</dimensions>
        <dimensions>1</dimensions>
        <isUsed>true</isUsed>
        <netSVCE>true</netSVCE>
        <numDimensions>2</numDimensions>
        <parameterName>DTC_stDTC_NotRun_C</parameterName>
      </globalVariables>
      <globalVariables type="ModelRefInfoRepo.BuiltinParameter" uuid="3fa8271b-f62f-4e0c-a987-90084eae4ff9">
        <datatypeID>3</datatypeID>
        <dimensions>1</dimensions>
        <dimensions>1</dimensions>
        <isUsed>true</isUsed>
        <netSVCE>true</netSVCE>
        <numDimensions>2</numDimensions>
        <parameterName>DTC_stDTC_Passed_C</parameterName>
      </globalVariables>
    </interfaceParameterInfo>
    <messageInfo type="ModelRefInfoRepo.MessageInformation" uuid="e9ca88cc-32b1-4661-a208-771f9543af79">
      <rootInportInfo type="ModelRefInfoRepo.MessageRootPortInfo" uuid="0812cc3e-1d97-4c83-85ee-addb1d3b0d51">
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="aad55887-4808-40e6-9a77-547b67bd2ed0">
          <portIdx>1</portIdx>
        </GrHierarchyMsgModeMap>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="c9b2ebe7-4d9f-48ec-826c-7c47b0ddcfce">
          <portIdx>2</portIdx>
        </GrHierarchyMsgModeMap>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="e21aa543-9efe-46d1-acfd-539fb225353d">
          <portIdx>3</portIdx>
        </GrHierarchyMsgModeMap>
      </rootInportInfo>
    </messageInfo>
    <methodInfo type="ModelRefInfoRepo.MethodExistenceInfo" uuid="f9dd6cd6-7493-4248-a91f-8364c9be3981">
      <hasSystemInitializeMethod>true</hasSystemInitializeMethod>
      <hasSystemResetMethod>true</hasSystemResetMethod>
      <hasTerminateMethod>true</hasTerminateMethod>
      <hasUpdateMethod>true</hasUpdateMethod>
    </methodInfo>
    <periodicEventPortUnsupportedBlockInfo type="ModelRefInfoRepo.PeriodicEventPortUnsupportedBlockInfo" uuid="b22f3796-99e1-4d2b-811d-a10c95026495"/>
    <portGroupsRequireSameRate type="ModelRefInfoRepo.PortGroupsRequireSameRate" uuid="4ddcf10e-c65d-4faf-9ecd-6a91baa9a478">
      <DSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="4da6de68-b254-4af9-9bd9-afcf8ee69d2c"/>
      <GlobalDSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="d839243e-8a74-4d77-a2a7-77cb2d027578"/>
      <mergedPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="369b4036-295d-4a84-a623-0788c3e39536"/>
    </portGroupsRequireSameRate>
    <rateBasedMdlGlobalDSMRateSpec type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="d6aab4ee-af04-4622-af28-dcf698756890">
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="6935f8e2-af20-41c1-8e49-4a0ec6a4d80a">
        <dSMName>CfgParam_CAL</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="50e90e7c-71fe-4102-ad0f-548de5a9c3be">
          <blockName>MtrCtrl_Mdl/Data Store Read1</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="a7cffe4c-ebd4-4ca7-9437-e026ceb02ed1">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
    </rateBasedMdlGlobalDSMRateSpec>
    <rateSpecOfGlobalDSMAccessedByDescExpFcnMdlMap type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="5867dc54-836d-4658-9f4f-a297ff72bd16"/>
    <rootBlockDiagramInterface type="ci.Model" uuid="b7ba8b95-e892-4823-9e8d-d5847a8125a7">
      <p_RootComponentInterface type="ci.ComponentInterface" uuid="b847ca74-0d2c-4415-9337-126468d7352e">
        <p_InputPorts type="ci.SignalInterface" uuid="194507a1-ee1d-4482-8230-dbd41f232bfd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PWMCtrl_MtrDir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="91ad1895-d731-4815-83a0-79cac84460b2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f83cb1f7-83f6-4146-8389-78459f8d7c5a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="686b6b66-1153-4f8b-b00d-22226426aba0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="faa38a40-1288-4473-a0cc-83df9582eb68">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="81303c18-8a67-48e9-9ac5-ed7d43f8e872">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b35d3410-62b3-464b-b19f-33115a7c9d00">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="500c24ca-acbc-4da3-8418-d1c2c33a3aeb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="428d08ee-03ba-43b6-b0b4-e99354aa28a0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="51ef3a00-5447-4fef-a8d7-98cfc7ca2138">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0875f238-8d4d-4031-92fd-263483a4f45f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ee37a632-b26f-4183-a00f-ed64bc0d8194">
          <p_ComputedNumericDimensions>2.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3b38fcdb-270f-4f4c-a3a9-fd2fba99d148">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="45d1a3d9-e76a-49f6-b782-e03c05d9a951">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="11935e44-db59-4db7-a15c-97968f7ecd53">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d22a240f-460e-4c13-9d41-d1e30401e975">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4f8b6b43-8d21-46e8-b208-67f602c62c8f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="29e0bfa6-dad3-4229-9c97-9ac08661da5d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3a6aa232-ee34-4ec1-b4e4-395bd04f22bf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d67e873e-816a-43b1-922c-ee392206b1a6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1e330f84-ac01-4ac4-9ec8-36b8806786ae">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="122cc9cf-61d8-4146-8a3a-0fa966ec2c8b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="de8c6379-7cea-406c-80e1-fa3403aec91e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>HallDeco_Dir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6da2a3a9-3ce9-4e05-82ed-e2b5a4008ee0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3bbe14f9-e56a-4e8e-aa62-46bbb370bfde">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>rad/s</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f269cdf0-2d4c-4b8c-80f9-4811d10985d0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="db7b8525-ac22-4132-aca6-1b81253e5169">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2fc514ff-93f6-429c-b344-bcaf5f371532">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ade32dda-a2a4-4b25-a7bc-51b462a1a82d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8a7c3a65-4837-47c1-a182-cfd2975a3061">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="253cdf6a-3271-40ed-b14b-7c7e15312925">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c0e945a5-332b-44bf-9072-1a0ec68d6721">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8c0d882a-2f26-444f-b329-11f6806c4499">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c3a2e77b-1caa-4db1-bdfd-eedd0d07beff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f6c68468-7e1f-486c-bc16-c807f2a37917">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="119c5980-9ed5-4bc9-98aa-73823fa46eaf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fac59237-605e-4f40-b3ea-0c0c489df2d0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dd100f80-8dcb-466c-ac6a-c237aefe3d0c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="880a090e-145a-48f1-87bc-60717aa82664">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="608ec78f-4db5-446d-8270-4be8c50764f5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9a8c46a6-14a6-46e9-9aa7-683c7d5bb7e8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6e32e322-83c0-49b9-a5cd-3e77d7030800">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="96033a42-ead6-4485-aa1a-320d720301dd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="22d4efd6-4b56-423e-8731-e55664c627aa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2db3fa01-bf03-4fe2-bc83-bfe233231111">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="77e0eda8-c079-4ac6-a19b-4413b087be35">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="09116c94-1ece-4739-b0dc-52e67ce412c1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="106f38af-957f-492b-a7fa-92753d5b5098">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_Area_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c10985c1-3cf3-4206-b29f-1b835b69adcb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_PnlPosArea_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7b43d9dc-14de-4adc-a422-ef8250e56335">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c3e65389-8b02-47b6-a8d7-6645d2b4a2f9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2e2fea09-bef9-4b88-8e86-bd65d345c4f4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="54821b4d-9e97-43ec-9068-d5696802f7c5">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="899fbe18-5f5f-4dc5-9114-f73c019f5793">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ccfdffe3-a3aa-41c0-a826-c32e62a39794">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="de5e3667-8ab7-4e2b-96d1-0df4b6b49d7e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7c3bd5ad-daba-4fac-a3b3-0f5688c826da">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3f1d4cb0-6f9a-4ab0-ae30-365aab64b4d8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4e6dfdf1-e2a6-4c6f-a7bb-329ecab1d024">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="07dc61b0-e687-4363-90cf-136a6624b110">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e4ad2832-037e-45d9-bdce-7417de285a07">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="276019b1-8699-4cb8-80c9-6ad886d2420a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ddfa488b-2082-4565-b2d6-35268db74f00">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6e9ba390-aaf1-4051-ab28-171b0f9da73d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ab9276c1-5519-49db-be70-fb95c7a1b743">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fd0a84a8-646d-473a-b0cb-ae2e363a940f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6e34dbef-ee74-43c0-afad-cf13e67ddb3a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8cad6bf1-e9b0-4d08-a3f2-57a3f1a774ec">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bdf9f2c0-555f-4e80-a068-bbfa658b4876">
          <p_ComputedNumericDimensions>3.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6d503c8c-454d-40f3-9be9-ee62242db2bf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SysFltDiag_LastStopReason_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="43272e8c-015d-4d89-a5bd-2a47ba607683">
          <p_ComputedNumericDimensions>10.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bfbf01f0-58d9-4223-9365-ddbb2b022fe3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="63b1a994-6f41-416a-b550-16fe499182f8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a60a7d75-c83d-4f8e-b31b-46be1b727de4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="598bf60b-ad0f-4140-9a2d-97722ee1c7d4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7c28a1ef-b833-4e84-bb98-ebc8b8ef0cf8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="aea41825-af28-470b-960d-e095c42e1117">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>BlockDet_Stall_dir</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a5873551-4194-41a3-bd24-dfb7478423f0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6083ae55-58af-4a04-90ca-18401d293e6e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ad71e38e-994b-4a83-b731-6c36414193c9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7f12cfe9-af6f-40a9-aabf-9976ead256a9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="469db182-1979-444a-876a-bf1501b487cc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="05d86962-1f23-4ffb-83d2-33bf96fc04d1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="16a124c8-ca75-43e6-9722-95593d2c975a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d5a0f574-f410-458a-9e3e-b6694054b05b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4bbe61b6-92b3-4db8-8490-2955d5d0e606">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MtrTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dc994bf1-991d-4047-a39d-3a2869fe0e10">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d174a195-febc-4675-af99-8b3ff8dcd74e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MosfetTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f08b801b-92c8-4a8d-aac1-127dc2b13ee6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_ATS_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="23bbf02a-1ce7-4b63-a32f-4e587f84e06b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="505c38e8-6d8c-411d-a363-6248ceb9b030">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>MtrCtrl_MtrDir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3d1b5e25-195d-4983-8cfe-************">
          <p_ComputedNumericDimensions>2.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2d001da8-4294-4ff7-bd55-8305e9b22dbe">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="58dd1c9f-4c17-40d0-9755-2c6027a8ca3e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_RelearnMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="19723c17-18cd-490b-b700-96ee34cd1d1c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f67aa6f2-6612-4c69-9c15-0b484248d0ec">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a27c0b69-f3bd-4abe-a3d3-b56a13fdd06f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a6715773-15c5-4388-9cf7-120566885eca">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f6a42d61-46a5-46b9-8d55-d6d2993e111d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2fea3f79-dfb2-4778-8338-14587e6e9166">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint32</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a31444fc-ebbf-4aa2-894a-48f881b57633">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="99204849-398d-4b7c-8b23-d11fdda78c2f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e98c0597-6ecd-4eb5-ad74-5d04bb799dce">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6c5da984-ec6c-4aca-9517-d412530a17a0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b04e7cd4-df93-42c6-82c9-1fa09fa2388a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5697c6da-f187-4c68-88c7-200a21932d79">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="71d71468-bbfc-4182-947a-2da6b953a5ff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="56cb5b01-e6e7-41b6-ba00-5d8c6a16fb3f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="599e9c91-4c4d-4620-9748-f28c1bc63c6c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1e81938b-cd4a-4fb7-a1ba-e510354f8863">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4071b4c6-91fc-4076-b197-656b464d0e52">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3aaf20f8-eccc-418d-a799-6962e4bf7a9e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>HallDeco_Dir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2e8a38dc-5786-48f4-86f4-f6e5541ee4a9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d68be480-6c69-4ef9-a0a0-c130b345d1af">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>rad/s</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a67b8402-419d-47ff-978c-49c37efe1dbb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7479d0e3-0eb4-421c-8c3a-fd0d373d3318">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f8bd9f87-4440-4e6e-8d74-1cb82af76881">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ca492e1c-2f92-4dd9-9145-e3acb77078d4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="08c7b4cd-f029-4d88-a46f-dd65599599c9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="932ba37c-07a4-476b-b0a6-0e62af9bdf32">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="31470110-e8f5-4f05-8bc8-99ebf6398e24">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="361cd125-e014-477c-8846-0e65bd81998a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>StateMach_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="358f7898-9a84-4393-a5f6-12979ee9b332">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="663633ef-b3a7-42c7-8b43-cea09bd6f926">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="13328792-ac0c-4995-be4f-43233dbc3837">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e986d658-aec3-4bcc-911a-ea3f95d0f960">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3692ad35-5422-4645-a3ad-f907d8460506">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8dd56c25-7ba8-4cf1-a24c-da16988c3193">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c44dc202-b8ce-49be-aa13-f40305c4f011">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e0ce36cb-aa9a-4efd-b0ab-7e09bc028dce">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a8fe5d49-f476-47a6-9fdc-6e0d70e45aca">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e148869f-6991-4bf0-b23f-07df623beb50">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b3e2f20c-8ca9-4662-a0d5-f17117252347">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cf36780d-6c1b-4f77-9331-d8fc26e32e2d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="72d2796b-6543-4a49-9a23-7beda17a68a1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0140098a-6041-45f3-9eeb-11e2654e290a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fcd7bd60-63e2-4ac1-9886-8c264b76cb9b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c45a859b-5d2c-433f-86aa-50fec863b691">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="98438172-c35a-49ea-999f-4efaec2a3643">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dbcc6a19-809f-44f6-9899-a6c6940a1265">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2b395f08-520f-4cd3-82aa-23f987efbad1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="551e5cf2-ceea-4521-9b18-8e648a8b2382">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e2ac0561-14e7-4dca-9960-3931dd4a882c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f70d21b1-3451-4bcb-919d-8deb8413b34f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="332a676c-026f-40ad-8c28-85335b48db95">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8aea6a13-785c-4b3f-bd80-e507c7be02f4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="28e915ec-a4f8-46dc-96bd-9e6eb4802cb3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint32</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="993aa147-ffae-44f3-b7a4-058cfad28855">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7e7d8831-a6d0-4570-9164-255061232e28">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="21d7531f-1cc5-4057-9ac2-f13a26323998">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="57f836d6-48a2-4117-bb67-fe5a95360afd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="be4084bf-4788-4422-b174-a6723f4a3efc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5d476fe3-6ffc-4b07-aab9-711e0a01eb1f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bae44897-aac9-4241-b7a0-165dd5a19697">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4c14827b-157c-489e-9af6-4449de15812b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="048bbb25-c0a2-451a-9b34-bb5dab3f1f5d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="57104d29-d2a2-4125-81ab-1d1dce7852df">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7e8e2668-6665-4446-b58c-a34ef86bbaf6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="92c6645f-e45e-43c1-b9fa-b59a1fc7db5b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8efe4b48-5fe8-4059-88fc-5ab67406a18f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bbef91db-3bf1-4f1e-8861-a7f926f31e14">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="75de39c4-19cc-490b-8186-2a7c72de13f0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>stdReturn_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ae1baad7-0960-4afc-bf8c-166a72a073ee">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d5d22589-1efd-4434-b2b7-85196030b4c4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="65915183-9ae8-49d3-b57a-ad000b9a755d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="913aabbc-c153-4c01-b6fd-a8827758504c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4f3d9eed-d6f6-4f7e-8233-4709a017ab95">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="95535d0e-983b-424d-a41b-28336b8f86e0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8d255cd8-f9f6-4085-9955-fc3eafeb1f7e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="390e60c3-86e0-46fe-a452-ee03693c3a7d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ca927045-720f-4bec-b2bb-4328ad3b7b4a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9d7ea96d-600c-4a73-8b62-e98dfa818a1f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b70f693e-cbc1-460a-a9a4-f2547ba58810">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="36a24544-e16c-4d9e-8602-19a1c3c1caea">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CfgParam_TestMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="685a9cd6-125b-4007-828b-c12a58cd2692">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_MtrCmd_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7fe2680f-aad1-47e8-8076-c91d57620901">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5b944b48-6b7c-481e-a92b-d9e443e21a72">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="584dc5a1-95f2-428c-942f-57c0bb0d78a9">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Rev_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f18f8b52-ea41-4a64-bf4b-7dca5920b06c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_ATS_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="61320a78-0077-4bd4-83e7-92e79d580983">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="421c6d21-404c-4178-ba82-ffcbca0bf27a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>%</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a13e00b2-5e3a-4cf7-bfb5-e269c8caf84a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_MovType_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f8452d2d-937a-49e1-a932-a31bf0b20241">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1c5e8cd3-ce0a-4870-9745-d42837418aa4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_RelearnMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5434cd17-aa0d-491e-a325-e7db086728d0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SysFltDiag_LastStopReason_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ede27ff9-93c7-4916-9091-3b799a8ee5ea">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c4ee2663-4acf-4ce6-b0b7-063b1f5e3633">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b1a1506e-2728-494b-8603-08b6c8a431cb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_Name>MtrCtrl_Mdl</p_Name>
        <p_OutputPorts type="ci.SignalInterface" uuid="e9b2c169-c235-445f-887a-b7b52494ff69">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>MtrCtrlBus</p_ComputedType>
        </p_OutputPorts>
        <p_Type>ROOT</p_Type>
      </p_RootComponentInterface>
    </rootBlockDiagramInterface>
    <simulinkFunctions type="ModelRefInfoRepo.SimulinkFunctions" uuid="50a05ebc-c020-4fcf-81fa-f1835f2aec77">
      <compSimulinkFunctionCatalog></compSimulinkFunctionCatalog>
    </simulinkFunctions>
    <sltpContext type="sltp.mm.core.Context" uuid="fc17c67f-57ba-4044-8bd6-436f928294d2">
      <globalData type="sltp.mm.core.GlobalData" uuid="eda5c9b9-53dd-49fb-9061-f5f99008e711">
        <dataName>CfgParam_CAL</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7e806c75-b289-484a-8159-cd208cb82aba">
        <dataName>MtrCtrl_DirectionStatus</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f9ac30c5-fef9-4fb1-859b-200c09922b11">
        <dataName>PnlOp_pMtrSpdCmdOverride</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f4a625a4-716b-4b8e-83b6-1097c8bcc629">
        <dataName>portCtrlLogBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="536439bc-0611-4743-b6fa-0239ddc5dab5">
        <dataName>portCtrlLogBus_CtrlLog_hReactTgtPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1ad50a9e-5dad-4d70-b959-64fe9bf54302">
        <dataName>portCtrlLogBus_CtrlLog_isMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2bb29b58-5217-4d92-8153-6b770b8371af">
        <dataName>portCtrlLogBus_CtrlLog_isPnlMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b4de69ce-685f-4e7d-8fb1-1431f62c0790">
        <dataName>portCtrlLogBus_CtrlLog_stDirCommand</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="855410b8-409f-4179-b02b-0ab180a1e6a6">
        <dataName>portCtrlLogBus_CtrlLog_stLastStopReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="27e592af-9cb6-40cf-9420-310c9a68e7e6">
        <dataName>portCtrlLogBus_CtrlLog_stLearnInt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f911f83e-2bb7-4e39-94fc-c8b8251652af">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movReact</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="90546e16-27e9-4232-a1e2-ee33748d7ebc">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b368df78-2113-4513-8ff8-e22dec7eca95">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a8d61e8d-8dd0-43c0-b59f-d3ba13443606">
        <dataName>portCtrlLogBus_CtrlLog_stMovType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d382085e-a4fb-45a8-a1f0-b8dd504b3724">
        <dataName>portCtrlLogBus_CtrlLog_stRelearnMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8a75a923-6a75-43b3-99dc-6d9360914f74">
        <dataName>portInport</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="34df0b54-3f2e-4347-beee-faaa04e4fe80">
        <dataName>portMtrCtrlBus</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="52ea0625-3c25-4e35-9eec-658c078b6cbb">
        <dataName>portMtrCtrlBus_DTC_MTR_Dir</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="465b95c7-a601-4697-9d2f-9c85a079c8be">
        <dataName>portMtrCtrlBus_IoHwAb_SetMtrDir</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cfc06bb0-3a70-41f0-99df-dbaa2ba101c6">
        <dataName>portMtrCtrlBus_IoHwAb_SetMtrDutyCycle</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1e08eb2f-1164-48dd-8a5e-b195e0b5f8b4">
        <dataName>portMtrCtrlBus_MtrCtrl_rClsLpFactor</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dc1e29f8-629a-4060-b99b-92abc7315900">
        <dataName>portMtrCtrlBus_MtrCtrl_rMtrSpdError</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fb8bb894-3e23-48ea-aa70-8d0593bbc56e">
        <dataName>portMtrCtrlBus_MtrCtrl_rMtrSpdTarget</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3e797b6e-6963-4e5a-b2e0-15d4f7d44236">
        <dataName>portMtrCtrlBus_MtrCtrl_rOpnLpFactor</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d822dfe8-01ec-4ee6-8caa-a770bb38e132">
        <dataName>portPnlOpBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2ae836ae-5d17-4653-9b8f-e39ba8368c63">
        <dataName>portPnlOpBus_PnlOp_hTrgtPosPtcd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9a843085-5dc3-4d8e-bb68-258ca8cb009e">
        <dataName>portPnlOpBus_PnlOp_isMtrMoving</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="48ec1a0f-4602-4b79-9594-cc5fd3b66a63">
        <dataName>portPnlOpBus_PnlOp_pMtrSpdCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4d512f5e-85fe-4ee7-ae2a-b22ac52ae159">
        <dataName>portPnlOpBus_PnlOp_stATS</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="09805ad7-9ee5-4f63-90e7-3e99ea53be7f">
        <dataName>portPnlOpBus_PnlOp_stMtrCtrlCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="18859f5e-d7ef-484b-8f89-e82e37ced837">
        <dataName>portPnlOpBus_PnlOp_stNormalMov</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="740a2fbd-d282-4f26-abc9-a980d4439502">
        <dataName>portPnlOpBus_PnlOp_stReversal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e58ffb5e-c653-48a4-9a81-351a44dff50d">
        <dataName>portSWC_DataHndlLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0c25c8e5-86a2-4582-9164-5860512cb3ea">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_BootLoadVer</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9c11f363-4162-4740-bed9-7f41dbf952e1">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_CALVersion</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="27e44ddf-ac5d-45ac-8158-7d764cb90a27">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_HWVersion</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ffd1d73c-aba2-4e3c-8c51-ff31b29a1e86">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_MtrSerialNum</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="363b83c5-ed3d-456c-b500-1ab1a27eacd2">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_RoofPartNum</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d364e12a-a7d5-451b-b61a-0a3dcd79872f">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_RoofSerialNum</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5fa3e0cc-6455-41a7-867c-377879c611d4">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SCUInfo</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8defa611-7f50-479e-92d8-2dd6913ef05b">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SCUPartNum</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3a10a10-e035-42a7-a045-0206fa76c06f">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SCUSerialNum</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="48993db8-4e9e-4f12-ac8c-687a1c65065a">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SWBuildNum</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="93caeb04-d140-4dc8-8075-d4c784ae3273">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SWVersion</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="95b2a587-2dd7-4868-9a4d-48cb024d3a3b">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isCALFileVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6834453d-85e1-411d-b7c9-37650369abdd">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isPosTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f9f3a51d-8fa0-41e2-987c-f13ee128f8d0">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="299ff2ac-0250-4d6f-91e3-02c975f247ad">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b84a1d90-a96e-4dd8-96fb-01316168af61">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stTestMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0e6aad79-ccdd-422f-b330-38ac09f60ad9">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currByteOfRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0db98267-d6f6-4b42-af27-353ecf4c255b">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currRFArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="68f52729-04b8-407c-a283-f5753a68c40d">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isInsideRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="58eb0c22-4d08-4ead-9555-2432cd73e81f">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="88eaa9ac-c8b7-4095-bdaf-b9e61aa0a124">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4acda85e-6cfe-4768-8591-79e653ea87f5">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmBak</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="32bfb132-587c-4787-9e4a-c7ac0ab1b4ee">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmSync</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4114ccb4-468b-4d35-a898-189001dd0dab">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d0067990-9714-4e0c-9329-e90429f2cb29">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bc952370-c6b3-491e-9672-b5a0d9c37601">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_stRFSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6fd06215-0d39-4863-8de3-7d298a1419ca">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_AmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5b6a185f-40fa-4075-bdd5-5bbc05faf92e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_CycRenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b10edbcf-8667-490f-88b5-3c7efd9475f0">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LeanrFailCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="04dd9072-4616-4e74-b4e2-91a8393c70a5">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LearnSuccessCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fb9b24fc-0eb0-4006-aa51-aacfcf21d7ee">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MOSFETTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="13faa47e-e3f3-42ce-9f58-106b932eaa84">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5e8929f8-04bc-4308-ad33-9ddd23fe86a2">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c80066a0-6228-466a-915c-a00f86063b7e">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="74b6c029-c80b-4de7-8d4e-1d1d4da44d53">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8f376ea9-2555-4fed-9e25-0b4d901f416b">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MotorOperTime</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e322544c-9e2a-4769-b3e8-f4925f10d326">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_RenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="424c4c76-a9f4-43d4-b45a-bb8a3e7b6404">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReprogrammingCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5d2c14b5-35d9-49b3-83e7-0cfa5f59c2cd">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2c37acf0-0939-4b1f-b656-6c17c75c76bd">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="353aea96-2242-465c-a220-5be6e9eacb7f">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d41e06cb-e142-44b7-a5d3-4ea18b19a36a">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalThres</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e81cf50e-5ff0-46b7-bb57-fb8486bf15f7">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_SlideCycCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0cbee59b-9ca8-4126-948c-76aef639d6c8">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_StallCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6d7f7f2a-76e2-4f63-a752-5dc9dcb48c56">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstCaseTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f3d716be-7679-4454-98f4-377bc47c73da">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b8ca3507-2ef4-4ae1-9319-05976d858f53">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1e8470df-314e-48fa-b020-d7433ab8b531">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ThermProtCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d01a4129-b03c-49ef-82aa-a2e4931ea8f1">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ce3b087f-d611-4b86-9759-49820fac30ab">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="342aa87e-da90-4f9f-81de-fc1299e8a257">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c4700bfa-9ad6-4774-bd43-df34fc36d706">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_unlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8e4e30c8-1473-4557-953c-cc6b7a8dc024">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHistl_TEstCaseTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e835a630-95b3-4bdf-a595-b6467a93117e">
        <dataName>portSWC_DiagLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0537a9b3-d153-4f25-b8d3-cf288225db0b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d425dff7-6704-4832-907d-10a3164c938b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_TMOSFETTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7281eb39-8ced-4368-8aa4-c09e81896838">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_u12VBattInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5873d56c-d038-4a68-afcb-5fca38442935">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uHallPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e0aefb49-8933-4b90-9488-88cf2909211a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uM1AVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e9a6948b-a1dc-4d87-b33f-397e8ad04812">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uM1BVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7957f67a-850a-4905-904d-fc416bb815f1">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_APDet_FDifference</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01884629-8fc2-4898-9277-e0661852da5d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_APDet_FTracking</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2b753d94-fb1a-4f87-b0d7-2039d5b24661">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_UsgHist_ATSRevReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="414175c0-1212-4e04-9c17-760ea08e45a7">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_AmbTemp_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e125f6ee-85d3-4414-8c72-2aa4fffe74d8">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_AmbTemp_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e615591b-b1c7-41a2-95a5-ca1a55e73925">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_BlockDet_BlockDet_hStallPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3760546f-8f20-4da3-8cd7-acf394e2749b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_BlockDet_BlockDet_stStallDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4e5b95ee-6d9d-498b-ae13-2338e63f34ab">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="71a4caf2-4d7c-48b1-93fb-2cc2eebb0d5b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_CtrlLog_CtrlLog_stRelearnMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1095bbd2-0f07-44d0-9efd-a74b7cc1d827">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallOutPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5386036c-6b65-45a7-8fad-4e73e4fac7f4">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens1PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a44f20cd-be62-4d86-be54-cad1903451b9">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens2PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9a7a0e21-5e5e-4c56-9743-40d1433f4517">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isINTPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="242e893b-8b6f-4a83-b4e9-b9ffa4d83457">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankAPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f95c6a11-2e1e-4aed-8f56-8eb4fd45a90a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankBPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6ace56b3-6b5f-40ce-8ec2-7e35adb679d1">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isVBATMeasEnbPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="39400afd-c564-409c-8401-8bed37187bca">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="90015bd4-129e-4ce6-a510-7a6e56335a2a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isHallSuppHS2Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d0e01fb6-a52c-404f-87cc-46cfcd86f6b1">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isMtrFebkCtrlHS1Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e33be036-92c3-4a5a-a09b-0fc5718ea661">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isSwtchSuppHS3Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ebfc7e1a-b2bf-4f64-a721-665c5be1f24e">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_u8GetPWMDutyCycleVal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b466dcf5-ddf7-4b76-aced-c1e789d1b1a3">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_DTC_HALL_Hall1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3a20a64f-c657-49d5-a968-5380af2984b4">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_HallDeco_DMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="132916b2-07a0-4a0c-b33e-847f700fe69f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_HallDeco_rMtrSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="68d6e112-e9f3-404f-8b43-cbbc9ba213bd">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_isHallSupplyFault</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0e1361ae-8683-4fc6-82bf-d82f916e05fe">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_isLearnComplete</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="73a75ba5-dde9-4478-8c8f-51425fe81269">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_isLearningAllowed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d2a9b62f-bdc1-4e22-8885-db99e5f1ee6d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_stMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2639b881-76d5-467a-b2b8-d2773696dc36">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1fc2e02a-d206-486d-bc3a-42cc856fa063">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrCtrl_IoHwAb_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d521a520-5b40-4e48-90d9-dcbff1752d6a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrCtrl_MtrCtrl_FeedbackStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="40276d95-b47b-4d1e-b92f-af3bdd8b753e">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrMdlBus_MtrMdl_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01645959-0e72-45d3-ac94-53cb08e23374">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PnlOp_PnlOp_isMtrMoving</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4bb94ad6-3868-4e32-8932-d1c7fe542799">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PnlOp_PnlOp_stATS</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cedd5f1f-74bd-4108-bc4f-ef16d21c534f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PosMon_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="acfc181d-610d-44dd-9df3-c7ac0a51c15e">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PosMon_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2fb74a64-3df6-406c-a18a-67bf8433d9dd">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_RefField_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e26b5aaf-c5c9-48ad-9d43-c5792e7eb8e0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_RefForce_RefForce_FRefForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="86fc984f-cabb-4d25-92c6-1bf9a9006b65">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_StateMach_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a8058c2a-3735-42cc-993b-6d59a5d2076d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e32aa0e7-07fc-4e08-acb2-8cdf84073000">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_stMosfetTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="64d4aa2e-dfe0-4fff-a5f3-bea6619f53b9">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_stMtrTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ae25adb8-3100-425d-80b5-f242aaf2f087">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThresForce_ThresForce_FatsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="97d4cd7a-f5de-4b2e-8f7f-088602e1be92">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_CycRenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="206d779f-2cef-4b96-a4da-de8ea357498b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_LeanrFailCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b012f7f4-7657-49fc-b2ce-647f177e3806">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_LearnSuccessCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ba1da64d-292b-402e-8797-f56f48522fa2">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MaxAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="52b8e56a-3406-4a73-8c42-3453e48453ea">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MaxBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cb9e7e89-1503-4316-8715-10707dce6878">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MinAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ab21ebd1-162b-4cae-9ccc-2562dee73510">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MinBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4a617426-724c-4737-8595-074fbb4c3e9e">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MotorOperTime</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e9fb6268-fe76-4df0-8bd9-237f55592f14">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_RenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c11965d6-b03e-4d1b-9ba0-83a5502edb08">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReprogrammingCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a7125adb-31f7-41f7-853d-0760d8832c28">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1134a8bd-8e2e-4725-8f05-4a2b2bad7c26">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cdcb3037-e948-4fca-8b00-7b0f58beb8b8">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3f6cb998-5dfd-48a3-bbac-3a7d423cfc1f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalThres</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fe023389-3461-4386-9e54-28ce29e8d7b0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_SlideCycCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6b116437-793e-4e8a-a8b7-6d8763c48628">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_StallCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="563b4eb2-2961-42a0-91bc-35063e9e4009">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ThermProtCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fe955231-4925-40a4-befb-dcfe74f10660">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e12dcf45-5436-435c-b267-70c887befe99">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VehCom_VehCom_vVehSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f57b51a8-ca45-4bf9-8633-f3f4fc39ca36">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="92f45fac-8bb2-4b5b-a690-62065bc31d3c">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="821d44e2-58f0-4ce2-bbb4-5387b71bccdc">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="43ee36ad-a16b-488f-94a0-7051bd69eff1">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="eeca6543-229c-44f5-bea8-ab7c74e4fd96">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_DTCStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="611c77d3-c847-48c6-9eb2-b241c66f3701">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stLast10DTCs</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="af5f7a54-558a-4eb5-94ff-dab77ee80192">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLasStpReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9da25186-8361-4d2d-8e3a-2ede5800275c">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLearFailReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c086fe32-**************-e086d17f18f2">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLearnFailReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a1daa475-c52c-4889-9094-cdb69de340ea">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsSysFaultReaction</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1cb8b136-7e37-4ca0-82a1-3ab5b355ae14">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsUnlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6572c98f-2751-43ae-accf-ab4f26111bf3">
        <dataName>portSWC_HwAbsLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3d084f8b-9a72-462f-bf8a-1e25d85b62c6">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="050ac66d-35af-4928-a420-615e24da0e29">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_TMOSFETTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ab5c3c73-bbd1-4201-9661-cc6950fc2934">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_u12VBattInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ad20fd8e-fde0-48b7-8226-ff989efdb06f">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uHallPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7345f331-85f4-4729-99c1-c5c1cedbfdce">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1AVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f404107b-43ff-4428-ab34-a674fa359895">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1BVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9662cac9-6576-4d8c-af97-158507682f48">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uSwchInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4d7771cc-bd54-437a-9692-7c973cbf57ce">
        <dataName>portSWC_HwAbsLyrBus_ADCMonBus_ADCMon_uSwchPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c9e3d561-adf3-4885-b225-4e3cd7d02eb8">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_PINReadStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e0d5b8c5-a3ff-41bd-b81b-df6f7a6255fc">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isDTCMtrInHigh</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="94e3dbb4-bb7e-4cf1-a138-bc1151efa63a">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallOutPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e55e159c-2bba-435f-ab3e-57ea428c2a37">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens1PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fd795051-f6f7-4fd3-85ed-4cb42af709e7">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens2PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="94832638-b653-45e9-81f2-dc0f2b5dd32b">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isINTPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1d33019d-01ab-4af8-a187-16155392436e">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankAPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e0f9196c-8c67-4222-82ae-93dcf5466761">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankBPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4f51fada-360c-499c-abc9-262b0810681f">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrInHigh</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7ecff89b-3e41-4669-bd75-1e069932c869">
        <dataName>portSWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isVBATMeasEnbPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9104b024-b046-4f38-9e95-30d418bee68a">
        <dataName>portSWC_HwAbsLyrBus_DiagComManBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ab627928-c504-4366-baa7-c1f5303da1f7">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isHallSuppHS2Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cc89024a-d310-45e1-8eec-4454f2df1fc1">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isMtrFebkCtrlHS1Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b8e14538-1252-451e-adbb-04401253ae3d">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_isSwtchSuppHS3Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="684214ab-2a72-49c3-a1d7-d8af823d7eb5">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_ExtDev_u8GetPWMDutyCycleVal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="07de86be-502a-4cbd-a376-32b57cc7ca08">
        <dataName>portSWC_HwAbsLyrBus_ExtDevBus_Signal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="90039b1f-95f0-42d4-b233-7ac86f0872c5">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="074fe440-8c5d-4539-8ae2-dad3b0d51a4d">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_DTC_HallMtrOptDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3dacd095-58ee-4f31-98e8-3b18fff7928d">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_DMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3f1159bc-e187-4ae8-817c-300e69711ece">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_isMtrSpdVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f5912e49-816d-4ad0-a266-1fffb880c7d4">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8af65cdc-67f0-48ce-8776-e4a9e5f1048e">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5b72117f-17ec-4e91-87fa-fe2b853d0048">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="174dc88f-c9be-480c-b823-232a2624e831">
        <dataName>portSWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFaultDebd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="92b724ae-724e-4d85-ba64-012ab8359a7a">
        <dataName>portSWC_HwAbsLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dda0429c-0be4-4fa6-b3fe-446ccebe34f3">
        <dataName>portSWC_HwAbsLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDutyCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="58626b40-6a03-46b3-8248-517b661eeeaf">
        <dataName>portSWC_ObjDetLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="75e1e66e-0939-4b6c-a4b1-9390e8c06704">
        <dataName>portSWC_ObjDetLyrBus_ADCMonBus_ADCMon_u12VBattInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="700fa6ce-c654-4398-ab06-fc59ec8cde80">
        <dataName>portSWC_ObjDetLyrBus_ADCMonBus_ADCMon_uSwchPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="08539e1e-d1b7-4e59-bed1-4b7a33179f7a">
        <dataName>portSWC_ObjDetLyrBus_DIOCtrlBus_DIOCtrl_PINReadStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6ba6e474-5f76-467b-a937-b2f6cb641031">
        <dataName>portSWC_ObjDetLyrBus_DiagComManBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e24e9a2e-144e-4d5a-a003-2770a0ef25a1">
        <dataName>portSWC_ObjDetLyrBus_HallDecoBus_HallDeco_DMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="201ba55b-e7a9-4c04-8ada-8cd8f5aad244">
        <dataName>portSWC_ObjDetLyrBus_HallDecoBus_HallDeco_isMtrSpdVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1479a51c-c160-436f-8416-0dc9a09448cc">
        <dataName>portSWC_ObjDetLyrBus_HallDecoBus_HallDeco_rMtrSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e69dd61c-4022-4516-b156-06ab13723540">
        <dataName>portSWC_ObjDetLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4647cb61-9d83-4df4-8df1-539c045748d3">
        <dataName>portSWC_ObjDetLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7878631d-8589-4647-89a6-1c652ef38e40">
        <dataName>portSWC_ObjDetLyrBus_PWMCtrlBus_PWMCtrl_SetMtrDutyCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8e9a244c-52e3-410d-9710-2be71d9a7c27">
        <dataName>portSWC_SigMonLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0a41cee6-9dbe-455b-b4f9-5de0bb562ac6">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bc25aed5-6441-4560-86e2-8afaa426bfbc">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="77466b79-8c62-4709-aca2-365b88f36d92">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stAmbTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="852a65a9-ef96-4fff-b186-3fc26afd5753">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stMCUTempNoCritFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a407956d-c912-4fd3-b619-3d5fee4464b4">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_IoHwAb_SetHallPwrOutM1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="52e9a551-c31c-4b34-ae7e-a329aec1d2e4">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_PosMon_isUpdateDisabled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e414ada0-22f5-4c0a-b817-42e15e28eba7">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="32aff9f0-fca8-421b-b640-ae3af324be90">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ce6bc510-b2b8-45d2-9284-4202183d0b78">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01f35bfc-816e-4967-b258-170d8ca092bc">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_stMosTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cd0d97a9-020a-4694-9185-1a8d1f9c475f">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_curPanelArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1993d629-db36-4d73-ac62-0b2852be68f5">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bea1ecd3-3b07-4119-8cf7-0eeb95083ea4">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="02d0e6f3-f607-4a29-bce1-0c4910111391">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hComfortMidStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a8716ebf-d264-4edf-bf6d-f374b724932e">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8b7e3c22-5ff7-4d51-ab22-2e3463fedd45">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="27b0ebd0-cfe1-4317-a222-1b8fe69def83">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hNearClsSld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="009f8f34-285d-4f29-907c-32d28b775005">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2598d7d5-d506-4264-ba4e-43413a6f6495">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="985dbe15-**************-7186916da259">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="25711fcc-22c4-4359-8cde-6507fd2165c0">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3b4f22d5-ed63-4333-811b-4dc347f002f6">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isOutOfRange</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="04e54775-807a-477d-ba9f-5e5ffef3b57c">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isRelearn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="97892d4e-8c9d-4159-91c9-72e3309bc75b">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4dde0af9-b85c-4e3c-bc98-202bbab599e4">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01bfc2fd-3f40-40e2-8bc5-beb55403da65">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_panelCurPosArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="139e6f61-1112-449e-b19d-40df4c3155f9">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_relativePinionSize</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="680ea674-fc73-4a3d-b372-b3b1555ef231">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_stPosSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="60bf2541-**************-a3c00663c7aa">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_isDebSwch</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2a12c996-dfdd-467a-8d7c-e4472e8eca12">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stMultSwchFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="df163339-3d74-439b-93fe-4c00a8988433">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="58193dca-9d8e-4b2d-ad9f-eb72e5a7694b">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchStickyFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9cfbf9e7-cb84-4482-9463-80058c8d9873">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchVltgFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7b67279b-bc28-4bb5-9172-1093458e17ae">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_Pullup</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="74f2f7a8-e7eb-4a65-83ae-e7db9cf622e7">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4c5b8574-c7b5-4055-97e1-8d939de16ad2">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3738c43d-2b5d-45d3-827a-5f6a20922a34">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="efb60596-cb72-45f7-9a10-fe88a4beed28">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isOverVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8e408370-e68a-4f34-aae1-f38ed0569451">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isPwrLost</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c57f1513-f554-4f8f-bddc-a8d098d4ca4e">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartUpRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="77cf55d2-1647-467f-bbcf-9ea7713f9e61">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="99c101bb-8fb4-4d94-93c1-d1fa6a98b9ed">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isUnderVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ccdcc363-49c6-482a-8330-6638efa33071">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isVoltDrop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="78202fd5-c74f-42a2-ba5b-8aee33e2bb4b">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c2a77e4f-1154-4f2c-8046-f4a5745666bc">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <priorityDirection>HighNumberLast</priorityDirection>
      <editorState type="sltp.mm.core.EditorState" uuid="ef1cfea2-484a-420d-ae76-3d2ab30b0402">
        <isSynchronized>true</isSynchronized>
        <panelState type="sltp.mm.core.EditorPanelState" uuid="0bb34eb9-3f07-403c-a299-ffd7020be893"/>
      </editorState>
      <rootTask type="sltp.mm.core.Task" uuid="ecf9e529-3898-4504-9db7-9fd3c0551f0c">
        <context type="sltp.mm.core.Context" uuid="fc17c67f-57ba-4044-8bd6-436f928294d2"/>
        <explicit>false</explicit>
        <name>Default</name>
        <priority>-2147483648</priority>
        <subgraph type="sltp.mm.core.Graph" uuid="b3c34928-b3ec-49bc-aa1e-99fd9d1d9bba">
          <tasks type="sltp.mm.core.Task" uuid="41ac4014-9068-4011-b1d6-6daca94b5f7a">
            <context type="sltp.mm.core.Context" uuid="fc17c67f-57ba-4044-8bd6-436f928294d2"/>
            <explicit>false</explicit>
            <id>1</id>
            <isTimed>true</isTimed>
            <name>D1</name>
            <priority>40</priority>
            <rates type="sltp.mm.core.Rate" uuid="bc0d9257-a259-4224-9c70-671c9184c672">
              <annotation>D1</annotation>
              <color>-436207361</color>
              <identifier>ClassicPeriodicDiscrete0.20</identifier>
              <rateSpec type="sltp.mm.core.RateSpec">
                <period>.2</period>
              </rateSpec>
              <sti>0</sti>
            </rates>
          </tasks>
        </subgraph>
      </rootTask>
    </sltpContext>
    <stateWriterToOwnerMap type="ModelRefInfoRepo.StateWriterInfo" uuid="d192d85e-d167-4075-8c3d-51dab26f578d"/>
    <stoClientDataRegistry type="sto.ClientDataRegistry" uuid="1a6003c3-e167-4c46-9ef7-097d809f1ce1">
      <dataSets type="sto.ClientClockNamedDataSet" uuid="e822678c-89c3-4a39-82ad-69dd3970ebf5">
        <tag>sltpEvents</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="80c77c44-3c99-46f2-9032-de687e4c86b8">
        <tag>sltpTaskGroups</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="5e4c55ac-2a3a-4187-8b0b-9f6d1a1afc05">
        <dSet type="ModelRefInfoRepo.SltpTaskData" uuid="3e13054b-b116-4ce2-b3d9-2c6ef4409ce5"/>
        <tSet type="ModelRefInfoRepo.SltpTaskData" uuid="3e13054b-b116-4ce2-b3d9-2c6ef4409ce5">
          <dataName>D1</dataName>
          <linkedSet type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="5e4c55ac-2a3a-4187-8b0b-9f6d1a1afc05"/>
          <id type="sto.TaskHierarchyElementId">
            <id>_task0</id>
          </id>
        </tSet>
        <tag>sltpTasks</tag>
      </dataSets>
    </stoClientDataRegistry>
    <varTsUIDMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="74d96798-76a7-4a9b-a048-26c4ca9537cd"/>
  </ModelRefInfoRepo.ModelRefInfoRoot>
</MF0>