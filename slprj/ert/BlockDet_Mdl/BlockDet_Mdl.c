/*
 * File: BlockDet_Mdl.c
 *
 * Code generated for Simulink model 'BlockDet_Mdl'.
 *
 * Model version                  : 1.187
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:29:26 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "BlockDet_Mdl.h"
#include "rtwtypes.h"
#include "BlockDet_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "BlockDet_Mdl_types.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "BlockDet_Mdl_private.h"
#include "zero_crossing_types.h"
#include "div_u32_round.h"
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "BlockDet_Mdl_Exp.h"

/* Named constants for Chart: '<S10>/DetectDoubleStall' */
#define BlockDet_Mdl_IN_doubleStall    ((uint8_T)1U)
#define BlockDet_Mdl_IN_idle           ((uint8_T)2U)
#define BlockDet_Mdl_IN_reversal       ((uint8_T)3U)

/* Named constants for Chart: '<S4>/Chart' */
#define BlockDet_Mdl_IN_idle_e         ((uint8_T)1U)
#define BlockDet_Mdl_IN_moving         ((uint8_T)2U)
#define BlockDet_Mdl_IN_start          ((uint8_T)3U)

MdlrefDW_BlockDet_Mdl_T BlockDet_Mdl_MdlrefDW;

/* Block signals (default storage) */
B_BlockDet_Mdl_c_T BlockDet_Mdl_B;

/* Block states (default storage) */
DW_BlockDet_Mdl_f_T BlockDet_Mdl_DW;

/* Previous zero-crossings (trigger) states */
ZCE_BlockDet_Mdl_T BlockDet_Mdl_PrevZCX;

/* System initialize for atomic system: '<S3>/ConsecutiveMotorblocksDetection' */
void BlockDet_Mdl_ConsecutiveMotorblocksDetection_Init(boolean_T
  *rty_isDoubleStallDetected)
{
  /* SystemInitialize for Triggered SubSystem: '<S10>/StallTypeCheck' */
  /* InitializeConditions for UnitDelay: '<S16>/Unit Delay' */
  BlockDet_Mdl_DW.UnitDelay_DSTATE_a[0] = MAX_uint16_T;
  BlockDet_Mdl_DW.UnitDelay_DSTATE_a[1] = MAX_uint16_T;

  /* End of SystemInitialize for SubSystem: '<S10>/StallTypeCheck' */

  /* SystemInitialize for Chart: '<S10>/DetectDoubleStall' */
  *rty_isDoubleStallDetected = false;
}

/* Output and update for atomic system: '<S3>/ConsecutiveMotorblocksDetection' */
void BlockDet_Mdl_ConsecutiveMotorblocksDetection(boolean_T rtu_isBlocked,
  boolean_T rtu_isLearningPos, const uint16_T *rtu_PosMon_hCurPos, uint8_T
  rtu_hMovPosErr, uint8_T rtu_nCounterLimit, BlockDet_Stall_t
  rtu_BlockDet_stStallType, const PnlOp_Rev_t *rtu_stReversal, boolean_T
  rtu_newPulsesDetected, uint8_T rtu_resetCounter, boolean_T
  *rty_isDoubleStallDetected)
{
  int16_T u;

  /* Outputs for Triggered SubSystem: '<S10>/StallTypeCheck' incorporates:
   *  TriggerPort: '<S16>/Trigger'
   */
  if (rtu_isBlocked && (BlockDet_Mdl_PrevZCX.StallTypeCheck_Trig_ZCE !=
                        POS_ZCSIG)) {
    /* Sum: '<S16>/Add' incorporates:
     *  UnitDelay: '<S16>/Unit Delay'
     */
    u = (int16_T)((int16_T)*rtu_PosMon_hCurPos - (int16_T)
                  BlockDet_Mdl_DW.UnitDelay_DSTATE_a[1]);

    /* Abs: '<S16>/Abs' */
    if (u < 0) {
      u = (int16_T)-u;
    }

    /* Switch: '<S16>/Switch' incorporates:
     *  Abs: '<S16>/Abs'
     *  DataTypeConversion: '<S10>/Data Type Conversion'
     *  Logic: '<S16>/Logical Operator'
     *  RelationalOperator: '<S16>/Relational Operator'
     *  RelationalOperator: '<S16>/Relational Operator2'
     *  UnitDelay: '<S16>/Unit Delay'
     */
    if ((rtu_BlockDet_stStallType != BlockDet_Mdl_DW.UnitDelay_DSTATE_a[0]) ||
        (u > rtu_hMovPosErr)) {
      /* Saturate: '<S16>/Saturation' incorporates:
       *  Constant: '<S16>/resetCounter'
       */
      BlockDet_Mdl_B.Saturation = 1U;
    } else {
      /* Saturate: '<S16>/Saturation' incorporates:
       *  Constant: '<S16>/resetCounter'
       *  Sum: '<S16>/Add1'
       *  UnitDelay: '<S10>/Unit Delay1'
       */
      BlockDet_Mdl_B.Saturation = (uint8_T)(BlockDet_Mdl_DW.UnitDelay1_DSTATE_n
        + 1U);
    }

    /* End of Switch: '<S16>/Switch' */

    /* Update for UnitDelay: '<S16>/Unit Delay' incorporates:
     *  DataTypeConversion: '<S10>/Data Type Conversion'
     */
    BlockDet_Mdl_DW.UnitDelay_DSTATE_a[0] = rtu_BlockDet_stStallType;
    BlockDet_Mdl_DW.UnitDelay_DSTATE_a[1] = *rtu_PosMon_hCurPos;
  }

  BlockDet_Mdl_PrevZCX.StallTypeCheck_Trig_ZCE = rtu_isBlocked;

  /* End of Outputs for SubSystem: '<S10>/StallTypeCheck' */

  /* Switch: '<S10>/Switch3' incorporates:
   *  RelationalOperator: '<S13>/FixPt Relational Operator'
   *  RelationalOperator: '<S17>/Compare'
   *  Switch: '<S10>/Switch2'
   *  UnitDelay: '<S13>/Delay Input1'
   *
   * Block description for '<S13>/Delay Input1':
   *
   *  Store in Global RAM
   */
  if (rtu_isLearningPos) {
    /* Switch: '<S10>/Switch3' */
    BlockDet_Mdl_DW.UnitDelay1_DSTATE_n = rtu_resetCounter;
  } else if ((int32_T)rtu_isBlocked > (int32_T)
             BlockDet_Mdl_DW.DelayInput1_DSTATE_j) {
    /* Switch: '<S10>/Switch3' incorporates:
     *  Switch: '<S10>/Switch2'
     */
    BlockDet_Mdl_DW.UnitDelay1_DSTATE_n = BlockDet_Mdl_B.Saturation;
  }

  /* End of Switch: '<S10>/Switch3' */

  /* Logic: '<S10>/Logical Operator' incorporates:
   *  RelationalOperator: '<S10>/Relational Operator'
   */
  BlockDet_Mdl_B.LogicalOperator = (rtu_isBlocked &&
    (BlockDet_Mdl_DW.UnitDelay1_DSTATE_n >= rtu_nCounterLimit));

  /* Chart: '<S10>/DetectDoubleStall' */
  BlockDet_Mdl_DW.stReversal_prev = BlockDet_Mdl_DW.stReversal_start;
  BlockDet_Mdl_DW.stReversal_start = *rtu_stReversal;
  if (BlockDet_Mdl_DW.is_active_c2_BlockDet_Mdl == 0U) {
    BlockDet_Mdl_DW.stReversal_prev = *rtu_stReversal;
    BlockDet_Mdl_DW.is_active_c2_BlockDet_Mdl = 1U;
    BlockDet_Mdl_DW.is_c2_BlockDet_Mdl = BlockDet_Mdl_IN_idle;
    *rty_isDoubleStallDetected = false;
  } else {
    switch (BlockDet_Mdl_DW.is_c2_BlockDet_Mdl) {
     case BlockDet_Mdl_IN_doubleStall:
      *rty_isDoubleStallDetected = true;
      if (rtu_newPulsesDetected) {
        /* When pulse is detected, set double stall fault back to false */
        BlockDet_Mdl_DW.is_c2_BlockDet_Mdl = BlockDet_Mdl_IN_idle;
        *rty_isDoubleStallDetected = false;
      }
      break;

     case BlockDet_Mdl_IN_idle:
      *rty_isDoubleStallDetected = false;
      if ((BlockDet_Mdl_DW.stReversal_prev != BlockDet_Mdl_DW.stReversal_start) &&
          (BlockDet_Mdl_DW.stReversal_start == PnlOp_Rev_t_RevInProcStall_C)) {
        /* When stall reversal is started, go to reversal state */
        BlockDet_Mdl_DW.is_c2_BlockDet_Mdl = BlockDet_Mdl_IN_reversal;
      }
      break;

     default:
      /* case IN_reversal: */
      if (*rtu_stReversal == PnlOp_Rev_t_RevComplStall_C) {
        /* When stall is complete without new hall pulse set double stall to true */
        BlockDet_Mdl_DW.is_c2_BlockDet_Mdl = BlockDet_Mdl_IN_doubleStall;
        *rty_isDoubleStallDetected = true;
      } else if (rtu_newPulsesDetected) {
        /* Abort double stall detection when pulses are received during reversal */
        BlockDet_Mdl_DW.is_c2_BlockDet_Mdl = BlockDet_Mdl_IN_idle;
        *rty_isDoubleStallDetected = false;
      }
      break;
    }
  }

  /* End of Chart: '<S10>/DetectDoubleStall' */

  /* Update for UnitDelay: '<S13>/Delay Input1' incorporates:
   *  RelationalOperator: '<S17>/Compare'
   *
   * Block description for '<S13>/Delay Input1':
   *
   *  Store in Global RAM
   */
  BlockDet_Mdl_DW.DelayInput1_DSTATE_j = rtu_isBlocked;
}

/* System initialize for atomic system: '<S2>/BlockFault_Determination' */
void BlockDet_Mdl_BlockFault_Determination_Init(void)
{
  /* SystemInitialize for Atomic SubSystem: '<S3>/ConsecutiveMotorblocksDetection' */
  BlockDet_Mdl_ConsecutiveMotorblocksDetection_Init
    (&BlockDet_Mdl_B.isDoubleStallFault);

  /* End of SystemInitialize for SubSystem: '<S3>/ConsecutiveMotorblocksDetection' */
}

/* Output and update for atomic system: '<S2>/BlockFault_Determination' */
void BlockDet_Mdl_BlockFault_Determination(BlockDet_Stall_t rtu_stStallType,
  boolean_T rtu_newPulsesDetected, const uint8_T *rtu_uiHallCountsCyclic, const
  CtrlLog_RelearnMode_t *rtu_stRelearnMode, uint8_T rtu_hMovPosErr, const
  uint16_T *rtu_hCurPos, const PnlOp_Rev_t *rtu_stReversal, uint8_T rtu_RUN_PASS,
  uint8_T rtu_RUN_FAIL, uint8_T rtu_nCounterLimit, BlockDet_FltType_t
  rtu_SamePosStallFault_C, BlockDet_FltType_t rtu_DoubleStallFault_C,
  BlockDet_FltType_t rtu_NoFault_C, uint8_T rtu_resetCounter, uint8_T
  *rty_stStallFlt, BlockDet_FltType_t *rty_stFltType)
{
  boolean_T rtb_Compare_hi;
  boolean_T rtb_Detectioncondition;
  boolean_T rtb_FixPtRelationalOperator_o;

  /* RelationalOperator: '<S11>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S11>/Delay Input1'
   *
   * Block description for '<S11>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_FixPtRelationalOperator_o = (*rtu_uiHallCountsCyclic !=
    BlockDet_Mdl_DW.DelayInput1_DSTATE_c);

  /* RelationalOperator: '<S7>/Compare' incorporates:
   *  Constant: '<S7>/Constant'
   */
  rtb_Compare_hi = (*rtu_stRelearnMode == CtrlLog_RelearnMode_t_LearningPos_C);

  /* Logic: '<S3>/Logical Operator1' incorporates:
   *  Constant: '<S8>/Constant'
   *  Constant: '<S9>/Constant'
   *  Logic: '<S3>/Logical Operator'
   *  Logic: '<S3>/Logical Operator3'
   *  RelationalOperator: '<S8>/Compare'
   *  RelationalOperator: '<S9>/Compare'
   */
  rtb_Detectioncondition = ((rtu_stStallType == BlockDet_Stall_t_IncStall_C) ||
    ((rtu_stStallType == BlockDet_Stall_t_DecStall_C) && (!rtb_Compare_hi)));

  /* Switch: '<S3>/Any hall pulse change heals the error' incorporates:
   *  Switch: '<S3>/As long as error cannot be deteremined use old value'
   */
  if (rtb_FixPtRelationalOperator_o) {
    *rty_stStallFlt = rtu_RUN_PASS;
  } else {
    if (rtb_Detectioncondition) {
      /* Switch: '<S3>/As long as error cannot be deteremined use old value' */
      BlockDet_Mdl_DW.UnitDelay_DSTATE_g = rtu_RUN_FAIL;
    }

    *rty_stStallFlt = BlockDet_Mdl_DW.UnitDelay_DSTATE_g;
  }

  /* End of Switch: '<S3>/Any hall pulse change heals the error' */

  /* Outputs for Atomic SubSystem: '<S3>/ConsecutiveMotorblocksDetection' */
  BlockDet_Mdl_ConsecutiveMotorblocksDetection(rtb_Detectioncondition,
    rtb_Compare_hi, rtu_hCurPos, rtu_hMovPosErr, rtu_nCounterLimit,
    rtu_stStallType, rtu_stReversal, rtu_newPulsesDetected, rtu_resetCounter,
    &BlockDet_Mdl_B.isDoubleStallFault);

  /* End of Outputs for SubSystem: '<S3>/ConsecutiveMotorblocksDetection' */

  /* Switch: '<S3>/Switch' incorporates:
   *  Switch: '<S3>/Switch1'
   */
  if (BlockDet_Mdl_B.LogicalOperator) {
    *rty_stFltType = rtu_SamePosStallFault_C;
  } else if (BlockDet_Mdl_B.isDoubleStallFault) {
    /* Switch: '<S3>/Switch1' */
    *rty_stFltType = rtu_DoubleStallFault_C;
  } else {
    *rty_stFltType = rtu_NoFault_C;
  }

  /* End of Switch: '<S3>/Switch' */

  /* Update for UnitDelay: '<S11>/Delay Input1'
   *
   * Block description for '<S11>/Delay Input1':
   *
   *  Store in Global RAM
   */
  BlockDet_Mdl_DW.DelayInput1_DSTATE_c = *rtu_uiHallCountsCyclic;

  /* Update for Switch: '<S3>/As long as error cannot be deteremined use old value' incorporates:
   *  UnitDelay: '<S3>/Unit Delay'
   */
  BlockDet_Mdl_DW.UnitDelay_DSTATE_g = *rty_stStallFlt;
}

/* Output and update for atomic system: '<S2>/Block_Det' */
void BlockDet_Mdl_Block_Det(const CtrlLog_tenTargetDirection *rtu_stMtrCtrlCmd,
  const uint8_T *rtu_uiHallCountsCyclic, BlockDet_Stall_t rtu_DecStall_C,
  uint16_T rtu_tBlockedTime_P, BlockDet_Stall_t rtu_NoStall_C, BlockDet_Stall_t
  rtu_IncStall_C, const uint16_T *rtu_hCurPos, uint32_T rtu_rPusleTimeSum, const
  uint16_T *rtu_newSoftStop, const boolean_T *rtu_isLearnComplete, const
  VehCom_Cmd_t *rtu_ComLog_stActvMovCmd, const CtrlLog_RelearnMode_t
  *rtu_stRelearnMode, uint16_T rtu_posSoftStallThreshold, const uint16_T
  rtu_thresholdValueDetection[2], uint16_T rtu_hMaxPosSoftStopClose,
  BlockDet_Stall_dir *rty_stStallDir, uint16_T *rty_hStallPos, BlockDet_Stall_t *
  rty_stStallType, boolean_T *rty_newPulsesDetected)
{
  int32_T rtb_MultiportSwitch2;
  int32_T tmp;
  uint32_T tmp_0;
  uint16_T rtb_Switch4;
  uint16_T rtb_UnitDelay2;
  boolean_T rtb_RelationalOperator5;

  /* Logic: '<S4>/Logical Operator' incorporates:
   *  Constant: '<S21>/Constant'
   *  Constant: '<S23>/Constant'
   *  RelationalOperator: '<S21>/Compare'
   *  RelationalOperator: '<S23>/Compare'
   *  RelationalOperator: '<S29>/FixPt Relational Operator'
   *  RelationalOperator: '<S4>/Relational Operator'
   *  UnitDelay: '<S29>/Delay Input1'
   *  UnitDelay: '<S4>/Unit Delay1'
   *
   * Block description for '<S29>/Delay Input1':
   *
   *  Store in Global RAM
   */
  *rty_newPulsesDetected = ((*rtu_stMtrCtrlCmd ==
    CtrlLog_tenTargetDirection_Stop_C) || (*rtu_stMtrCtrlCmd ==
    CtrlLog_tenTargetDirection_None_C) || (*rtu_stMtrCtrlCmd !=
    BlockDet_Mdl_DW.UnitDelay1_DSTATE) || (*rtu_uiHallCountsCyclic !=
    BlockDet_Mdl_DW.DelayInput1_DSTATE_h));

  /* Switch: '<S4>/Switch' */
  if (*rty_newPulsesDetected) {
    /* Switch: '<S4>/Switch' incorporates:
     *  Constant: '<S4>/ResetValue'
     */
    BlockDet_Mdl_DW.UnitDelay_DSTATE = 0U;
  } else {
    /* Sum: '<S4>/Sum' incorporates:
     *  Constant: '<S4>/Counter'
     *  UnitDelay: '<S4>/Unit Delay'
     */
    tmp_0 = BlockDet_Mdl_DW.UnitDelay_DSTATE + 1U;
    if (BlockDet_Mdl_DW.UnitDelay_DSTATE + 1U > 65535U) {
      tmp_0 = 65535U;
    }

    /* Switch: '<S4>/Switch' incorporates:
     *  Sum: '<S4>/Sum'
     */
    BlockDet_Mdl_DW.UnitDelay_DSTATE = (uint16_T)tmp_0;
  }

  /* End of Switch: '<S4>/Switch' */

  /* Product: '<S4>/Divide' incorporates:
   *  Constant: '<S4>/Constant7'
   */
  rtb_UnitDelay2 = (uint16_T)div_u32_round(rtu_tBlockedTime_P, ((uint16_T)
    RoofSys_tSmpBlockDet_SC));

  /* MultiPortSwitch: '<S4>/Multiport Switch2' incorporates:
   *  Constant: '<S4>/Constant1'
   */
  switch (*rtu_stMtrCtrlCmd) {
   case CtrlLog_tenTargetDirection_Open_C:
    rtb_MultiportSwitch2 = rtu_thresholdValueDetection[0];

    /* Sum: '<S4>/Subtract' incorporates:
     *  MultiPortSwitch: '<S4>/Multiport Switch1'
     */
    tmp = *rtu_newSoftStop - rtu_posSoftStallThreshold;
    if (tmp < 0) {
      tmp = 0;
    }

    /* RelationalOperator: '<S4>/Relational Operator3' incorporates:
     *  MultiPortSwitch: '<S4>/Multiport Switch1'
     *  Sum: '<S4>/Subtract'
     */
    rtb_RelationalOperator5 = (*rtu_hCurPos < (uint16_T)tmp);

    /* MultiPortSwitch: '<S4>/Multiport Switch1' incorporates:
     *  Logic: '<S4>/Logical Operator7'
     */
    rtb_RelationalOperator5 = ((*rtu_isLearnComplete) && rtb_RelationalOperator5);
    break;

   case CtrlLog_tenTargetDirection_Close_C:
    /* Logic: '<S4>/Logical Operator8' incorporates:
     *  Constant: '<S26>/Constant'
     *  RelationalOperator: '<S26>/Compare'
     */
    rtb_RelationalOperator5 = ((*rtu_stRelearnMode ==
      CtrlLog_RelearnMode_t_LearningPos_C) || (*rtu_isLearnComplete));

    /* Switch: '<S4>/Switch4' */
    if (rtb_RelationalOperator5) {
      rtb_Switch4 = rtu_thresholdValueDetection[1];
    } else {
      rtb_Switch4 = rtu_thresholdValueDetection[0];
    }

    /* End of Switch: '<S4>/Switch4' */
    rtb_MultiportSwitch2 = rtb_Switch4;

    /* MultiPortSwitch: '<S4>/Multiport Switch1' incorporates:
     *  RelationalOperator: '<S4>/Relational Operator4'
     */
    rtb_RelationalOperator5 = (*rtu_hCurPos > rtu_posSoftStallThreshold);
    break;

   default:
    rtb_MultiportSwitch2 = 10000;

    /* MultiPortSwitch: '<S4>/Multiport Switch1' incorporates:
     *  Constant: '<S4>/Constant'
     *  Constant: '<S4>/Constant1'
     */
    rtb_RelationalOperator5 = true;
    break;
  }

  /* End of MultiPortSwitch: '<S4>/Multiport Switch2' */

  /* Chart: '<S4>/Chart' */
  if (BlockDet_Mdl_DW.temporalCounter_i1 < 63U) {
    BlockDet_Mdl_DW.temporalCounter_i1++;
  }

  BlockDet_Mdl_DW.stMtrCtrlCmd_prev = BlockDet_Mdl_DW.stMtrCtrlCmd_start;
  BlockDet_Mdl_DW.stMtrCtrlCmd_start = *rtu_stMtrCtrlCmd;
  if (BlockDet_Mdl_DW.is_active_c1_BlockDet_Mdl == 0U) {
    BlockDet_Mdl_DW.stMtrCtrlCmd_prev = *rtu_stMtrCtrlCmd;
    BlockDet_Mdl_DW.is_active_c1_BlockDet_Mdl = 1U;
    BlockDet_Mdl_DW.is_c1_BlockDet_Mdl = BlockDet_Mdl_IN_idle_e;
    BlockDet_Mdl_B.isBlockDisabled = true;
  } else {
    switch (BlockDet_Mdl_DW.is_c1_BlockDet_Mdl) {
     case BlockDet_Mdl_IN_idle_e:
      BlockDet_Mdl_B.isBlockDisabled = true;
      if (BlockDet_Mdl_DW.stMtrCtrlCmd_prev !=
          BlockDet_Mdl_DW.stMtrCtrlCmd_start) {
        BlockDet_Mdl_DW.is_c1_BlockDet_Mdl = BlockDet_Mdl_IN_start;
        BlockDet_Mdl_DW.temporalCounter_i1 = 0U;
      }
      break;

     case BlockDet_Mdl_IN_moving:
      BlockDet_Mdl_B.isBlockDisabled = false;
      if ((*rtu_stMtrCtrlCmd == CtrlLog_tenTargetDirection_None_C) ||
          (*rtu_stMtrCtrlCmd == CtrlLog_tenTargetDirection_Stop_C)) {
        BlockDet_Mdl_DW.is_c1_BlockDet_Mdl = BlockDet_Mdl_IN_idle_e;
        BlockDet_Mdl_B.isBlockDisabled = true;
      } else if (BlockDet_Mdl_DW.stMtrCtrlCmd_prev !=
                 BlockDet_Mdl_DW.stMtrCtrlCmd_start) {
        BlockDet_Mdl_DW.is_c1_BlockDet_Mdl = BlockDet_Mdl_IN_start;
        BlockDet_Mdl_DW.temporalCounter_i1 = 0U;
        BlockDet_Mdl_B.isBlockDisabled = true;
      }
      break;

     default:
      /* case IN_start: */
      BlockDet_Mdl_B.isBlockDisabled = true;
      if (BlockDet_Mdl_DW.temporalCounter_i1 >= 60) {
        BlockDet_Mdl_DW.is_c1_BlockDet_Mdl = BlockDet_Mdl_IN_moving;
        BlockDet_Mdl_B.isBlockDisabled = false;
      } else if ((*rtu_stMtrCtrlCmd == CtrlLog_tenTargetDirection_None_C) ||
                 (*rtu_stMtrCtrlCmd == CtrlLog_tenTargetDirection_Stop_C)) {
        BlockDet_Mdl_DW.is_c1_BlockDet_Mdl = BlockDet_Mdl_IN_idle_e;
      } else if (BlockDet_Mdl_DW.stMtrCtrlCmd_prev !=
                 BlockDet_Mdl_DW.stMtrCtrlCmd_start) {
        BlockDet_Mdl_DW.is_c1_BlockDet_Mdl = BlockDet_Mdl_IN_start;
        BlockDet_Mdl_DW.temporalCounter_i1 = 0U;
      }
      break;
    }
  }

  /* End of Chart: '<S4>/Chart' */

  /* Switch: '<S4>/Switch3' incorporates:
   *  Constant: '<S24>/Constant'
   *  Constant: '<S25>/Constant'
   *  Logic: '<S4>/Logical Operator6'
   *  RelationalOperator: '<S24>/Compare'
   *  RelationalOperator: '<S25>/Compare'
   */
  if (rtb_RelationalOperator5 || BlockDet_Mdl_B.isBlockDisabled ||
      (*rtu_ComLog_stActvMovCmd == VehCom_Cmd_t_ManOpnCont_C) ||
      (*rtu_ComLog_stActvMovCmd == VehCom_Cmd_t_ManClsCont_C)) {
    /* Switch: '<S4>/Switch3' */
    BlockDet_Mdl_DW.UnitDelay3_DSTATE = rtu_rPusleTimeSum;
  }

  /* End of Switch: '<S4>/Switch3' */

  /* Sum: '<S4>/Subtract1' */
  BlockDet_Mdl_B.DetectionDifference = (int32_T)rtu_rPusleTimeSum - (int32_T)
    BlockDet_Mdl_DW.UnitDelay3_DSTATE;

  /* RelationalOperator: '<S4>/Relational Operator6' */
  rtb_RelationalOperator5 = (rtu_hMaxPosSoftStopClose >
    rtu_posSoftStallThreshold);

  /* RelationalOperator: '<S4>/Relational Operator5' */
  tmp = BlockDet_Mdl_B.DetectionDifference;
  if (BlockDet_Mdl_B.DetectionDifference < 0) {
    tmp = 0;
  }

  /* Switch: '<S4>/BlockDetected' incorporates:
   *  Constant: '<S20>/Constant'
   *  Constant: '<S27>/Constant'
   *  Constant: '<S28>/Constant'
   *  Logic: '<S4>/Logical Operator2'
   *  Logic: '<S4>/Logical Operator3'
   *  Logic: '<S4>/Logical Operator4'
   *  Logic: '<S4>/Logical Operator5'
   *  Logic: '<S4>/Logical Operator9'
   *  RelationalOperator: '<S20>/Compare'
   *  RelationalOperator: '<S27>/Compare'
   *  RelationalOperator: '<S28>/Compare'
   *  RelationalOperator: '<S4>/Relational Operator1'
   *  RelationalOperator: '<S4>/Relational Operator5'
   */
  if (((BlockDet_Mdl_DW.UnitDelay_DSTATE > rtb_UnitDelay2) || (((uint32_T)
         rtb_MultiportSwitch2 <= (uint32_T)tmp) && (rtb_RelationalOperator5 || (*
          rtu_stRelearnMode == CtrlLog_RelearnMode_t_Renorm_C) ||
         (*rtu_stRelearnMode == CtrlLog_RelearnMode_t_LearningPos_C) ||
         (*rtu_stRelearnMode == CtrlLog_RelearnMode_t_ReqLearn_C)))) &&
      (!BlockDet_Mdl_B.isBlockDisabled)) {
    /* Switch: '<S4>/Switch2' incorporates:
     *  Constant: '<S22>/Constant'
     *  RelationalOperator: '<S22>/Compare'
     */
    if (*rtu_stMtrCtrlCmd == CtrlLog_tenTargetDirection_Open_C) {
      *rty_stStallType = rtu_IncStall_C;
    } else {
      *rty_stStallType = rtu_DecStall_C;
    }

    /* End of Switch: '<S4>/Switch2' */
  } else {
    *rty_stStallType = rtu_NoStall_C;
  }

  /* End of Switch: '<S4>/BlockDetected' */

  /* RelationalOperator: '<S30>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S30>/Delay Input1'
   *
   * Block description for '<S30>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_RelationalOperator5 = (*rty_stStallType !=
    BlockDet_Mdl_DW.DelayInput1_DSTATE_n);

  /* MultiPortSwitch: '<S4>/Multiport Switch3' incorporates:
   *  Constant: '<S4>/Constant2'
   *  Constant: '<S4>/Constant3'
   *  Constant: '<S4>/Constant4'
   */
  switch (*rty_stStallType) {
   case BlockDet_Stall_t_NoStall_C:
    *rty_stStallDir = BlockDet_Stall_dir_NoMov_C;
    break;

   case BlockDet_Stall_t_IncStall_C:
    *rty_stStallDir = BlockDet_Stall_dir_Open_C;
    break;

   default:
    *rty_stStallDir = BlockDet_Stall_dir_Close_C;
    break;
  }

  /* End of MultiPortSwitch: '<S4>/Multiport Switch3' */

  /* Switch: '<S4>/Switch1' incorporates:
   *  Logic: '<S4>/Logical Operator1'
   *  RelationalOperator: '<S4>/Relational Operator2'
   *  UnitDelay: '<S4>/Unit Delay2'
   */
  if (rtb_RelationalOperator5 && (*rty_stStallType != rtu_NoStall_C)) {
    *rty_hStallPos = *rtu_hCurPos;
  } else {
    *rty_hStallPos = BlockDet_Mdl_DW.UnitDelay2_DSTATE;
  }

  /* End of Switch: '<S4>/Switch1' */

  /* Update for UnitDelay: '<S4>/Unit Delay1' */
  BlockDet_Mdl_DW.UnitDelay1_DSTATE = *rtu_stMtrCtrlCmd;

  /* Update for UnitDelay: '<S29>/Delay Input1'
   *
   * Block description for '<S29>/Delay Input1':
   *
   *  Store in Global RAM
   */
  BlockDet_Mdl_DW.DelayInput1_DSTATE_h = *rtu_uiHallCountsCyclic;

  /* Update for UnitDelay: '<S30>/Delay Input1'
   *
   * Block description for '<S30>/Delay Input1':
   *
   *  Store in Global RAM
   */
  BlockDet_Mdl_DW.DelayInput1_DSTATE_n = *rty_stStallType;

  /* Update for UnitDelay: '<S4>/Unit Delay2' */
  BlockDet_Mdl_DW.UnitDelay2_DSTATE = *rty_hStallPos;
}

/* System initialize for referenced model: 'BlockDet_Mdl' */
void BlockDet_Mdl_Init(void)
{
  /* SystemInitialize for Atomic SubSystem: '<S2>/BlockFault_Determination' */
  BlockDet_Mdl_BlockFault_Determination_Init();

  /* End of SystemInitialize for SubSystem: '<S2>/BlockFault_Determination' */
}

/* Output and update for referenced model: 'BlockDet_Mdl' */
void BlockDet_Mdl(const uint16_T *rtu_LearnAdapBus_LearnAdap_newSoftStop, const
                  boolean_T *rtu_LearnAdapBus_LearnAdap_isLearnComplete, const
                  PnlOp_Rev_t *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_stReversal,
                  const CtrlLog_RelearnMode_t
                  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode, const
                  CtrlLog_tenTargetDirection
                  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const
                  VehCom_Cmd_t
                  *rtu_SWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd, const
                  uint16_T *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, const
                  SWC_HwAbsLyrBus *rtu_SWC_HwAbsLyrBus, BlockDet_Stall_t
                  *rty_BlockDetBus_BlockDet_stStallType, uint8_T
                  *rty_BlockDetBus_BlockDet_stStallFlt, BlockDet_FltType_t
                  *rty_BlockDetBus_BlockDet_stFltType, uint16_T
                  *rty_BlockDetBus_BlockDet_hStallPos, BlockDet_Stall_dir
                  *rty_BlockDetBus_BlockDet_stStallDir)
{
  boolean_T rtb_LogicalOperator_c;
  boolean_T rtb_newPulsesDetected;

  /* Outputs for Atomic SubSystem: '<S2>/Block_Det' */
  /* Constant: '<Root>/Constant6' incorporates:
   *  Constant: '<Root>/Constant2'
   *  Constant: '<Root>/Constant3'
   *  DataStoreRead: '<Root>/Data Store Read1'
   *  DataStoreRead: '<Root>/Data Store Read2'
   *  DataStoreRead: '<Root>/Data Store Read4'
   */
  BlockDet_Mdl_Block_Det(rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand,
    &rtu_SWC_HwAbsLyrBus->HallDecoBus.HallDeco_uiHallCountsCyclic,
    BlockDet_Stall_t_DecStall_C, BlockDet_tBlockedTime_P,
    BlockDet_Stall_t_NoStall_C, BlockDet_Stall_t_IncStall_C,
    rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, HallDeco_sumFilterdPulseTicks,
    rtu_LearnAdapBus_LearnAdap_newSoftStop,
    rtu_LearnAdapBus_LearnAdap_isLearnComplete,
    rtu_SWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd,
    rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode,
    CfgParam_CAL.BlockDet_posSoftStallThreshold,
    &CfgParam_CAL.BlockDet_thresholdValueDetection[0],
    CfgParam_CAL.hMaxPosSoftStopClose, rty_BlockDetBus_BlockDet_stStallDir,
    rty_BlockDetBus_BlockDet_hStallPos, rty_BlockDetBus_BlockDet_stStallType,
    &rtb_newPulsesDetected);

  /* End of Outputs for SubSystem: '<S2>/Block_Det' */

  /* Logic: '<S6>/Logical Operator' incorporates:
   *  Constant: '<S6>/Constant2'
   *  RelationalOperator: '<S32>/FixPt Relational Operator'
   *  RelationalOperator: '<S6>/Relational Operator'
   *  UnitDelay: '<S32>/Delay Input1'
   *
   * Block description for '<S32>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_LogicalOperator_c = ((*rty_BlockDetBus_BlockDet_stStallDir !=
    BlockDet_Mdl_DW.DelayInput1_DSTATE_jw) &&
    (*rty_BlockDetBus_BlockDet_stStallDir != BlockDet_Stall_dir_NoMov_C));

  /* Outputs for Triggered SubSystem: '<S6>/writeStallDirToNVM' incorporates:
   *  TriggerPort: '<S35>/Trigger'
   */
  if (rtb_LogicalOperator_c && (BlockDet_Mdl_PrevZCX.writeStallDirToNVM_Trig_ZCE
       != POS_ZCSIG)) {
    /* DataStoreWrite: '<S35>/Data Store Write1' */
    BlockDet_stStallDir = *rty_BlockDetBus_BlockDet_stStallDir;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S35>/S-Function' incorporates:
     *  Constant: '<S35>/Constant5'
     */
    /* S-Function Block: <S35>/S-Function */
    BlockDet_Mdl_B.SFunction = NVMMan_enRequestNVMWrite_new(49U);
  }

  BlockDet_Mdl_PrevZCX.writeStallDirToNVM_Trig_ZCE = rtb_LogicalOperator_c;

  /* End of Outputs for SubSystem: '<S6>/writeStallDirToNVM' */

  /* RelationalOperator: '<S33>/FixPt Relational Operator' incorporates:
   *  UnitDelay: '<S33>/Delay Input1'
   *
   * Block description for '<S33>/Delay Input1':
   *
   *  Store in Global RAM
   */
  rtb_LogicalOperator_c = (*rty_BlockDetBus_BlockDet_hStallPos !=
    BlockDet_Mdl_DW.DelayInput1_DSTATE);

  /* Outputs for Triggered SubSystem: '<S6>/writeStallPosToNVM' incorporates:
   *  TriggerPort: '<S36>/Trigger'
   */
  if (rtb_LogicalOperator_c && (BlockDet_Mdl_PrevZCX.writeStallPosToNVM_Trig_ZCE
       != POS_ZCSIG)) {
    /* DataStoreWrite: '<S36>/Data Store Write' */
    BlockDet_hStallPos = *rty_BlockDetBus_BlockDet_hStallPos;

    /* S-Function (NVMMan_enRequestNVMWrite_new): '<S36>/S-Function1' incorporates:
     *  Constant: '<S36>/Constant6'
     */
    /* S-Function Block: <S36>/S-Function1 */
    BlockDet_Mdl_B.SFunction1 = NVMMan_enRequestNVMWrite_new(50U);
  }

  BlockDet_Mdl_PrevZCX.writeStallPosToNVM_Trig_ZCE = rtb_LogicalOperator_c;

  /* End of Outputs for SubSystem: '<S6>/writeStallPosToNVM' */

  /* Outputs for Atomic SubSystem: '<S2>/BlockFault_Determination' */
  /* DataStoreRead: '<Root>/Data Store Read2' incorporates:
   *  Constant: '<Root>/Constant'
   *  Constant: '<Root>/Constant1'
   *  Constant: '<Root>/Constant10'
   *  Constant: '<Root>/Constant5'
   *  Constant: '<Root>/Constant7'
   *  Constant: '<Root>/nCounterLimit'
   *  Constant: '<S2>/resetCounter'
   */
  BlockDet_Mdl_BlockFault_Determination(*rty_BlockDetBus_BlockDet_stStallType,
    rtb_newPulsesDetected,
    &rtu_SWC_HwAbsLyrBus->HallDecoBus.HallDeco_uiHallCountsCyclic,
    rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode,
    CfgParam_CAL.hMovPosErr, rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos,
    rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_stReversal, 1, 2, 3,
    BlockDet_FltType_t_SamePosStallFault_C,
    BlockDet_FltType_t_DoubleStallFault_C, BlockDet_FltType_t_NoFault_C, 0,
    rty_BlockDetBus_BlockDet_stStallFlt, rty_BlockDetBus_BlockDet_stFltType);

  /* End of Outputs for SubSystem: '<S2>/BlockFault_Determination' */

  /* Update for UnitDelay: '<S32>/Delay Input1'
   *
   * Block description for '<S32>/Delay Input1':
   *
   *  Store in Global RAM
   */
  BlockDet_Mdl_DW.DelayInput1_DSTATE_jw = *rty_BlockDetBus_BlockDet_stStallDir;

  /* Update for UnitDelay: '<S33>/Delay Input1'
   *
   * Block description for '<S33>/Delay Input1':
   *
   *  Store in Global RAM
   */
  BlockDet_Mdl_DW.DelayInput1_DSTATE = *rty_BlockDetBus_BlockDet_hStallPos;
}

/* Model initialize function */
void BlockDet_Mdl_initialize(const char_T **rt_errorStatus)
{
  RT_MODEL_BlockDet_Mdl_T *const BlockDet_Mdl_M = &(BlockDet_Mdl_MdlrefDW.rtm);

  /* Registration code */

  /* initialize error status */
  rtmSetErrorStatusPointer(BlockDet_Mdl_M, rt_errorStatus);
  BlockDet_Mdl_PrevZCX.StallTypeCheck_Trig_ZCE = POS_ZCSIG;
  BlockDet_Mdl_PrevZCX.writeStallDirToNVM_Trig_ZCE = POS_ZCSIG;
  BlockDet_Mdl_PrevZCX.writeStallPosToNVM_Trig_ZCE = POS_ZCSIG;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
