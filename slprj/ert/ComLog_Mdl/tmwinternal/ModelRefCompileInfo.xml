<?xml version="1.0" encoding="UTF-8"?>
<MF0 version="1.1" packageUris="http://schema.mathworks.com/mf0/ci/19700101 http://schema.mathworks.com/mf0/sl_modelref_info/R2022b http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112 http://schema.mathworks.com/mf0/sltp_mm/R2022b_202203181029">
  <ModelRefInfoRepo.ModelRefInfoRoot type="ModelRefInfoRepo.ModelRefInfoRoot" uuid="e00b77f8-b560-4f3f-9ee0-4dc6e9dfdc2b">
    <JITEngines>sGNX6SmFLkwWzhg2U7glLwG</JITEngines>
    <childModelRefInfo type="ModelRefInfoRepo.ChildModelRefInfo" uuid="16114065-2b3a-4c02-ac37-fb9a98a6756c">
      <isSingleInstance>true</isSingleInstance>
      <modelName>ComLog_Mdl</modelName>
      <modelPath>ComLog_Mdl</modelPath>
    </childModelRefInfo>
    <compDerivCacheNeedsReset>false</compDerivCacheNeedsReset>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="399e07ec-01d9-47ec-b65c-f104a011ac6e">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a0b7a272-8f04-4824-877e-826b1dbcb35b">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="279a7241-af48-4ed3-bbbc-d3ff24d15ad6">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ce919fb8-746f-47e6-8d95-4678b9cd3b0c">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8d170143-d8f5-49ab-b944-2924499ed603">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0ab023cd-c5b3-48b9-8d21-c5e90f0c123e">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="47ac6485-b5d6-4a25-aa0e-068f5ddcdacd">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b0bac91e-6957-4d0d-b2b3-b7026f397128">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c19f79e1-6774-424d-b8d5-36b4dcfc608a">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0ff601fa-ce81-4f64-8bbe-742bd337c377">
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="48e65d8c-5b25-451f-8b0e-ce35958ee539">
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="595175c6-1524-4a11-bb13-75f54eb772c6">
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="44790eca-3733-4ae8-9c32-0dc0eabeab28">
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="89616aec-644f-4f2f-a782-b52d5163d839">
      <grAndCompPorts>13</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="43f13115-1592-4e96-9e7b-7ac4ce1c5e54">
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="68b027d6-c1fa-4339-a308-84f38d8eadd8">
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1dd9b798-5253-48f6-abf7-95ee2f955998">
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4b59a5c2-9b72-4221-b382-5b856f808352">
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6fb8b597-0dc7-4c93-8700-bbea2cf49612">
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1c745c18-79df-41e9-a225-e5eb330b29d4">
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9f282ab0-f754-4fce-b419-8723be0e7882">
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2a4e2923-4647-4369-97c5-257f9047019b">
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b9eeba3c-7420-4f03-85f9-435a5ac56b08">
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="60fbb114-df2a-49a4-9176-81045ea73d3a">
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="dea2fa8b-32e3-45cf-995f-8c347b240d2b">
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7fa21117-77f3-457e-bfe7-9d2b5e971e3c">
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8277dd01-5fe0-4f7a-b612-86743f18fa19">
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="17c2cb60-ab86-4e9c-a0e3-d7c87fbc49ef">
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8fc5ebed-c64f-455c-b5f3-68a777a5bedd">
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="52169303-baeb-44d3-b37f-5768155d2270">
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="07707f83-6310-4f94-be92-12f6514fa951">
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6f247747-238d-4a15-8fb4-caebac1b2690">
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4d7166de-0ce6-4c52-a67a-607ac8fdce36">
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4c0f69c5-c0f7-40b2-bbd3-6ec38249d7ba">
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="92e2db88-bc55-483f-9d77-b872a8d48428">
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bda28a2d-56ec-4cdf-956d-5fa3827e478c">
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d10440b6-25e4-4ddb-b473-5d2d6401454b">
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="57e3ff05-7bcd-4f72-8adf-c46a4c1f9f94">
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="7a59278d-f7c0-4383-af21-a1e64d28d929">
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="45b88150-313f-4750-91a4-eabb4cae3229">
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a75542a4-2c10-46a0-8969-414117890ee5">
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8f8d3173-9da1-4220-884e-d5f98ecc570d">
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6c403dab-7c07-41a4-990d-5ee876ba5f2d">
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="13724df3-4eff-43a9-8d85-8bb704044ac8">
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="08312a36-bd6e-4468-a962-0f416d4aa398">
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5bba7d67-cb49-4ffd-aa4c-e6979663b53d">
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="747de6e8-3e4b-48c3-a770-fdfcf3c628f1">
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ce892318-9fae-4630-80a0-243ae1ca41ce">
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2ea68c41-60cd-4a27-b718-07ed44b22090">
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="908edc27-9130-47cd-8a12-fd640e789d7d">
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5289b0af-bb16-445b-845a-5fec6a263c12">
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e9dd0231-c7dd-47ee-96f1-12a793dcbf21">
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5237ca65-796f-4e5b-9942-fa20219429d3">
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4ffb263d-d9da-49a6-876c-11ec37ec5268">
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b38d951a-6bd9-4d97-9db7-d498b0f61a3c">
      <grAndCompPorts>54</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8d946ade-cc1d-479d-b9b8-5f0cb2e9e9e6">
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a9b0bb15-864b-4345-8c74-e7f2c73337f6">
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="712a233e-609f-4594-9253-77521e97f5e4">
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c89c9ca5-4df6-4eda-9f84-6fbf507ef871">
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b26017e5-f330-40b1-b9ba-c8b9e03022c4">
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d9d1b85b-8bb5-48ef-8115-30161ede1c0f">
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f77fa72c-8ed5-4904-bf31-4e35d293cbbc">
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c3a2ddf2-4f7c-4969-bee1-038e65b06892">
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8e25e7de-d54f-476a-9291-832b5cff400b">
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9aebc334-094a-4cb1-a305-26480693d84e">
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4b41bcf2-13c3-4cae-bf93-c8e537dc4285">
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d5bd8957-85e1-4af1-b271-aed6f851fce6">
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="92b3c8f5-4df0-40a0-8823-6248aa409189">
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="957f4342-84fc-4eca-976a-3605d95193e4">
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6722198f-57e4-4d7e-9067-38790bf49114">
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6cac722d-0491-489b-8318-da8cac7fdc9f">
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="296cddac-24c4-4bb8-9e84-1c9d884104b1">
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2ffb73d3-3f03-4e54-86a4-402445ae1299">
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ace9a925-f0c4-4239-a502-342e72be2c70">
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="eeb7ad74-8f03-444a-a970-970608bc7ef0">
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="da87968d-bace-4e51-a67f-c8fdec0a6bb1">
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9d58f734-b889-4e12-a0e3-17a4cf3cd30b">
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="605dbc65-3dda-4f74-9efd-3a7d214b9e11">
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="28570705-6bdd-4fa8-86fb-427ee852bc3d">
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8c3885c5-0a54-4073-ac86-1d8af4d7e723">
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="441773c5-f75a-4176-ac45-03e299b1a340">
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a09b0d32-4687-41ef-a3ab-2b3cf6f6a231">
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d60d636a-823d-40c1-879c-2112102c3771">
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cd863760-55f9-40af-8cbc-2c0e0fe8d1a6">
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3d704791-7eb7-48c3-934c-3182d08d7882">
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="840f7d2a-cc2a-4e0a-96b6-8085c43e42cb">
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8a123260-d8bc-4bfa-86ea-148a08cad34f">
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9212df3d-47c3-418d-b6e5-208345b2542b">
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="abe05927-1299-45fb-82f7-ba8a631e571c">
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="fc8a02bb-11ca-4d32-abce-9773dbea767b">
      <grAndCompPorts>89</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="83fd5ebf-cc0a-42cb-ba42-859ac951c846">
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4215d708-ba5a-4bf3-8376-f513327bdcd0">
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4487ba5d-82f8-4e03-8c88-5595239ecd9f">
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cee7336c-7bc2-4299-8aaa-6b08559cca5e">
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6fc7f86b-5908-4d48-80e2-6dabe792bb32">
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b69ddae7-d808-437e-8654-a3ee2f42ac19">
      <grAndCompPorts>95</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1f68e02d-ec8e-4ddd-8c84-0a8a0efe12c6">
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="43e686eb-9eee-4374-9174-a2760189e44a">
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a749f664-e884-44dc-afa3-1d4e0856427d">
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="443f2c44-dfe0-4c92-a3ef-4bd325c99b27">
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b0c121f6-0432-4725-bc5d-529227774f74">
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ca9fa720-3a39-4b1e-9345-a99672c88c1a">
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0d2b6cb0-1816-4eac-a258-1f5fefa5ac90">
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9ea82241-419d-45d5-a848-3044fd285dae">
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5796abe2-5e5b-4f70-8912-0c93f161f8f3">
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="69c79664-09c9-4d8c-b33b-28f9175d904f">
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="11653bf7-1665-4a62-ba07-e785e946ae97">
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="13743860-1d48-4a99-8a06-e223fc0e2a10">
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a94ba108-b19a-4784-b5e2-5fd21f83b2d5">
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c6d68500-a56f-48da-81be-5579b4618762">
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="05233ffe-994f-4a82-9af9-358bcb6494c6">
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9b0324e6-2bf2-417f-b29c-9b436f445a83">
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2f06f53b-6f0c-49bd-9498-045bd2b1d52b">
      <grAndCompPorts>112</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="93ee42ae-c041-40bf-ac7a-a24a2d39bc0a">
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9555c8a6-46c7-4ac3-9f39-dc4ab3906992">
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="560d6ca1-36b5-4a0f-a5e5-0fdf1aa89098">
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="00b602d8-f907-41f3-ae9f-a70555d2c0df">
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="90ebf2dd-9d28-4a6b-89ca-c71a8fb8cc78">
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1d545a67-ff0d-4ce1-9de8-8f3d02bca03f">
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="193ed719-5a79-4a89-a1f9-c28fe782116a">
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="746aeee0-2f10-4530-be6e-7115389af6af">
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ce97b7ae-d27f-448b-91af-6da67e036e0a">
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="4ddbcc67-b92c-4a82-8504-fd405f24fb07">
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ebc4ec7a-a115-4a1d-97e5-2d768dafc95f">
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8744fa0b-440b-4ef0-9ad0-5b89bf1d5178">
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="aa151248-3d39-4a9d-93fa-9dd86b3a7d8e">
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0c937442-7460-41ca-ab1e-ec29f2df8b6b">
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="22e8f0a0-ed00-4d30-8de5-813e41824009">
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="12580154-8169-40a7-a8ae-b06ff7d7afcf">
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cfa947f1-a61c-4b15-a347-f5b7e910b4a3">
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="06fc3356-7777-4c80-9a6f-da755f827790">
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="a41eb79e-6286-4056-a33b-7146c4db339d">
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="9661de00-d104-4f5c-9927-37fb26a70ad2">
      <grAndCompPorts>132</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5be07872-38e3-44ac-b896-1e505e79d8c2">
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="010fcfbc-9d6e-4fc8-9904-7d50aa9fd96b">
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0744327a-7229-4a2a-a5a3-231fa27bbda7">
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="cada9cd4-7f32-46a0-9473-bc489698e5b9">
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="78352666-e719-42f8-8f27-1901f74b3346">
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b8b4ab86-c330-4281-8377-b1d55289a4cc">
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="19e80de4-5a45-4667-a4c7-11b20dcaf321">
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6e41c99e-e9cb-4e72-95a2-41f1752beed6">
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="271f4a29-6985-4c02-a265-a871e296dffe">
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8a9e3331-9b62-40f9-81d1-78ee44d7b6ad">
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="48b75c85-a3e1-4ac8-9082-7b5b7fb46758">
      <grAndCompPorts>143</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="83b7e654-cb6c-45de-b587-d317d192fa0b">
      <grAndCompPorts>144</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6d4aed6e-be84-45e0-80ac-e5982bd3bcdd">
      <grAndCompPorts>145</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6d1cab9e-ba7b-4df0-9995-28f43d95b528">
      <grAndCompPorts>146</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="e2715ca9-a642-4332-bae3-b932181801ac">
      <grAndCompPorts>147</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="98a40499-5088-4576-ac25-8fbfa5861ecf">
      <grAndCompPorts>148</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="*************-4b8e-a2cc-6c1e72d75e96">
      <grAndCompPorts>149</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="d5ddd563-4792-4599-965a-438ebf620d3c">
      <grAndCompPorts>150</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="16812ad4-1d1d-4d64-a8e4-9822b197dc5b">
      <grAndCompPorts>151</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1640e64d-bcf9-4035-8ab3-a19ec622f821">
      <grAndCompPorts>152</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ebfe453a-d670-4806-a521-989bd5a6f7e9">
      <grAndCompPorts>153</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b9fcc566-6ac8-4817-8e4b-5e10b0cdb4db">
      <grAndCompPorts>154</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="6a7831c7-39d3-4d52-99b6-5ac72817df9f">
      <grAndCompPorts>155</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="ad70d78c-181e-4fb7-8c03-5c9fd43046fd">
      <grAndCompPorts>156</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0e276f27-1ed5-4652-9831-9923c11a0dd8">
      <grAndCompPorts>157</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b1479035-0d45-4142-8e46-9f07526cf4a1">
      <grAndCompPorts>158</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="06b75cc8-80aa-4b62-922a-c660bb3c14e2">
      <grAndCompPorts>159</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5554129e-d246-4046-9834-649a22e09bb3">
      <grAndCompPorts>160</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="20bbdf0b-89e0-485a-b593-2f3998b67cc4">
      <grAndCompPorts>161</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="1b3de502-249b-4dd2-9b82-6dc078c1ebd4">
      <grAndCompPorts>162</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="3c78a457-0e3c-41aa-a63f-57d19c229c16">
      <grAndCompPorts>163</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="21bda1a5-fd45-4eb4-91f5-c0fab05de447">
      <grAndCompPorts>164</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="409e5091-987d-4629-88ba-be9d549233c6">
      <grAndCompPorts>165</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrInputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="bf7da229-69d5-437e-87ae-de605b311207">
      <grAndCompPorts>166</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
    </compToGrInputPortMaps>
    <compToGrOutputPortMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="174f22da-96f6-4826-967e-95dac52d0b10">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </compToGrOutputPortMaps>
    <compZcCacheNeedsReset>false</compZcCacheNeedsReset>
    <dataDictionary>ComLog_Mdl_Data.sldd</dataDictionary>
    <dataDictionarySet>ComLog_Mdl_Data.sldd</dataDictionarySet>
    <dataDictionarySetForDataTypeCheck>ComLog_Mdl_Data.sldd</dataDictionarySetForDataTypeCheck>
    <dataTransferInfos>AAFJTQAAAAAOAAAAOAAAAAYAAAAIAAAAAgAAAAAAAAAFAAAACAAAAAAAAAABAAAAAQAAAAAAAAAFAAQAAQAAAAEAAAAAAAAA</dataTransferInfos>
    <defaultsCMapping>{&quot;Inports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Outports&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ParameterArguments&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;LocalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalParameters&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InternalData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedLocalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;GlobalDataStores&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Constants&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;DataTransfers&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;ModelData&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;InitializeTerminate&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;Execution&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;,&quot;SharedUtility&quot;:&quot;{\&quot;Name\&quot;:\&quot;Default\&quot;,\&quot;Checksum\&quot;:\&quot;Default\&quot;,\&quot;PerInstancePropertiesChecksum\&quot;:\&quot;\&quot;,\&quot;MemorySectionName\&quot;:\&quot;None\&quot;,\&quot;MemorySectionChecksum\&quot;:\&quot;None\&quot;}&quot;}</defaultsCMapping>
    <fundamentalSampleTimePeriod>.2</fundamentalSampleTimePeriod>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="b42af48a-4761-449d-92b4-aadb1b61c466">
      <dataType>CfgParam_CALBus_t</dataType>
      <name>CfgParam_CAL</name>
    </globalDSMInfo>
    <globalDSMInfo type="ModelRefInfoRepo.GlobalDataStoreMemoryInfo" uuid="12b93fc7-bce7-400a-bba7-2b34ac607a46">
      <dataType>VehCom_Cmd_t</dataType>
      <name>PIDDID_stDiagRtnCmd</name>
    </globalDSMInfo>
    <globalVariables>#APDetBus#APDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#AmbTempMonBus#AmbTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#BlockDetBus#BlockDet_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParamBus#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CAL#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_CALBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_MosfetLevels#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_TThermThreshBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#CfgParam_hPosBus_t#CfgParam_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#ComLogBus#ComLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#Configuration#ModelConfig.sldd#</globalVariables>
    <globalVariables>#CtrlLogBus#CtrlLog_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#LearnAdapBus#LearnAdap_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MOSFETTempBus#MOSFETTempMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdlBus#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#MtrMdl_CalcForTorqBus_t#MtrMdl_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDIDBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_ADCMonBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_AmbTempMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_BlockDetBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_CtrlLogBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_DIOCtrlBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_ExtDevBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_HallDecoBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_LearnAdapBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MOSFETTempBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MtrCtrlBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_MtrMdlBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_PnlOpBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_PosMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_StateMachBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_ThermProtBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_UsgHistBus#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PIDDID_VoltMonBus#PIDDID_Mdl_Data.sldd#</globalVariables>
    <globalVariables>#PIDDID_stDiagRtnCmd#PIDDID_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PnlOpBus#PnlOp_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#PosMonBus#PosMon_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefFieldBus#RefField_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#RefForceBus#RefForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#SWC_CommLyrBus#SWC_CommLyr.sldd#</globalVariables>
    <globalVariables>#SWC_DataHndlLyrBus#SWC_DataHndlLyr.sldd#</globalVariables>
    <globalVariables>#SWC_DiagLyrBus#SWC_DiagLyr.sldd#</globalVariables>
    <globalVariables>#SWC_ObjDetLyrBus#SWC_ObjDetLyr.sldd#</globalVariables>
    <globalVariables>#SWC_SigMonLyrBus#SWC_SigMonLyr.sldd#</globalVariables>
    <globalVariables>#SWC_StateMachLyrBus#SWC_StateMachLyr.sldd#</globalVariables>
    <globalVariables>#StateMachBus#StateMach_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#SysFaultReacBus#SysFaultReac_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#ThresForceBus#TheshForce_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#UsgHistBus#UsgHis_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#VehComBus#VehCom_Mdl_ExpData.sldd#</globalVariables>
    <globalVariables>#VoltMonBus#VoltMon_Mdl_ExpData.sldd#</globalVariables>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="14408fd5-0c15-4a4b-88d6-02b60d575ad9">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>6</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="2c27481f-5279-4a87-b295-0fa6a68f5fe7">
      <grAndCompPorts>1</grAndCompPorts>
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>9</grAndCompPorts>
      <grAndCompPorts>10</grAndCompPorts>
      <grAndCompPorts>11</grAndCompPorts>
      <grAndCompPorts>12</grAndCompPorts>
      <grAndCompPorts>13</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="162d4cbb-007a-4483-9b75-bbf352e7ffd6">
      <grAndCompPorts>2</grAndCompPorts>
      <grAndCompPorts>14</grAndCompPorts>
      <grAndCompPorts>15</grAndCompPorts>
      <grAndCompPorts>16</grAndCompPorts>
      <grAndCompPorts>17</grAndCompPorts>
      <grAndCompPorts>18</grAndCompPorts>
      <grAndCompPorts>19</grAndCompPorts>
      <grAndCompPorts>20</grAndCompPorts>
      <grAndCompPorts>21</grAndCompPorts>
      <grAndCompPorts>22</grAndCompPorts>
      <grAndCompPorts>23</grAndCompPorts>
      <grAndCompPorts>24</grAndCompPorts>
      <grAndCompPorts>25</grAndCompPorts>
      <grAndCompPorts>26</grAndCompPorts>
      <grAndCompPorts>27</grAndCompPorts>
      <grAndCompPorts>28</grAndCompPorts>
      <grAndCompPorts>29</grAndCompPorts>
      <grAndCompPorts>30</grAndCompPorts>
      <grAndCompPorts>31</grAndCompPorts>
      <grAndCompPorts>32</grAndCompPorts>
      <grAndCompPorts>33</grAndCompPorts>
      <grAndCompPorts>34</grAndCompPorts>
      <grAndCompPorts>35</grAndCompPorts>
      <grAndCompPorts>36</grAndCompPorts>
      <grAndCompPorts>37</grAndCompPorts>
      <grAndCompPorts>38</grAndCompPorts>
      <grAndCompPorts>39</grAndCompPorts>
      <grAndCompPorts>40</grAndCompPorts>
      <grAndCompPorts>41</grAndCompPorts>
      <grAndCompPorts>42</grAndCompPorts>
      <grAndCompPorts>43</grAndCompPorts>
      <grAndCompPorts>44</grAndCompPorts>
      <grAndCompPorts>45</grAndCompPorts>
      <grAndCompPorts>46</grAndCompPorts>
      <grAndCompPorts>47</grAndCompPorts>
      <grAndCompPorts>48</grAndCompPorts>
      <grAndCompPorts>49</grAndCompPorts>
      <grAndCompPorts>50</grAndCompPorts>
      <grAndCompPorts>51</grAndCompPorts>
      <grAndCompPorts>52</grAndCompPorts>
      <grAndCompPorts>53</grAndCompPorts>
      <grAndCompPorts>54</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="c0be3b67-e13b-46ed-a234-ac93ad5091ed">
      <grAndCompPorts>3</grAndCompPorts>
      <grAndCompPorts>55</grAndCompPorts>
      <grAndCompPorts>56</grAndCompPorts>
      <grAndCompPorts>57</grAndCompPorts>
      <grAndCompPorts>58</grAndCompPorts>
      <grAndCompPorts>59</grAndCompPorts>
      <grAndCompPorts>60</grAndCompPorts>
      <grAndCompPorts>61</grAndCompPorts>
      <grAndCompPorts>62</grAndCompPorts>
      <grAndCompPorts>63</grAndCompPorts>
      <grAndCompPorts>64</grAndCompPorts>
      <grAndCompPorts>65</grAndCompPorts>
      <grAndCompPorts>66</grAndCompPorts>
      <grAndCompPorts>67</grAndCompPorts>
      <grAndCompPorts>68</grAndCompPorts>
      <grAndCompPorts>69</grAndCompPorts>
      <grAndCompPorts>70</grAndCompPorts>
      <grAndCompPorts>71</grAndCompPorts>
      <grAndCompPorts>72</grAndCompPorts>
      <grAndCompPorts>73</grAndCompPorts>
      <grAndCompPorts>74</grAndCompPorts>
      <grAndCompPorts>75</grAndCompPorts>
      <grAndCompPorts>76</grAndCompPorts>
      <grAndCompPorts>77</grAndCompPorts>
      <grAndCompPorts>78</grAndCompPorts>
      <grAndCompPorts>79</grAndCompPorts>
      <grAndCompPorts>80</grAndCompPorts>
      <grAndCompPorts>81</grAndCompPorts>
      <grAndCompPorts>82</grAndCompPorts>
      <grAndCompPorts>83</grAndCompPorts>
      <grAndCompPorts>84</grAndCompPorts>
      <grAndCompPorts>85</grAndCompPorts>
      <grAndCompPorts>86</grAndCompPorts>
      <grAndCompPorts>87</grAndCompPorts>
      <grAndCompPorts>88</grAndCompPorts>
      <grAndCompPorts>89</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="b8ee0ad4-6165-49a5-a1fc-d89e2b0d8d23">
      <grAndCompPorts>4</grAndCompPorts>
      <grAndCompPorts>90</grAndCompPorts>
      <grAndCompPorts>91</grAndCompPorts>
      <grAndCompPorts>92</grAndCompPorts>
      <grAndCompPorts>93</grAndCompPorts>
      <grAndCompPorts>94</grAndCompPorts>
      <grAndCompPorts>95</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="0defb62b-3ade-4097-a4ac-37dfaa070c8a">
      <grAndCompPorts>5</grAndCompPorts>
      <grAndCompPorts>96</grAndCompPorts>
      <grAndCompPorts>97</grAndCompPorts>
      <grAndCompPorts>98</grAndCompPorts>
      <grAndCompPorts>99</grAndCompPorts>
      <grAndCompPorts>100</grAndCompPorts>
      <grAndCompPorts>101</grAndCompPorts>
      <grAndCompPorts>102</grAndCompPorts>
      <grAndCompPorts>103</grAndCompPorts>
      <grAndCompPorts>104</grAndCompPorts>
      <grAndCompPorts>105</grAndCompPorts>
      <grAndCompPorts>106</grAndCompPorts>
      <grAndCompPorts>107</grAndCompPorts>
      <grAndCompPorts>108</grAndCompPorts>
      <grAndCompPorts>109</grAndCompPorts>
      <grAndCompPorts>110</grAndCompPorts>
      <grAndCompPorts>111</grAndCompPorts>
      <grAndCompPorts>112</grAndCompPorts>
      <grAndCompPorts>113</grAndCompPorts>
      <grAndCompPorts>114</grAndCompPorts>
      <grAndCompPorts>115</grAndCompPorts>
      <grAndCompPorts>116</grAndCompPorts>
      <grAndCompPorts>117</grAndCompPorts>
      <grAndCompPorts>118</grAndCompPorts>
      <grAndCompPorts>119</grAndCompPorts>
      <grAndCompPorts>120</grAndCompPorts>
      <grAndCompPorts>121</grAndCompPorts>
      <grAndCompPorts>122</grAndCompPorts>
      <grAndCompPorts>123</grAndCompPorts>
      <grAndCompPorts>124</grAndCompPorts>
      <grAndCompPorts>125</grAndCompPorts>
      <grAndCompPorts>126</grAndCompPorts>
      <grAndCompPorts>127</grAndCompPorts>
      <grAndCompPorts>128</grAndCompPorts>
      <grAndCompPorts>129</grAndCompPorts>
      <grAndCompPorts>130</grAndCompPorts>
      <grAndCompPorts>131</grAndCompPorts>
      <grAndCompPorts>132</grAndCompPorts>
      <grAndCompPorts>133</grAndCompPorts>
      <grAndCompPorts>134</grAndCompPorts>
      <grAndCompPorts>135</grAndCompPorts>
      <grAndCompPorts>136</grAndCompPorts>
      <grAndCompPorts>137</grAndCompPorts>
      <grAndCompPorts>138</grAndCompPorts>
      <grAndCompPorts>139</grAndCompPorts>
      <grAndCompPorts>140</grAndCompPorts>
      <grAndCompPorts>141</grAndCompPorts>
      <grAndCompPorts>142</grAndCompPorts>
      <grAndCompPorts>143</grAndCompPorts>
      <grAndCompPorts>144</grAndCompPorts>
      <grAndCompPorts>145</grAndCompPorts>
      <grAndCompPorts>146</grAndCompPorts>
      <grAndCompPorts>147</grAndCompPorts>
      <grAndCompPorts>148</grAndCompPorts>
      <grAndCompPorts>149</grAndCompPorts>
      <grAndCompPorts>150</grAndCompPorts>
      <grAndCompPorts>151</grAndCompPorts>
      <grAndCompPorts>152</grAndCompPorts>
      <grAndCompPorts>153</grAndCompPorts>
      <grAndCompPorts>154</grAndCompPorts>
      <grAndCompPorts>155</grAndCompPorts>
      <grAndCompPorts>156</grAndCompPorts>
      <grAndCompPorts>157</grAndCompPorts>
      <grAndCompPorts>158</grAndCompPorts>
      <grAndCompPorts>159</grAndCompPorts>
      <grAndCompPorts>160</grAndCompPorts>
      <grAndCompPorts>161</grAndCompPorts>
      <grAndCompPorts>162</grAndCompPorts>
      <grAndCompPorts>163</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="5c5d20e6-119c-426f-a9d8-da87b79ea6fd">
      <grAndCompPorts>6</grAndCompPorts>
      <grAndCompPorts>164</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="24b8132f-c02e-4d33-bd86-996bdbc816c1">
      <grAndCompPorts>7</grAndCompPorts>
      <grAndCompPorts>165</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompInputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="f42cbc5e-1a53-4e3f-b1c9-d4187593092e">
      <grAndCompPorts>8</grAndCompPorts>
      <grAndCompPorts>166</grAndCompPorts>
    </grToCompInputPortsMaps>
    <grToCompOutputPortsMaps type="ModelRefInfoRepo.GrCompIOPortMaps" uuid="8065ed2c-78a2-47f7-bbc6-06ef0423f7b0">
      <grAndCompPorts>0</grAndCompPorts>
      <grAndCompPorts>0</grAndCompPorts>
    </grToCompOutputPortsMaps>
    <hasBwsAccessed>true</hasBwsAccessed>
    <hasBwsAccessedByAnyModel>true</hasBwsAccessedByAnyModel>
    <hasConstantOutput>false</hasConstantOutput>
    <hasModelWideEventTs>false</hasModelWideEventTs>
    <hasNonVirtualConstantTs>true</hasNonVirtualConstantTs>
    <hasStatesModifiedInOutputUpdate>true</hasStatesModifiedInOutputUpdate>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a5b3f790-c384-4b32-b523-4334f6c4ee82">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3891a297-8735-42cc-8f86-4d1b9a34f7c4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1f147415-ffe0-4d3b-a0e4-9c4a786119ba"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9c34b695-d527-4bea-ab79-e036c19f65ee">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e665792f-b895-4605-9dbb-285de68ef992"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a046a737-3727-46da-bb3d-f0f326286df4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="754a66c8-3400-4662-bcdf-47a5957d13b5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f9a86163-ca3d-4924-bfda-d7ff6fd482c9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="86cb55ea-e183-48e8-900a-838b90063d27"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a2bd06de-dc52-45b7-8949-a06af3edee7c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="33aac316-6b04-44d6-b1f8-b4d87d678df3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="43841fee-744c-4d87-abab-a5129b9bac99"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b7bc58ad-bf6c-49c3-97e5-618e2a85d536">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ba161d5d-3179-45c9-b718-dd6e4e258af3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="10622326-4afa-4c30-a42c-982e09f4a579"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d5309b15-96b3-4a55-aa40-1c16fa350a6a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="db267e73-4482-41d2-9558-eba0e2723dc1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="27264daf-a611-42b0-97b6-86a44bce3473"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8953d818-8cac-4225-9a34-cf1bc117e448">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ec245802-ddb7-43af-a66e-3e6ad6bd27d7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fbe2aeff-9bd8-4612-b463-6122be36837c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="24633daa-de7f-46d4-a859-fc03b2857055">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cdff1fb9-dd70-4254-9e4a-5b5f4be4cb2c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="eac1e604-13bd-4b71-a45b-6f29350220d2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2b49ea53-ec0f-494a-bb91-910ea93bb6e4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2baf79fd-072f-4f2b-905d-e4262fad645d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="92db62a3-71de-4fe8-b350-d118c4d8fd12"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4988cce8-90b5-432e-87de-9d4b419aecb9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5c156ab3-4913-42ec-8554-040e81fca360"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a9c33022-5fb5-4dc1-a96b-1d0b9b714933"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a46aa067-969d-45a1-9d1b-1d24e72174ad">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c395dce6-b9cc-4ce4-9d38-e70502a854c9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2dcf9a9c-c689-4180-bcbc-7e082a88809e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b6c5aaeb-5311-4b3c-a669-43fb1040e109">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3441fa20-5d09-4b5f-a39a-ac5aab855f55"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="05064f01-e756-4af0-bcd1-169a26e98fdd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fd0c5210-ba47-4aeb-98ae-c8ab8519d543">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ec0152ce-14da-46d7-a353-08f3f9fc2192"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="69f7a15e-d6cf-4f04-8635-055aa006e316"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="18d52ece-5eaa-45c1-b06f-c7a8a00fd949">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>1</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="92e7766f-8682-4754-bb1f-f009cbfe6ac2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b31da5d3-7b28-4968-ba76-b8459deb5f3d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f1d4e511-2b9b-43e6-943a-b7dd9d03fdff">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d72781a2-a6db-4486-accb-6b5b3f787e17"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ca27ffc9-5afe-4379-a053-e2f9dcc54572"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="22dc5e82-0f4b-465d-b42f-93132167399b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b0aa0dda-b256-4a45-9d6c-0bb689a73652"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="73d7e9ca-432a-409f-9208-6765b0c77685"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="29c63d92-8602-4f31-85f9-2dba4964a6d2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1bc4449d-20e0-41a1-920e-dbb30ea7c3c8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cb39b335-fc03-4941-b504-bbb0d1383f83"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f6097be0-b5f4-4128-9c15-1e98aea55804">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="474b43e9-b2a9-4b10-bac7-48c6cc80e1a4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8e1c5b37-d1f0-4d69-8ce5-9b2ed35a5d2b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ee7dec3b-a63c-42f7-956c-449ac432de53">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="69a3b82d-7900-468b-8062-314ade146aae"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a422db39-fa33-43a2-8128-12e5b3d0ad8e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="404330cc-e666-4ac9-ac02-c376cf296879">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="845d3769-a857-4297-a562-ff2d204cc240"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ff488360-327e-4945-820e-a5e1d5d412c3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0ddd92d5-5f0a-4871-b966-3e96f1747b19">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cb753e00-a8b4-4feb-8baa-645dfc5e868f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c33b0361-1c47-4f91-bece-52e25290486f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="17366552-0ade-49aa-ab6f-7be88554f5fa">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b0f34efd-720a-4668-97ea-791ba559853c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d0121e30-c612-4707-b534-89d1041d9620"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="62ab0c38-9033-410b-8741-ca9d320e0c69">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="db96043a-3ecd-4528-8f0c-0b55f80b47ef"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="eb30d589-01e6-42c3-b183-0750337c0f7f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f8110c5d-b070-44c2-8275-41c3593ed1da">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="857f1f03-ea7a-4f6d-afdd-df6b22617b6f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cd94797d-c06f-4d3f-87a0-dccf93cd2c28"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ffb7bda4-d075-430f-bac4-b2b61c8185e7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6bc5d6a8-500e-43bc-94d7-54c8072186d7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fbb37935-5477-4a0f-892d-e6611bbafe66"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f5e4f6dd-34bb-4a26-a3b2-f5f0849a6728">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6704d6ee-ff33-42d8-8de2-e3bd7fe1dc10"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="09fcaa1d-e6f2-43d3-85fe-2bb90c789b64"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5c096dff-c5af-4165-827a-e984168382e9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="89580104-0a59-4977-b632-286f8a0c4e7a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="862fa4bc-ba56-493c-8b43-33f71a687c05"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="61e7049f-0aff-473d-946f-d4b0a03ab142">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>16777215.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a8c30d94-1d51-4506-9401-45faa327a3ab"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4377e87d-1cd1-4a3d-92d6-fd5df686e7e4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8a0abb53-75df-4833-9458-905c2c41110f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4a3b1604-a643-4906-b221-a72537a693e5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="68a0aab2-abac-4c2e-92e5-a183927fa9e2"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f26f670c-2a95-42de-8ca7-e529259b4243">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="05fc78f1-d28b-4b78-9c8e-8fcc01f4abdb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="94311e27-bacd-46e7-bb78-b3a16737649f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6d1017c4-ade1-44c2-9647-65378c5c384c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6c6a8f10-ac13-4205-a3ac-2f05da80e374"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="894d209c-cb31-4f9d-a359-8a2e03083b02"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c4a10007-2ea9-499c-b489-8209d4262892">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1a895837-06c7-49d3-a090-5c08c52f4edf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="528fb040-db49-4675-a9a3-48a9dbed359c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0d1e5dd9-b8eb-47a1-9e20-49f7346e6a84">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ff2013b6-12ab-4c1b-9ce7-24e1fea06a62"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="eee6c760-bc85-42b0-b9ce-6fde498dbcac"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bc9d2f65-ccc6-4db0-9e95-c58f27f14c51">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cb408d48-df73-40d9-b32d-e52a79f2cde9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1a14fd6e-7b0c-4882-8fbb-b055d12dfe14"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7392b9d7-a7e7-433b-8d69-8bfffc1a502a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="01bde8f2-a71f-43a2-93d0-67d9b5946b00"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9cafcaab-2ae7-4b18-93a3-e8d2a1be61ff"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ebf563f6-042b-4f94-9be4-961ab2c742a5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dff64454-9bf6-4d8c-9a96-8b615b355dda"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8edb61cf-2ebc-44e8-b7a8-b32a40efd596"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0f133c77-b6d9-41eb-9aae-2fec88c670e2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0d0e4238-2985-4b88-a662-3992d96bfced"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fd6ea4cf-5a39-49f6-a300-ab756e90fa43"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bd8c3ac3-badd-46bc-94c6-9855e6689748">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="75fb10f4-4ab2-4a0f-ad7e-1764141566a3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="442e7836-4d45-47b7-95b3-7dbd65ade82c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="7f3d6192-ca99-459b-9190-453f54e4c35c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2fd0ea87-f7ec-467a-9eff-f45b68a1a714"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2744de42-97bf-419f-b8ba-2085988bab97"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="65e770aa-83b7-4094-8625-686e127992b5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0f232138-dba7-4190-ae66-9c7b8afece38"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="56698160-4ce9-4383-b09d-5f40d728d08a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0119051d-4c6f-408b-9d95-876fec6cb326">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="40d7f0ca-af0c-4d88-a841-1c34d7bffea3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fc7d243d-a3ee-4d5f-9c54-b67ac59e4697"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6b627311-fc4e-4fdb-970c-d8fc0ca01e68">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9be41853-4bd4-41c6-b16d-56e60f61e3ff"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6f61e3e3-2326-4d99-a6db-dfaedd1f322a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d4227a65-654e-4077-a97e-d876aa337c1d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="25e60bf8-abaf-4dfd-bdbe-4c69de94a5ba"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2e179876-d1de-49b5-92b2-832df2830943"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="083251e2-9804-4c09-8361-c6dd1594812e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="25162c32-e10f-4e5a-acfc-498be86df596"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="71d388ea-7990-4c64-bd5c-78c0b9a26be4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="644091a0-dd94-499f-b4bc-5209225ac3b9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ab9647ed-917f-4c4f-a8d0-1fcee5a69db2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="643ea6d4-f1ae-4656-a644-205cd6a7b2e1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5b5da631-2273-4d2d-825f-d37ab43c0bab">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="44e80662-7700-45e3-8370-e8f08e02139c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="84fd7c80-c3d9-4aaa-a755-caa393855a82"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="31098c9e-0852-498d-86df-21eafc249c10">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ceb98b98-a93e-4d91-b5c0-2e03b7fd8042"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6beddb3b-1bad-41f5-a593-be669b235d13"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="665ad5d3-839f-4ac7-a229-84d800b7c5d8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="291c01f6-5612-4d4f-859f-d376e32a4f1d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f1ba90c3-f03a-4c83-9c60-8793b3a35de5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9e63fcf9-b848-4fa3-a869-ae903d4e6a1f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dbf59d2b-5239-4aeb-8f7b-8a23cb6f3fcb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e5fe097d-d57e-40cb-8e59-265743eab4e7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3b2acaca-6891-4cbd-b3bd-9ba957993020">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e03d73ce-40ff-476f-bbd8-3d38b3023225"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3fff839d-9b92-4da5-88bc-362c354f4a76"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6e77f8c4-3068-48ce-a6bd-1449ea49705a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9e1c7ec9-3042-4e98-ae92-7340d4fad9c5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6e5a3bc6-fe7e-4faf-a5be-f0f2742f08c5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0d793906-2cce-4d67-902c-1b84fd08f1b2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="031bf717-901b-4893-95ff-46a14ad81804"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4540cba0-bd5d-43c9-98e9-e19613750819"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b12f791c-f648-4103-ac7d-b8ea78897ecc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0b623ec7-46b9-43d4-a541-547eec33ccf3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cedbd677-962a-4b1f-b613-a370cfc62e06"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a889a855-1519-4ab3-85c7-a777f7742a60">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="47923695-1b9a-49c9-80b8-f5641fd24c52"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="edde874d-17da-4221-abf0-7b28233cbc42"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9cb566af-c7e2-413d-a4f6-8d9768a12da0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>2</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9475710c-2c99-4832-820b-6a68c67f7998"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bd4e2a91-3431-48ec-b8d2-4badbec5711e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e3addf8d-c21c-407d-a2af-744adf68f269">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7a12ed1b-7e72-4a38-b245-d9c2e605fa3f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="02f68857-c4bb-4258-9213-5b599dd9890d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="03911aa3-72eb-48c2-8744-2b922205a9d5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2c8596af-e48e-41fc-b335-823ea5f45877"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8b02600a-1fd9-4629-87ed-e4f182da41d8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="812c5db3-214e-49e8-8ab6-04e102bf3d12">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b09abb16-8a45-432e-9830-6a5748e7b172"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6f68ad0f-ef6e-44ee-81af-37db2810480c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="832f8519-61a4-419c-adad-52418755f5f0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3231be3b-ba4b-43d9-acd4-5c981d7ffdee"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="be4a0970-f1c7-4146-a9c7-0773aab542ed"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="686005e8-d97a-4dbf-8798-4ec35afaec12">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a16ee8fe-1618-4820-b3fa-38e5b3a898da"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c77e668d-1d26-494e-908d-63abc100d138"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b83b1eea-ff02-448a-a6f7-b7b3c1b6cbd6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a45dcfde-0c3e-4bdf-b0ea-24b145af2e30"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f88072ff-5719-47de-a285-7d74bf0bb0c9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e63a04aa-2a7d-40a1-96c0-fca682246664">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d7bd6292-339a-4642-9236-b85fd3a55181"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="53c50dcb-e4b9-4dec-9f75-0bdf661cd04c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ff5d6c33-f89f-4839-8482-29fe08032e85">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8e303e21-43d4-404c-8e49-bdc0c8d8e225"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5b4ca56a-722c-4821-a709-4b05160e5dcf"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bfbbfc75-7c1c-44d8-8043-925e50d92d9d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ea22fa3d-e62f-4830-b1a7-68ec341dba92"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0b53c9af-b6d5-46f6-87d7-2ce30c70584c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cfa6294a-f898-4c6e-948c-36ee3637fc4d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b14b0c55-996c-4b45-9d87-79be5d02a124"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d6e57f7e-394c-4d95-846a-5e1b5e170a96"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="382f586c-b0fd-4f5e-a1cf-958978af8d20">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="821e6d29-0c46-4c8d-acf2-f066da1f0cb4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f657d227-2f36-40f0-b3e5-0cf5b2294057"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9c4dd621-6cc1-473d-9011-887fb9dcec03">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="46c62c30-84ad-4b1f-8ea6-7a65a25711fa"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bdc41960-a569-4e2e-898a-39a554004c50"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="60f31ddb-8c02-443d-9042-bdb1d1d559af">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="735334d5-0731-4191-a77c-5e64401d5f05"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b035479b-b564-4155-a0fe-eff3d0539f86"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8fb04019-cc94-4e2d-985a-46188ee77b39">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8f13db0f-ca85-46a6-adb9-50c38ae73055"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="49299e60-9b13-492e-aa5e-4c9785fb22f1"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="27d519bd-8a95-4cd6-a451-0922fbf3df1e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ecd5aa48-f4b0-4de9-b573-194fb340fc12"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="74e63b2d-c429-4ee4-af17-3447f8e633df"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2794c32e-501b-4187-b749-122f79f09e5e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="eba1b1b8-a798-42ec-bdfb-18515fe8833d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d7bc9bd1-b385-4571-9c06-6962707bc50d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d8e713c4-0754-40b8-8f8d-9851071bd617">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b706dbec-a49c-4307-8997-e571c88e2473"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dd451327-783d-443a-ad3f-b2bae10e42a8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8f329fbf-da8b-4281-b11d-779368e30cd3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fcdf902a-cdb2-4b69-9bfa-f436fd8820a3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="afcca57c-a6f2-4898-b3cf-1d98bd15bd02"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f4706abb-a289-4c65-81ca-0e159c7c5aaa">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ca165bb9-5c60-44fd-a885-5cd33290dd0d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fa7a5704-3732-40eb-8706-d60f77cb01c9"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="57d862c7-4030-4b29-be47-************">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="616d9fa2-23df-427b-a227-71ad28d62978"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7629eb45-b60a-4214-a751-898de10a6d6d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c54485e0-2b8e-43f5-a8f1-bb9065da693c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="04a08597-e7df-4158-9b95-46478e5db458"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cbab2571-1622-4a9a-b32d-af10c8607241"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="24080cad-8485-4927-b452-23d226a128c3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1b556b3b-0dd8-4e11-9f55-16805dcf30df"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fe1f3826-cfd1-46fd-9634-0d763d574b06"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d81aed21-f5ef-42ee-a8e3-ffcf6538d424">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="37da4513-f728-4d50-bff3-8660a65bedd8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d3849696-1a6a-4bf6-a84f-64f9e6877628"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="76a7f26b-4a3f-48dc-ab79-713f44529a48">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="43420c83-6dba-4cbb-b975-df169bc18881"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0e96e887-e05e-4042-b2ce-7122498e0a26"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="355d3f0f-4dee-44f1-821b-06d330c72f14">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7a435477-8116-4552-8e09-3e895f613a0c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0eaf3270-0577-4780-a4e8-f3e02dab1b7b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="07361a53-faa4-4e48-9ac9-a93566c3807b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3913ccc8-3451-4283-92f6-cf3ad1fe83cb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cf4906d9-0ddb-404f-a82a-0920dc50ea37"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="eb9cf248-af1e-4770-9cef-d4be51262c90">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="cc74584e-bf1f-4ee3-893b-331ee52e8357"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9e82e81b-5403-4cb3-9fcb-08e7a7a75338"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b103487a-fc91-4ef0-ac86-696de7bdc7b6">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f026b29b-13c3-4299-a51e-1e5da3e9e0ec"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7f7db827-f320-4e3d-a068-abe7e51bd614"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="76b36991-1e4f-46f6-98b1-83070f44bd76">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="df115c1f-25ab-4682-82dd-f0d100ed5b62"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ce8b2b4e-1812-4314-98e0-9f1bd3448f09"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="24498d88-3051-46c7-9529-eed6bd3da943">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="74ff527e-c46c-4c6c-b7d1-29e0e0ed2d07"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5b7eba5d-9e60-4280-a945-89f8fa9b0ad4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="afce98b0-d951-4fe5-a101-165a64ba1c1e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9432ccf1-220b-4eb0-bd25-712c67ec550a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="934da17f-1adc-4a66-8f3f-67d2a5f41cfd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cc2ee252-bfb0-4c10-a2ee-d217d9af22da">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dc2b8383-d9fa-4be3-8ef2-790bfceaa696"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0e88fcbb-381d-4630-8d9f-f5cfe4627a37"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f4f09934-055a-401f-9ccb-ab7335967014">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c26e4b95-62a2-4d5e-b73d-93be38c575ef"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="68dd1f36-a93a-4637-a427-bcd1d006f26e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="65bb5e14-649d-4107-9574-da1ddff267df">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3b642a7f-8386-4e77-937a-4239a365e98b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="58350780-da92-4cd2-b319-a357a57d9cef"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="139ea1c0-62c6-4397-9e45-e3ae9fadb824">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>3</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a76d522c-dcdf-4923-a829-d963facaf016"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7507f0c0-7bf9-44af-995e-6f8ea5eba689"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="efc12ec1-6114-4ee7-814f-0cc35af1d338">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="393e73e2-129a-4ccf-b804-e22cfc6788a5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="67d93adb-c124-42cd-bdc6-03f68b2aaf07"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1a35d668-ef8a-4629-a8d6-fe36da77876a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>100.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3cbe7bd0-5211-4dab-a062-a0c6753dbc52"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9268eee3-b6ca-411f-aeb5-258c35d3e3b5"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="edfd3774-3587-4e21-a766-5a1999d9f25f">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d4c198f0-2893-4dff-868a-55d7a0d1f391"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="158f5a55-8cd4-4905-b163-ca07ee77637c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="17453626-a1b6-41be-8d57-df69db5f7914">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="31877a38-d516-4c38-91b2-5591fb004abe"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="27be5ac9-9ef3-45ab-8a76-bc2bdd2615b3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e6bc799c-0b6d-40e3-b2da-ae87e7c8a0f5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>1.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d86bce8c-27e0-4867-84df-129b584e14be"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bc07075c-9980-410e-82bc-1ccf2c083476"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d28508d0-2738-4f0e-98e7-55482f93d37b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>4</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a15e4ac0-86d5-46dd-84a3-ed48be3a4ed6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8a796813-a5fe-483b-8a3d-06070bb7a2a4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="30518a84-2b68-4f72-879d-6f86755d3752">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d05d5a55-6ed1-4405-b18a-f676144324b3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0bd285da-02b1-4b35-a1e2-07e2388ce4ca"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f817bdf1-cfcc-4579-82ee-78551b6af1ad">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0b789c84-5465-436a-bdea-f277fcf6a886"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fc214d82-6b37-4332-a812-7d313b129f3f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="408d4369-cbe2-4baf-854d-4aa083571c65">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="842ca72f-8401-41d7-bf1b-ab58165d29f2"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9d55e8a4-e4e6-4204-906b-a20624e763c3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6892a347-2b40-42aa-b31f-cca3d517f91d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="16a326b5-10b1-43aa-b16b-39202065a91c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="600655d9-ddc4-4cc3-ba56-3ce38e8ecbec"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="12870904-1eb3-42d9-a1f6-74edc5b3479b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7b0a4daf-1943-4447-8724-a44afbb0f9c0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="66243b7f-1591-445e-b596-4d7cabaeb5cd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5c45ec95-e423-4138-9e58-b2c63e017d86">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7b0691a0-e013-4f40-a6b7-93977278f061"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fd7d9806-91aa-43e8-94cb-ee637c0d581b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="153d72cd-acc2-463b-95bb-71dc70e33f84">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a6e5f920-91b7-4f69-9d45-71c8f9761762"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="db175b21-c16e-41dc-9502-3abee4e56f04"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a37d97bf-c583-4ecb-8299-ec2a675e499a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1623301c-3c09-44a3-b0f1-2394d154e06a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c1667e86-a598-409c-a7dd-55c8053bf791"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="3f9c0b3c-061d-4152-b81f-a1ca845c1360">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="da70ebbc-2df8-4c59-b8c4-c91e9c63a9e9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7de322ca-f11e-4cc6-b9bb-1e97806e452b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2256187c-3d78-4f8e-84a6-ce824c491721">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bb761f84-65f0-4a69-b29a-2b5db9dcc76a"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4d9fb401-b668-4a63-91fd-72d836045d06"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="b1281a2e-a33c-4f62-9ba4-6754e1d9ce10">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="46a482f7-b016-4a15-b3dd-c4f272aae9a9"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="cea34a01-**************-df9533d4445f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="039cbd0c-2c7d-4fac-8016-c57bd815bc63">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="37a175ab-ee1b-4d1c-b595-708fadbab106"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bb91161a-ede3-447e-a3f3-fb944e855527"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c505b4f5-fdf9-4941-80f1-7abb8d6928b5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bad17c96-d270-4648-a6bb-7c7edb947413"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ff031a4e-d729-4e0f-a6c6-b3e1517414b0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="efc55a35-7f4c-4e4d-816d-b771c60e801d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fdc6b2dc-dbff-41ca-b5e4-74c773f59e8d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="de953419-9a1a-46e6-abd7-bb6293da3b02"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="60bcc37b-2f40-4eee-a26d-3e4d011e0b2b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="5aa2e635-b2d1-45ff-9e13-74ddee6d787f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fcf5a306-5a49-4360-a475-e6fa63785226"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="460d8a14-c644-4b81-b2fe-8398682a7c76">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="4c0b6215-a002-41ca-9576-0fe7ca3ac1b7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="2c1a5017-2381-42a1-b2ea-f3d6a40a160b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="37c6fcf3-1bc6-4bf8-a5dc-c800d63f4e20">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="607f54e3-1d92-47dc-8e4c-b3c59beb3faa"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bdd15d21-af4d-4811-a218-2a6f4903e371"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c5353c82-76b5-4a67-9305-ffee695df24a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="47beed84-a77d-4551-a646-6db1318af793"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="dd820946-129c-4874-8e18-e12fcb6dc02f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="87e4f696-43d1-433b-bf24-90db5c1dc750">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="09947749-769d-465b-8914-edf36e0977f5"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f1eefa1d-2af1-4681-8a61-894df7072af7"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="191254b9-6845-48c0-b868-05cea47d7912">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="da6cc87c-f499-4035-9ca0-1e10e2c25532"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a396a78c-a3bc-4559-91d6-96799d26720e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4a150001-bb83-4c7e-a25d-dee3ebb75bf4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="efc394a0-12c6-46a2-bec7-d07b68f75148"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e4330eb9-17a4-43b2-913a-7ebe499cbdb8"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="6f9a122f-82e1-437d-926e-d0a346477c11">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="989659c3-46f7-484d-8c47-24daa45c528d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="3a67fae6-a524-41a4-917b-e5938f0af424"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5adfc8cc-14f9-4a2f-99c8-d96ea0181ae8">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ecd9d07e-0588-45bb-8f3e-d044fcef5203"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="aaf44ef7-a96d-4274-a667-c9c8ec0b697d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="04980c5f-7a28-4a35-b242-3491b51d41b2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="fc590dcb-077a-4a90-9945-1260e5a363e3"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d41051a9-c795-4464-8243-100f5e007e4a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c304eaba-9461-478e-8446-d333d411ecff">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="71f6a90e-77a1-45a1-aaba-be071e29f2cb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a92370de-7feb-4e15-b66b-46787bf2f46b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5c53c61b-54ac-4776-80ac-31d896d679fc">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6815cbd6-ff58-4050-8a1f-1132f44b5fc6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fd7ef949-761c-466e-ad5c-e53382bfb703"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bc081019-0ad5-4237-9aca-5e350146b094">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="345eeed2-c243-425d-94d4-49a4a46c08e1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="5c4cc8af-8313-4975-b709-8a65f980d1f3"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="267307db-e2dd-4d4d-949b-************">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d0a0b378-6ced-4b52-b6bc-2693d71d3acf"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="a0f55fe9-d07c-4625-acff-bf3478993261"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="25450066-9b1a-41bd-be0f-878f4c0d60f0">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ae022661-1824-4439-98c5-8853154d7a67"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fb6e2c75-9963-4285-97fc-4d4c662a4c23"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="666c51b9-067d-4fef-814f-71fd24ec0c82">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7551d4d8-28e0-4acf-b8fc-5a96ebcdab1b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0afa3e42-9c6c-4f4c-8b36-a32f94c8f27a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8e58b55d-8ccb-4403-aef6-2c7ad6969d2c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1c751574-2916-4c37-b5ad-dd93938b68b1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e14b8338-90d1-4569-87ef-f54d5940b570"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1bc9528d-45ee-4573-b64b-75142fd0b86d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>205.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c78af574-bccd-44d9-94a1-9579d18b59f4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fd24464c-4bed-4ff9-aeb5-53bf746331f4"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="38d9099e-2e57-4c84-832b-0cdbca6516e5">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c3734921-c751-476a-bbb0-432b4a1a8de8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="55d48427-cf1d-4726-9e2c-349c1987b171"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1fa67aef-4c93-4d4c-a2a7-65524cc16826">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>65535.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ed3deee5-2d2e-428a-9a33-aa03f7cb6811"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="28b002c3-aa00-4811-b7cf-91c817eaa266"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8dfbaea6-8540-4fc8-bd86-cae56055bfd2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>16777215.0</designMax>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ed22ae83-06b0-414a-9f35-13eddbbabf3f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="911d90c8-2bda-41ca-9754-dfee771bb455"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="fe3e64a4-32f9-40cb-aaac-231950274e25">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="18d35a33-460b-4b1b-8009-e6a37e4acf83"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f6f1e314-16fb-4421-bc7e-21086a852f28"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1f44bd26-ac93-41ac-967d-ef3b0dbf7328">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d9979a68-16a7-4daf-bec6-c5d9694ccf5b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6c3240a3-18d9-4cf4-ad5b-d6c0261a353b"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9814a6de-4595-4c2b-81d7-bbf6760ae21d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3b44b487-4479-4ef8-9ebc-33081c626dab"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="e0676dc0-042e-40a4-b8ca-84e43cf772ec"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f1c814d8-08d8-4f77-bc0d-a9735ba19f5e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2f669aea-3697-4b9c-bc2e-c0d0049f9a15"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0d5fdb37-c28f-4a83-a70e-8eb8e6729920"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="85032a4e-6fd5-4927-ab8b-41c30336cba3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6a9abca6-1e87-4162-8687-43720880b70e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="75b9b70d-5171-4a7c-b66a-27ffe48df455"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="02698b99-b608-4e5e-a3e8-d8b9eaa1188a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3c0e3242-75f8-4efa-b6e2-6071b152d7c8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="1d11297a-eb0b-49d6-8d76-fb36f27673de"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="81448f23-e64c-443a-b5f9-fdd0495d7b72">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dcf2729e-4cb5-48fc-a885-3ab7372285d0"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="c3573f36-a687-46d2-be4a-07aa7b9b5360"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="a9e51c86-399d-4021-b41b-a87e63cdf70a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="ce49bef5-3f47-46d8-a215-d1eb511b88fe"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="790dcc93-f326-47e6-a3ca-90d30052d66f"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="19a3c013-3e8c-4a4a-8e8f-6c385eb802db">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="e04dd03b-fb32-4b2f-a38e-c44db409e56d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="457af615-65a1-403e-964c-13c8e42cd381"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1080b44f-51a8-44fc-9109-d3bcd5120207">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="873fd810-c97f-47b7-ab35-4b4e6237de03"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="9ed68724-69dd-48ce-8c2a-44d270cd654d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1b6f5a98-8344-4a32-af5c-4d8dc609747b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c5933ee0-7615-4da6-af85-feda9bf97fac"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ab70a997-3fe4-44ad-a5f2-d2d744510cce"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e04772f6-7dc7-4b7a-b0de-974bb33fc167">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9d6b2989-f5ce-4b69-8370-6c14c23c80ea"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="be662ef2-57a0-4dc5-8aed-dd821c80837d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d83409b1-ebe2-452f-9eb8-c8c49477e4c2">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b271a6fa-ec2c-4937-9f86-c5aab64014df"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="7ce5a3e9-d23c-46bd-83c0-996d04eaf6ea"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1734fc2e-9398-43a0-a6bb-f2b239f58d48">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="f76d2c46-3e42-4edb-81ae-a0c1f9f3b1a1"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="17f11152-539e-4f7f-a89f-0a999dfa4410"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="0d3a9b0b-709b-4715-ba64-c5348cd3bad7">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="d704144c-e483-41cc-889a-61c951252b1f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ae9ea49c-7413-4e04-8159-1058fc1a64fd"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ee69e93e-0a14-438c-a28a-4982164bc98c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="0e9b92ef-3b01-4ccd-9d73-21aa78cd00aa"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="18d96ad4-3e8d-4f0d-8ae9-5da194235037"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c0e2ba30-3a45-4bb2-9ef4-f2da07965b5d">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="c41f0ddf-3924-4c83-a45e-d9a58ad2c05f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="fbe05faa-e94b-40dc-9c62-d72e3f26bd24"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="8457c574-e42b-41c9-accc-1820be420bab">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>125.0</designMax>
      <designMin>-50.0</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1d5ff13e-03bf-4851-846e-3b2afa8c245c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="760b013f-3e99-4414-8284-4e827b0fcddc"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d16f1088-54cc-49f7-bac9-5e47d4b35b2e">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="a602a599-22e8-406c-a205-13cdc0c684cb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bb3b082c-f3c5-4c6b-800a-3e53e15f99c0"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="92d2178c-2488-48d6-bb59-c77f0a0bdd03">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="3d016809-6e42-441c-8826-0007cadd2b4f"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="4d905dd4-2a35-4767-92e8-68dbf8460b9a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="bdaad13a-068e-40c6-aef5-ceb5d20d0fe3">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="7b755923-0510-4198-8f5f-3af5e72a618c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8f1fcfe1-e117-44b5-bb89-97656114a73a"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="f6606ed5-b577-4f49-9ad2-5c29110441f4">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="1e4e83fa-2cbc-4d3b-9529-4da307f713cb"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8a864f09-0262-4c25-9137-3e5a3e998e86"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="73c0101a-265b-49fc-9bfc-08e784f95644">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="69793bbe-9e4c-478c-9d29-30d185adeb00"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f7f147c6-88b6-4a55-aa85-1412ded1754c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="85bb85b5-6538-496a-a813-ab5f2f800c1b">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="8a6e20fa-0748-4790-83f4-af5e700987fa"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="0ad44f27-a0d2-4efe-8d56-5500b8dded0e"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="cfe1a43d-0d14-412d-a5a2-880ded66fcd9">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="af68327b-989b-4722-8cc1-d579e37eda0c"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="bff110a6-c0f8-40f5-a859-4d145b19724d"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="e8ec2e9c-86c1-437b-98e1-feb86c52384a">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="b740d2a6-ca91-4127-bfb5-b9d23080e8c4"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ba92cd0e-b3e1-40e7-8155-500af12889a6"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="4ea3e47a-3fb9-4cf5-8ff8-5fd67d7690f1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="9ec3d600-84dc-48c2-9fc8-93bc07e1433e"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="b736abb9-d95f-4763-a5e2-a6d77c393692"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="c3a4bc73-0b78-4031-90bb-40f48de87abb">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="def8633d-5593-4817-8e43-001341c0b43d"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="6d503ca9-0b8a-4b8c-9d20-a92b1ba2273c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="2a117be0-58fc-40b8-aa27-ab3ed3c91faa">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="dba30af6-a189-4f07-82d7-6f964d404e70"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="8915bbcb-0070-496d-b869-5632f3fed99c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="5aa3a0f6-7d23-49c0-9c45-465b0369b12c">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="2facaa1b-b7ea-4c9e-8c72-1d849b3ef30b"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="ccb3db7f-1d3c-4f40-a985-61f6902d3099"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="d1cd7f71-1fe6-4608-8bb1-ece238eaf749">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="bf4756aa-1d0a-489d-b13d-a4bd6b6c59c8"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="41bc7608-9c33-4a30-a256-bd9f1d109001"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="51f86f82-2bbb-48e4-818a-abaa328e44e1">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="864f7288-1532-4f40-bcd2-6b127d900fab"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="d7de9652-80db-446b-a7cb-213e326c5d54"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="ea963483-2e00-409d-a9ff-2277dad38197">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>5</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="67f11b7c-dad7-4602-9047-11dbc30d6e67"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="64ca3416-688a-458e-ae2b-d454c301003c"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="9f2955bc-26a0-4310-96ab-ceca49b39648">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>6</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="6ebf791b-e5b9-423a-8ec0-aaab77da4fc7"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="46d95160-2cd3-43f3-823c-a8566419c485"/>
    </inports>
    <inports type="ModelRefInfoRepo.InportInformation" uuid="1574a92a-a247-4ef9-8164-2432f432a1ac">
      <isNotDerivPort>true</isNotDerivPort>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <originalPortNumber>7</originalPortNumber>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.InputExecutionInterface" uuid="481e6123-0b47-4084-b4a5-e6ad75f35d64"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="f5b504a2-48af-4e3b-9f0a-f0f703cd2f63"/>
    </inports>
    <isAperiodicRootFcnCallSystem>true</isAperiodicRootFcnCallSystem>
    <isBdInSimModeForSimCodegenVariants>false</isBdInSimModeForSimCodegenVariants>
    <isInlineParamsOn>true</isInlineParamsOn>
    <isNumAllowedInstancesOne>true</isNumAllowedInstancesOne>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>true</isOrigInportVirtualBus>
    <isOrigInportVirtualBus>false</isOrigInportVirtualBus>
    <isOrigOutportVirtualBus>false</isOrigOutportVirtualBus>
    <isPreCompSingleRate>false</isPreCompSingleRate>
    <isRefModelConstant>false</isRefModelConstant>
    <isRootFcnCallPortGroupsEmpty>true</isRootFcnCallPortGroupsEmpty>
    <loggingSaveFormat>2</loggingSaveFormat>
    <maxFreqHz>-1.0</maxFreqHz>
    <numDataInputPorts>8</numDataInputPorts>
    <numLoggableJacobianDStates>0</numLoggableJacobianDStates>
    <numModelWideEventTs>0</numModelWideEventTs>
    <numPortlessSimulinkFunctionPortGroups>0</numPortlessSimulinkFunctionPortGroups>
    <numRuntimeExportedRates>1</numRuntimeExportedRates>
    <numTs>1</numTs>
    <origInportBusType>CtrlLogBus</origInportBusType>
    <origInportBusType>PnlOpBus</origInportBusType>
    <origInportBusType>SWC_DataHndlLyrBus</origInportBusType>
    <origInportBusType>SWC_SigMonLyrBus</origInportBusType>
    <origInportBusType>SWC_CommLyrBus</origInportBusType>
    <origInportBusType>SWC_DiagLyrBus</origInportBusType>
    <origInportBusType>SWC_StateMachLyrBus</origInportBusType>
    <origInportBusType>SWC_ObjDetLyrBus</origInportBusType>
    <origOutportBusOutputAsStruct>true</origOutportBusOutputAsStruct>
    <origOutportBusType>ComLogBus</origOutportBusType>
    <outports type="ModelRefInfoRepo.OutportInformation" uuid="3f097c5a-cf01-4764-9097-02bf749867ce">
      <blkIOUpdatedInAnotherExecContext>true</blkIOUpdatedInAnotherExecContext>
      <computeICInFirstInitialize>true</computeICInFirstInitialize>
      <hasSystemInitMethod>true</hasSystemInitMethod>
      <isInitializedWithIC>true</isInitializedWithIC>
      <isState>true</isState>
      <okToMerge>1</okToMerge>
      <RTWSignalIdentifier>ComLogBus</RTWSignalIdentifier>
      <designMax>inf</designMax>
      <designMin>-inf</designMin>
      <indexType>2</indexType>
      <rateInfos type="ModelRefInfoRepo.RateInfo">
        <compiled>true</compiled>
        <isEmpty>true</isEmpty>
        <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
        <period>.2</period>
        <priority>40</priority>
        <rateIdx>0</rateIdx>
      </rateInfos>
      <resolvedSignalObject></resolvedSignalObject>
      <executionInterface type="ModelRefInfoRepo.OutputExecutionInterface" uuid="598000b0-e24c-42f3-be09-b5ebb26ce3c6"/>
      <sigNameToEMVCEMap type="ModelRefInfoRepo.SigNameEMVCEInfo" uuid="defee740-8014-4dd8-a173-97aca02b82cd"/>
    </outports>
    <preCompAllowConstTsOnPorts>false</preCompAllowConstTsOnPorts>
    <preCompAllowPortBasedInTriggeredSS>true</preCompAllowPortBasedInTriggeredSS>
    <removeResetFunc>true</removeResetFunc>
    <runtimeNonFcnCallRateInfos type="ModelRefInfoRepo.RateInfo">
      <compiled>true</compiled>
      <isEmpty>true</isEmpty>
      <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
      <period>.2</period>
      <priority>40</priority>
      <rateIdx>0</rateIdx>
    </runtimeNonFcnCallRateInfos>
    <sampleTimeInheritanceRule>1</sampleTimeInheritanceRule>
    <solverStatusFlags>331</solverStatusFlags>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="cc1ca177-bb67-4ade-9f96-86d216b672e6">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_MovType_t</enumName>
      <labels>None_C</labels>
      <labels>Manual_C</labels>
      <labels>Automatic_C</labels>
      <labels>Learning_C</labels>
      <labels>AtsReversal_C</labels>
      <labels>RelaxOfMech_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="98fd406a-886c-4f97-a9a7-44b69db53636">
      <defaultValue>Idle_C</defaultValue>
      <enumName>CtrlLog_RelearnMode_t</enumName>
      <labels>Idle_C</labels>
      <labels>ReqLearn_C</labels>
      <labels>LearningPos_C</labels>
      <labels>LearningRF_C</labels>
      <labels>Complete_C</labels>
      <labels>Interrupted_C</labels>
      <labels>Renorm_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="6b5bc776-51c3-40de-a018-a20b4429ac0c">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>SysFltDiag_LastStopReason_t</enumName>
      <labels>NoFault_C</labels>
      <labels>Debugger_C</labels>
      <labels>DiagReq_C</labels>
      <labels>PanicStp_C</labels>
      <labels>ThermPrtMtr_C</labels>
      <labels>ThermPrtMosfet_C</labels>
      <labels>RelaxMechComplt_C</labels>
      <labels>AtsRevComplt_C</labels>
      <labels>MovTimeout_C</labels>
      <labels>ClassEOV_C</labels>
      <labels>ClassEUV_C</labels>
      <labels>BattVltNonPlsbl_C</labels>
      <labels>AmbTmpNonPlsbl_C</labels>
      <labels>MosfetTempNonPlsbl_C</labels>
      <labels>SysIC_CommFlt_C</labels>
      <labels>LINCommFlt_C</labels>
      <labels>SysICFailSafe_C</labels>
      <labels>ChargePumpFlt_C</labels>
      <labels>HSSuppFlt_C</labels>
      <labels>VsIntFlt_C</labels>
      <labels>VsSuppFlt_C</labels>
      <labels>VCC1Flt_C</labels>
      <labels>RPInvalidFlt_C</labels>
      <labels>HallFlt_C</labels>
      <labels>SysICThermShdFlt_C</labels>
      <labels>MtrCtrlFlt_C</labels>
      <labels>HallSuppFlt_C</labels>
      <labels>UnderVotlage_C</labels>
      <labels>OverVoltage_C</labels>
      <labels>StallDurAtsRev_RlxMech_C</labels>
      <labels>OutsideEnvCond_C</labels>
      <labels>TargetPosRchd_C</labels>
      <labels>PrevReason_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>16</values>
      <values>17</values>
      <values>18</values>
      <values>19</values>
      <values>20</values>
      <values>21</values>
      <values>22</values>
      <values>23</values>
      <values>24</values>
      <values>25</values>
      <values>26</values>
      <values>27</values>
      <values>28</values>
      <values>29</values>
      <values>30</values>
      <values>31</values>
      <values>35</values>
      <values>37</values>
      <values>48</values>
      <values>49</values>
      <values>50</values>
      <values>51</values>
      <values>52</values>
      <values>255</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="d7ad5d66-f07d-4f50-a3e6-6fa26ccc3790">
      <defaultValue>None_C</defaultValue>
      <enumName>CtrlLog_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="4fea8683-4d0e-4368-a888-930c37d8ecdb">
      <defaultValue>CmdNone_C</defaultValue>
      <enumName>PnlOp_MtrCmd_t</enumName>
      <labels>CmdNone_C</labels>
      <labels>CmdInc_C</labels>
      <labels>CmdDec_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="a62b4b18-638b-4056-82f5-ca8ecf284df9">
      <defaultValue>RoofIdle_C</defaultValue>
      <enumName>PnlOp_Mode_t</enumName>
      <labels>RoofIdle_C</labels>
      <labels>Moving_C</labels>
      <labels>Intr_C</labels>
      <labels>ReachTarPos_C</labels>
      <labels>ReachStallPos_C</labels>
      <labels>Automatic_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="b99d4816-2179-49bd-b37a-7a3c5a4c051f">
      <defaultValue>RevIdle_C</defaultValue>
      <enumName>PnlOp_Rev_t</enumName>
      <labels>RevIdle_C</labels>
      <labels>RevInProcATS_C</labels>
      <labels>RevInProcStall_C</labels>
      <labels>RevComplATS_C</labels>
      <labels>RevComplStall_C</labels>
      <labels>RevInhibted_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="a791d135-7324-40fa-af92-2bded85edabf">
      <defaultValue>ATSDeactivated_C</defaultValue>
      <enumName>PnlOp_ATS_t</enumName>
      <labels>ATSDeactivated_C</labels>
      <labels>ATSAvailable_C</labels>
      <labels>ATSActive_C</labels>
      <labels>ATSOverrideStage1_C</labels>
      <labels>ATSOverrideStage2_C</labels>
      <labels>ATSDisabled_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="0a6400d2-**************-25334162d9cd">
      <defaultValue>IRS_E_OK</defaultValue>
      <enumName>stdReturn_t</enumName>
      <labels>IRS_E_OK</labels>
      <labels>IRS_E_NOK</labels>
      <labels>IRS_E_PENDING</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="79e11c0c-b5a6-4f36-85c0-da84285cf3d4">
      <defaultValue>Disabled_C</defaultValue>
      <enumName>CfgParam_TestMode_t</enumName>
      <labels>Disabled_C</labels>
      <labels>All_CW_C</labels>
      <labels>Sequential_C</labels>
      <labels>All_Stop_C</labels>
      <labels>All_CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="46901029-29c3-4cb1-854f-108261ecd7ad">
      <defaultValue>Init_C</defaultValue>
      <enumName>VoltMon_VoltClass_t</enumName>
      <labels>Init_C</labels>
      <labels>VoltClassA_C</labels>
      <labels>VoltClassB_C</labels>
      <labels>VoltClassC_C</labels>
      <labels>VoltClassD_C</labels>
      <labels>VoltClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="9495ae7b-c4e5-4494-8744-4c5a247c9ebb">
      <defaultValue>AreaSlide_C</defaultValue>
      <enumName>PosMon_Area_t</enumName>
      <labels>AreaSlide_C</labels>
      <labels>AreaVent_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="13e58752-98c8-4087-814b-66af00121bd5">
      <defaultValue>PnlZero</defaultValue>
      <enumName>PosMon_PnlPosArea_t</enumName>
      <labels>PnlZero</labels>
      <labels>PnlFlushClose</labels>
      <labels>PnlVentArea</labels>
      <labels>PnlVentOpen</labels>
      <labels>PnlComfOpnArea</labels>
      <labels>PnlComfStop</labels>
      <labels>PnlSlideOpnArea</labels>
      <labels>PnlFullOpen</labels>
      <labels>PnlOpenHS</labels>
      <labels>PnlOutOfRng</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="27cdbb03-7149-4ab7-aeca-0bc80bcf6569">
      <defaultValue>None_C</defaultValue>
      <enumName>VehCom_Cmd_t</enumName>
      <labels>None_C</labels>
      <labels>Stop_C</labels>
      <labels>ManOpn_C</labels>
      <labels>ManCls_C</labels>
      <labels>AutoToPos_C</labels>
      <labels>AutoToPosStep_C</labels>
      <labels>StepOpn_C</labels>
      <labels>StepCls_C</labels>
      <labels>Learn_C</labels>
      <labels>Invalid_C</labels>
      <labels>Learn_WOPreCond_C</labels>
      <labels>AutoHallToPos_C</labels>
      <labels>ManOpnCont_C</labels>
      <labels>ManClsCont_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
      <values>7</values>
      <values>8</values>
      <values>9</values>
      <values>10</values>
      <values>11</values>
      <values>12</values>
      <values>13</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="5cb047b9-fa32-4124-b0e5-1340cffb38f5">
      <defaultValue>Idle_C</defaultValue>
      <enumName>LearnAdap_Mode_t</enumName>
      <labels>Idle_C</labels>
      <labels>LearningPos_C</labels>
      <labels>AdaptionOpen_C</labels>
      <labels>AdaptionClose_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="cd964591-1aad-4c63-b223-d31e5e5da570">
      <defaultValue>NoMov_C</defaultValue>
      <enumName>BlockDet_Stall_dir</enumName>
      <labels>NoMov_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>reserved</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="be19e8fa-aaa6-471b-8e4e-ff643883e822">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MtrTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MtrTempClassA_C</labels>
      <labels>MtrTempClassB_C</labels>
      <labels>MtrTempClassC_C</labels>
      <labels>MtrTempClassD_C</labels>
      <labels>MtrTempClassE_C</labels>
      <labels>MtrTempClassF_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>6</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="1c14b33c-db0a-4c78-a65f-cb968ac82ebd">
      <defaultValue>Init_C</defaultValue>
      <enumName>ThermProt_MosfetTempClass_t</enumName>
      <labels>Init_C</labels>
      <labels>MosfetTempClassA_C</labels>
      <labels>MosfetTempClassB_C</labels>
      <labels>MosfetTempClassC_C</labels>
      <labels>MosfetTempClassD_C</labels>
      <labels>MosfetTempClassE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="66911f38-b03a-42d4-b7c0-66966c0bcfb4">
      <defaultValue>IDLE_C</defaultValue>
      <enumName>MtrCtrl_MtrDir_t</enumName>
      <labels>IDLE_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <labels>SECURE_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="0a2a0f2a-9bda-4fa2-883f-3b56a341fbb0">
      <defaultValue>DirNone_C</defaultValue>
      <enumName>HallDeco_Dir_t</enumName>
      <labels>DirNone_C</labels>
      <labels>CW_C</labels>
      <labels>CCW_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="e12632a4-2db9-48a5-be7f-06352ee52c53">
      <defaultValue>Init_C</defaultValue>
      <enumName>StateMach_Mode_t</enumName>
      <labels>Init_C</labels>
      <labels>NvMInit_C</labels>
      <labels>Startup_C</labels>
      <labels>FullRun_C</labels>
      <labels>Standby_C</labels>
      <labels>Shtdwn_C</labels>
      <labels>FastShtdwn_C</labels>
      <labels>SystemReset_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
      <values>7</values>
      <values>8</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="90e5787e-0001-4590-99b8-ea4b8a13bee9">
      <defaultValue>NoReq_C</defaultValue>
      <enumName>LearnAdap_Req_t</enumName>
      <labels>NoReq_C</labels>
      <labels>ClrReq_C</labels>
      <labels>SaveReq_C</labels>
      <labels>InvalidReq_C</labels>
      <labels>InterruptReq_C</labels>
      <labels>ClrReqHardStopOpen_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
      <values>5</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="b32b2395-0d75-4aee-9aa7-5cf4f827f6bc">
      <defaultValue>NoStall_C</defaultValue>
      <enumName>BlockDet_Stall_t</enumName>
      <labels>NoStall_C</labels>
      <labels>IncStall_C</labels>
      <labels>DecStall_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="1ba951e2-83d8-4195-8e37-ac7cf4ab88fd">
      <defaultValue>NoFault_C</defaultValue>
      <enumName>BlockDet_FltType_t</enumName>
      <labels>NoFault_C</labels>
      <labels>SamePosStallFault_C</labels>
      <labels>DoubleStallFault_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="5cb4f1fc-7e12-41a0-8fec-9dee7c90d0c1">
      <defaultValue>None_C</defaultValue>
      <enumName>APDet_tenTargetDirection</enumName>
      <labels>None_C</labels>
      <labels>Open_C</labels>
      <labels>Close_C</labels>
      <labels>Stop_C</labels>
      <storageType>int32</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="15daafe2-9212-49f3-baa2-d5172378313b">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoSyncParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>ManMov_C</labels>
      <labels>ManCls_C</labels>
      <labels>StepMov_C</labels>
      <labels>StepMovCls_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
      <values>2</values>
      <values>3</values>
      <values>4</values>
    </standaloneEnumInfos>
    <standaloneEnumInfos type="ModelRefInfoRepo.StandaloneEnumInfo" uuid="88c40989-0f01-4aaf-8a3e-8b9e7586d563">
      <defaultValue>NoCmd_C</defaultValue>
      <enumName>CfgParam_NoAppParam_t</enumName>
      <labels>NoCmd_C</labels>
      <labels>StepMovCloseReq_C</labels>
      <storageType>uint8</storageType>
      <values>0</values>
      <values>1</values>
    </standaloneEnumInfos>
    <timingAndTaskingRegistry>&lt;?xml version=&quot;1.0&quot;?&gt;
&lt;slexec_sto version=&quot;1.1&quot; packageUris=&quot;http://schema.mathworks.com/mf0/slexec_mm_sto/R2022b_202202091112&quot;&gt;
  &lt;sto.Registry type=&quot;sto.Registry&quot; uuid=&quot;f0b86b55-22be-45d0-a6fc-392cae1f3252&quot;&gt;
    &lt;executionSpec&gt;Undetermined&lt;/executionSpec&gt;
    &lt;clockRegistry type=&quot;sto.ClockRegistry&quot; uuid=&quot;054dad6f-a60a-46cd-80ac-98c823c4988b&quot;&gt;
      &lt;clocks type=&quot;sto.Timer&quot; uuid=&quot;0411739c-126d-4ba0-82ed-b5096b1af98e&quot;&gt;
        &lt;clockTickConstraint&gt;PeriodicWithFixedResolution&lt;/clockTickConstraint&gt;
        &lt;computedFundamentalDiscretePeriod&gt;.2&lt;/computedFundamentalDiscretePeriod&gt;
        &lt;resolution&gt;.2&lt;/resolution&gt;
        &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;b06d5d85-ec1a-4952-85ad-93f646c4e31a&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;ae772e60-8325-4c48-8fe7-d8f34037789b&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;baseRate type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;70645ecb-cf32-44b4-ba34-a6072b32fc38&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;3e393e7b-fe35-42fb-8188-601b3f25ddc2&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/baseRate&gt;
      &lt;/clocks&gt;
      &lt;clocks type=&quot;sto.Event&quot; uuid=&quot;847dba80-ed0d-4695-925c-4fc17dd05f01&quot;&gt;
        &lt;eventType&gt;PARAMETER_CHANGE_EVENT&lt;/eventType&gt;
        &lt;cNum&gt;1&lt;/cNum&gt;
        &lt;clockType&gt;Event&lt;/clockType&gt;
        &lt;identifier&gt;ParameterChangeEvent&lt;/identifier&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;2fb2dee3-616d-41fd-90c8-d59d6ea44351&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;ab8b0679-8952-4443-b44a-3c4455818cb8&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
      &lt;/clocks&gt;
      &lt;timeAdvanceMode&gt;FixedStep&lt;/timeAdvanceMode&gt;
    &lt;/clockRegistry&gt;
    &lt;taskRegistry type=&quot;sto.TaskRegistry&quot; uuid=&quot;0da4fcf7-4a5b-4fd7-8198-8b1bafbac59c&quot;&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;d7095d94-0ce1-458b-9990-534d147c7673&quot;&gt;
        &lt;isExplicit&gt;true&lt;/isExplicit&gt;
        &lt;rates type=&quot;sto.ModelWideEventRate&quot; uuid=&quot;c995dfde-7276-44a0-9523-7b9aa349d568&quot;&gt;
          &lt;clockId&gt;ParameterChangeEvent&lt;/clockId&gt;
          &lt;rateIdx&gt;-1&lt;/rateIdx&gt;
          &lt;taskId&gt;ModelWideParameterChangeEvent&lt;/taskId&gt;
          &lt;useForExecution&gt;NotForExecution&lt;/useForExecution&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;830fba7e-3b5f-45a4-905b-bafe4d5a3ad2&quot;&gt;
            &lt;period&gt;inf&lt;/period&gt;
            &lt;rateType&gt;ModelWideParameterChangeEvent&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;schedulingClockId&gt;ParameterChangeEvent&lt;/schedulingClockId&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;ModelWideParameterChangeEvent&lt;/identifier&gt;
        &lt;priority&gt;-1&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;rootTaskHierarchyElements type=&quot;sto.Task&quot; uuid=&quot;5b262853-11f8-47a5-886a-1e0cdad0e161&quot;&gt;
        &lt;isExecutable&gt;true&lt;/isExecutable&gt;
        &lt;orderIndex&gt;1&lt;/orderIndex&gt;
        &lt;rates type=&quot;sto.ClassicPeriodicDiscreteRate&quot; uuid=&quot;ea844f47-f454-4402-938f-995b6ab55a24&quot;&gt;
          &lt;annotation&gt;D1&lt;/annotation&gt;
          &lt;colorIndex&gt;2&lt;/colorIndex&gt;
          &lt;description&gt;Discrete 1&lt;/description&gt;
          &lt;taskId&gt;_task0&lt;/taskId&gt;
          &lt;rateSpec type=&quot;sto.RateSpec&quot; uuid=&quot;7c40c394-adbb-46aa-84e7-b0d141334a0d&quot;&gt;
            &lt;period&gt;.2&lt;/period&gt;
            &lt;rateType&gt;ClassicPeriodicDiscrete&lt;/rateType&gt;
          &lt;/rateSpec&gt;
        &lt;/rates&gt;
        &lt;elementType&gt;Task&lt;/elementType&gt;
        &lt;identifier&gt;_task0&lt;/identifier&gt;
        &lt;priority&gt;40&lt;/priority&gt;
      &lt;/rootTaskHierarchyElements&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;59f217a3-c3dc-46fd-924c-8e11af54e17a&quot;&gt;
        &lt;taskIdentifier&gt;_task0&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskDependencyGraph type=&quot;sto.SerializedTaskConnectionList&quot; uuid=&quot;49498ea5-d239-404d-9ade-5c7bc06ee1c1&quot;&gt;
        &lt;clockIdentifier&gt;ParameterChangeEvent&lt;/clockIdentifier&gt;
        &lt;taskIdentifier&gt;ModelWideParameterChangeEvent&lt;/taskIdentifier&gt;
      &lt;/taskDependencyGraph&gt;
      &lt;taskPriorityDirection&gt;HighNumberLast&lt;/taskPriorityDirection&gt;
      &lt;taskingMode&gt;ClassicMultiTasking&lt;/taskingMode&gt;
    &lt;/taskRegistry&gt;
  &lt;/sto.Registry&gt;
&lt;/slexec_sto&gt;</timingAndTaskingRegistry>
    <triggerTsType>triggered</triggerTsType>
    <triggerType>3</triggerType>
    <usePortBasedSampleTime>true</usePortBasedSampleTime>
    <zeroInternalMemoryAtStartupUnchecked>true</zeroInternalMemoryAtStartupUnchecked>
    <FMUBlockMap type="ModelRefInfoRepo.FMUBlockInfo" uuid="a8f2fafd-ebd9-40ff-9369-57644bd847ad"/>
    <codeGenInfo type="ModelRefInfoRepo.CodeGenInformation" uuid="b8256c93-0b9c-406a-b43a-f64007cc82e7">
      <DWorkTypeName>MdlrefDW_ComLog_Mdl_T</DWorkTypeName>
    </codeGenInfo>
    <compiledVariantInfos type="ModelRefInfoRepo.CompiledVariantInfoMap" uuid="e9626d78-9101-4b63-b503-e2f391775f9a"/>
    <configSettingsForConsistencyChecks type="ModelRefInfoRepo.ConfigSettingsForConsistencyChecks" uuid="663e2aef-a7e6-4d8b-9181-9f956be1e087">
      <consistentOutportInitialization>true</consistentOutportInitialization>
      <fixedStepSize>.2</fixedStepSize>
      <frameDiagnosticSetting>2</frameDiagnosticSetting>
      <hasHybridSampleTime>true</hasHybridSampleTime>
      <optimizedInitCode>true</optimizedInitCode>
      <signalLoggingSaveFormat>2</signalLoggingSaveFormat>
      <simSIMDOptimization>1</simSIMDOptimization>
      <solverName>FixedStepDiscrete</solverName>
      <solverType>SOLVER_TYPE_FIXEDSTEP</solverType>
      <hardwareSettings type="ModelRefInfoRepo.HardwareSettings" uuid="9e56ea2c-4621-4e6f-9bce-1f925ba18f78">
        <prodBitPerChar>8</prodBitPerChar>
        <prodBitPerDouble>64</prodBitPerDouble>
        <prodBitPerFloat>32</prodBitPerFloat>
        <prodBitPerInt>32</prodBitPerInt>
        <prodBitPerLong>32</prodBitPerLong>
        <prodBitPerLongLong>64</prodBitPerLongLong>
        <prodBitPerPointer>32</prodBitPerPointer>
        <prodBitPerPtrDiffT>32</prodBitPerPtrDiffT>
        <prodBitPerShort>16</prodBitPerShort>
        <prodBitPerSizeT>32</prodBitPerSizeT>
        <prodEndianess>1</prodEndianess>
        <prodLargestAtomicFloat>1</prodLargestAtomicFloat>
        <prodLargestAtomicInteger>3</prodLargestAtomicInteger>
        <prodShiftRight>true</prodShiftRight>
        <prodWordSize>32</prodWordSize>
      </hardwareSettings>
    </configSettingsForConsistencyChecks>
    <controllableInputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="4aabe113-0432-46a0-bd8b-0ede39a14412"/>
    <controllableOutputRatesMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="7e4397c7-3109-4913-b41a-8d614ee4cfd4"/>
    <dataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="c763badc-00c2-4b32-92e6-f036ae11db42">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>66</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>90</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>113</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>140</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>142</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
      <compDataInputPorts>144</compDataInputPorts>
      <compDataInputPorts>145</compDataInputPorts>
      <compDataInputPorts>146</compDataInputPorts>
      <compDataInputPorts>147</compDataInputPorts>
      <compDataInputPorts>148</compDataInputPorts>
      <compDataInputPorts>149</compDataInputPorts>
      <compDataInputPorts>150</compDataInputPorts>
      <compDataInputPorts>151</compDataInputPorts>
      <compDataInputPorts>152</compDataInputPorts>
      <compDataInputPorts>153</compDataInputPorts>
      <compDataInputPorts>154</compDataInputPorts>
      <compDataInputPorts>155</compDataInputPorts>
      <compDataInputPorts>156</compDataInputPorts>
      <compDataInputPorts>157</compDataInputPorts>
      <compDataInputPorts>158</compDataInputPorts>
      <compDataInputPorts>159</compDataInputPorts>
      <compDataInputPorts>160</compDataInputPorts>
      <compDataInputPorts>161</compDataInputPorts>
      <compDataInputPorts>162</compDataInputPorts>
      <compDataInputPorts>163</compDataInputPorts>
      <compDataInputPorts>164</compDataInputPorts>
      <compDataInputPorts>165</compDataInputPorts>
      <compDataOutputPorts>0</compDataOutputPorts>
      <dataInputPorts>0</dataInputPorts>
      <dataInputPorts>1</dataInputPorts>
      <dataInputPorts>2</dataInputPorts>
      <dataInputPorts>3</dataInputPorts>
      <dataInputPorts>4</dataInputPorts>
      <dataInputPorts>5</dataInputPorts>
      <dataInputPorts>6</dataInputPorts>
      <dataInputPorts>7</dataInputPorts>
      <dataOutputPorts>0</dataOutputPorts>
    </dataPortGroup>
    <expFcnUnconnectedDataPortGroup type="ModelRefInfoRepo.DataPortGroup" uuid="ea614967-9d14-41a1-ad4e-25107de6a3d2">
      <compDataInputPorts>0</compDataInputPorts>
      <compDataInputPorts>1</compDataInputPorts>
      <compDataInputPorts>2</compDataInputPorts>
      <compDataInputPorts>3</compDataInputPorts>
      <compDataInputPorts>4</compDataInputPorts>
      <compDataInputPorts>5</compDataInputPorts>
      <compDataInputPorts>6</compDataInputPorts>
      <compDataInputPorts>7</compDataInputPorts>
      <compDataInputPorts>8</compDataInputPorts>
      <compDataInputPorts>9</compDataInputPorts>
      <compDataInputPorts>10</compDataInputPorts>
      <compDataInputPorts>11</compDataInputPorts>
      <compDataInputPorts>12</compDataInputPorts>
      <compDataInputPorts>13</compDataInputPorts>
      <compDataInputPorts>14</compDataInputPorts>
      <compDataInputPorts>15</compDataInputPorts>
      <compDataInputPorts>16</compDataInputPorts>
      <compDataInputPorts>17</compDataInputPorts>
      <compDataInputPorts>18</compDataInputPorts>
      <compDataInputPorts>19</compDataInputPorts>
      <compDataInputPorts>20</compDataInputPorts>
      <compDataInputPorts>21</compDataInputPorts>
      <compDataInputPorts>22</compDataInputPorts>
      <compDataInputPorts>23</compDataInputPorts>
      <compDataInputPorts>24</compDataInputPorts>
      <compDataInputPorts>25</compDataInputPorts>
      <compDataInputPorts>26</compDataInputPorts>
      <compDataInputPorts>27</compDataInputPorts>
      <compDataInputPorts>28</compDataInputPorts>
      <compDataInputPorts>29</compDataInputPorts>
      <compDataInputPorts>30</compDataInputPorts>
      <compDataInputPorts>31</compDataInputPorts>
      <compDataInputPorts>32</compDataInputPorts>
      <compDataInputPorts>33</compDataInputPorts>
      <compDataInputPorts>34</compDataInputPorts>
      <compDataInputPorts>35</compDataInputPorts>
      <compDataInputPorts>36</compDataInputPorts>
      <compDataInputPorts>37</compDataInputPorts>
      <compDataInputPorts>38</compDataInputPorts>
      <compDataInputPorts>39</compDataInputPorts>
      <compDataInputPorts>40</compDataInputPorts>
      <compDataInputPorts>41</compDataInputPorts>
      <compDataInputPorts>42</compDataInputPorts>
      <compDataInputPorts>43</compDataInputPorts>
      <compDataInputPorts>44</compDataInputPorts>
      <compDataInputPorts>45</compDataInputPorts>
      <compDataInputPorts>46</compDataInputPorts>
      <compDataInputPorts>47</compDataInputPorts>
      <compDataInputPorts>48</compDataInputPorts>
      <compDataInputPorts>49</compDataInputPorts>
      <compDataInputPorts>50</compDataInputPorts>
      <compDataInputPorts>51</compDataInputPorts>
      <compDataInputPorts>52</compDataInputPorts>
      <compDataInputPorts>53</compDataInputPorts>
      <compDataInputPorts>54</compDataInputPorts>
      <compDataInputPorts>55</compDataInputPorts>
      <compDataInputPorts>56</compDataInputPorts>
      <compDataInputPorts>57</compDataInputPorts>
      <compDataInputPorts>58</compDataInputPorts>
      <compDataInputPorts>59</compDataInputPorts>
      <compDataInputPorts>60</compDataInputPorts>
      <compDataInputPorts>61</compDataInputPorts>
      <compDataInputPorts>62</compDataInputPorts>
      <compDataInputPorts>63</compDataInputPorts>
      <compDataInputPorts>64</compDataInputPorts>
      <compDataInputPorts>65</compDataInputPorts>
      <compDataInputPorts>67</compDataInputPorts>
      <compDataInputPorts>68</compDataInputPorts>
      <compDataInputPorts>69</compDataInputPorts>
      <compDataInputPorts>70</compDataInputPorts>
      <compDataInputPorts>71</compDataInputPorts>
      <compDataInputPorts>72</compDataInputPorts>
      <compDataInputPorts>73</compDataInputPorts>
      <compDataInputPorts>74</compDataInputPorts>
      <compDataInputPorts>75</compDataInputPorts>
      <compDataInputPorts>76</compDataInputPorts>
      <compDataInputPorts>77</compDataInputPorts>
      <compDataInputPorts>78</compDataInputPorts>
      <compDataInputPorts>79</compDataInputPorts>
      <compDataInputPorts>80</compDataInputPorts>
      <compDataInputPorts>81</compDataInputPorts>
      <compDataInputPorts>82</compDataInputPorts>
      <compDataInputPorts>83</compDataInputPorts>
      <compDataInputPorts>84</compDataInputPorts>
      <compDataInputPorts>85</compDataInputPorts>
      <compDataInputPorts>86</compDataInputPorts>
      <compDataInputPorts>87</compDataInputPorts>
      <compDataInputPorts>88</compDataInputPorts>
      <compDataInputPorts>89</compDataInputPorts>
      <compDataInputPorts>91</compDataInputPorts>
      <compDataInputPorts>92</compDataInputPorts>
      <compDataInputPorts>93</compDataInputPorts>
      <compDataInputPorts>94</compDataInputPorts>
      <compDataInputPorts>95</compDataInputPorts>
      <compDataInputPorts>96</compDataInputPorts>
      <compDataInputPorts>97</compDataInputPorts>
      <compDataInputPorts>98</compDataInputPorts>
      <compDataInputPorts>99</compDataInputPorts>
      <compDataInputPorts>100</compDataInputPorts>
      <compDataInputPorts>101</compDataInputPorts>
      <compDataInputPorts>102</compDataInputPorts>
      <compDataInputPorts>103</compDataInputPorts>
      <compDataInputPorts>104</compDataInputPorts>
      <compDataInputPorts>105</compDataInputPorts>
      <compDataInputPorts>106</compDataInputPorts>
      <compDataInputPorts>107</compDataInputPorts>
      <compDataInputPorts>108</compDataInputPorts>
      <compDataInputPorts>109</compDataInputPorts>
      <compDataInputPorts>110</compDataInputPorts>
      <compDataInputPorts>111</compDataInputPorts>
      <compDataInputPorts>112</compDataInputPorts>
      <compDataInputPorts>113</compDataInputPorts>
      <compDataInputPorts>114</compDataInputPorts>
      <compDataInputPorts>115</compDataInputPorts>
      <compDataInputPorts>116</compDataInputPorts>
      <compDataInputPorts>117</compDataInputPorts>
      <compDataInputPorts>118</compDataInputPorts>
      <compDataInputPorts>119</compDataInputPorts>
      <compDataInputPorts>120</compDataInputPorts>
      <compDataInputPorts>121</compDataInputPorts>
      <compDataInputPorts>122</compDataInputPorts>
      <compDataInputPorts>123</compDataInputPorts>
      <compDataInputPorts>124</compDataInputPorts>
      <compDataInputPorts>125</compDataInputPorts>
      <compDataInputPorts>126</compDataInputPorts>
      <compDataInputPorts>127</compDataInputPorts>
      <compDataInputPorts>128</compDataInputPorts>
      <compDataInputPorts>129</compDataInputPorts>
      <compDataInputPorts>130</compDataInputPorts>
      <compDataInputPorts>131</compDataInputPorts>
      <compDataInputPorts>132</compDataInputPorts>
      <compDataInputPorts>133</compDataInputPorts>
      <compDataInputPorts>134</compDataInputPorts>
      <compDataInputPorts>135</compDataInputPorts>
      <compDataInputPorts>136</compDataInputPorts>
      <compDataInputPorts>137</compDataInputPorts>
      <compDataInputPorts>138</compDataInputPorts>
      <compDataInputPorts>139</compDataInputPorts>
      <compDataInputPorts>140</compDataInputPorts>
      <compDataInputPorts>141</compDataInputPorts>
      <compDataInputPorts>142</compDataInputPorts>
      <compDataInputPorts>143</compDataInputPorts>
      <compDataInputPorts>144</compDataInputPorts>
      <compDataInputPorts>145</compDataInputPorts>
      <compDataInputPorts>146</compDataInputPorts>
      <compDataInputPorts>147</compDataInputPorts>
      <compDataInputPorts>148</compDataInputPorts>
      <compDataInputPorts>149</compDataInputPorts>
      <compDataInputPorts>150</compDataInputPorts>
      <compDataInputPorts>151</compDataInputPorts>
      <compDataInputPorts>152</compDataInputPorts>
      <compDataInputPorts>153</compDataInputPorts>
      <compDataInputPorts>154</compDataInputPorts>
      <compDataInputPorts>155</compDataInputPorts>
      <compDataInputPorts>156</compDataInputPorts>
      <compDataInputPorts>157</compDataInputPorts>
      <compDataInputPorts>158</compDataInputPorts>
      <compDataInputPorts>159</compDataInputPorts>
      <compDataInputPorts>160</compDataInputPorts>
      <compDataInputPorts>161</compDataInputPorts>
      <compDataInputPorts>162</compDataInputPorts>
      <compDataInputPorts>163</compDataInputPorts>
      <compDataInputPorts>164</compDataInputPorts>
    </expFcnUnconnectedDataPortGroup>
    <interfaceParameterInfo type="ModelRefInfoRepo.InterfaceParameterInfo" uuid="98313214-f6a8-4f08-9e7a-1580d770e58d"/>
    <messageInfo type="ModelRefInfoRepo.MessageInformation" uuid="11b67aaf-ecbe-4565-987b-df00a9de516b">
      <rootInportInfo type="ModelRefInfoRepo.MessageRootPortInfo" uuid="be85d258-4de5-4c12-8069-39003af46f4d">
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="8d97acb0-f8a0-4a0d-baac-856fe8d5f3a5"/>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="53078614-295b-4aa6-918f-9c982ee4c8cd">
          <portIdx>1</portIdx>
        </GrHierarchyMsgModeMap>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="a79c686b-e67c-4f1e-bfc0-96df13ef7be7">
          <portIdx>2</portIdx>
        </GrHierarchyMsgModeMap>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="aa397985-1737-457e-9ab1-7f8f6b245545">
          <portIdx>5</portIdx>
        </GrHierarchyMsgModeMap>
        <GrHierarchyMsgModeMap type="ModelRefInfoRepo.MessageModeMap" uuid="a77abd30-2b00-4b49-8bf3-7936505d3d90">
          <portIdx>6</portIdx>
        </GrHierarchyMsgModeMap>
      </rootInportInfo>
    </messageInfo>
    <methodInfo type="ModelRefInfoRepo.MethodExistenceInfo" uuid="9ce03add-9335-40fd-86fc-36ed3e621fa6">
      <hasSystemInitializeMethod>true</hasSystemInitializeMethod>
      <hasSystemResetMethod>true</hasSystemResetMethod>
      <hasTerminateMethod>true</hasTerminateMethod>
    </methodInfo>
    <periodicEventPortUnsupportedBlockInfo type="ModelRefInfoRepo.PeriodicEventPortUnsupportedBlockInfo" uuid="b840e02f-3508-4ff4-8844-d0db501df398"/>
    <portGroupsRequireSameRate type="ModelRefInfoRepo.PortGroupsRequireSameRate" uuid="3f55280c-ef02-4abe-9676-2e89f8bf49c1">
      <DSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="0bffe4f2-**************-6d61e72100bd"/>
      <GlobalDSMPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="7fa202ff-2977-44fe-9a4b-41e0914ff544"/>
      <mergedPortGroups type="ModelRefInfoRepo.NameToPortGroupIdxVectMap" uuid="4117e9b8-95dd-4b02-a689-757f80a26158"/>
    </portGroupsRequireSameRate>
    <rateBasedMdlGlobalDSMRateSpec type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="b50af543-7ff8-4402-bff3-22df947be081">
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="f1f20acd-6f85-483e-a77e-9d01c2334ba3">
        <dSMName>CfgParam_CAL</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="74be91c7-e5d8-462d-a627-bf9c40ecb9db">
          <blockName>ComLog_Mdl/Data Store Read</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="1fa5048a-88d0-4583-9c04-7ade6881a0e6">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
      <entries type="ModelRefInfoRepo.GlobalDSMRateSpecAssociation" uuid="4679b931-1698-4fd4-a7be-b797f06c0b25">
        <dSMName>PIDDID_stDiagRtnCmd</dSMName>
        <rateSpecInfoVect type="ModelRefInfoRepo.GlobalDSMRateSpecInfo" uuid="9f7af14d-d67b-46fe-9d7f-a55af9ea6b7b">
          <blockName>ComLog_Mdl/Data Store Read1</blockName>
          <nonFcnCallPartitionName>D1</nonFcnCallPartitionName>
          <rateSpec type="ModelRefInfoRepo.RateSpec" uuid="97305866-d45b-4f6c-8d38-b6cc6ab3e05e">
            <period>.2</period>
          </rateSpec>
        </rateSpecInfoVect>
      </entries>
    </rateBasedMdlGlobalDSMRateSpec>
    <rateSpecOfGlobalDSMAccessedByDescExpFcnMdlMap type="ModelRefInfoRepo.GlobalDSMRateSpecMap" uuid="1c2f89ef-bd0f-4e9b-af09-cb64b822e1c8"/>
    <rootBlockDiagramInterface type="ci.Model" uuid="348fef54-5d27-4d4d-9337-3213cd19f4f5">
      <p_RootComponentInterface type="ci.ComponentInterface" uuid="52761633-9a65-41b2-9f9c-e4fdb43ad873">
        <p_InputPorts type="ci.SignalInterface" uuid="135d4364-2b51-4cbe-9c21-ae809e059eec">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_MovType_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1c1f46db-2f9a-4c56-8c9b-fe06ec18e7cf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="941faa08-780a-4bd3-b81a-9012bfd94b25">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_RelearnMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0529c371-0bf1-410a-a92f-864047a5a050">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SysFltDiag_LastStopReason_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dbef73a0-110b-43a4-81e7-81cfbd644c00">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="438dd8f3-9048-422f-8a9a-0adc00231512">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="811bbddb-f987-4d7b-aea8-80e0f42c3ecb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d6b08b67-4979-4b48-bbc6-99db56b1f687">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_MtrCmd_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b69cb098-79de-4488-bd3b-134d58bd8076">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="00bd1340-d271-4736-8c72-ba3f0d1729fb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="54d9523b-48ec-421f-b85b-8a1ac4da3929">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_Rev_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="04f287de-4126-414b-aee2-8d8defd50169">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_ATS_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6391a532-1a97-46da-919d-779615cac128">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>sfix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="75074080-e955-4dde-9c2c-c62b5f45539a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>%</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="25a75056-6a8b-4ace-9eb6-51fe012a8b31">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8d7287af-895e-48ec-b81e-e98ddc7b3547">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="92ff8334-867b-494a-a7b6-b54190d24db7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2859c5b4-c88a-4f99-924d-919fb1762026">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fb3c914c-e6a3-4e88-b6c4-5cf7f4d9092b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dd4f1b28-3b66-486e-9e29-d9e629a9db77">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3ba5a52c-e354-4793-9a57-7d10fc49fe45">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ffd8ae78-9695-4dab-883a-04887ae2a0cf">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5e8c0d5e-85a3-40d4-8d7c-56ddb4ac4542">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b28e8ae8-ffc6-4b3f-9420-43bcfb28a225">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6b9c89d9-9e2c-42ed-8942-0dffcdabf82d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2c7bbccc-cdf9-4125-a28c-806f337e7475">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c27001be-fae8-4574-aa9e-8fa6c1a8a2a7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7de863e2-f474-4d96-b9fe-a734f3122c91">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint32</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="23679bc5-003f-4510-86c9-6dc718d5f7e6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="78768cca-b0e0-4562-8bd8-16729af40914">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1d01dea9-08a5-4c39-8d36-1bf19c386627">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e7faad5f-2319-410e-af60-39187810904b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="01ba5226-fa9a-4b1b-b0a5-6c9983525ff1">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ac5b02a1-422a-4bd5-bd47-44de8a171a92">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="22d6523f-**************-4f5246e6ecb7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9d1a65ec-994a-446f-93d3-d72d51e615e2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5aadca8c-affe-4a6a-9869-ce1a4f6155e8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ca7eacc6-0726-4fd2-a589-3e151b07778c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="a43a30ed-6368-43bd-84f0-98589041b04b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>N</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ea72c468-18d4-47b8-a2af-3f5c53deb4fd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3af4b8b9-47c2-4595-8107-bade336c8780">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2c762ad3-966c-4ea2-804a-999babcbef41">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="3eabe980-2c31-42b0-b262-764eccbd874b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>stdReturn_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="334b1c8d-dddf-4740-ab16-96f7e10fc67f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="95a0743b-7fbe-42a7-a60d-aefcae8eef98">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ec236399-5d1b-4d91-8698-c04043226fb6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="03da97b8-67ec-4fc3-9f42-c64876791e27">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d33b6e99-d0f9-4824-9eab-5b368f19a010">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e695a360-b72b-4e6d-953d-cc703b06f225">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e97123ee-5c93-4fe7-865b-9820ffd9d769">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9d6de20a-7419-49a7-a2b7-d59edf34f123">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e415249a-4b5d-4722-b9a1-0946346a7823">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d8328b6c-2b86-42c5-905a-88e95228551c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e10aa2e8-cc7b-4dd0-ba62-88d6c8507b9e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6a1d7737-2646-479d-aac2-8eefd044c70f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CfgParam_TestMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f596a3c4-1fb3-46be-8f03-8f955fce7070">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="16c9913f-19ba-436a-b982-42d9ca6fc58b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fe1e0367-84c0-4d3e-b6a1-cb05143f4d86">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0e745a5e-e380-4e2a-a6be-c0b33484b61f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c2138864-3f48-4a95-a91a-cfd7abd1bdb2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="332a3da3-bd7f-42e3-8ba1-abae6506a8f2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b9a61fa8-920d-416b-aa81-d354f8c38714">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="107055e4-9f2b-4017-8dec-0f655118b121">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="d0630100-2595-4df4-af4e-3b4b0f975434">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1b68f338-99ff-4202-a2a9-06d32abebf65">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1d5bcb78-031a-4b72-909e-a1adf3634159">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2d1337b5-8cf6-483a-aefc-820923b5a62b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6781648d-65af-4ff1-b775-0e6c9f3d868b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5aec96a4-74c7-4d2e-b9d8-bd4cba1a8036">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1aae0cce-e568-41f2-acf6-e568ef5aff76">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="2869d467-db7b-49e5-9b05-a5e7ad16ed56">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="91eb1f62-22dd-48ce-87c5-2d8803b57500">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bfeb8ebc-4950-4835-904a-94b9338aa5c2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_Area_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1efb5df1-206a-49a9-9028-89cb0e16ca51">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PosMon_PnlPosArea_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9d848377-09a6-4feb-9cb0-f429f5ae8f55">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e006b81b-0bf3-4b63-a68f-1b593b89a187">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f1e2d040-98fd-4a73-9397-5bd66aa53270">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e4ebb35b-331c-4714-964a-48d1c08601ef">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ufix16_En8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="7f353ccf-5948-48d5-85ae-5bab7a7e808f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="72b9fff7-50a2-4801-a99b-7f2b7f275d25">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c4be78ef-21c7-4017-a16c-fda40c2492cd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b1c79f63-b77c-4443-bc79-563a8db6e1db">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0aa7f8d2-b743-4c45-8e4b-9f7403737eea">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="07ea599e-407c-4a63-b124-cb27614ecf51">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="138a5d13-f6bf-4ef7-9bfd-e1ddfba9353d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="44fd16e8-22cb-40ab-bc9c-a253190a4d1b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="053d2ea0-4edc-482e-9105-a91437e01c5a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="645fbf54-6a62-4a3e-a229-b8d095b3bf0d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0c156b38-dead-4612-9b0f-28557a2e6d09">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4448a6b3-d8f1-41fc-b66b-1fb2d2a43825">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5d63738e-9976-4931-922f-de788b19b29b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VehCom_Cmd_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="01749843-7b8f-4bff-bf62-2e4e40e9ec1c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="92057362-7ec6-4b23-8591-603584de8f58">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="05688cef-0356-4cbd-a3a8-67c041db62ff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fbee518b-e78e-4fc2-8399-1ed651bf3715">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="347d329c-0856-4d14-b5c5-cc586bce6b1c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fda8c36a-6a9a-4cd5-891d-923e1473fdcd">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="b907e5a0-e631-4cab-a7ac-9b48dc23f4fa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cb0c7e2b-0b35-4f62-8ed8-74faca275abc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1ff3f74f-f75c-490e-8f39-6e78ab6f1255">
          <p_ComputedNumericDimensions>3.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="20d994d5-a660-4150-a3d1-cc7c89b6cca4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SysFltDiag_LastStopReason_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bff34aff-9fd2-461a-ae93-bc30ded39889">
          <p_ComputedNumericDimensions>10.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4d8d48b6-faa9-46a1-9b5d-4ed33fcecf57">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="966adbc7-967f-4c4a-9fd4-4fb713e8f06f">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>LearnAdap_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bcaf2ddb-c259-423a-b7f9-5cc811922226">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="703abae6-9c8f-4532-a05c-6b55dc298e8d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5566603b-02de-4ee4-8f6d-fed255614c47">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c3253779-2b8b-48ce-84b5-819c26855529">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>BlockDet_Stall_dir</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="8322fbaa-5809-48c2-abc4-defe11b31263">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bcc02cf0-7b21-4832-96f5-74b052284f9b">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="628764c8-5565-474c-9263-41ff7ab55c82">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="919683a5-b849-4284-ba56-e14c43ae302c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1a868c0a-c598-4307-b046-0c31da573afa">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>VoltMon_VoltClass_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="91fcd9d4-e512-451b-8756-9dbe08819e41">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e3eae982-50e4-4a6d-b968-12e0a5c2458a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ecafeaf3-b557-4c1a-8e81-676e0fcf19ff">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9f30d519-c8e4-4955-b74a-cef6979b84df">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MtrTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fc1d1fa7-edf3-4ab9-8c88-18871507dc4c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="234dea58-a12f-4b0a-a8b8-580f508f4751">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ThermProt_MosfetTempClass_t</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="214d1916-4bc3-463e-8a2b-fff7e9950e25">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>PnlOp_ATS_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ba082cbf-f440-4e86-a1f1-edab31351753">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5cc2071a-be5a-4973-83a8-22c66003dd90">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>MtrCtrl_MtrDir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="07215824-43ef-4199-9e92-464267e6ab93">
          <p_ComputedNumericDimensions>2.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bf370f0e-bc81-4887-9251-a40ca793b559">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5f40ef70-cdf3-40a5-ae9b-d73bc2a822cb">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_RelearnMode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="57f97173-b2ff-493e-97ad-ae9e34ae57c4">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>count</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="738774bb-b602-4f12-9fb6-6519aaa3a977">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f580919d-36e3-4614-965c-bc94d66e261d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cb75ae69-af5e-42b2-a2b4-284fd6880c1d">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="605c8ceb-30b2-4e4a-a711-0ee9291c515c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6baac41c-8b12-4a0c-a110-448ace19d133">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint32</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="cfd660df-f7ae-42f1-89cb-494262ed8f9c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ce0b1f84-a632-47fe-8d1c-4945fbcbe1ee">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c73878e8-0825-48da-93c9-2ad61156fa0e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6fb783d2-b4ea-4bb1-b768-74ec9aa91001">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c66d5d3a-bc62-44c3-8a27-987f9ac11f08">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="41c15b9f-1e47-426a-aaac-23302dfb9ede">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bed5928f-0c43-4f61-9aa1-************">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0c9a7fd8-8548-437d-85e5-146f79de6555">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="080674e6-9704-4ce3-b6e9-3a880f0612c2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c8ca71b0-68d5-4e93-9645-b1a2b8e75708">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>CtrlLog_tenTargetDirection</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="fd7cc4b2-d0ea-4817-88d9-203fb703777c">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="1bbf4941-f25d-4e66-ba94-947efaaac081">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>HallDeco_Dir_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5c42c866-d662-4b1b-956d-1f20cba90132">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="893e92d4-3b24-421e-92a0-d946d1aa0cde">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>rad/s</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ea720674-ba7f-453f-bce9-f3448fe724c7">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="0ba96b99-8464-4539-8a05-f8015bb4bce6">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6fa357bf-22ea-4f8b-8099-648cfba0ddd3">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="16b0ae79-5acf-4f9c-8cfc-8ea0cf35bd36">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>int16</p_ComputedType>
          <p_ComputedUnit>degC</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="ae037b15-a0d5-46f3-830c-6f739de99bd0">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="9385e0c9-495b-4a43-bd06-d7f21f01f181">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="6f11f5ce-4864-4c94-879f-b5061ee1b39a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint16</p_ComputedType>
          <p_ComputedUnit>mV</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5f5d6f78-038d-42c3-bac1-a89c090e7e4a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>StateMach_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e6fd8f89-1b94-4eab-94a8-5413d36cced2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="340dec4d-dd0f-445b-b0f2-0d5372454139">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="bb9d41a9-3569-42da-a33b-398b8a11a356">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="794ad96d-56e9-4989-82f7-69ce3599b078">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f7d8dea2-1336-412c-ab71-5d7353492cf8">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5d7ce787-0e2b-4346-a251-f19171dd7ad2">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
          <p_ComputedUnit>1</p_ComputedUnit>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="5c3e8289-6d6c-4936-b445-53261c33d758">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="c5dbaf93-cc99-462d-b260-2f06d5f95c89">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="dc1786c0-bb5f-4603-a53e-8eb7e0720c39">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="e67068c9-d9ae-41ed-ad9e-f1d08ac8939e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>boolean</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="4c8b837f-77a0-4828-ab60-2ccd1c3daffc">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>uint8</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="876992c2-da86-4c3e-a6e4-b14c0cb4c545">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>StateMach_Mode_t</p_ComputedType>
        </p_InputPorts>
        <p_InputPorts type="ci.SignalInterface" uuid="f7a77f61-41a0-4d59-9d8e-ced5d374799a">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>SWC_ObjDetLyrBus</p_ComputedType>
        </p_InputPorts>
        <p_Name>ComLog_Mdl</p_Name>
        <p_OutputPorts type="ci.SignalInterface" uuid="4ae817f4-d9fb-4450-a207-1280a6ba260e">
          <p_ComputedNumericDimensions>1.0</p_ComputedNumericDimensions>
          <p_ComputedSampleTime>-1.0</p_ComputedSampleTime>
          <p_ComputedSampleTime>0.0</p_ComputedSampleTime>
          <p_ComputedType>ComLogBus</p_ComputedType>
        </p_OutputPorts>
        <p_Type>ROOT</p_Type>
      </p_RootComponentInterface>
    </rootBlockDiagramInterface>
    <simulinkFunctions type="ModelRefInfoRepo.SimulinkFunctions" uuid="f0b7d747-5afb-4364-b86b-fb77bde0a12f">
      <compSimulinkFunctionCatalog></compSimulinkFunctionCatalog>
    </simulinkFunctions>
    <sltpContext type="sltp.mm.core.Context" uuid="1d4d49e0-49ba-4367-9f81-7937fe4805ee">
      <globalData type="sltp.mm.core.GlobalData" uuid="0eb236ac-6eca-4519-93b8-3b41b5535ae8">
        <dataName>CfgParam_CAL</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1d3cf1da-f4e6-48bf-aa18-bebe449027f1">
        <dataName>PIDDID_stDiagRtnCmd</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="51e3bcf1-54cd-4d63-8833-0ccdb648229e">
        <dataName>PIDDID_stDiagSwchCmd</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47483820-74dc-49b4-86c9-091e32f7b9c6">
        <dataName>overWriteVehCom</dataName>
        <type>DataStoreMemory</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ff2bc536-2d92-49d0-aac4-6f3c04bb0f0c">
        <dataName>portComLogBus</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="76457c9c-51e6-4c5e-b525-e44178144fef">
        <dataName>portCtrlLogBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a7223db6-429f-40ab-8493-b54a0268140b">
        <dataName>portCtrlLogBus_CtrlLog_hReactTgtPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6dae1f70-ee18-494e-b93f-103b12ce949e">
        <dataName>portCtrlLogBus_CtrlLog_isMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bdefcbcd-1e84-4c96-87ba-06d16e81b689">
        <dataName>portCtrlLogBus_CtrlLog_isPnlMovIdle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4db7eaf6-3195-4dfe-ae19-c96ab41dcff2">
        <dataName>portCtrlLogBus_CtrlLog_stDirCommand</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="605ca341-e18e-4e7b-813b-ca32fa004a74">
        <dataName>portCtrlLogBus_CtrlLog_stLastStopReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c8f3b7e6-c321-45ea-b01d-2edbb1ef4941">
        <dataName>portCtrlLogBus_CtrlLog_stLearnInt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b4385aef-05dd-4602-bf7e-02f8020609aa">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movReact</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="55e09222-19e7-4656-8a73-a57ee6e34c35">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movSrc</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="22b2aee7-12e0-4a1b-8bc7-0e5655aa2e66">
        <dataName>portCtrlLogBus_CtrlLog_stMovReact_movType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="416b4662-66ea-4574-b2a3-a9b4a24fc299">
        <dataName>portCtrlLogBus_CtrlLog_stMovType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0c11a1b9-437f-4329-ac3b-09b3cbd66140">
        <dataName>portCtrlLogBus_CtrlLog_stRelearnMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d2255caa-fccf-440b-a557-f871246f6c25">
        <dataName>portInport1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2f801c15-ffa6-4616-aec7-c65b35f4dcba">
        <dataName>portOutport</dataName>
        <type>RootOutport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="50eebcb7-cb05-429a-98da-0afa6dc0a6c6">
        <dataName>portPnlOpBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="441ad9c9-3bf4-43f8-9790-8c60289f777e">
        <dataName>portPnlOpBus_PnlOp_hTrgtPosPtcd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9c27aa80-1cc2-49d8-b7b0-2bc54046954a">
        <dataName>portPnlOpBus_PnlOp_isMtrMoving</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f5ec4657-aa2c-4900-afab-1c93c1a8ffc3">
        <dataName>portPnlOpBus_PnlOp_pMtrSpdCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="84b54f98-01f1-4d7a-b82f-4fb17973c17d">
        <dataName>portPnlOpBus_PnlOp_stATS</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e1a38365-3f80-400a-947d-2f4140407d40">
        <dataName>portPnlOpBus_PnlOp_stMtrCtrlCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="89000210-8e38-439f-b24d-27050535ee4a">
        <dataName>portPnlOpBus_PnlOp_stNormalMov</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a8723aa3-b596-407b-9ff7-60c382f5ed1f">
        <dataName>portPnlOpBus_PnlOp_stReversal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="aa37e38b-f207-41d4-8881-2e1e8e717d6f">
        <dataName>portSWC_CommLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b336e6e9-d899-4c66-ad1c-d52977c0325b">
        <dataName>portSWC_CommLyrBus_VehComBus_DTC_VehSig_LossCommLIN</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="67716340-441a-4296-9e10-53592b540c39">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="67216a9f-5daf-4769-8098-558e0c3ead3b">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_bSleepReady</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="39d53bc6-3746-4680-951b-680d959a91dd">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_hToPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="455c662b-cb5a-4f1d-94e2-0f28438f4d50">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_isAuthEnbl</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ecd0de5f-c6fe-4310-bf98-a92a39333a32">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_isVehSpdVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="07681f33-63fa-42c3-94fa-6df90cb1111c">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_percRelPosn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="64cb4ef4-c73c-49d7-8107-1c53df672e32">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_stVehCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="66bb2301-565c-4905-aab6-1252517bea8d">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_typeCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="990570b8-f50b-4ee0-9943-f271f4889643">
        <dataName>portSWC_CommLyrBus_VehComBus_VehCom_vVehSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a2a0b995-a074-46fc-9ce5-aaf8a8cef810">
        <dataName>portSWC_DataHndlLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="26a90256-615f-4846-be7c-ce50a9139326">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SCUInfo</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="61d767e1-a453-4a70-9438-f7525db3db36">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_SWVersion</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="76ac4f8f-0b25-4d42-93bc-9b25d1e8b2f5">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isCALFileVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1cb4d226-e244-4ad6-bde3-90ebbdefe64a">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isPosTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bc054fd3-529d-41f0-beb6-6a668a33e15d">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="01f2bdc3-ccbe-42e2-aeb2-bbfa69c44715">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c2de9ad9-35cd-4157-bdec-135b3368d001">
        <dataName>portSWC_DataHndlLyrBus_CfgParamBus_CfgParam_stTestMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8ec7bffd-a7b0-4a69-b624-f937faa7410a">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currByteOfRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="367520dc-65fd-47f9-bd94-e757324ed70c">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_currRFArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1454608c-b42e-48c5-9f03-4f6fb0211cc8">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isInsideRF</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bb6e870b-b1c4-4b91-8092-3ce84b78f7de">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="37e6fb20-7afb-4753-8d4a-6377f173124d">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="47e33d6c-83d6-4d17-8c5b-b179e0dc8095">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmBak</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0e3bf97d-acfa-4008-9a20-76a5366a5ed2">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isRFTblVldNvmSync</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f627950b-23bd-457e-acdd-7b4c2b0836eb">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3e552235-a7af-45e3-9078-e438a26783a8">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="45f8eef6-4ac6-4b0a-ad19-0be1b3ae73bf">
        <dataName>portSWC_DataHndlLyrBus_RefFieldBus_RefField_stRFSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="29f48f7a-59a3-4229-a3dd-ffeb395dee3b">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_AmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="505db1c9-9947-4b8b-b96a-2e33f0f2345c">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_CycRenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="72065eb5-abb3-412d-b151-e90514f858b2">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LeanrFailCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d0e76335-8ade-4553-bf97-d5b2dd6ad7f8">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_LearnSuccessCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b962d3e4-61cd-4a1a-a369-472f7534d75c">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MOSFETTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f571295b-7180-4eaf-a4dc-d1a623640e7f">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c66673e2-13df-469f-bc12-83a9d369bad4">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MaxBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="98e024eb-edfe-4aa0-aa98-d39ea7ea8bff">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="97d33cb6-9cc6-4e77-a609-c072602dbd3d">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MinBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="598150f4-8bb3-4094-abde-a08c91a62156">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_MotorOperTime</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b4cff304-6fac-494c-90cc-c80b9db89df1">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_RenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cf8eba68-c783-4ada-a644-585516bcfd11">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReprogrammingCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="72e418c7-3cc9-4aaf-b1d3-4ccb7bf8cf66">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dae93a29-8545-4af8-af07-38b3b1da56a3">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ab1fa3fe-a6d9-422c-acdd-52b9343a510f">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7d0e10ae-6329-4175-aa26-b9f067f53d73">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ReversalThres</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3f3ad853-9bb8-4fb1-b117-51afc792486a">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_SlideCycCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="37ab0e5c-6b9b-4aed-85f5-a64472252a88">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_StallCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6646f90e-b7cd-4da2-bbd5-6d9146651e00">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstCaseTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d2bc5c58-dd49-4573-b6b2-820d659c1480">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9602e59f-54f9-4903-aeb8-ff964e9a8852">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_TEstMtrTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3a2e9c5d-e4ac-444e-8d4f-debdc786fc3c">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_ThermProtCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e4ed4f16-195b-46eb-b475-26acfb39cc99">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="73290599-08da-4a42-b9a9-f20dbe83c1fe">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a619bcbc-13d1-490e-b9f9-057197841423">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a5a37575-4d4d-4bfb-8500-675fa7b1ebaa">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHist_unlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4f8b1f5f-c5c3-4efc-8bc5-24f8b724c88c">
        <dataName>portSWC_DataHndlLyrBus_UsgHistBus_UsgHistl_TEstCaseTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3b8c4237-c2f7-4345-9852-3f5c153c5364">
        <dataName>portSWC_DiagLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a7e80d47-c03e-4a4b-a365-498a6e8bc898">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c47b6dc4-1c8e-430d-ab0f-493ecb1351e9">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_TMOSFETTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b94c5084-71e8-415c-9299-f24fbf654760">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_u12VBattInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d9cb919c-148f-47c3-b1eb-f0c064a7e7a5">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uHallPwrInst</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="26759e47-dbe6-43e0-b621-2d8484a00f4f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uM1AVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dec2d15f-c0f6-4ab1-89f9-3165cac99d33">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ADCMon_ADCMon_uM1BVolt_mv</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1cf423ea-72a9-42c2-952a-5061b12f6e0c">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_APDet_FDifference</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="77b9a51e-6cf1-4eed-a639-ed95f5f34bfb">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_APDet_FTracking</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b2b1ac96-92ae-430f-b199-a86aef358a24">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_APDet_UsgHist_ATSRevReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bfa49dc4-c505-4522-8a2f-7299a45615d8">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_AmbTemp_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0309ae85-51a0-4cdf-85a2-73db17301040">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_AmbTemp_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="e5518039-a224-43de-b19f-8e9b2b13aba0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_BlockDet_BlockDet_hStallPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4ace42ee-6868-47ec-b678-226affae908d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_BlockDet_BlockDet_stStallDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="044edc7e-7365-4b66-aa5d-c96386bf7315">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6bd04c1a-d5a7-4aff-8f9d-8659cad2ce51">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_CtrlLog_CtrlLog_stRelearnMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="11b4deb9-629f-4b59-a239-a323ad02c364">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallOutPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c67229d7-07de-41e7-abf2-5aee5c23613d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens1PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="48a99e73-2372-4178-a7f1-25c5517a3fdd">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens2PinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="30fb8232-fa65-4ec7-a7da-ff4444a4b6c1">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isINTPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2d14377c-8055-4eeb-8887-04ac0ea4e6da">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankAPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="74a272ae-0f54-49ae-8520-6bff899bc615">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankBPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="76d856ce-6281-47d7-bd23-5279b8c67c8d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_DIOCtrl_DIOCtrl_isVBATMeasEnbPinStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="905689c4-ba4c-4184-a625-8dd91c89110d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c9d8d010-0a5a-40d1-92cd-ba5d36842202">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isHallSuppHS2Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a1618592-bffb-43be-9e23-1405511475a4">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isMtrFebkCtrlHS1Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4918dde2-e5b2-4e33-a761-823d80fec298">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_isSwtchSuppHS3Status</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7bea6bf4-5efe-4975-aa42-4907a1c3a078">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ExtDev_ExtDev_u8GetPWMDutyCycleVal</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b95d91d3-9073-4a96-be5e-48f47f10952d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_DTC_HALL_Hall1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9ffae714-af73-4bc6-8e57-253455d3a15b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_HallDeco_DMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a5b22a81-e5c1-4645-95b1-0556f64bc223">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_HallDeco_rMtrSpd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bf52c572-406d-4f23-94b3-6ebe2c807812">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_HallDeco_isHallSupplyFault</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f5def009-598e-4d4c-a684-6e227b8d17dc">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_isLearnComplete</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0915b9a6-30a2-450d-83cc-8c514d13d7d8">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_isLearningAllowed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a18e8166-af03-44cd-9b60-916f571ff508">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_stMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8db15016-e382-4331-b749-cb2e23083b6b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="513f9c17-b77c-4efc-9358-103504e62c2a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrCtrl_IoHwAb_SetMtrDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2c7953b5-6c70-4625-b83d-7cc118bf615e">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrCtrl_MtrCtrl_FeedbackStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="fc56e140-0e3f-4c7e-a522-d0c7bec5116f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_MtrMdlBus_MtrMdl_TEstMtrTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8a409cbb-8584-4ecd-a203-f3f9fcadbbed">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PnlOp_PnlOp_isMtrMoving</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="32a6f4ce-da93-499c-a9c7-6dd9d8675e14">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PnlOp_PnlOp_stATS</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d40b2378-5639-4d1b-a054-4d450ec71518">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PosMon_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8950d056-8ce5-4461-917e-9d1ac35b321c">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_PosMon_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8ab28d18-24f7-4a4a-a245-54660205eca8">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_RefField_RefField_isRFTblVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="82e1ff9c-937f-437a-b603-8c6b86b91de7">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_RefForce_RefForce_FRefForce</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="372dc61c-4cda-4f43-8b5c-cd703d4b7158">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_StateMach_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="55e2a5d7-2fcf-4017-86e7-c6072265ffec">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="12ddb628-eb8d-4690-a272-5d4d777d5724">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_stMosfetTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="66cc170e-b0aa-4ddb-a998-84f6585fa037">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThermProt_ThermProt_stMtrTempClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="38f80a1d-9401-4cf4-bd66-4740e3450c8d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_ThresForce_ThresForce_FatsThreshold</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cf60307f-f6ef-4d84-a8ef-30fb67375b5b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_CycRenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3cd9f84-4784-41f7-bf2f-cec3bc13f854">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_LeanrFailCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="*************-4eeb-bfe9-72d2e767a416">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_LearnSuccessCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="395e0d53-a234-4a12-ac39-cc01da96e36c">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MaxAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="caa817c2-368a-45b8-b05a-9fde5030023d">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MaxBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a012c913-bbb1-4fcd-a88e-37fdb0fcd38c">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MinAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2b177bb3-c328-4930-9d0b-caf91a5ece9a">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MinBattVtg</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a7e84f8d-436c-49d7-b435-a350d66f3767">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_MotorOperTime</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1ce6f0ec-0bef-4906-b11d-c096d4586fae">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_RenormCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8b9ddf68-b589-4cdf-b7c2-186fc5134986">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReprogrammingCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f7092232-5077-4e7f-bddf-285a28554c4f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0354ac83-ba11-415b-9473-6bec0e18a3c2">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalDir</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3a0ad1d2-dfbd-4da5-9f60-04b217885ca0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5e8270e4-8e18-4f61-8d4d-13bc60d59c3f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ReversalThres</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cb7aca28-08bd-42b8-8873-807901a6e1b0">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_SlideCycCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c64ca8b2-23bd-46e9-afa6-4e46769fa41b">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_StallCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="621643cb-3a6e-46fa-a190-0c0b2fc8fa74">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_ThermProtCount</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="963e062b-17a3-4e5b-8625-80a9b7ba8ce7">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_UsgHist_UsgHist_nCycle</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="c3af6c5b-6acd-4cd8-8e3b-7a4cf38ae266">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7f83b615-716c-4244-ba2a-fa2cfe40184f">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7f81564e-095d-4c73-9b9a-3d2f36d0d390">
        <dataName>portSWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1a6381e4-3dbf-4d72-876d-923335cc2126">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="97b0a031-2a0b-4ea3-854f-5f07b53f6bf9">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_DTCStatus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="0a3a389e-ba5e-496c-bb41-4ab018891990">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stLast10DTCs</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a5356721-0dc4-4d6f-bb6b-fae61e9d1fb5">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLasStpReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="8ba48974-36cf-461b-946f-96ada5baafda">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsLearnFailReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1eb85e40-7f21-4786-b0a6-f1bd0a614fb5">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsSysFaultReaction</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a00d1cce-f747-4211-8807-9c9bfca568b8">
        <dataName>portSWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsUnlearnReason</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7da6aee8-5b88-450d-b1ab-4bcdec48e83f">
        <dataName>portSWC_ObjDetLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b84dd415-d9d8-4cfd-a2ca-7cbb7d7887dd">
        <dataName>portSWC_OperLogLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cbfb6fae-6a6d-4c9d-9cb8-5d701addc33c">
        <dataName>portSWC_SigMonLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="f50e6eaf-a158-41bb-a7bf-a7cc9552f89f">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b9d032d1-aa5f-4d16-bba0-b7a8beaa2426">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="672ca6b7-6aa5-42ed-b378-3c41427223e7">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stAmbTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2189a31c-f75d-4830-8267-382531e1120b">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_stMCUTempNoCritFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="14c57bb6-87fc-46bd-9b49-6b615f2f9349">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_IoHwAb_SetHallPwrOutM1</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="72d8930a-95fc-4082-87b8-728d338db74a">
        <dataName>portSWC_SigMonLyrBus_AmbTempMonBus_PosMon_isUpdateDisabled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4d624184-157c-406b-989d-4830c771bfc1">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_Dummy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="2c653b78-33c2-47b8-98d1-1133d29fb198">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="bfce07de-a95c-45d2-83b6-4c62929c7764">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="a3be51a4-b898-4fdf-a201-e478c79bd450">
        <dataName>portSWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_stMosTempSensFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="268ccbe3-b062-45af-9b30-ddbf0b2cfac5">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_curPanelArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="13bb4f7b-7a3a-40bd-9d92-6ee45e8e3403">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7a819545-1cd8-408b-900e-6d8fa310c159">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="664bd872-590a-4efa-a200-f42b66c4ba09">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hComfortMidStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4a1d1f71-ed7e-4758-86fd-a5b5c558642d">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3d8a9493-5d46-44f2-9100-0d2f3929e88b">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hHardStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d86726d3-c0a2-49b2-aa59-b9fec30994a8">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hNearClsSld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="be84ab9e-4869-442b-a44c-70aa0935303f">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9f73b550-3923-49da-af00-bf7198266c3a">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="082911d8-fb14-47d8-908b-a4aa30487031">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7aac18e2-aca7-45f0-8ed3-3765c040837b">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVldNvm</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5d38c44c-8e8a-48eb-bd18-9fea8d55206f">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isOutOfRange</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3ffe1b0e-4847-45cf-8202-d988c0605252">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isRelearn</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="4ce9a3b9-22a5-42d2-a5e0-28ffa94b25b6">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isShdnRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="7c0bf266-179e-4b6d-933a-233f5ba9abd7">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9ba8dd62-b18f-45e6-8f0b-c89988e0162b">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_panelCurPosArea</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9185f97f-64b4-4ef1-9890-69bc8dc2fb7a">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_relativePinionSize</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="274d7abe-4e98-44ff-bb5e-41f1b4425697">
        <dataName>portSWC_SigMonLyrBus_PosMonBus_PosMon_stPosSave</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="dc62d081-89d0-4a1a-99fc-80ef9aac6098">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_isDebSwch</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6a6aee1e-4c55-41d3-aff8-d18a99f6d17b">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stMultSwchFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="d00f012f-c707-4a61-80e9-6e2c5c4f14ee">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchCmd</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6df3a136-872b-4104-8192-1c8fb6bb9d21">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchStickyFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3ffb0b8a-a8da-49d4-a314-2cebed5c5ff2">
        <dataName>portSWC_SigMonLyrBus_SwitchLogBus_SwitchLog_stSwchVltgFlt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="433ea334-feaf-480e-a350-1e8180e9cd13">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_Pullup</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1c2f2493-d4b4-48f0-a1f8-34a35f8bfac2">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_DTC_VOLT_VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="651f9007-bfc7-43fc-9993-1ea8f90baa2b">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="ec08e6de-0e68-4891-b538-5cbca34647df">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="94c468dd-5e59-463c-89c4-9c198b706501">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isOverVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="cc79b3a8-97a1-412e-8117-3d9ce9be5298">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isPwrLost</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="27e2695c-4a97-48db-b04b-63e5144a4d51">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartUpRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="42873666-e192-4064-8840-51dfff38a948">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartupRdy</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="61ceddf0-2b8a-467e-a8ae-6e8e50f73c61">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isUnderVtgFlgSet</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="78cd6354-286e-41d5-85d9-560132aa1f75">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_isVoltDrop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="02ad1e35-204b-4ed6-a133-296e43c31524">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="3991df32-9166-4137-b0eb-9cc935475694">
        <dataName>portSWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="9ed16a7c-0aa1-4431-97b6-78f876a01998">
        <dataName>portSWC_StateMachLyrBus</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="b9a66cda-627c-4164-a1bc-28432e587911">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isATSActive</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="6cfbcec6-9459-46d1-a40e-38beef04df0a">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isDeNormed</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="75452b62-31c0-4e6a-b378-592db66bc30d">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isEmergMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="5c71bd5c-6bdc-46c4-b7fb-4351b3066c5a">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isFactMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="25789e0c-b653-4d26-8ec2-021ff0823004">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isMfgMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="64b86bab-338c-468f-a8e9-928d7a2a89d1">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isMtrStalled</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="61a8fbfb-87de-4635-8201-78783c36ff42">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_isUnLearned</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="73300c87-72a3-4c2d-809f-dfa964593c7b">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stCurrMovStop</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="83a98901-bc6e-4b85-b5c8-311129ed93d5">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stEcuRstResp</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="1d71ecba-4508-493e-b08c-e957ba241324">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stErrMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="da070ac2-046b-4c9a-a4c9-78dd09eb3890">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stNewMovInh</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="efa295ff-bfac-432a-9f7e-657cfb50a386">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stSCUOperMode</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="72ccf4d8-27a9-44e7-939e-8b002f8e69b9">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stShdnType</dataName>
        <type>RootInport</type>
      </globalData>
      <globalData type="sltp.mm.core.GlobalData" uuid="617c0b58-65b2-4d2b-9210-f08ba8b5b5e0">
        <dataName>portSWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode</dataName>
        <type>RootInport</type>
      </globalData>
      <priorityDirection>HighNumberLast</priorityDirection>
      <editorState type="sltp.mm.core.EditorState" uuid="ef1cfea2-484a-420d-ae76-3d2ab30b0402">
        <isSynchronized>true</isSynchronized>
        <panelState type="sltp.mm.core.EditorPanelState" uuid="0bb34eb9-3f07-403c-a299-ffd7020be893"/>
      </editorState>
      <rootTask type="sltp.mm.core.Task" uuid="5dfcc999-c416-431f-821d-27c11fd37cd0">
        <context type="sltp.mm.core.Context" uuid="1d4d49e0-49ba-4367-9f81-7937fe4805ee"/>
        <explicit>false</explicit>
        <name>Default</name>
        <priority>-2147483648</priority>
        <subgraph type="sltp.mm.core.Graph" uuid="c4a09dd1-3b86-480c-aafa-fbd6db4cc1ea">
          <tasks type="sltp.mm.core.Task" uuid="6b1d47e1-436c-4f37-a2e2-fb88899d050c">
            <context type="sltp.mm.core.Context" uuid="1d4d49e0-49ba-4367-9f81-7937fe4805ee"/>
            <explicit>false</explicit>
            <id>1</id>
            <isTimed>true</isTimed>
            <name>D1</name>
            <priority>40</priority>
            <rates type="sltp.mm.core.Rate" uuid="41e2e403-b53f-4a2a-841a-17990201f5f5">
              <annotation>D1</annotation>
              <color>-436207361</color>
              <identifier>ClassicPeriodicDiscrete0.20</identifier>
              <rateSpec type="sltp.mm.core.RateSpec">
                <period>.2</period>
              </rateSpec>
              <sti>0</sti>
            </rates>
          </tasks>
        </subgraph>
      </rootTask>
    </sltpContext>
    <stateWriterToOwnerMap type="ModelRefInfoRepo.StateWriterInfo" uuid="872b6b5d-5bb4-4105-b627-65004d1c291d"/>
    <stoClientDataRegistry type="sto.ClientDataRegistry" uuid="5af5085c-636c-49e7-9a4d-fec05159e7e4">
      <dataSets type="sto.ClientClockNamedDataSet" uuid="ea2ddd5f-924c-431e-99e8-e9ef0168b9c4">
        <tag>sltpEvents</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="e4428f9a-9d4d-4fa5-b40e-5365012271b6">
        <tag>sltpTaskGroups</tag>
      </dataSets>
      <dataSets type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="19f68172-21ca-4da3-be46-51dbdbd20bf0">
        <dSet type="ModelRefInfoRepo.SltpTaskData" uuid="9dadf5a2-4c29-430f-b356-3a1f8828cf7f"/>
        <tSet type="ModelRefInfoRepo.SltpTaskData" uuid="9dadf5a2-4c29-430f-b356-3a1f8828cf7f">
          <dataName>D1</dataName>
          <linkedSet type="sto.ClientTaskHierarchyElementNamedDataSet" uuid="19f68172-21ca-4da3-be46-51dbdbd20bf0"/>
          <id type="sto.TaskHierarchyElementId">
            <id>_task0</id>
          </id>
        </tSet>
        <tag>sltpTasks</tag>
      </dataSets>
    </stoClientDataRegistry>
    <varTsUIDMap type="ModelRefInfoRepo.VarTsUIDMap" uuid="d6f2ea11-ae92-46b3-ae42-7509077cf8ba"/>
  </ModelRefInfoRepo.ModelRefInfoRoot>
</MF0>