/*
 * File: PIDDID_Exp.h
 *
 * Code generated for Simulink model 'PIDDID_RoutineCtrl_Func'.
 *
 * Model version                  : 7.249
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:36:22 2025
 */

#ifndef RTW_HEADER_PIDDID_Exp_h_
#define RTW_HEADER_PIDDID_Exp_h_
#include "rtwtypes.h"
#include "PIDDID_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "irs_std_types.h"

/* Exported data declaration */
/* Declaration for custom storage class: ExportToFile */
extern uint8_T PIDDID_MtrDutyCycle;
extern uint16_T PIDDID_SoftStartPoint2Time;
extern int16_T PIDDID_TAmbTempOffset;
extern int16_T PIDDID_TMOSFETTempOffset;
extern PIDDID_WrResp_t PIDDID_WrResp;

/* Write Response */
extern PIDDID_WriteID_t PIDDID_WriteID;

/* Write ID */
extern uint16_T PIDDID_hDiagToPos;

/* Diagnostic Goto Position (Glass and Rollo) */
extern boolean_T PIDDID_isATSEnabled;

/* Diagnostic ATS Enable and Disable */
extern boolean_T PIDDID_isClearHisRtnActive;

/* Diagnostic clear History Routine active
   0: clear History Routine is active
   1: clear History Routine is inactive */
extern boolean_T PIDDID_isGoToTrgtActv;

/* To indicate whether go to target position routine is active or not */
extern boolean_T PIDDID_isMtrCtrlRtnBusy;
extern boolean_T PIDDID_isOtherRtnBusy;

/* Diagnostic other Routine Busy status
   0: Not busy
   1: otherroutine in process
 */
extern boolean_T PIDDID_isOverridePWMRtnBusy;
extern boolean_T PIDDID_isReqUnlearn;

/* Diagnostic request entering unlearned state command (Glass and Rollo)
   0: No request
   1: Request entering unlearned state */
extern boolean_T PIDDID_isRoofCtrlRtnBusy;

/* Diagnostic Roof Control Routine Busy status
   0: Not busy
   1: Roof Control  routine in process
 */
extern boolean_T PIDDID_isRtnBusy;

/* Diagnostic Learning Routine Busy status
   0: Not busy
   1: Learning routine in process
 */
extern boolean_T PIDDID_isRtnToPosBusy;

/* Diagnostic Goto Position Routine Busy status
   0: Not busy
   1: Goto Position routine in process
 */
extern boolean_T PIDDID_isSuppressCyclicRenormRtnBusy;
extern boolean_T PIDDID_isSyncToNVMEnable;
extern PIDDID_RoutineStatus_t PIDDID_stClearRtnResult;
extern PIDDID_RoutineStatus_t PIDDID_stControlIntermediatePosRoutineResult;
extern uint8_T PIDDID_stDiagHallPwrCmd;

/* Diagnostic Hall power control command (Rollo only)
   Bit0(1/0): Hall power diagnostic command active or not
   Bit1(1/0): Hall power ON or OFF
   Bit2~Bit7(1/0): Reserved */
extern uint8_T PIDDID_stDiagRlyCmd;

/* Diagnostic motor relay control command (Glass and Rollo)
   Bit0(1/0): Relay diagnostic command active or not
   Bit1(1/0): Relay 1 ON or OFF
   Bit2(1/0): Relay 2 ON or OFF
   Bit3~Bit7: Reserved */
extern VehCom_Cmd_t PIDDID_stDiagRtnCmd;

/* Diagnostic Switch Command (Glass and Rollo)
   None_C       ---->0: No Command
   ExpOpn_C   ---->1: Express Open Command
   ExpCls_C     ---->2: Express Close Command
   ManOpn_C  ---->3: Manual Open Command
   ManCls_C    ---->4: Manual Close Command
   ExpVent_C   ---->5: Express Vent Command
   ManVent_C  ---->6: Manual Vent Command
   Stop_C        ---->7: Stop Command
   ToPos_C      ---->8: To Position Command
   Learn_C      ---->9: Learning Command */
extern PIDDID_RoutineStatus_t PIDDID_stDummyRPRoutineResult;
extern uint8_T PIDDID_stIOTestCmd;
extern PIDDID_RoutineStatus_t PIDDID_stNVMSyncRoutineResult;
extern PIDDID_RoutineStatus_t PIDDID_stOpenClosePosRoutineResult;
extern PIDDID_RoutineStatus_t PIDDID_stOverridePWMRoutineResult;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_AutoCyc;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_ClearRPRF;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_GotoTgtPos;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_IOTestRtn;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_LearnRtn;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_MtrCtrlrtn;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_NormMtrRtn;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_RoofCtrlrtn;
extern PIDDID_RoutineStatus_t PIDDID_stRoutineResult_SuppressCyclicRenorm;
extern PIDDID_RoutineStatus_t PIDDID_stSoftStartRoutineResult;
extern PIDDID_RoutineStatus_t PIDDID_stSoftStopRoutineResult;
extern HwAbs_tenStatus PIDDID_stSyncToNvm;
extern int16_T PIDDID_u12VBattInstOffset;

#endif                                 /* RTW_HEADER_PIDDID_Exp_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
