<?xml version="1.0" encoding="utf-8"?>
<HarnessInformation>
  <Harness>
    <P Name="Name">UnitTest_HALLDeco_Mdl_HallFailureDetection</P>
    <P Name="Description"/>
    <P Name="CreatedBy">suress</P>
    <P Name="CreatedOn">Mon Apr 28 11:15:21 2025</P>
    <P Name="ModifiedBy">palanv</P>
    <P Name="LastSaved">Fri May 30 00:55:02 2025</P>
    <P Name="Owner">1296</P>
    <P Name="OwnerType">SS_HARNESS</P>
    <P Name="OwnerPath">HallDeco/HallFailureDetection</P>
    <P Name="Directory"/>
    <P Name="FileName">UnitTest_HALLDeco_Mdl_HallFailureDetection.slx</P>
    <P Name="SavedVersion">10.6</P>
    <P Name="HarnessUUID">677bb7c5-4df1-4888-a861-7925a7ff1617</P>
    <P Name="HarnessConnectionsMap">0,1,2,3,4,5,6,7,8,9|0,1,2,3,4|0,0,0,0||</P>
    <P Name="VerificationMode">0</P>
    <P Name="HarnessOwnerCheckSum">0,0,0,0</P>
    <P Name="RebuildOnOpen">false</P>
    <P Name="RebuildModelData">false</P>
    <P Name="Graphical">false</P>
    <P Name="DriveFcnCallWithTS">true</P>
    <P Name="ScheduleInitTermReset">false</P>
    <P Name="AutoShapeInputs">false</P>
    <P Name="SLDVCompatible">no</P>
    <P Name="SynchronizationMode">0</P>
    <P Name="IsImported">false</P>
    <P Name="PostRebuildCB"/>
    <P Name="IsCodeContext">false</P>
    <P Name="SchedulerBlock">1</P>
    <P Name="SourceTypeString">Test Sequence</P>
    <P Name="SinkTypeString">Test Assessment</P>
    <P Name="CustomSourcePath"/>
    <P Name="CustomSinkPath"/>
    <P Name="BlockSIDs">16|78|0|0|0|120|0</P>
    <P Name="OpenGraphs"/>
    <P Name="ExistingBuildFolder"/>
    <P Name="FunctionInterfaceName"/>
  </Harness>
  <Harness>
    <P Name="Name">UnitTest_HALLDeco_Mdl_Dir_Timeout</P>
    <P Name="Description"/>
    <P Name="CreatedBy">suress</P>
    <P Name="CreatedOn">Wed Apr 23 10:55:17 2025</P>
    <P Name="ModifiedBy">palanv</P>
    <P Name="LastSaved">Thu May 29 23:42:44 2025</P>
    <P Name="Owner">1411</P>
    <P Name="OwnerType">SS_HARNESS</P>
    <P Name="OwnerPath">HallDeco/Dir_Timeout</P>
    <P Name="Directory"/>
    <P Name="FileName">UnitTest_HALLDeco_Mdl_Dir_Timeout.slx</P>
    <P Name="SavedVersion">10.6</P>
    <P Name="HarnessUUID">9f492482-e4e9-4e44-ab52-88ea9a3a5534</P>
    <P Name="HarnessConnectionsMap">0,1,2,3,4,5,6,7|0|0,0,0,0||</P>
    <P Name="VerificationMode">0</P>
    <P Name="HarnessOwnerCheckSum">0,0,0,0</P>
    <P Name="RebuildOnOpen">false</P>
    <P Name="RebuildModelData">false</P>
    <P Name="Graphical">false</P>
    <P Name="DriveFcnCallWithTS">true</P>
    <P Name="ScheduleInitTermReset">false</P>
    <P Name="AutoShapeInputs">false</P>
    <P Name="SLDVCompatible">no</P>
    <P Name="SynchronizationMode">0</P>
    <P Name="IsImported">false</P>
    <P Name="PostRebuildCB"/>
    <P Name="IsCodeContext">false</P>
    <P Name="SchedulerBlock">1</P>
    <P Name="SourceTypeString">Test Sequence</P>
    <P Name="SinkTypeString">Test Assessment</P>
    <P Name="CustomSourcePath"/>
    <P Name="CustomSinkPath"/>
    <P Name="BlockSIDs">11|63|0|0|0|73|0</P>
    <P Name="OpenGraphs">||UnitTest_HALLDeco_Mdl_Dir_Timeout/Dir_Timeout</P>
    <P Name="ExistingBuildFolder"/>
    <P Name="FunctionInterfaceName"/>
  </Harness>
  <Harness>
    <P Name="Name">UnitTest_HALLDeco_Mdl_HallDeco_Spd</P>
    <P Name="Description"/>
    <P Name="CreatedBy">suress</P>
    <P Name="CreatedOn">Tue Mar 18 11:44:12 2025</P>
    <P Name="ModifiedBy">suress</P>
    <P Name="LastSaved">Tue Mar 18 11:48:45 2025</P>
    <P Name="Owner">805</P>
    <P Name="OwnerType">SS_HARNESS</P>
    <P Name="OwnerPath">HallDeco/HallDeco_Spd</P>
    <P Name="Directory"/>
    <P Name="FileName">UnitTest_HALLDeco_Mdl_HallDeco_Spd.slx</P>
    <P Name="SavedVersion">10.6</P>
    <P Name="HarnessUUID">a059490e-d250-43b2-8469-0901fdcae0eb</P>
    <P Name="HarnessConnectionsMap">0,1,2,3,4,5,6|0,1|0,0,0,0||</P>
    <P Name="VerificationMode">0</P>
    <P Name="HarnessOwnerCheckSum">0,0,0,0</P>
    <P Name="RebuildOnOpen">false</P>
    <P Name="RebuildModelData">false</P>
    <P Name="Graphical">false</P>
    <P Name="DriveFcnCallWithTS">true</P>
    <P Name="ScheduleInitTermReset">false</P>
    <P Name="AutoShapeInputs">false</P>
    <P Name="SLDVCompatible">no</P>
    <P Name="SynchronizationMode">0</P>
    <P Name="IsImported">false</P>
    <P Name="PostRebuildCB"/>
    <P Name="IsCodeContext">false</P>
    <P Name="SchedulerBlock">1</P>
    <P Name="SourceTypeString">Test Sequence</P>
    <P Name="SinkTypeString">Test Assessment</P>
    <P Name="CustomSourcePath"/>
    <P Name="CustomSinkPath"/>
    <P Name="BlockSIDs">11|58|0|0|0|76|0</P>
    <P Name="OpenGraphs">||UnitTest_HALLDeco_Mdl_HallDeco_Spd</P>
    <P Name="ExistingBuildFolder"/>
    <P Name="FunctionInterfaceName"/>
  </Harness>
  <ModelMetadata>
    <P Name="ModelUUID">e3dd3329-448c-450c-bd1c-f48558a70cde</P>
    <P Name="OwnerBDName">HALLDeco_Mdl</P>
  </ModelMetadata>
</HarnessInformation>
