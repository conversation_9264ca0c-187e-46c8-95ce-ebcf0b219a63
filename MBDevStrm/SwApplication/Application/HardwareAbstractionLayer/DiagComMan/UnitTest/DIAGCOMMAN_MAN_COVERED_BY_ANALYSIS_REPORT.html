<!DOCTYPE html>
<!-- VectorCAST Report header -->
<html lang="en">
  <head>
    <title>Covered By Analysis Report</title>
    <meta charset="utf-8"/>
    <style>
    /*! normalize.css v8.0.0 | MIT License | github.com/necolas/normalize.css */
html{line-height:1.15;-webkit-text-size-adjust:100%}
body{margin:0}
h1{font-size:2em;margin:.67em 0}
hr{box-sizing:content-box;height:0;overflow:visible}
pre{font-family:monospace,monospace;font-size:1em}
a{background-color:transparent}
abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}
b,strong{font-weight:bolder}
code,kbd,samp{font-family:monospace,monospace;font-size:1em}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sub{bottom:-.25em}
sup{top:-.5em}
img{border-style:none}
button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;line-height:1.15;margin:0}
button,input{overflow:visible}
button,select{text-transform:none}
[type=button],[type=reset],[type=submit],button{-webkit-appearance:button}
[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner{border-style:none;padding:0}
[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring,button:-moz-focusring{outline:1px dotted ButtonText}
fieldset{padding:.35em .75em .625em}
legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}
progress{vertical-align:baseline}
textarea{overflow:auto}
[type=checkbox],[type=radio]{box-sizing:border-box;padding:0}
[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}
[type=search]{-webkit-appearance:textfield;outline-offset:-2px}
[type=search]::-webkit-search-decoration{-webkit-appearance:none}
::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}
details{display:block}
summary{display:list-item}
template{display:none}
[hidden]{display:none}
 html {box-sizing:border-box;position:relative;height:100%;width:100%;}
*, *:before, *:after {box-sizing:inherit;}
body {position:relative;height:100%;width:100%;font-size:10pt;font-family:helvetica, Arial, sans-serif;color:#3a3e3f;}
.alternate-font {font-family:Arial Unicode MS, Arial, sans-serif;}
#page {position:relative;width:100%;height:100%;overflow:hidden;}
#title-bar {position:absolute;top:0px;left:0em;right:0px;height:1.8em;background-color:#B1B6BA;white-space:nowrap;box-shadow:1px 1px 5px black;z-index:100;}
#report-title {font-size:3em;text-align:center;font-weight:bold;background-color:white;padding:0.5em;margin-bottom:0.75em;border:1px solid #e5e5e5;}
.contents-block {position:absolute;top:1.8em;left:0em;width:22em;bottom:0em;overflow:auto;background-color:#DADEE1;border-right:1px solid silver;padding-left:0.75em;padding-right:0.5em;}
.testcase .report-block, .testcase .report-block-coverage {padding-bottom:2em;border-bottom:3px double #e5e5e5;}
.testcase .report-block:last-child, .testcase .report-block-coverage:last-child {border-bottom:0px solid white;}
.report-body {position:absolute;top:1.8em;left:22em;right:0em;bottom:0em;padding-left:2em;padding-right:2em;overflow:auto;padding-bottom:1.5em;background-color:#DADEE1;}
.report-body.no-toc {left:0em;}
.report-body > .report-block, .report-body > .report-block-coverage, .report-body > .report-block-scroll, .report-body > .testcase {border:1px solid #e5e5e5;margin-bottom:2em;padding-bottom:1em;padding-right:2em;background-color:white;padding-left:2em;padding-top:0.1em;margin-top:1em;overflow-x:auto;}
.report-body > .report-block-scroll {overflow-x:visible;background-color:inherit;}
.title-bar-heading {display:none;position:absolute;text-align:center;width:100%;color:white;font-size:3em;bottom:0px;margin-bottom:0.3em;}
.title-bar-logo {display:inline-block;height:100%;}
.title-bar-logo img {width:120px;margin-left:0.5em;margin-top:-16px;}
.contents-block ul {padding-left:1.5em;list-style-type:none;line-height:1.5;}
li.collapsible-toc > input ~ ul {display:none;}
li.collapsible-toc > input:checked ~ ul {display:block;}
li.collapsible-toc > input {display:none;}
li.collapsible-toc > label {cursor:pointer;}
li.collapsible-toc > label.collapse {display:none;}
li.collapsible-toc > label.expand {display:inline;}
li.collapsible-toc > input:checked ~ label.expand {display:none;}
li.collapsible-toc > input:checked ~ label.collapse {display:inline;}
.contents-block ul.toc-level1 {padding-left:1em;}
.contents-block ul.toc-level1 > li {margin-top:0.75em;}
.contents-block li {white-space:nowrap;text-overflow:ellipsis;overflow:hidden;}
.tc-passed:before, .tc-failed:before, .tc-none:before{display:inline-block;margin-right:0.25em;width:0.5em;text-align:center;}
.tc-passed:before {content:"✓";color:darkgreen;}
.tc-failed:before {content:"✗";color:#b70032;}
.tc-none:before {content:"•";}
.tc-item {margin-top:0.75em;margin-left:-1em;}
table {margin-top:0.5em;margin-bottom:1em;border-collapse:collapse;min-width:40em;}
.expansion_row_icon_minus, .expansion_file_icon_minus, .expansion_all_icon_minus{display:none !important;}
.sfp-table{margin:0;border-bottom:1px solid gray;min-width:50em;}
.sfp-table th{padding:0 !important;border-bottom:solid 1px grey;font-size:15px;padding-left:2px !important;padding-right:2px !important;}
.sfp-table td{padding:0;border-right:solid 1px grey;border-bottom:0;}
.sfp-table td:last-child {border-right:0;padding-left:5px !important;}
.sfp-table tr:hover, .highlight:hover{background-color:#F3F4F5;}
table.table-hover tbody tr:hover {background-color:#DADEE1;}
th, td {text-align:left;padding:0.25em;padding-right:1em;border-bottom:1px solid #e5e5e5;}
table thead th {border-bottom:2px solid silver;border-top:0;border-left:0;border-right:0;}
.top-align {vertical-align:top;}
.pull-right {display:none;}
h1, h2 {border-bottom:3px solid silver;}
h1, h2, h3, h4 {margin-top:1em;margin-bottom:0.25em;}
h1 {font-size:3.5em;}
h2 {font-size:2.5em;}
h3 {font-size:2em;}
h4 {font-size:1.25em;border-bottom:0;margin-bottom:0;}
h5 {font-size:1em;font-weight:bold;margin-top:0.5em;margin-bottom:0.25em;}
pre {padding:1em;padding-left:1.5em;border:1px solid #e5e5e5;background-color:#DADEE1;min-width:40em;width:auto;clear:both;display:inline-block;margin-top:0.25em;}
.sfp-pre{padding:0;background-color:transparent;border:0;margin:0;}
ul.unstyled {list-style-type:none;margin-top:0.25em;padding-left:0px;}
ul {line-height:1.3;}
p {margin-top:0.5em;margin-bottom:1em;max-width:50em;}
pre p {max-width:inherit;}
a, a:visited {color:inherit;text-decoration:none;}
a:hover {text-decoration:underline;text-decoration-color:#b70032;-webkit-text-decoration-color:#b70032;-moz-text-decoration-color:#b70032;cursor:pointer;}
pre.aggregate-coverage {padding-left:0px;padding-right:0px;margin-top:1em;}
pre.aggregate-coverage span {width:100%;display:inline-block;padding-left:1em;padding-right:1em;line-height:1.3;}
.sfp-span{width:auto ! important;padding-left:0 ! important;padding-right:0 ! important;}
.sfp-span-color{width:100% !important;padding-left:1px !important;padding-right:1px !important;}
.sfp_number{text-align:right;width:1px;white-space:nowrap;padding-right:2px !important;}
.sfp_coverage{text-align:center;width:1px;}
.sfp_color{padding-left:0 !important;padding-right:0 !important;width:1px;}
.up_arrow{position:absolute;left:65px;}
.collapse_icon{float:right;height:24px;width:24px;display:inline-block;color:black;font-size:24px;line-height:24px;text-align:center;border-radius:50%;cursor:pointer;}
.underline{color:#a0a0a0;}
.temp_description{font-style:italic;}
.ips{display:none;}
.bg-success, .success, span.full-cvg {background-color:#c8f0c8;}
.sfp-full{background-color:#28a745;}
.sfp-part{background-color:#ffc107;}
.sfp-none{background-color:#dc3545;}
span.ann-cvg, span.annpart-cvg {background-color:#cacaff;}
.bg-danger, .danger, span.no-cvg {background-color:#facaca;}
.bg-warning, .warning, span.part-cvg {background-color:#f5f5c8;}
.fit-content {width:max-content;}
.rel-pos {position:relative;}
.report-block > .bs-callout.test-timeline, .mcdc-condition {padding-left:0px;}
.report-block.single-test {padding-left:2em;padding-right:2em;width:100%;height:100%;overflow:auto;}
.test-action-header + h4.event{margin-top:0.5em;}
.event.bs-callout > .bs-callout, .mcdc-condition {margin-bottom:2em;border-left:2px solid silver;}
.event.bs-callout {margin-top:1.5em;}
.bs-callout, .mcdc-condition {padding-left:1.5em;}
.bs-callout.bs-callout-success {border-left:2px solid green;margin-left:0px;}
.bs-callout.bs-callout-success .bs-callout.bs-callout-success {border-left:none;margin-left:0px;}
.bs-callout.bs-callout-danger {border-left:2px solid #b70032;margin-left:0px;}
.bs-callout.bs-callout-warning {border-left:2px solid wheat;padding-left:1.5em;margin-left:0px;}
.event.bs-callout .bs-callout.bs-callout-warning {border-left:2px solid wheat;padding-left:1.5em;margin-left:0px;}
.bs-callout.bs-callout-danger .bs-callout.bs-callout-danger {margin-left:1.5em;border-left:2px solid #b70032;padding-left:1.5em;}
.bs-callout-info {margin-top:1em;}
.text-muted {font-size:0.9em;}
.test-action-header {border-top:3px solid silver;border-bottom:1px solid #e5e5e5;min-width:42em;white-space:nowrap;margin-bottom:0.5em;padding-top:0.25em;padding-bottom:0.25em;margin-top:2em;background-color:#DADEE1;margin-left:-1.5em;padding-left:1.5em;}
.test-action-header h4 {margin-top:0px;}
.test-action-header:first-child{margin-top:0px;}
.test-timeline > .event.bs-callout:first-child{margin-top:1em;}
.event > .bs-callout {margin-left:2px !important;}
.testcase-notes {white-space:pre;word-wrap:none;}
.mcdc-condition {margin-bottom:3em;}
.mcdc-table {margin-top:0px;}
.mcdc-rows-table {margin-top:2em;min-width:auto;}
.mcdc-rows-table td{border:1px solid #e5e5e5;}
.mcdc-condition.full-cvg {border-left-color:green;border-left-width:2px;}
.mcdc-condition.part-cvg {border-left-color:wheat;border-left-width:2px;}
.mcdc-condition.no-cvg {border-left-color:#b70032;border-left-width:2px;}
.i0 {padding-left:0.25em;min-width:11em }
.i1 {padding-left:1.25em;min-width:11em }
.i2 {padding-left:2.25em;}
.i3 {padding-left:3.25em;}
.i4 {padding-left:4.25em;}
.i5 {padding-left:5.25em;}
.i6 {padding-left:6.25em;}
.i7 {padding-left:7.25em;}
.i8 {padding-left:8.25em;}
.i9 {padding-left:9.25em;}
.i10 {padding-left:10.25em;}
.i11 {padding-left:11.25em;}
.i12 {padding-left:12.25em;}
.i13 {padding-left:13.25em;}
.i14 {padding-left:14.25em;}
.i15 {padding-left:15.25em;}
.i16 {padding-left:16.25em;}
.i17 {padding-left:17.25em;}
.i18 {padding-left:18.25em;}
.i19 {padding-left:19.25em;}
.i20 {padding-left:20.25em;}
.right {float:right;}
.req-notes-list {white-space:pre;}
.uncovered_func_calls-list {white-space:pre;}
.bold-text {font-weight:bold;}
.static-cell {}
.static-cell-right {text-align:right;}
.static-doc {width:100%;}
.static-title-bar {height:1.9em;width:100%;font-size:1.7em;border-bottom:1px solid #e5e5e5;margin:0;padding-top:8px;font-weight:bold;}
.static-id {float:left;width:100px;}
.static-title {float:left;}
.static-doctext {width:auto;background-color:#efefef;border:1px solid #e5e5e5;min-width:40em;}
.static-doctext pre {padding:0;margin:0.2em 0 0.2em 1em;border:none;}
.static-doctext p {padding:0;margin:0.2em 0 0.2em 1em;}
.static-doctext h2 {font-size:1.5em;}
.static-doctext h3 {font-size:1.3em;}
.missing_control_flow {max-width:40em;}
.preformatted {font-family:monospace;white-space:pre;}
.probe_point_notes {font-family:monospace;width:15%;}
.col_unit {word-break:break-all;width:30%;}
.col_subprogram {word-break:break-all;width:30%;}
.col_complexity {white-space:nowrap;}
.col_metric {white-space:nowrap;}
.col_nowrap {white-space:nowrap;}
.col_wrap {word-break:break-all;}
.subtitle {font-size:1.3em;}
@media print{html, body {height:auto;overflow:auto;color:black;}
.contents-block {display:none;}
#title-bar {position:initial;height:20pt;}
#main-scroller {background-color:white;margin:0;padding:0;position:initial;left:auto;top:auto;}
pre {background-color:white;font-size:10pt;border:none;border-left:1px solid #e5e5e5;font-size:9pt;}
.report-body > .testcase, .report-body > .report-block, .report-body > .report-block-coverage {border:none;padding-bottom:0in;}
.report-body > .testcase {page-break-before:always }
#report-title {border:none;font-size:3em;text-align:left;font-weight:bold;padding-bottom:0px;border-bottom:3px solid silver;}
h1, h2, h3, h4 {padding-top:0;margin-top:0.2in;}
html, body, .report-body {background-color:white;font-size:1vw;}
}

    </style>
  </head>
  <body>
    <div id='page'><!-- ReportTitle -->
<div id="title-bar">
  <div class="title-bar-logo">
    <img alt="Vector" src="data:image/svg+xml;base64,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"/>
  </div>
</div><!-- TableOfContents -->
<div class='contents-block'>
  <a id="TableOfContents"></a>
  <h3 class="toc-title-small">Contents</h3>
  <ul class="toc-level1">
        <li class=""><a href="#ConfigurationData">Configuration Data</a></li>
        <li class=" collapsible-toc" title="Covered By Analysis">
      <label for="collapsible-5">Covered By Analysis</label>
        <input type="checkbox" id="collapsible-5"/>
        <label class="expand" for="collapsible-5">►</label>
        <label class="collapse" for="collapsible-5">▼</label>
      <ul>
          <li class=""><a href="#Covered By Analysis, Per Line">Per Line</a></li>
        
          <li class=""><a href="#cba_2_CBA_ISOUDS">File: CBA_ISOUDS</a></li>
        
          <li class=""><a href="#cba_1_CBA_DIAGCOMMAN_MAN_unreachable_code">File: CBA_DIAGCOMMAN_MAN_unreachable_code</a></li>
        
          <li class=""><a href="#cba_3_CBA_ISOUDS_CntrlDTCSetting">File: CBA_ISOUDS_CntrlDTCSetting</a></li>
        
          <li class=""><a href="#cba_4_CBA_ISOUDS_CommCntrl">File: CBA_ISOUDS_CommCntrl</a></li>
        
          <li class=""><a href="#cba_5_CBA_ISOUDS_ECUReset">File: CBA_ISOUDS_ECUReset</a></li>
        
          <li class=""><a href="#cba_6_CBA_ISOUDS_IOCtrlByID">File: CBA_ISOUDS_IOCtrlByID</a></li>
        
          <li class=""><a href="#cba_7_CBA_ISOUDS_LinkCntrl">File: CBA_ISOUDS_LinkCntrl</a></li>
        
          <li class=""><a href="#cba_8_CBA_ISOUDS_RdDaByID">File: CBA_ISOUDS_RdDaByID</a></li>
        
          <li class=""><a href="#cba_9_CBA_ISOUDS_RdMemByAddr">File: CBA_ISOUDS_RdMemByAddr</a></li>
        
          <li class=""><a href="#cba_10_CBA_ISOUDS_RdScDaByID">File: CBA_ISOUDS_RdScDaByID</a></li>
        
          <li class=""><a href="#cba_11_CBA_ISOUDS_RdScDaByID - #2">File: CBA_ISOUDS_RdScDaByID - #2</a></li>
        
          <li class=""><a href="#cba_12_CBA_ISOUDS_ReqDwnld">File: CBA_ISOUDS_ReqDwnld</a></li>
        
          <li class=""><a href="#cba_13_CBA_ISOUDS_ReqUpld">File: CBA_ISOUDS_ReqUpld</a></li>
        
          <li class=""><a href="#cba_14_CBA_ISOUDS_RtnCntrl">File: CBA_ISOUDS_RtnCntrl</a></li>
        
          <li class=""><a href="#cba_15_CBA_ISOUDS_SA">File: CBA_ISOUDS_SA</a></li>
        
          <li class=""><a href="#cba_16_CBA_ISOUDS_StrtDiagSess">File: CBA_ISOUDS_StrtDiagSess</a></li>
        
          <li class=""><a href="#cba_17_CBA_ISOUDS_TrnsfrDa">File: CBA_ISOUDS_TrnsfrDa</a></li>
        
          <li class=""><a href="#cba_18_CBA_ISOUDS_WrDaByID">File: CBA_ISOUDS_WrDaByID</a></li>
        
          <li class=""><a href="#cba_19_CBA_ISOUDS_WrMemByAddr">File: CBA_ISOUDS_WrMemByAddr</a></li>
        
          <li class=""><a href="#cba_20_CBA_LINTP">File: CBA_LINTP</a></li>
        
          <li class=""><a href="#cba_21_CBA_LINTP_NodeSrv_LIN_RdLookUp">File: CBA_LINTP_NodeSrv_LIN_RdLookUp</a></li>
        </ul>
    </li>
  </ul>
</div>
  <div class="report-body" id="main-scroller">
    <div id="report-title">Covered By Analysis Report</div>
<!-- ConfigData -->
      <div class='report-block'>
        <h2><a id="ConfigurationData"></a>Configuration Data</h2>
        <table class='table table-small'>
          <tr><th>Environment Name</th><td>DIAGCOMMAN_MAN</td></tr>
          <tr><th>Date of Report Creation</th><td>30 MAY 2025</td></tr>
          <tr><th>Time of Report Creation</th><td>2:14:33 AM</td></tr>
        </table>
      </div>
<!-- CoveredByAnalysis -->
      <div class='report-block'>
        <div class="row">
          <div class="col-md-10">
            <h2><a id="CoveredByAnalysis"></a>Covered By Analysis</h2>
          </div>
        </div>
        <div class="row">
          <div class="col-md-10"><h4><a id="Covered By Analysis, Per Line"></a>Covered By Analysis, Per Line</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <table class='table table-small table-hover'>
          <thead>
            <tr><th>&nbsp;</th><th>Unit</th><th>Subprogram</th><th>Sub. ID</th><th>Cond</th><th>Covered</th><th>Result File</th></tr>
          </thead>
          <tbody>
            <tr><td>&nbsp;</td><td>ISOUDS</td><td>UDS_SrvPTask</td><td>3</td><td>3</td><td>(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.1</td><td>(T)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.2</td><td>(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>4</td><td>(T)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>4.1</td><td>(T)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>5</td><td>*</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.1</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.2</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.3</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.4</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.5</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.6</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.7</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>6.8</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>7</td><td>*</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>8</td><td>*</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>9</td><td>*</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>10</td><td>*</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>11</td><td>(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>11.1</td><td>(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>UDS_LookUpTbl</td><td>10</td><td>2</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>2.1</td><td>(T)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>2.2</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.1</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.2</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.3</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.4</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.5</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>3.6</td><td>(T)(F)</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>4</td><td>*</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>5</td><td>*</td><td><a href="#cba_2_CBA_ISOUDS">CBA_ISOUDS</a></td></tr>
          </tbody>
        </table>
        <div class="row">
          <div class="col-md-10"><h4><a id="cba_2_CBA_ISOUDS"></a>Covered By Analysis Result File: CBA_ISOUDS</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <table class='table table-small table-hover'>
          <thead>
            <tr><th>&nbsp;</th><th>Unit</th><th>Subprogram</th><th>Complexity</th><th>Statements</th><th>Branches</th><th>MCDC Pairs *</th></tr>
          </thead>
          <tbody>
            <tr><td>&nbsp;</td><td>ISOUDS</td><td>UDS_SrvPTask</td><td>10</td><td>9 / 23 (39%)</td><td>25 / 55 (45%)</td><td>11 / 18 (61%)</td></tr>
            <tr><td>&nbsp;</td><td>&nbsp;</td><td>UDS_LookUpTbl</td><td>3</td><td>4 / 6 (66%)</td><td>19 / 21 (90%)</td><td>8 / 8 (100%)</td></tr>
          </tbody>
        </table>
        <p>* An MCDC Pair is considered satisfied in the table above if at least one of the pair components comes from
           the CBA result referenced in the table. The other component may be provided by any CBA or execution result.</p>
        <h5>Notes</h5>
        <pre>rxIDFound is initiallized as 1 and never updated .So this condition will never be false.

idx is uninitiallized .So cannot figure out which value it is taking.

srvFound isinitiallized as 0 and never updated.So condition cannot be false

ISOUDS_iTab[ is user defined.Cannot be modfied from test case</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_DIAGCOMMAN_MAN_unreachable_code</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>Unreachable code

--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_DIAGCOMMAN_MAN_unreachable_code
&gt;&gt;&gt;   Unit: 39
&gt;&gt;&gt;   Function: lin_diagservice_assign_frame_id_range
&gt;&gt;&gt;   Context Before: /* Unassign frame */
start_index++;


&gt;&gt;&gt;   Line Context: (prot_user_config_ptr-&gt;list_identifiers_RAM_ptr)[start_index] = 0xFFU;

&gt;&gt;&gt;   Context After: break;
case 0xFFU:

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_DIAGCOMMAN_MAN_unreachable_code
&gt;&gt;&gt;   Unit: 39
&gt;&gt;&gt;   Function: lin_diagservice_assign_frame_id_range
&gt;&gt;&gt;   Context Before: /* Calculate frame ID and Assign ID to frame */
start_index++;


&gt;&gt;&gt;   Line Context: (prot_user_config_ptr-&gt;list_identifiers_RAM_ptr)[start_index] = lin_process_parity(dataBuff[i], 1U);

&gt;&gt;&gt;   Context After: break;
}

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_DIAGCOMMAN_MAN_unreachable_code
&gt;&gt;&gt;   Unit: 39
&gt;&gt;&gt;   Function: lin_diagservice_assign_frame_id_range
&gt;&gt;&gt;   Context Before: (prot_user_config_ptr-&gt;list_identifiers_RAM_ptr)[start_index] = 0xFFU;
break;


&gt;&gt;&gt;   Line Context: case 0xFFU:

&gt;&gt;&gt;   Context After: /* keep the previous assigned value of this frame */
break;

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_DIAGCOMMAN_MAN_unreachable_code
&gt;&gt;&gt;   Unit: 39
&gt;&gt;&gt;   Function: lin_diagservice_assign_frame_id_range
&gt;&gt;&gt;   Context Before: (prot_user_config_ptr-&gt;list_identifiers_RAM_ptr)[start_index] = 0xFFU;
break;


&gt;&gt;&gt;   Line Context: case 0xFFU:

&gt;&gt;&gt;   Context After: /* keep the previous assigned value of this frame */
break;
</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_CntrlDTCSetting</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>roor is updated as 1and never updated.

cnc is updated as 0 and never updated.

posResp is updated by databuff[0].This cannot be faluse as databuff[0] can be either 1 or 2.</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_CommCntrl</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>req is initialized as 0 and is never updated.So this condition can never be true.</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_ECUReset</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>

posResp is updated by databuff[0] and will always update as 0 .

--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_ECUReset
&gt;&gt;&gt;   Unit: 14
&gt;&gt;&gt;   Function: ISOUDS_ECUReset
&gt;&gt;&gt;   Context Before: {
/* Security Access Denied */


&gt;&gt;&gt;   Line Context: ISOUDSConfPtr-&gt;srvNegResp = (uint8)(0x22U);

&gt;&gt;&gt;   Context After: }
}

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_ECUReset
&gt;&gt;&gt;   Unit: 14
&gt;&gt;&gt;   Function: ISOUDS_ECUReset
&gt;&gt;&gt;   Context Before: {
/* No respond */


&gt;&gt;&gt;   Line Context: ISOUDS_Rst();

&gt;&gt;&gt;   Context After: }
}

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_ECUReset
&gt;&gt;&gt;   Unit: 14
&gt;&gt;&gt;   Function: ISOUDS_ECUReset
&gt;&gt;&gt;   Context Before: UPDATE THE VARIABLE cnc ACCORDINGLY
*****************************************************************/


&gt;&gt;&gt;   Line Context: if (cnc == (uint8)0)

&gt;&gt;&gt;   Context After: {
/* request ECU Reset */

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_ECUReset
&gt;&gt;&gt;   Unit: 14
&gt;&gt;&gt;   Function: ISOUDS_ECUReset
&gt;&gt;&gt;   Context Before: UPDATE THE VARIABLE cnc ACCORDINGLY
*****************************************************************/


&gt;&gt;&gt;   Line Context: if (cnc == (uint8)0)

&gt;&gt;&gt;   Context After: {
/* request ECU Reset */

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_ECUReset
&gt;&gt;&gt;   Unit: 14
&gt;&gt;&gt;   Function: ISOUDS_ECUReset
&gt;&gt;&gt;   Context Before: ISOUDS_Sess = (uint8)(0x01U);
/* check if positive response is to be sent */


&gt;&gt;&gt;   Line Context: if (posResp == (uint8)0)

&gt;&gt;&gt;   Context After: {
/* Store the reset type in response buffer */

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_ECUReset
&gt;&gt;&gt;   Unit: 14
&gt;&gt;&gt;   Function: ISOUDS_ECUReset
&gt;&gt;&gt;   Context Before: ISOUDS_Sess = (uint8)(0x01U);
/* check if positive response is to be sent */


&gt;&gt;&gt;   Line Context: if (posResp == (uint8)0)

&gt;&gt;&gt;   Context After: {
/* Store the reset type in response buffer */
</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_IOCtrlByID</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>ISOUDS_IoConf and PIDDID_IoCtrlbyID[ is user defined cannot be updated from test case

--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: if ((boolean)1u == unlock)
{


&gt;&gt;&gt;   Line Context: (ISOUDS_IoConf.ioDid_funPtr)(ioDID,ioCtrlPrm,&amp;dataBuff[3],&amp;resNRC);

&gt;&gt;&gt;   Context After: }
else

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{


&gt;&gt;&gt;   Line Context: if ((((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x01) == 0x01) &amp;&amp; (ISOUDS_Sess == (0x01U))) ||

&gt;&gt;&gt;   Context After: (((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x02) == 0x02) &amp;&amp; (ISOUDS_Sess == (0x02U))) ||
(((PIDDID_IoCtrlbyID[idxTbl].bitsForID.stSession &amp; 0x04) == 0x04) &amp;&amp; (ISOUDS_Sess == (0x03U))) ||

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: /* Copy the Control state #1 parameter */
ioCtrlState = dataBuff[3];


&gt;&gt;&gt;   Line Context: if ((ISOUDS_IoConf.ioDid_funPtr != ((void (*) (uint16,uint8,uint8[],uint8 *))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: /* Copy the Control state #1 parameter */
ioCtrlState = dataBuff[3];


&gt;&gt;&gt;   Line Context: if ((ISOUDS_IoConf.ioDid_funPtr != ((void (*) (uint16,uint8,uint8[],uint8 *))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: /* Copy the Control state #1 parameter */
ioCtrlState = dataBuff[3];


&gt;&gt;&gt;   Line Context: if ((ISOUDS_IoConf.ioDid_funPtr != ((void (*) (uint16,uint8,uint8[],uint8 *))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)9),&amp;idxTbl,((uint8)2))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if ((boolean)1u == unlock)

&gt;&gt;&gt;   Context After: {
(ISOUDS_IoConf.ioDid_funPtr)(ioDID,ioCtrlPrm,&amp;dataBuff[3],&amp;resNRC);

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if ((boolean)1u == unlock)

&gt;&gt;&gt;   Context After: {
(ISOUDS_IoConf.ioDid_funPtr)(ioDID,ioCtrlPrm,&amp;dataBuff[3],&amp;resNRC);

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if ((boolean)1u == unlock)

&gt;&gt;&gt;   Context After: {
(ISOUDS_IoConf.ioDid_funPtr)(ioDID,ioCtrlPrm,&amp;dataBuff[3],&amp;resNRC);


--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: dataBuff[2] = ioCtrlPrm;
/* Update Response length for the current Service - including SID */


&gt;&gt;&gt;   Line Context: ISOUDSConfPtr-&gt;srvLen = 4;

&gt;&gt;&gt;   Context After: /* Send positive response */
ISOUDSConfPtr-&gt;srvSt = (uint8)(0x03U);

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: ISOUDSConfPtr-&gt;srvLen = 4;
/* Send positive response */


&gt;&gt;&gt;   Line Context: ISOUDSConfPtr-&gt;srvSt = (uint8)(0x03U);

&gt;&gt;&gt;   Context After: }
else

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Unit: 15
&gt;&gt;&gt;   Function: ISOUDS_IOCtrlByID
&gt;&gt;&gt;   Context Before: dataBuff[0] = (uint8)(ioDID &gt;&gt; 8);
dataBuff[1] = (uint8)ioDID;


&gt;&gt;&gt;   Line Context: dataBuff[2] = ioCtrlPrm;

&gt;&gt;&gt;   Context After: /* Update Response length for the current Service - including SID */
ISOUDSConfPtr-&gt;srvLen = 4;
</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_LinkCntrl</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>rtnNoError cannot be false
posresp cannot be false as databuff[0] is 3.</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_RdDaByID</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>ISOUDS_RdConf and PIDDID_RdWrDID[ are user defined.Cannot be updated from test case

--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: dataBuff[(uint8)1 + (idx * (uint8)(0x02U))+((uint8)0)]);
/* DID checking, if it is configured */


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RdConf.rdDid_funPtr != ((void (*) (uint16,uint8 rdData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locDID,PIDDID_RdWrDID,((uint16)100),&amp;idxTbl,((uint8)0))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: dataBuff[(uint8)1 + (idx * (uint8)(0x02U))+((uint8)0)]);
/* DID checking, if it is configured */


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RdConf.rdDid_funPtr != ((void (*) (uint16,uint8 rdData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locDID,PIDDID_RdWrDID,((uint16)100),&amp;idxTbl,((uint8)0))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: dataBuff[(uint8)1 + (idx * (uint8)(0x02U))+((uint8)0)]);
/* DID checking, if it is configured */


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RdConf.rdDid_funPtr != ((void (*) (uint16,uint8 rdData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locDID,PIDDID_RdWrDID,((uint16)100),&amp;idxTbl,((uint8)0))))
{


--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if(0 == nSupportDids)

&gt;&gt;&gt;   Context After: {
/* NRC - Request Out Of Range */

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if(0 == nSupportDids)

&gt;&gt;&gt;   Context After: {
/* NRC - Request Out Of Range */


--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: {
/* Execute the function for read purpose */


&gt;&gt;&gt;   Line Context: (ISOUDS_RdConf.rdDid_funPtr) (locDID,&amp;txData[(txPos+(uint8)(0x02U))],&amp;resNRC);

&gt;&gt;&gt;   Context After: if ((0x00U) == resNRC)
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if ((boolean)1u == unlock)

&gt;&gt;&gt;   Context After: {
/* Execute the function for read purpose */

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if ((boolean)1u == unlock)

&gt;&gt;&gt;   Context After: {
/* Execute the function for read purpose */

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RdDaByID
&gt;&gt;&gt;   Unit: 20
&gt;&gt;&gt;   Function: ISOUDS_RdDaByID
&gt;&gt;&gt;   Context Before: }
}


&gt;&gt;&gt;   Line Context: if ((boolean)1u == unlock)

&gt;&gt;&gt;   Context After: {
/* Execute the function for read purpose */
</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_RdMemByAddr</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>dataBuff[0] == (uint8)0x14) cannot be true as dataBuff[0] = (uint8)0x32.If not kept 0x32 it will not pass abaove if condition i.e.
ISOUDSConfPtr-&gt;srvLen == (uint16)((uint16)2 + (uint16)memAddrSizeLen)))</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_RdScDaByID</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>lean)1u == ISOUDS_RdScLookUp (locDID)) cannot be true as databuff[0] needs to be updated with value less than 1 but not 0</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_RdScDaByID - #2</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>ISOUDS_RdScConfTab is user defined.Hence cannot be updated

rdDidFound is updated as 0 so rdDidFound != (boolean)1u) cannot be false</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_ReqDwnld</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>dataBuff[1] == (uint8)0x33) cannot be true as srvlen needs to be updated as 8.065 which is not possible</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_ReqUpld</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>dataBuff[1] == (uint8)0x33) cannot be true as srvlen need to be updated as 8.065</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_RtnCntrl</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>ISOUDS_RtnConf. user defined .Cannot be updated.

PIDDID_Rtn is userdefined.Cannot be updated

--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RtnCntrl
&gt;&gt;&gt;   Unit: 27
&gt;&gt;&gt;   Function: ISOUDS_RtnCntrl
&gt;&gt;&gt;   Context Before: ((uint16)dataBuff[(((uint8)(((uint8)0)+(uint8)(0x01U)))+1)]));
/* ID checking, if it is configured to routine control*/


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RtnConf.rtnId_funPtr != ((void (*) (uint16,uint8,uint8,const uint8 rtnOption[],uint8 rtnTxData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locID,PIDDID_Rtn,((uint16)23),&amp;idxTbl,((uint8)2))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RtnCntrl
&gt;&gt;&gt;   Unit: 27
&gt;&gt;&gt;   Function: ISOUDS_RtnCntrl
&gt;&gt;&gt;   Context Before: ((uint16)dataBuff[(((uint8)(((uint8)0)+(uint8)(0x01U)))+1)]));
/* ID checking, if it is configured to routine control*/


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RtnConf.rtnId_funPtr != ((void (*) (uint16,uint8,uint8,const uint8 rtnOption[],uint8 rtnTxData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locID,PIDDID_Rtn,((uint16)23),&amp;idxTbl,((uint8)2))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RtnCntrl
&gt;&gt;&gt;   Unit: 27
&gt;&gt;&gt;   Function: ISOUDS_RtnCntrl
&gt;&gt;&gt;   Context Before: else
{


&gt;&gt;&gt;   Line Context: txPos += (uint8)(0x00U);

&gt;&gt;&gt;   Context After: }
}


--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RtnCntrl
&gt;&gt;&gt;   Unit: 27
&gt;&gt;&gt;   Function: ISOUDS_RtnCntrl
&gt;&gt;&gt;   Context Before: ((uint16)dataBuff[(((uint8)(((uint8)0)+(uint8)(0x01U)))+1)]));
/* ID checking, if it is configured to routine control*/


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RtnConf.rtnId_funPtr != ((void (*) (uint16,uint8,uint8,const uint8 rtnOption[],uint8 rtnTxData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locID,PIDDID_Rtn,((uint16)35),&amp;idxTbl,((uint8)2))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RtnCntrl
&gt;&gt;&gt;   Unit: 27
&gt;&gt;&gt;   Function: ISOUDS_RtnCntrl
&gt;&gt;&gt;   Context Before: ((uint16)dataBuff[(((uint8)(((uint8)0)+(uint8)(0x01U)))+1)]));
/* ID checking, if it is configured to routine control*/


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RtnConf.rtnId_funPtr != ((void (*) (uint16,uint8,uint8,const uint8 rtnOption[],uint8 rtnTxData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locID,PIDDID_Rtn,((uint16)35),&amp;idxTbl,((uint8)2))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_RtnCntrl
&gt;&gt;&gt;   Unit: 27
&gt;&gt;&gt;   Function: ISOUDS_RtnCntrl
&gt;&gt;&gt;   Context Before: ((uint16)dataBuff[(((uint8)(((uint8)0)+(uint8)(0x01U)))+1)]));
/* ID checking, if it is configured to routine control*/


&gt;&gt;&gt;   Line Context: if ((ISOUDS_RtnConf.rtnId_funPtr != ((void (*) (uint16,uint8,uint8,const uint8 rtnOption[],uint8 rtnTxData[],uint8 * retVal))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(locID,PIDDID_Rtn,((uint16)35),&amp;idxTbl,((uint8)2))))
{
</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_SA</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>CheckRequestedLevel &gt;= 1)&amp;&amp; (
CheckRequestedLevel &lt;= 0x61))

this condition can never be made false as CheckRequestedLevel % 2) == 1 condition should be passed to reach next condition


SecuritySeedRandom is not updating as 0 from test case.


ISOUDS_SAState value is not getting updated from test case.
Unreachable code</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_StrtDiagSess</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>posResp is initallized as 0 and never updated .So this condition will never be false</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_TrnsfrDa</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>flshacc  is never updated as 2 or 5

unreachable code</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_WrDaByID</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>Abnormal termination occurs if srvst value is other than 0x02

PIDDID_RdWrDID[ is user defined.Cannot be updated

resNRC is never updated as 0 or 78.

--- Invalidated Analysis Lines ---
&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_WrDaByID
&gt;&gt;&gt;   Unit: 34
&gt;&gt;&gt;   Function: ISOUDS_WrDaByID
&gt;&gt;&gt;   Context Before: ISOUDS_RecvWrDid = (((uint16)(((uint16)dataBuff[((uint8)0)]) &lt;&lt; 8)) + ((uint16)dataBuff[(((uint8)0)+1)]));
/* DID checking, if it is configured */


&gt;&gt;&gt;   Line Context: if ((ISOUDS_WrConf.wrDid_funPtr != ((uint8 (*) (uint16,const uint8 wrData[],uint8))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(ISOUDS_RecvWrDid,PIDDID_RdWrDID,((uint16)100),&amp;idxTbl,((uint8)1))))
{

&gt;&gt;&gt; Failed to find matching line for:
&gt;&gt;&gt;   Result: CBA_ISOUDS_WrDaByID
&gt;&gt;&gt;   Unit: 34
&gt;&gt;&gt;   Function: ISOUDS_WrDaByID
&gt;&gt;&gt;   Context Before: ISOUDS_RecvWrDid = (((uint16)(((uint16)dataBuff[((uint8)0)]) &lt;&lt; 8)) + ((uint16)dataBuff[(((uint8)0)+1)]));
/* DID checking, if it is configured */


&gt;&gt;&gt;   Line Context: if ((ISOUDS_WrConf.wrDid_funPtr != ((uint8 (*) (uint16,const uint8 wrData[],uint8))0))&amp;&amp;

&gt;&gt;&gt;   Context After: ((boolean)1u == UDS_LookUpTbl(ISOUDS_RecvWrDid,PIDDID_RdWrDID,((uint16)100),&amp;idxTbl,((uint8)1))))
{
</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_ISOUDS_WrMemByAddr</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>temp not taking value as per calculation

wrDaSt is never updates with value 3.</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_LINTP</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>This condition can never be false as arrlin[] values need to be set different to reach this statement.


Lin_iTpNodeRespReq is never taking 0u

RespStatus is updating with value 2 but this condition is never false.
</pre>
        <div class="row">
          <div class="col-md-10"><h4><a id=""></a>Covered By Analysis Result File: CBA_LINTP_NodeSrv_LIN_RdLookUp</h4></div>
          <div class="return-to-top col-md-2"><span class="pull-right"><a href="#TableOfContents">Top</a></span></div>
        </div>
        <p>No coverage data exists.</p>
        <h5>Notes</h5>
        <pre>1.ridFound is initallized to 0.As it is local variable this cannot be covered.

2.LIN_RdConTab is user defined.hence cannnot be covered.

3.idx should be initaliized to 0.

4. LIN_SrvMsgTab is user defined

5.list_identifiers_ROM_ptr cannot be updated

6.supplierID2Chk == 0x7FFf can never be true as   decimal value is 32.76

7.functionID2Chk == 0xFFFF can not be true as decimal value is 65.535

8.identifier &gt;= 63U) cannot be true as 1st condition will get satisifed and it will not check this condition as it is OR opeation</pre>
      </div>
<!-- VectorCAST Report footer -->
    </div></div>
  </body>
</html>