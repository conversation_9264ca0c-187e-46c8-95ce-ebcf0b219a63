#line 1 "vcast_preprocess.10472.0.c"
#line 1 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"





#pragma language=extended
#line 13 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
__intrinsic __interwork __nounwind __softfp float __aeabi_fadd ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fsub ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_frsub( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fmul ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fdiv ( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpeq( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmplt( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpgt( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpge( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpun( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfcmpeq( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfrcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_i2f( int x );
__intrinsic __interwork __nounwind __softfp float __aeabi_ui2f( unsigned int x );
__intrinsic __interwork __nounwind __softfp int __aeabi_f2iz( float x );
__intrinsic __interwork __nounwind __softfp unsigned int __aeabi_f2uiz( float x );
__intrinsic __interwork __nounwind __softfp float __aeabi_l2f( long long x );
__intrinsic __interwork __nounwind __softfp float __aeabi_ul2f( unsigned long long x );
__intrinsic __interwork __nounwind __softfp long long __aeabi_f2lz( float x );
__intrinsic __interwork __nounwind __softfp unsigned long long __aeabi_f2ulz( float x );
__intrinsic __interwork __nounwind __softfp double __aeabi_dadd( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_dsub( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_drsub( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_dmul( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_ddiv( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpeq( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmplt( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpge( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpgt( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpun( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdcmpeq( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdrcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_i2d( int x );
__intrinsic __interwork __nounwind __softfp double __aeabi_ui2d( unsigned int x );
__intrinsic __interwork __nounwind __softfp int __aeabi_d2iz( double d );
__intrinsic __interwork __nounwind __softfp unsigned int __aeabi_d2uiz( double d );
__intrinsic __interwork __nounwind __softfp double __aeabi_f2d( float f );
__intrinsic __interwork __nounwind __softfp float __aeabi_d2f( double d );
__intrinsic __interwork __nounwind __softfp double __aeabi_l2d( long long x );
__intrinsic __interwork __nounwind __softfp double __aeabi_ul2d( unsigned long long x );
__intrinsic __interwork __nounwind __softfp long long __aeabi_d2lz( double d );
__intrinsic __interwork __nounwind __softfp unsigned long long __aeabi_d2ulz( double d );
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_lmul(long long, long long); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_llsl(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_llsr(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_lasr(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_lcmp(long long, long long); 
__intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_ulcmp(unsigned long long, unsigned long long); 
_Pragma("function_effects = no_state, no_write(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_uread4(void *address); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_uwrite4(int value, void *address); 
_Pragma("function_effects = no_state, no_write(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_uread8(void *address); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_uwrite8(long long value, void *address); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy8 (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy4 (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy  (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove8(void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove4(void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset8 (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset4 (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset  (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr8 (void *d, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr4 (void *d, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr  (void *d, unsigned int n); 
__intrinsic __interwork __nounwind unsigned int __get_SP( void ); 
__intrinsic __interwork __nounwind unsigned int __get_LR( void ); 
__intrinsic __interwork __nounwind unsigned int __get_PC( void ); 
__intrinsic __interwork __nounwind void __set_SP( unsigned int ); 
__intrinsic __interwork __nounwind void __set_LR( unsigned int ); 
__intrinsic __interwork __nounwind unsigned int __get_return_address( void ); 
#pragma no_bounds
__intrinsic int __semihosting( unsigned int id, void *p ); 
__intrinsic int __semihosting2( unsigned int id, unsigned int id2, void *p ); 
#line 109 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind void *__aeabi_read_tp(void);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fp2bits32(float);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned long long __iar_fp2bits64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fpgethi64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fpgetlow64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_fpsethi64(double, unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_fpsetlow64(double, unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind float __iar_bits2fp32(unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_bits2fp64(unsigned long long);
__intrinsic __interwork __nounwind __noreturn void __stack_chk_fail(void);
extern unsigned int __stack_chk_guard;
#line 127 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
#pragma language=default
#line 2 "vcast_preprocess.10472.0.c"
#line 1 "C:/Users/<USER>/MBVan/MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC/MBDevStrm/SwApplication/Application/HardwareAbstractionLayer/DiagComMan/Code/ISOUDS_TrnsfrDa.c"
/***************************************************************************************************
**
** -------------------------------------------------------------------------------------------------
** File Name    : ISOUDS_TrnsfrDa.c
**
** Description  : Transfer Data Service. This service is used to tranfer data between client and
**                server
**
** -------------------------------------------------------------------------------------------------
**
***************************************************************************************************/

/**************************************** Inclusion files *****************************************/
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS_TrnsfrDa.h"
/***************************************************************************************************
**
** -------------------------------------------------------------------------------------------------
** File Name    : ISOUDS_TrnsfrDa.h
**
** Description  : Include file of component ISOUDS_TrnsfrDa.c
**
** -------------------------------------------------------------------------------------------------
**
***************************************************************************************************/

/* To avoid multi-inclusions */



/************************************* Inclusion files ********************************************/
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"
/*******************************************************************************
** Copyright (c) 2015 INALFA
**
** This software is the property of Inalfa.
** It can not be used or duplicated without Inalfa.
**
** -----------------------------------------------------------------------------
** File Name   : ISOUDS.h
** Module Name : UDS
** -----------------------------------------------------------------------------
**
** Description : UDS Service Layer
** This file must exclusively contain informations needed to
** use this component.
**
** -----------------------------------------------------------------------------
**
** Documentation reference : 
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** Date        Author      Item      Description
** 2018-11-28  Yongdong    [#5470]   Added shared data protection
** V01.00  16/11/2015
** - Baseline 
**
*******************************************************************************/
/*************************** Inclusion files **********************************/
/* To avoid multi-inclusions */



/************************************* Inclusion files ************************/
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS_Cfg.h"
/*******************************************************************************
** Copyright (c) 2015 INALFA
**
** This software is the property of Inalfa.
** It can not be used or duplicated without Inalfa.
**
** -----------------------------------------------------------------------------
** File Name   : ISOUDS_Cfg.h
** Module Name : UDS
** -----------------------------------------------------------------------------
**
** Description : UDS Service Layer
** This file must exclusively contain informations needed to
** use this component.
**
** -----------------------------------------------------------------------------
**
** Documentation reference : 
**
********************************************************************************
** R E V I S I O N H I S T O R Y
********************************************************************************
** Date        Author      Item      Description
** 2019-17-06  boutenp1    [#7052]   Increased table for ISOUDS_SIDRDMEMBYADDR
** 
** V01.00  16/11/2015
** - Baseline 
**
*******************************************************************************/
/* To avoid multi-inclusions */



/************************************* Inclusion files ************************/
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\irs_std_types.h"


/** @defgroup common Common Group
 ** @ingroup project
 **
 **  IRS common includes
 ** @{ */

/** @file
 **
 ** Header for the IRS standard types
 **/

/*******************************************************************************
*   File Name               :   irs_std_types.h
*   Component Name          :   COMMON
*   UCom                    :   NXP S32K Series
*
*   Create Date             :   2024-05-31
*   Author                  :   kortam
*   Corporation             :   Inalfa Roof Systems
*
*   Abstract Description    :   public header of irs standard types
*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved
*                               Unauthorized copying of this file, via any medium
*                               is strictly prohibited
*                               Proprietary and confidential
*---Revision History------------------------------------------------------------
*   Date        Author    Item     Description
*   2024-05-22  tasdec    [#] initial revision 
*   2024-05-31  kortam    [44976] update to fix compiler warning 
 *******************************************************************************/


/*******************************************************************************
*   Debug Switch Section
*******************************************************************************/

/*******************************************************************************
*    Include File Section
*******************************************************************************/

#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"
/* stdint.h standard header */
/* Copyright 2003-2017 IAR Systems AB.  */




  #pragma system_include


#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ycheck.h"
/* ycheck.h internal checking header file. */
/* Copyright 2005-2017 IAR Systems AB. */

/* Note that there is no include guard for this header. This is intentional. */


  #pragma system_include


/* __AEABI_PORTABILITY_INTERNAL_LEVEL
 *
 * Note: Redefined each time ycheck.h is included, i.e. for each
 * system header, to ensure that ABI support could be turned off/on
 * individually for each file.
 *
 * Possible values for this preprocessor symbol:
 *
 * 0 - ABI portability mode is disabled.
 *
 * 1 - ABI portability mode (version 1) is enabled.
 *
 * All other values are reserved for future use.
 */





#line 11 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
/* yvals.h internal configuration header file. */
/* Copyright 2001-2017 IAR Systems AB. */





  #pragma system_include


/* Convenience macros */









/* Used to refer to '__aeabi' symbols in the library. */


/* Dinkum version */


/* DLib version */




/* Module consistency. */
#pragma rtmodel = "__dlib_version", "6"

/* IAR compiler version check */





/* Read configuration */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"
/***************************************************
 *
 * DLib_Defaults.h is the library configuration manager.
 *
 * Copyright 2003-2017 IAR Systems AB.
 *
 * This configuration header file performs the following tasks:
 *
 * 1. Includes the configuration header file, defined by _DLIB_CONFIG_FILE,
 *    that sets up a particular runtime environment.
 *
 * 2. Includes the product configuration header file, DLib_Product.h, that
 *    specifies default values for the product and makes sure that the
 *    configuration is valid.
 *
 * 3. Sets up default values for all remaining configuration symbols.
 *
 * This configuration header file, the one defined by _DLIB_CONFIG_FILE, and
 * DLib_Product.h configures how the runtime environment should behave. This
 * includes all system headers and the library itself, i.e. all system headers
 * includes this configuration header file, and the library has been built
 * using this configuration header file.
 *
 ***************************************************
 *
 * DO NOT MODIFY THIS FILE!
 *
 ***************************************************/





  #pragma system_include


/* Include the main configuration header file. */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Config_Normal.h"
/* DLib configuration. */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include


/* No changes to the defaults. */

#line 40 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"
  /* _DLIB_CONFIG_FILE_STRING is the quoted variant of above */
#line 47 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/* Include the product specific header file. */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Product.h"
/* Copyright 2017 IAR Systems AB. */





   #pragma system_include



/*********************************************************************
*
*       Configuration
*
*********************************************************************/

/* Wide character and multi byte character support in library.
 * This is not allowed to vary over configurations, since math-library
 * is built with wide character support.
 */


/* This ensures that the standard header file "string.h" includes
 * the Arm-specific file "DLib_Product_string.h". */


/* This ensures that the standard header file "fenv.h" includes
 * the Arm-specific file "DLib_Product_fenv.h". */


/* This ensures that the standard header file "stdlib.h" includes
 * the Arm-specific file "DLib_Product_stdlib.h". */


/* Max buffer used for swap in qsort */









/* Enable AEABI support */


/* Enable rtmodel for setjump buffer size */


/* Enable parsing of hex floats */






/* Special placement for locale structures when building ropi libraries */




/* Use atomic if possible */




/* CPP-library uses software floatingpoint interface (NOT --libc++) */




/* functions for setting errno should be __no_scratch */


/* Use speedy implementation of floats (simple quad). */


/* Configure time types */


/* Configure generic ELF init routines. */
#line 111 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Product.h"













#line 51 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"



/*
 * The remainder of the file sets up defaults for a number of
 * configuration symbols, each corresponds to a feature in the
 * libary.
 *
 * The value of the symbols should either be 1, if the feature should
 * be supported, or 0 if it shouldn't. (Except where otherwise
 * noted.)
 */


/*
 * File handling
 *
 * Determines whether FILE descriptors and related functions exists or not.
 * When this feature is selected, i.e. set to 1, then FILE descriptors and
 * related functions (e.g. fprintf, fopen) exist. All files, even stdin,
 * stdout, and stderr will then be handled with a file system mechanism that
 * buffers files before accessing the lowlevel I/O interface (__open, __read,
 * __write, etc).
 *
 * If not selected, i.e. set to 0, then FILE descriptors and related functions
 * (e.g. fprintf, fopen) does not exist. All functions that normally uses
 * stderr will use stdout instead. Functions that uses stdout and stdin (like
 * printf and scanf) will access the lowlevel I/O interface directly (__open,
 * __read, __write, etc), i.e. there will not be any buffering.
 *
 * The default is not to have support for FILE descriptors.
 */






/*
 * Use static buffers for stdout
 *
 * This setting controls whether the stream stdout uses a static 80 bytes
 * buffer or uses a one byte buffer allocated in the file descriptor. This
 * setting is only applicable if the FILE descriptors are enabled above.
 *
 * Default is to use a static 80 byte buffer.
 */






/*
 * Support of locale interface
 *
 * "Locale" is the system in C that support language- and
 * contry-specific settings for a number of areas, including currency
 * symbols, date and time, and multibyte encodings.
 *
 * This setting determines whether the locale interface exist or not.
 * When this feature is selected, i.e. set to 1, the locale interface exist
 * (setlocale, etc). A number of preselected locales can be activated during
 * runtime. The preselected locales and encodings are choosen at linkage. The
 * application will start with the "C" locale choosen. (Single byte encoding is
 * always supported in this mode.)
 *
 *
 * If not selected, i.e. set to 0, the locale interface (setlocale, etc) does
 * not exist. The C locale is then preset and cannot be changed.
 *
 * The default is not to have support for the locale interface with the "C"
 * locale and the single byte encoding.
 */





/*
 * Define what memory to place the locale table segment (.iar.locale_table)
 * in.
 */




/*
 * Wide character and multi byte character support in library.
 */

#line 153 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Support of multibytes in printf- and scanf-like functions
 *
 * This is the default value for _DLIB_PRINTF_MULTIBYTE and
 * _DLIB_SCANF_MULTIBYTE. See them for a description.
 *
 * Default is to not have support for multibytes in printf- and scanf-like
 * functions.
 */

#line 172 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Hexadecimal floating-point numbers in strtod
 *
 * If selected, i.e. set to 1, strtod supports C99 hexadecimal floating-point
 * numbers. This also enables hexadecimal floating-points in internal functions
 * used for converting strings and wide strings to float, double, and long
 * double.
 *
 * If not selected, i.e. set to 0, C99 hexadecimal floating-point numbers
 * aren't supported.
 *
 * Default is not to support hexadecimal floating-point numbers.
 */






/*
 * Printf configuration symbols.
 *
 * All the configuration symbols described further on controls the behaviour
 * of printf, sprintf, and the other printf variants.
 *
 * The library proves four formatters for printf: 'tiny', 'small',
 * 'large', and 'default'.  The setup in this file controls all except
 * 'tiny'.  Note that both small' and 'large' explicitly removes
 * some features.
 */

/*
 * Handle multibytes in printf
 *
 * This setting controls whether multibytes and wchar_ts are supported in
 * printf. Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default setting.
 */
#line 223 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Support of formatting anything larger than int in printf
 *
 * This setting controls if 'int' should be used internally in printf, rather
 * than the largest existing integer type. If 'int' is used, any integer or
 * pointer type formatting use 'int' as internal type even though the
 * formatted type is larger. Set to 1 to use 'int' as internal type, otherwise
 * set to 0.
 *
 * See also next configuration.
 *
 * Default is to internally use largest existing internally type.
 */




/*
 * Support of formatting anything larger than long in printf
 *
 * This setting controls if 'long' should be used internally in printf, rather
 * than the largest existing integer type. If 'long' is used, any integer or
 * pointer type formatting use 'long' as internal type even though the
 * formatted type is larger. Set to 1 to use 'long' as internal type,
 * otherwise set to 0.
 *
 * See also previous configuration.
 *
 * Default is to internally use largest existing internally type.
 */








/*
 * Emit a char a time in printf
 *
 * This setting controls internal output handling. If selected, i.e. set to 1,
 * then printf emits one character at a time, which requires less code but
 * can be slightly slower for some types of output.
 *
 * If not selected, i.e. set to 0, then printf buffers some outputs.
 *
 * Note that it is recommended to either use full file support (see
 * _DLIB_FILE_DESCRIPTOR) or -- for debug output -- use the linker
 * option "-e__write_buffered=__write" to enable buffered I/O rather
 * than deselecting this feature.
 */





/*
 * Scanf configuration symbols.
 *
 * All the configuration symbols described here controls the
 * behaviour of scanf, sscanf, and the other scanf variants.
 *
 * The library proves three formatters for scanf: 'small', 'large',
 * and 'default'.  The setup in this file controls all, however both
 * 'small' and 'large' explicitly removes some features.
 */

/*
 * Handle multibytes in scanf
 *
 * This setting controls whether multibytes and wchar_t:s are supported in
 * scanf. Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default.
 */
#line 311 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Handle multibytes in asctime and strftime.
 *
 * This setting controls whether multibytes and wchar_ts are
 * supported.Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default setting.
 */
#line 331 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Implement "qsort" using a bubble sort algorithm.
 *
 * Bubble sort is less efficient than quick sort but requires less RAM
 * and ROM resources.
 */






/*
 * Set Buffert size used in qsort
 */






/*
 * Use a simple rand implementation to reduce memory footprint.
 *
 * The default "rand" function uses an array of 32 32-bit integers of memory to
 * store the current state.
 *
 * The simple "rand" function uses only a single 32-bit integer. However, the
 * quality of the generated psuedo-random numbers are not as good as
 * the default implementation.
 */






/*
 * Set attributes for the function used by the C-SPY debug interface to stop at.
 */





/*
 * Used by products where one runtime library can be used by applications
 * with different data models, in order to reduce the total number of
 * libraries required. Typically, this is used when the pointer types does
 * not change over the data models used, but the placement of data variables
 * or/and constant variables do.
 *
 * If defined, this symbol is typically defined to the memory attribute that
 * is used by the runtime library. The actual define must use a
 * _Pragma("type_attribute = xxx") construct. In the header files, it is used
 * on all statically linked data objects seen by the application.
 */




#line 400 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Turn on support for the Target-specific ABI. The ABI is based on the
 * ARM AEABI. A target, except ARM, may deviate from it.
 */

#line 414 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


  /* Possible AEABI deviations */
#line 424 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

#line 432 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

  /*
   * The "difunc" table contains information about C++ objects that
   * should be dynamically initialized, where each entry in the table
   * represents an initialization function that should be called. When
   * the symbol _DLIB_AEABI_DIFUNC_CONTAINS_OFFSETS is true, each
   * entry in the table is encoded as an offset from the entry
   * location. When false, the entries contain the actual addresses to
   * call.
   */





/*
 * Only use IA64 functions
 *
 * Remove the C++ __aeabi functions when using the IA64 interface. Used in
 * ARM AARCH64 mode.
 *
 */
#line 461 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Turn on usage of a pragma to tell the linker the number of elements used
 * in a setjmp jmp_buf.
 */






/*
 * If true, the product supplies a "DLib_Product_string.h" file that
 * is included from "string.h".
 */





/*
 * Determine whether the math fma routines are fast or not.
 */





/*
 * Favor speed versus some size enlargements in floating point functions.
 */





/*
 * Include dlmalloc as an alternative heap manager in the product.
 *
 * Typically, an application will use a "malloc" heap manager that is
 * relatively small but not that efficient. An application can
 * optionally use the "dlmalloc" package, which provides a more
 * effective "malloc" heap manager, if it is included in the product
 * and supported by the settings.
 *
 * See the product documentation on how to use it, and whether or not
 * it is included in the product.
 */


  /* size_t/ptrdiff_t must be a 4 bytes unsigned integer. */
#line 518 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Make sure certain C++ functions use the soft floating point variant.
 */






/*
 * Allow the 64-bit time_t interface?
 *
 * Default is yes if long long is 64 bits.
 */

#line 542 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Is time_t 64 or 32 bits?
 *
 * Default is 64 bits for the platform.
 */






/*
 * Do we include math functions that demands lots of constant bytes?
 * (like erf, erfc, expm1, fma, lgamma, tgamma, and *_accurate)
 *
 */






/*
 * Support of weak.
 *
 * __weak must be supported. Support of weak means that the call to
 * a weak declared function that isn't part of the application will be
 * executed as a nop instruction.
 *
 */

#line 582 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Deleted options
 */












#line 43 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"






/* A definiton for a function of what effects it has.
   NS  = no_state, errno, i.e. it uses no internal or external state. It may
         write to errno though
   NE  = no_state, i.e. it uses no internal or external state, not even
         writing to errno.
   NRx = no_read(x), i.e. it doesn't read through pointer parameter x.
   NWx = no_write(x), i.e. it doesn't write through pointer parameter x.
   Rx  = returns x, i.e. the function will return parameter x.

   All the functions with effects also has "always_returns",
   i.e. the function will always return.
*/

#line 81 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* Common function attribute macros */






/* Extern "C" handling */
#line 99 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"


/*
 * Support for C99/C11 functionality, C99 secure C functionality, and some
 * other functionality.
 *
 * This setting makes available some macros, functions, etc that are
 * either mandatory in C99/C11 or beneficial.
 *
 * Default is to include them.
 *
 * Disabling this in C++ mode will not compile (some C++ functions uses C99
 * functionality).
 */


  /* Default turned on only when compiling C89 (not C++, C99, or C11). */
#line 124 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"





/* Secure C */
#line 142 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"




/* C++ language setup */
#line 191 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 199 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 206 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* MB_LEN_MAX (max for utf-8 is 4) */


/* for parsing numerics */




/* wchar_t setup */





  typedef unsigned int _Wchart;
  typedef unsigned int _Wintt;
#line 238 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* POINTER PROPERTIES */


/* size_t setup */
typedef unsigned int     _Sizet;

/* Basic integer sizes */
typedef signed char   __int8_t;
typedef unsigned char  __uint8_t;
typedef signed short int   __int16_t;
typedef unsigned short int  __uint16_t;
typedef signed int   __int32_t;
typedef unsigned int  __uint32_t;

   typedef signed long long int   __int64_t;
   typedef unsigned long long int  __uint64_t;




typedef signed int   __intptr_t;
typedef unsigned int  __uintptr_t;

/* mbstatet setup */
typedef struct _Mbstatet
{ /* state of a multibyte translation */

    unsigned int _Wchar;  /* Used as an intermediary value (up to 32-bits) */
    unsigned int _State;  /* Used as a state value (only some bits used) */
#line 275 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 299 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
} _Mbstatet;






/* printf setup */


/* stdarg PROPERTIES */
#line 321 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
  typedef struct __va_list __Va_list;














/* File position */
typedef struct
{

    long long _Off;    /* can be system dependent */



  _Mbstatet _Wstate;
} _Fpost;





/* THREAD AND LOCALE CONTROL */


/* MULTITHREAD PROPERTIES */

  
  /* The lock interface for DLib to use. */
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_StaticGuard(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_StaticGuard(void);

#line 373 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

  typedef void *__iar_Rmtx;

  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Initdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Dstdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockdynamiclock(__iar_Rmtx *);

  
#line 406 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 446 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"



/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 12 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"





/* Fixed size types. These are all optional. */

  typedef signed char          int8_t;
  typedef unsigned char        uint8_t;



  typedef signed short int         int16_t;
  typedef unsigned short int       uint16_t;



  typedef signed int         int32_t;
  typedef unsigned int       uint32_t;



  typedef signed long long int         int64_t;
  typedef unsigned long long int       uint64_t;


/* Types capable of holding at least a certain number of bits.
   These are not optional for the sizes 8, 16, 32, 64. */
typedef signed char      int_least8_t;
typedef unsigned char    uint_least8_t;

typedef signed short int     int_least16_t;
typedef unsigned short int   uint_least16_t;

typedef signed int     int_least32_t;
typedef unsigned int   uint_least32_t;

/* This isn't really optional, but make it so for now. */

  typedef signed long long int   int_least64_t;


  typedef unsigned long long int uint_least64_t;


/* The fastest type holding at least a certain number of bits.
   These are not optional for the size 8, 16, 32, 64.
   For now, the 64 bit size is optional in IAR compilers. */
typedef signed int       int_fast8_t;
typedef unsigned int     uint_fast8_t;

typedef signed int      int_fast16_t;
typedef unsigned int    uint_fast16_t;

typedef signed int      int_fast32_t;
typedef unsigned int    uint_fast32_t;


  typedef signed long long int    int_fast64_t;


  typedef unsigned long long int  uint_fast64_t;


/* The integer type capable of holding the largest number of bits. */
typedef signed long long int          intmax_t;
typedef unsigned long long int        uintmax_t;

/* An integer type large enough to be able to hold a pointer.
   This is optional, but always supported in IAR compilers. */
typedef signed int          intptr_t;
typedef unsigned int        uintptr_t;

/* An integer capable of holding a pointer to a specific memory type. */



typedef int __data_intptr_t; typedef unsigned int __data_uintptr_t;


/* Minimum and maximum limits. */

























































































/* Macros expanding to integer constants. */
































/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 44 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\irs_std_types.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdbool.h"
/* stdbool.h header */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include













/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 45 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\irs_std_types.h"

/*******************************************************************************
*   Macro Define Section
*******************************************************************************/















/* Adding these data types to handle issues in building the SW - Start */
typedef uint8_t uint8;
typedef uint16_t uint16;
typedef uint32_t uint32;
typedef _Bool boolean;
typedef int8_t int8;
typedef int16_t int16;



/* Adding these data types to handle issues in building the SW - End */

/*******************************************************************************
*   Type Define Section
*******************************************************************************/
/** IRS return values for standard information*/
typedef enum 
{
	SCU_STATUS_SUCCESS                      = 0x000U,    /*!< ECU success status */
	SCU_STATUS_ERROR                        = 0x001U,    /*!< ECU failure status */
    SCU_STATUS_BUSY                         = 0x002U,    /*!< ECU busy status */
    SCU_STATUS_TIMEOUT                      = 0x003U,    /*!< ECU timeout status */
	SCU_STATUS_CRC_ERROR                    = 0x004U    /*!< ECU NVM CRC errors */
} HwAbs_tenStatus;

/*ExtDev_tenFault should not be more than 15 count*/
typedef enum 
{
	EXTDEV_SPI_CRC_FAULT= 0,
	EXTDEV_VCC1WARN_FAULT,
	EXTDEV_CHRAGE_PUMP_FAULT,
	EXTDEV_THERM_STAT_TPW_TSD1_FAULT,
	EXTDEV_THERM_STAT_TSD2_TSD2SAFE_FAULT,
	EXTDEV_HS_SUPPLY_FAULT,
	EXTDEV_VSINT_FAULT,
	EXTDEV_VS_SUPPLY_FAULT,
	EXTDEV_LIN_FAIL_FAULT,
	EXTDEV_DSOV_MTRACT_FAULT,
	EXTDEV_WKSTS_MTRBKEMF_WKUP_FAULT,
	EXTDEV_WKSTS_SPURIOUS_WKUP_FAULT,
	EXTDEV_DEV_STAT_FAILURE_FAULT,
	EXTDEV_SUP_STAT_VCC1FAULT,
} ExtDev_tenFaults;

typedef enum 
{
	MotorDirection_CW = 0,
	MotorDirection_CCW,
	MotorDirection_STOP
} ExtDev_tenDirection;
/*******************************************************************************
*   Global Variable Declaration Section
*******************************************************************************/


/*******************************************************************************
*   Global Function Declaration Section
*******************************************************************************/


/**@} */
#line 36 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS_Cfg.h"

/************************** Declaration of global symbol and constants ********/
/* The periodic scheduling interval of iso14229-1 */


/* Timer Threshold before ECU is reset */


/* Service distributor configuration table size */

/* UDS Buffer size */

 /* Standard P2 limit = 50 ms */

/* NRC 78 minimum limit = 4500 ms */

/* NRC 78 maximum limit = 5000 ms */

/* Timer S3 limit = 5000 ms */

   
/********************************* Declaration of global macros ***************/

/********************************* Declaration of global types ****************/

/****************************** External links of global variables ************/

/****************************** External links of global constants ************/

/*******************************************************************************
**                                      FUNCTIONS                             **
*******************************************************************************/

/********************************** Function definitions **********************/

#line 36 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"


/** @addtogroup  COMMON
 ** @{ */

/** @file
 **
 ** Public header for common includes and common definitions
 **/

/*******************************************************************************
*   File Name               :   common.h
*   Component Name          :   COMMON
*   UCom                    :   NXP S32K series
*
*   Create Date             :   2024-05-22
*   Author                  :   tasdec
*   Corporation             :   Inalfa Roof Systems
*
*   Abstract Description    :   Public header of the IRS libs
*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved
*                               Unauthorized copying of this file, via any medium
*                               is strictly prohibited
*                               Proprietary and confidential
*---Revision History------------------------------------------------------------
*   Date        Author    Item     Description
*   2024-05-22  tasdec    [#]  initial revision
*******************************************************************************/


/*******************************************************************************
*   Debug Switch Section
*******************************************************************************/


/*******************************************************************************
*    Include File Section
*******************************************************************************/
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\status.h"
/*
 * Copyright (c) 2016, Freescale Semiconductor, Inc.
 * Copyright 2016-2020 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */




/**
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.3, Global typedef not referenced.
 * status_t is referenced from all drivers.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Local macro not referenced.
 * The defined macro is used as include guard.
 *
 */

/*******************************************************************************
 * Definitions
 ******************************************************************************/

/*! @brief Status return codes.
 * Common error codes will be a unified enumeration (C enum) that will contain all error codes
 * (common and specific). There will be separate "error values spaces" (or slots), each of 256
 * positions, allocated per functionality.
 */
typedef enum
{
    /* Generic error codes */
    STATUS_SUCCESS                         = 0x000U,    /*!< Generic operation success status */
    STATUS_ERROR                           = 0x001U,    /*!< Generic operation failure status */
    STATUS_BUSY                            = 0x002U,    /*!< Generic operation busy status */
    STATUS_TIMEOUT                         = 0x003U,    /*!< Generic operation timeout status */
    STATUS_UNSUPPORTED                     = 0x004U,    /*!< Generic operation unsupported status */
    /* MCU specific error codes */
    STATUS_MCU_GATED_OFF                   = 0x100U,  /*!< Module is gated off */
    STATUS_MCU_TRANSITION_FAILED           = 0x101U,  /*!< Error occurs during transition. */
    STATUS_MCU_INVALID_STATE               = 0x102U,  /*!< Unsupported in current state. */
    STATUS_MCU_NOTIFY_BEFORE_ERROR         = 0x103U,  /*!< Error occurs during send "BEFORE" notification. */
    STATUS_MCU_NOTIFY_AFTER_ERROR          = 0x104U,  /*!< Error occurs during send "AFTER" notification. */
    /* I2C specific error codes */
    STATUS_I2C_RECEIVED_NACK               = 0x200U,  /*!< NACK signal received  */
    STATUS_I2C_TX_UNDERRUN                 = 0x201U,  /*!< TX underrun error */
    STATUS_I2C_RX_OVERRUN                  = 0x202U,  /*!< RX overrun error */
    STATUS_I2C_ARBITRATION_LOST            = 0x203U,  /*!< Arbitration lost */
    STATUS_I2C_ABORTED                     = 0x204U,  /*!< A transfer was aborted */
    STATUS_I2C_BUS_BUSY                    = 0x205U,  /*!< I2C bus is busy, cannot start transfer */
    /* CAN specific error codes */
    STATUS_CAN_BUFF_OUT_OF_RANGE           = 0x300U,  /*!< The specified MB index is out of the configurable range */
    STATUS_CAN_NO_TRANSFER_IN_PROGRESS     = 0x301U,  /*!< There is no transmission or reception in progress */
    /* Security specific error codes */
    STATUS_SEC_SEQUENCE_ERROR              = 0x402U,  /*!< The sequence of commands or subcommands is out of
                                                            sequence */
    STATUS_SEC_KEY_NOT_AVAILABLE           = 0x403U,  /*!< A key is locked due to failed boot measurement or
                                                            an active debugger */
    STATUS_SEC_KEY_INVALID                 = 0x404U,  /*!< A function is called to perform an operation with
                                                            a key that is not allowed for the given operation */
    STATUS_SEC_KEY_EMPTY                   = 0x405U,  /*!< Attempt to use a key that has not been initialized yet */
    STATUS_SEC_NO_SECURE_BOOT              = 0x406U,  /*!< The conditions for a secure boot process are not met */
    STATUS_SEC_KEY_WRITE_PROTECTED         = 0x407U,  /*!< Request for updating a write protected key slot,
                                                            or activating debugger with write protected key(s) */
    STATUS_SEC_KEY_UPDATE_ERROR            = 0x408U,  /*!< Key update did not succeed due to errors in
                                                            verification of the messages */
    STATUS_SEC_RNG_SEED                    = 0x409U,  /*!< Returned by CMD_RND and CMD_DEBUG if the seed has not
                                                            been initialized before */
    STATUS_SEC_NO_DEBUGGING                = 0x40AU,  /*!< DEBUG command authentication failed */
    STATUS_SEC_MEMORY_FAILURE              = 0x40CU,  /*!< General memory technology failure
                                                            (multibit ECC error, common fault detected) */
    STATUS_SEC_HSM_INTERNAL_MEMORY_ERROR   = 0x410U,  /*!< An internal memory error encountered while
                                                           executing the command */
    STATUS_SEC_INVALID_COMMAND             = 0x411U,  /*!< Command value out of range */
    STATUS_SEC_TRNG_ERROR                  = 0x412U,  /*!< One or more statistical tests run on the TRNG output failed */
    STATUS_SEC_HSM_FLASH_BLOCK_ERROR       = 0x413U,  /*!< Error reading, programming or erasing one of the HSM flash blocks */
    STATUS_SEC_INTERNAL_CMD_ERROR          = 0x414U,  /*!< An internal command processor error while executing a command */
    STATUS_SEC_MAC_LENGTH_ERROR            = 0x415U,  /*!< MAC/Message length out of range */
    STATUS_SEC_INVALID_ARG                 = 0x421U,  /*!< Invalid command argument */
    STATUS_SEC_TRNG_CLOCK_ERROR            = 0x423U,  /*!< TRNG not provided with a stable clock */
    /* SPI specific error codes */
    STATUS_SPI_TX_UNDERRUN                 = 0x500U,  /*!< TX underrun error */
    STATUS_SPI_RX_OVERRUN                  = 0x501U,  /*!< RX overrun error */
    STATUS_SPI_ABORTED                     = 0x502U,  /*!< A transfer was aborted */
    /* UART specific error codes */
    STATUS_UART_TX_UNDERRUN                = 0x600U,  /*!< TX underrun error */
    STATUS_UART_RX_OVERRUN                 = 0x601U,  /*!< RX overrun error */
    STATUS_UART_ABORTED                    = 0x602U,  /*!< A transfer was aborted */
	STATUS_UART_FRAMING_ERROR              = 0x603U,  /*!< Framing error */
	STATUS_UART_PARITY_ERROR               = 0x604U,  /*!< Parity error */
	STATUS_UART_NOISE_ERROR                = 0x605U,  /*!< Noise error */
    /* I2S specific error codes */
    STATUS_I2S_TX_UNDERRUN                 = 0x700U,  /*!< TX underrun error */
    STATUS_I2S_RX_OVERRUN                  = 0x701U,  /*!< RX overrun error */
    STATUS_I2S_ABORTED                     = 0x702U,  /*!< A transfer was aborted */
    /* SBC specific error codes */
    SBC_NVN_ERROR                          = 0x801U, /*!< Unsuccessful attempt writing to non volatile memory
                                                          (0x73 and 0x74). Set device to factory settings. */
    SBC_COMM_ERROR                         = 0x802U, /*!< Data transfer was aborted */
    SBC_CMD_ERROR                          = 0x804U, /*!< Wrong command. */
    SBC_ERR_NA                             = 0x808U, /*!< Feature/device not available */
    SBC_MTPNV_LOCKED                       = 0x810U, /*!< Unable to write MTPNV cells, NVMPS = 0 */

    /* FLASH specific error codes */
    STATUS_FLASH_ERROR_ENABLE              = 0x901U, /*!< It's impossible to enable an operation */
    STATUS_FLASH_ERROR_NO_BLOCK            = 0x902U, /*!< No blocks have been enabled for Array Integrity check */
    STATUS_FLASH_INPROGRESS                = 0x903U, /*!< InProgress status */

    /* SAI specific error codes */
    STATUS_SAI_ABORTED                     = 0xA00U, /*!< SAI aborted status */

    /* ENET specific error codes */
    STATUS_ENET_RX_QUEUE_EMPTY             = 0xA01U, /*!< There is no available frame in the receive queue */
    STATUS_ENET_TX_QUEUE_FULL              = 0xA02U, /*!< There is no available space for the frame in the transmit queue */
    STATUS_ENET_BUFF_NOT_FOUND             = 0xA03U, /*!< The specified buffer was not found in the queue */

    /* FCCU specific error codes */
    STATUS_FCCU_ERROR_CONFIG_TIMEOUT       = 0xB01U, /*!< FCCU triggers TimeOut when try to enter in Config State */
    STATUS_FCCU_ERROR_INIT_FCCU            = 0xB02U, /*!< FCCU Initializing FCCU Module */
    STATUS_FCCU_ERROR_SET_CONFIG           = 0xB03U, /*!< FCCU Fail to Enter in Config Mode  */
    STATUS_FCCU_ERROR_SET_NORMAL           = 0xB04U, /*!< FCCU Fail to Enter in Normal Mode  */
    STATUS_FCCU_ERROR_APPLY_NCF_CONFIG     = 0xB05U, /*!< FCCU Fail to set NoCritical Faults  */
    STATUS_FCCU_ERROR_UPDATE_FREEZE        = 0xB06U, /*!< FCCU Fail to update Freez Status registers */
    STATUS_FCCU_ERROR_CLEAR_FREEZE         = 0xB07U, /*!< FCCU Fail to Clear Freez Status registers */
    STATUS_FCCU_ERROR_SET_EOUT             = 0xB08U, /*!< FCCU Fail to Set Eout Configuration */
    STATUS_FCCU_ERROR_FAULT_DETECTED       = 0xB09U, /*!< FCCU Faults Detected */
    STATUS_FCCU_ERROR_OTHER                = 0xB0AU, /*!< FCCU other Error */

    /* EMIOS specific error codes */
    STATUS_EMIOS_WRONG_MODE                = 0xC00U,   /*!< EMIOS unsuccessful attempt selecting wrong mode. */
    STATUS_EMIOS_CNT_BUS_OVERFLOW          = 0xC01U,   /*!< EMIOS counter bus overflow. */
    STATUS_EMIOS_WRONG_CNT_BUS             = 0xC02U,   /*!< EMIOS unsuccessful attempt selecting wrong counter bus. */
    STATUS_EMIOS_ENABLE_GLOBAL_FRZ         = 0xC03U,   /*!< EMIOS must set global allow enter debug mode first. */

    /* EEE specific error codes */
    STATUS_EEE_ERROR_NO_ENOUGH_SPACE       = 0xD00U, /*!< The data is too big to fit in any of the block */
    STATUS_EEE_ERROR_NO_ENOUGH_BLOCK       = 0xD01U, /*!< The block numbers is not enough for round robin */
    STATUS_EEE_ERROR_DATA_NOT_FOUND        = 0xD02U, /*!< The required data is not found in the EEPROM emulation */
    STATUS_EEE_ERROR_NOT_IN_CACHE          = 0xD03U, /*!< The required data is not in the cache table */
    STATUS_EEE_ERROR_PROGRAM_INDICATOR     = 0xD04U, /*!< Failed to make block indicator to non-blank for several times */
    STATUS_EEE_HVOP_INPROGRESS             = 0xD05U, /*!< The high voltage operation is in progress */

    /* uSDHC specific error codes */
    STATUS_USDHC_OUT_OF_RANGE              = 0xE00U,    /*!< The size of data to be sent is larger than maximum size of ADMA table */
    STATUS_USDHC_PREPARE_ADMA_FAILED       = 0xE01U,    /*!< Failed to prepare the ADMA table */

    /* TDM specific error codes */
    STATUS_TDM_DIARY_FULL                  = 0xF01U, /*!< No empty flash left in diary region */

    /* PHY specific error codes */
    STATUS_PHY_ACCESS_FAILED               = 0x1001U, /*!< Could not access PHY registers */
    STATUS_PHY_INCOMPATIBLE_DEVICE         = 0x1002U  /*!< The selected PHY driver is not compatible with the device */
} status_t;



/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 40 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"


/*******************************************************************************
*   File Name               :   TimerHwAbs.h
*   Component Name          :   TimerHwAbs
*   UCom                    :   NXP S32K118 series
*
*   Create Date             :   2024-05-23
*   Author                  :   N.GASMI
*   Corporation             :   Inalfa Roof Systems
*
*   Abstract Description    :   public header of example
*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved
*                               Unauthorized copying of this file, via any medium
*                               is strictly prohibited
*                               Proprietary and confidential
*---Revision History------------------------------------------------------------
*   Date        Author    Item     Description
*   2024-05-23  N.GASMI  [#1234]  initial revision
*
*******************************************************************************/

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"
#line 138 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"

#line 27 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\LPTMR\\CODE\\lptmr_driver.h"
/*
 * Copyright (c) 2016, Freescale Semiconductor, Inc.
 * Copyright 2016-2020 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */




#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"
/*
** ###################################################################
**     Abstract:
**         Common include file for CMSIS register access layer headers.
**
**     Copyright (c) 2015 Freescale Semiconductor, Inc.
**     Copyright 2016-2020 NXP
**     All rights reserved.
**
**     NXP Confidential. This software is owned or controlled by NXP and may only be
**     used strictly in accordance with the applicable license terms. By expressly
**     accepting such terms or by downloading, installing, activating and/or otherwise
**     using the software, you are agreeing that you have read, and that you agree to
**     comply with and are bound by, such license terms. If you do not agree to be
**     bound by the applicable license terms, then you may not retain, install,
**     activate or otherwise use the software. The production use license in
**     Section 2.3 is expressly granted for this software.
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
** ###################################################################
*/




/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, global macro not referenced.
* The macro defines the device currently in use and may be used by components for specific checks.
*
*/

/*
 * Include the cpu specific register header files.
 *
 * The CPU macro should be declared in the project or makefile.
 */

#line 117 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"



    /* Specific core definitions */
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"
/*
 * Copyright (c) 2015-2016 Freescale Semiconductor, Inc.
 * Copyright 2016-2017 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 *
 */
/*!
 * @file s32_core_cm0.h
 *
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
 * Function-like macros are used instead of inline functions in order to ensure
 * that the performance will not be decreased if the functions will not be
 * inlined by the compiler.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Global macro not referenced.
 * The macros defined are used only on some of the drivers, so this might be reported
 * when the analysis is made only on one driver.
 */

/*
 * Tool Chains:
 *   GNUC flag is defined also by ARM compiler - it shows the current major version of the compatible GCC version
 *   __GNUC__   : GNU Compiler Collection
 *   __ghs__    : Green Hills ARM Compiler
 *   __ICCARM__ : IAR ARM Compiler
 *   __DCC__    : Wind River Diab Compiler
 *   __ARMCC_VERSION    : ARM Compiler
 */









/** \brief  BKPT_ASM
 *
 *   Macro to be used to trigger an debug interrupt
 */






/** \brief  Enable interrupts
 */
#line 70 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"

/** \brief  Disable interrupts
 */
#line 80 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"


/** \brief  Enter low-power standby state
 *    WFI (Wait For Interrupt) makes the processor suspend execution (Clock is stopped) until an IRQ interrupts.
 */
#line 92 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"

/** \brief  No-op
 */



/** \brief  Reverse byte order in a word.
 * ARM Documentation Cortex-M0 Devices Generic User Guide
 * Accordingly to 3.5.7. REV, REV16, and REVSH
 * Restriction "This function requires low registers R0-R7 register"
 */
#line 111 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"

/** \brief  Reverse byte order in each halfword independently.
 * ARM Documentation Cortex-M0 Devices Generic User Guide
 * Accordingly to 3.5.7. REV, REV16, and REVSH
 * Restriction "This function requires low registers R0-R7 register"
 */
#line 125 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"

/** \brief  Places a function in RAM.
 */
#line 148 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"
                                                   
    /* For GCC, IAR, GHS, Diab and ARMC there is no need to specify the section when
    defining a function, it is enough to specify it at the declaration. This
    also enables compatibility with software analysis tools. */



#line 162 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"

/** \brief  Get Core ID
 *
 *   GET_CORE_ID returns the processor identification number for cm0
 */


/** \brief  Data alignment.
 */
#line 181 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\s32_core_cm0.h"

/** \brief  Endianness.
 */








/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 122 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"

#line 131 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"


        /* Register definitions */
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/*
** ###################################################################
**     Processor:           S32K118
**     Reference manual:    S32K1XXRM Rev. 12.1, 02/2020
**     Version:             rev. 1.4, 2020-05-14
**     Build:               b200514
**
**     Abstract:
**         Peripheral Access Layer for S32K118
**
**     Copyright (c) 1997 - 2016 Freescale Semiconductor, Inc.
**     Copyright 2016-2020 NXP
**     All rights reserved.
**
**     NXP Confidential. This software is owned or controlled by NXP and may only be
**     used strictly in accordance with the applicable license terms. By expressly
**     accepting such terms or by downloading, installing, activating and/or otherwise
**     using the software, you are agreeing that you have read, and that you agree to
**     comply with and are bound by, such license terms. If you do not agree to be
**     bound by the applicable license terms, then you may not retain, install,
**     activate or otherwise use the software. The production use license in
**     Section 2.3 is expressly granted for this software.
**
**     http:                 www.nxp.com
**     mail:                 <EMAIL>
**
**     Revisions:
**     - rev. 1.0 (2017-12-14) - Mihai Volmer
**         Initial version based on S32K1XXRM Rev. 6, 12/2017.
**     - rev. 1.1 (2018-02-08) - Mihai Volmer
**         Renamed the NVIC register array IP to IPR to reflect the register access difference from Cortex-M4 NVIC registers
**         Fixed CSE_PRAM base address
**     - rev. 1.2 (2018-07-19) - Dan Nastasa
**         Updated the header based on S32K1XXRM Rev. 8, 06/2018.
**         Added MTB_DWT peripheral to the header file
**     - rev. 1.3 (2019-02-19) - Ionut Pavel
**         Updated the header based on S32K1XXRM Rev. 9, 09/2018.
**         Removed LMEM_LMDR2 register from the header file.
**         Modified LMEM_LMPECR register to Read-Only.
**     - rev. 1.4 (2020-05-14) - Van Nguyen Nam
**         Updated the header based on S32K1XXRM Rev. 12.1, 02/2020.
**
** ###################################################################
*/

/*!
 * @file S32K118.h
 * @version 1.4
 * @date 2020-05-14
 * @brief Peripheral Access Layer for S32K118
 *
 * This file contains register definitions and macros for easy access to their
 * bit fields.
 *
 * This file assumes LITTLE endian system.
 */

/**
* @page misra_violations MISRA-C:2012 violations
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.3, local typedef not referenced
* The SoC header defines typedef for all modules.
*
* @section [global]
* Violates MISRA 2012 Advisory Rule 2.5, local macro not referenced
* The SoC header defines macros for all modules and registers.
*
* @section [global]
* Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
* These are generated macros used for accessing the bit-fields from registers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.1, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.2, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.4, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 5.5, identifier clash
* The supported compilers use more than 31 significant characters for identifiers.
*
* @section [global]
* Violates MISRA 2012 Required Rule 21.1, defined macro '__I' is reserved to the compiler
* This type qualifier is needed to ensure correct I/O access and addressing.
*/

/* ----------------------------------------------------------------------------
   -- MCU activation
   ---------------------------------------------------------------------------- */

/* Prevention from multiple including the same memory map */





/* Check if another memory map has not been also included */







/** Memory map major version (memory maps with equal major version number are
 * compatible) */

/** Memory map minor version */


/* ----------------------------------------------------------------------------
   -- Generic macros
   ---------------------------------------------------------------------------- */

/* IO definitions (access restrictions to peripheral registers) */
/**
*   IO Type Qualifiers are used
*   \li to specify the access to peripheral variables.
*   \li for automatic generation of peripheral register debug information.
*/
#line 137 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"


/**
* @brief 32 bits memory read macro.
*/




/**
* @brief 32 bits memory write macro.
*/




/**
* @brief 32 bits bits setting macro.
*/




/**
* @brief 32 bits bits clearing macro.
*/




/**
* @brief 32 bit clear bits and set with new value
* @note It is user's responsability to make sure that value has only "mask" bits set - (value&~mask)==0
*/





/* ----------------------------------------------------------------------------
   -- Interrupt vector numbers for S32K118
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Interrupt_vector_numbers_S32K118 Interrupt vector numbers for S32K118
 * @{
 */

/** Interrupt Number Definitions */


/**
 * @brief Defines the Interrupt Numbers definitions
 *
 * This enumeration is used to configure the interrupts.
 *
 * Implements : IRQn_Type_Class
 */
typedef enum
{
  /* Auxiliary constants */
  NotAvail_IRQn                = -128,             /**< Not available device specific interrupt */

  /* Core interrupts */
  NonMaskableInt_IRQn          = -14,              /**< Non Maskable Interrupt */
  HardFault_IRQn               = -13,              /**< Cortex-M0 SV Hard Fault Interrupt */
  SVCall_IRQn                  = -5,               /**< Cortex-M0 SV Call Interrupt */
  PendSV_IRQn                  = -2,               /**< Cortex-M0 Pend SV Interrupt */
  SysTick_IRQn                 = -1,               /**< Cortex-M0 System Tick Interrupt */

  /* Device specific interrupts */
  DMA0_IRQn                    = 0u,               /**< DMA channel 0 transfer complete */
  DMA1_IRQn                    = 1u,               /**< DMA channel 1 transfer complete */
  DMA2_IRQn                    = 2u,               /**< DMA channel 2 transfer complete */
  DMA3_IRQn                    = 3u,               /**< DMA channel 3 transfer complete */
  DMA_Error_IRQn               = 4u,               /**< DMA error interrupt channels 0-3 */
  ERM_fault_IRQn               = 5u,               /**< ERM single and double bit error correction */
  RTC_IRQn                     = 6u,               /**< RTC alarm interrupt */
  RTC_Seconds_IRQn             = 7u,               /**< RTC seconds interrupt */
  LPTMR0_IRQn                  = 8u,               /**< LPTIMER interrupt request */
  PORT_IRQn                    = 9u,               /**< Port A, B, C, D and E pin detect interrupt */
  CAN0_ORed_Err_Wakeup_IRQn    = 10u,              /**< OR’ed [Bus Off OR Bus Off Done OR Transmit Warning OR Receive Warning], Interrupt indicating that errors were detected on the CAN bus, Interrupt asserted when Pretended Networking operation is enabled, and a valid message matches the selected filter criteria during Low Power mode */
  CAN0_ORed_0_31_MB_IRQn       = 11u,              /**< OR'ed Message buffer (0-15, 16-31) */
  FTM0_Ch0_7_IRQn              = 12u,              /**< FTM0 Channel 0 to 7 interrupt */
  FTM0_Fault_IRQn              = 13u,              /**< FTM0 Fault interrupt */
  FTM0_Ovf_Reload_IRQn         = 14u,              /**< FTM0 Counter overflow and Reload interrupt */
  FTM1_Ch0_7_IRQn              = 15u,              /**< FTM1 Channel 0 to 7 interrupt */
  FTM1_Fault_IRQn              = 16u,              /**< FTM1 Fault interrupt */
  FTM1_Ovf_Reload_IRQn         = 17u,              /**< FTM1 Counter overflow and Reload interrupt */
  FTFC_IRQn                    = 18u,              /**< FTFC Command complete, Read collision and Double bit fault detect */
  PDB0_IRQn                    = 19u,              /**< PDB0 interrupt */
  LPIT0_IRQn                   = 20u,              /**< LPIT interrupt */
  SCG_CMU_LVD_LVWSCG_IRQn      = 21u,              /**< PMC Low voltage detect interrupt, SCG bus interrupt request and CMU loss of range interrupt */
  WDOG_IRQn                    = 22u,              /**< WDOG interrupt request out before wdg reset out */
  RCM_IRQn                     = 23u,              /**< RCM Asynchronous Interrupt */
  LPI2C0_Master_Slave_IRQn     = 24u,              /**< LPI2C0 Master Interrupt and Slave Interrupt */
  FLEXIO_IRQn                  = 25u,              /**< FlexIO Interrupt */
  LPSPI0_IRQn                  = 26u,              /**< LPSPI0 Interrupt */
  LPSPI1_IRQn                  = 27u,              /**< LPSPI1 Interrupt */
  ADC0_IRQn                    = 28u,              /**< ADC0 interrupt request. */
  CMP0_IRQn                    = 29u,              /**< CMP0 interrupt request */
  LPUART1_RxTx_IRQn            = 30u,              /**< LPUART1 Transmit / Receive  Interrupt */
  LPUART0_RxTx_IRQn            = 31u               /**< LPUART0 Transmit / Receive Interrupt */
} IRQn_Type;

/*!
 * @}
 */ /* end of group Interrupt_vector_numbers_S32K118 */


/* ----------------------------------------------------------------------------
   -- Device Peripheral Access Layer for S32K118
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Peripheral_access_layer_S32K118 Device Peripheral Access Layer for S32K118
 * @{
 */

/* @brief This module covers memory mapped registers available on SoC */

/* ----------------------------------------------------------------------------
   -- ADC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ADC_Peripheral_Access_Layer ADC Peripheral Access Layer
 * @{
 */


/** ADC - Size of Registers Arrays */




/** ADC - Register Layout Typedef */
typedef struct {
  volatile uint32_t SC1[16u];                /**< ADC Status and Control Register 1, array offset: 0x0, array step: 0x4 */
  volatile uint32_t CFG1;                              /**< ADC Configuration Register 1, offset: 0x40 */
  volatile uint32_t CFG2;                              /**< ADC Configuration Register 2, offset: 0x44 */
  volatile const  uint32_t R[16u];                    /**< ADC Data Result Registers, array offset: 0x48, array step: 0x4 */
  volatile uint32_t CV[2u];                  /**< Compare Value Registers, array offset: 0x88, array step: 0x4 */
  volatile uint32_t SC2;                               /**< Status and Control Register 2, offset: 0x90 */
  volatile uint32_t SC3;                               /**< Status and Control Register 3, offset: 0x94 */
  volatile uint32_t BASE_OFS;                          /**< BASE Offset Register, offset: 0x98 */
  volatile uint32_t OFS;                               /**< ADC Offset Correction Register, offset: 0x9C */
  volatile uint32_t USR_OFS;                           /**< USER Offset Correction Register, offset: 0xA0 */
  volatile uint32_t XOFS;                              /**< ADC X Offset Correction Register, offset: 0xA4 */
  volatile uint32_t YOFS;                              /**< ADC Y Offset Correction Register, offset: 0xA8 */
  volatile uint32_t G;                                 /**< ADC Gain Register, offset: 0xAC */
  volatile uint32_t UG;                                /**< ADC User Gain Register, offset: 0xB0 */
  volatile uint32_t CLPS;                              /**< ADC General Calibration Value Register S, offset: 0xB4 */
  volatile uint32_t CLP3;                              /**< ADC Plus-Side General Calibration Value Register 3, offset: 0xB8 */
  volatile uint32_t CLP2;                              /**< ADC Plus-Side General Calibration Value Register 2, offset: 0xBC */
  volatile uint32_t CLP1;                              /**< ADC Plus-Side General Calibration Value Register 1, offset: 0xC0 */
  volatile uint32_t CLP0;                              /**< ADC Plus-Side General Calibration Value Register 0, offset: 0xC4 */
  volatile uint32_t CLPX;                              /**< ADC Plus-Side General Calibration Value Register X, offset: 0xC8 */
  volatile uint32_t CLP9;                              /**< ADC Plus-Side General Calibration Value Register 9, offset: 0xCC */
  volatile uint32_t CLPS_OFS;                          /**< ADC General Calibration Offset Value Register S, offset: 0xD0 */
  volatile uint32_t CLP3_OFS;                          /**< ADC Plus-Side General Calibration Offset Value Register 3, offset: 0xD4 */
  volatile uint32_t CLP2_OFS;                          /**< ADC Plus-Side General Calibration Offset Value Register 2, offset: 0xD8 */
  volatile uint32_t CLP1_OFS;                          /**< ADC Plus-Side General Calibration Offset Value Register 1, offset: 0xDC */
  volatile uint32_t CLP0_OFS;                          /**< ADC Plus-Side General Calibration Offset Value Register 0, offset: 0xE0 */
  volatile uint32_t CLPX_OFS;                          /**< ADC Plus-Side General Calibration Offset Value Register X, offset: 0xE4 */
  volatile uint32_t CLP9_OFS;                          /**< ADC Plus-Side General Calibration Offset Value Register 9, offset: 0xE8 */
} ADC_Type, *ADC_MemMapPtr;

 /** Number of instances of the ADC module. */



/* ADC - Peripheral instance base addresses */
/** Peripheral ADC0 base address */

/** Peripheral ADC0 base pointer */

/** Array initializer of ADC peripheral base addresses */

/** Array initializer of ADC peripheral base pointers */

 /** Number of interrupt vector arrays for the ADC module. */

 /** Number of interrupt channels for the ADC module. */

/** Interrupt vectors for the ADC peripheral type */


/* ----------------------------------------------------------------------------
   -- ADC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ADC_Register_Masks ADC Register Masks
 * @{
 */

/* SC1 Bit Fields */
#line 347 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CFG1 Bit Fields */
#line 364 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CFG2 Bit Fields */




/* R Bit Fields */




/* CV Bit Fields */




/* SC2 Bit Fields */
#line 420 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SC3 Bit Fields */
#line 437 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* BASE_OFS Bit Fields */




/* OFS Bit Fields */




/* USR_OFS Bit Fields */




/* XOFS Bit Fields */




/* YOFS Bit Fields */




/* G Bit Fields */




/* UG Bit Fields */




/* CLPS Bit Fields */




/* CLP3 Bit Fields */




/* CLP2 Bit Fields */




/* CLP1 Bit Fields */




/* CLP0 Bit Fields */




/* CLPX Bit Fields */




/* CLP9 Bit Fields */




/* CLPS_OFS Bit Fields */




/* CLP3_OFS Bit Fields */




/* CLP2_OFS Bit Fields */




/* CLP1_OFS Bit Fields */




/* CLP0_OFS Bit Fields */




/* CLPX_OFS Bit Fields */




/* CLP9_OFS Bit Fields */





/*!
 * @}
 */ /* end of group ADC_Register_Masks */


/*!
 * @}
 */ /* end of group ADC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- AIPS Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup AIPS_Peripheral_Access_Layer AIPS Peripheral Access Layer
 * @{
 */


/** AIPS - Size of Registers Arrays */



/** AIPS - Register Layout Typedef */
typedef struct {
  volatile uint32_t MPRA;                              /**< Master Privilege Register A, offset: 0x0 */
       uint8_t RESERVED_0[28];
  volatile uint32_t PACR[4u];             /**< Peripheral Access Control Register, array offset: 0x20, array step: 0x4 */
       uint8_t RESERVED_1[16];
  volatile uint32_t OPACR[12u];           /**< Off-Platform Peripheral Access Control Register, array offset: 0x40, array step: 0x4 */
} AIPS_Type, *AIPS_MemMapPtr;

 /** Number of instances of the AIPS module. */



/* AIPS - Peripheral instance base addresses */
/** Peripheral AIPS base address */

/** Peripheral AIPS base pointer */

/** Array initializer of AIPS peripheral base addresses */

/** Array initializer of AIPS peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- AIPS Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup AIPS_Register_Masks AIPS Register Masks
 * @{
 */

/* MPRA Bit Fields */
#line 636 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PACR Bit Fields */
#line 673 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* OPACR Bit Fields */
#line 770 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group AIPS_Register_Masks */


/*!
 * @}
 */ /* end of group AIPS_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- CAN Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CAN_Peripheral_Access_Layer CAN Peripheral Access Layer
 * @{
 */


/** CAN - Size of Registers Arrays */




/** CAN - Register Layout Typedef */
typedef struct {
  volatile uint32_t MCR;                               /**< Module Configuration Register, offset: 0x0 */
  volatile uint32_t CTRL1;                             /**< Control 1 register, offset: 0x4 */
  volatile uint32_t TIMER;                             /**< Free Running Timer, offset: 0x8 */
       uint8_t RESERVED_0[4];
  volatile uint32_t RXMGMASK;                          /**< Rx Mailboxes Global Mask Register, offset: 0x10 */
  volatile uint32_t RX14MASK;                          /**< Rx 14 Mask register, offset: 0x14 */
  volatile uint32_t RX15MASK;                          /**< Rx 15 Mask register, offset: 0x18 */
  volatile uint32_t ECR;                               /**< Error Counter, offset: 0x1C */
  volatile uint32_t ESR1;                              /**< Error and Status 1 register, offset: 0x20 */
       uint8_t RESERVED_1[4];
  volatile uint32_t IMASK1;                            /**< Interrupt Masks 1 register, offset: 0x28 */
       uint8_t RESERVED_2[4];
  volatile uint32_t IFLAG1;                            /**< Interrupt Flags 1 register, offset: 0x30 */
  volatile uint32_t CTRL2;                             /**< Control 2 register, offset: 0x34 */
  volatile const  uint32_t ESR2;                              /**< Error and Status 2 register, offset: 0x38 */
       uint8_t RESERVED_3[8];
  volatile const  uint32_t CRCR;                              /**< CRC Register, offset: 0x44 */
  volatile uint32_t RXFGMASK;                          /**< Rx FIFO Global Mask register, offset: 0x48 */
  volatile const  uint32_t RXFIR;                             /**< Rx FIFO Information Register, offset: 0x4C */
  volatile uint32_t CBT;                               /**< CAN Bit Timing Register, offset: 0x50 */
       uint8_t RESERVED_4[44];
  volatile uint32_t RAMn[128u];              /**< Embedded RAM, array offset: 0x80, array step: 0x4 */
       uint8_t RESERVED_5[1536];
  volatile uint32_t RXIMR[32u];            /**< Rx Individual Mask Registers, array offset: 0x880, array step: 0x4 */
       uint8_t RESERVED_6[512];
  volatile uint32_t CTRL1_PN;                          /**< Pretended Networking Control 1 Register, offset: 0xB00 */
  volatile uint32_t CTRL2_PN;                          /**< Pretended Networking Control 2 Register, offset: 0xB04 */
  volatile uint32_t WU_MTC;                            /**< Pretended Networking Wake Up Match Register, offset: 0xB08 */
  volatile uint32_t FLT_ID1;                           /**< Pretended Networking ID Filter 1 Register, offset: 0xB0C */
  volatile uint32_t FLT_DLC;                           /**< Pretended Networking DLC Filter Register, offset: 0xB10 */
  volatile uint32_t PL1_LO;                            /**< Pretended Networking Payload Low Filter 1 Register, offset: 0xB14 */
  volatile uint32_t PL1_HI;                            /**< Pretended Networking Payload High Filter 1 Register, offset: 0xB18 */
  volatile uint32_t FLT_ID2_IDMASK;                    /**< Pretended Networking ID Filter 2 Register / ID Mask Register, offset: 0xB1C */
  volatile uint32_t PL2_PLMASK_LO;                     /**< Pretended Networking Payload Low Filter 2 Register / Payload Low Mask Register, offset: 0xB20 */
  volatile uint32_t PL2_PLMASK_HI;                     /**< Pretended Networking Payload High Filter 2 low order bits / Payload High Mask Register, offset: 0xB24 */
       uint8_t RESERVED_7[24];
  struct {                                         /* offset: 0xB40, array step: 0x10 */
    volatile const  uint32_t WMBn_CS;                           /**< Wake Up Message Buffer Register for C/S, array offset: 0xB40, array step: 0x10 */
    volatile const  uint32_t WMBn_ID;                           /**< Wake Up Message Buffer Register for ID, array offset: 0xB44, array step: 0x10 */
    volatile const  uint32_t WMBn_D03;                          /**< Wake Up Message Buffer Register for Data 0-3, array offset: 0xB48, array step: 0x10 */
    volatile const  uint32_t WMBn_D47;                          /**< Wake Up Message Buffer Register Data 4-7, array offset: 0xB4C, array step: 0x10 */
  } WMB[4u];
       uint8_t RESERVED_8[128];
  volatile uint32_t FDCTRL;                            /**< CAN FD Control Register, offset: 0xC00 */
  volatile uint32_t FDCBT;                             /**< CAN FD Bit Timing Register, offset: 0xC04 */
  volatile const  uint32_t FDCRC;                             /**< CAN FD CRC Register, offset: 0xC08 */
} CAN_Type, *CAN_MemMapPtr;

 /** Number of instances of the CAN module. */



/* CAN - Peripheral instance base addresses */
/** Peripheral CAN0 base address */

/** Peripheral CAN0 base pointer */

/** Array initializer of CAN peripheral base addresses */

/** Array initializer of CAN peripheral base pointers */

 /** Number of interrupt vector arrays for the CAN module. */

 /** Number of interrupt channels for the Rx_Warning type of CAN module. */

 /** Number of interrupt channels for the Tx_Warning type of CAN module. */

 /** Number of interrupt channels for the Wake_Up type of CAN module. */

 /** Number of interrupt channels for the Error type of CAN module. */

 /** Number of interrupt channels for the Bus_Off type of CAN module. */

 /** Number of interrupt channels for the ORed_0_15_MB type of CAN module. */

 /** Number of interrupt channels for the ORed_16_31_MB type of CAN module. */

/** Interrupt vectors for the CAN peripheral type */
#line 883 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/* ----------------------------------------------------------------------------
   -- CAN Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CAN_Register_Masks CAN Register Masks
 * @{
 */

/* MCR Bit Fields */
#line 970 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CTRL1 Bit Fields */
#line 1035 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TIMER Bit Fields */




/* RXMGMASK Bit Fields */




/* RX14MASK Bit Fields */




/* RX15MASK Bit Fields */




/* ECR Bit Fields */
#line 1072 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ESR1 Bit Fields */
#line 1173 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* IMASK1 Bit Fields */




/* IFLAG1 Bit Fields */
#line 1203 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CTRL2 Bit Fields */
#line 1248 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ESR2 Bit Fields */
#line 1261 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CRCR Bit Fields */
#line 1270 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RXFGMASK Bit Fields */




/* RXFIR Bit Fields */




/* CBT Bit Fields */
#line 1305 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RAMn Bit Fields */
#line 1322 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RXIMR Bit Fields */




/* CTRL1_PN Bit Fields */
#line 1352 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CTRL2_PN Bit Fields */




/* WU_MTC Bit Fields */
#line 1370 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FLT_ID1 Bit Fields */
#line 1383 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FLT_DLC Bit Fields */
#line 1392 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PL1_LO Bit Fields */
#line 1409 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PL1_HI Bit Fields */
#line 1426 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FLT_ID2_IDMASK Bit Fields */
#line 1439 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PL2_PLMASK_LO Bit Fields */
#line 1456 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PL2_PLMASK_HI Bit Fields */
#line 1473 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* WMBn_CS Bit Fields */
#line 1490 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* WMBn_ID Bit Fields */




/* WMBn_D03 Bit Fields */
#line 1512 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* WMBn_D47 Bit Fields */
#line 1529 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FDCTRL Bit Fields */
#line 1554 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FDCBT Bit Fields */
#line 1575 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FDCRC Bit Fields */
#line 1584 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group CAN_Register_Masks */


/*!
 * @}
 */ /* end of group CAN_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- CMP Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CMP_Peripheral_Access_Layer CMP Peripheral Access Layer
 * @{
 */


/** CMP - Size of Registers Arrays */

/** CMP - Register Layout Typedef */
typedef struct {
  volatile uint32_t C0;                                /**< CMP Control Register 0, offset: 0x0 */
  volatile uint32_t C1;                                /**< CMP Control Register 1, offset: 0x4 */
  volatile uint32_t C2;                                /**< CMP Control Register 2, offset: 0x8 */
} CMP_Type, *CMP_MemMapPtr;

 /** Number of instances of the CMP module. */



/* CMP - Peripheral instance base addresses */
/** Peripheral CMP0 base address */

/** Peripheral CMP0 base pointer */

/** Array initializer of CMP peripheral base addresses */

/** Array initializer of CMP peripheral base pointers */

 /** Number of interrupt vector arrays for the CMP module. */

 /** Number of interrupt channels for the CMP module. */

/** Interrupt vectors for the CMP peripheral type */


/* ----------------------------------------------------------------------------
   -- CMP Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CMP_Register_Masks CMP Register Masks
 * @{
 */

/* C0 Bit Fields */
#line 1712 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* C1 Bit Fields */
#line 1773 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* C2 Bit Fields */
#line 1834 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group CMP_Register_Masks */


/*!
 * @}
 */ /* end of group CMP_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- CMU_FC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CMU_FC_Peripheral_Access_Layer CMU_FC Peripheral Access Layer
 * @{
 */


/** CMU_FC - Size of Registers Arrays */

/** CMU_FC - Register Layout Typedef */
typedef struct {
  volatile uint32_t GCR;                               /**< CMU Frequency Check Global Configuration Register, offset: 0x0 */
  volatile uint32_t RCCR;                              /**< CMU Frequency Check Reference Count Configuration Register, offset: 0x4 */
  volatile uint32_t HTCR;                              /**< CMU Frequency Check High Threshold Configuration Register, offset: 0x8 */
  volatile uint32_t LTCR;                              /**< CMU Frequency Check Low Threshold Configuration Register, offset: 0xC */
  volatile uint32_t SR;                                /**< CMU Frequency Check Status Register, offset: 0x10 */
  volatile uint32_t IER;                               /**< CMU Frequency Check Interrupt/Event Enable Register, offset: 0x14 */
} CMU_FC_Type, *CMU_FC_MemMapPtr;

 /** Number of instances of the CMU_FC module. */



/* CMU_FC - Peripheral instance base addresses */
/** Peripheral CMU_FC_0 base address */

/** Peripheral CMU_FC_0 base pointer */

/** Peripheral CMU_FC_1 base address */

/** Peripheral CMU_FC_1 base pointer */

/** Array initializer of CMU_FC peripheral base addresses */

/** Array initializer of CMU_FC peripheral base pointers */

 /** Number of interrupt vector arrays for the CMU_FC module. */

 /** Number of interrupt channels for the CMU_FC module. */

/** Interrupt vectors for the CMU_FC peripheral type */


/* ----------------------------------------------------------------------------
   -- CMU_FC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CMU_FC_Register_Masks CMU_FC Register Masks
 * @{
 */

/* GCR Bit Fields */




/* RCCR Bit Fields */




/* HTCR Bit Fields */




/* LTCR Bit Fields */




/* SR Bit Fields */
#line 1937 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* IER Bit Fields */
#line 1954 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group CMU_FC_Register_Masks */


/*!
 * @}
 */ /* end of group CMU_FC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- CRC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CRC_Peripheral_Access_Layer CRC Peripheral Access Layer
 * @{
 */


/** CRC - Size of Registers Arrays */

/** CRC - Register Layout Typedef */
typedef struct {
  union {                                          /* offset: 0x0 */
    volatile uint32_t DATA;                              /**< CRC Data register, offset: 0x0 */
    struct {                                         /* offset: 0x0 */
      volatile uint16_t L;                                 /**< CRC_DATAL register., offset: 0x0 */
      volatile uint16_t H;                                 /**< CRC_DATAH register., offset: 0x2 */
    } DATA_16;
    struct {                                         /* offset: 0x0 */
      volatile uint8_t LL;                                 /**< CRC_DATALL register., offset: 0x0 */
      volatile uint8_t LU;                                 /**< CRC_DATALU register., offset: 0x1 */
      volatile uint8_t HL;                                 /**< CRC_DATAHL register., offset: 0x2 */
      volatile uint8_t HU;                                 /**< CRC_DATAHU register., offset: 0x3 */
    } DATA_8;
  } DATAu;
  volatile uint32_t GPOLY;                             /**< CRC Polynomial register, offset: 0x4 */
  volatile uint32_t CTRL;                              /**< CRC Control register, offset: 0x8 */
} CRC_Type, *CRC_MemMapPtr;

 /** Number of instances of the CRC module. */



/* CRC - Peripheral instance base addresses */
/** Peripheral CRC base address */

/** Peripheral CRC base pointer */

/** Array initializer of CRC peripheral base addresses */

/** Array initializer of CRC peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- CRC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CRC_Register_Masks CRC Register Masks
 * @{
 */

/* DATAu_DATA Bit Fields */
#line 2036 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DATAu_DATA_16_L Bit Fields */




/* DATAu_DATA_16_H Bit Fields */




/* DATAu_DATA_8_LL Bit Fields */




/* DATAu_DATA_8_LU Bit Fields */




/* DATAu_DATA_8_HL Bit Fields */




/* DATAu_DATA_8_HU Bit Fields */




/* GPOLY Bit Fields */
#line 2075 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CTRL Bit Fields */
#line 2096 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group CRC_Register_Masks */


/*!
 * @}
 */ /* end of group CRC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- CSE_PRAM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CSE_PRAM_Peripheral_Access_Layer CSE_PRAM Peripheral Access Layer
 * @{
 */


/** CSE_PRAM - Size of Registers Arrays */


/** CSE_PRAM - Register Layout Typedef */
typedef struct {
  union {                                          /* offset: 0x0, array step: 0x4 */
    volatile uint32_t DATA_32;                           /**< CSE PRAM 0 Register..CSE PRAM 31 Register, array offset: 0x0, array step: 0x4 */
    struct {                                         /* offset: 0x0, array step: 0x4 */
      volatile uint8_t DATA_8LL;                           /**< CSE PRAM0LL register...CSE PRAM31LL register., array offset: 0x0, array step: 0x4 */
      volatile uint8_t DATA_8LU;                           /**< CSE PRAM0LU register...CSE PRAM31LU register., array offset: 0x1, array step: 0x4 */
      volatile uint8_t DATA_8HL;                           /**< CSE PRAM0HL register...CSE PRAM31HL register., array offset: 0x2, array step: 0x4 */
      volatile uint8_t DATA_8HU;                           /**< CSE PRAM0HU register...CSE PRAM31HU register., array offset: 0x3, array step: 0x4 */
    } ACCESS8BIT;
  } RAMn[32u];
} CSE_PRAM_Type, *CSE_PRAM_MemMapPtr;

 /** Number of instances of the CSE_PRAM module. */



/* CSE_PRAM - Peripheral instance base addresses */
/** Peripheral CSE_PRAM base address */

/** Peripheral CSE_PRAM base pointer */

/** Array initializer of CSE_PRAM peripheral base addresses */

/** Array initializer of CSE_PRAM peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- CSE_PRAM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup CSE_PRAM_Register_Masks CSE_PRAM Register Masks
 * @{
 */

/* RAMn_DATA_32 Bit Fields */
#line 2173 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RAMn_ACCESS8BIT_DATA_8LL Bit Fields */




/* RAMn_ACCESS8BIT_DATA_8LU Bit Fields */




/* RAMn_ACCESS8BIT_DATA_8HL Bit Fields */




/* RAMn_ACCESS8BIT_DATA_8HU Bit Fields */





/*!
 * @}
 */ /* end of group CSE_PRAM_Register_Masks */


/*!
 * @}
 */ /* end of group CSE_PRAM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- DMA Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMA_Peripheral_Access_Layer DMA Peripheral Access Layer
 * @{
 */


/** DMA - Size of Registers Arrays */



/** DMA - Register Layout Typedef */
typedef struct {
  volatile uint32_t CR;                                /**< Control Register, offset: 0x0 */
  volatile const  uint32_t ES;                                /**< Error Status Register, offset: 0x4 */
       uint8_t RESERVED_0[4];
  volatile uint32_t ERQ;                               /**< Enable Request Register, offset: 0xC */
       uint8_t RESERVED_1[4];
  volatile uint32_t EEI;                               /**< Enable Error Interrupt Register, offset: 0x14 */
  volatile  uint8_t CEEI;                               /**< Clear Enable Error Interrupt Register, offset: 0x18 */
  volatile  uint8_t SEEI;                               /**< Set Enable Error Interrupt Register, offset: 0x19 */
  volatile  uint8_t CERQ;                               /**< Clear Enable Request Register, offset: 0x1A */
  volatile  uint8_t SERQ;                               /**< Set Enable Request Register, offset: 0x1B */
  volatile  uint8_t CDNE;                               /**< Clear DONE Status Bit Register, offset: 0x1C */
  volatile  uint8_t SSRT;                               /**< Set START Bit Register, offset: 0x1D */
  volatile  uint8_t CERR;                               /**< Clear Error Register, offset: 0x1E */
  volatile  uint8_t CINT;                               /**< Clear Interrupt Request Register, offset: 0x1F */
       uint8_t RESERVED_2[4];
  volatile uint32_t INT;                               /**< Interrupt Request Register, offset: 0x24 */
       uint8_t RESERVED_3[4];
  volatile uint32_t ERR;                               /**< Error Register, offset: 0x2C */
       uint8_t RESERVED_4[4];
  volatile const  uint32_t HRS;                               /**< Hardware Request Status Register, offset: 0x34 */
       uint8_t RESERVED_5[12];
  volatile uint32_t EARS;                              /**< Enable Asynchronous Request in Stop Register, offset: 0x44 */
       uint8_t RESERVED_6[184];
  volatile uint8_t DCHPRI[4u];           /**< Channel n Priority Register, array offset: 0x100, array step: 0x1 */
       uint8_t RESERVED_7[3836];
  struct {                                         /* offset: 0x1000, array step: 0x20 */
    volatile uint32_t SADDR;                             /**< TCD Source Address, array offset: 0x1000, array step: 0x20 */
    volatile uint16_t SOFF;                              /**< TCD Signed Source Address Offset, array offset: 0x1004, array step: 0x20 */
    volatile uint16_t ATTR;                              /**< TCD Transfer Attributes, array offset: 0x1006, array step: 0x20 */
    union {                                          /* offset: 0x1008, array step: 0x20 */
      volatile uint32_t MLNO;                              /**< TCD Minor Byte Count (Minor Loop Mapping Disabled), array offset: 0x1008, array step: 0x20 */
      volatile uint32_t MLOFFNO;                           /**< TCD Signed Minor Loop Offset (Minor Loop Mapping Enabled and Offset Disabled), array offset: 0x1008, array step: 0x20 */
      volatile uint32_t MLOFFYES;                          /**< TCD Signed Minor Loop Offset (Minor Loop Mapping and Offset Enabled), array offset: 0x1008, array step: 0x20 */
    } NBYTES;
    volatile uint32_t SLAST;                             /**< TCD Last Source Address Adjustment, array offset: 0x100C, array step: 0x20 */
    volatile uint32_t DADDR;                             /**< TCD Destination Address, array offset: 0x1010, array step: 0x20 */
    volatile uint16_t DOFF;                              /**< TCD Signed Destination Address Offset, array offset: 0x1014, array step: 0x20 */
    union {                                          /* offset: 0x1016, array step: 0x20 */
      volatile uint16_t ELINKNO;                           /**< TCD Current Minor Loop Link, Major Loop Count (Channel Linking Disabled), array offset: 0x1016, array step: 0x20 */
      volatile uint16_t ELINKYES;                          /**< TCD Current Minor Loop Link, Major Loop Count (Channel Linking Enabled), array offset: 0x1016, array step: 0x20 */
    } CITER;
    volatile uint32_t DLASTSGA;                          /**< TCD Last Destination Address Adjustment/Scatter Gather Address, array offset: 0x1018, array step: 0x20 */
    volatile uint16_t CSR;                               /**< TCD Control and Status, array offset: 0x101C, array step: 0x20 */
    union {                                          /* offset: 0x101E, array step: 0x20 */
      volatile uint16_t ELINKNO;                           /**< TCD Beginning Minor Loop Link, Major Loop Count (Channel Linking Disabled), array offset: 0x101E, array step: 0x20 */
      volatile uint16_t ELINKYES;                          /**< TCD Beginning Minor Loop Link, Major Loop Count (Channel Linking Enabled), array offset: 0x101E, array step: 0x20 */
    } BITER;
  } TCD[4u];
} DMA_Type, *DMA_MemMapPtr;

 /** Number of instances of the DMA module. */



/* DMA - Peripheral instance base addresses */
/** Peripheral DMA base address */

/** Peripheral DMA base pointer */

/** Array initializer of DMA peripheral base addresses */

/** Array initializer of DMA peripheral base pointers */

 /** Number of interrupt vector arrays for the DMA module. */

 /** Number of interrupt channels for the CHN type of DMA module. */

 /** Number of interrupt channels for the ERROR type of DMA module. */

/** Interrupt vectors for the DMA peripheral type */



/* ----------------------------------------------------------------------------
   -- DMA Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMA_Register_Masks DMA Register Masks
 * @{
 */

/* CR Bit Fields */
#line 2339 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ES Bit Fields */
#line 2388 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ERQ Bit Fields */
#line 2453 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* EEI Bit Fields */
#line 2518 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CEEI Bit Fields */
#line 2531 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SEEI Bit Fields */
#line 2544 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CERQ Bit Fields */
#line 2557 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SERQ Bit Fields */
#line 2570 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CDNE Bit Fields */
#line 2583 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SSRT Bit Fields */
#line 2596 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CERR Bit Fields */
#line 2609 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CINT Bit Fields */
#line 2622 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* INT Bit Fields */
#line 2687 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ERR Bit Fields */
#line 2752 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* HRS Bit Fields */
#line 2817 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* EARS Bit Fields */
#line 2882 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DCHPRI Bit Fields */
#line 2895 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_SADDR Bit Fields */




/* TCD_SOFF Bit Fields */




/* TCD_ATTR Bit Fields */
#line 2922 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_NBYTES_MLNO Bit Fields */




/* TCD_NBYTES_MLOFFNO Bit Fields */
#line 2940 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_NBYTES_MLOFFYES Bit Fields */
#line 2957 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_SLAST Bit Fields */




/* TCD_DADDR Bit Fields */




/* TCD_DOFF Bit Fields */




/* TCD_CITER_ELINKNO Bit Fields */
#line 2981 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_CITER_ELINKYES Bit Fields */
#line 2994 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_DLASTSGA Bit Fields */




/* TCD_CSR Bit Fields */
#line 3040 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_BITER_ELINKNO Bit Fields */
#line 3049 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCD_BITER_ELINKYES Bit Fields */
#line 3062 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group DMA_Register_Masks */


/*!
 * @}
 */ /* end of group DMA_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- DMAMUX Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMAMUX_Peripheral_Access_Layer DMAMUX Peripheral Access Layer
 * @{
 */


/** DMAMUX - Size of Registers Arrays */


/** DMAMUX - Register Layout Typedef */
typedef struct {
  volatile uint8_t CHCFG[4u];          /**< Channel Configuration register, array offset: 0x0, array step: 0x1 */
} DMAMUX_Type, *DMAMUX_MemMapPtr;

 /** Number of instances of the DMAMUX module. */



/* DMAMUX - Peripheral instance base addresses */
/** Peripheral DMAMUX base address */

/** Peripheral DMAMUX base pointer */

/** Array initializer of DMAMUX peripheral base addresses */

/** Array initializer of DMAMUX peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- DMAMUX Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup DMAMUX_Register_Masks DMAMUX Register Masks
 * @{
 */

/* CHCFG Bit Fields */
#line 3127 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group DMAMUX_Register_Masks */


/*!
 * @}
 */ /* end of group DMAMUX_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- EIM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup EIM_Peripheral_Access_Layer EIM Peripheral Access Layer
 * @{
 */


/** EIM - Size of Registers Arrays */


/** EIM - Register Layout Typedef */
typedef struct {
  volatile uint32_t EIMCR;                             /**< Error Injection Module Configuration Register, offset: 0x0 */
  volatile uint32_t EICHEN;                            /**< Error Injection Channel Enable register, offset: 0x4 */
       uint8_t RESERVED_0[248];
  struct {                                         /* offset: 0x100, array step: 0x8 */
    volatile uint32_t WORD0;                             /**< Error Injection Channel Descriptor n, Word0, array offset: 0x100, array step: 0x8 */
    volatile uint32_t WORD1;                             /**< Error Injection Channel Descriptor n, Word1, array offset: 0x104, array step: 0x8 */
  } EICHDn[1u];
} EIM_Type, *EIM_MemMapPtr;

 /** Number of instances of the EIM module. */



/* EIM - Peripheral instance base addresses */
/** Peripheral EIM base address */

/** Peripheral EIM base pointer */

/** Array initializer of EIM peripheral base addresses */

/** Array initializer of EIM peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- EIM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup EIM_Register_Masks EIM Register Masks
 * @{
 */

/* EIMCR Bit Fields */




/* EICHEN Bit Fields */




/* EICHDn_WORD0 Bit Fields */




/* EICHDn_WORD1 Bit Fields */





/*!
 * @}
 */ /* end of group EIM_Register_Masks */


/*!
 * @}
 */ /* end of group EIM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- ERM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ERM_Peripheral_Access_Layer ERM Peripheral Access Layer
 * @{
 */


/** ERM - Size of Registers Arrays */


/** ERM - Register Layout Typedef */
typedef struct {
  volatile uint32_t CR0;                               /**< ERM Configuration Register 0, offset: 0x0 */
       uint8_t RESERVED_0[12];
  volatile uint32_t SR0;                               /**< ERM Status Register 0, offset: 0x10 */
       uint8_t RESERVED_1[236];
  volatile const  uint32_t EARn[1u];              /**< ERM Memory n Error Address Register, array offset: 0x100, array step: 0x4 */
} ERM_Type, *ERM_MemMapPtr;

 /** Number of instances of the ERM module. */



/* ERM - Peripheral instance base addresses */
/** Peripheral ERM base address */

/** Peripheral ERM base pointer */

/** Array initializer of ERM peripheral base addresses */

/** Array initializer of ERM peripheral base pointers */

 /** Number of interrupt vector arrays for the ERM module. */

 /** Number of interrupt channels for the SINGLE type of ERM module. */

 /** Number of interrupt channels for the DOUBLE type of ERM module. */

/** Interrupt vectors for the ERM peripheral type */



/* ----------------------------------------------------------------------------
   -- ERM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup ERM_Register_Masks ERM Register Masks
 * @{
 */

/* CR0 Bit Fields */
#line 3279 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SR0 Bit Fields */
#line 3288 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* EARn Bit Fields */





/*!
 * @}
 */ /* end of group ERM_Register_Masks */


/*!
 * @}
 */ /* end of group ERM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- FLEXIO Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FLEXIO_Peripheral_Access_Layer FLEXIO Peripheral Access Layer
 * @{
 */


/** FLEXIO - Size of Registers Arrays */
#line 3324 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/** FLEXIO - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< Parameter Register, offset: 0x4 */
  volatile uint32_t CTRL;                              /**< FlexIO Control Register, offset: 0x8 */
  volatile const  uint32_t PIN;                               /**< Pin State Register, offset: 0xC */
  volatile uint32_t SHIFTSTAT;                         /**< Shifter Status Register, offset: 0x10 */
  volatile uint32_t SHIFTERR;                          /**< Shifter Error Register, offset: 0x14 */
  volatile uint32_t TIMSTAT;                           /**< Timer Status Register, offset: 0x18 */
       uint8_t RESERVED_0[4];
  volatile uint32_t SHIFTSIEN;                         /**< Shifter Status Interrupt Enable, offset: 0x20 */
  volatile uint32_t SHIFTEIEN;                         /**< Shifter Error Interrupt Enable, offset: 0x24 */
  volatile uint32_t TIMIEN;                            /**< Timer Interrupt Enable Register, offset: 0x28 */
       uint8_t RESERVED_1[4];
  volatile uint32_t SHIFTSDEN;                         /**< Shifter Status DMA Enable, offset: 0x30 */
       uint8_t RESERVED_2[76];
  volatile uint32_t SHIFTCTL[4u];   /**< Shifter Control N Register, array offset: 0x80, array step: 0x4 */
       uint8_t RESERVED_3[112];
  volatile uint32_t SHIFTCFG[4u];   /**< Shifter Configuration N Register, array offset: 0x100, array step: 0x4 */
       uint8_t RESERVED_4[240];
  volatile uint32_t SHIFTBUF[4u];   /**< Shifter Buffer N Register, array offset: 0x200, array step: 0x4 */
       uint8_t RESERVED_5[112];
  volatile uint32_t SHIFTBUFBIS[4u]; /**< Shifter Buffer N Bit Swapped Register, array offset: 0x280, array step: 0x4 */
       uint8_t RESERVED_6[112];
  volatile uint32_t SHIFTBUFBYS[4u]; /**< Shifter Buffer N Byte Swapped Register, array offset: 0x300, array step: 0x4 */
       uint8_t RESERVED_7[112];
  volatile uint32_t SHIFTBUFBBS[4u]; /**< Shifter Buffer N Bit Byte Swapped Register, array offset: 0x380, array step: 0x4 */
       uint8_t RESERVED_8[112];
  volatile uint32_t TIMCTL[4u];       /**< Timer Control N Register, array offset: 0x400, array step: 0x4 */
       uint8_t RESERVED_9[112];
  volatile uint32_t TIMCFG[4u];       /**< Timer Configuration N Register, array offset: 0x480, array step: 0x4 */
       uint8_t RESERVED_10[112];
  volatile uint32_t TIMCMP[4u];       /**< Timer Compare N Register, array offset: 0x500, array step: 0x4 */
} FLEXIO_Type, *FLEXIO_MemMapPtr;

 /** Number of instances of the FLEXIO module. */



/* FLEXIO - Peripheral instance base addresses */
/** Peripheral FLEXIO base address */

/** Peripheral FLEXIO base pointer */

/** Array initializer of FLEXIO peripheral base addresses */

/** Array initializer of FLEXIO peripheral base pointers */

 /** Number of interrupt vector arrays for the FLEXIO module. */

 /** Number of interrupt channels for the FLEXIO module. */

/** Interrupt vectors for the FLEXIO peripheral type */


/* ----------------------------------------------------------------------------
   -- FLEXIO Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FLEXIO_Register_Masks FLEXIO Register Masks
 * @{
 */

/* VERID Bit Fields */
#line 3402 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PARAM Bit Fields */
#line 3419 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CTRL Bit Fields */
#line 3440 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PIN Bit Fields */




/* SHIFTSTAT Bit Fields */




/* SHIFTERR Bit Fields */




/* TIMSTAT Bit Fields */




/* SHIFTSIEN Bit Fields */




/* SHIFTEIEN Bit Fields */




/* TIMIEN Bit Fields */




/* SHIFTSDEN Bit Fields */




/* SHIFTCTL Bit Fields */
#line 3505 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SHIFTCFG Bit Fields */
#line 3518 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SHIFTBUF Bit Fields */




/* SHIFTBUFBIS Bit Fields */




/* SHIFTBUFBYS Bit Fields */




/* SHIFTBUFBBS Bit Fields */




/* TIMCTL Bit Fields */
#line 3567 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TIMCFG Bit Fields */
#line 3596 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TIMCMP Bit Fields */





/*!
 * @}
 */ /* end of group FLEXIO_Register_Masks */


/*!
 * @}
 */ /* end of group FLEXIO_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- FTFC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FTFC_Peripheral_Access_Layer FTFC Peripheral Access Layer
 * @{
 */


/** FTFC - Size of Registers Arrays */



/** FTFC - Register Layout Typedef */
typedef struct {
  volatile uint8_t FSTAT;                              /**< Flash Status Register, offset: 0x0 */
  volatile uint8_t FCNFG;                              /**< Flash Configuration Register, offset: 0x1 */
  volatile const  uint8_t FSEC;                               /**< Flash Security Register, offset: 0x2 */
  volatile const  uint8_t FOPT;                               /**< Flash Option Register, offset: 0x3 */
  volatile uint8_t FCCOB[12u];            /**< Flash Common Command Object Registers, array offset: 0x4, array step: 0x1 */
  volatile uint8_t FPROT[4u];            /**< Program Flash Protection Registers, array offset: 0x10, array step: 0x1 */
       uint8_t RESERVED_0[2];
  volatile uint8_t FEPROT;                             /**< EEPROM Protection Register, offset: 0x16 */
  volatile uint8_t FDPROT;                             /**< Data Flash Protection Register, offset: 0x17 */
       uint8_t RESERVED_1[20];
  volatile const  uint8_t FCSESTAT;                           /**< Flash CSEc Status Register, offset: 0x2C */
       uint8_t RESERVED_2[1];
  volatile uint8_t FERSTAT;                            /**< Flash Error Status Register, offset: 0x2E */
  volatile uint8_t FERCNFG;                            /**< Flash Error Configuration Register, offset: 0x2F */
} FTFC_Type, *FTFC_MemMapPtr;

 /** Number of instances of the FTFC module. */



/* FTFC - Peripheral instance base addresses */
/** Peripheral FTFC base address */

/** Peripheral FTFC base pointer */

/** Array initializer of FTFC peripheral base addresses */

/** Array initializer of FTFC peripheral base pointers */

 /** Number of interrupt vector arrays for the FTFC module. */

 /** Number of interrupt channels for the COMMAND_COMPLETE type of FTFC module. */

 /** Number of interrupt channels for the READ_COLLISION type of FTFC module. */

/** Interrupt vectors for the FTFC peripheral type */



/* ----------------------------------------------------------------------------
   -- FTFC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FTFC_Register_Masks FTFC Register Masks
 * @{
 */

/* FSTAT Bit Fields */
#line 3697 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FCNFG Bit Fields */
#line 3722 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FSEC Bit Fields */
#line 3739 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FOPT Bit Fields */




/* FCCOB Bit Fields */




/* FPROT Bit Fields */




/* FEPROT Bit Fields */




/* FDPROT Bit Fields */




/* FCSESTAT Bit Fields */
#line 3797 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FERSTAT Bit Fields */




/* FERCNFG Bit Fields */
#line 3811 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group FTFC_Register_Masks */


/*!
 * @}
 */ /* end of group FTFC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- FTM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FTM_Peripheral_Access_Layer FTM Peripheral Access Layer
 * @{
 */


/** FTM - Size of Registers Arrays */



/** FTM - Register Layout Typedef */
typedef struct {
  volatile uint32_t SC;                                /**< Status And Control, offset: 0x0 */
  volatile uint32_t CNT;                               /**< Counter, offset: 0x4 */
  volatile uint32_t MOD;                               /**< Modulo, offset: 0x8 */
  struct {                                         /* offset: 0xC, array step: 0x8 */
    volatile uint32_t CnSC;                              /**< Channel (n) Status And Control, array offset: 0xC, array step: 0x8 */
    volatile uint32_t CnV;                               /**< Channel (n) Value, array offset: 0x10, array step: 0x8 */
  } CONTROLS[8u];
  volatile uint32_t CNTIN;                             /**< Counter Initial Value, offset: 0x4C */
  volatile uint32_t STATUS;                            /**< Capture And Compare Status, offset: 0x50 */
  volatile uint32_t MODE;                              /**< Features Mode Selection, offset: 0x54 */
  volatile uint32_t SYNC;                              /**< Synchronization, offset: 0x58 */
  volatile uint32_t OUTINIT;                           /**< Initial State For Channels Output, offset: 0x5C */
  volatile uint32_t OUTMASK;                           /**< Output Mask, offset: 0x60 */
  volatile uint32_t COMBINE;                           /**< Function For Linked Channels, offset: 0x64 */
  volatile uint32_t DEADTIME;                          /**< Deadtime Configuration, offset: 0x68 */
  volatile uint32_t EXTTRIG;                           /**< FTM External Trigger, offset: 0x6C */
  volatile uint32_t POL;                               /**< Channels Polarity, offset: 0x70 */
  volatile uint32_t FMS;                               /**< Fault Mode Status, offset: 0x74 */
  volatile uint32_t FILTER;                            /**< Input Capture Filter Control, offset: 0x78 */
  volatile uint32_t FLTCTRL;                           /**< Fault Control, offset: 0x7C */
  volatile uint32_t QDCTRL;                            /**< Quadrature Decoder Control And Status, offset: 0x80 */
  volatile uint32_t CONF;                              /**< Configuration, offset: 0x84 */
  volatile uint32_t FLTPOL;                            /**< FTM Fault Input Polarity, offset: 0x88 */
  volatile uint32_t SYNCONF;                           /**< Synchronization Configuration, offset: 0x8C */
  volatile uint32_t INVCTRL;                           /**< FTM Inverting Control, offset: 0x90 */
  volatile uint32_t SWOCTRL;                           /**< FTM Software Output Control, offset: 0x94 */
  volatile uint32_t PWMLOAD;                           /**< FTM PWM Load, offset: 0x98 */
  volatile uint32_t HCR;                               /**< Half Cycle Register, offset: 0x9C */
  volatile uint32_t PAIR0DEADTIME;                     /**< Pair 0 Deadtime Configuration, offset: 0xA0 */
       uint8_t RESERVED_0[4];
  volatile uint32_t PAIR1DEADTIME;                     /**< Pair 1 Deadtime Configuration, offset: 0xA8 */
       uint8_t RESERVED_1[4];
  volatile uint32_t PAIR2DEADTIME;                     /**< Pair 2 Deadtime Configuration, offset: 0xB0 */
       uint8_t RESERVED_2[4];
  volatile uint32_t PAIR3DEADTIME;                     /**< Pair 3 Deadtime Configuration, offset: 0xB8 */
       uint8_t RESERVED_3[324];
  volatile uint32_t MOD_MIRROR;                        /**< Mirror of Modulo Value, offset: 0x200 */
  volatile uint32_t CV_MIRROR[8u];    /**< Mirror of Channel (n) Match Value, array offset: 0x204, array step: 0x4 */
} FTM_Type, *FTM_MemMapPtr;

 /** Number of instances of the FTM module. */



/* FTM - Peripheral instance base addresses */
/** Peripheral FTM0 base address */

/** Peripheral FTM0 base pointer */

/** Peripheral FTM1 base address */

/** Peripheral FTM1 base pointer */

/** Array initializer of FTM peripheral base addresses */

/** Array initializer of FTM peripheral base pointers */

 /** Number of interrupt vector arrays for the FTM module. */

 /** Number of interrupt channels for the FTM module. */

 /** Number of interrupt channels for the Fault type of FTM module. */

 /** Number of interrupt channels for the Overflow type of FTM module. */

 /** Number of interrupt channels for the Reload type of FTM module. */

/** Interrupt vectors for the FTM peripheral type */






/* ----------------------------------------------------------------------------
   -- FTM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup FTM_Register_Masks FTM Register Masks
 * @{
 */

/* SC Bit Fields */
#line 3986 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CNT Bit Fields */




/* MOD Bit Fields */




/* CnSC Bit Fields */
#line 4041 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CnV Bit Fields */




/* CNTIN Bit Fields */




/* STATUS Bit Fields */
#line 4084 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MODE Bit Fields */
#line 4113 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SYNC Bit Fields */
#line 4146 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* OUTINIT Bit Fields */
#line 4179 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* OUTMASK Bit Fields */
#line 4212 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* COMBINE Bit Fields */
#line 4341 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DEADTIME Bit Fields */
#line 4354 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* EXTTRIG Bit Fields */
#line 4395 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* POL Bit Fields */
#line 4428 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FMS Bit Fields */
#line 4457 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FILTER Bit Fields */
#line 4474 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FLTCTRL Bit Fields */
#line 4515 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* QDCTRL Bit Fields */
#line 4548 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CONF Bit Fields */
#line 4569 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FLTPOL Bit Fields */
#line 4586 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SYNCONF Bit Fields */
#line 4647 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* INVCTRL Bit Fields */
#line 4664 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SWOCTRL Bit Fields */
#line 4729 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PWMLOAD Bit Fields */
#line 4778 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* HCR Bit Fields */




/* PAIR0DEADTIME Bit Fields */
#line 4796 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PAIR1DEADTIME Bit Fields */
#line 4809 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PAIR2DEADTIME Bit Fields */
#line 4822 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PAIR3DEADTIME Bit Fields */
#line 4835 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MOD_MIRROR Bit Fields */
#line 4844 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CV_MIRROR Bit Fields */
#line 4853 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group FTM_Register_Masks */


/*!
 * @}
 */ /* end of group FTM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- GPIO Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup GPIO_Peripheral_Access_Layer GPIO Peripheral Access Layer
 * @{
 */


/** GPIO - Size of Registers Arrays */

/** GPIO - Register Layout Typedef */
typedef struct {
  volatile uint32_t PDOR;                              /**< Port Data Output Register, offset: 0x0 */
  volatile  uint32_t PSOR;                              /**< Port Set Output Register, offset: 0x4 */
  volatile  uint32_t PCOR;                              /**< Port Clear Output Register, offset: 0x8 */
  volatile  uint32_t PTOR;                              /**< Port Toggle Output Register, offset: 0xC */
  volatile const  uint32_t PDIR;                              /**< Port Data Input Register, offset: 0x10 */
  volatile uint32_t PDDR;                              /**< Port Data Direction Register, offset: 0x14 */
  volatile uint32_t PIDR;                              /**< Port Input Disable Register, offset: 0x18 */
} GPIO_Type, *GPIO_MemMapPtr;

 /** Number of instances of the GPIO module. */



/* GPIO - Peripheral instance base addresses */
/** Peripheral PTA base address */

/** Peripheral PTA base pointer */

/** Peripheral PTB base address */

/** Peripheral PTB base pointer */

/** Peripheral PTC base address */

/** Peripheral PTC base pointer */

/** Peripheral PTD base address */

/** Peripheral PTD base pointer */

/** Peripheral PTE base address */

/** Peripheral PTE base pointer */

/** Array initializer of GPIO peripheral base addresses */

/** Array initializer of GPIO peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- GPIO Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup GPIO_Register_Masks GPIO Register Masks
 * @{
 */

/* PDOR Bit Fields */




/* PSOR Bit Fields */




/* PCOR Bit Fields */




/* PTOR Bit Fields */




/* PDIR Bit Fields */




/* PDDR Bit Fields */




/* PIDR Bit Fields */





/*!
 * @}
 */ /* end of group GPIO_Register_Masks */


/*!
 * @}
 */ /* end of group GPIO_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LMEM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LMEM_Peripheral_Access_Layer LMEM Peripheral Access Layer
 * @{
 */


/** LMEM - Size of Registers Arrays */

/** LMEM - Register Layout Typedef */
typedef struct {
  volatile uint32_t PCCCR;                             /**< Cache control register, offset: 0x0 */
  volatile uint32_t PCCLCR;                            /**< Cache line control register, offset: 0x4 */
  volatile uint32_t PCCSAR;                            /**< Cache search address register, offset: 0x8 */
  volatile uint32_t PCCCVR;                            /**< Cache read/write value register, offset: 0xC */
       uint8_t RESERVED_0[16];
  volatile uint32_t PCCRMR;                            /**< Cache regions mode register, offset: 0x20 */
} LMEM_Type, *LMEM_MemMapPtr;

 /** Number of instances of the LMEM module. */



/* LMEM - Peripheral instance base addresses */
/** Peripheral LMEM base address */

/** Peripheral LMEM base pointer */

/** Array initializer of LMEM peripheral base addresses */

/** Array initializer of LMEM peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- LMEM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LMEM_Register_Masks LMEM Register Masks
 * @{
 */

/* PCCCR Bit Fields */
#line 5050 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PCCLCR Bit Fields */
#line 5091 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PCCSAR Bit Fields */
#line 5100 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PCCCVR Bit Fields */




/* PCCRMR Bit Fields */
#line 5170 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group LMEM_Register_Masks */


/*!
 * @}
 */ /* end of group LMEM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LPI2C Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPI2C_Peripheral_Access_Layer LPI2C Peripheral Access Layer
 * @{
 */


/** LPI2C - Size of Registers Arrays */

/** LPI2C - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< Parameter Register, offset: 0x4 */
       uint8_t RESERVED_0[8];
  volatile uint32_t MCR;                               /**< Master Control Register, offset: 0x10 */
  volatile uint32_t MSR;                               /**< Master Status Register, offset: 0x14 */
  volatile uint32_t MIER;                              /**< Master Interrupt Enable Register, offset: 0x18 */
  volatile uint32_t MDER;                              /**< Master DMA Enable Register, offset: 0x1C */
  volatile uint32_t MCFGR0;                            /**< Master Configuration Register 0, offset: 0x20 */
  volatile uint32_t MCFGR1;                            /**< Master Configuration Register 1, offset: 0x24 */
  volatile uint32_t MCFGR2;                            /**< Master Configuration Register 2, offset: 0x28 */
  volatile uint32_t MCFGR3;                            /**< Master Configuration Register 3, offset: 0x2C */
       uint8_t RESERVED_1[16];
  volatile uint32_t MDMR;                              /**< Master Data Match Register, offset: 0x40 */
       uint8_t RESERVED_2[4];
  volatile uint32_t MCCR0;                             /**< Master Clock Configuration Register 0, offset: 0x48 */
       uint8_t RESERVED_3[4];
  volatile uint32_t MCCR1;                             /**< Master Clock Configuration Register 1, offset: 0x50 */
       uint8_t RESERVED_4[4];
  volatile uint32_t MFCR;                              /**< Master FIFO Control Register, offset: 0x58 */
  volatile const  uint32_t MFSR;                              /**< Master FIFO Status Register, offset: 0x5C */
  volatile uint32_t MTDR;                              /**< Master Transmit Data Register, offset: 0x60 */
       uint8_t RESERVED_5[12];
  volatile const  uint32_t MRDR;                              /**< Master Receive Data Register, offset: 0x70 */
       uint8_t RESERVED_6[156];
  volatile uint32_t SCR;                               /**< Slave Control Register, offset: 0x110 */
  volatile uint32_t SSR;                               /**< Slave Status Register, offset: 0x114 */
  volatile uint32_t SIER;                              /**< Slave Interrupt Enable Register, offset: 0x118 */
  volatile uint32_t SDER;                              /**< Slave DMA Enable Register, offset: 0x11C */
       uint8_t RESERVED_7[4];
  volatile uint32_t SCFGR1;                            /**< Slave Configuration Register 1, offset: 0x124 */
  volatile uint32_t SCFGR2;                            /**< Slave Configuration Register 2, offset: 0x128 */
       uint8_t RESERVED_8[20];
  volatile uint32_t SAMR;                              /**< Slave Address Match Register, offset: 0x140 */
       uint8_t RESERVED_9[12];
  volatile const  uint32_t SASR;                              /**< Slave Address Status Register, offset: 0x150 */
  volatile uint32_t STAR;                              /**< Slave Transmit ACK Register, offset: 0x154 */
       uint8_t RESERVED_10[8];
  volatile uint32_t STDR;                              /**< Slave Transmit Data Register, offset: 0x160 */
       uint8_t RESERVED_11[12];
  volatile const  uint32_t SRDR;                              /**< Slave Receive Data Register, offset: 0x170 */
} LPI2C_Type, *LPI2C_MemMapPtr;

 /** Number of instances of the LPI2C module. */



/* LPI2C - Peripheral instance base addresses */
/** Peripheral LPI2C0 base address */

/** Peripheral LPI2C0 base pointer */

/** Array initializer of LPI2C peripheral base addresses */

/** Array initializer of LPI2C peripheral base pointers */

 /** Number of interrupt vector arrays for the LPI2C module. */

 /** Number of interrupt channels for the MASTER type of LPI2C module. */

 /** Number of interrupt channels for the SLAVE type of LPI2C module. */

/** Interrupt vectors for the LPI2C peripheral type */



/* ----------------------------------------------------------------------------
   -- LPI2C Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPI2C_Register_Masks LPI2C Register Masks
 * @{
 */

/* VERID Bit Fields */
#line 5282 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PARAM Bit Fields */
#line 5291 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCR Bit Fields */
#line 5316 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MSR Bit Fields */
#line 5361 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MIER Bit Fields */
#line 5398 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MDER Bit Fields */
#line 5407 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCFGR0 Bit Fields */
#line 5428 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCFGR1 Bit Fields */
#line 5453 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCFGR2 Bit Fields */
#line 5466 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCFGR3 Bit Fields */




/* MDMR Bit Fields */
#line 5480 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCCR0 Bit Fields */
#line 5497 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCCR1 Bit Fields */
#line 5514 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MFCR Bit Fields */
#line 5523 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MFSR Bit Fields */
#line 5532 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MTDR Bit Fields */
#line 5541 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MRDR Bit Fields */
#line 5550 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SCR Bit Fields */
#line 5575 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SSR Bit Fields */
#line 5632 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SIER Bit Fields */
#line 5681 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SDER Bit Fields */
#line 5694 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SCFGR1 Bit Fields */
#line 5739 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SCFGR2 Bit Fields */
#line 5756 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SAMR Bit Fields */
#line 5765 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SASR Bit Fields */
#line 5774 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* STAR Bit Fields */




/* STDR Bit Fields */




/* SRDR Bit Fields */
#line 5797 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group LPI2C_Register_Masks */


/*!
 * @}
 */ /* end of group LPI2C_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LPIT Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPIT_Peripheral_Access_Layer LPIT Peripheral Access Layer
 * @{
 */


/** LPIT - Size of Registers Arrays */


/** LPIT - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< Parameter Register, offset: 0x4 */
  volatile uint32_t MCR;                               /**< Module Control Register, offset: 0x8 */
  volatile uint32_t MSR;                               /**< Module Status Register, offset: 0xC */
  volatile uint32_t MIER;                              /**< Module Interrupt Enable Register, offset: 0x10 */
  volatile uint32_t SETTEN;                            /**< Set Timer Enable Register, offset: 0x14 */
  volatile uint32_t CLRTEN;                            /**< Clear Timer Enable Register, offset: 0x18 */
       uint8_t RESERVED_0[4];
  struct {                                         /* offset: 0x20, array step: 0x10 */
    volatile uint32_t TVAL;                              /**< Timer Value Register, array offset: 0x20, array step: 0x10 */
    volatile const  uint32_t CVAL;                              /**< Current Timer Value, array offset: 0x24, array step: 0x10 */
    volatile uint32_t TCTRL;                             /**< Timer Control Register, array offset: 0x28, array step: 0x10 */
         uint8_t RESERVED_0[4];
  } TMR[4u];
} LPIT_Type, *LPIT_MemMapPtr;

 /** Number of instances of the LPIT module. */



/* LPIT - Peripheral instance base addresses */
/** Peripheral LPIT0 base address */

/** Peripheral LPIT0 base pointer */

/** Array initializer of LPIT peripheral base addresses */

/** Array initializer of LPIT peripheral base pointers */

 /** Number of interrupt vector arrays for the LPIT module. */

 /** Number of interrupt channels for the LPIT module. */

/** Interrupt vectors for the LPIT peripheral type */


/* ----------------------------------------------------------------------------
   -- LPIT Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPIT_Register_Masks LPIT Register Masks
 * @{
 */

/* VERID Bit Fields */
#line 5881 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PARAM Bit Fields */
#line 5890 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MCR Bit Fields */
#line 5907 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MSR Bit Fields */
#line 5924 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MIER Bit Fields */
#line 5941 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SETTEN Bit Fields */
#line 5958 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CLRTEN Bit Fields */
#line 5975 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TMR_TVAL Bit Fields */




/* TMR_CVAL Bit Fields */




/* TMR_TCTRL Bit Fields */
#line 6018 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group LPIT_Register_Masks */


/*!
 * @}
 */ /* end of group LPIT_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LPSPI Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPSPI_Peripheral_Access_Layer LPSPI Peripheral Access Layer
 * @{
 */


/** LPSPI - Size of Registers Arrays */

/** LPSPI - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< Parameter Register, offset: 0x4 */
       uint8_t RESERVED_0[8];
  volatile uint32_t CR;                                /**< Control Register, offset: 0x10 */
  volatile uint32_t SR;                                /**< Status Register, offset: 0x14 */
  volatile uint32_t IER;                               /**< Interrupt Enable Register, offset: 0x18 */
  volatile uint32_t DER;                               /**< DMA Enable Register, offset: 0x1C */
  volatile uint32_t CFGR0;                             /**< Configuration Register 0, offset: 0x20 */
  volatile uint32_t CFGR1;                             /**< Configuration Register 1, offset: 0x24 */
       uint8_t RESERVED_1[8];
  volatile uint32_t DMR0;                              /**< Data Match Register 0, offset: 0x30 */
  volatile uint32_t DMR1;                              /**< Data Match Register 1, offset: 0x34 */
       uint8_t RESERVED_2[8];
  volatile uint32_t CCR;                               /**< Clock Configuration Register, offset: 0x40 */
       uint8_t RESERVED_3[20];
  volatile uint32_t FCR;                               /**< FIFO Control Register, offset: 0x58 */
  volatile const  uint32_t FSR;                               /**< FIFO Status Register, offset: 0x5C */
  volatile uint32_t TCR;                               /**< Transmit Command Register, offset: 0x60 */
  volatile  uint32_t TDR;                               /**< Transmit Data Register, offset: 0x64 */
       uint8_t RESERVED_4[8];
  volatile const  uint32_t RSR;                               /**< Receive Status Register, offset: 0x70 */
  volatile const  uint32_t RDR;                               /**< Receive Data Register, offset: 0x74 */
} LPSPI_Type, *LPSPI_MemMapPtr;

 /** Number of instances of the LPSPI module. */



/* LPSPI - Peripheral instance base addresses */
/** Peripheral LPSPI0 base address */

/** Peripheral LPSPI0 base pointer */

/** Peripheral LPSPI1 base address */

/** Peripheral LPSPI1 base pointer */

/** Array initializer of LPSPI peripheral base addresses */

/** Array initializer of LPSPI peripheral base pointers */

 /** Number of interrupt vector arrays for the LPSPI module. */

 /** Number of interrupt channels for the LPSPI module. */

/** Interrupt vectors for the LPSPI peripheral type */


/* ----------------------------------------------------------------------------
   -- LPSPI Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPSPI_Register_Masks LPSPI Register Masks
 * @{
 */

/* VERID Bit Fields */
#line 6113 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PARAM Bit Fields */
#line 6122 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CR Bit Fields */
#line 6147 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SR Bit Fields */
#line 6184 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* IER Bit Fields */
#line 6217 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DER Bit Fields */
#line 6226 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CFGR0 Bit Fields */
#line 6247 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CFGR1 Bit Fields */
#line 6284 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DMR0 Bit Fields */




/* DMR1 Bit Fields */




/* CCR Bit Fields */
#line 6311 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FCR Bit Fields */
#line 6320 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FSR Bit Fields */
#line 6329 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TCR Bit Fields */
#line 6378 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TDR Bit Fields */




/* RSR Bit Fields */
#line 6392 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RDR Bit Fields */





/*!
 * @}
 */ /* end of group LPSPI_Register_Masks */


/*!
 * @}
 */ /* end of group LPSPI_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LPTMR Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPTMR_Peripheral_Access_Layer LPTMR Peripheral Access Layer
 * @{
 */


/** LPTMR - Size of Registers Arrays */

/** LPTMR - Register Layout Typedef */
typedef struct {
  volatile uint32_t CSR;                               /**< Low Power Timer Control Status Register, offset: 0x0 */
  volatile uint32_t PSR;                               /**< Low Power Timer Prescale Register, offset: 0x4 */
  volatile uint32_t CMR;                               /**< Low Power Timer Compare Register, offset: 0x8 */
  volatile uint32_t CNR;                               /**< Low Power Timer Counter Register, offset: 0xC */
} LPTMR_Type, *LPTMR_MemMapPtr;

 /** Number of instances of the LPTMR module. */



/* LPTMR - Peripheral instance base addresses */
/** Peripheral LPTMR0 base address */

/** Peripheral LPTMR0 base pointer */

/** Array initializer of LPTMR peripheral base addresses */

/** Array initializer of LPTMR peripheral base pointers */

 /** Number of interrupt vector arrays for the LPTMR module. */

 /** Number of interrupt channels for the LPTMR module. */

/** Interrupt vectors for the LPTMR peripheral type */


/* ----------------------------------------------------------------------------
   -- LPTMR Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPTMR_Register_Masks LPTMR Register Masks
 * @{
 */

/* CSR Bit Fields */
#line 6490 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PSR Bit Fields */
#line 6503 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CMR Bit Fields */




/* CNR Bit Fields */





/*!
 * @}
 */ /* end of group LPTMR_Register_Masks */


/*!
 * @}
 */ /* end of group LPTMR_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- LPUART Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPUART_Peripheral_Access_Layer LPUART Peripheral Access Layer
 * @{
 */


/** LPUART - Size of Registers Arrays */

/** LPUART - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< Parameter Register, offset: 0x4 */
  volatile uint32_t GLOBAL;                            /**< LPUART Global Register, offset: 0x8 */
  volatile uint32_t PINCFG;                            /**< LPUART Pin Configuration Register, offset: 0xC */
  volatile uint32_t BAUD;                              /**< LPUART Baud Rate Register, offset: 0x10 */
  volatile uint32_t STAT;                              /**< LPUART Status Register, offset: 0x14 */
  volatile uint32_t CTRL;                              /**< LPUART Control Register, offset: 0x18 */
  volatile uint32_t DATA;                              /**< LPUART Data Register, offset: 0x1C */
  volatile uint32_t MATCH;                             /**< LPUART Match Address Register, offset: 0x20 */
  volatile uint32_t MODIR;                             /**< LPUART Modem IrDA Register, offset: 0x24 */
  volatile uint32_t FIFO;                              /**< LPUART FIFO Register, offset: 0x28 */
  volatile uint32_t WATER;                             /**< LPUART Watermark Register, offset: 0x2C */
} LPUART_Type, *LPUART_MemMapPtr;

 /** Number of instances of the LPUART module. */



/* LPUART - Peripheral instance base addresses */
/** Peripheral LPUART0 base address */

/** Peripheral LPUART0 base pointer */

/** Peripheral LPUART1 base address */

/** Peripheral LPUART1 base pointer */

/** Array initializer of LPUART peripheral base addresses */

/** Array initializer of LPUART peripheral base pointers */

 /** Number of interrupt vector arrays for the LPUART module. */

 /** Number of interrupt channels for the RX_TX type of LPUART module. */

/** Interrupt vectors for the LPUART peripheral type */


/* ----------------------------------------------------------------------------
   -- LPUART Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup LPUART_Register_Masks LPUART Register Masks
 * @{
 */

/* VERID Bit Fields */
#line 6598 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PARAM Bit Fields */
#line 6607 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* GLOBAL Bit Fields */




/* PINCFG Bit Fields */




/* BAUD Bit Fields */
#line 6674 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* STAT Bit Fields */
#line 6747 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CTRL Bit Fields */
#line 6860 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DATA Bit Fields */
#line 6921 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MATCH Bit Fields */
#line 6930 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MODIR Bit Fields */
#line 6967 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FIFO Bit Fields */
#line 7020 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* WATER Bit Fields */
#line 7037 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group LPUART_Register_Masks */


/*!
 * @}
 */ /* end of group LPUART_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MCM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MCM_Peripheral_Access_Layer MCM Peripheral Access Layer
 * @{
 */


/** MCM - Size of Registers Arrays */


/** MCM - Register Layout Typedef */
typedef struct {
       uint8_t RESERVED_0[8];
  volatile const  uint16_t PLASC;                             /**< Crossbar Switch (AXBS) Slave Configuration, offset: 0x8 */
  volatile const  uint16_t PLAMC;                             /**< Crossbar Switch (AXBS) Master Configuration, offset: 0xA */
  volatile uint32_t CPCR;                              /**< Core Platform Control Register, offset: 0xC */
       uint8_t RESERVED_1[32];
  volatile uint32_t PID;                               /**< Process ID Register, offset: 0x30 */
       uint8_t RESERVED_2[12];
  volatile uint32_t CPO;                               /**< Compute Operation Control Register, offset: 0x40 */
       uint8_t RESERVED_3[956];
  volatile uint32_t LMDR[2u];              /**< Local Memory Descriptor Register, array offset: 0x400, array step: 0x4 */
       uint8_t RESERVED_4[120];
  volatile const  uint32_t LMPECR;                            /**< LMEM Parity and ECC Control Register, offset: 0x480 */
       uint8_t RESERVED_5[4];
  volatile uint32_t LMPEIR;                            /**< LMEM Parity and ECC Interrupt Register, offset: 0x488 */
       uint8_t RESERVED_6[4];
  volatile const  uint32_t LMFAR;                             /**< LMEM Fault Address Register, offset: 0x490 */
  volatile const  uint32_t LMFATR;                            /**< LMEM Fault Attribute Register, offset: 0x494 */
       uint8_t RESERVED_7[8];
  volatile const  uint32_t LMFDHR;                            /**< LMEM Fault Data High Register, offset: 0x4A0 */
  volatile const  uint32_t LMFDLR;                            /**< LMEM Fault Data Low Register, offset: 0x4A4 */
} MCM_Type, *MCM_MemMapPtr;

 /** Number of instances of the MCM module. */



/* MCM - Peripheral instance base addresses */
/** Peripheral MCM base address */

/** Peripheral MCM base pointer */

/** Array initializer of MCM peripheral base addresses */

/** Array initializer of MCM peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- MCM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MCM_Register_Masks MCM Register Masks
 * @{
 */

/* PLASC Bit Fields */




/* PLAMC Bit Fields */




/* CPCR Bit Fields */
#line 7143 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PID Bit Fields */




/* CPO Bit Fields */
#line 7161 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LMDR Bit Fields */
#line 7190 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LMPEIR Bit Fields */
#line 7207 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LMFAR Bit Fields */




/* LMFATR Bit Fields */
#line 7233 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LMFDHR Bit Fields */




/* LMFDLR Bit Fields */





/*!
 * @}
 */ /* end of group MCM_Register_Masks */


/*!
 * @}
 */ /* end of group MCM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MPU Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MPU_Peripheral_Access_Layer MPU Peripheral Access Layer
 * @{
 */


/** MPU - Size of Registers Arrays */




/** MPU - Register Layout Typedef */
typedef struct {
  volatile uint32_t CESR;                              /**< Control/Error Status Register, offset: 0x0 */
       uint8_t RESERVED_0[12];
  struct {                                         /* offset: 0x10, array step: 0x8 */
    volatile const  uint32_t EAR;                               /**< Error Address Register, slave port
    							0..Error Address Register, slave port
    							1, array offset: 0x10, array step: 0x8 */
    volatile const  uint32_t EDR;                               /**< Error Detail Register, slave port
    							0..Error Detail Register, slave port
    							1, array offset: 0x14, array step: 0x8 */
  } EAR_EDR[2u];
       uint8_t RESERVED_1[992];
  struct {                                         /* offset: 0x400, array step: 0x10 */
    volatile uint32_t WORD0;                             /**< Region Descriptor 0, Word 0..Region Descriptor 7, Word 0, array offset: 0x400, array step: 0x10 */
    volatile uint32_t WORD1;                             /**< Region Descriptor 0, Word 1..Region Descriptor 7, Word 1, array offset: 0x404, array step: 0x10 */
    volatile uint32_t WORD2;                             /**< Region Descriptor 0, Word 2..Region Descriptor 7, Word 2, array offset: 0x408, array step: 0x10 */
    volatile uint32_t WORD3;                             /**< Region Descriptor 0, Word 3..Region Descriptor 7, Word 3, array offset: 0x40C, array step: 0x10 */
  } RGD[8u];
       uint8_t RESERVED_2[896];
  volatile uint32_t RGDAAC[8u];          /**< Region Descriptor Alternate Access Control
  							0..Region Descriptor Alternate Access Control
  							7, array offset: 0x800, array step: 0x4 */
} MPU_Type, *MPU_MemMapPtr;

 /** Number of instances of the MPU module. */



/* MPU - Peripheral instance base addresses */
/** Peripheral MPU base address */

/** Peripheral MPU base pointer */

/** Array initializer of MPU peripheral base addresses */

/** Array initializer of MPU peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- MPU Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MPU_Register_Masks MPU Register Masks
 * @{
 */

/* CESR Bit Fields */
#line 7342 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* EAR Bit Fields */




/* EDR Bit Fields */
#line 7368 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RGD_WORD0 Bit Fields */




/* RGD_WORD1 Bit Fields */




/* RGD_WORD2 Bit Fields */
#line 7447 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RGD_WORD3 Bit Fields */
#line 7460 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RGDAAC Bit Fields */
#line 7529 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group MPU_Register_Masks */


/*!
 * @}
 */ /* end of group MPU_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MSCM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MSCM_Peripheral_Access_Layer MSCM Peripheral Access Layer
 * @{
 */


/** MSCM - Size of Registers Arrays */


/** MSCM - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t CPxTYPE;                           /**< Processor X Type Register, offset: 0x0 */
  volatile const  uint32_t CPxNUM;                            /**< Processor X Number Register, offset: 0x4 */
  volatile const  uint32_t CPxMASTER;                         /**< Processor X Master Register, offset: 0x8 */
  volatile const  uint32_t CPxCOUNT;                          /**< Processor X Count Register, offset: 0xC */
  volatile const  uint32_t CPxCFG0;                           /**< Processor X Configuration Register 0, offset: 0x10 */
  volatile const  uint32_t CPxCFG1;                           /**< Processor X Configuration Register 1, offset: 0x14 */
  volatile const  uint32_t CPxCFG2;                           /**< Processor X Configuration Register 2, offset: 0x18 */
  volatile const  uint32_t CPxCFG3;                           /**< Processor X Configuration Register 3, offset: 0x1C */
  volatile const  uint32_t CP0TYPE;                           /**< Processor 0 Type Register, offset: 0x20 */
  volatile const  uint32_t CP0NUM;                            /**< Processor 0 Number Register, offset: 0x24 */
  volatile const  uint32_t CP0MASTER;                         /**< Processor 0 Master Register, offset: 0x28 */
  volatile const  uint32_t CP0COUNT;                          /**< Processor 0 Count Register, offset: 0x2C */
  volatile const  uint32_t CP0CFG0;                           /**< Processor 0 Configuration Register 0, offset: 0x30 */
  volatile const  uint32_t CP0CFG1;                           /**< Processor 0 Configuration Register 1, offset: 0x34 */
  volatile const  uint32_t CP0CFG2;                           /**< Processor 0 Configuration Register 2, offset: 0x38 */
  volatile const  uint32_t CP0CFG3;                           /**< Processor 0 Configuration Register 3, offset: 0x3C */
       uint8_t RESERVED_0[960];
  volatile uint32_t OCMDR[3u];           /**< On-Chip Memory Descriptor Register, array offset: 0x400, array step: 0x4 */
} MSCM_Type, *MSCM_MemMapPtr;

 /** Number of instances of the MSCM module. */



/* MSCM - Peripheral instance base addresses */
/** Peripheral MSCM base address */

/** Peripheral MSCM base pointer */

/** Array initializer of MSCM peripheral base addresses */

/** Array initializer of MSCM peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- MSCM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MSCM_Register_Masks MSCM Register Masks
 * @{
 */

/* CPxTYPE Bit Fields */
#line 7607 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CPxNUM Bit Fields */




/* CPxMASTER Bit Fields */




/* CPxCOUNT Bit Fields */




/* CPxCFG0 Bit Fields */
#line 7639 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CPxCFG1 Bit Fields */
#line 7648 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CPxCFG2 Bit Fields */
#line 7657 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CPxCFG3 Bit Fields */
#line 7690 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CP0TYPE Bit Fields */
#line 7699 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CP0NUM Bit Fields */




/* CP0MASTER Bit Fields */




/* CP0COUNT Bit Fields */




/* CP0CFG0 Bit Fields */
#line 7731 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CP0CFG1 Bit Fields */
#line 7740 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CP0CFG2 Bit Fields */
#line 7749 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CP0CFG3 Bit Fields */
#line 7782 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* OCMDR Bit Fields */
#line 7815 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group MSCM_Register_Masks */


/*!
 * @}
 */ /* end of group MSCM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- MTB_DWT Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MTB_DWT_Peripheral_Access_Layer MTB_DWT Peripheral Access Layer
 * @{
 */


/** MTB_DWT - Size of Registers Arrays */




/** MTB_DWT - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t CTRL;                              /**< MTB DWT Control Register, offset: 0x0 */
       uint8_t RESERVED_0[28];
  struct {                                         /* offset: 0x20, array step: 0x10 */
    volatile uint32_t COMP;                              /**< MTB_DWT Comparator Register, array offset: 0x20, array step: 0x10 */
    volatile uint32_t MASK;                              /**< MTB_DWT Comparator Mask Register, array offset: 0x24, array step: 0x10 */
    volatile uint32_t FCT;                               /**< MTB_DWT Comparator Function Register 0..MTB_DWT Comparator Function Register 1, array offset: 0x28, array step: 0x10 */
         uint8_t RESERVED_0[4];
  } CMF[2u];
       uint8_t RESERVED_1[448];
  volatile uint32_t TBCTRL;                            /**< MTB_DWT Trace Buffer Control Register, offset: 0x200 */
       uint8_t RESERVED_2[3524];
  volatile const  uint32_t DEVICECFG;                         /**< Device Configuration Register, offset: 0xFC8 */
  volatile const  uint32_t DEVICETYPID;                       /**< Device Type Identifier Register, offset: 0xFCC */
  volatile const  uint32_t PERIPHID[8u];  /**< Peripheral ID Register, array offset: 0xFD0, array step: 0x4 */
  volatile const  uint32_t COMPID[4u];      /**< Component ID Register, array offset: 0xFF0, array step: 0x4 */
} MTB_DWT_Type, *MTB_DWT_MemMapPtr;

 /** Number of instances of the MTB_DWT module. */



/* MTB_DWT - Peripheral instance base addresses */
/** Peripheral MTB_DWT base address */

/** Peripheral MTB_DWT base pointer */

/** Array initializer of MTB_DWT peripheral base addresses */

/** Array initializer of MTB_DWT peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- MTB_DWT Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup MTB_DWT_Register_Masks MTB_DWT Register Masks
 * @{
 */

/* CTRL Bit Fields */
#line 7892 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* COMP Bit Fields */




/* MASK Bit Fields */




/* FCT Bit Fields */
#line 7923 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TBCTRL Bit Fields */
#line 7936 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DEVICECFG Bit Fields */




/* DEVICETYPID Bit Fields */




/* PERIPHID Bit Fields */




/* COMPID Bit Fields */





/*!
 * @}
 */ /* end of group MTB_DWT_Register_Masks */


/*!
 * @}
 */ /* end of group MTB_DWT_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- PCC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PCC_Peripheral_Access_Layer PCC Peripheral Access Layer
 * @{
 */


/** PCC - Size of Registers Arrays */


/** PCC - Register Layout Typedef */
typedef struct {
  volatile uint32_t PCCn[116u];              /**< PCC Reserved Register 0..PCC CMP0 Register, array offset: 0x0, array step: 0x4 */
} PCC_Type, *PCC_MemMapPtr;

 /** Number of instances of the PCC module. */



/* PCC - Peripheral instance base addresses */
/** Peripheral PCC base address */

/** Peripheral PCC base pointer */

/** Array initializer of PCC peripheral base addresses */

/** Array initializer of PCC peripheral base pointers */


/* PCC index offsets */
#line 8025 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/* ----------------------------------------------------------------------------
   -- PCC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PCC_Register_Masks PCC Register Masks
 * @{
 */

/* PCCn Bit Fields */
#line 8056 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group PCC_Register_Masks */


/*!
 * @}
 */ /* end of group PCC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- PDB Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PDB_Peripheral_Access_Layer PDB Peripheral Access Layer
 * @{
 */


/** PDB - Size of Registers Arrays */




/** PDB - Register Layout Typedef */
typedef struct {
  volatile uint32_t SC;                                /**< Status and Control register, offset: 0x0 */
  volatile uint32_t MOD;                               /**< Modulus register, offset: 0x4 */
  volatile const  uint32_t CNT;                               /**< Counter register, offset: 0x8 */
  volatile uint32_t IDLY;                              /**< Interrupt Delay register, offset: 0xC */
  struct {                                         /* offset: 0x10, array step: 0x28 */
    volatile uint32_t C1;                                /**< Channel n Control register 1, array offset: 0x10, array step: 0x28 */
    volatile uint32_t S;                                 /**< Channel n Status register, array offset: 0x14, array step: 0x28 */
    volatile uint32_t DLY[8u];                /**< Channel n Delay 0 register..Channel n Delay 7 register, array offset: 0x18, array step: index*0x28, index2*0x4 */
  } CH[2u];
       uint8_t RESERVED_0[304];
  volatile uint32_t POEN;                              /**< Pulse-Out n Enable register, offset: 0x190 */
  union {                                          /* offset: 0x194, array step: 0x4 */
    volatile uint32_t PODLY;                             /**< Pulse-Out n Delay register, array offset: 0x194, array step: 0x4 */
    struct {                                         /* offset: 0x194, array step: 0x4 */
      volatile uint16_t DLY2;                              /**< PDB0_DLY2 register., array offset: 0x194, array step: 0x4 */
      volatile uint16_t DLY1;                              /**< PDB0_DLY1 register., array offset: 0x196, array step: 0x4 */
    } ACCESS16BIT;
  } POnDLY[1u];
} PDB_Type, *PDB_MemMapPtr;

 /** Number of instances of the PDB module. */



/* PDB - Peripheral instance base addresses */
/** Peripheral PDB0 base address */

/** Peripheral PDB0 base pointer */

/** Array initializer of PDB peripheral base addresses */

/** Array initializer of PDB peripheral base pointers */

 /** Number of interrupt vector arrays for the PDB module. */

 /** Number of interrupt channels for the PDB module. */

/** Interrupt vectors for the PDB peripheral type */


/* ----------------------------------------------------------------------------
   -- PDB Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PDB_Register_Masks PDB Register Masks
 * @{
 */

/* SC Bit Fields */
#line 8182 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MOD Bit Fields */




/* CNT Bit Fields */




/* IDLY Bit Fields */




/* C1 Bit Fields */
#line 8210 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* S Bit Fields */
#line 8219 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* DLY Bit Fields */




/* POEN Bit Fields */




/* POnDLY_PODLY Bit Fields */
#line 8238 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* POnDLY_ACCESS16BIT_DLY2 Bit Fields */




/* POnDLY_ACCESS16BIT_DLY1 Bit Fields */





/*!
 * @}
 */ /* end of group PDB_Register_Masks */


/*!
 * @}
 */ /* end of group PDB_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- PMC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PMC_Peripheral_Access_Layer PMC Peripheral Access Layer
 * @{
 */


/** PMC - Size of Registers Arrays */

/** PMC - Register Layout Typedef */
typedef struct {
  volatile uint8_t LVDSC1;                             /**< Low Voltage Detect Status and Control 1 Register, offset: 0x0 */
  volatile uint8_t LVDSC2;                             /**< Low Voltage Detect Status and Control 2 Register, offset: 0x1 */
  volatile uint8_t REGSC;                              /**< Regulator Status and Control Register, offset: 0x2 */
       uint8_t RESERVED_0[1];
  volatile uint8_t LPOTRIM;                            /**< Low Power Oscillator Trim Register, offset: 0x4 */
} PMC_Type, *PMC_MemMapPtr;

 /** Number of instances of the PMC module. */



/* PMC - Peripheral instance base addresses */
/** Peripheral PMC base address */

/** Peripheral PMC base pointer */

/** Array initializer of PMC peripheral base addresses */

/** Array initializer of PMC peripheral base pointers */

 /** Number of interrupt vector arrays for the PMC module. */

 /** Number of interrupt channels for the PMC module. */

/** Interrupt vectors for the PMC peripheral type */


/* ----------------------------------------------------------------------------
   -- PMC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PMC_Register_Masks PMC Register Masks
 * @{
 */

/* LVDSC1 Bit Fields */
#line 8326 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LVDSC2 Bit Fields */
#line 8339 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* REGSC Bit Fields */
#line 8360 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LPOTRIM Bit Fields */





/*!
 * @}
 */ /* end of group PMC_Register_Masks */


/*!
 * @}
 */ /* end of group PMC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- PORT Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PORT_Peripheral_Access_Layer PORT Peripheral Access Layer
 * @{
 */


/** PORT - Size of Registers Arrays */


/** PORT - Register Layout Typedef */
typedef struct {
  volatile uint32_t PCR[32u];               /**< Pin Control Register n, array offset: 0x0, array step: 0x4 */
  volatile  uint32_t GPCLR;                             /**< Global Pin Control Low Register, offset: 0x80 */
  volatile  uint32_t GPCHR;                             /**< Global Pin Control High Register, offset: 0x84 */
  volatile  uint32_t GICLR;                             /**< Global Interrupt Control Low Register, offset: 0x88 */
  volatile  uint32_t GICHR;                             /**< Global Interrupt Control High Register, offset: 0x8C */
       uint8_t RESERVED_0[16];
  volatile uint32_t ISFR;                              /**< Interrupt Status Flag Register, offset: 0xA0 */
       uint8_t RESERVED_1[28];
  volatile uint32_t DFER;                              /**< Digital Filter Enable Register, offset: 0xC0 */
  volatile uint32_t DFCR;                              /**< Digital Filter Clock Register, offset: 0xC4 */
  volatile uint32_t DFWR;                              /**< Digital Filter Width Register, offset: 0xC8 */
} PORT_Type, *PORT_MemMapPtr;

 /** Number of instances of the PORT module. */



/* PORT - Peripheral instance base addresses */
/** Peripheral PORTA base address */

/** Peripheral PORTA base pointer */

/** Peripheral PORTB base address */

/** Peripheral PORTB base pointer */

/** Peripheral PORTC base address */

/** Peripheral PORTC base pointer */

/** Peripheral PORTD base address */

/** Peripheral PORTD base pointer */

/** Peripheral PORTE base address */

/** Peripheral PORTE base pointer */

/** Array initializer of PORT peripheral base addresses */

/** Array initializer of PORT peripheral base pointers */

 /** Number of interrupt vector arrays for the PORT module. */

 /** Number of interrupt channels for the PORT module. */

/** Interrupt vectors for the PORT peripheral type */


/* ----------------------------------------------------------------------------
   -- PORT Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup PORT_Register_Masks PORT Register Masks
 * @{
 */

/* PCR Bit Fields */
#line 8482 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* GPCLR Bit Fields */
#line 8491 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* GPCHR Bit Fields */
#line 8500 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* GICLR Bit Fields */
#line 8509 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* GICHR Bit Fields */
#line 8518 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ISFR Bit Fields */




/* DFER Bit Fields */




/* DFCR Bit Fields */




/* DFWR Bit Fields */





/*!
 * @}
 */ /* end of group PORT_Register_Masks */


/*!
 * @}
 */ /* end of group PORT_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- RCM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RCM_Peripheral_Access_Layer RCM Peripheral Access Layer
 * @{
 */


/** RCM - Size of Registers Arrays */

/** RCM - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< Parameter Register, offset: 0x4 */
  volatile const  uint32_t SRS;                               /**< System Reset Status Register, offset: 0x8 */
  volatile uint32_t RPC;                               /**< Reset Pin Control register, offset: 0xC */
       uint8_t RESERVED_0[8];
  volatile uint32_t SSRS;                              /**< Sticky System Reset Status Register, offset: 0x18 */
  volatile uint32_t SRIE;                              /**< System Reset Interrupt Enable Register, offset: 0x1C */
} RCM_Type, *RCM_MemMapPtr;

 /** Number of instances of the RCM module. */



/* RCM - Peripheral instance base addresses */
/** Peripheral RCM base address */

/** Peripheral RCM base pointer */

/** Array initializer of RCM peripheral base addresses */

/** Array initializer of RCM peripheral base pointers */

 /** Number of interrupt vector arrays for the RCM module. */

 /** Number of interrupt channels for the RCM module. */

/** Interrupt vectors for the RCM peripheral type */


/* ----------------------------------------------------------------------------
   -- RCM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RCM_Register_Masks RCM Register Masks
 * @{
 */

/* VERID Bit Fields */
#line 8614 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PARAM Bit Fields */
#line 8675 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SRS Bit Fields */
#line 8724 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RPC Bit Fields */
#line 8737 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SSRS Bit Fields */
#line 8786 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SRIE Bit Fields */
#line 8835 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group RCM_Register_Masks */


/*!
 * @}
 */ /* end of group RCM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- RTC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RTC_Peripheral_Access_Layer RTC Peripheral Access Layer
 * @{
 */


/** RTC - Size of Registers Arrays */

/** RTC - Register Layout Typedef */
typedef struct {
  volatile uint32_t TSR;                               /**< RTC Time Seconds Register, offset: 0x0 */
  volatile uint32_t TPR;                               /**< RTC Time Prescaler Register, offset: 0x4 */
  volatile uint32_t TAR;                               /**< RTC Time Alarm Register, offset: 0x8 */
  volatile uint32_t TCR;                               /**< RTC Time Compensation Register, offset: 0xC */
  volatile uint32_t CR;                                /**< RTC Control Register, offset: 0x10 */
  volatile uint32_t SR;                                /**< RTC Status Register, offset: 0x14 */
  volatile uint32_t LR;                                /**< RTC Lock Register, offset: 0x18 */
  volatile uint32_t IER;                               /**< RTC Interrupt Enable Register, offset: 0x1C */
} RTC_Type, *RTC_MemMapPtr;

 /** Number of instances of the RTC module. */



/* RTC - Peripheral instance base addresses */
/** Peripheral RTC base address */

/** Peripheral RTC base pointer */

/** Array initializer of RTC peripheral base addresses */

/** Array initializer of RTC peripheral base pointers */

 /** Number of interrupt vector arrays for the RTC module. */

 /** Number of interrupt channels for the RTC module. */

 /** Number of interrupt channels for the SECONDS type of RTC module. */

/** Interrupt vectors for the RTC peripheral type */



/* ----------------------------------------------------------------------------
   -- RTC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup RTC_Register_Masks RTC Register Masks
 * @{
 */

/* TSR Bit Fields */




/* TPR Bit Fields */




/* TAR Bit Fields */




/* TCR Bit Fields */
#line 8934 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CR Bit Fields */
#line 8963 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SR Bit Fields */
#line 8980 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LR Bit Fields */
#line 8997 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* IER Bit Fields */
#line 9018 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group RTC_Register_Masks */


/*!
 * @}
 */ /* end of group RTC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- S32_NVIC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup S32_NVIC_Peripheral_Access_Layer S32_NVIC Peripheral Access Layer
 * @{
 */


/** S32_NVIC - Size of Registers Arrays */






/** S32_NVIC - Register Layout Typedef */
typedef struct {
  volatile uint32_t ISER[1u];         /**< Interrupt Set Enable Register, array offset: 0x0, array step: 0x4 */
       uint8_t RESERVED_0[124];
  volatile uint32_t ICER[1u];         /**< Interrupt Clear Enable Register, array offset: 0x80, array step: 0x4 */
       uint8_t RESERVED_1[124];
  volatile uint32_t ISPR[1u];         /**< Interrupt Set Pending Register, array offset: 0x100, array step: 0x4 */
       uint8_t RESERVED_2[124];
  volatile uint32_t ICPR[1u];         /**< Interrupt Clear Pending Register, array offset: 0x180, array step: 0x4 */
       uint8_t RESERVED_3[380];
  volatile uint32_t IPR[8u];           /**< Interrupt Priority Register n, array offset: 0x300, array step: 0x4 */
} S32_NVIC_Type, *S32_NVIC_MemMapPtr;

 /** Number of instances of the S32_NVIC module. */



/* S32_NVIC - Peripheral instance base addresses */
/** Peripheral S32_NVIC base address */

/** Peripheral S32_NVIC base pointer */

/** Array initializer of S32_NVIC peripheral base addresses */

/** Array initializer of S32_NVIC peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- S32_NVIC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup S32_NVIC_Register_Masks S32_NVIC Register Masks
 * @{
 */

/* ISER Bit Fields */




/* ICER Bit Fields */




/* ISPR Bit Fields */




/* ICPR Bit Fields */




/* IPR Bit Fields */
#line 9119 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group S32_NVIC_Register_Masks */


/*!
 * @}
 */ /* end of group S32_NVIC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- S32_SCB Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup S32_SCB_Peripheral_Access_Layer S32_SCB Peripheral Access Layer
 * @{
 */


/** S32_SCB - Size of Registers Arrays */

/** S32_SCB - Register Layout Typedef */
typedef struct {
       uint8_t RESERVED_0[8];
  volatile const  uint32_t ACTLR;                             /**< Auxiliary Control Register,, offset: 0x8 */
       uint8_t RESERVED_1[3316];
  volatile const  uint32_t CPUID;                             /**< CPUID Base Register, offset: 0xD00 */
  volatile uint32_t ICSR;                              /**< Interrupt Control and State Register, offset: 0xD04 */
  volatile uint32_t VTOR;                              /**< Vector Table Offset Register, offset: 0xD08 */
  volatile uint32_t AIRCR;                             /**< Application Interrupt and Reset Control Register, offset: 0xD0C */
  volatile uint32_t SCR;                               /**< System Control Register, offset: 0xD10 */
  volatile const  uint32_t CCR;                               /**< Configuration and Control Register, offset: 0xD14 */
       uint8_t RESERVED_2[4];
  volatile uint32_t SHPR2;                             /**< System Handler Priority Register 2, offset: 0xD1C */
  volatile uint32_t SHPR3;                             /**< System Handler Priority Register 3, offset: 0xD20 */
  volatile uint32_t SHCSR;                             /**< System Handler Control and State Register, offset: 0xD24 */
       uint8_t RESERVED_3[8];
  volatile uint32_t DFSR;                              /**< Debug Fault Status Register, offset: 0xD30 */
} S32_SCB_Type, *S32_SCB_MemMapPtr;

 /** Number of instances of the S32_SCB module. */



/* S32_SCB - Peripheral instance base addresses */
/** Peripheral S32_SCB base address */

/** Peripheral S32_SCB base pointer */

/** Array initializer of S32_SCB peripheral base addresses */

/** Array initializer of S32_SCB peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- S32_SCB Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup S32_SCB_Register_Masks S32_SCB Register Masks
 * @{
 */

/* CPUID Bit Fields */
#line 9201 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ICSR Bit Fields */
#line 9234 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* VTOR Bit Fields */




/* AIRCR Bit Fields */
#line 9256 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SCR Bit Fields */
#line 9269 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CCR Bit Fields */
#line 9278 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SHPR2 Bit Fields */




/* SHPR3 Bit Fields */
#line 9292 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SHCSR Bit Fields */




/* DFSR Bit Fields */
#line 9318 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group S32_SCB_Register_Masks */


/*!
 * @}
 */ /* end of group S32_SCB_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- S32_SysTick Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup S32_SysTick_Peripheral_Access_Layer S32_SysTick Peripheral Access Layer
 * @{
 */


/** S32_SysTick - Size of Registers Arrays */

/** S32_SysTick - Register Layout Typedef */
typedef struct {
  volatile uint32_t CSR;                               /**< SysTick Control and Status Register, offset: 0x0 */
  volatile uint32_t RVR;                               /**< SysTick Reload Value Register, offset: 0x4 */
  volatile uint32_t CVR;                               /**< SysTick Current Value Register, offset: 0x8 */
  volatile const  uint32_t CALIB;                             /**< SysTick Calibration Value Register, offset: 0xC */
} S32_SysTick_Type, *S32_SysTick_MemMapPtr;

 /** Number of instances of the S32_SysTick module. */



/* S32_SysTick - Peripheral instance base addresses */
/** Peripheral S32_SysTick base address */

/** Peripheral S32_SysTick base pointer */

/** Array initializer of S32_SysTick peripheral base addresses */

/** Array initializer of S32_SysTick peripheral base pointers */

 /** Number of interrupt vector arrays for the S32_SysTick module. */

 /** Number of interrupt channels for the S32_SysTick module. */

/** Interrupt vectors for the S32_SysTick peripheral type */


/* ----------------------------------------------------------------------------
   -- S32_SysTick Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup S32_SysTick_Register_Masks S32_SysTick Register Masks
 * @{
 */

/* CSR Bit Fields */
#line 9395 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RVR Bit Fields */




/* CVR Bit Fields */




/* CALIB Bit Fields */
#line 9418 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group S32_SysTick_Register_Masks */


/*!
 * @}
 */ /* end of group S32_SysTick_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- SCG Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SCG_Peripheral_Access_Layer SCG Peripheral Access Layer
 * @{
 */


/** SCG - Size of Registers Arrays */

/** SCG - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< Parameter Register, offset: 0x4 */
       uint8_t RESERVED_0[8];
  volatile const  uint32_t CSR;                               /**< Clock Status Register, offset: 0x10 */
  volatile uint32_t RCCR;                              /**< Run Clock Control Register, offset: 0x14 */
  volatile uint32_t VCCR;                              /**< VLPR Clock Control Register, offset: 0x18 */
       uint8_t RESERVED_1[4];
  volatile uint32_t CLKOUTCNFG;                        /**< SCG CLKOUT Configuration Register, offset: 0x20 */
       uint8_t RESERVED_2[220];
  volatile uint32_t SOSCCSR;                           /**< System OSC Control Status Register, offset: 0x100 */
  volatile uint32_t SOSCDIV;                           /**< System OSC Divide Register, offset: 0x104 */
  volatile uint32_t SOSCCFG;                           /**< System Oscillator Configuration Register, offset: 0x108 */
       uint8_t RESERVED_3[244];
  volatile uint32_t SIRCCSR;                           /**< Slow IRC Control Status Register, offset: 0x200 */
  volatile uint32_t SIRCDIV;                           /**< Slow IRC Divide Register, offset: 0x204 */
  volatile uint32_t SIRCCFG;                           /**< Slow IRC Configuration Register, offset: 0x208 */
       uint8_t RESERVED_4[244];
  volatile uint32_t FIRCCSR;                           /**< Fast IRC Control Status Register, offset: 0x300 */
  volatile uint32_t FIRCDIV;                           /**< Fast IRC Divide Register, offset: 0x304 */
  volatile uint32_t FIRCCFG;                           /**< Fast IRC Configuration Register, offset: 0x308 */
} SCG_Type, *SCG_MemMapPtr;

 /** Number of instances of the SCG module. */



/* SCG - Peripheral instance base addresses */
/** Peripheral SCG base address */

/** Peripheral SCG base pointer */

/** Array initializer of SCG peripheral base addresses */

/** Array initializer of SCG peripheral base pointers */

 /** Number of interrupt vector arrays for the SCG module. */

 /** Number of interrupt channels for the SCG module. */

/** Interrupt vectors for the SCG peripheral type */


/* ----------------------------------------------------------------------------
   -- SCG Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SCG_Register_Masks SCG Register Masks
 * @{
 */

/* VERID Bit Fields */




/* PARAM Bit Fields */
#line 9508 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CSR Bit Fields */
#line 9525 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* RCCR Bit Fields */
#line 9542 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* VCCR Bit Fields */
#line 9559 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CLKOUTCNFG Bit Fields */




/* SOSCCSR Bit Fields */
#line 9593 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SOSCDIV Bit Fields */
#line 9602 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SOSCCFG Bit Fields */
#line 9615 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SIRCCSR Bit Fields */
#line 9640 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SIRCDIV Bit Fields */
#line 9649 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SIRCCFG Bit Fields */




/* FIRCCSR Bit Fields */
#line 9679 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FIRCDIV Bit Fields */
#line 9688 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FIRCCFG Bit Fields */





/*!
 * @}
 */ /* end of group SCG_Register_Masks */


/*!
 * @}
 */ /* end of group SCG_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- SIM Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SIM_Peripheral_Access_Layer SIM Peripheral Access Layer
 * @{
 */


/** SIM - Size of Registers Arrays */

/** SIM - Register Layout Typedef */
typedef struct {
       uint8_t RESERVED_0[4];
  volatile uint32_t CHIPCTL;                           /**< Chip Control register, offset: 0x4 */
       uint8_t RESERVED_1[4];
  volatile uint32_t FTMOPT0;                           /**< FTM Option Register 0, offset: 0xC */
  volatile uint32_t LPOCLKS;                           /**< LPO Clock Select Register, offset: 0x10 */
       uint8_t RESERVED_2[4];
  volatile uint32_t ADCOPT;                            /**< ADC Options Register, offset: 0x18 */
  volatile uint32_t FTMOPT1;                           /**< FTM Option Register 1, offset: 0x1C */
  volatile uint32_t MISCTRL0;                          /**< Miscellaneous control register 0, offset: 0x20 */
  volatile const  uint32_t SDID;                              /**< System Device Identification Register, offset: 0x24 */
       uint8_t RESERVED_3[24];
  volatile uint32_t PLATCGC;                           /**< Platform Clock Gating Control Register, offset: 0x40 */
       uint8_t RESERVED_4[8];
  volatile uint32_t FCFG1;                             /**< Flash Configuration Register 1, offset: 0x4C */
       uint8_t RESERVED_5[4];
  volatile const  uint32_t UIDH;                              /**< Unique Identification Register High, offset: 0x54 */
  volatile const  uint32_t UIDMH;                             /**< Unique Identification Register Mid-High, offset: 0x58 */
  volatile const  uint32_t UIDML;                             /**< Unique Identification Register Mid Low, offset: 0x5C */
  volatile const  uint32_t UIDL;                              /**< Unique Identification Register Low, offset: 0x60 */
       uint8_t RESERVED_6[4];
  volatile uint32_t CLKDIV4;                           /**< System Clock Divider Register 4, offset: 0x68 */
  volatile uint32_t MISCTRL1;                          /**< Miscellaneous Control register 1, offset: 0x6C */
} SIM_Type, *SIM_MemMapPtr;

 /** Number of instances of the SIM module. */



/* SIM - Peripheral instance base addresses */
/** Peripheral SIM base address */

/** Peripheral SIM base pointer */

/** Array initializer of SIM peripheral base addresses */

/** Array initializer of SIM peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- SIM Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SIM_Register_Masks SIM Register Masks
 * @{
 */

/* CHIPCTL Bit Fields */
#line 9806 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FTMOPT0 Bit Fields */
#line 9839 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* LPOCLKS Bit Fields */
#line 9856 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* ADCOPT Bit Fields */
#line 9881 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FTMOPT1 Bit Fields */
#line 9922 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MISCTRL0 Bit Fields */
#line 9947 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* SDID Bit Fields */
#line 9976 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PLATCGC Bit Fields */
#line 10001 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* FCFG1 Bit Fields */
#line 10010 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* UIDH Bit Fields */




/* UIDMH Bit Fields */




/* UIDML Bit Fields */




/* UIDL Bit Fields */




/* CLKDIV4 Bit Fields */
#line 10043 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* MISCTRL1 Bit Fields */





/*!
 * @}
 */ /* end of group SIM_Register_Masks */


/*!
 * @}
 */ /* end of group SIM_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- SMC Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SMC_Peripheral_Access_Layer SMC Peripheral Access Layer
 * @{
 */


/** SMC - Size of Registers Arrays */

/** SMC - Register Layout Typedef */
typedef struct {
  volatile const  uint32_t VERID;                             /**< SMC Version ID Register, offset: 0x0 */
  volatile const  uint32_t PARAM;                             /**< SMC Parameter Register, offset: 0x4 */
  volatile uint32_t PMPROT;                            /**< Power Mode Protection register, offset: 0x8 */
  volatile uint32_t PMCTRL;                            /**< Power Mode Control register, offset: 0xC */
  volatile uint32_t STOPCTRL;                          /**< Stop Control Register, offset: 0x10 */
  volatile const  uint32_t PMSTAT;                            /**< Power Mode Status register, offset: 0x14 */
} SMC_Type, *SMC_MemMapPtr;

 /** Number of instances of the SMC module. */



/* SMC - Peripheral instance base addresses */
/** Peripheral SMC base address */

/** Peripheral SMC base pointer */

/** Array initializer of SMC peripheral base addresses */

/** Array initializer of SMC peripheral base pointers */


/* ----------------------------------------------------------------------------
   -- SMC Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup SMC_Register_Masks SMC Register Masks
 * @{
 */

/* VERID Bit Fields */
#line 10117 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PARAM Bit Fields */
#line 10134 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* PMPROT Bit Fields */




/* PMCTRL Bit Fields */
#line 10152 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* STOPCTRL Bit Fields */




/* PMSTAT Bit Fields */





/*!
 * @}
 */ /* end of group SMC_Register_Masks */


/*!
 * @}
 */ /* end of group SMC_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- TRGMUX Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup TRGMUX_Peripheral_Access_Layer TRGMUX Peripheral Access Layer
 * @{
 */


/** TRGMUX - Size of Registers Arrays */


/** TRGMUX - Register Layout Typedef */
typedef struct {
  volatile uint32_t TRGMUXn[26u];     /**< TRGMUX DMAMUX0 Register..TRGMUX LPTMR0 Register, array offset: 0x0, array step: 0x4 */
} TRGMUX_Type, *TRGMUX_MemMapPtr;

 /** Number of instances of the TRGMUX module. */



/* TRGMUX - Peripheral instance base addresses */
/** Peripheral TRGMUX base address */

/** Peripheral TRGMUX base pointer */

/** Array initializer of TRGMUX peripheral base addresses */

/** Array initializer of TRGMUX peripheral base pointers */


/* TRGMUX index offsets */
#line 10222 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/* ----------------------------------------------------------------------------
   -- TRGMUX Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup TRGMUX_Register_Masks TRGMUX Register Masks
 * @{
 */

/* TRGMUXn Bit Fields */
#line 10253 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group TRGMUX_Register_Masks */


/*!
 * @}
 */ /* end of group TRGMUX_Peripheral_Access_Layer */


/* ----------------------------------------------------------------------------
   -- WDOG Peripheral Access Layer
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup WDOG_Peripheral_Access_Layer WDOG Peripheral Access Layer
 * @{
 */


/** WDOG - Size of Registers Arrays */

/** WDOG - Register Layout Typedef */
typedef struct {
  volatile uint32_t CS;                                /**< Watchdog Control and Status Register, offset: 0x0 */
  volatile uint32_t CNT;                               /**< Watchdog Counter Register, offset: 0x4 */
  volatile uint32_t TOVAL;                             /**< Watchdog Timeout Value Register, offset: 0x8 */
  volatile uint32_t WIN;                               /**< Watchdog Window Register, offset: 0xC */
} WDOG_Type, *WDOG_MemMapPtr;

 /** Number of instances of the WDOG module. */



/* WDOG - Peripheral instance base addresses */
/** Peripheral WDOG base address */

/** Peripheral WDOG base pointer */

/** Array initializer of WDOG peripheral base addresses */

/** Array initializer of WDOG peripheral base pointers */

 /** Number of interrupt vector arrays for the WDOG module. */

 /** Number of interrupt channels for the WDOG module. */

/** Interrupt vectors for the WDOG peripheral type */


/* ----------------------------------------------------------------------------
   -- WDOG Register Masks
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup WDOG_Register_Masks WDOG Register Masks
 * @{
 */

/* CS Bit Fields */
#line 10370 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* CNT Bit Fields */
#line 10379 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* TOVAL Bit Fields */
#line 10388 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"
/* WIN Bit Fields */
#line 10397 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/*!
 * @}
 */ /* end of group WDOG_Register_Masks */


/*!
 * @}
 */ /* end of group WDOG_Peripheral_Access_Layer */


/*!
 * @}
 */ /* end of group Peripheral_access_layer_S32K118 */


/* ----------------------------------------------------------------------------
   -- Backward Compatibility for S32K118
   ---------------------------------------------------------------------------- */

/*!
 * @addtogroup Backward_Compatibility_Symbols_S32K118 Backward Compatibility for S32K118
 * @{
 */

/* No backward compatibility issues. */

/*!
 * @}
 */ /* end of group Backward_Compatibility_Symbols_S32K118 */


#line 10437 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118.h"

/* S32K118.h, eof. */
#line 135 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"
        /* CPU specific feature definitions */
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"
/*
 * Copyright 2018-2020 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

/*!
 * @file S32K118_features.h
 * @brief Chip specific module features
 *
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.3, Global typedef not referenced.
 * Type used only in some modules of the SDK.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.4, tag unused outside of typedefs
 * Tag defined specifically for typedef
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Global macro not referenced.
 * The macros defined are used to define features for each driver, so this might be reported
 * when the analysis is made only on one driver.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
 * These are very simple macros used for abstracting hw implementation.
 * They help make the code easy to understand.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.1, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.2, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.4, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.5, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 */





/* ERRATA sections*/

/* @brief E10792: LPI2C: Slave Transmit Data Flag may incorrectly read as one when TXCFG is zero.
 * Interrupts for transfer data should be enabled after the address valid event is detected and
 * disabled at the end of the transfer. */


/* @brief Number of cores. */


/* SOC module features */

/* @brief PORT availability on the SoC. */




/* @brief Slow IRC high range clock frequency. */


/* @brief Fast IRC trimmed clock frequency(48MHz). */

/* @brief VECTKEY value so that AIRCR register write is not ignored. */


/* FLASH module features */

/* @brief Is of type FTFA. */

/* @brief Is of type FTFC. */

/* @brief Is of type FTFE. */

/* @brief Is of type FTFL. */

/* @brief Is of type FTFM. */

/* @brief Has flags indicating the status of the FlexRAM (register bits FCNFG[EEERDY], FCNFG[RAMRDY] and FCNFG[PFLSH]). */

/* @brief Has program flash swapping status flag (register bit FCNFG[SWAP]). */

/* @brief Has EEPROM region protection (register FEPROT). */

/* @brief Has data flash region protection (register FDPROT). */

/* @brief P-Flash block count. */

/* @brief P-Flash block size. */

/* @brief P-Flash sector size. */

/* @brief P-Flash write unit size. */

/* @brief P-Flash block swap feature. */

/* @brief Has FlexNVM memory. */

/* @brief FlexNVM block count. */

/* @brief FlexNVM block size. */

/* @brief FlexNVM sector size. */

/* @brief FlexNVM write unit size. */

/* @brief FlexNVM start address. (Valid only if FlexNVM is available.) */

/* @brief Has FlexRAM memory. */

/* @brief FlexRAM size. */

/* @brief FlexRAM start address. (Valid only if FlexRAM is available.) */

/* @brief Has 0x00 Read 1s Block command. */

/* @brief Has 0x01 Read 1s Section command. */

/* @brief Has 0x02 Program Check command. */

/* @brief Has 0x03 Read Resource command. */

/* @brief Has 0x06 Program Longword command. */

/* @brief Has 0x07 Program Phrase command. */

/* @brief Has 0x08 Erase Flash Block command. */

/* @brief Has 0x09 Erase Flash Sector command. */

/* @brief Has 0x0B Program Section command. */

/* @brief Has 0x40 Read 1s All Blocks command. */

/* @brief Has 0x41 Read Once command. */

/* @brief Has 0x43 Program Once command. */

/* @brief Has 0x44 Erase All Blocks command. */

/* @brief Has 0x45 Verify Backdoor Access Key command. */

/* @brief Has 0x46 Swap Control command. */

/* @brief Has 0x49 Erase All Blocks unsecure command. */

/* @brief Has 0x80 Program Partition command. */

/* @brief Has 0x81 Set FlexRAM Function command. */

/* @brief P-Flash Erase/Read 1st all block command address alignment. */

/* @brief P-Flash Erase sector command address alignment. */

/* @brief P-Flash Program/Verify section command address alignment. */

/* @brief P-Flash Read resource command address alignment. */

/* @brief P-Flash Program check command address alignment. */

/* @brief P-Flash Program check command address alignment. */

/* @brief FlexNVM Erase/Read 1st all block command address alignment. */

/* @brief FlexNVM Erase sector command address alignment. */

/* @brief FlexNVM Program/Verify section command address alignment. */

/* @brief FlexNVM Read resource command address alignment. */

/* @brief FlexNVM Program check command address alignment. */

/* @brief FlexNVM partition code 0000 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 0001 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 0010 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 0011 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 0100 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 0101 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 0110 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 0111 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1000 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1001 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1010 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1011 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1100 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1101 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1110 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief FlexNVM partition code 1111 mapping to data flash size in bytes (0xFFFFFFFF = reserved). */

/* @brief Emulated EEPROM size code 0000 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 0001 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 0010 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 0011 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 0100 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 0101 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 0110 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 0111 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1000 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1001 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1010 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1011 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1100 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1101 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1110 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Emulated EEPROM size code 1111 mapping to emulated EEPROM size in bytes (0xFFFF = reserved). */

/* @brief Has the detection of uncorrected ECC errors. */

/* @brief Has the interrupt double bit fault detect. */


/* SMC module features */

/* @brief Has stop option (register bit STOPCTRL[STOPO]). */

/* @brief Has partial stop option (register bit STOPCTRL[PSTOPO]). */

/* @brief Has WAIT and VLPW options. */

/* @brief Has high speed run mode (register bit PMPROT[AHSRUN]). */


/* RCM module feature */

/* @brief Has existence of CMU loss of clock as reset source */

/* @brief Has CMU loss of clock as reset source */

/* @brief Has sticky CMU loss of clock as reset source */


/* WDOG module features */

/* @brief The 32-bit value used for unlocking the WDOG. */

/* @brief The 32-bit value used for resetting the WDOG counter. */

/* @brief The reset value of the timeout register. */

/* @brief The value minimum of the timeout register. */

/* @brief The reset value of the window register. */

/* @brief The mask of the reserved bit in the CS register. */

/* @brief The value used to set WDOG source clock from LPO. */

/* @brief The first 16-bit value used for unlocking the WDOG. */

/* @brief The second 16-bit value used for unlocking the WDOG. */

/* @brief The first 16-bit value used for resetting the WDOG counter. */

/* @brief The second 16-bit value used for resetting the WDOG counter. */

/* @brief Default reset value of the CS register. */


/* Interrupt module features */

/* @brief Lowest interrupt request number. */

/* @brief Highest interrupt request number. */

/**< Number of priority bits implemented in the NVIC */

/* @brief Has software interrupt. */

/* @brief Has pending interrupt state. */

/* @brief Has active interrupt state. */

/* @brief Multicore support for interrupts */

/* @brief Registers in which the start of interrupt vector table needs to be configured */


/* FTM module features */

/* @brief Number of PWM channels */

/* @brief Number of fault channels */

/* @brief Width of control channel */

/* @brief Output channel offset */

/* @brief Max counter value */

/* @brief Input capture for single shot */

/* @brief Dithering has supported on the generated PWM signals */

/* @brief Number of interrupt vector for channels of the FTM module. */


/* LPIT module features */

/*! @brief Number of interrupt vector for channels of the LPIT module. */

/*! @brief Clock names for LPIT. */


/* LPI2C module features */

/* @brief DMA instance used for LPI2C module */


/* @brief EDMA requests for LPI2C module. */

/* @brief PCC clocks for LPI2C module. */


/* @brief Disable high-speed and ultra-fast operating modes for S32K14x. */




/* LPI2C module features */

/* @brief DMA instance used for LPI2C module */


/* @brief EDMA requests for LPI2C module. */

/* @brief PCC clocks for LPI2C module. */


/* @brief Disable high-speed and ultra-fast operating modes for S32K14x. */




/* MSCM module features */

/* @brief Has interrupt router control registers (IRSPRCn). */

/* @brief Has directed CPU interrupt routerregisters (IRCPxxx). */


/* CSEc module features */

/*! @brief CSE_PRAM offset of the page length parameter used by the following
commands: CMD_ENC_ECB, CMD_ENC_CBC, CMD_DEC_ECB, CMD_DEC_CBC, CMD_MP_COMPRESS */

/*! @brief CSE_PRAM offset of the message length parameter used by the following
commands: CMD_GENERATE_MAC, CMD_VERIFY_MAC (both copy and pointer methods) */

/*! @brief CSE_PRAM offset of the MAC length parameter used by the following
commands: CMD_VERIFY_MAC (both copy and pointer methods) */

/*! @brief CSE_PRAM offset of the boot size parameter used by the following
commands: CMD_BOOT_DEFINE */

/*! @brief CSE_PRAM offset of the boot flavor parameter used by the following
commands: CMD_BOOT_DEFINE */

/*! @brief CSE_PRAM offset of the Flash start address parameter used by the
following commands: CMD_GENERATE_MAC, CMD_VERIFY_MAC (pointer method) */

/*! @brief CSE_PRAM offset of the verification status parameter used by the
following commands: CMD_VERIFY_MAC (both copy and pointer methods) */

/*! @brief CSE_PRAM offset of the error bits field contained by all commands */

/*! @brief CSE_PRAM offset of the SREG parameter used by the following commands:
CMD_GET_ID */


/*! @brief Macro that enables the use of FTFM flash module on a platform */


/*! @brief CSE_PRAM offset of page 0 */

/*! @brief CSE_PRAM offset of page 1 */

/*! @brief CSE_PRAM offset of page 2 */

/*! @brief CSE_PRAM offset of page 3 */

/*! @brief CSE_PRAM offset of page 4 */

/*! @brief CSE_PRAM offset of page 5 */

/*! @brief CSE_PRAM offset of page 6 */

/*! @brief CSE_PRAM offset of page 7 */


/* CRC module features */

/* @brief CRC module use for S32K. */

/* @brief Default CRC bit width */

/* @brief Default CRC read transpose */

/* @brief Default CRC write transpose */

/* @brief Default polynomial 0x1021U */

/* @brief Default seed value is 0xFFFFU */


/* PORT module features */
/*! @brief PORT Used for setting Pins */

/* @brief Has control lock (register bit PCR[LK]). */

/* @brief Has open drain control (register bit PCR[ODE]). */

/* @brief Has digital filter (registers DFER, DFCR and DFWR). */

/* @brief Has trigger output to trigger other peripherals (register bit field PCR[IRQC] values). */

/* @brief Has setting flag only (register bit field PCR[IRQC] values). */

/* @brief Has over-current feature (register bit field PCR[OCIE] values). */

/* @brief Has pull resistor selection available. */

/* @brief Has slew rate control (register bit PCR[SRE]). */

/* @brief Has passive filter (register bit field PCR[PFE]). */

/* @brief Has drive strength (register bit PCR[DSE]). */

/* @brief Has drive strength control bits*/

/* @brief Has port input disable control bits*/

/* @brief SIM_CHIPCTL_ADC_INTERLEAVE_EN bit is not available */


/* MPU module features */

/* @brief Specifies hardware revision level. */

/* @brief Has process identifier support. */

/* @brief The number of master has process identifier. */

/* @brief Specifies total number of bus masters. */

/* @brief Specifies maximum number of masters which have separated
privilege rights for user and supervisor mode accesses (e.g. master0~3 in S32K1xx).
*/

/* @brief Specifies maximum number of masters which have only
read and write permissions (e.g. master4~7 in S32K1xx).
*/


/* @brief Specifies number of set access control right bits for
   masters which have separated privilege rights for user and
   supervisor mode accesses (e.g. master0~3 in S32K1xx).
*/

/* @brief Specifies number of set access control right bits for
   masters which have only read and write permissions(e.g. master4~7 in S32K1xx).
*/


/* @brief The MPU Logical Bus Master Number for core bus master. */

/* @brief The MPU Logical Bus Master Number for Debugger master. */

/* @brief The MPU Logical Bus Master Number for DMA master. */

/* @brief Specifies master number. */
#line 526 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"

/* @brief Specifies total number of slave ports. */

/* @brief The MPU Slave Port Assignment for Flash Controller and boot ROM. */

/* @brief The MPU Slave Port Assignment for SRAM, MTB, DWT and MCM. */

/* @brief The MPU Slave Port mask. */






/* @brief Supports high speed run mode. */

/* @brief Supports SPLL clock source. */


/* CMP module features */

/* @brief Comparator hard block offset control */

/* @brief Comparator fix DAC input to mux side */

/* @brief Comparator initialization delay */





















/* ISELED Pins */

#line 595 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"

#line 610 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"

#line 617 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"



/* @brief Supports LPO peripheral clock source. */

/*! @brief Clock names. */
typedef enum {
    /* Main clocks */
    CORE_CLK                     = 0u,       /*!< Core clock                     */
    BUS_CLK                      = 1u,       /*!< Bus clock                      */
    SLOW_CLK                     = 2u,       /*!< Slow clock                     */
    CLKOUT_CLK                   = 3u,       /*!< CLKOUT clock                   */

    /* Other internal clocks used by peripherals. */
    SIRC_CLK                     = 4u,       /*!< SIRC clock                     */
    FIRC_CLK                     = 5u,       /*!< FIRC clock                     */
    SOSC_CLK                     = 6u,       /*!< SOSC clock                     */
    RTC_CLKIN_CLK                = 8u,       /*!< RTC_CLKIN clock                */
    SCG_CLKOUT_CLK               = 9u,       /*!< SCG CLK_OUT clock              */
    SIRCDIV1_CLK                 = 10u,      /*!< SIRCDIV1 functional clock      */
    SIRCDIV2_CLK                 = 11u,      /*!< SIRCDIV2 functional clock      */
    FIRCDIV1_CLK                 = 12u,      /*!< FIRCDIV1 functional clock      */
    FIRCDIV2_CLK                 = 13u,      /*!< FIRCDIV2 functional clock      */
    SOSCDIV1_CLK                 = 14u,      /*!< SOSCDIV1 functional clock      */
    SOSCDIV2_CLK                 = 15u,      /*!< SOSCDIV2 functional clock      */

    SCG_END_OF_CLOCKS            = 18u,      /*!< End of SCG clocks              */

    /* SIM clocks */
    SIM_FTM0_CLOCKSEL            = 21u,      /*!< FTM0 External Clock Pin Select */
    SIM_FTM1_CLOCKSEL            = 22u,      /*!< FTM1 External Clock Pin Select */
    SIM_CLKOUTSELL               = 23u,      /*!< CLKOUT Select                  */
    SIM_RTCCLK_CLK               = 24u,      /*!< RTCCLK clock                   */
    SIM_LPO_CLK                  = 25u,      /*!< LPO clock                      */
    SIM_LPO_1K_CLK               = 26u,      /*!< LPO 1KHz clock                 */
    SIM_LPO_32K_CLK              = 27u,      /*!< LPO 32KHz clock                */
    SIM_LPO_128K_CLK             = 28u,      /*!< LPO 128KHz clock               */
    SIM_EIM_CLK                  = 29u,      /*!< EIM clock source               */
    SIM_ERM_CLK                  = 30u,      /*!< ERM clock source               */
    SIM_DMA_CLK                  = 31u,      /*!< DMA clock source               */
    SIM_MPU_CLK                  = 32u,      /*!< MPU clock source               */
    SIM_MSCM_CLK                 = 33u,      /*!< MSCM clock source              */
    SIM_END_OF_CLOCKS            = 34u,      /*!< End of SIM clocks              */

    CMP0_CLK                     = 41u,      /*!< CMP0 clock source              */
    CRC0_CLK                     = 42u,      /*!< CRC0 clock source              */
    DMAMUX0_CLK                  = 43u,      /*!< DMAMUX0 clock source           */
    PORTA_CLK                    = 44u,      /*!< PORTA clock source             */
    PORTB_CLK                    = 45u,      /*!< PORTB clock source             */
    PORTC_CLK                    = 46u,      /*!< PORTC clock source             */
    PORTD_CLK                    = 47u,      /*!< PORTD clock source             */
    PORTE_CLK                    = 48u,      /*!< PORTE clock source             */
    RTC0_CLK                     = 49u,      /*!< RTC0 clock source              */
    PCC_END_OF_BUS_CLOCKS        = 50u,      /*!< End of BUS clocks              */
    FlexCAN0_CLK                 = 51u,      /*!< FlexCAN0 clock source          */
    PDB0_CLK                     = 52u,      /*!< PDB0 clock source              */
    PCC_END_OF_SYS_CLOCKS        = 53u,      /*!< End of SYS clocks              */
    FTFC0_CLK                    = 54u,      /*!< FTFC0 clock source             */
    PCC_END_OF_SLOW_CLOCKS       = 55u,      /*!< End of SLOW clocks             */
    FTM0_CLK                     = 56u,      /*!< FTM0 clock source              */
    FTM1_CLK                     = 57u,      /*!< FTM1 clock source              */
    PCC_END_OF_ASYNCH_DIV1_CLOCKS= 58u,      /*!< End of ASYNCH DIV1 clocks      */
    ADC0_CLK                     = 59u,      /*!< ADC0 clock source              */
    FLEXIO0_CLK                  = 60u,      /*!< FLEXIO0 clock source           */
    LPI2C0_CLK                   = 61u,      /*!< LPI2C0 clock source            */
    LPIT0_CLK                    = 62u,      /*!< LPIT0 clock source             */
    LPSPI0_CLK                   = 63u,      /*!< LPSPI0 clock source            */
    LPSPI1_CLK                   = 64u,      /*!< LPSPI1 clock source            */
    LPTMR0_CLK                   = 65u,      /*!< LPTMR0 clock source            */
    LPUART0_CLK                  = 66u,      /*!< LPUART0 clock source           */
    LPUART1_CLK                  = 67u,      /*!< LPUART1 clock source           */
    PCC_END_OF_ASYNCH_DIV2_CLOCKS= 68u,      /*!< End of ASYNCH DIV2 clocks      */
    PCC_END_OF_CLOCKS            = 69u,      /*!< End of PCC clocks              */
    CLOCK_NAME_COUNT             = 70u,      /*!< The total number of entries    */
} clock_names_t;



  /*! @brief PCC clock name mappings
   *  Mappings between clock names and peripheral clock control indexes.
   *  If there is no peripheral clock control index for a clock name,
   *  then the corresponding value is PCC_INVALID_INDEX.
   */
#line 773 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"

/*! @brief Peripheral instance features
 *  List of features that are supported by a peripheral instance
 */
#line 786 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"

/*! @brief Peripheral features.
*  List of features for each clock name. If a clock name is not
*  a peripheral, no feature is supported.
*/
#line 864 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"

/* Time to wait for SIRC to stabilize (number of
 * cycles when core runs at maximum speed - 112 MHz */


/* Time to wait for FIRC to stabilize (number of
 * cycles when core runs at maximum speed - 112 MHz */


/* Time to wait for SOSC to stabilize (number of
 * cycles when core runs at maximum speed - 112 MHz */


/* Time to wait for SPLL to stabilize (number of
 * cycles when core runs at maximum speed - 112 MHz */



/*! @brief Temporary system clock source configurations.
 *         Each line represents the SYS(CORE), BUS and SLOW(FLASH) dividers
 *         for SIRC, FIRC, SOSC and SPLL clock sources.
  *
 *          SYS_CLK  BUS_CLK  SLOW_CLK
 *  SIRC       *        *         *
 *  FIRC       *        *         *
 *  SOSC       *        *         *
 *  SPLL       *        *         *
 */












#line 911 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"
/* @brief template system clock configuration in VLPR mode*/




/* DMA module features */

/* @brief Number of DMA channels. */

/* @brief Number of DMA virtual channels. */

/* @brief Number of DMA interrupt lines. */

/* @brief Number of DMA virtual interrupt lines. */

/* @brief Number of DMA error interrupt lines. */

/* @brief Number of DMA virtual error interrupt lines. */

/* @brief DMA module has error interrupt. */

/* @brief DMA module separate interrupt lines for each channel */

/* @brief Conversion from channel index to DCHPRI index. */

/* @brief DMA channel groups count. */

/* @brief Clock name for DMA */

/* @brief DMA channel width based on number of TCDs: 2^N, N=4,5,... */

/* @brief DMA channel to instance */

/* @brief DMA virtual channel to channel */

/* @brief DMA supports the following particular channel priorities: */

/* @brief DMA supports bus bandwidth control. */


/* DMAMUX module features */

/* @brief DMAMUX peripheral is available in silicon. */

/* @brief Number of DMA channels. */

/* @brief Has the periodic trigger capability */

/* @brief Conversion from request source to the actual DMAMUX channel */

/* @brief Mapping between request source and DMAMUX instance */

/* @brief Conversion from eDMA channel index to DMAMUX channel. */

/* @brief Conversion from DMAMUX channel DMAMUX register index. */

/* @brief Clock names for DMAMUX. */


/*!
 * @brief Structure for the DMA hardware request
 *
 * Defines the structure for the DMA hardware request collections. The user can configure the
 * hardware request into DMAMUX to trigger the DMA transfer accordingly. The index
 * of the hardware request varies according  to the to SoC.
 */
typedef enum {
    EDMA_REQ_DISABLED = 0U,
    EDMA_REQ_LPUART0_RX = 2U,
    EDMA_REQ_LPUART0_TX = 3U,
    EDMA_REQ_LPUART1_RX = 4U,
    EDMA_REQ_LPUART1_TX = 5U,
    EDMA_REQ_FLEXIO_SHIFTER0 = 10U,
    EDMA_REQ_FLEXIO_SHIFTER1 = 11U,
    EDMA_REQ_FLEXIO_SHIFTER2 = 12U,
    EDMA_REQ_FLEXIO_SHIFTER3 = 13U,
    EDMA_REQ_LPSPI0_RX = 14U,
    EDMA_REQ_LPSPI0_TX = 15U,
    EDMA_REQ_LPSPI1_RX = 16U,
    EDMA_REQ_LPSPI1_TX = 17U,
    EDMA_REQ_FTM1_CHANNEL_0 = 20U,
    EDMA_REQ_FTM1_CHANNEL_1 = 21U,
    EDMA_REQ_FTM1_CHANNEL_2 = 22U,
    EDMA_REQ_FTM1_CHANNEL_3 = 23U,
    EDMA_REQ_FTM1_CHANNEL_4 = 24U,
    EDMA_REQ_FTM1_CHANNEL_5 = 25U,
    EDMA_REQ_FTM1_CHANNEL_6 = 26U,
    EDMA_REQ_FTM1_CHANNEL_7 = 27U,
    EDMA_REQ_FTM0_OR_CH0_CH7 = 36U,
    EDMA_REQ_ADC0 = 42U,
    EDMA_REQ_LPI2C0_RX = 44U,
    EDMA_REQ_LPI2C0_TX = 45U,
    EDMA_REQ_PDB0 = 46U,
    EDMA_REQ_CMP0 = 48U,
    EDMA_REQ_PORTA = 49U,
    EDMA_REQ_PORTB = 50U,
    EDMA_REQ_PORTC = 51U,
    EDMA_REQ_PORTD = 52U,
    EDMA_REQ_PORTE = 53U,
    EDMA_REQ_FLEXCAN0 = 54U,
    EDMA_REQ_LPTMR0 = 59U,
    EDMA_REQ_DMAMUX_ALWAYS_ENABLED0 = 62U,
    EDMA_REQ_DMAMUX_ALWAYS_ENABLED1 = 63U
} dma_request_source_t;


/* TRGMUX module features */
/*!
 * @brief Enumeration for trigger source module of TRGMUX
 *
 * Describes all possible inputs (trigger sources) of the TRGMUX IP
 * This enumeration depends on the supported instances in device
 */
enum trgmux_trigger_source_e
{
    TRGMUX_TRIG_SOURCE_DISABLED             = 0U,
    TRGMUX_TRIG_SOURCE_VDD                  = 1U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN0           = 2U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN1           = 3U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN2           = 4U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN3           = 5U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN4           = 6U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN5           = 7U,
	TRGMUX_TRIG_SOURCE_TRGMUX_IN6           = 8U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN7           = 9U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN8           = 10U,
    TRGMUX_TRIG_SOURCE_TRGMUX_IN9           = 11U,
    TRGMUX_TRIG_SOURCE_CMP0_OUT             = 14U,
    TRGMUX_TRIG_SOURCE_LPIT_CH0             = 17U,
    TRGMUX_TRIG_SOURCE_LPIT_CH1             = 18U,
    TRGMUX_TRIG_SOURCE_LPIT_CH2             = 19U,
    TRGMUX_TRIG_SOURCE_LPIT_CH3             = 20U,
    TRGMUX_TRIG_SOURCE_LPTMR0               = 21U,
    TRGMUX_TRIG_SOURCE_FTM0_INIT_TRIG       = 22U,
    TRGMUX_TRIG_SOURCE_FTM0_EXT_TRIG        = 23U,
    TRGMUX_TRIG_SOURCE_FTM1_INIT_TRIG       = 24U,
    TRGMUX_TRIG_SOURCE_FTM1_EXT_TRIG        = 25U,
    TRGMUX_TRIG_SOURCE_ADC0_SC1A_COCO       = 30U,
    TRGMUX_TRIG_SOURCE_ADC0_SC1B_COCO       = 31U,
    TRGMUX_TRIG_SOURCE_PDB0_CH0_TRIG        = 34U,
    TRGMUX_TRIG_SOURCE_PDB0_PULSE_OUT       = 36U,
    TRGMUX_TRIG_SOURCE_RTC_ALARM            = 43U,
    TRGMUX_TRIG_SOURCE_RTC_SECOND           = 44U,
    TRGMUX_TRIG_SOURCE_FLEXIO_TRIG0         = 45U,
    TRGMUX_TRIG_SOURCE_FLEXIO_TRIG1         = 46U,
    TRGMUX_TRIG_SOURCE_FLEXIO_TRIG2         = 47U,
    TRGMUX_TRIG_SOURCE_FLEXIO_TRIG3         = 48U,
    TRGMUX_TRIG_SOURCE_LPUART0_RX_DATA      = 49U,
    TRGMUX_TRIG_SOURCE_LPUART0_TX_DATA      = 50U,
    TRGMUX_TRIG_SOURCE_LPUART0_RX_IDLE      = 51U,
    TRGMUX_TRIG_SOURCE_LPUART1_RX_DATA      = 52U,
    TRGMUX_TRIG_SOURCE_LPUART1_TX_DATA      = 53U,
    TRGMUX_TRIG_SOURCE_LPUART1_RX_IDLE      = 54U,
    TRGMUX_TRIG_SOURCE_LPI2C0_MASTER_TRIG   = 55U,
    TRGMUX_TRIG_SOURCE_LPI2C0_SLAVE_TRIG    = 56U,
    TRGMUX_TRIG_SOURCE_LPSPI0_FRAME         = 59U,
    TRGMUX_TRIG_SOURCE_LPSPI0_RX_DATA       = 60U,
	TRGMUX_TRIG_SOURCE_LPSPI1_FRAME         = 61U,
    TRGMUX_TRIG_SOURCE_LPSPI1_RX_DATA       = 62U,
    TRGMUX_TRIG_SOURCE_SIM_SW_TRIG          = 63U,
};

/*!
 * @brief Enumeration for target module of TRGMUX
 *
 * Describes all possible outputs (target modules) of the TRGMUX IP
 * This enumeration depends on the supported instances in device
 */
enum trgmux_target_module_e
{
    TRGMUX_TARGET_MODULE_DMA_CH0            = 0U,
    TRGMUX_TARGET_MODULE_DMA_CH1            = 1U,
    TRGMUX_TARGET_MODULE_DMA_CH2            = 2U,
    TRGMUX_TARGET_MODULE_DMA_CH3            = 3U,
    TRGMUX_TARGET_MODULE_TRGMUX_OUT0        = 4U,
    TRGMUX_TARGET_MODULE_TRGMUX_OUT1        = 5U,
    TRGMUX_TARGET_MODULE_TRGMUX_OUT2        = 6U,
    TRGMUX_TARGET_MODULE_TRGMUX_OUT3        = 7U,
	TRGMUX_TARGET_MODULE_TRGMUX_OUT4        = 8U,
    TRGMUX_TARGET_MODULE_TRGMUX_OUT5        = 9U,
    TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA0    = 12U,
    TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA1    = 13U,
    TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA2    = 14U,
    TRGMUX_TARGET_MODULE_ADC0_ADHWT_TLA3    = 15U,
    TRGMUX_TARGET_MODULE_CMP0_SAMPLE        = 28U,
    TRGMUX_TARGET_MODULE_FTM0_HWTRIG0       = 40U,
    TRGMUX_TARGET_MODULE_FTM0_FAULT0        = 41U,
    TRGMUX_TARGET_MODULE_FTM0_FAULT1        = 42U,
    TRGMUX_TARGET_MODULE_FTM0_FAULT2        = 43U,
    TRGMUX_TARGET_MODULE_FTM1_HWTRIG0       = 44U,
    TRGMUX_TARGET_MODULE_FTM1_FAULT0        = 45U,
    TRGMUX_TARGET_MODULE_FTM1_FAULT1        = 46U,
    TRGMUX_TARGET_MODULE_FTM1_FAULT2        = 47U,
    TRGMUX_TARGET_MODULE_PDB0_TRG_IN        = 56U,
    TRGMUX_TARGET_MODULE_FLEXIO_TRG_TIM0    = 68U,
    TRGMUX_TARGET_MODULE_FLEXIO_TRG_TIM1    = 69U,
    TRGMUX_TARGET_MODULE_FLEXIO_TRG_TIM2    = 70U,
    TRGMUX_TARGET_MODULE_FLEXIO_TRG_TIM3    = 71U,
    TRGMUX_TARGET_MODULE_LPIT_TRG_CH0       = 72U,
    TRGMUX_TARGET_MODULE_LPIT_TRG_CH1       = 73U,
    TRGMUX_TARGET_MODULE_LPIT_TRG_CH2       = 74U,
    TRGMUX_TARGET_MODULE_LPIT_TRG_CH3       = 75U,
    TRGMUX_TARGET_MODULE_LPUART0_TRG        = 76U,
    TRGMUX_TARGET_MODULE_LPUART1_TRG        = 80U,
    TRGMUX_TARGET_MODULE_LPI2C0_TRG         = 84U,
    TRGMUX_TARGET_MODULE_LPSPI0_TRG         = 92U,
	TRGMUX_TARGET_MODULE_LPSPI1_TRG         = 96U,
    TRGMUX_TARGET_MODULE_LPTMR0_ALT0        = 100U,
};

/* @brief Constant array storing the value of all TRGMUX output(target module) identifiers */
#line 1163 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\S32K118_features.h"


/* LPSPI module features */
/* @brief Initial value for state structure */

/* @brief Clock indexes for LPSPI clock */


/* FlexIO module features */

/* @brief Define the maximum number of shifters for any FlexIO instance. */

/* @brief Define DMA request names for Flexio. */





/* LPUART module features */

/* @brief Has extended data register ED. */

/* @brief Hardware flow control (RTS, CTS) is supported. */

/* @brief Baud rate oversampling is available. */

/* @brief Baud rate oversampling is available. */

/* @brief Capacity (number of entries) of the transmit/receive FIFO (or zero if no FIFO is available). */

/* @brief Supports two match addresses to filter incoming frames. */

/* @brief Has transmitter/receiver DMA enable bits. */

/* @brief Flag clearance mask for STAT register. */

/* @brief Flag clearance mask for FIFO register. */

/* @brief Reset mask for FIFO register. */

/* @brief Default oversampling ratio. */

/* @brief Default baud rate modulo divisor. */

/* @brief Clock names for LPUART. */


/* ADC module features */

/*! @brief ADC feature flag for extended number of SC1 and R registers,
 * generically named 'alias registers' */



/*! @brief ADC feature flag for defining number of external ADC channels.
 * If each ADC instance has different number of external channels, then
 * this define is set with the maximum value. */




/*! @brief ADC number of control channels */






/*! @brief ADC default Sample Time from RM */

/*! @brief ADC default User Gain from RM */

/* @brief Max of adc clock frequency */

/* @brief Min of adc clock frequency */


/* CAN module features */

/* @brief Frames available in Rx FIFO flag shift */

/* @brief Rx FIFO warning flag shift */

/* @brief Rx FIFO overflow flag shift */

/* @brief The list contains definitions of the FD feature support on all instances */

/* @brief Has Flexible Data Rate for CAN0 */

/* @brief Maximum number of Message Buffers supported for payload size 8 for CAN0 */

/* @brief Has PE clock source select (bit field CAN_CTRL1[CLKSRC]). */

/* @brief Has DMA enable (bit field MCR[DMA]). */

/* @brief Maximum number of Message Buffers supported for payload size 8 for any of the CAN instances */

/* @brief Maximum number of Message Buffers supported for payload size 8 for any of the CAN instances */

/* @brief Has Pretending Networking mode */

/* @brief Has Stuff Bit Count Enable Bit */

/* @brief Has ISO CAN FD Enable Bit */

/* @brief Has Message Buffer Data Size Region 1 */

/* @brief Has Message Buffer Data Size Region 2 */

/* @brief DMA hardware requests for all FlexCAN instances */


/* @brief Maximum number of Message Buffers IRQs */

/* @brief Message Buffers IRQs */


/* @brief Has Wake Up Irq channels (CAN_Wake_Up_IRQS_CH_COUNT > 0u) */

/* @brief Has Self Wake Up mode */

/* @brief Has Flexible Data Rate */

/* @brief Clock name for the PE oscillator clock source */

/* @bried FlexCAN has Detection And Correction of Memory Errors */


/* LPTMR module features */

/* @brief LPTMR pulse counter input options */


/* OSIF module features */






/* PDB module features */

/* @brief PDB has instance back to back mode between PDB0 CH0 and PDB1 CH0 pre-triggers */


/* @brief PDB has inter-channel back to back mode between PDBx CH0 and PDBx CH1 pre-triggers */




/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 137 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"



#line 367 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"

#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\devassert.h"
/*
 * Copyright (c) 2015, Freescale Semiconductor, Inc.
 * Copyright 2016-2020 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */






/**
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, global macro not referenced.
 * The macro is defined to be used by drivers to validate input parameters and can be disabled.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Directive 4.9, Function-like macro defined.
 * The macros are used to validate input parameters to driver functions.
 *
 */

/**
\page Error_detection_and_reporting Error detection and reporting

S32 SDK drivers can use a mechanism to validate data coming from upper software layers (application code) by performing
a number of checks on input parameters' range or other invariants that can be statically checked (not dependent on
runtime conditions). A failed validation is indicative of a software bug in application code, therefore it is important
to use this mechanism during development.

The validation is performed by using DEV_ASSERT macro.
A default implementation of this macro is provided in this file. However, application developers can provide their own
implementation in a custom file. This requires defining the CUSTOM_DEVASSERT symbol with the specific file name in the
project configuration (for example: -DCUSTOM_DEVASSERT="custom_devassert.h")

The default implementation accommodates two behaviors, based on DEV_ERROR_DETECT symbol:
 - When DEV_ERROR_DETECT symbol is defined in the project configuration (for example: -DDEV_ERROR_DETECT), the validation
   performed by the DEV_ASSERT macro is enabled, and a failed validation triggers a software breakpoint and further execution is
   prevented (application spins in an infinite loop)
   This configuration is recommended for development environments, as it prevents further execution and allows investigating
   potential problems from the point of error detection.
 - When DEV_ERROR_DETECT symbol is not defined, the DEV_ASSERT macro is implemented as no-op, therefore disabling all validations.
   This configuration can be used to eliminate the overhead of development-time checks.

It is the application developer's responsibility to decide the error detection strategy for production code: one can opt to
disable development-time checking altogether (by not defining DEV_ERROR_DETECT symbol), or one can opt to keep the checks
in place and implement a recovery mechanism in case of a failed validation, by defining CUSTOM_DEVASSERT to point
to the file containing the custom implementation.
*/

#line 73 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\devassert.h"
    /* Assert macro does nothing */





/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 369 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWINTEGRATION\\00_BUILDSETUP\\STARTUPFILE\\CODE\\device_registers.h"



/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 23 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\LPTMR\\CODE\\lptmr_driver.h"
/*! @file lptmr_driver.h*/

/*!
 * @addtogroup lptmr_driver
 * @{
 */

/*******************************************************************************
 * Definitions
 ******************************************************************************/

/*! @brief Pulse Counter Input selection
 *  Implements : lptmr_pinselect_t_Class
 */
typedef enum {
    LPTMR_PINSELECT_TRGMUX  = 0x00u, /*!< Count pulses from TRGMUX trigger */

    LPTMR_PINSELECT_ALT1    = 0x01u, /*!< Count pulses from pin alternative 1 */

    LPTMR_PINSELECT_ALT2    = 0x02u, /*!< Count pulses from pin alternative 2 */
    LPTMR_PINSELECT_ALT3    = 0x03u  /*!< Count pulses from pin alternative 3 */
} lptmr_pinselect_t;

/*! @brief Pulse Counter input polarity
 *  Implements : lptmr_pinpolarity_t_Class
 */
typedef enum {
    LPTMR_PINPOLARITY_RISING    = 0u, /*!< Count pulse on rising edge */
    LPTMR_PINPOLARITY_FALLING   = 1u  /*!< Count pulse on falling edge */
} lptmr_pinpolarity_t;

/*! @brief Work Mode
 *  Implements : lptmr_workmode_t_Class
 */
typedef enum {
    LPTMR_WORKMODE_TIMER        = 0u, /*!< Timer */
    LPTMR_WORKMODE_PULSECOUNTER = 1u  /*!< Pulse counter */
} lptmr_workmode_t;

/*! @brief Prescaler Selection
 *  Implements : lptmr_prescaler_t_Class
 */
typedef enum {
    LPTMR_PRESCALE_2                        = 0x00u, /*!< Timer mode: prescaler 2, Glitch filter mode: invalid */
    LPTMR_PRESCALE_4_GLITCHFILTER_2         = 0x01u, /*!< Timer mode: prescaler 4, Glitch filter mode: 2 clocks */
    LPTMR_PRESCALE_8_GLITCHFILTER_4         = 0x02u, /*!< Timer mode: prescaler 8, Glitch filter mode: 4 clocks */
    LPTMR_PRESCALE_16_GLITCHFILTER_8        = 0x03u, /*!< Timer mode: prescaler 16, Glitch filter mode: 8 clocks */
    LPTMR_PRESCALE_32_GLITCHFILTER_16       = 0x04u, /*!< Timer mode: prescaler 32, Glitch filter mode: 16 clocks */
    LPTMR_PRESCALE_64_GLITCHFILTER_32       = 0x05u, /*!< Timer mode: prescaler 64, Glitch filter mode: 32 clocks */
    LPTMR_PRESCALE_128_GLITCHFILTER_64      = 0x06u, /*!< Timer mode: prescaler 128, Glitch filter mode: 64 clocks */
    LPTMR_PRESCALE_256_GLITCHFILTER_128     = 0x07u, /*!< Timer mode: prescaler 256, Glitch filter mode: 128 clocks */
    LPTMR_PRESCALE_512_GLITCHFILTER_256     = 0x08u, /*!< Timer mode: prescaler 512, Glitch filter mode: 256 clocks */
    LPTMR_PRESCALE_1024_GLITCHFILTER_512    = 0x09u, /*!< Timer mode: prescaler 1024, Glitch filter mode: 512 clocks */
    LPTMR_PRESCALE_2048_GLITCHFILTER_1024   = 0x0Au, /*!< Timer mode: prescaler 2048, Glitch filter mode: 1024 clocks */
    LPTMR_PRESCALE_4096_GLITCHFILTER_2048   = 0x0Bu, /*!< Timer mode: prescaler 4096, Glitch filter mode: 2048 clocks */
    LPTMR_PRESCALE_8192_GLITCHFILTER_4096   = 0x0Cu, /*!< Timer mode: prescaler 8192, Glitch filter mode: 4096 clocks */
    LPTMR_PRESCALE_16384_GLITCHFILTER_8192  = 0x0Du, /*!< Timer mode: prescaler 16384, Glitch filter mode: 8192 clocks */
    LPTMR_PRESCALE_32768_GLITCHFILTER_16384 = 0x0Eu, /*!< Timer mode: prescaler 32768, Glitch filter mode: 16384 clocks */
    LPTMR_PRESCALE_65536_GLITCHFILTER_32768 = 0x0Fu  /*!< Timer mode: prescaler 65536, Glitch filter mode: 32768 clocks */
} lptmr_prescaler_t;

/*! @brief Clock Source selection
 *  Implements : lptmr_clocksource_t_Class
 */
typedef enum {
    LPTMR_CLOCKSOURCE_SIRCDIV2  = 0x00u, /*!< SIRC clock */
    LPTMR_CLOCKSOURCE_1KHZ_LPO  = 0x01u, /*!< 1kHz LPO clock */
    LPTMR_CLOCKSOURCE_RTC       = 0x02u, /*!< RTC clock */
    LPTMR_CLOCKSOURCE_PCC       = 0x03u  /*!< PCC configured clock */
} lptmr_clocksource_t;

/*!
 * @brief Defines the LPTMR counter units available for configuring or reading the timer compare value.
 *
 * Implements : lptmr_counter_units_t_Class
 */
typedef enum
{
    LPTMR_COUNTER_UNITS_TICKS           = 0x00U,
    LPTMR_COUNTER_UNITS_MICROSECONDS    = 0x01U
} lptmr_counter_units_t;

/*!
 * @brief Defines the configuration structure for LPTMR.
 *
 * Implements : lptmr_config_t_Class
 */
typedef struct
{
    /* General parameters */
    _Bool dmaRequest;                    /*!< Enable/Disable DMA requests */
    _Bool interruptEnable;               /*!< Enable/Disable Interrupt */
    _Bool freeRun;                       /*!< Enable/Disable Free Running Mode */
    lptmr_workmode_t workMode;          /*!< Time/Pulse Counter Mode */
    /* Counter parameters */
    lptmr_clocksource_t clockSelect;    /*!< Clock selection for Timer/Glitch filter */
    lptmr_prescaler_t prescaler;        /*!< Prescaler Selection */
    _Bool bypassPrescaler;               /*!< Enable/Disable prescaler bypass */
    uint32_t compareValue;              /*!< Compare value */
    lptmr_counter_units_t counterUnits; /*!< Compare value units */
    /* Pulse Counter specific parameters */
    lptmr_pinselect_t pinSelect;        /*!< Pin selection for Pulse-Counter */
    lptmr_pinpolarity_t pinPolarity;    /*!< Pin Polarity for Pulse-Counter */
} lptmr_config_t;

/*******************************************************************************
 * Function prototypes
 ******************************************************************************/
/*!
 * @name LPTMR Driver Functions
 * @{
 */





/*!
 * @brief Initialize a configuration structure with default values.
 *
 * @param[out] config - Pointer to the configuration structure to be initialized
 */
void LPTMR_DRV_InitConfigStruct(lptmr_config_t * const config);

/*!
 * @brief Initialize a LPTMR instance with values from an input configuration structure.
 *
 * When (counterUnits == LPTMR_COUNTER_UNITS_MICROSECONDS) the function will
 * automatically configure the timer for the input compareValue in microseconds.
 * The input parameters for 'prescaler' and 'bypassPrescaler' will be ignored - their values
 * will be adapted by the function, to best fit the input compareValue
 * (in microseconds) for the operating clock frequency.
 *
 * LPTMR_COUNTER_UNITS_MICROSECONDS may only be used for LPTMR_WORKMODE_TIMER mode.
 * Otherwise the function shall not convert 'compareValue' in ticks
 * and this is likely to cause erroneous behavior.
 *
 * When (counterUnits == LPTMR_COUNTER_UNITS_TICKS) the function will use the
 * 'prescaler' and 'bypassPrescaler' provided in the input configuration structure.
 *
 * @param[in] instance     - LPTMR instance number
 * @param[in] config       - Pointer to the input configuration structure
 * @param[in] startCounter - Flag for starting the counter immediately after configuration
 */
void LPTMR_DRV_Init(const uint32_t instance,
                    const lptmr_config_t * const config,
                    const _Bool startCounter);

/*!
 * @brief Configure a LPTMR instance.
 *
 * When (counterUnits == LPTMR_COUNTER_UNITS_MICROSECONDS) the function will
 * automatically configure the timer for the input compareValue in microseconds.
 * The input parameters for 'prescaler' and 'bypassPrescaler' will be ignored - their values
 * will be adapted by the function, to best fit the input compareValue
 * (in microseconds) for the operating clock frequency.
 *
 * LPTMR_COUNTER_UNITS_MICROSECONDS may only be used for LPTMR_WORKMODE_TIMER mode.
 * Otherwise the function shall not convert 'compareValue' in ticks
 * and this is likely to cause erroneous behavior.
 *
 * When (counterUnits == LPTMR_COUNTER_UNITS_TICKS) the function will use the
 * 'prescaler' and 'bypassPrescaler' provided in the input configuration structure.
 *
 * @param[in] instance - LPTMR instance number
 * @param[in] config   - Pointer to the input configuration structure
 */
void LPTMR_DRV_SetConfig(const uint32_t instance,
                         const lptmr_config_t * const config);

/*!
 * @brief Get the current configuration of a LPTMR instance.
 *
 * @param[in] instance - LPTMR instance number
 * @param[out] config  - Pointer to the output configuration structure
 */
void LPTMR_DRV_GetConfig(const uint32_t instance,
                         lptmr_config_t * const config);

/*!
 * @brief De-initialize a LPTMR instance
 *
 * @param[in] instance - LPTMR instance number
 */
void LPTMR_DRV_Deinit(const uint32_t instance);

/*!
 * @brief Set the compare value in counter tick units, for a LPTMR instance.
 *
 * @param[in] instance            - LPTMR instance number
 * @param[in] compareValueByCount - The compare value in counter ticks, to be written
 * @return Operation status:
 * - STATUS_SUCCESS: completed successfully
 * - STATUS_ERROR: cannot reconfigure compare value (TCF not set)
 * - STATUS_TIMEOUT: compare value greater then current counter value
 */
status_t LPTMR_DRV_SetCompareValueByCount(const uint32_t instance,
                                          const uint16_t compareValueByCount);

/*!
 * @brief Get the compare value in counter tick units, of a LPTMR instance.
 *
 * @param[in] instance             - LPTMR instance number
 * @param[out] compareValueByCount - Pointer to current compare value, in counter ticks
 */
void LPTMR_DRV_GetCompareValueByCount(const uint32_t instance,
                                      uint16_t * const compareValueByCount);

/*!
 * @brief Set the compare value for Timer Mode in microseconds, for a LPTMR instance.
 *
 * @param[in] instance       - LPTMR peripheral instance number
 * @param[in] compareValueUs - Compare value in microseconds
 * @return Operation status:
 * - STATUS_SUCCESS: completed successfully
 * - STATUS_ERROR: cannot reconfigure compare value
 * - STATUS_TIMEOUT: compare value greater then current counter value
 */
status_t LPTMR_DRV_SetCompareValueByUs(const uint32_t instance,
                                       const uint32_t compareValueUs);

/*!
 * @brief Get the compare value in microseconds, of a LPTMR instance.
 *
 * @param[in] instance        - LPTMR instance number
 * @param[out] compareValueUs - Pointer to current compare value, in microseconds
 */
void LPTMR_DRV_GetCompareValueByUs(const uint32_t instance,
                                   uint32_t * const compareValueUs);

/*!
 * @brief Get the current state of the Compare Flag of a LPTMR instance.
 *
 * @param[in] instance - LPTMR instance number
 * @return The state of the Compare Flag
 */
_Bool LPTMR_DRV_GetCompareFlag(const uint32_t instance);

/*!
 * @brief Clear the Compare Flag of a LPTMR instance.
 *
 * @param[in] instance - LPTMR instance number
 */
void LPTMR_DRV_ClearCompareFlag(const uint32_t instance);

/*!
 * @brief Get the run state of a LPTMR instance.
 *
 * @param[in] instance - LPTMR instance number
 * @return The run state of the LPTMR instance:
 *  - true: Timer/Counter started
 *  - false: Timer/Counter stopped
 */
_Bool LPTMR_DRV_IsRunning(const uint32_t instance);

/*!
 * @brief Enable/disable the LPTMR interrupt.
 *
 * @param[in] instance        - LPTMR instance number
 * @param[in] enableInterrupt - The new state of the LPTMR interrupt enable flag.
 */
void LPTMR_DRV_SetInterrupt(const uint32_t instance,
                            const _Bool enableInterrupt);

/*!
 * @brief Get the current counter value in counter tick units.
 *
 * @param[in] instance - LPTMR instance number
 * @return The current counter value
 */
uint16_t LPTMR_DRV_GetCounterValueByCount(const uint32_t instance);

/*!
 * @brief Enable the LPTMR / Start the counter
 *
 * @param[in] instance - LPTMR instance number
 */
void LPTMR_DRV_StartCounter(const uint32_t instance);

/*!
 * @brief Disable the LPTMR / Stop the counter
 *
 * @param[in] instance - LPTMR instance number
 */
void LPTMR_DRV_StopCounter(const uint32_t instance);

/*!
 * @brief Set the Input Pin configuration for Pulse Counter mode
 *
 * @param[in] instance    - LPTMR instance number
 * @param[in] pinSelect   - LPTMR pin selection
 * @param[in] pinPolarity - Polarity on which to increment counter (rising/falling edge)
 */
void LPTMR_DRV_SetPinConfiguration(const uint32_t instance,
                                   const lptmr_pinselect_t pinSelect,
                                   const lptmr_pinpolarity_t pinPolarity);





/*! @}*/

/*! @}*/


/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 28 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\LPIT\\CODE\\lpit_driver.h"
/*
 * Copyright (c) 2016, Freescale Semiconductor, Inc.
 * Copyright 2016-2020 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */

/*!
 * @file lpit_driver.h
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Global macro not referenced.
 * The macros define the maximum of timer period in some modes and might be used by user.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.1, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.2, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.4, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 5.5, identifier clash
 * The supported compilers use more than 31 significant characters for identifiers.
 */




#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"
/* stddef.h standard header */
/* Copyright 2009-2017 IAR Systems AB. */




  #pragma system_include


#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ycheck.h"
/* ycheck.h internal checking header file. */
/* Copyright 2005-2017 IAR Systems AB. */

/* Note that there is no include guard for this header. This is intentional. */


  #pragma system_include


/* __AEABI_PORTABILITY_INTERNAL_LEVEL
 *
 * Note: Redefined each time ycheck.h is included, i.e. for each
 * system header, to ensure that ABI support could be turned off/on
 * individually for each file.
 *
 * Possible values for this preprocessor symbol:
 *
 * 0 - ABI portability mode is disabled.
 *
 * 1 - ABI portability mode (version 1) is enabled.
 *
 * All other values are reserved for future use.
 */





#line 11 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ysizet.h"
/* ysizet.h internal header file. */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include


#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ycheck.h"
/* ycheck.h internal checking header file. */
/* Copyright 2005-2017 IAR Systems AB. */

/* Note that there is no include guard for this header. This is intentional. */


  #pragma system_include


/* __AEABI_PORTABILITY_INTERNAL_LEVEL
 *
 * Note: Redefined each time ycheck.h is included, i.e. for each
 * system header, to ensure that ABI support could be turned off/on
 * individually for each file.
 *
 * Possible values for this preprocessor symbol:
 *
 * 0 - ABI portability mode is disabled.
 *
 * 1 - ABI portability mode (version 1) is enabled.
 *
 * All other values are reserved for future use.
 */





#line 12 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ysizet.h"


/* type definitions */



  typedef _Sizet size_t;




typedef unsigned int __data_size_t;




#line 13 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"

/* macros */








/* type definitions */



  typedef   signed int ptrdiff_t;




  typedef   _Wchart wchar_t;









    typedef union
    {
      long long _ll;
      long double _ld;
      void *_vp;
    } _Max_align_t;
    typedef _Max_align_t max_align_t;



#line 58 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"



/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 47 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\LPIT\\CODE\\lpit_driver.h"


/*!
 * @addtogroup lpit_drv
 * @{
 */

/*******************************************************************************
 * Definitions
 ******************************************************************************/
/*! @brief Max period in count of all operation mode except for dual 16 bit periodic counter mode */

/*! @brief Max period in count of dual 16 bit periodic counter mode                               */

/*! @brief Max count of 16 bit                                */


/*!
 * @brief Mode options available for the LPIT timer
 * Implements : lpit_timer_modes_t_Class
 */
typedef enum
{
    LPIT_PERIODIC_COUNTER      = 0x00U,  /*!< 32-bit Periodic Counter        */
    LPIT_DUAL_PERIODIC_COUNTER = 0x01U,  /*!< Dual 16-bit Periodic Counter   */
    LPIT_TRIGGER_ACCUMULATOR   = 0x02U,  /*!< 32-bit Trigger Accumulator     */
    LPIT_INPUT_CAPTURE         = 0x03U   /*!< 32-bit Trigger Input Capture   */
} lpit_timer_modes_t;

/*!
 * @brief Trigger source options.
 *
 * This is used for both internal and external trigger sources. The actual trigger
 * options available is SoC specific, user should refer to the reference manual.
 * Implements : lpit_trigger_source_t_Class
 */
typedef enum
{
    LPIT_TRIGGER_SOURCE_EXTERNAL = 0x00U, /*!< Use external trigger  */
    LPIT_TRIGGER_SOURCE_INTERNAL = 0x01U  /*!< Use internal trigger  */
}  lpit_trigger_source_t;

/*!
 * @brief Unit options for LPIT period.
 *
 * This is used to determine unit of timer period
 * Implements : lpit_period_units_t_Class
 */
typedef enum
{
    LPIT_PERIOD_UNITS_COUNTS        = 0x00U, /*!< Period value unit is count */
    LPIT_PERIOD_UNITS_MICROSECONDS  = 0x01U  /*!< Period value unit is microsecond */
} lpit_period_units_t;

/*!
 * @brief LPIT configuration structure
 *
 * This structure holds the configuration settings for the LPIT peripheral to
 * enable or disable LPIT module in DEBUG and DOZE mode
 * Implements : lpit_user_config_t_Class
 */
typedef struct
{
    _Bool enableRunInDebug; /*!< True: Timer channels continue to run in debug mode
                                False: Timer channels stop in debug mode            */
    _Bool enableRunInDoze;  /*!< True: Timer channels continue to run in doze mode
                                False: Timer channels stop in doze mode             */
} lpit_user_config_t;

/*! @brief Structure to configure the channel timer
 *
 * This structure holds the configuration settings for the LPIT timer channel
 * Implements : lpit_user_channel_config_t_Class
 */
typedef struct
{
    lpit_timer_modes_t timerMode;        /*!< Operation mode of timer channel                               */
    lpit_period_units_t periodUnits;     /*!< Timer period value units                                      */
    uint32_t period;                     /*!< Period of timer channel                                       */
    lpit_trigger_source_t triggerSource; /*!< Selects between internal and external trigger sources         */
    uint32_t triggerSelect;              /*!< Selects one trigger from the internal trigger sources
                                              this field makes sense if trigger source is internal          */
    _Bool enableReloadOnTrigger;          /*!< True: Timer channel will reload on selected trigger
                                              False: Timer channel will not reload on selected trigger      */
    _Bool enableStopOnInterrupt;          /*!< True: Timer will stop after timeout
                                              False: Timer channel does not stop after timeout              */
    _Bool enableStartOnTrigger;           /*!< True: Timer channel starts to decrement when rising edge
                                              on selected trigger is detected.
                                              False: Timer starts to decrement immediately based on
                                              restart condition                                             */
    _Bool chainChannel;                   /*!< Channel chaining enable                                       */
    _Bool isInterruptEnabled;             /*!< Timer channel interrupt generation enable                     */
} lpit_user_channel_config_t;

/*******************************************************************************
 * API
 ******************************************************************************/





/*!
 * @name Initialization and De-initialization
 * @{
 */

/*!
 * @brief Gets the default LPIT configuration
 *
 * This function gets default LPIT module configuration structure, with the following settings:
 * - PIT runs in debug mode: Disable
 * - PIT runs in doze mode: Disable
 *
 * @param[out] config The configuration structure
 */
void LPIT_DRV_GetDefaultConfig(lpit_user_config_t * const config);

/*!
 * @brief Gets the default timer channel configuration
 *
 * This function gets the default timer channel configuration structure, with the following settings:
 * - Timer mode: 32-bit Periodic Counter
 * - Period unit: Period value unit is microsecond
 * - Period: 1000000 microseconds(1 second)
 * - Trigger sources: External trigger
 * - Trigger select: Trigger from channel 0
 * - Reload on trigger: Disable
 * - Stop on interrupt : Disable
 * - Start on trigger: Disable
 * - Channel chaining: Disable
 * - Interrupt generating: Enable
 *
 * @param[out] config The channel configuration structure
 */
void LPIT_DRV_GetDefaultChanConfig(lpit_user_channel_config_t * const config);

/*!
 * @brief Initializes the LPIT module.
 *
 * This function resets LPIT module, enables the LPIT module, configures LPIT
 * module operation in Debug and DOZE mode. The LPIT configuration structure shall
 * be passed as arguments.
 * This configuration structure affects all timer channels.
 * This function should be called before calling any other LPIT driver function.
 *
 * This is an example demonstrating how to define a LPIT configuration structure:
   @code
   lpit_user_config_t lpitInit =
   {
        .enableRunInDebug = false,
        .enableRunInDoze = true
   };
   @endcode
 *
 * @param[in] instance LPIT module instance number.
 * @param[in] userConfig Pointer to LPIT configuration structure.
 */
void LPIT_DRV_Init(uint32_t instance,
                   const lpit_user_config_t * userConfig);

/*!
 * @brief De-Initializes the LPIT module.
 *
 * This function disables LPIT module.
 * In order to use the LPIT module again, LPIT_DRV_Init must be called.
 *
 * @param[in] instance LPIT module instance number
 */
void LPIT_DRV_Deinit(uint32_t instance);

/*!
 * @brief Initializes the LPIT channel.
 *
 * This function initializes the LPIT timers by using a channel, this function
 * configures timer channel chaining, timer channel mode, timer channel period,
 * interrupt generation, trigger source, trigger select, reload on trigger,
 * stop on interrupt and start on trigger.
 * The timer channel number and its configuration structure shall be passed as arguments.
 * Timer channels do not start counting by default after calling this function.
 * The function LPIT_DRV_StartTimerChannels must be called to start the timer channel counting.
 * In order to re-configures the period, call the LPIT_DRV_SetTimerPeriodByUs or
 * LPIT_DRV_SetTimerPeriodByCount.
 *
 * This is an example demonstrating how to define a LPIT channel configuration structure:
   @code
   lpit_user_channel_config_t lpitTestInit =
   {
    .timerMode = LPIT_PERIODIC_COUNTER,
    .periodUnits = LPTT_PERIOD_UNITS_MICROSECONDS,
    .period = 1000000U,
    .triggerSource = LPIT_TRIGGER_SOURCE_INTERNAL,
    .triggerSelect = 1U,
    .enableReloadOnTrigger = false,
    .enableStopOnInterrupt = false,
    .enableStartOnTrigger = false,
    .chainChannel = false,
    .isInterruptEnabled = true
   };
   @endcode
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @param[in] userChannelConfig Pointer to LPIT channel configuration structure
 * @return Operation status
 *         - STATUS_SUCCESS: Operation was successful.
 *         - STATUS_ERROR: The channel 0 is chained.
 *         - STATUS_ERROR: The input period is invalid.
 */
status_t LPIT_DRV_InitChannel(uint32_t instance,
                              uint32_t channel,
                              const lpit_user_channel_config_t * userChannelConfig);

/* @} */

/*!
 * @name Timer Start and Stop
 * @{
 */

/*!
 * @brief Starts the timer channel counting.
 *
 * This function allows starting timer channels simultaneously .
 * After calling this function, timer channels are going operate depend on mode and
 * control bits which controls timer channel start, reload and restart.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] mask Timer channels starting mask that decides which channels
 * will be started
 * - For example:
 *      - with mask = 0x01U then channel 0 will be started
 *      - with mask = 0x02U then channel 1 will be started
 *      - with mask = 0x03U then channel 0 and channel 1 will be started
 */
void LPIT_DRV_StartTimerChannels(uint32_t instance,
                                 uint32_t mask);

/*!
 * @brief Stops the timer channel counting.
 *
 * This function allows stop timer channels simultaneously from counting.
 * Timer channels reload their periods respectively after the next time
 * they call the LPIT_DRV_StartTimerChannels. Note that: In 32-bit Trigger Accumulator
 * mode, the counter will load on the first trigger rising edge.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] mask Timer channels stopping mask that decides which channels
 * will be stopped
 * - For example:
 *      - with mask = 0x01U then channel 0 will be stopped
 *      - with mask = 0x02U then channel 1 will be stopped
 *      - with mask = 0x03U then channel 0 and channel 1 will be stopped
 */
void LPIT_DRV_StopTimerChannels(uint32_t instance,
                                uint32_t mask);

/* @} */

/*!
 * @name Timer Period
 * @{
 */

/*!
 * @brief Sets the timer channel period in microseconds.
 *
 * This function sets the timer channel period in microseconds
 * when timer channel mode is 32 bit periodic or dual 16 bit counter mode.
 * The period range depends on the frequency of the LPIT functional clock and
 * operation mode of timer channel.
 * If the required period is out of range, use the suitable mode if applicable.
 * This function is only valid for one single channel.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @param[in] periodUs Timer channel period in microseconds
 * @return Operation status
 *         - STATUS_SUCCESS: Input period of timer channel is valid.
 *         - STATUS_ERROR: Input period of timer channel is invalid.
 */
status_t LPIT_DRV_SetTimerPeriodByUs(uint32_t instance,
                                     uint32_t channel,
                                     uint32_t periodUs);

/*!
 * @brief Sets the timer channel period in microseconds.
 *
 * This function sets the timer channel period in microseconds
 * when timer channel mode is dual 16 bit periodic counter mode.
 * The period range depends on the frequency of the LPIT functional clock and
 * operation mode of timer channel.
 * If the required period is out of range, use the suitable mode if applicable.
 * This function is only valid for one single channel.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @param[in] periodHigh Period of higher 16 bit in microseconds
 * @param[in] periodLow Period of lower 16 bit in microseconds
 * @return Operation status
 *         - STATUS_SUCCESS: Input period of timer channel is valid.
 *         - STATUS_ERROR: Input period of timer channel is invalid.
 */
status_t LPIT_DRV_SetTimerPeriodInDual16ModeByUs(uint32_t instance,
                                                 uint32_t channel,
                                                 uint16_t periodHigh,
                                                 uint16_t periodLow);

/*!
 * @brief Gets the timer channel period in microseconds.
 *
 * This function gets the timer channel period in microseconds.
 * The returned period here makes sense if the operation mode of timer channel
 * is 32 bit periodic counter or dual 16 bit periodic counter.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @return Timer channel period in microseconds
 */
uint64_t LPIT_DRV_GetTimerPeriodByUs(uint32_t instance,
                                     uint32_t channel);

/*!
 * @brief Gets the current timer channel counting value in microseconds.
 *
 * This function returns an absolute time stamp in microseconds.
 * One common use of this function is to measure the running time of a part of
 * code. Call this function at both the beginning and end of code. The time
 * difference between these two time stamps is the running time.
 * The return counting value here makes sense if the operation mode of timer channel
 * is 32 bit periodic counter or dual 16 bit periodic counter or 32-bit trigger input capture.
 * Need to make sure the running time will not exceed the timer channel period.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @return Current timer channel counting value in microseconds
 */
uint64_t LPIT_DRV_GetCurrentTimerUs(uint32_t instance,
                                    uint32_t channel);

/*!
 * @brief Sets the timer channel period in count unit.
 *
 * This function sets the timer channel period in count unit.
 * The counter period of a running timer channel can be modified by first setting
 * a new load value, the value will be loaded after the timer channel expires.
 * To abort the current cycle and start a timer channel period with the new value,
 * the timer channel must be disabled and enabled again.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @param[in] count Timer channel period in count unit
 */
void LPIT_DRV_SetTimerPeriodByCount(uint32_t instance,
                                    uint32_t channel,
                                    uint32_t count);

/*!
 * @brief Sets the timer channel period in count unit.
 *
 * This function sets the timer channel period in count unit when timer channel
 * mode is dual 16 periodic counter mode.
 * The counter period of a running timer channel can be modified by first setting
 * a new load value, the value will be loaded after the timer channel expires.
 * To abort the current cycle and start a timer channel period with the new value,
 * the timer channel must be disabled and enabled again.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @param[in] periodHigh Period of higher 16 bit in count unit
 * @param[in] periodLow Period of lower 16 bit in count unit
 */
void LPIT_DRV_SetTimerPeriodInDual16ModeByCount(uint32_t instance,
                                                uint32_t channel,
                                                uint16_t periodHigh,
                                                uint16_t periodLow);

/*!
 * @brief Gets the current timer channel period in count unit.
 *
 * This function returns current period of timer channel given as argument.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @return Timer channel period in count unit
 */
uint32_t LPIT_DRV_GetTimerPeriodByCount(uint32_t instance,
                                        uint32_t channel);

/*!
 * @brief Gets the current timer channel counting value in count.
 *
 * This function returns the real-time timer channel counting value, the value in
 * a range from 0 to timer channel period.
 * Need to make sure the running time does not exceed the timer channel period.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] channel Timer channel number
 * @return Current timer channel counting value in count
 */
uint32_t LPIT_DRV_GetCurrentTimerCount(uint32_t instance,
                                       uint32_t channel);
/* @} */

/*!
 * @name Interrupt
 * @{
 */

/*!
 * @brief Enables the interrupt generation of timer channel.
 *
 * This function allows enabling interrupt generation of timer channel
 * when timeout occurs or input trigger occurs.
 *
 * @param[in] instance LPIT module instance number.
 * @param[in] mask The mask that decides which channels will be enabled interrupt.
 * - For example:
 *      - with mask = 0x01u then the interrupt of channel 0 will be enabled
 *      - with mask = 0x02u then the interrupt of channel 1 will be enabled
 *      - with mask = 0x03u then the interrupt of channel 0 and channel 1 will be enabled
 */
void LPIT_DRV_EnableTimerChannelInterrupt(uint32_t instance,
                                          uint32_t mask);

/*!
 * @brief Disables the interrupt generation of timer channel.
 *
 * This function allows disabling interrupt generation of timer channel
 * when timeout occurs or input trigger occurs.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] mask The mask that decides which channels will be disable interrupt.
 * - For example:
 *      - with mask = 0x01u then the interrupt of channel 0 will be disable
 *      - with mask = 0x02u then the interrupt of channel 1 will be disable
 *      - with mask = 0x03u then the interrupt of channel 0 and channel 1 will be disable
 */
void LPIT_DRV_DisableTimerChannelInterrupt(uint32_t instance,
                                           uint32_t mask);

/*!
 * @brief Gets the current interrupt flag of timer channels.
 *
 * This function gets the current interrupt flag of timer channels.
 * In compare modes, the flag sets to 1 at the end of the timer period.
 * In capture modes, the flag sets to 1 when the trigger asserts.
 *
 * @param[in] instance LPIT module instance number.
 * @param[in] mask The interrupt flag getting mask that decides which channels will
 * be got interrupt flag.
 * - For example:
 *      - with mask = 0x01u then the interrupt flag of channel 0 only will be got
 *      - with mask = 0x02u then the interrupt flag of channel 1 only will be got
 *      - with mask = 0x03u then the interrupt flags of channel 0 and channel 1 will be got
 * @return Current the interrupt flag of timer channels
 */
uint32_t LPIT_DRV_GetInterruptFlagTimerChannels(uint32_t instance,
                                                uint32_t mask);

/*!
 * @brief Clears the interrupt flag of timer channels.
 *
 * This function clears the interrupt flag of timer channels after
 * their interrupt event occurred.
 *
 * @param[in] instance LPIT module instance number
 * @param[in] mask The interrupt flag clearing mask that decides which channels will
 * be cleared interrupt flag
 * - For example:
 *      - with mask = 0x01u then the interrupt flag of channel 0 only will be cleared
 *      - with mask = 0x02u then the interrupt flag of channel 1 only will be cleared
 *      - with mask = 0x03u then the interrupt flags of channel 0 and channel 1 will be cleared
 */
void LPIT_DRV_ClearInterruptFlagTimerChannels(uint32_t instance,
                                              uint32_t mask);

/* @} */





/*! @}*/


/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 30 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\LPIT\\CODE\\lpit_hw_access.h"
/*
 * Copyright 2017-2020 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */








/*******************************************************************************
 * Definitions
 ******************************************************************************/

/*******************************************************************************
 * API
 ******************************************************************************/





/*!
 * @brief Enables the LPIT module.
 *
 * This function enables the functional clock of LPIT module (Note: this function
 * does not un-gate the system clock gating control). It should be called before
 * setup any timer channel.
 *
 * @param[in] base LPIT peripheral base address
 */
static inline void LPIT_Enable(LPIT_Type * const base, volatile uint32_t delay)
{
    base->MCR |= 0x1u;
    /* Run this counter down to zero
        If the delay is 0, the four clock delay between setting and clearing
        the SW_RST bit is ensured by the read-modify-write operation.
    */
    while(delay != 0u)
    {
        /* Since we need a four cycle delay, we assume the decrement is one cycle
            and insert three NOP instructions. The actual delay will be larger because
            of the loop overhead and the compiler optimization.
        */
        delay--;
        __asm volatile ("nop");
        __asm volatile ("nop");
        __asm volatile ("nop");
    }
}

/*!
 * @brief Disables the LPIT module.
 *
 * This function disables functional clock of LPIT module (Note: it does not
 * affect the system clock gating control).
 *
 * @param[in] base LPIT peripheral base address
 */
static inline void LPIT_Disable(LPIT_Type * const base)
{
    base->MCR &= ~0x1u;
}

/*!
 * @brief Resets the LPIT module.
 *
 * This function sets all LPIT registers to reset value,
 * except the Module Control Register.
 *
 * @param[in] base LPIT peripheral base address
 */
static inline void LPIT_Reset(LPIT_Type * const base, volatile uint32_t delay)
{
    base->MCR |= 0x2u;
    /* Run this counter down to zero
        If the delay is 0, the four clock delay between setting and clearing
        the SW_RST bit is ensured by the read-modify-write operation.
    */
    while(delay != 0u)
    {
        /* Since we need a four cycle delay, we assume the decrement is one cycle
            and insert three NOP instructions. The actual delay will be larger because
            of the loop overhead and the compiler optimization.
        */
        delay--;
        __asm volatile ("nop");
        __asm volatile ("nop");
        __asm volatile ("nop");
    }
    base->MCR &= ~0x2u;
}

/*!
  @brief Starts the timer channel counting.
 *
 * This function allows starting timer channels simultaneously .
 * After calling this function, timer channels are going operate depend on mode and
 * control bits which controls timer channel start, reload and restart.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] mask Timer channels starting mask that decides which channels
 * will be started
 * - For example:
 *      - with mask = 0x01U then channel 0 will be started
 *      - with mask = 0x02U then channel 1 will be started
 *      - with mask = 0x03U then channel 0 and channel 1 will be started
 */
static inline void LPIT_StartTimerChannels(LPIT_Type * const base,
                                           uint32_t mask)
{
    base->SETTEN |= mask;
}

/*!
 * @brief Stops the timer channel from counting.
 *
 * This function allows stop timer channels simultaneously from counting.
 * Timer channels reload their periods respectively after the next time
 * they call the LPIT_DRV_StartTimerChannels. Note that: In 32-bit Trigger Accumulator
 * mode, the counter will load on the first trigger rising edge.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] mask Timer channels stopping mask that decides which channels
 * will be stopped
 * - For example:
 *      - with mask = 0x01U then channel 0 will be stopped
 *      - with mask = 0x02U then channel 1 will be stopped
 *      - with mask = 0x03U then channel 0 and channel 1 will be stopped
 */
static inline void LPIT_StopTimerChannels(LPIT_Type * const base,
                                          uint32_t mask)
{
    base->CLRTEN |= mask;
}

/*!
 * @brief Sets the timer channel period in count unit.
 *
 * This function sets the timer channel period in count unit.
 * The period range depends on the frequency of the LPIT functional clock and
 * operation mode of timer channel.
 * If the required period is out of range, use the suitable mode if applicable.
 * Timer channel begins counting from the value that is set by this function.
 * The counter period of a running timer channel can be modified by first setting
 * a new load value, the value will be loaded after the timer channel expires.
 * To abort the current cycle and start a timer channel period with the new value,
 * the timer channel must be disabled and enabled again.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @param[in] count Timer channel period in count unit
 */
static inline void LPIT_SetTimerPeriodByCount(LPIT_Type * const base,
                                              uint32_t channel,
                                              uint32_t count)
{
    base->TMR[channel].TVAL = count;
}

/*!
 * @brief Gets the timer channel period in count unit.
 *
 * This function returns current period of timer channel given as argument.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @return Timer channel period in count unit
 */
static inline uint32_t LPIT_GetTimerPeriodByCount(const LPIT_Type * base,
                                                  uint32_t channel)
{
    return (base->TMR[channel].TVAL);
}

/*!
 * @brief Gets the current timer channel counting value.
 *
 * This function returns the real-time timer channel counting value, the value in
 * a range from 0 to timer channel period.
 * Need to make sure the running time does not exceed the timer channel period.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @return Current timer channel counting value
 */
static inline uint32_t LPIT_GetCurrentTimerCount(const LPIT_Type * base,
                                                 uint32_t channel)
{
    return (base->TMR[channel].CVAL);
}

/*!
 * @brief Enables the interrupt generation for timer channels.
 *
 * This function allows enabling interrupt generation for timer channels simultaneously.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] mask The interrupt enabling mask that decides which channels will
 * be enabled interrupt.
 * - For example:
 *      - with mask = 0x01u then will enable interrupt for channel 0 only
 *      - with mask = 0x02u then will enable interrupt for channel 1 only
 *      - with mask = 0x03u then will enable interrupt for channel 0 and channel 1
 */
static inline void LPIT_EnableInterruptTimerChannels(LPIT_Type * const base,
                                                     uint32_t mask)
{
    base->MIER |= mask;
}

/*!
 * @brief Disables the interrupt generation for timer channels.
 *
 * This function allows disabling interrupt generation for timer channels simultaneously.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] mask The interrupt disabling mask that decides which channels will
 * be disabled interrupt.
 * - For example:
 *      - with mask = 0x01u then will disable interrupt for channel 0 only
 *      - with mask = 0x02u then will disable interrupt for channel 1 only
 *      - with mask = 0x03u then will disable interrupt for channel 0 and channel 1
 */
static inline void LPIT_DisableInterruptTimerChannels(LPIT_Type * const base,
                                                      uint32_t mask)
{
    base->MIER &= ~mask;
}

/*!
 * @brief Gets the interrupt flag of timer channels.
 *
 * This function gets current interrupt flag of timer channels.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] mask The interrupt flag getting mask that decides which channels will
 * be got interrupt flag.
 * - For example:
 *      - with mask = 0x01u then the interrupt flag of channel 0 only will be got
 *      - with mask = 0x02u then the interrupt flag of channel 1 only will be got
 *      - with mask = 0x03u then the interrupt flags of channel 0 and channel 1 will be got
 * @return The interrupt flag of timer channels.
 */
static inline uint32_t LPIT_GetInterruptFlagTimerChannels(const LPIT_Type * base,
                                                          uint32_t mask)
{
    return (base->MSR) & mask;
}

/*!
 * @brief Clears the interrupt flag of timer channels.
 *
 * This function clears current interrupt flag of timer channels.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] mask The interrupt flag clearing mask that decides which channels will
 * be cleared interrupt flag.
 * - For example:
 *      - with mask = 0x01u then the interrupt flag of channel 0 only will be cleared
 *      - with mask = 0x02u then the interrupt flag of channel 1 only will be cleared
 *      - with mask = 0x03u then the interrupt flags of channel 0 and channel 1 will be cleared
 */
static inline void LPIT_ClearInterruptFlagTimerChannels(LPIT_Type * const base,
                                                        uint32_t mask)
{
    /* Write 1 to clear the interrupt flag. */
    base->MSR = mask;




}

/*!
 * @brief Sets operation mode of timer channel
 *
 * This function sets the timer channel operation mode which control how
 * the timer channel decrements.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @param[in] mode Operation mode of timer channel that is member of lpit_timer_modes_t
 */
static inline void LPIT_SetTimerChannelModeCmd(LPIT_Type * const base,
                                               uint32_t channel,
                                               lpit_timer_modes_t mode)
{
    base->TMR[channel].TCTRL &= ~0xCu;
    base->TMR[channel].TCTRL |=  (((uint32_t)(((uint32_t)(mode))<<2u))&0xCu);
}

/*!
 * @brief Gets current operation mode of timer channel.
 *
 * This function gets current operation mode of the timer channel given as argument.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @return Operation mode of timer channel that is one of lpit_timer_modes_t
 */
static inline lpit_timer_modes_t LPIT_GetTimerChannelModeCmd(const LPIT_Type * base,
                                                             uint32_t channel)
{
    uint32_t tmp;
    lpit_timer_modes_t mode;

    tmp = (((base->TMR[channel].TCTRL) & 0xCu)
                                 >> 2u);
    switch (tmp)
    {
        case 0x00U:
            mode = LPIT_PERIODIC_COUNTER;
            break;
        case 0x01U:
            mode = LPIT_DUAL_PERIODIC_COUNTER;
            break;
        case 0x02U:
            mode = LPIT_TRIGGER_ACCUMULATOR;
            break;
        case 0x03U:
            mode = LPIT_INPUT_CAPTURE;
            break;
        default:
            mode = LPIT_PERIODIC_COUNTER;
            break;
    }
    return mode;
}

/*!
 * @brief Sets internal trigger source for timer channel
 *
 * This function selects one trigger from the set of internal triggers that is
 * generated by other timer channels.
 * The selected trigger is used for starting and/or reloading the timer channel.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @param[in] triggerChannelSelect Number of the channel which is selected to be trigger source
 */
static inline void LPIT_SetTriggerSelectCmd(LPIT_Type * const base,
                                            uint32_t channel,
                                            uint32_t triggerChannelSelect)
{
    base->TMR[channel].TCTRL &= ~0xF000000u;
    base->TMR[channel].TCTRL |=  (((uint32_t)(((uint32_t)(triggerChannelSelect))<<24u))&0xF000000u);
}

/*!
 * @brief Sets trigger source of timer channel.
 *
 * This function sets trigger source of the timer channel to be internal or external trigger.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @param[in] triggerSource Trigger source of timer channel(internal or external source)
 */
static inline void LPIT_SetTriggerSourceCmd(LPIT_Type * const base,
                                            uint32_t channel,
                                            lpit_trigger_source_t triggerSource)
{
    base->TMR[channel].TCTRL &= ~0x800000u;
    base->TMR[channel].TCTRL |=  (((uint32_t)(((uint32_t)(triggerSource))<<23u))&0x800000u);
}

/*!
 * @brief Sets timer channel reload on trigger.
 *
 * This function sets the timer channel to reload/don't reload on trigger.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @param[in] isReloadOnTrigger Timer channel reload on trigger
 *        - True : timer channel will reload on trigger
 *        - False : timer channel will not reload on trigger
 */
static inline void LPIT_SetReloadOnTriggerCmd(LPIT_Type * const base,
                                              uint32_t channel,
                                              _Bool isReloadOnTrigger)
{
    base->TMR[channel].TCTRL &= ~0x40000u;
    base->TMR[channel].TCTRL |=  (((uint32_t)(((uint32_t)(isReloadOnTrigger ? 1UL : 0UL))<<18u))&0x40000u);
}

/*!
 * @brief Sets timer channel stop on interrupt.
 *
 * This function sets the timer channel to stop or don't stop after it times out.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @param[in] isStopOnInterrupt Timer channel stop on interrupt
 *        - True : Timer channel will stop after it times out
 *        - False : Timer channel will not stop after it times out
 */
static inline void LPIT_SetStopOnInterruptCmd(LPIT_Type * const base,
                                              uint32_t channel,
                                              _Bool isStopOnInterrupt)
{
    base->TMR[channel].TCTRL &= ~0x20000u;
    base->TMR[channel].TCTRL |=  (((uint32_t)(((uint32_t)(isStopOnInterrupt ? 1UL : 0UL))<<17u))&0x20000u);
}

/*!
 * @brief Sets timer channel start on trigger.
 *
 * This function sets the timer channel to starts/don't start on trigger.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number
 * @param[in] isStartOnTrigger Timer channel start on trigger
 *        - True : Timer channel starts to decrement when rising edge on selected trigger is detected
 *        - False : Timer channel starts to decrement immediately based on restart condition
 *                      (controlled by Timer Stop On Interrupt bit)
 */
static inline void LPIT_SetStartOnTriggerCmd(LPIT_Type * const base,
                                             uint32_t channel,
                                             _Bool isStartOnTrigger)
{
    base->TMR[channel].TCTRL &= ~0x10000u;
    base->TMR[channel].TCTRL |=  (((uint32_t)(((uint32_t)(isStartOnTrigger ? 1UL : 0UL))<<16u))&0x10000u);
}

/*!
 * @brief Sets timer channel chaining.
 *
 * This function sets the timer channel to be chained or not chained.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] channel Timer channel number(Note: The timer channel 0 cannot be chained)
 * @param[in] isChannelChained Timer channel chaining
 *        - True : Timer channel is chained. Timer channel decrements on previous channel's timeout
 *        - False : Timer channel is not chained. Timer channel runs independently
 */
static inline void LPIT_SetTimerChannelChainCmd(LPIT_Type * const base,
                                                uint32_t channel,
                                                _Bool isChannelChained)
{
    base->TMR[channel].TCTRL &= ~0x2u;
    base->TMR[channel].TCTRL |=  (((uint32_t)(((uint32_t)(isChannelChained ? 1UL : 0UL))<<1u))&0x2u);
}

/*!
 * @brief Sets operation of LPIT in debug mode.
 *
 * When the device enters debug mode, the timer channels may or may not be frozen,
 * based on the configuration of this function. This is intended to aid software development,
 * allowing the developer to halt the processor, investigate the current state of
 * the system (for example, the timer channel values), and continue the operation.
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] isRunInDebug LPIT run in debug mode
 *        - True: LPIT continue to run when the device enters debug mode
 *        - False: LPIT stop when the device enters debug mode
 */
static inline void LPIT_SetTimerRunInDebugCmd(LPIT_Type * const base,
                                              _Bool isRunInDebug)
{
    base->MCR &= ~0x8u;
    base->MCR |= (((uint32_t)(((uint32_t)(isRunInDebug ? 1UL: 0UL))<<3u))&0x8u);
}

/*!
 * @brief Sets operation of LPIT in DOZE mode.
 *
 * When the device enters debug mode, the timer channels may or may not be frozen,
 * based on the configuration of this function. The LPIT must use an external or
 * internal clock source which remains operating during DOZE modes(low power mode).
 *
 * @param[in] base LPIT peripheral base address
 * @param[in] isRunInDoze LPIT run in DOZE mode
 *        - True: LPIT continue to run when the device enters DOZE mode
 *        - False: LPIT channels stop when the device enters DOZE mode
 */
static inline void LPIT_SetTimerRunInDozeCmd(LPIT_Type * const base,
                                             _Bool isRunInDoze)
{
    base->MCR &= ~0x4u;
    base->MCR |= (((uint32_t)(((uint32_t)(isRunInDoze ? 1UL : 0UL))<<2u))&0x4u);
}






/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 31 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\LPIT\\CODE\\peripherals_lpit_config_1.h"
/***********************************************************************************************************************
 * This file was generated by the S32 Config Tools. Any manual edits made to this file
 * will be overwritten if the respective S32 Config Tools is used to update this file.
 **********************************************************************************************************************/




/**
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Global macro not referenced.
 * The global macro will be used in function call of the module.
 *
 */
/*******************************************************************************
 * Included files 
 ******************************************************************************/


/*******************************************************************************
 * Definitions 
 ******************************************************************************/

/*Device instance number */


/*******************************************************************************
 * Global variables 
 ******************************************************************************/

/* LPIT global configuration */
extern const lpit_user_config_t lpit1_InitConfig;

/* LPIT channel configuration */
extern lpit_user_channel_config_t lpit1_ChnConfig0;



#line 32 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\LPTMR\\CODE\\peripherals_lptmr_1.h"
/***********************************************************************************************************************
 * This file was generated by the S32 Config Tools. Any manual edits made to this file
 * will be overwritten if the respective S32 Config Tools is used to update this file.
 **********************************************************************************************************************/




/**
 * @page misra_violations MISRA-C:2012 violations
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 2.5, Global macro not referenced.
 * The global macro will be used in function call of the module.
 *
 */
/*******************************************************************************
 * Included files 
 ******************************************************************************/


/*******************************************************************************
 * Definitions 
 ******************************************************************************/


/*******************************************************************************
 * Global variables 
 ******************************************************************************/

/* LPTMR Configuration 0 */
extern const lptmr_config_t lptmr_1_config0;



#line 33 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\IM\\CODE\\interrupt_manager.h"
/*
 * Copyright (c) 2013 - 2016, Freescale Semiconductor, Inc.
 * Copyright 2016-2020 NXP
 * All rights reserved.
 *
 * NXP Confidential. This software is owned or controlled by NXP and may only be
 * used strictly in accordance with the applicable license terms. By expressly
 * accepting such terms or by downloading, installing, activating and/or otherwise
 * using the software, you are agreeing that you have read, and that you agree to
 * comply with and are bound by, such license terms. If you do not agree to be
 * bound by the applicable license terms, then you may not retain, install,
 * activate or otherwise use the software. The production use license in
 * Section 2.3 is expressly granted for this software.
 */






/**
 * @page misra_violations MISRA-C:2012 violations
 *
 *
 * @section [global]
 * Violates MISRA 2012 Required Rule 11.6, A cast shall not be performed
 * between pointer to void and an arithmetic type.
 * The address of hardware modules is provided as integer so
 * it needs to be cast to pointer.
 *
 * @section [global]
 * Violates MISRA 2012 Advisory Rule 11.4, A conversion should not be performed
 * between a pointer to object and an integer type.
 * The address of hardware modules is provided as integer so
 * a conversion between a pointer and an integer has to be performed.
 */

 /*! @file interrupt_manager.h */

/*! @addtogroup interrupt_manager*/
/*! @{*/

/*******************************************************************************
 * Definitions
 ******************************************************************************/

#line 61 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\IM\\CODE\\interrupt_manager.h"

#line 74 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\IM\\CODE\\interrupt_manager.h"

/*! @brief Interrupt handler type */
typedef void (* isr_t)(void);

/*******************************************************************************
 * Default interrupt handler - implemented in startup.s
 ******************************************************************************/
/*! @brief Default ISR. */
void DefaultISR(void);

/*******************************************************************************
 * API
 ******************************************************************************/





/*! @name Interrupt manager APIs*/
/*@{*/

/*!
 * @brief Installs an interrupt handler routine for a given IRQ number. 
 *
 * This function lets the application register/replace the interrupt
 * handler for a specified IRQ number. See a chip-specific reference
 * manual for details and the  startup_<SoC>.s file for each chip
 * family to find out the default interrupt handler for each device.
 *
 * @note This method is applicable only if interrupt vector is copied in RAM.
 *
 * @param irqNumber   IRQ number
 * @param newHandler  New interrupt handler routine address pointer
 * @param oldHandler  Pointer to a location to store current interrupt handler
 */
void INT_SYS_InstallHandler(IRQn_Type irqNumber,
                            const isr_t newHandler,
                            isr_t* const oldHandler);

/*!
 * @brief Enables an interrupt for a given IRQ number. 
 *
 * This function  enables the individual interrupt for a specified IRQ number.
 *
 * @param irqNumber IRQ number
 */
void INT_SYS_EnableIRQ(IRQn_Type irqNumber);

/*!
 * @brief Disables an interrupt for a given IRQ number. 
 *
 * This function disables the individual interrupt for a specified IRQ number.
 *
 * @param irqNumber IRQ number
 */
void INT_SYS_DisableIRQ(IRQn_Type irqNumber);

/*!
 * @brief Enables system interrupt.
 *
 * This function enables the global interrupt by calling the core API.
 *
 */
void INT_SYS_EnableIRQGlobal(void);

/*!
 * @brief Disable system interrupt. 
 *
 * This function disables the global interrupt by calling the core API.
 *
 */
void INT_SYS_DisableIRQGlobal(void);

/*! @brief  Set Interrupt Priority
 *
 *   The function sets the priority of an interrupt.
 *
 *   @param  irqNumber  Interrupt number.
 *   @param  priority  Priority to set.
 */
void INT_SYS_SetPriority(IRQn_Type irqNumber, uint8_t priority);

/*! @brief  Get Interrupt Priority
 *
 *   The function gets the priority of an interrupt.
 *
 *   @param  irqNumber  Interrupt number.
 *   @return priority   Priority of the interrupt.
 */
uint8_t INT_SYS_GetPriority(IRQn_Type irqNumber);


/*!
 * @brief Clear Pending Interrupt
 *
 * The function clears the pending bit of a peripheral interrupt
 * or a directed interrupt to this CPU (if available).
 *
 * @param irqNumber IRQ number
 */
void INT_SYS_ClearPending(IRQn_Type irqNumber);

/*!
 * @brief Set Pending Interrupt
 *
 * The function configures the pending bit of a peripheral interrupt.
 *
 * @param irqNumber IRQ number
 */
void INT_SYS_SetPending(IRQn_Type irqNumber);

/*!
 * @brief Get Pending Interrupt
 *
 * The function gets the pending bit of a peripheral interrupt
 * or a directed interrupt to this CPU (if available).
 *
 * @param irqNumber IRQ number
 * @return pending  Pending status 0/1
 */
uint32_t INT_SYS_GetPending(IRQn_Type irqNumber);



#line 210 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\IM\\CODE\\interrupt_manager.h"

#line 232 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\IM\\CODE\\interrupt_manager.h"


#line 290 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\IM\\CODE\\interrupt_manager.h"


#line 305 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\DRIVERLAYER\\IM\\CODE\\interrupt_manager.h"

/*@}*/





/*! @}*/


/*******************************************************************************
 * EOF
 ******************************************************************************/
#line 34 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\HARDWAREABSTRACTIONLAYER\\TIMERHWABSTR\\CODE\\TimerHwAbs.h"
/*******************************************************************************
*   Debug Switch Section
*******************************************************************************/



/*******************************************************************************
*   Macro Define Section
*******************************************************************************/


/*******************************************************************************
*   Type Define Section
*******************************************************************************/


/*******************************************************************************
*   Global Variable Declaration Section
*******************************************************************************/


/*******************************************************************************
*   Global Function Declaration Section
*******************************************************************************/

/*----------------------------------------------------------------------------*/
/** initialization of LPIT driver
 **
 ** @param [in] None
 **
 ** @return HwAbs_tenStatus
 **/
/*----------------------------------------------------------------------------*/
extern HwAbs_tenStatus TimerHwAbs_enInitLPIT(void);

/*----------------------------------------------------------------------------*/
/** initialization of LPTMR driver
 **
 ** @param [in] None
 **
 ** @return None
 **/
/*----------------------------------------------------------------------------*/
extern void TimerHwAbs_vInitLPTMR(void);


/*----------------------------------------------------------------------------*/
/** Return 1ms counter
 **
 ** @param [in] None
 **
 ** @return uint32_t
 **/
/*----------------------------------------------------------------------------*/
extern uint32_t TimerHwAbs_u32Get1msCtr(void);

/*----------------------------------------------------------------------------*/
/** LPIT interrupt callback
 **
 **@param [in] None
 **
 ** @return None
 **/
/*----------------------------------------------------------------------------*/
extern void TimerHwAbs_vLPITCallback(void);

extern uint32_t TimerHwAbs_GetElapsedTime(uint32_t u32nTimCtrMarked);

#line 42 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"

/*******************************************************************************
*   Macro Define Section
*******************************************************************************/
#line 52 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"
//#define LIN_EVENTFRAME_INTEGRATION


/* #define XCP_NVM_INTEGRATION */


#line 70 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"


#line 94 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\common.h"

/*******************************************************************************
*   Type Define Section
*******************************************************************************/



/*******************************************************************************
*   Global Variable Declaration Section
*******************************************************************************/


/*******************************************************************************
*   Global Function Declaration Section
*******************************************************************************/
extern HwAbs_tenStatus enConvertErrorCode(status_t errCode);

/*----------------------------------------------------------------------------*/
/** bGetBitFromByte
 ** returns the bit value from the uint16_t type variable
 **
 ** @param [in]     none
 **
 ** @return    bool
 ** @retval    bool   True : Bit is 1
 ** 				  False : Bit is 0
 ** **
 **
 **/
/*----------------------------------------------------------------------------*/
extern _Bool bGetBitFromByte(uint16_t status, uint8_t bit);


/** vSetBitUint16
 **  Function to set the "bit_position"th bit of pVar
 **
 ** @param [pVar] uint16_t input to get bit value from
 ** @param [bit_position] bit position (15...0)
 **
 **/
/*----------------------------------------------------------------------------*/
extern void vSetBitUint16(uint16_t* pVar, uint32_t bit_position);
/**@} */


#line 37 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\UnitTest\\PIDDID_ReadDID_stub.h"






typedef unsigned short uint16_T;
typedef unsigned char uint8_T;

extern void PIDDID_ReadDID(uint16_T rtu_rdDID, uint8_T rty_srcBuf[252], uint8_T *
  rty_retVal);

#line 39 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\UnitTest\\PIDDID_WriteDID_stub.h"




typedef unsigned short uint16_T;
typedef unsigned char uint8_T;

extern uint8_T PIDDID_WriteDID(uint16_T rtu_wrDID, const uint8_T rtu_srcBuf[252],
  uint8_T rtu_wrLen);

#line 40 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\UnitTest\\PIDDID_RoutineCtrl_stub.h"






typedef unsigned short uint16_T;
typedef unsigned char uint8_T;

extern void PIDDID_RoutineCtrl(uint16_T rtu_rtnID, uint8_T rtu_rtnType, uint8_T
  rtu_rtnLen, const uint8_T rtu_rtnOption[10], uint8_T rty_TxData[10], uint8_T
  *rty_retVal);

#line 41 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\UnitTest\\PIDDID_IoCtrl_stub.h"





typedef unsigned short uint16_T;
typedef unsigned char uint8_T;

extern void PIDDID_IoCtrl(uint16_T rtu_ioDID, uint8_T rtu_CtrlParam, uint8_T
  ioLen, uint8_T rtuy_srcBuf[22], uint8_T *rty_retVal);



/*
 * File trailer for generated code.
 *
 * [EOF]
 */
#line 42 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"


/** @defgroup PIDDID PIDDID Component
 ** @ingroup Diag Layer
 **
 **  PIDDID
 ** @{ */

/** @file
 **
 ** Public PIDDID header  
 **/

/*******************************************************************************
*   File Name               :   PIDDID_Cfg.h
*   Component Name          :   PIDDID
*   UCom                    :   Freescale S12G series
*
*   Create Date             :   2017-03-09
*   Author                  :   guanam
*   Corporation             :   Inalfa Roof Systems
*
*   Abstract Description    :   Public PIDDID header for PIDDID 
*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved
*                               Unauthorized copying of this file, via any medium
*                               is strictly prohibited
*                               Proprietary and confidential
*---Revision History------------------------------------------------------------
*   Date        Author    Item     Description
*   2025-02-19	chanv    [#49510]  Added RDIDs and IODIDs for MBVan
*   2019-12-17	guanam    [#8518]  Changed PIDDID_INTERLOCK_ENABLE_LEN from 0 to 1 to support result output
*   2019-12-11	guanam    [#8466]  Changed unlearn reason data length to 4 due to data type change
*   2019-11-12	guanam    [#7009]  Add read and write service for fault snapshot data
*   2019-11-05	guanam    [#6341]  Updated ATS reversal reason data length to 18 bytes
*   2019-06-18	boutenp1  [#7054]  Add read service for SCU reset reason
*   2019-04-15  Yongdong  [#6103]  rename ID_SECURED to ID_SECURED_BYD
*   2018-11-14  zhifei    [#4330]  - ADD PIDDID_PANEL_GO_TARGET_POS_LEN DEFINE
*   2018-11-14  zhifei    [#4330]  - change PIDDID_MTC_STATUS_LEN from 3 to 2
*   2018-11-13  boutenp1  [#4330]  - Added sizes for FE0A, FE0B and FE18
*   2018-11-13  boutenp1  [#4330]  - Increased table size for R/W FE0A, FE0B and R of FE18
*   2018-10-22  guanam	  [#4289]  - Deleted #pragma MESSAGE DISABLE C1106 line to remove the compile warning 
*   2018-03-16  boutenp1  [#3594]  - Added routine override thermal protection
*   2018-02-13  guanam	  [#3379]  - added Read ATS event data service(Glass and Rollo)
*   2018-02-01  guanam	  [#3362]  - added ATD enable and disable routine control service
*   2018-01-26  guanam	  [#3377]  - added Read ATS related service(ATS information, Glass RF and Rollo RF)
*   2017-11-15  trupkesi  [#3220]  - added Read Learning Failed Reason service
*   2017-10-31  trupkesi  [#2703]  - added IOCtrl information
*   2017-03-09  trupkesi  [#2206]  - renamed PIDDID_CAL_ROUTINE_LEN to PIDDID_LEARN_ROUTINE_LEN
*                                  - added Calibration data length information
*                                  - changed table size due to new identifier
*   2017-03-09  guanam	  [#2206]  initial revision
*
*******************************************************************************/


/*******************************************************************************
*   Debug Switch Section
*******************************************************************************/

/*******************************************************************************
*    Include File Section
*******************************************************************************/



/*******************************************************************************
*   Macro Define Section
*******************************************************************************/
/* Numbers of DID Read Support */


/* DID Read/Write Table size */


/* DID Data Length */
#line 125 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"


/* Routine Ctrl Table size */

/* Routine Control Data Length */



// #define     PIDDID_PV_TEST_MODE_LEN                      ((uint8)0) 	/* Nothing to return, routine status service not supported */

// #define     PIDDID_PANEL_GO_TARGET_POS_LEN               ((uint8)0) 	/* Nothing to return, routine status service not supported */
#line 170 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"

/* IO Control Table size */

/* IO Control Data Length*/
#line 187 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"






/* DID Read Support */


/* DID Write Support */


/* ID Secured */




/* Access Type */




/* DID Session Selection (DIDSS) Configuration */
/*----------------------------------------------------------------------------------------------*/
/*               E E P D                    EOLEMS   = EOL Session */
/*               O X R S                    EXTDS    = Extended Disgnostic Session */
/*               L T G                      PRGS     = Programming Session */
/*               S D S                      DS       = Default Session */
/*                 S                        */
/*                                          */
/*----------------------------------------------------------------------------------------------*/






#line 233 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"
    

/*******************************************************************************
*   Type Define Section
*******************************************************************************/
typedef struct
{
	uint16 rxID;
	struct {
		uint8 isRead	:1;		/* Support read? */
		uint8 isWrite	:1;		/* Support write? */
		uint8 securedId	:2;		/* Secured ID */
		uint8 stSession	:4;		/* Supported Session bit3-default session,bit4-program session,bit5 extended session */
	} bitsForID;
	uint8 rxtxLen;					/* Receiving or Sending Length */
}ISOUDS_rxID;

/*******************************************************************************
*   Global Variable Declaration Section
*******************************************************************************/
extern const ISOUDS_rxID PIDDID_RdWrDID[((uint16)64)];
extern const ISOUDS_rxID PIDDID_Rtn[((uint16)36)];
extern const ISOUDS_rxID PIDDID_IoCtrlbyID[((uint8)13)];

//#pragma DATA_SEG SHARED_BOOT
extern uint8 BOOT_versionNumber[((uint8)0x09)];
//#pragma DATA_SEG DEFAULT_RAM

/*******************************************************************************
*   Global Function Declaration Section
*******************************************************************************/

/**@} */


#line 49 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"

/************************** Declaration of global symbol and constants ********/





/*LIN SID for Negative response*/

/* Master ECU node address */

/* Broadcast address */  









/* Negative response codes (ISO 14229) */
#line 89 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"


/* Negative response length */


/* State machine values */
#line 104 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS.h"








 
/********************************* Declaration of global macros ***************/









/* Shared data protection function. */



/********************************* Declaration of global types ****************/
typedef struct
{
    uint8  srvSt;          /* UDS service state */
    uint8  srvId;          /* UDS service id */
    uint8  srvNegResp;     /* Negative response code of the service */
    uint8  srvFrame;/* Service request received in single or multiple frame */
    uint16 srvLen;         /* length */
}ISOUDS_ConfType;



/****************************** External links of global variables ************/

//#pragma DATA_SEG SHARED_RAM
extern uint8 LINSTACK_ECUSt;
//#pragma DATA_SEG DEFAULT




/* UDS Buffer for data */

extern uint8 *ISOUDS_Buff;

/* UDS current session */
extern uint8 ISOUDS_Sess;

/* Timer for Ecu Reset */
extern uint32 ISOUDS_TmrECURst;

/* UDS configuration */
extern ISOUDS_ConfType ISOUDS_Conf;

/****************************** External links of global constants ************/

/*******************************************************************************
**                                      FUNCTIONS                             **
*******************************************************************************/

/********************************** Function definitions **********************/
extern void UDS_SrvPInit (void);
extern void UDS_SrvPTask (void);
extern uint8 UDS_SrvPNewMsgInd (const uint8 *srvBuffPtr, uint16 frmLnth, boolean firstFrame);
extern void ISOUDS_Rst (void);
extern void UDS_SrvPIndTxMsg(void);
extern void ISOUDS_ReqECUReset (uint8 u8ResetType);
extern void UDS_Init(void);
extern void UDS(void);
extern boolean UDS_LookUpTbl (uint16 rxID, const ISOUDS_rxID LookUpTbl[], uint16 numSizeTbl, uint16 *idxTbl, uint8 stAccessType);
extern uint8 ISOUDS_GetECUResetReq(void);
extern void ISOUDS_EcuResetPosRespComplete();
extern _Bool ISOUDS_GetEcuResetPosRespStatus();
extern void ISOUDS_ReqECUReset(uint8 u8ResetType);
extern void ISOUDS_ClearEcuResetPosRespFlag();
#line 18 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS_TrnsfrDa.h"

/************************** Declaration of global symbol and constants ****************************/
#line 27 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\ISOUDS_TrnsfrDa.h"

/********************************* Declaration of global macros ***********************************/

/********************************* Declaration of global types ************************************/
/* SID of Transfer Data service */


/****************************** External links of global variables ********************************/

/****************************** External links of global constants ********************************/

/***************************************************************************************************
**                                      FUNCTIONS                                                 **
***************************************************************************************************/

/********************************** Function definitions ******************************************/
extern void ISOUDS_TrnsfrDa (ISOUDS_ConfType *ISOUDSConfPtr, uint8 dataBuff[]);
extern void ISOUDS_TrnsfrDaExit (void);
extern boolean ISOUDS_ReqTrnsfrDa (uint32 addr, uint32 size, uint8 servreq);
extern boolean ISOUDS_ChkTrnsfrExit (void);

#line 15 "C:/Users/<USER>/MBVan/MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC/MBDevStrm/SwApplication/Application/HardwareAbstractionLayer/DiagComMan/Code/ISOUDS_TrnsfrDa.c"

/********************** Declaration of local symbol and constants *********************************/

/********************************* Declaration of local macros ************************************/


/*  MISRA-C: 2012 Rules 2.5 VIOLATION:
    A project should not contain unused macro declarations.
    Macros are required for future implementation.    
*/
/* NOTE:REMOVE BELOW MACROS AFTER INTEGRATION WITH FLASH MODULE */



/********************************* Declaration of local types *************************************/

/******************************* Declaration of local variables ***********************************/
/* Transfer data block sequence counter */
static uint8 ISOUDS_DwBlkSeqCntr;
/* Transfer data block sequence counter for upload */
static uint8 ISOUDS_UpBlkSeqCntr;
/* Transfer data state */
static uint8 ISOUDS_TrnsfrDaSt;
/* Memory Size */
static uint32 ISOUDS_MemSize;
/* Memory start address */
static uint32 ISOUDS_MemStartAddr;
/* Number of bytes sent in previous response */
static uint16 ISOUDS_PrevLen;
/******************************* Declaration of local constants ***********************************/

/****************************** Declaration of exported variables *********************************/

/****************************** Declaration of exported constants *********************************/

/***************************************************************************************************
**                                      FUNCTIONS                                                 **
***************************************************************************************************/

/**************************** Internal functions declarations *************************************/

/******************************** Function definitions ********************************************/

/***************************************************************************************************
** Function                 : ISOUDS_TrnsfrDa

** Description              : Sends response to Transfer data service request

** Parameter ISOUDSConfPtr : Pointer to service configuration structure

** Parameter dataBuff       : Data array

** Return value             : None

** Remarks                  : None
***************************************************************************************************/
/*  MISRA-C: 2012 Rules 2.2 VIOLATION:
    There shall be no dead code.There shall be no dead code: useless write
    The write operations are required for future implementation.   
*/

void ISOUDS_TrnsfrDa (ISOUDS_ConfType *ISOUDSConfPtr, uint8 dataBuff[])
{
    uint8  flshacc;
    uint16  length;
    uint16  idx;
    uint8   *Readptr;

    flshacc = 0x0U;
    
    /* check if the length is valid */
    if (ISOUDSConfPtr->srvLen > (uint16)(1))
    {
        if ((ISOUDS_TrnsfrDaSt == (uint8)0))
        {
            /* send negative response. Request Sequence Error */
            ISOUDSConfPtr->srvNegResp = (uint8)(0x24U);

            /* Negative response */
            ISOUDSConfPtr->srvSt = (uint8)(0x04U);
        }
        else
        {
            switch (ISOUDS_TrnsfrDaSt)
            {
                case (uint8)1:
                case (uint8)2:
                case (uint8)3:
                {
                    /* get the length */
                    length = ISOUDSConfPtr->srvLen - (uint16)2;

                    /* check whether this function is being called due to response pending */
                    if (ISOUDSConfPtr->srvSt == (uint8)(0x05U))
                    {

                        /****************************************************************

                        INSERT CODE TO CHECK IF FLASH WAS SUCCESS &
                        UPDATE THE VARIABLE flshacc ACCORDINGLY

                        *****************************************************************/
                        /* MISRA-C:2012 RULE 9.1 VIOLATION:The value of an object with  
                           automatic storage duration shall not be read before it has been set.
                           'flshacc' variables setting needs to be taken care by user
                            in future implementation */
                        /* if previous flash is success */
                        if ((uint8)0x02 == flshacc)
                        {
                            /* update the address */
                            ISOUDS_MemStartAddr += (uint32)length;

                            /* decrement the number of bytes written */
                            ISOUDS_MemSize -= (uint32)length;

                            /* send successful download block sequence counter*/
                            dataBuff[0] = ISOUDS_DwBlkSeqCntr;

                            /* increment the download block sequence counter */
                            ISOUDS_DwBlkSeqCntr += (uint8)1;

                            if (ISOUDS_MemSize == (uint32)0)
                            {
                                /* change the state transfer finish */
                                ISOUDS_TrnsfrDaSt = (uint8)3;
                            }

                            /* Response length for the current Service - including SID */
                            ISOUDSConfPtr->srvLen = (uint16)2;

                            /* Send positive response */
                            ISOUDSConfPtr->srvSt = (uint8)(0x03U);
                        }
                        else if((uint8)0x05 == flshacc)
                        {
                            /* send negative response. General Programming Failure */
                            ISOUDSConfPtr->srvNegResp = (uint8)(0x72U);

                            /* Negative response */
                            ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                        }
                        else
                        {
                            /* wait in Response pending */
                            ISOUDSConfPtr->srvSt = (uint8)(0x05U);
                        }
                    }
                    else
                    {
                        /* check download sequence counter with the requested sequence counter*/
                        if ((ISOUDS_TrnsfrDaSt != (uint8)3) && (ISOUDS_DwBlkSeqCntr == dataBuff[(uint8)0]))
                        {
                            /* check if we have received more bytes than the required no. of bytes */
                            if ((uint32)length <= ISOUDS_MemSize)
                            {

                                /****************************************************************

                                INSERT CODE TO START FLASHING FROM THE ADDRESS ISOUDS_MemStartAddr
                                WITH DATA BEING POINTED BY dataBuff[1] & SIZE INDICATED BY length &
                                UPDATE THE VARIABLE flshacc ACCORDINGLY

                                *****************************************************************/

                                /* if flash request is not accepted*/
                                if (flshacc != (uint8)0)
                                {
                                    /* send negative response. General Programming Failure */
                                    ISOUDSConfPtr->srvNegResp = (uint8)(0x72U);

                                    /* Negative response */
                                    ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                                }
                                else
                                {
                                    /* update state machine to Request download transfer data */
                                    ISOUDS_TrnsfrDaSt = (uint8)2;

                                    /* wait until a positive response is obtained */

                                    /* Response pending */
                                    ISOUDSConfPtr->srvNegResp = (uint8)(0x78U);

                                    /* Negative response */
                                    ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                                }
                            }
                            else
                            {
                                /* send negative response. Transfer Data Suspended */
                                ISOUDSConfPtr->srvNegResp = (uint8)(0x71U);

                                /* Negative response */
                                ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                            }
                        }
                        /* check download sequence counter with previous requested sequence counter*/
                        else if ((ISOUDS_TrnsfrDaSt != (uint8)1)
                                && ((ISOUDS_DwBlkSeqCntr - (uint8)0x01) == dataBuff[0]))
                        {
                            /* send successful download block sequence counter*/
                            dataBuff[0] = ISOUDS_DwBlkSeqCntr - (uint8)0x01;

                            /* Response length for the current Service - including SID */
                            ISOUDSConfPtr->srvLen = (uint16)2;

                            /* Send positive response */
                            ISOUDSConfPtr->srvSt = (uint8)(0x03U);
                        }
                        else if (ISOUDS_TrnsfrDaSt == (uint8)3)
                        {
                            /* Request Sequence Error. All the bytes have been read */
                            ISOUDSConfPtr->srvNegResp = (uint8)(0x24U);

                            /* Negative response */
                            ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                        }
                        else
                        {
                            /* send negative response. Wrong Block Sequence Counter */
                            ISOUDSConfPtr->srvNegResp = (uint8)(0x73U);

                            /* Negative response */
                            ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                        }
                    }
                }/* end of case ISOUDS_REQDW */
                break;

                case (uint8)4:
                case (uint8)5:
                case (uint8)6:
                {
                    /* check upload sequence counter with the requested sequence counter*/
                    if ((ISOUDS_TrnsfrDaSt != (uint8)6) && (ISOUDS_UpBlkSeqCntr == dataBuff[(uint8)0]))
                    {
                        /* check number of bytes to be sent */
                        if (ISOUDS_MemSize > (uint32)((uint32)(256) - (uint8)2))
                        {
                            /* read only upto what can be handled */
                            length = (uint16)((uint16)(256) - (uint8)2);
                        }
                        else
                        {
                            /* read remaining bytes */
                            length = (uint16)ISOUDS_MemSize;
                        }
                        /* MISRA RULE 11.4 VIOLATION: cast from pointer to uint8* pointer to facilitate reading -
                           - of individual bytes from the address locations */
                        /* get the address */
                        Readptr = (uint8*)ISOUDS_MemStartAddr;

                        for (idx = (uint16)0; idx < length; idx++)
                        {
                            /* Read data into array */
                            dataBuff[idx + (uint8)1] = *((uint8*)&Readptr[idx]);
                        }

                        /* decrement the number of bytes read */
                        ISOUDS_MemSize -= (uint32)length;

                        /* increment the address */
                        ISOUDS_MemStartAddr += (uint32)length;

                        /* Store number of bytes transmitted in this response, if we get
                        request for transmitting the same data due to timeout in application
                        layer while receiving response.Refer ISO 14229 */
                        ISOUDS_PrevLen = (uint16)length;

                        /* send successful upload block sequence counter*/
                        dataBuff[0] = ISOUDS_UpBlkSeqCntr;

                        /* increment upload sequence counter */
                        ISOUDS_UpBlkSeqCntr += (uint8)1;

                        if (ISOUDS_MemSize == (uint32)0x0)
                        {
                            /* update state machine to Request upload transfer data finish */
                            ISOUDS_TrnsfrDaSt = (uint8)6;
                        }
                        else
                        {
                            /* update state machine to Request upload transfer data */
                            ISOUDS_TrnsfrDaSt = (uint8)5;
                        }

                        /* Response length for the current Service - including SID and sequence counter */
                        ISOUDSConfPtr->srvLen = (uint16)(length + (uint16)2);

                        /* Positive response response */
                        ISOUDSConfPtr->srvSt = (uint8)(0x03U);
                    }
                    /* check upload sequence counter with previous requested sequence counter*/
                    else if ((ISOUDS_TrnsfrDaSt != (uint8)4)
                            && ((ISOUDS_UpBlkSeqCntr - (uint8)0x01) == dataBuff[0]))
                    {
                        /* NOTE: The server responds with transfer data positively,
                        but application might not receive the data. In such a scenario, application
                        times out and re requests the data. In such case we reach this else if
                        condition. since the data is already available in UDS buffer, we can simply
                        set the length (previously transmitted number of bytes) and transmit the
                        data. instead of re requesting the data, if application request for some
                        other service, then UDS buffer will not hold data w.r.t. upload service. In
                        such a scenario, APPLICATION has to re read the data with address set to
                        (ISOUDS_MemStartAddr - (uint32)ISOUDS_PrevLen) and number of bytes to
                        read should be set to ISOUDS_PrevLen.*/

                        /* send successful upload block sequence counter*/
                        dataBuff[0] = ISOUDS_UpBlkSeqCntr - (uint8)0x01;

                        /* Response length for the current Service - including SID */
                        ISOUDSConfPtr->srvLen = (uint16)(ISOUDS_PrevLen + (uint16)2);

                        /* Send positive response */
                        ISOUDSConfPtr->srvSt = (uint8)(0x03U);
                    }
                    else if (ISOUDS_TrnsfrDaSt == (uint8)6)
                    {
                        /* Request Sequence Error. All the bytes have been read */
                        ISOUDSConfPtr->srvNegResp = (uint8)(0x24U);

                        /* Negative response */
                        ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                    }
                    else
                    {
                        /* Wrong Block Sequence Counter */
                        ISOUDSConfPtr->srvNegResp = (uint8)(0x73U);

                        /* Negative response */
                        ISOUDSConfPtr->srvSt = (uint8)(0x04U);
                    }
                }/* end of case ISOUDS_REQUP */
                break;

                default:
                {
                    /* we shouldn't reach here */
                    ISOUDS_TrnsfrDaSt = (uint8)0;
                }
                break;
            }/* end of switch */
        }
    }
    else
    {
        /* Invalid Message Length Or Invalid Format */
        ISOUDSConfPtr->srvNegResp = (uint8)(0x13U);

        /* Negative response */
        ISOUDSConfPtr->srvSt = (uint8)(0x04U);
    }
}

/***************************************************************************************************
** Function                 : ISOUDS_TrnsfrDaExit

** Description              : Resets Transfer data service state

** Parameters               : None

** Return value             : None
***************************************************************************************************/
void ISOUDS_TrnsfrDaExit (void)
{
    /* change the state to idle */
    ISOUDS_TrnsfrDaSt = (uint8)0;

    /* Reset the Memory start address */
    ISOUDS_MemStartAddr = (uint32)0x00;

    /* Reset the Memory size */
    ISOUDS_MemSize =  (uint32)0x00;

    /* Reset download block sequence counter */
    ISOUDS_DwBlkSeqCntr = (uint8)0;

    /* Reset upload block sequence counter */
    ISOUDS_UpBlkSeqCntr = (uint8)0;

    /* Number of bytes sent in previous response */
    ISOUDS_PrevLen = (uint16)0;
}

/***************************************************************************************************
** Function                 : ISOUDS_ReqTrnsfrDa

** Description              : Request for Transfer data service

** Parameter addr           : Address form where the download/upload has to start

** Parameter size           : Number of bytes to be downloaded/uploaded

** Parameter servreq        : Service Request- Request Download or Request Upload

** Return value             : (boolean)TRUE : Success (boolean)FALSE: Download/Upload in progress
***************************************************************************************************/
boolean ISOUDS_ReqTrnsfrDa (uint32 addr, uint32 size, uint8 servreq)
{
    boolean return_val;

    /* init return value */
    return_val = (boolean)0u;

    if (((ISOUDS_TrnsfrDaSt == (uint8)2) || (ISOUDS_TrnsfrDaSt == (uint8)3)) ||
        ((ISOUDS_TrnsfrDaSt == (uint8)5) || (ISOUDS_TrnsfrDaSt == (uint8)6)))
    {
        /* transfer data for download/upload is in progress */
        return_val = (boolean)0u;
    }
    else
    {
        if ((uint8)1 == servreq)
        {
            /* change the state to request download */
            ISOUDS_TrnsfrDaSt = (uint8)1;

            /* initialise download sequence counter */
            ISOUDS_DwBlkSeqCntr = (uint8)1;

            /* reset upload sequence counter */
            ISOUDS_UpBlkSeqCntr = (uint8)0;
        }
        else if ((uint8)4 == servreq)
        {
            /* change the state to request upload */
            ISOUDS_TrnsfrDaSt = (uint8)4;

            /* initialise download sequence counter */
            ISOUDS_UpBlkSeqCntr = (uint8)1;

            /* reset download sequence counter */
            ISOUDS_DwBlkSeqCntr = (uint8)0;
        }
        else
        {
            /* we shouldn't reach here. Change state to idle */
            ISOUDS_TrnsfrDaSt = (uint8)0;
        }


        /* get the address from where the data needs to be read/written */
        ISOUDS_MemStartAddr = addr;

        /* get the number of bytes to be read/written */
        ISOUDS_MemSize = size;

        /* Number of bytes sent in previous response */
        ISOUDS_PrevLen = (uint16)0;

        /* transfer data for Upload/Download accepted */
        return_val = (boolean)1u;
    }

    return (return_val);
}

/***************************************************************************************************
** Function                 : ISOUDS_ChkTrnsfrExit

** Description              : Checks for Transfer data service exit

** Parameters               : None

** Return value             : (boolean)TRUE: exit transfer data/ (boolean)FALSE: conditions not correct to exit
***************************************************************************************************/
boolean ISOUDS_ChkTrnsfrExit (void)
{
    boolean return_val;

    /* check if download/upload is active */
    if (((ISOUDS_TrnsfrDaSt == (uint8)0) && (ISOUDS_DwBlkSeqCntr == (uint8)0)) &&
         (ISOUDS_UpBlkSeqCntr == (uint8)0))
    {
        return_val = (boolean)0u;
    }
    else
    {
        return_val = (boolean)1u;
    }

    return (return_val);
}
#line 3 "vcast_preprocess.10472.0.c"
