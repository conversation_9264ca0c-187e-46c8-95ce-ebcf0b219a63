<?xml version="1.0" encoding="UTF-8"?>
<changeset checksum="3993718270">
  <modification id="0">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Enable">
      <line>8506</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_1_1;
extern unsigned P_21_1_2;
extern unsigned char SBF_21_1;
if(SBF_21_1) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1036976405
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1036976405
  if ( vcast_is_in_driver ) {
    P_21_1_1 = base;
    P_21_1_2 = delay;
    vCAST_COMMON_STUB_PROC_21( 21, 1, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1036976405
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1036976405
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="1">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Enable">
      <line>8506</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="2">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Enable">
      <line>8523</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="3">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_1_1;
unsigned P_21_1_2;
unsigned char SBF_21_1 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="4">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Disable">
      <line>8534</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_2_1;
extern unsigned char SBF_21_2;
if(SBF_21_2) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3934361587
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3934361587
  if ( vcast_is_in_driver ) {
    P_21_2_1 = base;
    vCAST_COMMON_STUB_PROC_21( 21, 2, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3934361587
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3934361587
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="5">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Disable">
      <line>8534</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="6">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Disable">
      <line>8536</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="7">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_2_1;
unsigned char SBF_21_2 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="8">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Reset">
      <line>8547</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_3_1;
extern unsigned P_21_3_2;
extern unsigned char SBF_21_3;
if(SBF_21_3) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1588594490
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1588594490
  if ( vcast_is_in_driver ) {
    P_21_3_1 = base;
    P_21_3_2 = delay;
    vCAST_COMMON_STUB_PROC_21( 21, 3, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1588594490
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1588594490
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="9">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Reset">
      <line>8547</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="10">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Reset">
      <line>8565</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="11">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_3_1;
unsigned P_21_3_2;
unsigned char SBF_21_3 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="12">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StartTimerChannels">
      <line>8584</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_4_1;
extern unsigned P_21_4_2;
extern unsigned char SBF_21_4;
if(SBF_21_4) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_512632996
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_512632996
  if ( vcast_is_in_driver ) {
    P_21_4_1 = base;
    P_21_4_2 = mask;
    vCAST_COMMON_STUB_PROC_21( 21, 4, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_512632996
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_512632996
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="13">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StartTimerChannels">
      <line>8584</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="14">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_StartTimerChannels">
      <line>8586</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="15">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_4_1;
unsigned P_21_4_2;
unsigned char SBF_21_4 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="16">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StopTimerChannels">
      <line>8606</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_5_1;
extern unsigned P_21_5_2;
extern unsigned char SBF_21_5;
if(SBF_21_5) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2951192847
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2951192847
  if ( vcast_is_in_driver ) {
    P_21_5_1 = base;
    P_21_5_2 = mask;
    vCAST_COMMON_STUB_PROC_21( 21, 5, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2951192847
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2951192847
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="17">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StopTimerChannels">
      <line>8606</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="18">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_StopTimerChannels">
      <line>8608</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="19">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_5_1;
unsigned P_21_5_2;
unsigned char SBF_21_5 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="20">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerPeriodByCount">
      <line>8630</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_6_1;
extern unsigned P_21_6_2;
extern unsigned P_21_6_3;
extern unsigned char SBF_21_6;
if(SBF_21_6) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_937993421
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_937993421
  if ( vcast_is_in_driver ) {
    P_21_6_1 = base;
    P_21_6_2 = channel;
    P_21_6_3 = count;
    vCAST_COMMON_STUB_PROC_21( 21, 6, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_937993421
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_937993421
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="21">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerPeriodByCount">
      <line>8630</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="22">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerPeriodByCount">
      <line>8632</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="23">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_6_1;
unsigned P_21_6_2;
unsigned P_21_6_3;
unsigned char SBF_21_6 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="24">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerPeriodByCount">
      <line>8645</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_7_1;
extern unsigned P_21_7_2;
extern unsigned R_21_7;
extern unsigned char SBF_21_7;
if(SBF_21_7) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1861595802
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1861595802
  if ( vcast_is_in_driver ) {
    P_21_7_1 = ((LPIT_Type *)(base));
    P_21_7_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 7, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1861595802
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1861595802
  vCAST_USER_CODE_TIMER_START();
  return R_21_7;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="25">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerPeriodByCount">
      <line>8645</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="26">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetTimerPeriodByCount">
      <line>8647</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="27">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_7_1;
unsigned P_21_7_2;
unsigned R_21_7;
unsigned char SBF_21_7 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="28">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetCurrentTimerCount">
      <line>8662</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_8_1;
extern unsigned P_21_8_2;
extern unsigned R_21_8;
extern unsigned char SBF_21_8;
if(SBF_21_8) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1231179100
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1231179100
  if ( vcast_is_in_driver ) {
    P_21_8_1 = ((LPIT_Type *)(base));
    P_21_8_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 8, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1231179100
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1231179100
  vCAST_USER_CODE_TIMER_START();
  return R_21_8;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="29">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetCurrentTimerCount">
      <line>8662</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="30">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetCurrentTimerCount">
      <line>8664</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="31">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_8_1;
unsigned P_21_8_2;
unsigned R_21_8;
unsigned char SBF_21_8 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="32">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_EnableInterruptTimerChannels">
      <line>8681</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_9_1;
extern unsigned P_21_9_2;
extern unsigned char SBF_21_9;
if(SBF_21_9) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3101418129
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3101418129
  if ( vcast_is_in_driver ) {
    P_21_9_1 = base;
    P_21_9_2 = mask;
    vCAST_COMMON_STUB_PROC_21( 21, 9, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3101418129
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3101418129
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="33">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_EnableInterruptTimerChannels">
      <line>8681</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="34">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_EnableInterruptTimerChannels">
      <line>8683</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="35">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_9_1;
unsigned P_21_9_2;
unsigned char SBF_21_9 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="36">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_DisableInterruptTimerChannels">
      <line>8700</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_10_1;
extern unsigned P_21_10_2;
extern unsigned char SBF_21_10;
if(SBF_21_10) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3927551259
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3927551259
  if ( vcast_is_in_driver ) {
    P_21_10_1 = base;
    P_21_10_2 = mask;
    vCAST_COMMON_STUB_PROC_21( 21, 10, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3927551259
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3927551259
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="37">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_DisableInterruptTimerChannels">
      <line>8700</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="38">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_DisableInterruptTimerChannels">
      <line>8702</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="39">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_10_1;
unsigned P_21_10_2;
unsigned char SBF_21_10 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="40">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8720</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_11_1;
extern unsigned P_21_11_2;
extern unsigned R_21_11;
extern unsigned char SBF_21_11;
if(SBF_21_11) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_991102759
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_991102759
  if ( vcast_is_in_driver ) {
    P_21_11_1 = ((LPIT_Type *)(base));
    P_21_11_2 = mask;
    vCAST_COMMON_STUB_PROC_21( 21, 11, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_991102759
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_991102759
  vCAST_USER_CODE_TIMER_START();
  return R_21_11;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="41">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8720</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="42">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8722</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="43">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_11_1;
unsigned P_21_11_2;
unsigned R_21_11;
unsigned char SBF_21_11 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="44">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8739</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_12_1;
extern unsigned P_21_12_2;
extern unsigned char SBF_21_12;
if(SBF_21_12) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1087092936
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1087092936
  if ( vcast_is_in_driver ) {
    P_21_12_1 = base;
    P_21_12_2 = mask;
    vCAST_COMMON_STUB_PROC_21( 21, 12, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1087092936
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1087092936
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="45">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8739</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="46">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8746</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="47">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_12_1;
unsigned P_21_12_2;
unsigned char SBF_21_12 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="48">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelModeCmd">
      <line>8761</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_13_1;
extern unsigned P_21_13_2;
extern lpit_timer_modes_t P_21_13_3;
extern unsigned char SBF_21_13;
if(SBF_21_13) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3324386835
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3324386835
  if ( vcast_is_in_driver ) {
    P_21_13_1 = base;
    P_21_13_2 = channel;
    P_21_13_3 = mode;
    vCAST_COMMON_STUB_PROC_21( 21, 13, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3324386835
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3324386835
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="49">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelModeCmd">
      <line>8761</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="50">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerChannelModeCmd">
      <line>8764</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="51">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_13_1;
unsigned P_21_13_2;
lpit_timer_modes_t P_21_13_3;
unsigned char SBF_21_13 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="52">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerChannelModeCmd">
      <line>8777</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_14_1;
extern unsigned P_21_14_2;
extern lpit_timer_modes_t R_21_14;
extern unsigned char SBF_21_14;
if(SBF_21_14) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_863029074
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_863029074
  if ( vcast_is_in_driver ) {
    P_21_14_1 = ((LPIT_Type *)(base));
    P_21_14_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 14, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_863029074
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_863029074
  vCAST_USER_CODE_TIMER_START();
  return R_21_14;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="53">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerChannelModeCmd">
      <line>8777</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="54">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetTimerChannelModeCmd">
      <line>8802</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="55">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_14_1;
unsigned P_21_14_2;
lpit_timer_modes_t R_21_14;
unsigned char SBF_21_14 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="56">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSelectCmd">
      <line>8818</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_15_1;
extern unsigned P_21_15_2;
extern unsigned P_21_15_3;
extern unsigned char SBF_21_15;
if(SBF_21_15) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3396282018
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3396282018
  if ( vcast_is_in_driver ) {
    P_21_15_1 = base;
    P_21_15_2 = channel;
    P_21_15_3 = triggerChannelSelect;
    vCAST_COMMON_STUB_PROC_21( 21, 15, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3396282018
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3396282018
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="57">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSelectCmd">
      <line>8818</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="58">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTriggerSelectCmd">
      <line>8821</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="59">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_15_1;
unsigned P_21_15_2;
unsigned P_21_15_3;
unsigned char SBF_21_15 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="60">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSourceCmd">
      <line>8835</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_16_1;
extern unsigned P_21_16_2;
extern lpit_trigger_source_t P_21_16_3;
extern unsigned char SBF_21_16;
if(SBF_21_16) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1604313518
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1604313518
  if ( vcast_is_in_driver ) {
    P_21_16_1 = base;
    P_21_16_2 = channel;
    P_21_16_3 = triggerSource;
    vCAST_COMMON_STUB_PROC_21( 21, 16, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1604313518
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1604313518
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="61">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSourceCmd">
      <line>8835</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="62">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTriggerSourceCmd">
      <line>8838</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="63">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_16_1;
unsigned P_21_16_2;
lpit_trigger_source_t P_21_16_3;
unsigned char SBF_21_16 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="64">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetReloadOnTriggerCmd">
      <line>8854</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_17_1;
extern unsigned P_21_17_2;
extern _Bool P_21_17_3;
extern unsigned char SBF_21_17;
if(SBF_21_17) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3946980553
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3946980553
  if ( vcast_is_in_driver ) {
    P_21_17_1 = base;
    P_21_17_2 = channel;
    P_21_17_3 = isReloadOnTrigger;
    vCAST_COMMON_STUB_PROC_21( 21, 17, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3946980553
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3946980553
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="65">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetReloadOnTriggerCmd">
      <line>8854</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="66">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetReloadOnTriggerCmd">
      <line>8857</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="67">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_17_1;
unsigned P_21_17_2;
_Bool P_21_17_3;
unsigned char SBF_21_17 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="68">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStopOnInterruptCmd">
      <line>8873</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_18_1;
extern unsigned P_21_18_2;
extern _Bool P_21_18_3;
extern unsigned char SBF_21_18;
if(SBF_21_18) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_786862186
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_786862186
  if ( vcast_is_in_driver ) {
    P_21_18_1 = base;
    P_21_18_2 = channel;
    P_21_18_3 = isStopOnInterrupt;
    vCAST_COMMON_STUB_PROC_21( 21, 18, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_786862186
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_786862186
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="69">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStopOnInterruptCmd">
      <line>8873</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="70">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetStopOnInterruptCmd">
      <line>8876</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="71">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_18_1;
unsigned P_21_18_2;
_Bool P_21_18_3;
unsigned char SBF_21_18 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="72">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStartOnTriggerCmd">
      <line>8893</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_19_1;
extern unsigned P_21_19_2;
extern _Bool P_21_19_3;
extern unsigned char SBF_21_19;
if(SBF_21_19) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_971397961
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_971397961
  if ( vcast_is_in_driver ) {
    P_21_19_1 = base;
    P_21_19_2 = channel;
    P_21_19_3 = isStartOnTrigger;
    vCAST_COMMON_STUB_PROC_21( 21, 19, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_971397961
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_971397961
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="73">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStartOnTriggerCmd">
      <line>8893</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="74">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetStartOnTriggerCmd">
      <line>8896</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="75">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_19_1;
unsigned P_21_19_2;
_Bool P_21_19_3;
unsigned char SBF_21_19 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="76">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelChainCmd">
      <line>8912</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_20_1;
extern unsigned P_21_20_2;
extern _Bool P_21_20_3;
extern unsigned char SBF_21_20;
if(SBF_21_20) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3908885058
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3908885058
  if ( vcast_is_in_driver ) {
    P_21_20_1 = base;
    P_21_20_2 = channel;
    P_21_20_3 = isChannelChained;
    vCAST_COMMON_STUB_PROC_21( 21, 20, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3908885058
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3908885058
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="77">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelChainCmd">
      <line>8912</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="78">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerChannelChainCmd">
      <line>8915</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="79">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_20_1;
unsigned P_21_20_2;
_Bool P_21_20_3;
unsigned char SBF_21_20 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="80">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDebugCmd">
      <line>8932</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_21_1;
extern _Bool P_21_21_2;
extern unsigned char SBF_21_21;
if(SBF_21_21) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3232929205
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3232929205
  if ( vcast_is_in_driver ) {
    P_21_21_1 = base;
    P_21_21_2 = isRunInDebug;
    vCAST_COMMON_STUB_PROC_21( 21, 21, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3232929205
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3232929205
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="81">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDebugCmd">
      <line>8932</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="82">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerRunInDebugCmd">
      <line>8935</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="83">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_21_1;
_Bool P_21_21_2;
unsigned char SBF_21_21 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="84">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDozeCmd">
      <line>8951</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_21_22_1;
extern _Bool P_21_22_2;
extern unsigned char SBF_21_22;
if(SBF_21_22) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3944652595
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3944652595
  if ( vcast_is_in_driver ) {
    P_21_22_1 = base;
    P_21_22_2 = isRunInDoze;
    vCAST_COMMON_STUB_PROC_21( 21, 22, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3944652595
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3944652595
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="85">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDozeCmd">
      <line>8951</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="86">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerRunInDozeCmd">
      <line>8954</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="87">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_21_22_1;
_Bool P_21_22_2;
unsigned char SBF_21_22 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="88">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_GetCmdCompleteFlag">
      <line>14802</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool R_21_23;
extern unsigned char SBF_21_23;
if(SBF_21_23) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2103085494
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2103085494
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_21( 21, 23, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2103085494
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2103085494
  vCAST_USER_CODE_TIMER_START();
  return R_21_23;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="89">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_GetCmdCompleteFlag">
      <line>14802</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="90">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FLASH_DRV_GetCmdCompleteFlag">
      <line>14804</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="91">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool R_21_23;
unsigned char SBF_21_23 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="92">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_GetReadColisionFlag">
      <line>14833</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool R_21_24;
extern unsigned char SBF_21_24;
if(SBF_21_24) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2708202769
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2708202769
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_21( 21, 24, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2708202769
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2708202769
  vCAST_USER_CODE_TIMER_START();
  return R_21_24;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="93">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_GetReadColisionFlag">
      <line>14833</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="94">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FLASH_DRV_GetReadColisionFlag">
      <line>14835</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="95">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool R_21_24;
unsigned char SBF_21_24 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="96">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_ClearReadColisionFlag">
      <line>14843</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern unsigned char SBF_21_25;
if(SBF_21_25) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2519252637
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2519252637
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_21( 21, 25, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2519252637
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2519252637
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="97">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_ClearReadColisionFlag">
      <line>14843</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="98">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FLASH_DRV_ClearReadColisionFlag">
      <line>14845</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="99">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
unsigned char SBF_21_25 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="100">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_GetDoubleBitFaultFlag">
      <line>14877</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool R_21_26;
extern unsigned char SBF_21_26;
if(SBF_21_26) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_925900247
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_925900247
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_21( 21, 26, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_925900247
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_925900247
  vCAST_USER_CODE_TIMER_START();
  return R_21_26;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="101">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_GetDoubleBitFaultFlag">
      <line>14877</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="102">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FLASH_DRV_GetDoubleBitFaultFlag">
      <line>14879</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="103">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool R_21_26;
unsigned char SBF_21_26 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="104">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_ClearDoubleBitFaultFlag">
      <line>14887</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern unsigned char SBF_21_27;
if(SBF_21_27) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1556051375
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1556051375
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_21( 21, 27, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1556051375
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1556051375
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="105">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_ClearDoubleBitFaultFlag">
      <line>14887</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="106">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FLASH_DRV_ClearDoubleBitFaultFlag">
      <line>14889</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="107">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
unsigned char SBF_21_27 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="108">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_ForceDoubleBitFaultDetectCmd">
      <line>14902</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool P_21_28_1;
extern unsigned char SBF_21_28;
if(SBF_21_28) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_868415177
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_868415177
  if ( vcast_is_in_driver ) {
    P_21_28_1 = isEnable;
    vCAST_COMMON_STUB_PROC_21( 21, 28, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_868415177
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_868415177
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="109">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FLASH_DRV_ForceDoubleBitFaultDetectCmd">
      <line>14902</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="110">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FLASH_DRV_ForceDoubleBitFaultDetectCmd">
      <line>14911</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="111">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool P_21_28_1;
unsigned char SBF_21_28 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="112">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetClockFilterPs">
      <line>18880</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_29_1;
extern unsigned char P_21_29_2;
extern unsigned char SBF_21_29;
if(SBF_21_29) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2126549812
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2126549812
  if ( vcast_is_in_driver ) {
    P_21_29_1 = ftmBase;
    P_21_29_2 = filterPrescale;
    vCAST_COMMON_STUB_PROC_21( 21, 29, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2126549812
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2126549812
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="113">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetClockFilterPs">
      <line>18880</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="114">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetClockFilterPs">
      <line>18882</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="115">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_29_1;
unsigned char P_21_29_2;
unsigned char SBF_21_29 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="116">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetClockFilterPs">
      <line>18894</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_30_1;
extern unsigned char R_21_30;
extern unsigned char SBF_21_30;
if(SBF_21_30) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1289150765
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1289150765
  if ( vcast_is_in_driver ) {
    P_21_30_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 30, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1289150765
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1289150765
  vCAST_USER_CODE_TIMER_START();
  return R_21_30;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="117">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetClockFilterPs">
      <line>18894</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="118">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetClockFilterPs">
      <line>18896</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="119">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_30_1;
unsigned char R_21_30;
unsigned char SBF_21_30 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="120">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetCounter">
      <line>18908</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_31_1;
extern unsigned short R_21_31;
extern unsigned char SBF_21_31;
if(SBF_21_31) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3479720702
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3479720702
  if ( vcast_is_in_driver ) {
    P_21_31_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 31, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3479720702
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3479720702
  vCAST_USER_CODE_TIMER_START();
  return R_21_31;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="121">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetCounter">
      <line>18908</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="122">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetCounter">
      <line>18910</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="123">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_31_1;
unsigned short R_21_31;
unsigned char SBF_21_31 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="124">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetMod">
      <line>18922</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_32_1;
extern unsigned short R_21_32;
extern unsigned char SBF_21_32;
if(SBF_21_32) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_510825270
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_510825270
  if ( vcast_is_in_driver ) {
    P_21_32_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 32, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_510825270
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_510825270
  vCAST_USER_CODE_TIMER_START();
  return R_21_32;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="125">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetMod">
      <line>18922</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="126">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetMod">
      <line>18924</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="127">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_32_1;
unsigned short R_21_32;
unsigned char SBF_21_32 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="128">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetCounterInitVal">
      <line>18936</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_33_1;
extern unsigned short R_21_33;
extern unsigned char SBF_21_33;
if(SBF_21_33) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2898701370
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2898701370
  if ( vcast_is_in_driver ) {
    P_21_33_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 33, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2898701370
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2898701370
  vCAST_USER_CODE_TIMER_START();
  return R_21_33;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="129">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetCounterInitVal">
      <line>18936</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="130">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetCounterInitVal">
      <line>18938</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="131">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_33_1;
unsigned short R_21_33;
unsigned char SBF_21_33 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="132">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_ClearChSC">
      <line>18950</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_34_1;
extern unsigned char P_21_34_2;
extern unsigned char SBF_21_34;
if(SBF_21_34) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1438044518
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1438044518
  if ( vcast_is_in_driver ) {
    P_21_34_1 = ftmBase;
    P_21_34_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 34, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1438044518
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1438044518
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="133">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_ClearChSC">
      <line>18950</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="134">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_ClearChSC">
      <line>18958</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="135">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_34_1;
unsigned char P_21_34_2;
unsigned char SBF_21_34 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="136">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnEdgeLevel">
      <line>18972</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_35_1;
extern unsigned char P_21_35_2;
extern unsigned char R_21_35;
extern unsigned char SBF_21_35;
if(SBF_21_35) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3299605208
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3299605208
  if ( vcast_is_in_driver ) {
    P_21_35_1 = ((FTM_Type *)(ftmBase));
    P_21_35_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 35, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3299605208
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3299605208
  vCAST_USER_CODE_TIMER_START();
  return R_21_35;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="137">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnEdgeLevel">
      <line>18972</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="138">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetChnEdgeLevel">
      <line>18981</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="139">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_35_1;
unsigned char P_21_35_2;
unsigned char R_21_35;
unsigned char SBF_21_35 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="140">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnIcrstCmd">
      <line>18997</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_36_1;
extern unsigned char P_21_36_2;
extern _Bool P_21_36_3;
extern unsigned char SBF_21_36;
if(SBF_21_36) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1125738527
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1125738527
  if ( vcast_is_in_driver ) {
    P_21_36_1 = ftmBase;
    P_21_36_2 = channel;
    P_21_36_3 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 36, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1125738527
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1125738527
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="141">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnIcrstCmd">
      <line>18997</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="142">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetChnIcrstCmd">
      <line>19002</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="143">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_36_1;
unsigned char P_21_36_2;
_Bool P_21_36_3;
unsigned char SBF_21_36 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="144">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsChnIcrst">
      <line>19017</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_37_1;
extern unsigned char P_21_37_2;
extern _Bool R_21_37;
extern unsigned char SBF_21_37;
if(SBF_21_37) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3763887131
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3763887131
  if ( vcast_is_in_driver ) {
    P_21_37_1 = ((FTM_Type *)(ftmBase));
    P_21_37_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 37, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3763887131
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3763887131
  vCAST_USER_CODE_TIMER_START();
  return R_21_37;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="145">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsChnIcrst">
      <line>19017</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="146">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_IsChnIcrst">
      <line>19021</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="147">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_37_1;
unsigned char P_21_37_2;
_Bool R_21_37;
unsigned char SBF_21_37 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="148">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnDmaCmd">
      <line>19037</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_38_1;
extern unsigned char P_21_38_2;
extern _Bool P_21_38_3;
extern unsigned char SBF_21_38;
if(SBF_21_38) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3573632298
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3573632298
  if ( vcast_is_in_driver ) {
    P_21_38_1 = ftmBase;
    P_21_38_2 = channel;
    P_21_38_3 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 38, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3573632298
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3573632298
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="149">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnDmaCmd">
      <line>19037</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="150">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetChnDmaCmd">
      <line>19042</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="151">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_38_1;
unsigned char P_21_38_2;
_Bool P_21_38_3;
unsigned char SBF_21_38 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="152">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsChnDma">
      <line>19057</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_39_1;
extern unsigned char P_21_39_2;
extern _Bool R_21_39;
extern unsigned char SBF_21_39;
if(SBF_21_39) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3389920768
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3389920768
  if ( vcast_is_in_driver ) {
    P_21_39_1 = ((FTM_Type *)(ftmBase));
    P_21_39_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 39, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3389920768
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3389920768
  vCAST_USER_CODE_TIMER_START();
  return R_21_39;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="153">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsChnDma">
      <line>19057</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="154">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_IsChnDma">
      <line>19061</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="155">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_39_1;
unsigned char P_21_39_2;
_Bool R_21_39;
unsigned char SBF_21_39 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="156">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetTrigModeControlCmd">
      <line>19077</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_40_1;
extern unsigned char P_21_40_2;
extern _Bool P_21_40_3;
extern unsigned char SBF_21_40;
if(SBF_21_40) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_4289118629
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_4289118629
  if ( vcast_is_in_driver ) {
    P_21_40_1 = ftmBase;
    P_21_40_2 = channel;
    P_21_40_3 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 40, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_4289118629
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_4289118629
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="157">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetTrigModeControlCmd">
      <line>19077</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="158">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetTrigModeControlCmd">
      <line>19082</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="159">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_40_1;
unsigned char P_21_40_2;
_Bool P_21_40_3;
unsigned char SBF_21_40 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="160">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetTriggerControled">
      <line>19097</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_41_1;
extern unsigned char P_21_41_2;
extern _Bool R_21_41;
extern unsigned char SBF_21_41;
if(SBF_21_41) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2988330068
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2988330068
  if ( vcast_is_in_driver ) {
    P_21_41_1 = ((FTM_Type *)(ftmBase));
    P_21_41_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 41, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2988330068
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2988330068
  vCAST_USER_CODE_TIMER_START();
  return R_21_41;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="161">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetTriggerControled">
      <line>19097</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="162">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetTriggerControled">
      <line>19101</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="163">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_41_1;
unsigned char P_21_41_2;
_Bool R_21_41;
unsigned char SBF_21_41 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="164">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChInputState">
      <line>19116</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_42_1;
extern unsigned char P_21_42_2;
extern _Bool R_21_42;
extern unsigned char SBF_21_42;
if(SBF_21_42) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2429387759
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2429387759
  if ( vcast_is_in_driver ) {
    P_21_42_1 = ((FTM_Type *)(ftmBase));
    P_21_42_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 42, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2429387759
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2429387759
  vCAST_USER_CODE_TIMER_START();
  return R_21_42;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="165">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChInputState">
      <line>19116</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="166">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetChInputState">
      <line>19120</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="167">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_42_1;
unsigned char P_21_42_2;
_Bool R_21_42;
unsigned char SBF_21_42 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="168">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChOutputValue">
      <line>19135</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_43_1;
extern unsigned char P_21_43_2;
extern _Bool R_21_43;
extern unsigned char SBF_21_43;
if(SBF_21_43) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2720007619
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2720007619
  if ( vcast_is_in_driver ) {
    P_21_43_1 = ((FTM_Type *)(ftmBase));
    P_21_43_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 43, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2720007619
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2720007619
  vCAST_USER_CODE_TIMER_START();
  return R_21_43;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="169">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChOutputValue">
      <line>19135</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="170">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetChOutputValue">
      <line>19139</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="171">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_43_1;
unsigned char P_21_43_2;
_Bool R_21_43;
unsigned char SBF_21_43 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="172">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnCountVal">
      <line>19153</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_44_1;
extern unsigned char P_21_44_2;
extern unsigned short R_21_44;
extern unsigned char SBF_21_44;
if(SBF_21_44) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1469147386
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1469147386
  if ( vcast_is_in_driver ) {
    P_21_44_1 = ((FTM_Type *)(ftmBase));
    P_21_44_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 44, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1469147386
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1469147386
  vCAST_USER_CODE_TIMER_START();
  return R_21_44;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="173">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnCountVal">
      <line>19153</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="174">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetChnCountVal">
      <line>19157</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="175">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_44_1;
unsigned char P_21_44_2;
unsigned short R_21_44;
unsigned char SBF_21_44 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="176">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnEventStatus">
      <line>19173</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_45_1;
extern unsigned char P_21_45_2;
extern _Bool R_21_45;
extern unsigned char SBF_21_45;
if(SBF_21_45) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_820736779
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_820736779
  if ( vcast_is_in_driver ) {
    P_21_45_1 = ((FTM_Type *)(ftmBase));
    P_21_45_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 45, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_820736779
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_820736779
  vCAST_USER_CODE_TIMER_START();
  return R_21_45;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="177">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnEventStatus">
      <line>19173</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="178">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetChnEventStatus">
      <line>19177</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="179">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_45_1;
unsigned char P_21_45_2;
_Bool R_21_45;
unsigned char SBF_21_45 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="180">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetEventStatus">
      <line>19189</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_46_1;
extern unsigned R_21_46;
extern unsigned char SBF_21_46;
if(SBF_21_46) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_129086471
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_129086471
  if ( vcast_is_in_driver ) {
    P_21_46_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 46, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_129086471
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_129086471
  vCAST_USER_CODE_TIMER_START();
  return R_21_46;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="181">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetEventStatus">
      <line>19189</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="182">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetEventStatus">
      <line>19191</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="183">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_46_1;
unsigned R_21_46;
unsigned char SBF_21_46 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="184">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_ClearChnEventStatus">
      <line>19203</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_47_1;
extern unsigned char P_21_47_2;
extern unsigned char SBF_21_47;
if(SBF_21_47) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3267566510
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3267566510
  if ( vcast_is_in_driver ) {
    P_21_47_1 = ftmBase;
    P_21_47_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 47, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3267566510
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3267566510
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="185">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_ClearChnEventStatus">
      <line>19203</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="186">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_ClearChnEventStatus">
      <line>19211</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="187">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_47_1;
unsigned char P_21_47_2;
unsigned char SBF_21_47 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="188">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnOutputMask">
      <line>19227</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_48_1;
extern unsigned char P_21_48_2;
extern _Bool P_21_48_3;
extern unsigned char SBF_21_48;
if(SBF_21_48) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1249879735
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1249879735
  if ( vcast_is_in_driver ) {
    P_21_48_1 = ftmBase;
    P_21_48_2 = channel;
    P_21_48_3 = mask;
    vCAST_COMMON_STUB_PROC_21( 21, 48, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1249879735
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1249879735
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="189">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnOutputMask">
      <line>19227</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="190">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetChnOutputMask">
      <line>19238</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="191">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_48_1;
unsigned char P_21_48_2;
_Bool P_21_48_3;
unsigned char SBF_21_48 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="192">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnOutputInitStateCmd">
      <line>19254</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_49_1;
extern unsigned char P_21_49_2;
extern _Bool P_21_49_3;
extern unsigned char SBF_21_49;
if(SBF_21_49) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1317688489
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1317688489
  if ( vcast_is_in_driver ) {
    P_21_49_1 = ftmBase;
    P_21_49_2 = channel;
    P_21_49_3 = state;
    vCAST_COMMON_STUB_PROC_21( 21, 49, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1317688489
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1317688489
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="193">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnOutputInitStateCmd">
      <line>19254</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="194">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetChnOutputInitStateCmd">
      <line>19265</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="195">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_49_1;
unsigned char P_21_49_2;
_Bool P_21_49_3;
unsigned char SBF_21_49 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="196">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_DisableFaultInt">
      <line>19275</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_50_1;
extern unsigned char SBF_21_50;
if(SBF_21_50) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1064298221
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1064298221
  if ( vcast_is_in_driver ) {
    P_21_50_1 = ftmBase;
    vCAST_COMMON_STUB_PROC_21( 21, 50, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1064298221
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1064298221
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="197">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_DisableFaultInt">
      <line>19275</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="198">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_DisableFaultInt">
      <line>19277</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="199">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_50_1;
unsigned char SBF_21_50 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="200">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetCaptureTestCmd">
      <line>19291</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_51_1;
extern _Bool P_21_51_2;
extern unsigned char SBF_21_51;
if(SBF_21_51) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1233366424
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1233366424
  if ( vcast_is_in_driver ) {
    P_21_51_1 = ftmBase;
    P_21_51_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 51, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1233366424
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1233366424
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="201">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetCaptureTestCmd">
      <line>19291</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="202">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetCaptureTestCmd">
      <line>19293</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="203">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_51_1;
_Bool P_21_51_2;
unsigned char SBF_21_51 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="204">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsFtmEnable">
      <line>19306</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_52_1;
extern _Bool R_21_52;
extern unsigned char SBF_21_52;
if(SBF_21_52) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1731542521
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1731542521
  if ( vcast_is_in_driver ) {
    P_21_52_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 52, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1731542521
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1731542521
  vCAST_USER_CODE_TIMER_START();
  return R_21_52;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="205">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsFtmEnable">
      <line>19306</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="206">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_IsFtmEnable">
      <line>19308</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="207">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_52_1;
_Bool R_21_52;
unsigned char SBF_21_52 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="208">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetCountReinitSyncCmd">
      <line>19323</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_53_1;
extern _Bool P_21_53_2;
extern unsigned char SBF_21_53;
if(SBF_21_53) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_4156927420
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_4156927420
  if ( vcast_is_in_driver ) {
    P_21_53_1 = ftmBase;
    P_21_53_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 53, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_4156927420
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_4156927420
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="209">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetCountReinitSyncCmd">
      <line>19323</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="210">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetCountReinitSyncCmd">
      <line>19325</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="211">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_53_1;
_Bool P_21_53_2;
unsigned char SBF_21_53 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="212">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsWriteProtectionEnabled">
      <line>19339</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_54_1;
extern _Bool R_21_54;
extern unsigned char SBF_21_54;
if(SBF_21_54) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2990159279
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2990159279
  if ( vcast_is_in_driver ) {
    P_21_54_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 54, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2990159279
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2990159279
  vCAST_USER_CODE_TIMER_START();
  return R_21_54;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="213">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsWriteProtectionEnabled">
      <line>19339</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="214">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_IsWriteProtectionEnabled">
      <line>19341</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="215">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_54_1;
_Bool R_21_54;
unsigned char SBF_21_54 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="216">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsFaultInputEnabled">
      <line>19355</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_55_1;
extern _Bool R_21_55;
extern unsigned char SBF_21_55;
if(SBF_21_55) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1722744871
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1722744871
  if ( vcast_is_in_driver ) {
    P_21_55_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 55, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1722744871
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1722744871
  vCAST_USER_CODE_TIMER_START();
  return R_21_55;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="217">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsFaultInputEnabled">
      <line>19355</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="218">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_IsFaultInputEnabled">
      <line>19357</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="219">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_55_1;
_Bool R_21_55;
unsigned char SBF_21_55 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="220">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsFaultFlagDetected">
      <line>19373</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_56_1;
extern unsigned char P_21_56_2;
extern _Bool R_21_56;
extern unsigned char SBF_21_56;
if(SBF_21_56) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2973359815
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2973359815
  if ( vcast_is_in_driver ) {
    P_21_56_1 = ((FTM_Type *)(ftmBase));
    P_21_56_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 56, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2973359815
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2973359815
  vCAST_USER_CODE_TIMER_START();
  return R_21_56;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="221">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_IsFaultFlagDetected">
      <line>19373</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="222">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_IsFaultFlagDetected">
      <line>19377</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="223">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_56_1;
unsigned char P_21_56_2;
_Bool R_21_56;
unsigned char SBF_21_56 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="224">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_ClearFaultFlagDetected">
      <line>19389</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_57_1;
extern unsigned char P_21_57_2;
extern unsigned char SBF_21_57;
if(SBF_21_57) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_4070457085
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_4070457085
  if ( vcast_is_in_driver ) {
    P_21_57_1 = ftmBase;
    P_21_57_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 57, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_4070457085
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_4070457085
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="225">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_ClearFaultFlagDetected">
      <line>19389</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="226">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_ClearFaultFlagDetected">
      <line>19397</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="227">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_57_1;
unsigned char P_21_57_2;
unsigned char SBF_21_57 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="228">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetDualChnInvertCmd">
      <line>19413</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_58_1;
extern unsigned char P_21_58_2;
extern _Bool P_21_58_3;
extern unsigned char SBF_21_58;
if(SBF_21_58) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3042768023
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3042768023
  if ( vcast_is_in_driver ) {
    P_21_58_1 = ftmBase;
    P_21_58_2 = chnlPairNum;
    P_21_58_3 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 58, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3042768023
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3042768023
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="229">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetDualChnInvertCmd">
      <line>19413</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="230">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetDualChnInvertCmd">
      <line>19424</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="231">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_58_1;
unsigned char P_21_58_2;
_Bool P_21_58_3;
unsigned char SBF_21_58 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="232">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnSoftwareCtrlCmd">
      <line>19440</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_59_1;
extern unsigned char P_21_59_2;
extern _Bool P_21_59_3;
extern unsigned char SBF_21_59;
if(SBF_21_59) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2546567742
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2546567742
  if ( vcast_is_in_driver ) {
    P_21_59_1 = ftmBase;
    P_21_59_2 = channel;
    P_21_59_3 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 59, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2546567742
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2546567742
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="233">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnSoftwareCtrlCmd">
      <line>19440</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="234">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetChnSoftwareCtrlCmd">
      <line>19451</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="235">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_59_1;
unsigned char P_21_59_2;
_Bool P_21_59_3;
unsigned char SBF_21_59 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="236">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnSoftwareCtrlVal">
      <line>19470</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_60_1;
extern unsigned char P_21_60_2;
extern _Bool P_21_60_3;
extern unsigned char SBF_21_60;
if(SBF_21_60) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_793390235
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_793390235
  if ( vcast_is_in_driver ) {
    P_21_60_1 = ftmBase;
    P_21_60_2 = channel;
    P_21_60_3 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 60, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_793390235
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_793390235
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="237">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetChnSoftwareCtrlVal">
      <line>19470</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="238">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetChnSoftwareCtrlVal">
      <line>19481</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="239">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_60_1;
unsigned char P_21_60_2;
_Bool P_21_60_3;
unsigned char SBF_21_60 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="240">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetGlobalLoadCmd">
      <line>19492</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_61_1;
extern unsigned char SBF_21_61;
if(SBF_21_61) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1797013312
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1797013312
  if ( vcast_is_in_driver ) {
    P_21_61_1 = ftmBase;
    vCAST_COMMON_STUB_PROC_21( 21, 61, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1797013312
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1797013312
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="241">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetGlobalLoadCmd">
      <line>19492</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="242">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetGlobalLoadCmd">
      <line>19494</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="243">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_61_1;
unsigned char SBF_21_61 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="244">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetLoadCmd">
      <line>19508</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_62_1;
extern _Bool P_21_62_2;
extern unsigned char SBF_21_62;
if(SBF_21_62) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_4036042479
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_4036042479
  if ( vcast_is_in_driver ) {
    P_21_62_1 = ftmBase;
    P_21_62_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 62, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_4036042479
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_4036042479
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="245">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetLoadCmd">
      <line>19508</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="246">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetLoadCmd">
      <line>19517</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="247">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_62_1;
_Bool P_21_62_2;
unsigned char SBF_21_62 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="248">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetHalfCycleCmd">
      <line>19531</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_63_1;
extern _Bool P_21_63_2;
extern unsigned char SBF_21_63;
if(SBF_21_63) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2290848160
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2290848160
  if ( vcast_is_in_driver ) {
    P_21_63_1 = ftmBase;
    P_21_63_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 63, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2290848160
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2290848160
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="249">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetHalfCycleCmd">
      <line>19531</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="250">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetHalfCycleCmd">
      <line>19540</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="251">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_63_1;
_Bool P_21_63_2;
unsigned char SBF_21_63 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="252">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPwmLoadCmd">
      <line>19554</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_64_1;
extern _Bool P_21_64_2;
extern unsigned char SBF_21_64;
if(SBF_21_64) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3524238007
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3524238007
  if ( vcast_is_in_driver ) {
    P_21_64_1 = ftmBase;
    P_21_64_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 64, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3524238007
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3524238007
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="253">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPwmLoadCmd">
      <line>19554</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="254">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetPwmLoadCmd">
      <line>19563</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="255">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_64_1;
_Bool P_21_64_2;
unsigned char SBF_21_64 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="256">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPwmLoadChnSelCmd">
      <line>19579</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_65_1;
extern unsigned char P_21_65_2;
extern _Bool P_21_65_3;
extern unsigned char SBF_21_65;
if(SBF_21_65) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1429034000
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1429034000
  if ( vcast_is_in_driver ) {
    P_21_65_1 = ftmBase;
    P_21_65_2 = channel;
    P_21_65_3 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 65, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1429034000
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1429034000
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="257">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPwmLoadChnSelCmd">
      <line>19579</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="258">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetPwmLoadChnSelCmd">
      <line>19590</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="259">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_65_1;
unsigned char P_21_65_2;
_Bool P_21_65_3;
unsigned char SBF_21_65 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="260">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetInitTrigOnReloadCmd">
      <line>19605</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_66_1;
extern _Bool P_21_66_2;
extern unsigned char SBF_21_66;
if(SBF_21_66) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_591665755
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_591665755
  if ( vcast_is_in_driver ) {
    P_21_66_1 = ftmBase;
    P_21_66_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 66, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_591665755
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_591665755
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="261">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetInitTrigOnReloadCmd">
      <line>19605</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="262">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetInitTrigOnReloadCmd">
      <line>19607</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="263">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_66_1;
_Bool P_21_66_2;
unsigned char SBF_21_66 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="264">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetGlobalTimeBaseOutputCmd">
      <line>19621</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_67_1;
extern _Bool P_21_67_2;
extern unsigned char SBF_21_67;
if(SBF_21_67) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_4144582878
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_4144582878
  if ( vcast_is_in_driver ) {
    P_21_67_1 = ftmBase;
    P_21_67_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 67, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_4144582878
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_4144582878
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="265">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetGlobalTimeBaseOutputCmd">
      <line>19621</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="266">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetGlobalTimeBaseOutputCmd">
      <line>19623</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="267">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_67_1;
_Bool P_21_67_2;
unsigned char SBF_21_67 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="268">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetGlobalTimeBaseCmd">
      <line>19637</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_68_1;
extern _Bool P_21_68_2;
extern unsigned char SBF_21_68;
if(SBF_21_68) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2098749980
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2098749980
  if ( vcast_is_in_driver ) {
    P_21_68_1 = ftmBase;
    P_21_68_2 = enable;
    vCAST_COMMON_STUB_PROC_21( 21, 68, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2098749980
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2098749980
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="269">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetGlobalTimeBaseCmd">
      <line>19637</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="270">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetGlobalTimeBaseCmd">
      <line>19639</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="271">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_68_1;
_Bool P_21_68_2;
unsigned char SBF_21_68 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="272">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetLoadFreq">
      <line>19651</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_69_1;
extern unsigned char P_21_69_2;
extern unsigned char SBF_21_69;
if(SBF_21_69) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_4244301294
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_4244301294
  if ( vcast_is_in_driver ) {
    P_21_69_1 = ftmBase;
    P_21_69_2 = val;
    vCAST_COMMON_STUB_PROC_21( 21, 69, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_4244301294
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_4244301294
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="273">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetLoadFreq">
      <line>19651</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="274">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetLoadFreq">
      <line>19653</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="275">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_69_1;
unsigned char P_21_69_2;
unsigned char SBF_21_69 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="276">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetExtPairDeadtimeValue">
      <line>19667</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_70_1;
extern unsigned char P_21_70_2;
extern unsigned char P_21_70_3;
extern unsigned char SBF_21_70;
if(SBF_21_70) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2149926859
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2149926859
  if ( vcast_is_in_driver ) {
    P_21_70_1 = ftmBase;
    P_21_70_2 = channelPair;
    P_21_70_3 = value;
    vCAST_COMMON_STUB_PROC_21( 21, 70, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2149926859
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2149926859
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="277">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetExtPairDeadtimeValue">
      <line>19667</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="278">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetExtPairDeadtimeValue">
      <line>19689</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="279">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_70_1;
unsigned char P_21_70_2;
unsigned char P_21_70_3;
unsigned char SBF_21_70 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="280">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPairDeadtimePrescale">
      <line>19706</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_71_1;
extern unsigned char P_21_71_2;
extern ftm_deadtime_ps_t P_21_71_3;
extern unsigned char SBF_21_71;
if(SBF_21_71) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1911897550
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1911897550
  if ( vcast_is_in_driver ) {
    P_21_71_1 = ftmBase;
    P_21_71_2 = channelPair;
    P_21_71_3 = divider;
    vCAST_COMMON_STUB_PROC_21( 21, 71, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1911897550
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1911897550
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="281">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPairDeadtimePrescale">
      <line>19706</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="282">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetPairDeadtimePrescale">
      <line>19727</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="283">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_71_1;
unsigned char P_21_71_2;
ftm_deadtime_ps_t P_21_71_3;
unsigned char SBF_21_71 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="284">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPairDeadtimeCount">
      <line>19745</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_72_1;
extern unsigned char P_21_72_2;
extern unsigned char P_21_72_3;
extern unsigned char SBF_21_72;
if(SBF_21_72) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_3047877397
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_3047877397
  if ( vcast_is_in_driver ) {
    P_21_72_1 = ftmBase;
    P_21_72_2 = channelPair;
    P_21_72_3 = count;
    vCAST_COMMON_STUB_PROC_21( 21, 72, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_3047877397
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_3047877397
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="285">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetPairDeadtimeCount">
      <line>19745</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="286">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetPairDeadtimeCount">
      <line>19767</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="287">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_72_1;
unsigned char P_21_72_2;
unsigned char P_21_72_3;
unsigned char SBF_21_72 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="288">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetMirrorMod">
      <line>19780</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_73_1;
extern unsigned short P_21_73_2;
extern unsigned char SBF_21_73;
if(SBF_21_73) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2750439605
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2750439605
  if ( vcast_is_in_driver ) {
    P_21_73_1 = ftmBase;
    P_21_73_2 = value;
    vCAST_COMMON_STUB_PROC_21( 21, 73, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2750439605
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2750439605
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="289">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetMirrorMod">
      <line>19780</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="290">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetMirrorMod">
      <line>19782</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="291">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_73_1;
unsigned short P_21_73_2;
unsigned char SBF_21_73 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="292">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetMirrorMod">
      <line>19794</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_74_1;
extern unsigned short R_21_74;
extern unsigned char SBF_21_74;
if(SBF_21_74) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2901325366
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2901325366
  if ( vcast_is_in_driver ) {
    P_21_74_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 74, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2901325366
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2901325366
  vCAST_USER_CODE_TIMER_START();
  return R_21_74;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="293">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetMirrorMod">
      <line>19794</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="294">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetMirrorMod">
      <line>19796</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="295">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_74_1;
unsigned short R_21_74;
unsigned char SBF_21_74 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="296">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetModFracVal">
      <line>19808</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_75_1;
extern unsigned char R_21_75;
extern unsigned char SBF_21_75;
if(SBF_21_75) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1121613814
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1121613814
  if ( vcast_is_in_driver ) {
    P_21_75_1 = ((FTM_Type *)(ftmBase));
    vCAST_COMMON_STUB_PROC_21( 21, 75, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1121613814
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1121613814
  vCAST_USER_CODE_TIMER_START();
  return R_21_75;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="297">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetModFracVal">
      <line>19808</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="298">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetModFracVal">
      <line>19810</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="299">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_75_1;
unsigned char R_21_75;
unsigned char SBF_21_75 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="300">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetMirrorChnMatchVal">
      <line>19824</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_76_1;
extern unsigned char P_21_76_2;
extern unsigned short P_21_76_3;
extern unsigned char SBF_21_76;
if(SBF_21_76) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_136979885
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_136979885
  if ( vcast_is_in_driver ) {
    P_21_76_1 = ftmBase;
    P_21_76_2 = channel;
    P_21_76_3 = value;
    vCAST_COMMON_STUB_PROC_21( 21, 76, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_136979885
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_136979885
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="301">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_SetMirrorChnMatchVal">
      <line>19824</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="302">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_SetMirrorChnMatchVal">
      <line>19826</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="303">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_76_1;
unsigned char P_21_76_2;
unsigned short P_21_76_3;
unsigned char SBF_21_76 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="304">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetMirrorChnMatchVal">
      <line>19840</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_77_1;
extern unsigned char P_21_77_2;
extern unsigned short R_21_77;
extern unsigned char SBF_21_77;
if(SBF_21_77) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_2785109457
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_2785109457
  if ( vcast_is_in_driver ) {
    P_21_77_1 = ((FTM_Type *)(ftmBase));
    P_21_77_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 77, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_2785109457
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_2785109457
  vCAST_USER_CODE_TIMER_START();
  return R_21_77;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="305">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetMirrorChnMatchVal">
      <line>19840</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="306">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetMirrorChnMatchVal">
      <line>19842</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="307">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_77_1;
unsigned char P_21_77_2;
unsigned short R_21_77;
unsigned char SBF_21_77 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="308">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnMatchFracVal">
      <line>19856</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern FTM_Type *P_21_78_1;
extern unsigned char P_21_78_2;
extern unsigned char R_21_78;
extern unsigned char SBF_21_78;
if(SBF_21_78) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_4271688046
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_4271688046
  if ( vcast_is_in_driver ) {
    P_21_78_1 = ((FTM_Type *)(ftmBase));
    P_21_78_2 = channel;
    vCAST_COMMON_STUB_PROC_21( 21, 78, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_4271688046
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_4271688046
  vCAST_USER_CODE_TIMER_START();
  return R_21_78;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="309">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="FTM_DRV_GetChnMatchFracVal">
      <line>19856</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="310">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="FTM_DRV_GetChnMatchFracVal">
      <line>19858</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="311">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
FTM_Type *P_21_78_1;
unsigned char P_21_78_2;
unsigned char R_21_78;
unsigned char SBF_21_78 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="312">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_RdMemByAddr">
      <line>23075</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern ISOUDS_ConfType *P_21_79_1;
extern vCAST_boolean P_21_79_2_set;
extern unsigned char *P_21_79_2;
extern unsigned char SBF_21_79;
if(SBF_21_79) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_21_1140801815
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_21_1140801815
  if ( vcast_is_in_driver ) {
    P_21_79_1 = ISOUDSConfPtr;
    P_21_79_2 = dataBuff;
    vCAST_COMMON_STUB_PROC_21( 21, 79, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_21_1140801815
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_21_1140801815
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="313">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_RdMemByAddr">
      <line>23075</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="314">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_RdMemByAddr">
      <line>23185</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="315">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>23188</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
ISOUDS_ConfType *P_21_79_1;
vCAST_boolean P_21_79_2_set = vCAST_false
;
unsigned char *P_21_79_2;
unsigned char SBF_21_79 = 0;

/*vcast_internal_end*/
</text>
  </modification>
</changeset>
