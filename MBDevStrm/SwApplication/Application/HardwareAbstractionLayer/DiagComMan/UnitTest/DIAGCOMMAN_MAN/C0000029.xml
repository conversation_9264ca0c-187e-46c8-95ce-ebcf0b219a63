<?xml version="1.0" encoding="UTF-8"?>
<coverage_data xml_testcase_version="7">
  <unit index="29" coverage_type="STATEMENT_MCDC" max_mcdc_conditions="8" field_width="8">
    <name>ISOUDS_StrtDiagSess</name>
    <configuration>
      <empty_statements_coverable>1</empty_statements_coverable>
      <cover_catch_as_branch>1</cover_catch_as_branch>
      <cover_logical_operators_in_parameters>1</cover_logical_operators_in_parameters>
      <handle_ternary_operator>1</handle_ternary_operator>
      <cover_nested_conditional>1</cover_nested_conditional>
      <coverage_for_included_c_files>1</coverage_for_included_c_files>
      <coverage_for_headers>0</coverage_for_headers>
      <instrument_by_block>0</instrument_by_block>
      <coverage_for_declarations>1</coverage_for_declarations>
      <basis_paths_for_constant_branches>1</basis_paths_for_constant_branches>
      <edg_coverage_available>1</edg_coverage_available>
      <cover_included_c_files>1</cover_included_c_files>
      <cover_headers>0</cover_headers>
      <preprocessed>1</preprocessed>
      <masking_mcdc>0</masking_mcdc>
      <cuda_mode>0</cuda_mode>
      <coverage_for_lambdas>2</coverage_for_lambdas>
      <pack_instr_storage>1</pack_instr_storage>
      <global_buffer_opts>0</global_buffer_opts>
      <coverage_unit_registration>0</coverage_unit_registration>
      <coverage_for_template_instantiations>0</coverage_for_template_instantiations>
    </configuration>
    <attributes>
      <has_empty_statements>0</has_empty_statements>
      <has_catch_statements>0</has_catch_statements>
      <has_logical_operators_in_parameters>0</has_logical_operators_in_parameters>
      <has_ternary_operator>0</has_ternary_operator>
      <has_nested_ternary_operator>0</has_nested_ternary_operator>
      <has_constant_branch>0</has_constant_branch>
      <number_ascii_bytes>772</number_ascii_bytes>
      <probe_point_after_last_include>9826</probe_point_after_last_include>
      <probe_point_beginning_of_file>108</probe_point_beginning_of_file>
      <probe_point_end_of_file>10020</probe_point_end_of_file>
      <file_timestamp>1748231454</file_timestamp>
      <file_checksum>1691934957</file_checksum>
      <translation_unit_checksum>3218293488</translation_unit_checksum>
    </attributes>
    <source_files>
      <source_file index="0" path="c:/users/<USER>/mbvan/mb_assy_scu_sw_devstrm_shamalisunil.kalekar@tessolve_workspace_vc/mbdevstrm/swapplication/application/hardwareabstractionlayer/diagcomman/code/isouds_strtdiagsess.c" crc_checksum="1691934957" sha_checksum="3309cd6340fe83b4a17053fb7f8c07876b9707ee" timestamp="1748231454" lis_file_path="C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\DiagComMan\UnitTest\DIAGCOMMAN_MAN\B0000029.LIS"/>
    </source_files>
    <template_function_prototypes/>
    <subprogram index="1">
      <name>ISOUDS_StrtDiagSess</name>
      <source_file_index>0</source_file_index>
      <parameterized>ISOUDS_StrtDiagSess(ISOUDS_ConfType*,uint8[])</parameterized>
      <mangled>ISOUDS_StrtDiagSess</mangled>
      <source_function_checksum>3403211597</source_function_checksum>
      <translation_unit_function_checksum>1470811722</translation_unit_function_checksum>
      <lines>
        <line number="0" conditions="1" condition_value="1" id="1" stmt_id="0" startline="68" startcol="1" endline="68" endcol="1" tustartline="9872" tustartcol="1" tuendline="9872" tuendcol="1" allowed_position="3"/>
        <line number="1" id="1" stmt_id="2" startline="75" startcol="5" endline="75" endcol="23" tustartline="9879" tustartcol="5" tuendline="9879" tuendcol="23" allowed_position="2"/>
        <line number="2" id="2" stmt_id="4" startline="78" startcol="5" endline="78" endcol="28" tustartline="9882" tustartcol="5" tuendline="9882" tuendcol="28" is_macro="1" allowed_position="2"/>
        <line number="3" conditions="4" id="1" stmt_id="6" startline="81" startcol="5" endline="81" endcol="45" tustartline="9885" tustartcol="5" tuendline="9885" tuendcol="45" is_macro="1" allowed_position="0"/>
        <line number="4" id="3" stmt_id="10" startline="84" startcol="9" endline="84" endcol="44" tustartline="9888" tustartcol="9" tuendline="9888" tuendcol="44" allowed_position="2"/>
        <line number="5" id="4" stmt_id="12" startline="88" startcol="9" endline="88" endcol="44" tustartline="9892" tustartcol="9" tuendline="9892" tuendcol="44" allowed_position="2"/>
        <line number="6" conditions="4" id="2" stmt_id="14" startline="91" startcol="9" endline="91" endcol="38" tustartline="9895" tustartcol="9" tuendline="9895" tuendcol="38" is_macro="1" allowed_position="0"/>
        <line number="7" id="5" stmt_id="18" startline="94" startcol="13" endline="94" endcol="46" tustartline="9898" tustartcol="13" tuendline="9898" tuendcol="46" allowed_position="2"/>
        <line number="8" conditions="4" id="3" stmt_id="20" startline="97" startcol="13" endline="97" endcol="41" tustartline="9901" tustartcol="13" tuendline="9901" tuendcol="41" is_macro="1" allowed_position="0"/>
        <line number="9" id="6" stmt_id="24" startline="100" startcol="17" endline="100" endcol="54" tustartline="9904" tustartcol="17" tuendline="9904" tuendcol="54" is_macro="1" allowed_position="2"/>
        <line number="10" id="7" stmt_id="26" startline="103" startcol="17" endline="103" endcol="59" tustartline="9907" tustartcol="17" tuendline="9907" tuendcol="59" is_macro="1" allowed_position="2"/>
        <line number="11" conditions="4" id="4" stmt_id="30" startline="107" startcol="14" endline="107" endcol="43" tustartline="9911" tustartcol="14" tuendline="9911" tuendcol="43" is_macro="1" allowed_position="0"/>
        <line number="12" id="8" stmt_id="34" startline="110" startcol="13" endline="110" endcol="49" tustartline="9914" tustartcol="13" tuendline="9914" tuendcol="49" allowed_position="2"/>
        <line number="13" conditions="4" id="5" stmt_id="36" startline="113" startcol="13" endline="113" endcol="41" tustartline="9917" tustartcol="13" tuendline="9917" tuendcol="41" is_macro="1" allowed_position="0"/>
        <line number="14" id="9" stmt_id="40" startline="116" startcol="17" endline="116" endcol="54" tustartline="9920" tustartcol="17" tuendline="9920" tuendcol="54" is_macro="1" allowed_position="2"/>
        <line number="15" id="10" stmt_id="42" startline="119" startcol="17" endline="119" endcol="59" tustartline="9923" tustartcol="17" tuendline="9923" tuendcol="59" is_macro="1" allowed_position="2"/>
        <line number="16" conditions="4" id="6" stmt_id="46" startline="123" startcol="14" endline="123" endcol="43" tustartline="9927" tustartcol="14" tuendline="9927" tuendcol="43" is_macro="1" allowed_position="0"/>
        <line number="17" conditions="4" id="7" stmt_id="50" startline="125" startcol="13" endline="125" endcol="46" tustartline="9929" tustartcol="13" tuendline="9929" tuendcol="46" is_macro="1" allowed_position="0"/>
        <line number="18" id="11" stmt_id="54" startline="128" startcol="17" endline="128" endcol="54" tustartline="9932" tustartcol="17" tuendline="9932" tuendcol="54" is_macro="1" allowed_position="2"/>
        <line number="19" id="12" stmt_id="56" startline="131" startcol="17" endline="131" endcol="59" tustartline="9935" tustartcol="17" tuendline="9935" tuendcol="59" is_macro="1" allowed_position="2"/>
        <line number="20" conditions="4" id="8" stmt_id="60" startline="136" startcol="17" endline="136" endcol="50" tustartline="9940" tustartcol="17" tuendline="9940" tuendcol="50" is_macro="1" allowed_position="0"/>
        <line number="21" id="13" stmt_id="64" startline="139" startcol="21" endline="139" endcol="54" tustartline="9943" tustartcol="21" tuendline="9943" tuendcol="54" allowed_position="2"/>
        <line number="22" conditions="4" id="9" stmt_id="66" startline="142" startcol="21" endline="142" endcol="49" tustartline="9946" tustartcol="21" tuendline="9946" tuendcol="49" is_macro="1" allowed_position="0"/>
        <line number="23" id="14" stmt_id="70" startline="145" startcol="25" endline="145" endcol="62" tustartline="9949" tustartcol="25" tuendline="9949" tuendcol="62" is_macro="1" allowed_position="2"/>
        <line number="24" id="15" stmt_id="72" startline="148" startcol="25" endline="148" endcol="67" tustartline="9952" tustartcol="25" tuendline="9952" tuendcol="67" is_macro="1" allowed_position="2"/>
        <line number="25" id="16" stmt_id="76" startline="154" startcol="22" endline="154" endcol="53" tustartline="9958" tustartcol="22" tuendline="9958" tuendcol="53" allowed_position="2"/>
        <line number="26" id="17" stmt_id="80" startline="166" startcol="21" endline="166" endcol="58" tustartline="9970" tustartcol="21" tuendline="9970" tuendcol="58" is_macro="1" allowed_position="2"/>
        <line number="27" id="18" stmt_id="82" startline="169" startcol="21" endline="169" endcol="63" tustartline="9973" tustartcol="21" tuendline="9973" tuendcol="63" is_macro="1" allowed_position="2"/>
        <line number="28" conditions="4" id="10" stmt_id="86" startline="173" startcol="14" endline="173" endcol="43" tustartline="9977" tustartcol="14" tuendline="9977" tuendcol="43" is_macro="1" allowed_position="0"/>
        <line number="29" id="19" stmt_id="90" startline="176" startcol="13" endline="176" endcol="45" tustartline="9980" tustartcol="13" tuendline="9980" tuendcol="45" allowed_position="2"/>
        <line number="30" conditions="4" id="11" stmt_id="92" startline="179" startcol="13" endline="179" endcol="41" tustartline="9983" tustartcol="13" tuendline="9983" tuendcol="41" is_macro="1" allowed_position="0"/>
        <line number="31" id="20" stmt_id="96" startline="182" startcol="17" endline="182" endcol="54" tustartline="9986" tustartcol="17" tuendline="9986" tuendcol="54" is_macro="1" allowed_position="2"/>
        <line number="32" id="21" stmt_id="98" startline="185" startcol="17" endline="185" endcol="59" tustartline="9989" tustartcol="17" tuendline="9989" tuendcol="59" is_macro="1" allowed_position="2"/>
        <line number="33" id="22" stmt_id="102" startline="192" startcol="13" endline="192" endcol="50" tustartline="9996" tustartcol="13" tuendline="9996" tuendcol="50" is_macro="1" allowed_position="2"/>
        <line number="34" id="23" stmt_id="104" startline="195" startcol="13" endline="195" endcol="55" tustartline="9999" tustartcol="13" tuendline="9999" tuendcol="55" is_macro="1" allowed_position="2"/>
        <line number="35" id="24" stmt_id="108" startline="202" startcol="9" endline="202" endcol="46" tustartline="10006" tustartcol="9" tuendline="10006" tuendcol="46" is_macro="1" allowed_position="2"/>
        <line number="36" id="25" stmt_id="110" startline="205" startcol="9" endline="205" endcol="51" tustartline="10009" tustartcol="9" tuendline="10009" tuendcol="51" is_macro="1" allowed_position="2"/>
        <line number="37" conditions="4" id="12" stmt_id="112" startline="209" startcol="5" endline="209" endcol="33" tustartline="10013" tustartcol="5" tuendline="10013" tuendcol="33" is_macro="1" allowed_position="0"/>
        <line number="38" conditions="4" id="13" stmt_id="116" startline="212" startcol="9" endline="212" endcol="32" tustartline="10016" tustartcol="9" tuendline="10016" tuendcol="32" allowed_position="0"/>
        <line number="39" id="26" stmt_id="120" startline="215" startcol="13" endline="215" endcol="45" tustartline="10019" tustartcol="13" tuendline="10019" tuendcol="45" allowed_position="2"/>
        <line number="40" id="27" stmt_id="122" startline="230" startcol="13" endline="230" endcol="46" tustartline="10022" tustartcol="13" tuendline="10022" tuendcol="46" allowed_position="2"/>
        <line number="41" id="28" stmt_id="124" startline="233" startcol="13" endline="233" endcol="50" tustartline="10025" tustartcol="13" tuendline="10025" tuendcol="50" is_macro="1" allowed_position="2"/>
        <line number="42" id="29" stmt_id="128" startline="238" startcol="13" endline="238" endcol="25" tustartline="10030" tustartcol="13" tuendline="10030" tuendcol="25" allowed_position="2"/>
      </lines>
      <startline>9872</startline>
      <startcol>1</startcol>
      <endline>10033</endline>
      <endcol>1</endcol>
      <orig_startline>68</orig_startline>
      <orig_endline>241</orig_endline>
      <firstexec_startline>9879</firstexec_startline>
      <firstexec_startcol>5</firstexec_startcol>
      <firstexec_orig_startline>75</firstexec_orig_startline>
      <open_brace_tu_line>9872</open_brace_tu_line>
      <open_brace_tu_col>1</open_brace_tu_col>
      <open_brace_src_line>68</open_brace_src_line>
      <metrics>
        <complexity>14</complexity>
        <coverage coverage_type="STATEMENT">
          <total>42</total>
        </coverage>
        <coverage coverage_type="MCDC">
          <total>53</total>
        </coverage>
        <coverage coverage_type="MCDC_PAIRS">
          <total>13</total>
        </coverage>
      </metrics>
    </subprogram>
    <subprogram index="2">
      <name>ISOUDS_SwtDfltSess</name>
      <source_file_index>0</source_file_index>
      <parameterized>ISOUDS_SwtDfltSess()boolean</parameterized>
      <mangled>ISOUDS_SwtDfltSess</mangled>
      <source_function_checksum>1891329623</source_function_checksum>
      <translation_unit_function_checksum>8777359</translation_unit_function_checksum>
      <lines>
        <line number="0" conditions="1" condition_value="1" id="2" stmt_id="0" startline="257" startcol="1" endline="257" endcol="1" tustartline="10049" tustartcol="1" tuendline="10049" tuendcol="1" allowed_position="3"/>
        <line number="1" conditions="4" id="14" stmt_id="1" startline="261" startcol="5" endline="261" endcol="38" tustartline="10053" tustartcol="5" tuendline="10053" tuendcol="38" is_macro="1" allowed_position="0"/>
        <line number="2" id="30" stmt_id="5" startline="264" startcol="9" endline="264" endcol="29" tustartline="10056" tustartcol="9" tuendline="10056" tuendcol="29" is_macro="1" allowed_position="2"/>
        <line number="3" id="31" stmt_id="9" startline="270" startcol="9" endline="270" endcol="37" tustartline="10062" tustartcol="9" tuendline="10062" tuendcol="37" is_macro="1" allowed_position="2"/>
        <line number="4" id="32" stmt_id="11" startline="273" startcol="9" endline="273" endcol="29" tustartline="10065" tustartcol="9" tuendline="10065" tuendcol="29" is_macro="1" allowed_position="2"/>
        <line number="5" id="33" stmt_id="13" startline="276" startcol="5" endline="276" endcol="20" tustartline="10068" tustartcol="5" tuendline="10068" tuendcol="20" allowed_position="0"/>
      </lines>
      <startline>10049</startline>
      <startcol>1</startcol>
      <endline>10069</endline>
      <endcol>1</endcol>
      <orig_startline>257</orig_startline>
      <orig_endline>277</orig_endline>
      <firstexec_startline>10053</firstexec_startline>
      <firstexec_startcol>5</firstexec_startcol>
      <firstexec_orig_startline>261</firstexec_orig_startline>
      <open_brace_tu_line>10049</open_brace_tu_line>
      <open_brace_tu_col>1</open_brace_tu_col>
      <open_brace_src_line>257</open_brace_src_line>
      <metrics>
        <complexity>2</complexity>
        <coverage coverage_type="STATEMENT">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC_PAIRS">
          <total>1</total>
        </coverage>
      </metrics>
    </subprogram>
    <subprogram index="3">
      <name>ISOUDS_SwtExtDiagSess</name>
      <source_file_index>0</source_file_index>
      <parameterized>ISOUDS_SwtExtDiagSess()boolean</parameterized>
      <mangled>ISOUDS_SwtExtDiagSess</mangled>
      <source_function_checksum>2924905836</source_function_checksum>
      <translation_unit_function_checksum>480624441</translation_unit_function_checksum>
      <lines>
        <line number="0" conditions="1" condition_value="1" id="3" stmt_id="0" startline="291" startcol="1" endline="291" endcol="1" tustartline="10083" tustartcol="1" tuendline="10083" tuendcol="1" allowed_position="3"/>
        <line number="1" conditions="4" id="15" stmt_id="1" startline="295" startcol="5" endline="295" endcol="38" tustartline="10087" tustartcol="5" tuendline="10087" tuendcol="38" is_macro="1" allowed_position="0"/>
        <line number="2" id="34" stmt_id="5" startline="300" startcol="9" endline="300" endcol="29" tustartline="10092" tustartcol="9" tuendline="10092" tuendcol="29" is_macro="1" allowed_position="2"/>
        <line number="3" id="35" stmt_id="9" startline="305" startcol="9" endline="305" endcol="37" tustartline="10097" tustartcol="9" tuendline="10097" tuendcol="37" is_macro="1" allowed_position="2"/>
        <line number="4" id="36" stmt_id="11" startline="310" startcol="9" endline="310" endcol="29" tustartline="10102" tustartcol="9" tuendline="10102" tuendcol="29" is_macro="1" allowed_position="2"/>
        <line number="5" id="37" stmt_id="13" startline="313" startcol="5" endline="313" endcol="20" tustartline="10105" tustartcol="5" tuendline="10105" tuendcol="20" allowed_position="0"/>
      </lines>
      <startline>10083</startline>
      <startcol>1</startcol>
      <endline>10106</endline>
      <endcol>1</endcol>
      <orig_startline>291</orig_startline>
      <orig_endline>314</orig_endline>
      <firstexec_startline>10087</firstexec_startline>
      <firstexec_startcol>5</firstexec_startcol>
      <firstexec_orig_startline>295</firstexec_orig_startline>
      <open_brace_tu_line>10083</open_brace_tu_line>
      <open_brace_tu_col>1</open_brace_tu_col>
      <open_brace_src_line>291</open_brace_src_line>
      <metrics>
        <complexity>2</complexity>
        <coverage coverage_type="STATEMENT">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC_PAIRS">
          <total>1</total>
        </coverage>
      </metrics>
    </subprogram>
    <subprogram index="4">
      <name>ISOUDS_SwtProgSess</name>
      <source_file_index>0</source_file_index>
      <parameterized>ISOUDS_SwtProgSess()boolean</parameterized>
      <mangled>ISOUDS_SwtProgSess</mangled>
      <source_function_checksum>323386685</source_function_checksum>
      <translation_unit_function_checksum>1583058975</translation_unit_function_checksum>
      <lines>
        <line number="0" conditions="1" condition_value="1" id="4" stmt_id="0" startline="328" startcol="1" endline="328" endcol="1" tustartline="10120" tustartcol="1" tuendline="10120" tuendcol="1" allowed_position="3"/>
        <line number="1" conditions="4" id="16" stmt_id="1" startline="332" startcol="5" endline="332" endcol="38" tustartline="10124" tustartcol="5" tuendline="10124" tuendcol="38" is_macro="1" allowed_position="0"/>
        <line number="2" id="38" stmt_id="7" startline="338" startcol="10" endline="338" endcol="30" tustartline="10130" tustartcol="10" tuendline="10130" tuendcol="30" is_macro="1" allowed_position="2"/>
        <line number="3" id="39" stmt_id="13" startline="352" startcol="10" endline="352" endcol="38" tustartline="10144" tustartcol="10" tuendline="10144" tuendcol="38" is_macro="1" allowed_position="2"/>
        <line number="4" id="40" stmt_id="15" startline="355" startcol="4" endline="355" endcol="24" tustartline="10147" tustartcol="4" tuendline="10147" tuendcol="24" is_macro="1" allowed_position="2"/>
        <line number="5" id="41" stmt_id="17" startline="364" startcol="5" endline="364" endcol="20" tustartline="10156" tustartcol="5" tuendline="10156" tuendcol="20" allowed_position="0"/>
      </lines>
      <startline>10120</startline>
      <startcol>1</startcol>
      <endline>10157</endline>
      <endcol>1</endcol>
      <orig_startline>328</orig_startline>
      <orig_endline>365</orig_endline>
      <firstexec_startline>10124</firstexec_startline>
      <firstexec_startcol>5</firstexec_startcol>
      <firstexec_orig_startline>332</firstexec_orig_startline>
      <open_brace_tu_line>10120</open_brace_tu_line>
      <open_brace_tu_col>1</open_brace_tu_col>
      <open_brace_src_line>328</open_brace_src_line>
      <metrics>
        <complexity>2</complexity>
        <coverage coverage_type="STATEMENT">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC_PAIRS">
          <total>1</total>
        </coverage>
      </metrics>
    </subprogram>
    <subprogram index="5">
      <name>ISOUDS_SwtEolSess</name>
      <source_file_index>0</source_file_index>
      <parameterized>ISOUDS_SwtEolSess()boolean</parameterized>
      <mangled>ISOUDS_SwtEolSess</mangled>
      <source_function_checksum>3382768421</source_function_checksum>
      <translation_unit_function_checksum>2725644930</translation_unit_function_checksum>
      <lines>
        <line number="0" conditions="1" condition_value="1" id="5" stmt_id="0" startline="379" startcol="1" endline="379" endcol="1" tustartline="10171" tustartcol="1" tuendline="10171" tuendcol="1" allowed_position="3"/>
        <line number="1" conditions="4" id="17" stmt_id="1" startline="383" startcol="5" endline="383" endcol="38" tustartline="10175" tustartcol="5" tuendline="10175" tuendcol="38" is_macro="1" allowed_position="0"/>
        <line number="2" id="42" stmt_id="5" startline="388" startcol="9" endline="388" endcol="29" tustartline="10180" tustartcol="9" tuendline="10180" tuendcol="29" is_macro="1" allowed_position="2"/>
        <line number="3" id="43" stmt_id="9" startline="393" startcol="9" endline="393" endcol="37" tustartline="10185" tustartcol="9" tuendline="10185" tuendcol="37" is_macro="1" allowed_position="2"/>
        <line number="4" id="44" stmt_id="11" startline="398" startcol="9" endline="398" endcol="29" tustartline="10190" tustartcol="9" tuendline="10190" tuendcol="29" is_macro="1" allowed_position="2"/>
        <line number="5" id="45" stmt_id="13" startline="401" startcol="5" endline="401" endcol="20" tustartline="10193" tustartcol="5" tuendline="10193" tuendcol="20" allowed_position="0"/>
      </lines>
      <startline>10171</startline>
      <startcol>1</startcol>
      <endline>10194</endline>
      <endcol>1</endcol>
      <orig_startline>379</orig_startline>
      <orig_endline>402</orig_endline>
      <firstexec_startline>10175</firstexec_startline>
      <firstexec_startcol>5</firstexec_startcol>
      <firstexec_orig_startline>383</firstexec_orig_startline>
      <open_brace_tu_line>10171</open_brace_tu_line>
      <open_brace_tu_col>1</open_brace_tu_col>
      <open_brace_src_line>379</open_brace_src_line>
      <metrics>
        <complexity>2</complexity>
        <coverage coverage_type="STATEMENT">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC">
          <total>5</total>
        </coverage>
        <coverage coverage_type="MCDC_PAIRS">
          <total>1</total>
        </coverage>
      </metrics>
    </subprogram>
  </unit>
</coverage_data>
