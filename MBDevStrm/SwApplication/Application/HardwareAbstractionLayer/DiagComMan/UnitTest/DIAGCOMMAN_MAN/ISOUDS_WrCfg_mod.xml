<?xml version="1.0" encoding="UTF-8"?>
<changeset checksum="166547288">
  <modification id="0">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Enable">
      <line>8505</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_1_1;
extern unsigned P_33_1_2;
extern unsigned char SBF_33_1;
if(SBF_33_1) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_1036976405
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_1036976405
  if ( vcast_is_in_driver ) {
    P_33_1_1 = base;
    P_33_1_2 = delay;
    vCAST_COMMON_STUB_PROC_33( 33, 1, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_1036976405
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_1036976405
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="1">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Enable">
      <line>8505</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="2">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Enable">
      <line>8522</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="3">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_1_1;
unsigned P_33_1_2;
unsigned char SBF_33_1 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="4">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Disable">
      <line>8533</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_2_1;
extern unsigned char SBF_33_2;
if(SBF_33_2) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3934361587
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3934361587
  if ( vcast_is_in_driver ) {
    P_33_2_1 = base;
    vCAST_COMMON_STUB_PROC_33( 33, 2, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3934361587
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3934361587
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="5">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Disable">
      <line>8533</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="6">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Disable">
      <line>8535</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="7">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_2_1;
unsigned char SBF_33_2 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="8">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Reset">
      <line>8546</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_3_1;
extern unsigned P_33_3_2;
extern unsigned char SBF_33_3;
if(SBF_33_3) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_1588594490
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_1588594490
  if ( vcast_is_in_driver ) {
    P_33_3_1 = base;
    P_33_3_2 = delay;
    vCAST_COMMON_STUB_PROC_33( 33, 3, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_1588594490
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_1588594490
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="9">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Reset">
      <line>8546</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="10">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Reset">
      <line>8564</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="11">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_3_1;
unsigned P_33_3_2;
unsigned char SBF_33_3 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="12">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StartTimerChannels">
      <line>8583</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_4_1;
extern unsigned P_33_4_2;
extern unsigned char SBF_33_4;
if(SBF_33_4) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_512632996
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_512632996
  if ( vcast_is_in_driver ) {
    P_33_4_1 = base;
    P_33_4_2 = mask;
    vCAST_COMMON_STUB_PROC_33( 33, 4, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_512632996
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_512632996
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="13">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StartTimerChannels">
      <line>8583</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="14">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_StartTimerChannels">
      <line>8585</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="15">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_4_1;
unsigned P_33_4_2;
unsigned char SBF_33_4 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="16">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StopTimerChannels">
      <line>8605</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_5_1;
extern unsigned P_33_5_2;
extern unsigned char SBF_33_5;
if(SBF_33_5) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_2951192847
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_2951192847
  if ( vcast_is_in_driver ) {
    P_33_5_1 = base;
    P_33_5_2 = mask;
    vCAST_COMMON_STUB_PROC_33( 33, 5, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_2951192847
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_2951192847
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="17">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StopTimerChannels">
      <line>8605</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="18">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_StopTimerChannels">
      <line>8607</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="19">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_5_1;
unsigned P_33_5_2;
unsigned char SBF_33_5 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="20">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerPeriodByCount">
      <line>8629</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_6_1;
extern unsigned P_33_6_2;
extern unsigned P_33_6_3;
extern unsigned char SBF_33_6;
if(SBF_33_6) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_937993421
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_937993421
  if ( vcast_is_in_driver ) {
    P_33_6_1 = base;
    P_33_6_2 = channel;
    P_33_6_3 = count;
    vCAST_COMMON_STUB_PROC_33( 33, 6, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_937993421
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_937993421
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="21">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerPeriodByCount">
      <line>8629</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="22">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerPeriodByCount">
      <line>8631</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="23">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_6_1;
unsigned P_33_6_2;
unsigned P_33_6_3;
unsigned char SBF_33_6 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="24">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerPeriodByCount">
      <line>8644</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_7_1;
extern unsigned P_33_7_2;
extern unsigned R_33_7;
extern unsigned char SBF_33_7;
if(SBF_33_7) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_1861595802
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_1861595802
  if ( vcast_is_in_driver ) {
    P_33_7_1 = ((LPIT_Type *)(base));
    P_33_7_2 = channel;
    vCAST_COMMON_STUB_PROC_33( 33, 7, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_1861595802
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_1861595802
  vCAST_USER_CODE_TIMER_START();
  return R_33_7;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="25">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerPeriodByCount">
      <line>8644</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="26">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetTimerPeriodByCount">
      <line>8646</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="27">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_7_1;
unsigned P_33_7_2;
unsigned R_33_7;
unsigned char SBF_33_7 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="28">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetCurrentTimerCount">
      <line>8661</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_8_1;
extern unsigned P_33_8_2;
extern unsigned R_33_8;
extern unsigned char SBF_33_8;
if(SBF_33_8) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_1231179100
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_1231179100
  if ( vcast_is_in_driver ) {
    P_33_8_1 = ((LPIT_Type *)(base));
    P_33_8_2 = channel;
    vCAST_COMMON_STUB_PROC_33( 33, 8, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_1231179100
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_1231179100
  vCAST_USER_CODE_TIMER_START();
  return R_33_8;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="29">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetCurrentTimerCount">
      <line>8661</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="30">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetCurrentTimerCount">
      <line>8663</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="31">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_8_1;
unsigned P_33_8_2;
unsigned R_33_8;
unsigned char SBF_33_8 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="32">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_EnableInterruptTimerChannels">
      <line>8680</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_9_1;
extern unsigned P_33_9_2;
extern unsigned char SBF_33_9;
if(SBF_33_9) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3101418129
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3101418129
  if ( vcast_is_in_driver ) {
    P_33_9_1 = base;
    P_33_9_2 = mask;
    vCAST_COMMON_STUB_PROC_33( 33, 9, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3101418129
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3101418129
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="33">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_EnableInterruptTimerChannels">
      <line>8680</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="34">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_EnableInterruptTimerChannels">
      <line>8682</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="35">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_9_1;
unsigned P_33_9_2;
unsigned char SBF_33_9 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="36">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_DisableInterruptTimerChannels">
      <line>8699</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_10_1;
extern unsigned P_33_10_2;
extern unsigned char SBF_33_10;
if(SBF_33_10) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3927551259
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3927551259
  if ( vcast_is_in_driver ) {
    P_33_10_1 = base;
    P_33_10_2 = mask;
    vCAST_COMMON_STUB_PROC_33( 33, 10, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3927551259
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3927551259
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="37">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_DisableInterruptTimerChannels">
      <line>8699</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="38">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_DisableInterruptTimerChannels">
      <line>8701</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="39">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_10_1;
unsigned P_33_10_2;
unsigned char SBF_33_10 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="40">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8719</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_11_1;
extern unsigned P_33_11_2;
extern unsigned R_33_11;
extern unsigned char SBF_33_11;
if(SBF_33_11) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_991102759
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_991102759
  if ( vcast_is_in_driver ) {
    P_33_11_1 = ((LPIT_Type *)(base));
    P_33_11_2 = mask;
    vCAST_COMMON_STUB_PROC_33( 33, 11, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_991102759
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_991102759
  vCAST_USER_CODE_TIMER_START();
  return R_33_11;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="41">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8719</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="42">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8721</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="43">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_11_1;
unsigned P_33_11_2;
unsigned R_33_11;
unsigned char SBF_33_11 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="44">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8738</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_12_1;
extern unsigned P_33_12_2;
extern unsigned char SBF_33_12;
if(SBF_33_12) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_1087092936
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_1087092936
  if ( vcast_is_in_driver ) {
    P_33_12_1 = base;
    P_33_12_2 = mask;
    vCAST_COMMON_STUB_PROC_33( 33, 12, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_1087092936
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_1087092936
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="45">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8738</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="46">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8745</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="47">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_12_1;
unsigned P_33_12_2;
unsigned char SBF_33_12 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="48">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelModeCmd">
      <line>8760</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_13_1;
extern unsigned P_33_13_2;
extern lpit_timer_modes_t P_33_13_3;
extern unsigned char SBF_33_13;
if(SBF_33_13) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3324386835
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3324386835
  if ( vcast_is_in_driver ) {
    P_33_13_1 = base;
    P_33_13_2 = channel;
    P_33_13_3 = mode;
    vCAST_COMMON_STUB_PROC_33( 33, 13, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3324386835
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3324386835
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="49">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelModeCmd">
      <line>8760</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="50">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerChannelModeCmd">
      <line>8763</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="51">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_13_1;
unsigned P_33_13_2;
lpit_timer_modes_t P_33_13_3;
unsigned char SBF_33_13 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="52">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerChannelModeCmd">
      <line>8776</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_14_1;
extern unsigned P_33_14_2;
extern lpit_timer_modes_t R_33_14;
extern unsigned char SBF_33_14;
if(SBF_33_14) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_863029074
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_863029074
  if ( vcast_is_in_driver ) {
    P_33_14_1 = ((LPIT_Type *)(base));
    P_33_14_2 = channel;
    vCAST_COMMON_STUB_PROC_33( 33, 14, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_863029074
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_863029074
  vCAST_USER_CODE_TIMER_START();
  return R_33_14;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="53">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerChannelModeCmd">
      <line>8776</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="54">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetTimerChannelModeCmd">
      <line>8801</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="55">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_14_1;
unsigned P_33_14_2;
lpit_timer_modes_t R_33_14;
unsigned char SBF_33_14 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="56">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSelectCmd">
      <line>8817</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_15_1;
extern unsigned P_33_15_2;
extern unsigned P_33_15_3;
extern unsigned char SBF_33_15;
if(SBF_33_15) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3396282018
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3396282018
  if ( vcast_is_in_driver ) {
    P_33_15_1 = base;
    P_33_15_2 = channel;
    P_33_15_3 = triggerChannelSelect;
    vCAST_COMMON_STUB_PROC_33( 33, 15, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3396282018
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3396282018
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="57">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSelectCmd">
      <line>8817</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="58">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTriggerSelectCmd">
      <line>8820</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="59">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_15_1;
unsigned P_33_15_2;
unsigned P_33_15_3;
unsigned char SBF_33_15 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="60">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSourceCmd">
      <line>8834</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_16_1;
extern unsigned P_33_16_2;
extern lpit_trigger_source_t P_33_16_3;
extern unsigned char SBF_33_16;
if(SBF_33_16) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_1604313518
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_1604313518
  if ( vcast_is_in_driver ) {
    P_33_16_1 = base;
    P_33_16_2 = channel;
    P_33_16_3 = triggerSource;
    vCAST_COMMON_STUB_PROC_33( 33, 16, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_1604313518
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_1604313518
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="61">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSourceCmd">
      <line>8834</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="62">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTriggerSourceCmd">
      <line>8837</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="63">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_16_1;
unsigned P_33_16_2;
lpit_trigger_source_t P_33_16_3;
unsigned char SBF_33_16 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="64">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetReloadOnTriggerCmd">
      <line>8853</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_17_1;
extern unsigned P_33_17_2;
extern _Bool P_33_17_3;
extern unsigned char SBF_33_17;
if(SBF_33_17) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3946980553
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3946980553
  if ( vcast_is_in_driver ) {
    P_33_17_1 = base;
    P_33_17_2 = channel;
    P_33_17_3 = isReloadOnTrigger;
    vCAST_COMMON_STUB_PROC_33( 33, 17, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3946980553
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3946980553
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="65">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetReloadOnTriggerCmd">
      <line>8853</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="66">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetReloadOnTriggerCmd">
      <line>8856</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="67">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_17_1;
unsigned P_33_17_2;
_Bool P_33_17_3;
unsigned char SBF_33_17 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="68">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStopOnInterruptCmd">
      <line>8872</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_18_1;
extern unsigned P_33_18_2;
extern _Bool P_33_18_3;
extern unsigned char SBF_33_18;
if(SBF_33_18) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_786862186
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_786862186
  if ( vcast_is_in_driver ) {
    P_33_18_1 = base;
    P_33_18_2 = channel;
    P_33_18_3 = isStopOnInterrupt;
    vCAST_COMMON_STUB_PROC_33( 33, 18, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_786862186
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_786862186
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="69">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStopOnInterruptCmd">
      <line>8872</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="70">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetStopOnInterruptCmd">
      <line>8875</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="71">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_18_1;
unsigned P_33_18_2;
_Bool P_33_18_3;
unsigned char SBF_33_18 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="72">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStartOnTriggerCmd">
      <line>8892</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_19_1;
extern unsigned P_33_19_2;
extern _Bool P_33_19_3;
extern unsigned char SBF_33_19;
if(SBF_33_19) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_971397961
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_971397961
  if ( vcast_is_in_driver ) {
    P_33_19_1 = base;
    P_33_19_2 = channel;
    P_33_19_3 = isStartOnTrigger;
    vCAST_COMMON_STUB_PROC_33( 33, 19, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_971397961
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_971397961
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="73">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStartOnTriggerCmd">
      <line>8892</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="74">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetStartOnTriggerCmd">
      <line>8895</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="75">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_19_1;
unsigned P_33_19_2;
_Bool P_33_19_3;
unsigned char SBF_33_19 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="76">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelChainCmd">
      <line>8911</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_20_1;
extern unsigned P_33_20_2;
extern _Bool P_33_20_3;
extern unsigned char SBF_33_20;
if(SBF_33_20) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3908885058
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3908885058
  if ( vcast_is_in_driver ) {
    P_33_20_1 = base;
    P_33_20_2 = channel;
    P_33_20_3 = isChannelChained;
    vCAST_COMMON_STUB_PROC_33( 33, 20, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3908885058
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3908885058
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="77">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelChainCmd">
      <line>8911</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="78">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerChannelChainCmd">
      <line>8914</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="79">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_20_1;
unsigned P_33_20_2;
_Bool P_33_20_3;
unsigned char SBF_33_20 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="80">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDebugCmd">
      <line>8931</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_21_1;
extern _Bool P_33_21_2;
extern unsigned char SBF_33_21;
if(SBF_33_21) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3232929205
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3232929205
  if ( vcast_is_in_driver ) {
    P_33_21_1 = base;
    P_33_21_2 = isRunInDebug;
    vCAST_COMMON_STUB_PROC_33( 33, 21, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3232929205
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3232929205
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="81">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDebugCmd">
      <line>8931</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="82">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerRunInDebugCmd">
      <line>8934</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="83">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_21_1;
_Bool P_33_21_2;
unsigned char SBF_33_21 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="84">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDozeCmd">
      <line>8950</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_33_22_1;
extern _Bool P_33_22_2;
extern unsigned char SBF_33_22;
if(SBF_33_22) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_33_3944652595
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_33_3944652595
  if ( vcast_is_in_driver ) {
    P_33_22_1 = base;
    P_33_22_2 = isRunInDoze;
    vCAST_COMMON_STUB_PROC_33( 33, 22, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_33_3944652595
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_33_3944652595
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="85">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDozeCmd">
      <line>8950</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="86">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerRunInDozeCmd">
      <line>8953</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="87">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>9758</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_33_22_1;
_Bool P_33_22_2;
unsigned char SBF_33_22 = 0;

/*vcast_internal_end*/
</text>
  </modification>
</changeset>
