<?xml version="1.0" encoding="UTF-8"?>
<changeset checksum="74850238">
  <modification id="0">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Enable">
      <line>8504</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_1_1;
extern unsigned P_17_1_2;
extern unsigned char SBF_17_1;
if(SBF_17_1) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1036976405
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1036976405
  if ( vcast_is_in_driver ) {
    P_17_1_1 = base;
    P_17_1_2 = delay;
    vCAST_COMMON_STUB_PROC_17( 17, 1, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1036976405
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1036976405
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="1">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Enable">
      <line>8504</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="2">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Enable">
      <line>8521</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="3">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_1_1;
unsigned P_17_1_2;
unsigned char SBF_17_1 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="4">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Disable">
      <line>8532</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_2_1;
extern unsigned char SBF_17_2;
if(SBF_17_2) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3934361587
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3934361587
  if ( vcast_is_in_driver ) {
    P_17_2_1 = base;
    vCAST_COMMON_STUB_PROC_17( 17, 2, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3934361587
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3934361587
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="5">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Disable">
      <line>8532</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="6">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Disable">
      <line>8534</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="7">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_2_1;
unsigned char SBF_17_2 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="8">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Reset">
      <line>8545</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_3_1;
extern unsigned P_17_3_2;
extern unsigned char SBF_17_3;
if(SBF_17_3) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1588594490
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1588594490
  if ( vcast_is_in_driver ) {
    P_17_3_1 = base;
    P_17_3_2 = delay;
    vCAST_COMMON_STUB_PROC_17( 17, 3, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1588594490
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1588594490
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="9">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_Reset">
      <line>8545</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="10">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_Reset">
      <line>8563</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="11">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_3_1;
unsigned P_17_3_2;
unsigned char SBF_17_3 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="12">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StartTimerChannels">
      <line>8582</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_4_1;
extern unsigned P_17_4_2;
extern unsigned char SBF_17_4;
if(SBF_17_4) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_512632996
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_512632996
  if ( vcast_is_in_driver ) {
    P_17_4_1 = base;
    P_17_4_2 = mask;
    vCAST_COMMON_STUB_PROC_17( 17, 4, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_512632996
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_512632996
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="13">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StartTimerChannels">
      <line>8582</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="14">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_StartTimerChannels">
      <line>8584</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="15">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_4_1;
unsigned P_17_4_2;
unsigned char SBF_17_4 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="16">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StopTimerChannels">
      <line>8604</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_5_1;
extern unsigned P_17_5_2;
extern unsigned char SBF_17_5;
if(SBF_17_5) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_2951192847
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_2951192847
  if ( vcast_is_in_driver ) {
    P_17_5_1 = base;
    P_17_5_2 = mask;
    vCAST_COMMON_STUB_PROC_17( 17, 5, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_2951192847
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_2951192847
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="17">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_StopTimerChannels">
      <line>8604</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="18">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_StopTimerChannels">
      <line>8606</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="19">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_5_1;
unsigned P_17_5_2;
unsigned char SBF_17_5 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="20">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerPeriodByCount">
      <line>8628</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_6_1;
extern unsigned P_17_6_2;
extern unsigned P_17_6_3;
extern unsigned char SBF_17_6;
if(SBF_17_6) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_937993421
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_937993421
  if ( vcast_is_in_driver ) {
    P_17_6_1 = base;
    P_17_6_2 = channel;
    P_17_6_3 = count;
    vCAST_COMMON_STUB_PROC_17( 17, 6, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_937993421
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_937993421
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="21">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerPeriodByCount">
      <line>8628</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="22">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerPeriodByCount">
      <line>8630</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="23">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_6_1;
unsigned P_17_6_2;
unsigned P_17_6_3;
unsigned char SBF_17_6 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="24">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerPeriodByCount">
      <line>8643</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_7_1;
extern unsigned P_17_7_2;
extern unsigned R_17_7;
extern unsigned char SBF_17_7;
if(SBF_17_7) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1861595802
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1861595802
  if ( vcast_is_in_driver ) {
    P_17_7_1 = ((LPIT_Type *)(base));
    P_17_7_2 = channel;
    vCAST_COMMON_STUB_PROC_17( 17, 7, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1861595802
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1861595802
  vCAST_USER_CODE_TIMER_START();
  return R_17_7;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="25">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerPeriodByCount">
      <line>8643</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="26">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetTimerPeriodByCount">
      <line>8645</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="27">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_7_1;
unsigned P_17_7_2;
unsigned R_17_7;
unsigned char SBF_17_7 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="28">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetCurrentTimerCount">
      <line>8660</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_8_1;
extern unsigned P_17_8_2;
extern unsigned R_17_8;
extern unsigned char SBF_17_8;
if(SBF_17_8) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1231179100
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1231179100
  if ( vcast_is_in_driver ) {
    P_17_8_1 = ((LPIT_Type *)(base));
    P_17_8_2 = channel;
    vCAST_COMMON_STUB_PROC_17( 17, 8, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1231179100
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1231179100
  vCAST_USER_CODE_TIMER_START();
  return R_17_8;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="29">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetCurrentTimerCount">
      <line>8660</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="30">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetCurrentTimerCount">
      <line>8662</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="31">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_8_1;
unsigned P_17_8_2;
unsigned R_17_8;
unsigned char SBF_17_8 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="32">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_EnableInterruptTimerChannels">
      <line>8679</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_9_1;
extern unsigned P_17_9_2;
extern unsigned char SBF_17_9;
if(SBF_17_9) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3101418129
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3101418129
  if ( vcast_is_in_driver ) {
    P_17_9_1 = base;
    P_17_9_2 = mask;
    vCAST_COMMON_STUB_PROC_17( 17, 9, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3101418129
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3101418129
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="33">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_EnableInterruptTimerChannels">
      <line>8679</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="34">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_EnableInterruptTimerChannels">
      <line>8681</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="35">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_9_1;
unsigned P_17_9_2;
unsigned char SBF_17_9 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="36">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_DisableInterruptTimerChannels">
      <line>8698</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_10_1;
extern unsigned P_17_10_2;
extern unsigned char SBF_17_10;
if(SBF_17_10) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3927551259
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3927551259
  if ( vcast_is_in_driver ) {
    P_17_10_1 = base;
    P_17_10_2 = mask;
    vCAST_COMMON_STUB_PROC_17( 17, 10, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3927551259
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3927551259
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="37">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_DisableInterruptTimerChannels">
      <line>8698</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="38">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_DisableInterruptTimerChannels">
      <line>8700</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="39">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_10_1;
unsigned P_17_10_2;
unsigned char SBF_17_10 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="40">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8718</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_11_1;
extern unsigned P_17_11_2;
extern unsigned R_17_11;
extern unsigned char SBF_17_11;
if(SBF_17_11) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_991102759
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_991102759
  if ( vcast_is_in_driver ) {
    P_17_11_1 = ((LPIT_Type *)(base));
    P_17_11_2 = mask;
    vCAST_COMMON_STUB_PROC_17( 17, 11, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_991102759
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_991102759
  vCAST_USER_CODE_TIMER_START();
  return R_17_11;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="41">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8718</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="42">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetInterruptFlagTimerChannels">
      <line>8720</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="43">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_11_1;
unsigned P_17_11_2;
unsigned R_17_11;
unsigned char SBF_17_11 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="44">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8737</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_12_1;
extern unsigned P_17_12_2;
extern unsigned char SBF_17_12;
if(SBF_17_12) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1087092936
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1087092936
  if ( vcast_is_in_driver ) {
    P_17_12_1 = base;
    P_17_12_2 = mask;
    vCAST_COMMON_STUB_PROC_17( 17, 12, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1087092936
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1087092936
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="45">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8737</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="46">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_ClearInterruptFlagTimerChannels">
      <line>8744</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="47">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_12_1;
unsigned P_17_12_2;
unsigned char SBF_17_12 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="48">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelModeCmd">
      <line>8759</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_13_1;
extern unsigned P_17_13_2;
extern lpit_timer_modes_t P_17_13_3;
extern unsigned char SBF_17_13;
if(SBF_17_13) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3324386835
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3324386835
  if ( vcast_is_in_driver ) {
    P_17_13_1 = base;
    P_17_13_2 = channel;
    P_17_13_3 = mode;
    vCAST_COMMON_STUB_PROC_17( 17, 13, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3324386835
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3324386835
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="49">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelModeCmd">
      <line>8759</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="50">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerChannelModeCmd">
      <line>8762</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="51">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_13_1;
unsigned P_17_13_2;
lpit_timer_modes_t P_17_13_3;
unsigned char SBF_17_13 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="52">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerChannelModeCmd">
      <line>8775</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_14_1;
extern unsigned P_17_14_2;
extern lpit_timer_modes_t R_17_14;
extern unsigned char SBF_17_14;
if(SBF_17_14) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_863029074
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_863029074
  if ( vcast_is_in_driver ) {
    P_17_14_1 = ((LPIT_Type *)(base));
    P_17_14_2 = channel;
    vCAST_COMMON_STUB_PROC_17( 17, 14, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_863029074
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_863029074
  vCAST_USER_CODE_TIMER_START();
  return R_17_14;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="53">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_GetTimerChannelModeCmd">
      <line>8775</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="54">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_GetTimerChannelModeCmd">
      <line>8800</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="55">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_14_1;
unsigned P_17_14_2;
lpit_timer_modes_t R_17_14;
unsigned char SBF_17_14 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="56">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSelectCmd">
      <line>8816</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_15_1;
extern unsigned P_17_15_2;
extern unsigned P_17_15_3;
extern unsigned char SBF_17_15;
if(SBF_17_15) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3396282018
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3396282018
  if ( vcast_is_in_driver ) {
    P_17_15_1 = base;
    P_17_15_2 = channel;
    P_17_15_3 = triggerChannelSelect;
    vCAST_COMMON_STUB_PROC_17( 17, 15, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3396282018
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3396282018
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="57">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSelectCmd">
      <line>8816</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="58">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTriggerSelectCmd">
      <line>8819</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="59">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_15_1;
unsigned P_17_15_2;
unsigned P_17_15_3;
unsigned char SBF_17_15 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="60">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSourceCmd">
      <line>8833</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_16_1;
extern unsigned P_17_16_2;
extern lpit_trigger_source_t P_17_16_3;
extern unsigned char SBF_17_16;
if(SBF_17_16) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1604313518
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1604313518
  if ( vcast_is_in_driver ) {
    P_17_16_1 = base;
    P_17_16_2 = channel;
    P_17_16_3 = triggerSource;
    vCAST_COMMON_STUB_PROC_17( 17, 16, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1604313518
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1604313518
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="61">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTriggerSourceCmd">
      <line>8833</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="62">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTriggerSourceCmd">
      <line>8836</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="63">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_16_1;
unsigned P_17_16_2;
lpit_trigger_source_t P_17_16_3;
unsigned char SBF_17_16 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="64">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetReloadOnTriggerCmd">
      <line>8852</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_17_1;
extern unsigned P_17_17_2;
extern _Bool P_17_17_3;
extern unsigned char SBF_17_17;
if(SBF_17_17) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3946980553
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3946980553
  if ( vcast_is_in_driver ) {
    P_17_17_1 = base;
    P_17_17_2 = channel;
    P_17_17_3 = isReloadOnTrigger;
    vCAST_COMMON_STUB_PROC_17( 17, 17, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3946980553
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3946980553
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="65">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetReloadOnTriggerCmd">
      <line>8852</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="66">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetReloadOnTriggerCmd">
      <line>8855</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="67">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_17_1;
unsigned P_17_17_2;
_Bool P_17_17_3;
unsigned char SBF_17_17 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="68">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStopOnInterruptCmd">
      <line>8871</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_18_1;
extern unsigned P_17_18_2;
extern _Bool P_17_18_3;
extern unsigned char SBF_17_18;
if(SBF_17_18) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_786862186
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_786862186
  if ( vcast_is_in_driver ) {
    P_17_18_1 = base;
    P_17_18_2 = channel;
    P_17_18_3 = isStopOnInterrupt;
    vCAST_COMMON_STUB_PROC_17( 17, 18, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_786862186
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_786862186
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="69">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStopOnInterruptCmd">
      <line>8871</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="70">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetStopOnInterruptCmd">
      <line>8874</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="71">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_18_1;
unsigned P_17_18_2;
_Bool P_17_18_3;
unsigned char SBF_17_18 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="72">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStartOnTriggerCmd">
      <line>8891</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_19_1;
extern unsigned P_17_19_2;
extern _Bool P_17_19_3;
extern unsigned char SBF_17_19;
if(SBF_17_19) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_971397961
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_971397961
  if ( vcast_is_in_driver ) {
    P_17_19_1 = base;
    P_17_19_2 = channel;
    P_17_19_3 = isStartOnTrigger;
    vCAST_COMMON_STUB_PROC_17( 17, 19, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_971397961
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_971397961
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="73">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetStartOnTriggerCmd">
      <line>8891</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="74">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetStartOnTriggerCmd">
      <line>8894</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="75">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_19_1;
unsigned P_17_19_2;
_Bool P_17_19_3;
unsigned char SBF_17_19 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="76">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelChainCmd">
      <line>8910</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_20_1;
extern unsigned P_17_20_2;
extern _Bool P_17_20_3;
extern unsigned char SBF_17_20;
if(SBF_17_20) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3908885058
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3908885058
  if ( vcast_is_in_driver ) {
    P_17_20_1 = base;
    P_17_20_2 = channel;
    P_17_20_3 = isChannelChained;
    vCAST_COMMON_STUB_PROC_17( 17, 20, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3908885058
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3908885058
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="77">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerChannelChainCmd">
      <line>8910</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="78">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerChannelChainCmd">
      <line>8913</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="79">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_20_1;
unsigned P_17_20_2;
_Bool P_17_20_3;
unsigned char SBF_17_20 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="80">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDebugCmd">
      <line>8930</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_21_1;
extern _Bool P_17_21_2;
extern unsigned char SBF_17_21;
if(SBF_17_21) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3232929205
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3232929205
  if ( vcast_is_in_driver ) {
    P_17_21_1 = base;
    P_17_21_2 = isRunInDebug;
    vCAST_COMMON_STUB_PROC_17( 17, 21, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3232929205
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3232929205
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="81">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDebugCmd">
      <line>8930</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="82">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerRunInDebugCmd">
      <line>8933</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="83">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_21_1;
_Bool P_17_21_2;
unsigned char SBF_17_21 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="84">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDozeCmd">
      <line>8949</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern LPIT_Type *P_17_22_1;
extern _Bool P_17_22_2;
extern unsigned char SBF_17_22;
if(SBF_17_22) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3944652595
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3944652595
  if ( vcast_is_in_driver ) {
    P_17_22_1 = base;
    P_17_22_2 = isRunInDoze;
    vCAST_COMMON_STUB_PROC_17( 17, 22, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3944652595
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3944652595
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="85">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="LPIT_SetTimerRunInDozeCmd">
      <line>8949</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="86">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="LPIT_SetTimerRunInDozeCmd">
      <line>8952</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="87">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
LPIT_Type *P_17_22_1;
_Bool P_17_22_2;
unsigned char SBF_17_22 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="88">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_LinkCntrlInit">
      <line>9786</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern unsigned char SBF_17_23;
if(SBF_17_23) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_132706151
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_132706151
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_17( 17, 23, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_132706151
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_132706151
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="89">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_LinkCntrlInit">
      <line>9786</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="90">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_LinkCntrlInit">
      <line>9798</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="91">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
unsigned char SBF_17_23 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="92">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_LinkCntrl">
      <line>9812</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern ISOUDS_ConfType *P_17_24_1;
extern vCAST_boolean P_17_24_2_set;
extern unsigned char *P_17_24_2;
extern unsigned char SBF_17_24;
if(SBF_17_24) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_2568210352
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_2568210352
  if ( vcast_is_in_driver ) {
    P_17_24_1 = ISOUDSConfPtr;
    P_17_24_2 = dataBuff;
    vCAST_COMMON_STUB_PROC_17( 17, 24, 3, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_2568210352
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_2568210352
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="93">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_LinkCntrl">
      <line>9812</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="94">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_LinkCntrl">
      <line>9971</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="95">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
ISOUDS_ConfType *P_17_24_1;
vCAST_boolean P_17_24_2_set = vCAST_false
;
unsigned char *P_17_24_2;
unsigned char SBF_17_24 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="96">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_GetBaudrateSt">
      <line>9984</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool R_17_25;
extern unsigned char SBF_17_25;
if(SBF_17_25) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_589260755
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_589260755
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_17( 17, 25, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_589260755
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_589260755
  vCAST_USER_CODE_TIMER_START();
  return R_17_25;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="97">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_GetBaudrateSt">
      <line>9984</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="98">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_GetBaudrateSt">
      <line>9986</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="99">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool R_17_25;
unsigned char SBF_17_25 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="100">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_SetBaudrateSt">
      <line>9998</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool P_17_26_1;
extern unsigned char SBF_17_26;
if(SBF_17_26) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1470213023
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1470213023
  if ( vcast_is_in_driver ) {
    P_17_26_1 = value;
    vCAST_COMMON_STUB_PROC_17( 17, 26, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1470213023
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1470213023
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="101">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_SetBaudrateSt">
      <line>9998</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="102">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_SetBaudrateSt">
      <line>10007</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="103">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool P_17_26_1;
unsigned char SBF_17_26 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="104">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_GetBaudrateReq">
      <line>10020</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool R_17_27;
extern unsigned char SBF_17_27;
if(SBF_17_27) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3101850377
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3101850377
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_17( 17, 27, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3101850377
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3101850377
  vCAST_USER_CODE_TIMER_START();
  return R_17_27;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="105">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_GetBaudrateReq">
      <line>10020</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="106">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_GetBaudrateReq">
      <line>10022</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="107">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool R_17_27;
unsigned char SBF_17_27 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="108">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_SetBaudrateReq">
      <line>10035</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern _Bool P_17_28_1;
extern unsigned char SBF_17_28;
if(SBF_17_28) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_3355232382
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_3355232382
  if ( vcast_is_in_driver ) {
    P_17_28_1 = value;
    vCAST_COMMON_STUB_PROC_17( 17, 28, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_3355232382
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_3355232382
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="109">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_SetBaudrateReq">
      <line>10035</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="110">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_SetBaudrateReq">
      <line>10044</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="111">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
_Bool P_17_28_1;
unsigned char SBF_17_28 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="112">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_ChangeBaudrate">
      <line>10056</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern unsigned char SBF_17_29;
if(SBF_17_29) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1827264681
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1827264681
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_17( 17, 29, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1827264681
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1827264681
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="113">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_ChangeBaudrate">
      <line>10056</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="114">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_ChangeBaudrate">
      <line>10092</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="115">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
unsigned char SBF_17_29 = 0;

/*vcast_internal_end*/
</text>
  </modification>
  <modification id="116">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_BaudrateCheck">
      <line>10110</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
extern unsigned char P_17_30_1;
extern unsigned char P_17_30_2;
extern unsigned P_17_30_3;
extern unsigned char R_17_30;
extern unsigned char SBF_17_30;
if(SBF_17_30) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_17_1506411301
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_17_1506411301
  if ( vcast_is_in_driver ) {
    P_17_30_1 = link_cntrltype;
    P_17_30_2 = baudrateID;
    P_17_30_3 = baud_rate;
    vCAST_COMMON_STUB_PROC_17( 17, 30, 4, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_17_1506411301
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_17_1506411301
  vCAST_USER_CODE_TIMER_START();
  return R_17_30;
}
/*vcast_internal_end*/
</text>
  </modification>
  <modification id="117">
    <kind>ADD_TEXT_AFTER</kind>
    <pos description="FUNCTION_BODY_OPEN" function="ISOUDS_BaudrateCheck">
      <line>10110</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/{/*vcast_internal_end*/
</text>
  </modification>
  <modification id="118">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="FUNCTION_BODY_CLOSE" function="ISOUDS_BaudrateCheck">
      <line>10242</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/}/*vcast_internal_end*/
</text>
  </modification>
  <modification id="119">
    <kind>ADD_TEXT_BEFORE</kind>
    <pos description="UNIT_APPENDIX_START">
      <line>10245</line>
      <col>1</col>
    </pos>
    <text> /*vcast_internal_start*/
unsigned char P_17_30_1;
unsigned char P_17_30_2;
unsigned P_17_30_3;
unsigned char R_17_30;
unsigned char SBF_17_30 = 0;

/*vcast_internal_end*/
</text>
  </modification>
</changeset>
