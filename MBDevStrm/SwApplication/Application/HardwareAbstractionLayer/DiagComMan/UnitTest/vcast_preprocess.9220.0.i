#line 1 "vcast_preprocess.9220.0.c"
#line 1 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"





#pragma language=extended
#line 13 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
__intrinsic __interwork __nounwind __softfp float __aeabi_fadd ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fsub ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_frsub( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fmul ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fdiv ( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpeq( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmplt( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpgt( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpge( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpun( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfcmpeq( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfrcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_i2f( int x );
__intrinsic __interwork __nounwind __softfp float __aeabi_ui2f( unsigned int x );
__intrinsic __interwork __nounwind __softfp int __aeabi_f2iz( float x );
__intrinsic __interwork __nounwind __softfp unsigned int __aeabi_f2uiz( float x );
__intrinsic __interwork __nounwind __softfp float __aeabi_l2f( long long x );
__intrinsic __interwork __nounwind __softfp float __aeabi_ul2f( unsigned long long x );
__intrinsic __interwork __nounwind __softfp long long __aeabi_f2lz( float x );
__intrinsic __interwork __nounwind __softfp unsigned long long __aeabi_f2ulz( float x );
__intrinsic __interwork __nounwind __softfp double __aeabi_dadd( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_dsub( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_drsub( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_dmul( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_ddiv( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpeq( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmplt( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpge( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpgt( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpun( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdcmpeq( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdrcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_i2d( int x );
__intrinsic __interwork __nounwind __softfp double __aeabi_ui2d( unsigned int x );
__intrinsic __interwork __nounwind __softfp int __aeabi_d2iz( double d );
__intrinsic __interwork __nounwind __softfp unsigned int __aeabi_d2uiz( double d );
__intrinsic __interwork __nounwind __softfp double __aeabi_f2d( float f );
__intrinsic __interwork __nounwind __softfp float __aeabi_d2f( double d );
__intrinsic __interwork __nounwind __softfp double __aeabi_l2d( long long x );
__intrinsic __interwork __nounwind __softfp double __aeabi_ul2d( unsigned long long x );
__intrinsic __interwork __nounwind __softfp long long __aeabi_d2lz( double d );
__intrinsic __interwork __nounwind __softfp unsigned long long __aeabi_d2ulz( double d );
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_lmul(long long, long long); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_llsl(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_llsr(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_lasr(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_lcmp(long long, long long); 
__intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_ulcmp(unsigned long long, unsigned long long); 
_Pragma("function_effects = no_state, no_write(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_uread4(void *address); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_uwrite4(int value, void *address); 
_Pragma("function_effects = no_state, no_write(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_uread8(void *address); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_uwrite8(long long value, void *address); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy8 (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy4 (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy  (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove8(void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove4(void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset8 (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset4 (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset  (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr8 (void *d, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr4 (void *d, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr  (void *d, unsigned int n); 
__intrinsic __interwork __nounwind unsigned int __get_SP( void ); 
__intrinsic __interwork __nounwind unsigned int __get_LR( void ); 
__intrinsic __interwork __nounwind unsigned int __get_PC( void ); 
__intrinsic __interwork __nounwind void __set_SP( unsigned int ); 
__intrinsic __interwork __nounwind void __set_LR( unsigned int ); 
__intrinsic __interwork __nounwind unsigned int __get_return_address( void ); 
#pragma no_bounds
__intrinsic int __semihosting( unsigned int id, void *p ); 
__intrinsic int __semihosting2( unsigned int id, unsigned int id2, void *p ); 
#line 109 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind void *__aeabi_read_tp(void);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fp2bits32(float);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned long long __iar_fp2bits64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fpgethi64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fpgetlow64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_fpsethi64(double, unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_fpsetlow64(double, unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind float __iar_bits2fp32(unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_bits2fp64(unsigned long long);
__intrinsic __interwork __nounwind __noreturn void __stack_chk_fail(void);
extern unsigned int __stack_chk_guard;
#line 127 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
#pragma language=default
#line 2 "vcast_preprocess.9220.0.c"
#line 1 "C:/Users/<USER>/MBVan/MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC/MBDevStrm/SwApplication/Application/HardwareAbstractionLayer/DiagComMan/Code/PIDDID_Cfg.c"
/** @addtogroup  PIDDID
 ** @{ */

/** @file
 **
 ** Implementation of PIDDID
 **/

/*******************************************************************************
*   File Name               :   PIDDID_Cfg.c
*   Component Name          :   PIDDID
*   UCom                    :   Freescale S12G series
*
*   Create Date             :   2017-03-09
*   Author                  :   guanam
*   Corporation             :   Inalfa Roof Systems
*
*   Abstract Description    :   Implementation of PIDDID 
*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved
*                               Unauthorized copying of this file, via any medium
*                               is strictly prohibited
*                               Proprietary and confidential
*---Revision History------------------------------------------------------------
*   Date        Author    Item     Description
*   2025-02-19	chanv    [#49510]  Added RDIDs and IODIDs for MBVan
*   2024-12-20	chans     [#7009]  Add new Read/Write DID's for MBVan
*   2019-11-12	guanam    [#7009]  Add read and write service for fault snapshot data
*   2019-06-18	boutenp1  [#7054]  Add read service for SCU reset reason
*   2019-04-15  Yongdong  [#6103]  Support rd/wr calibration data using BYD access security
*   2018-11-21  Yongdong  [#5501]  Remove default session support for FE 04
*   2018-11-14  zhifei    [#4330]  - Added IO_CTRL FD04
*   2018-11-13  boutenp1  [#4330]  - Added R/W FE0A, FE0B and R of FE18
*   2018-09-03	boutenp1  [#4820]  - Added bootversion declaration in shared segment
*   2018-03-16  boutenp1  [#3594]  - Added routine override thermal protection
*   2018-02-13  guanam	  [#3379]  - added Read ATS event data service(Glass and Rollo)
*   2018-02-01  guanam	  [#3362]  - added ATD enable and disable routine control service
*   2018-01-26  guanam	  [#3377]  - added Read ATS related service(ATS information, Glass RF and Rollo RF)
*   2017-11-15  trupkesi  [#3220]  - added Read Learning Failed Reason service
*   2017-10-31  trupkesi  [#2703]  - added IOCtrl information
*   2017-05-31  trupkesi  [#2206]  - added calibration identifier
*                                  -  selected session and security access for the services
*                                  - renamed PIDDID_CAL_ROUTINE_LEN to PIDDID_LEARN_ROUTINE_LEN
*   2017-03-09  guanam	  [#2206]  initial revision
*
*******************************************************************************/


/*******************************************************************************
*   Debug Switch Section
*******************************************************************************/

/*******************************************************************************
*   Include File Section
*******************************************************************************/
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"


/** @defgroup PIDDID PIDDID Component
 ** @ingroup Diag Layer
 **
 **  PIDDID
 ** @{ */

/** @file
 **
 ** Public PIDDID header  
 **/

/*******************************************************************************
*   File Name               :   PIDDID_Cfg.h
*   Component Name          :   PIDDID
*   UCom                    :   Freescale S12G series
*
*   Create Date             :   2017-03-09
*   Author                  :   guanam
*   Corporation             :   Inalfa Roof Systems
*
*   Abstract Description    :   Public PIDDID header for PIDDID 
*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved
*                               Unauthorized copying of this file, via any medium
*                               is strictly prohibited
*                               Proprietary and confidential
*---Revision History------------------------------------------------------------
*   Date        Author    Item     Description
*   2025-02-19	chanv    [#49510]  Added RDIDs and IODIDs for MBVan
*   2019-12-17	guanam    [#8518]  Changed PIDDID_INTERLOCK_ENABLE_LEN from 0 to 1 to support result output
*   2019-12-11	guanam    [#8466]  Changed unlearn reason data length to 4 due to data type change
*   2019-11-12	guanam    [#7009]  Add read and write service for fault snapshot data
*   2019-11-05	guanam    [#6341]  Updated ATS reversal reason data length to 18 bytes
*   2019-06-18	boutenp1  [#7054]  Add read service for SCU reset reason
*   2019-04-15  Yongdong  [#6103]  rename ID_SECURED to ID_SECURED_BYD
*   2018-11-14  zhifei    [#4330]  - ADD PIDDID_PANEL_GO_TARGET_POS_LEN DEFINE
*   2018-11-14  zhifei    [#4330]  - change PIDDID_MTC_STATUS_LEN from 3 to 2
*   2018-11-13  boutenp1  [#4330]  - Added sizes for FE0A, FE0B and FE18
*   2018-11-13  boutenp1  [#4330]  - Increased table size for R/W FE0A, FE0B and R of FE18
*   2018-10-22  guanam	  [#4289]  - Deleted #pragma MESSAGE DISABLE C1106 line to remove the compile warning 
*   2018-03-16  boutenp1  [#3594]  - Added routine override thermal protection
*   2018-02-13  guanam	  [#3379]  - added Read ATS event data service(Glass and Rollo)
*   2018-02-01  guanam	  [#3362]  - added ATD enable and disable routine control service
*   2018-01-26  guanam	  [#3377]  - added Read ATS related service(ATS information, Glass RF and Rollo RF)
*   2017-11-15  trupkesi  [#3220]  - added Read Learning Failed Reason service
*   2017-10-31  trupkesi  [#2703]  - added IOCtrl information
*   2017-03-09  trupkesi  [#2206]  - renamed PIDDID_CAL_ROUTINE_LEN to PIDDID_LEARN_ROUTINE_LEN
*                                  - added Calibration data length information
*                                  - changed table size due to new identifier
*   2017-03-09  guanam	  [#2206]  initial revision
*
*******************************************************************************/


/*******************************************************************************
*   Debug Switch Section
*******************************************************************************/

/*******************************************************************************
*    Include File Section
*******************************************************************************/
#line 1 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\irs_std_types.h"


/** @defgroup common Common Group
 ** @ingroup project
 **
 **  IRS common includes
 ** @{ */

/** @file
 **
 ** Header for the IRS standard types
 **/

/*******************************************************************************
*   File Name               :   irs_std_types.h
*   Component Name          :   COMMON
*   UCom                    :   NXP S32K Series
*
*   Create Date             :   2024-05-31
*   Author                  :   kortam
*   Corporation             :   Inalfa Roof Systems
*
*   Abstract Description    :   public header of irs standard types
*   Copyright               :   Copyright (C) Inalfa, Inc - All Rights Reserved
*                               Unauthorized copying of this file, via any medium
*                               is strictly prohibited
*                               Proprietary and confidential
*---Revision History------------------------------------------------------------
*   Date        Author    Item     Description
*   2024-05-22  tasdec    [#] initial revision 
*   2024-05-31  kortam    [44976] update to fix compiler warning 
 *******************************************************************************/


/*******************************************************************************
*   Debug Switch Section
*******************************************************************************/

/*******************************************************************************
*    Include File Section
*******************************************************************************/

#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"
/* stdint.h standard header */
/* Copyright 2003-2017 IAR Systems AB.  */




  #pragma system_include


#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ycheck.h"
/* ycheck.h internal checking header file. */
/* Copyright 2005-2017 IAR Systems AB. */

/* Note that there is no include guard for this header. This is intentional. */


  #pragma system_include


/* __AEABI_PORTABILITY_INTERNAL_LEVEL
 *
 * Note: Redefined each time ycheck.h is included, i.e. for each
 * system header, to ensure that ABI support could be turned off/on
 * individually for each file.
 *
 * Possible values for this preprocessor symbol:
 *
 * 0 - ABI portability mode is disabled.
 *
 * 1 - ABI portability mode (version 1) is enabled.
 *
 * All other values are reserved for future use.
 */





#line 11 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
/* yvals.h internal configuration header file. */
/* Copyright 2001-2017 IAR Systems AB. */





  #pragma system_include


/* Convenience macros */









/* Used to refer to '__aeabi' symbols in the library. */


/* Dinkum version */


/* DLib version */




/* Module consistency. */
#pragma rtmodel = "__dlib_version", "6"

/* IAR compiler version check */





/* Read configuration */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"
/***************************************************
 *
 * DLib_Defaults.h is the library configuration manager.
 *
 * Copyright 2003-2017 IAR Systems AB.
 *
 * This configuration header file performs the following tasks:
 *
 * 1. Includes the configuration header file, defined by _DLIB_CONFIG_FILE,
 *    that sets up a particular runtime environment.
 *
 * 2. Includes the product configuration header file, DLib_Product.h, that
 *    specifies default values for the product and makes sure that the
 *    configuration is valid.
 *
 * 3. Sets up default values for all remaining configuration symbols.
 *
 * This configuration header file, the one defined by _DLIB_CONFIG_FILE, and
 * DLib_Product.h configures how the runtime environment should behave. This
 * includes all system headers and the library itself, i.e. all system headers
 * includes this configuration header file, and the library has been built
 * using this configuration header file.
 *
 ***************************************************
 *
 * DO NOT MODIFY THIS FILE!
 *
 ***************************************************/





  #pragma system_include


/* Include the main configuration header file. */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Config_Normal.h"
/* DLib configuration. */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include


/* No changes to the defaults. */

#line 40 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"
  /* _DLIB_CONFIG_FILE_STRING is the quoted variant of above */
#line 47 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/* Include the product specific header file. */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Product.h"
/* Copyright 2017 IAR Systems AB. */





   #pragma system_include



/*********************************************************************
*
*       Configuration
*
*********************************************************************/

/* Wide character and multi byte character support in library.
 * This is not allowed to vary over configurations, since math-library
 * is built with wide character support.
 */


/* This ensures that the standard header file "string.h" includes
 * the Arm-specific file "DLib_Product_string.h". */


/* This ensures that the standard header file "fenv.h" includes
 * the Arm-specific file "DLib_Product_fenv.h". */


/* This ensures that the standard header file "stdlib.h" includes
 * the Arm-specific file "DLib_Product_stdlib.h". */


/* Max buffer used for swap in qsort */









/* Enable AEABI support */


/* Enable rtmodel for setjump buffer size */


/* Enable parsing of hex floats */






/* Special placement for locale structures when building ropi libraries */




/* Use atomic if possible */




/* CPP-library uses software floatingpoint interface (NOT --libc++) */




/* functions for setting errno should be __no_scratch */


/* Use speedy implementation of floats (simple quad). */


/* Configure time types */


/* Configure generic ELF init routines. */
#line 111 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Product.h"













#line 51 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"



/*
 * The remainder of the file sets up defaults for a number of
 * configuration symbols, each corresponds to a feature in the
 * libary.
 *
 * The value of the symbols should either be 1, if the feature should
 * be supported, or 0 if it shouldn't. (Except where otherwise
 * noted.)
 */


/*
 * File handling
 *
 * Determines whether FILE descriptors and related functions exists or not.
 * When this feature is selected, i.e. set to 1, then FILE descriptors and
 * related functions (e.g. fprintf, fopen) exist. All files, even stdin,
 * stdout, and stderr will then be handled with a file system mechanism that
 * buffers files before accessing the lowlevel I/O interface (__open, __read,
 * __write, etc).
 *
 * If not selected, i.e. set to 0, then FILE descriptors and related functions
 * (e.g. fprintf, fopen) does not exist. All functions that normally uses
 * stderr will use stdout instead. Functions that uses stdout and stdin (like
 * printf and scanf) will access the lowlevel I/O interface directly (__open,
 * __read, __write, etc), i.e. there will not be any buffering.
 *
 * The default is not to have support for FILE descriptors.
 */






/*
 * Use static buffers for stdout
 *
 * This setting controls whether the stream stdout uses a static 80 bytes
 * buffer or uses a one byte buffer allocated in the file descriptor. This
 * setting is only applicable if the FILE descriptors are enabled above.
 *
 * Default is to use a static 80 byte buffer.
 */






/*
 * Support of locale interface
 *
 * "Locale" is the system in C that support language- and
 * contry-specific settings for a number of areas, including currency
 * symbols, date and time, and multibyte encodings.
 *
 * This setting determines whether the locale interface exist or not.
 * When this feature is selected, i.e. set to 1, the locale interface exist
 * (setlocale, etc). A number of preselected locales can be activated during
 * runtime. The preselected locales and encodings are choosen at linkage. The
 * application will start with the "C" locale choosen. (Single byte encoding is
 * always supported in this mode.)
 *
 *
 * If not selected, i.e. set to 0, the locale interface (setlocale, etc) does
 * not exist. The C locale is then preset and cannot be changed.
 *
 * The default is not to have support for the locale interface with the "C"
 * locale and the single byte encoding.
 */





/*
 * Define what memory to place the locale table segment (.iar.locale_table)
 * in.
 */




/*
 * Wide character and multi byte character support in library.
 */

#line 153 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Support of multibytes in printf- and scanf-like functions
 *
 * This is the default value for _DLIB_PRINTF_MULTIBYTE and
 * _DLIB_SCANF_MULTIBYTE. See them for a description.
 *
 * Default is to not have support for multibytes in printf- and scanf-like
 * functions.
 */

#line 172 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Hexadecimal floating-point numbers in strtod
 *
 * If selected, i.e. set to 1, strtod supports C99 hexadecimal floating-point
 * numbers. This also enables hexadecimal floating-points in internal functions
 * used for converting strings and wide strings to float, double, and long
 * double.
 *
 * If not selected, i.e. set to 0, C99 hexadecimal floating-point numbers
 * aren't supported.
 *
 * Default is not to support hexadecimal floating-point numbers.
 */






/*
 * Printf configuration symbols.
 *
 * All the configuration symbols described further on controls the behaviour
 * of printf, sprintf, and the other printf variants.
 *
 * The library proves four formatters for printf: 'tiny', 'small',
 * 'large', and 'default'.  The setup in this file controls all except
 * 'tiny'.  Note that both small' and 'large' explicitly removes
 * some features.
 */

/*
 * Handle multibytes in printf
 *
 * This setting controls whether multibytes and wchar_ts are supported in
 * printf. Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default setting.
 */
#line 223 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Support of formatting anything larger than int in printf
 *
 * This setting controls if 'int' should be used internally in printf, rather
 * than the largest existing integer type. If 'int' is used, any integer or
 * pointer type formatting use 'int' as internal type even though the
 * formatted type is larger. Set to 1 to use 'int' as internal type, otherwise
 * set to 0.
 *
 * See also next configuration.
 *
 * Default is to internally use largest existing internally type.
 */




/*
 * Support of formatting anything larger than long in printf
 *
 * This setting controls if 'long' should be used internally in printf, rather
 * than the largest existing integer type. If 'long' is used, any integer or
 * pointer type formatting use 'long' as internal type even though the
 * formatted type is larger. Set to 1 to use 'long' as internal type,
 * otherwise set to 0.
 *
 * See also previous configuration.
 *
 * Default is to internally use largest existing internally type.
 */








/*
 * Emit a char a time in printf
 *
 * This setting controls internal output handling. If selected, i.e. set to 1,
 * then printf emits one character at a time, which requires less code but
 * can be slightly slower for some types of output.
 *
 * If not selected, i.e. set to 0, then printf buffers some outputs.
 *
 * Note that it is recommended to either use full file support (see
 * _DLIB_FILE_DESCRIPTOR) or -- for debug output -- use the linker
 * option "-e__write_buffered=__write" to enable buffered I/O rather
 * than deselecting this feature.
 */





/*
 * Scanf configuration symbols.
 *
 * All the configuration symbols described here controls the
 * behaviour of scanf, sscanf, and the other scanf variants.
 *
 * The library proves three formatters for scanf: 'small', 'large',
 * and 'default'.  The setup in this file controls all, however both
 * 'small' and 'large' explicitly removes some features.
 */

/*
 * Handle multibytes in scanf
 *
 * This setting controls whether multibytes and wchar_t:s are supported in
 * scanf. Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default.
 */
#line 311 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Handle multibytes in asctime and strftime.
 *
 * This setting controls whether multibytes and wchar_ts are
 * supported.Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default setting.
 */
#line 331 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Implement "qsort" using a bubble sort algorithm.
 *
 * Bubble sort is less efficient than quick sort but requires less RAM
 * and ROM resources.
 */






/*
 * Set Buffert size used in qsort
 */






/*
 * Use a simple rand implementation to reduce memory footprint.
 *
 * The default "rand" function uses an array of 32 32-bit integers of memory to
 * store the current state.
 *
 * The simple "rand" function uses only a single 32-bit integer. However, the
 * quality of the generated psuedo-random numbers are not as good as
 * the default implementation.
 */






/*
 * Set attributes for the function used by the C-SPY debug interface to stop at.
 */





/*
 * Used by products where one runtime library can be used by applications
 * with different data models, in order to reduce the total number of
 * libraries required. Typically, this is used when the pointer types does
 * not change over the data models used, but the placement of data variables
 * or/and constant variables do.
 *
 * If defined, this symbol is typically defined to the memory attribute that
 * is used by the runtime library. The actual define must use a
 * _Pragma("type_attribute = xxx") construct. In the header files, it is used
 * on all statically linked data objects seen by the application.
 */




#line 400 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Turn on support for the Target-specific ABI. The ABI is based on the
 * ARM AEABI. A target, except ARM, may deviate from it.
 */

#line 414 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


  /* Possible AEABI deviations */
#line 424 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

#line 432 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

  /*
   * The "difunc" table contains information about C++ objects that
   * should be dynamically initialized, where each entry in the table
   * represents an initialization function that should be called. When
   * the symbol _DLIB_AEABI_DIFUNC_CONTAINS_OFFSETS is true, each
   * entry in the table is encoded as an offset from the entry
   * location. When false, the entries contain the actual addresses to
   * call.
   */





/*
 * Only use IA64 functions
 *
 * Remove the C++ __aeabi functions when using the IA64 interface. Used in
 * ARM AARCH64 mode.
 *
 */
#line 461 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Turn on usage of a pragma to tell the linker the number of elements used
 * in a setjmp jmp_buf.
 */






/*
 * If true, the product supplies a "DLib_Product_string.h" file that
 * is included from "string.h".
 */





/*
 * Determine whether the math fma routines are fast or not.
 */





/*
 * Favor speed versus some size enlargements in floating point functions.
 */





/*
 * Include dlmalloc as an alternative heap manager in the product.
 *
 * Typically, an application will use a "malloc" heap manager that is
 * relatively small but not that efficient. An application can
 * optionally use the "dlmalloc" package, which provides a more
 * effective "malloc" heap manager, if it is included in the product
 * and supported by the settings.
 *
 * See the product documentation on how to use it, and whether or not
 * it is included in the product.
 */


  /* size_t/ptrdiff_t must be a 4 bytes unsigned integer. */
#line 518 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Make sure certain C++ functions use the soft floating point variant.
 */






/*
 * Allow the 64-bit time_t interface?
 *
 * Default is yes if long long is 64 bits.
 */

#line 542 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Is time_t 64 or 32 bits?
 *
 * Default is 64 bits for the platform.
 */






/*
 * Do we include math functions that demands lots of constant bytes?
 * (like erf, erfc, expm1, fma, lgamma, tgamma, and *_accurate)
 *
 */






/*
 * Support of weak.
 *
 * __weak must be supported. Support of weak means that the call to
 * a weak declared function that isn't part of the application will be
 * executed as a nop instruction.
 *
 */

#line 582 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Deleted options
 */












#line 43 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"






/* A definiton for a function of what effects it has.
   NS  = no_state, errno, i.e. it uses no internal or external state. It may
         write to errno though
   NE  = no_state, i.e. it uses no internal or external state, not even
         writing to errno.
   NRx = no_read(x), i.e. it doesn't read through pointer parameter x.
   NWx = no_write(x), i.e. it doesn't write through pointer parameter x.
   Rx  = returns x, i.e. the function will return parameter x.

   All the functions with effects also has "always_returns",
   i.e. the function will always return.
*/

#line 81 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* Common function attribute macros */






/* Extern "C" handling */
#line 99 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"


/*
 * Support for C99/C11 functionality, C99 secure C functionality, and some
 * other functionality.
 *
 * This setting makes available some macros, functions, etc that are
 * either mandatory in C99/C11 or beneficial.
 *
 * Default is to include them.
 *
 * Disabling this in C++ mode will not compile (some C++ functions uses C99
 * functionality).
 */


  /* Default turned on only when compiling C89 (not C++, C99, or C11). */
#line 124 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"





/* Secure C */
#line 142 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"




/* C++ language setup */
#line 191 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 199 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 206 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* MB_LEN_MAX (max for utf-8 is 4) */


/* for parsing numerics */




/* wchar_t setup */





  typedef unsigned int _Wchart;
  typedef unsigned int _Wintt;
#line 238 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* POINTER PROPERTIES */


/* size_t setup */
typedef unsigned int     _Sizet;

/* Basic integer sizes */
typedef signed char   __int8_t;
typedef unsigned char  __uint8_t;
typedef signed short int   __int16_t;
typedef unsigned short int  __uint16_t;
typedef signed int   __int32_t;
typedef unsigned int  __uint32_t;

   typedef signed long long int   __int64_t;
   typedef unsigned long long int  __uint64_t;




typedef signed int   __intptr_t;
typedef unsigned int  __uintptr_t;

/* mbstatet setup */
typedef struct _Mbstatet
{ /* state of a multibyte translation */

    unsigned int _Wchar;  /* Used as an intermediary value (up to 32-bits) */
    unsigned int _State;  /* Used as a state value (only some bits used) */
#line 275 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 299 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
} _Mbstatet;






/* printf setup */


/* stdarg PROPERTIES */
#line 321 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
  typedef struct __va_list __Va_list;














/* File position */
typedef struct
{

    long long _Off;    /* can be system dependent */



  _Mbstatet _Wstate;
} _Fpost;





/* THREAD AND LOCALE CONTROL */


/* MULTITHREAD PROPERTIES */

  
  /* The lock interface for DLib to use. */
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_StaticGuard(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_StaticGuard(void);

#line 373 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

  typedef void *__iar_Rmtx;

  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Initdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Dstdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockdynamiclock(__iar_Rmtx *);

  
#line 406 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 446 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"



/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 12 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"





/* Fixed size types. These are all optional. */

  typedef signed char          int8_t;
  typedef unsigned char        uint8_t;



  typedef signed short int         int16_t;
  typedef unsigned short int       uint16_t;



  typedef signed int         int32_t;
  typedef unsigned int       uint32_t;



  typedef signed long long int         int64_t;
  typedef unsigned long long int       uint64_t;


/* Types capable of holding at least a certain number of bits.
   These are not optional for the sizes 8, 16, 32, 64. */
typedef signed char      int_least8_t;
typedef unsigned char    uint_least8_t;

typedef signed short int     int_least16_t;
typedef unsigned short int   uint_least16_t;

typedef signed int     int_least32_t;
typedef unsigned int   uint_least32_t;

/* This isn't really optional, but make it so for now. */

  typedef signed long long int   int_least64_t;


  typedef unsigned long long int uint_least64_t;


/* The fastest type holding at least a certain number of bits.
   These are not optional for the size 8, 16, 32, 64.
   For now, the 64 bit size is optional in IAR compilers. */
typedef signed int       int_fast8_t;
typedef unsigned int     uint_fast8_t;

typedef signed int      int_fast16_t;
typedef unsigned int    uint_fast16_t;

typedef signed int      int_fast32_t;
typedef unsigned int    uint_fast32_t;


  typedef signed long long int    int_fast64_t;


  typedef unsigned long long int  uint_fast64_t;


/* The integer type capable of holding the largest number of bits. */
typedef signed long long int          intmax_t;
typedef unsigned long long int        uintmax_t;

/* An integer type large enough to be able to hold a pointer.
   This is optional, but always supported in IAR compilers. */
typedef signed int          intptr_t;
typedef unsigned int        uintptr_t;

/* An integer capable of holding a pointer to a specific memory type. */



typedef int __data_intptr_t; typedef unsigned int __data_uintptr_t;


/* Minimum and maximum limits. */

























































































/* Macros expanding to integer constants. */
































/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 44 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\irs_std_types.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdbool.h"
/* stdbool.h header */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include













/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 45 "C:\\USERS\\<USER>\\MBVAN\\MB_ASSY_SCU_SW_DEVSTRM_SHAMALISUNIL.KALEKAR@TESSOLVE_WORKSPACE_VC\\MBDEVSTRM\\SWAPPLICATION\\APPLICATION\\COMMON\\CODE\\irs_std_types.h"

/*******************************************************************************
*   Macro Define Section
*******************************************************************************/















/* Adding these data types to handle issues in building the SW - Start */
typedef uint8_t uint8;
typedef uint16_t uint16;
typedef uint32_t uint32;
typedef _Bool boolean;
typedef int8_t int8;
typedef int16_t int16;



/* Adding these data types to handle issues in building the SW - End */

/*******************************************************************************
*   Type Define Section
*******************************************************************************/
/** IRS return values for standard information*/
typedef enum 
{
	SCU_STATUS_SUCCESS                      = 0x000U,    /*!< ECU success status */
	SCU_STATUS_ERROR                        = 0x001U,    /*!< ECU failure status */
    SCU_STATUS_BUSY                         = 0x002U,    /*!< ECU busy status */
    SCU_STATUS_TIMEOUT                      = 0x003U,    /*!< ECU timeout status */
	SCU_STATUS_CRC_ERROR                    = 0x004U    /*!< ECU NVM CRC errors */
} HwAbs_tenStatus;

/*ExtDev_tenFault should not be more than 15 count*/
typedef enum 
{
	EXTDEV_SPI_CRC_FAULT= 0,
	EXTDEV_VCC1WARN_FAULT,
	EXTDEV_CHRAGE_PUMP_FAULT,
	EXTDEV_THERM_STAT_TPW_TSD1_FAULT,
	EXTDEV_THERM_STAT_TSD2_TSD2SAFE_FAULT,
	EXTDEV_HS_SUPPLY_FAULT,
	EXTDEV_VSINT_FAULT,
	EXTDEV_VS_SUPPLY_FAULT,
	EXTDEV_LIN_FAIL_FAULT,
	EXTDEV_DSOV_MTRACT_FAULT,
	EXTDEV_WKSTS_MTRBKEMF_WKUP_FAULT,
	EXTDEV_WKSTS_SPURIOUS_WKUP_FAULT,
	EXTDEV_DEV_STAT_FAILURE_FAULT,
	EXTDEV_SUP_STAT_VCC1FAULT,
} ExtDev_tenFaults;

typedef enum 
{
	MotorDirection_CW = 0,
	MotorDirection_CCW,
	MotorDirection_STOP
} ExtDev_tenDirection;
/*******************************************************************************
*   Global Variable Declaration Section
*******************************************************************************/


/*******************************************************************************
*   Global Function Declaration Section
*******************************************************************************/


/**@} */
#line 64 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"


/*******************************************************************************
*   Macro Define Section
*******************************************************************************/
/* Numbers of DID Read Support */


/* DID Read/Write Table size */


/* DID Data Length */
#line 125 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"


/* Routine Ctrl Table size */

/* Routine Control Data Length */



// #define     PIDDID_PV_TEST_MODE_LEN                      ((uint8)0) 	/* Nothing to return, routine status service not supported */

// #define     PIDDID_PANEL_GO_TARGET_POS_LEN               ((uint8)0) 	/* Nothing to return, routine status service not supported */
#line 170 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"

/* IO Control Table size */

/* IO Control Data Length*/
#line 187 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"






/* DID Read Support */


/* DID Write Support */


/* ID Secured */




/* Access Type */




/* DID Session Selection (DIDSS) Configuration */
/*----------------------------------------------------------------------------------------------*/
/*               E E P D                    EOLEMS   = EOL Session */
/*               O X R S                    EXTDS    = Extended Disgnostic Session */
/*               L T G                      PRGS     = Programming Session */
/*               S D S                      DS       = Default Session */
/*                 S                        */
/*                                          */
/*----------------------------------------------------------------------------------------------*/






#line 233 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\DiagComMan\\Code\\PIDDID_Cfg.h"
    

/*******************************************************************************
*   Type Define Section
*******************************************************************************/
typedef struct
{
	uint16 rxID;
	struct {
		uint8 isRead	:1;		/* Support read? */
		uint8 isWrite	:1;		/* Support write? */
		uint8 securedId	:2;		/* Secured ID */
		uint8 stSession	:4;		/* Supported Session bit3-default session,bit4-program session,bit5 extended session */
	} bitsForID;
	uint8 rxtxLen;					/* Receiving or Sending Length */
}ISOUDS_rxID;

/*******************************************************************************
*   Global Variable Declaration Section
*******************************************************************************/
extern const ISOUDS_rxID PIDDID_RdWrDID[((uint16)64)];
extern const ISOUDS_rxID PIDDID_Rtn[((uint16)36)];
extern const ISOUDS_rxID PIDDID_IoCtrlbyID[((uint8)13)];

//#pragma DATA_SEG SHARED_BOOT
extern uint8 BOOT_versionNumber[((uint8)0x09)];
//#pragma DATA_SEG DEFAULT_RAM

/*******************************************************************************
*   Global Function Declaration Section
*******************************************************************************/

/**@} */


#line 56 "C:/Users/<USER>/MBVan/MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC/MBDevStrm/SwApplication/Application/HardwareAbstractionLayer/DiagComMan/Code/PIDDID_Cfg.c"

/*******************************************************************************
*   Local Macro Define Section
*******************************************************************************/

/*******************************************************************************
*   Local Type Define Section
*******************************************************************************/

/*******************************************************************************
*   Global Variable Define Section
*******************************************************************************/
/* Read and Write DID table */
const ISOUDS_rxID PIDDID_RdWrDID[((uint16)64)] =
{
	/* ID */            /* Read */        /* Write */		 /* Secured */   /* Session */      /* Routine Result Sending Length */
	{ (uint16)0xF173, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)2)        },
	{ (uint16)0xF170, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)2)          },
	{ (uint16)0xF170, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)2)          },
	{ (uint16)0xF171, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)3)         },
	{ (uint16)0xF172, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)4)          },
    { (uint16)0xFE01, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)6)   },
    { (uint16)0xFE01, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)6)   },
	{ (uint16)0xFE02, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)6)    },
	{ (uint16)0xFE02, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)6)    },
	{ (uint16)0xFE03, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)9)     },
	{ (uint16)0xFE03, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)9)     },
	{ (uint16)0xFE0A, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)9)   },
	{ (uint16)0xFE0A, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)9)   },
	{ (uint16)0xFE0B, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)6)    },
	{ (uint16)0xFE0B, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)6)    },
    { (uint16)0xFD01, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)1)   	   },
	{ (uint16)0xFD02, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)2)      },
	{ (uint16)0xFD03, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)2)   },
	{ (uint16)0xFD04, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)2)       },
	{ (uint16)0xFD06, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)1)   },
    { (uint16)0xFD07, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)1)  },
	{ (uint16)0xFD08, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)5)         },
	{ (uint16)0xFD08, { ((uint8)0), ((uint8)1),     ((uint8)0), ((uint8)0x0F) }, ((uint8)5)         }, //only 2nd byte (R+W) Factory mode status
	{ (uint16)0xFD09, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)1)      },
	{ (uint16)0xFD10, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)1)    },
	{ (uint16)0xFD11, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)1)   },
    { (uint16)0xFD12, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)7)       },
	{ (uint16)0xFE09, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)6)         },
	{ (uint16)0xFE09, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)6)         },
	{ (uint16)0xFE11, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)2)       },
	{ (uint16)0xFE12, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)4)      },
	{ (uint16)0xFE12, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)4)      },
	{ (uint16)0xFE13, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)6)     },
    { (uint16)0xFE14, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)8)   },
    { (uint16)0xFE15, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)1)    },
	{ (uint16)0xFE15, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)1)    },
	{ (uint16)0xFE22, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)4)        },
	{ (uint16)0xFE24, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)4)         },
	{ (uint16)0xFE27, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)2)         },
    { (uint16)0xFE28, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)3)          },
	{ (uint16)0xFE29, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)24)         },
	{ (uint16)0xFE2A, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)10)       },
	{ (uint16)0xFE00, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)204)            },
    { (uint16)0xFE00, { ((uint8)0), ((uint8)1),     ((uint8)0), ((uint8)0x0F) }, ((uint8)204)            },
	{ (uint16)0xFE06, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)204)            },
    { (uint16)0xFE06, { ((uint8)0), ((uint8)1),     ((uint8)0), ((uint8)0x0F) }, ((uint8)204)            },
	{ (uint16)0xF100, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)4)         },
	{ (uint16)0xF10D, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)4)      },
    { (uint16)0xF111, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)10)      },
    { (uint16)0xF111, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)10)      },
    { (uint16)0xF150, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)3)      },
	{ (uint16)0xF150, { ((uint8)0), ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)3)      },
	{ (uint16)0xF151, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)3)      },
	{ (uint16)0xF154, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)2)      },
	{ (uint16)0xF155, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)2)      },
	{ (uint16)0xF18C, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)6)      },
	{ (uint16)0xF131, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)1)      },
	{ (uint16)0x0102, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)12)      },
	{ (uint16)0x0102, { ((uint8)0), ((uint8)1),     ((uint8)0), ((uint8)0x0F) }, ((uint8)12)      },
	{ (uint16)0xF103, { ((uint8)1), ((uint8)0),     ((uint8)0), ((uint8)0x0F) }, ((uint8)48)      },
	{ (uint16)0xFE30, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)((uint8)4) - 2) },
	{ (uint16)0xFE31, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)((uint8)4) - 2)  },
	{ (uint16)0xFE32, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)((uint8)22) - 2)    },
	{ (uint16)0xFE33, { ((uint8)1),     ((uint8)0), ((uint8)0), ((uint8)0x0F) }, ((uint8)((uint8)4) - 2) },



};

/* Routine Control ID table */
const ISOUDS_rxID PIDDID_Rtn[((uint16)36)] =
{
	  /* ID */          /* Read */        /* Write */        /* Secured */   /* Session */      /* Routine Result Sending Length */
	{ (uint16)0xF000, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)       },
    { (uint16)0xF001, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)1)  },
	{ (uint16)0xF002, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)1)  },
//	{ (uint16)0xF003, { READ_NOT_SUPPORT, WRITE_NOT_SUPPORT, ID_SECURED_EOL, SESSION_T_T_F_F }, PIDDID_PV_TEST_MODE_LEN        }, // Shall not be enabled for use at customer or together with a roof, because it might damage the roof due to the unstoppable movement
	{ (uint16)0xF004, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)              },
//	{ (uint16)0xFD04, { READ_NOT_SUPPORT, WRITE_NOT_SUPPORT, ID_SECURED_EOL, SESSION_T_T_F_F }, PIDDID_PANEL_GO_TARGET_POS_LEN }, // Implemented as part of GEN2 0xF010
	{ (uint16)0xFE09, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)1)         },
    { (uint16)0xF00F, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)9)           },
    { (uint16)0xF010, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)1)                  },
    { (uint16)0xF011, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)1)                },
    { (uint16)0xF012, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)1)                      },
    { (uint16)0xF013, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)                       },
    { (uint16)0xF020, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)              },
    { (uint16)0xF021, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)             },
    { (uint16)0xF022, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)             },
    { (uint16)0xF030, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)3)                   },
    { (uint16)0xF095, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)           },    
    { (uint16)0xF096, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)            },
    { (uint16)0xF097, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)            },
    { (uint16)0xF099, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)                 },
    { (uint16)0xF09A, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)     },
    { (uint16)0xF09B, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)2)     },
    { (uint16)0xF09D, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)0)        },
    { (uint16)0xF09E, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)0)        },
    { (uint16)0xF0FA, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)0)                   },
    { (uint16)0x0307, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)0)                      },
    { (uint16)0x0219, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)                          },
    { (uint16)0x0245, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)                   },
    { (uint16)0xF005, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)              },
    { (uint16)0xF006, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)1)           },
    { (uint16)0xF007, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)4)                    },
    { (uint16)0xF008, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)        },
    { (uint16)0xF009, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)0)             },
    { (uint16)0xF00A, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)                  },
    { (uint16)0xFE08, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x0C) }, ((uint8)1)                     },
    { (uint16)0xFE0A, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)                    },
	{ (uint16)0xF00B, { ((uint8)0), ((uint8)0), ((uint8)0), ((uint8)0x0C) }, ((uint8)1)                    },
};

/* IO Control ID table */
const ISOUDS_rxID PIDDID_IoCtrlbyID[((uint8)13)] =
{
	  /* ID */          /* Read */        /* Write */        /* Secured */   /* Session */      /* Routine Result Sending Length */
	{ (uint16)0xFE10, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)   },
	{ (uint16)0xFE11, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)   },
	{ (uint16)0xFE15, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0) },
    { (uint16)0xFD10, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)},
	{ (uint16)0xFD15, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)},
	{ (uint16)0xFD16, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)},
    { (uint16)0xFD3E, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)},
	{ (uint16)0xFD3F, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)},
	{ (uint16)0xFD41, { ((uint8)0), ((uint8)0), ((uint8)2), ((uint8)0x08) }, ((uint8)0)},
    { (uint16)0xFE30, { ((uint8)1),     ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)4)    },
    { (uint16)0xFE31, { ((uint8)1),     ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)4)   },
    { (uint16)0xFE32, { ((uint8)1),     ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)22)     },
    { (uint16)0xFE33, { ((uint8)1),     ((uint8)1),     ((uint8)2), ((uint8)0x0C) }, ((uint8)4) },
};

//#pragma DATA_SEG SHARED_BOOT
uint8 BOOT_versionNumber[((uint8)0x09)];
//#pragma DATA_SEG DEFAULT_RAM


/*******************************************************************************
*   Local Variable Define Section
*******************************************************************************/

/******************************************************************************
*   Local Function Prototype  Section
******************************************************************************/

/*******************************************************************************
*   Local Function Define Section
*******************************************************************************/

/*******************************************************************************
*   Global Function Define Section
*******************************************************************************/


/**@} */

#line 3 "vcast_preprocess.9220.0.c"
