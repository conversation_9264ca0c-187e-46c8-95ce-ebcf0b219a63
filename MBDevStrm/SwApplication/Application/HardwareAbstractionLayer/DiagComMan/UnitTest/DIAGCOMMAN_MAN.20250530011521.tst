-- VectorCAST 22 (03/17/22)
-- Test Case Script
--
-- Environment    : DIAGCOMMAN_MAN
-- Unit(s) Under Test: ISOUDS ISOUDS_ClrDTC ISOUDS_CntrlDTCSetting ISOUDS_CommCntrl ISOUDS_ECUReset ISOUDS_IOCtrlByID ISOUDS_IoCfg ISOUDS_LinkCntrl ISOUDS_RdCfg ISOUDS_RdDTCInf ISOUDS_RdDaByID ISOUDS_RdMemByAddr ISOUDS_RdScCfg ISOUDS_RdScDaByID ISOUDS_ReqDwnld ISOUDS_ReqUpld ISOUDS_RtnCfg ISOUDS_RtnCntrl ISOUDS_SA ISOUDS_StrtDiagSess ISOUDS_TrnsfrDa ISOUDS_TrnsfrExit ISOUDS_TstrPrsnt ISOUDS_WrCfg ISOUDS_WrDaByID ISOUDS_WrMemByAddr LINTP LINTP_Event LINTP_NM LINTP_NodeSrv LINTP_NodeSrv_Cfg LINTP_Srv PIDDID_Cfg
--
-- Script Features
TEST.SCRIPT_FEATURE:C_DIRECT_ARRAY_INDEXING
TEST.SCRIPT_FEATURE:CPP_CLASS_OBJECT_REVISION
TEST.SCRIPT_FEATURE:MULTIPLE_UUT_SUPPORT
TEST.SCRIPT_FEATURE:REMOVED_CL_PREFIX
TEST.SCRIPT_FEATURE:MIXED_CASE_NAMES
TEST.SCRIPT_FEATURE:STATIC_HEADER_FUNCS_IN_UUTS
TEST.SCRIPT_FEATURE:VCAST_MAIN_NOT_RENAMED
--

-- Unit: ISOUDS

-- Subprogram: ISOUDS_ClearEcuResetPosRespFlag

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ClearEcuResetPosRespFlag.bEcuRstPosResp0
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:ISOUDS_ClearEcuResetPosRespFlag
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ClearEcuResetPosRespFlag.bEcuRstPosResp0
TEST.NOTES:
verify bEcuRstPosResp =0
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.bEcuRstPosResp:false
TEST.END

-- Subprogram: ISOUDS_EcuResetPosRespComplete

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_EcuResetPosRespComplete.bEcuRstPosResp1
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:ISOUDS_EcuResetPosRespComplete
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_EcuResetPosRespComplete.bEcuRstPosResp1
TEST.NOTES:
bEcuRstPosResp = 1;
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.bEcuRstPosResp:true
TEST.END

-- Subprogram: ISOUDS_GetECUResetReq

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetECUResetReq
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:ISOUDS_GetECUResetReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetECUResetReq
TEST.NOTES:
Verify return value should be same as ISOUDS_EcuRstReq
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_EcuRstReq:1
TEST.EXPECTED:ISOUDS.ISOUDS_GetECUResetReq.return:1
TEST.END

-- Subprogram: ISOUDS_GetEcuResetPosRespStatus

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetEcuResetPosRespStatus.bEcuRstPosResp1
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:ISOUDS_GetEcuResetPosRespStatus
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetEcuResetPosRespStatus.bEcuRstPosResp1
TEST.NOTES:
Set bEcuRstPosResp = 1;
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.bEcuRstPosResp:true
TEST.EXPECTED:ISOUDS.ISOUDS_GetEcuResetPosRespStatus.return:true
TEST.END

-- Subprogram: ISOUDS_ReqECUReset

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqECUReset.ISOUDS_EcuRstReq_1
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:ISOUDS_ReqECUReset
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqECUReset.ISOUDS_EcuRstReq_1
TEST.NOTES:
Verify ISOUDS_EcuRstReq =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS.ISOUDS_ReqECUReset.u8ResetType:1
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_EcuRstReq:1
TEST.END

-- Subprogram: ISOUDS_Rst

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_Rst
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:ISOUDS_Rst
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_Rst
TEST.NOTES:
ISOUDS_Conf.srvSt = (0x00U);
    /* Reset the service id of the Lin service to 0 */
  ISOUDS_Conf.srvId = (uint8)0x00;
    /* Holds the num of bytes have been received from the multi frame 
        reception */
  ISOUDS_Conf.srvLen = (uint16)0x00;
    /* Holds the negative response code of the service */
  ISOUDS_Conf.srvNegResp = (uint8)0x00;
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvId:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvNegResp:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_Rst.ISOUDS_Sess_1
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:ISOUDS_Rst
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_Rst.ISOUDS_Sess_1
TEST.NOTES:
set ISOUDS_Sess =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:1
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvId:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvNegResp:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:0
TEST.END

-- Subprogram: UDS

-- Test Case: DIAGCOMMAN_MAN_UDS
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS
TEST.END

-- Subprogram: UDS_Init

-- Test Case: DIAGCOMMAN_MAN_UDS_Init
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_Init
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_Init
TEST.END

-- Subprogram: UDS_LookUpTbl

-- Test Case: DIAGCOMMAN_MAN_UDS_LookUpTbl.default
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_LookUpTbl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_LookUpTbl.default
TEST.END

-- Subprogram: UDS_SrvPIndTxMsg

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPIndTxMsg
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPIndTxMsg
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPIndTxMsg
TEST.NOTES:
Verify servst =8
TEST.END_NOTES:
TEST.EXPECTED:LINTP.<<GLOBAL>>.ISOUDS_Conf.srvSt:8
TEST.END

-- Subprogram: UDS_SrvPInit

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPInit
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPInit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPInit
TEST.NOTES:
values should be updated as 
ISOUDS_Sess = (uint8)(0x01U);
    /* Initialize the state to IDLE */
  ISOUDS_Conf.srvSt = (uint8)(0x00U);
    /* Initialize Frame */
  ISOUDS_Conf.srvFrame = (uint8)(0);
    /* Ecu Reset Request */
  ISOUDS_EcuRstReq = 0u;
  LINSTACK_ECUSt = 0x00;
  ISOUDS_S3ServerTimer = 0;
  ISOUDS_S3ServerTimer_Running = 0u
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvFrame:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer_Running:false
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_EcuRstReq:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.LINSTACK_ECUSt:0
TEST.END

-- Subprogram: UDS_SrvPNewMsgInd

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPNewMsgInd
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPNewMsgInd
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPNewMsgInd
TEST.NOTES:
Values should be updated as 
ISOUDS_Conf.srvSt = (0x02U)

ISOUDS_S3ServerTimer = 0;
      ISOUDS_S3ServerTimer_Running = 0u
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:2
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer_Running:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPNewMsgInd.firstFrame_1
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPNewMsgInd
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPNewMsgInd.firstFrame_1
TEST.NOTES:
Set firstFrame = true
ISOUDS_S3ServerTimer = 0;
    ISOUDS_S3ServerTimer_Running = 0u;
TEST.END_NOTES:
TEST.VALUE:ISOUDS.UDS_SrvPNewMsgInd.firstFrame:true
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer_Running:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPNewMsgInd.srvst_2
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPNewMsgInd
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPNewMsgInd.srvst_2
TEST.NOTES:
Set
ISOUDS_Conf.srvSt = (0x02U)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:2
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer:0
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer_Running:false
TEST.END

-- Subprogram: UDS_SrvPTask

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPTask
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPTask
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPTask
TEST.END

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPTask.ISOUDS_S3ServerTimer_5001
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPTask
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPTask.ISOUDS_S3ServerTimer_5001
TEST.NOTES:
Set SOUDS_S3ServerTimer >5000
TimerHwAbs_GetElapsedTime >5000
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer:5001
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer_Running:true
TEST.VALUE:uut_prototype_stubs.TimerHwAbs_GetElapsedTime.return:5001
TEST.END

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPTask.ISOUDS_S3ServerTimer_Running
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPTask
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPTask.ISOUDS_S3ServerTimer_Running
TEST.NOTES:
Set ISOUDS_S3ServerTimer_Running != 0u
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_S3ServerTimer_Running:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPTask.ISOUDS_Sess_1
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPTask
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPTask.ISOUDS_Sess_1
TEST.NOTES:
set ISOUDS_Sess =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:0
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPTask.srvSt_2
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPTask
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPTask.srvSt_2
TEST.NOTES:
Set srvst =2
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_UDS_SrvPTask.srvst_5
TEST.UNIT:ISOUDS
TEST.SUBPROGRAM:UDS_SrvPTask
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_UDS_SrvPTask.srvst_5
TEST.NOTES:
Set srvst =5
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:5
TEST.END

-- Unit: ISOUDS_ClrDTC

-- Subprogram: ISOUDS_ClrDiagInf

-- Test Case: DIAGCOMMAN_ISOUDS_ClrDiagInf_ISOUDS_GetSAStLevel_61
TEST.UNIT:ISOUDS_ClrDTC
TEST.SUBPROGRAM:ISOUDS_ClrDiagInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_ISOUDS_ClrDiagInf_ISOUDS_GetSAStLevel_61
TEST.NOTES:
set ISOUDS_GetSAStLevel=1
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSAStLevel
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvLen:4
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff:<<malloc 3>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[0]:0xFF
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[1]:0xFF
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[2]:0xFF
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSAStLevel.SALevel:0x61
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSAStLevel.return:true
TEST.END

-- Test Case: DIAGCOMMAN_ISOUDS_ClrDiagInf_dataBuff[0]_0xFF
TEST.UNIT:ISOUDS_ClrDTC
TEST.SUBPROGRAM:ISOUDS_ClrDiagInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_ISOUDS_ClrDiagInf_dataBuff[0]_0xFF
TEST.NOTES:
set dataBuff[0] == (uint8)0xFF)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvLen:4
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[0]:0xFF
TEST.END

-- Test Case: DIAGCOMMAN_ISOUDS_ClrDiagInf_dataBuff[1]_0xFF
TEST.UNIT:ISOUDS_ClrDTC
TEST.SUBPROGRAM:ISOUDS_ClrDiagInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_ISOUDS_ClrDiagInf_dataBuff[1]_0xFF
TEST.NOTES:
set dataBuff[1] == (uint8)0xFF)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvLen:4
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff:<<malloc 2>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[0]:0xFF
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[1]:0xFF
TEST.END

-- Test Case: DIAGCOMMAN_ISOUDS_ClrDiagInf_dataBuff[2]_0xFF
TEST.UNIT:ISOUDS_ClrDTC
TEST.SUBPROGRAM:ISOUDS_ClrDiagInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_ISOUDS_ClrDiagInf_dataBuff[2]_0xFF
TEST.NOTES:
set dataBuff[2] == (uint8)0xFF)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvLen:4
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff:<<malloc 3>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[0]:0xFF
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[1]:0xFF
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.dataBuff[2]:0xFF
TEST.END

-- Test Case: DIAGCOMMAN_ISOUDS_ClrDiagInf_default
TEST.UNIT:ISOUDS_ClrDTC
TEST.SUBPROGRAM:ISOUDS_ClrDiagInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_ISOUDS_ClrDiagInf_default
TEST.NOTES:
set srvst =2 and srvlen more than 5
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvLen:7
TEST.END

-- Test Case: DIAGCOMMAN_ISOUDS_ClrDiagInf_srvlen4
TEST.UNIT:ISOUDS_ClrDTC
TEST.SUBPROGRAM:ISOUDS_ClrDiagInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_ISOUDS_ClrDiagInf_srvlen4
TEST.NOTES:
set srvst =2 and srvlen =4
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_ISOUDS_ClrDiagInf_srvst4
TEST.UNIT:ISOUDS_ClrDTC
TEST.SUBPROGRAM:ISOUDS_ClrDiagInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_ISOUDS_ClrDiagInf_srvst4
TEST.NOTES:
set srvst =2 and srvlen=4
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvSt:4
TEST.VALUE:ISOUDS_ClrDTC.ISOUDS_ClrDiagInf.ISOUDSConfPtr[0].srvLen:7
TEST.END

-- Unit: ISOUDS_CntrlDTCSetting

-- Subprogram: ISOUDS_CntrlDTCSetting

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.DtcOnOff_1
TEST.UNIT:ISOUDS_CntrlDTCSetting
TEST.SUBPROGRAM:ISOUDS_CntrlDTCSetting
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.DtcOnOff_1
TEST.VALUE:ISOUDS_CntrlDTCSetting.ISOUDS_CntrlDTCSetting.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_CntrlDTCSetting.ISOUDS_CntrlDTCSetting.dataBuff[0]:0x1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.DtcOnOff_2
TEST.UNIT:ISOUDS_CntrlDTCSetting
TEST.SUBPROGRAM:ISOUDS_CntrlDTCSetting
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.DtcOnOff_2
TEST.NOTES:
set databuf = 2 to make dtcONoff 2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_CntrlDTCSetting.ISOUDS_CntrlDTCSetting.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_CntrlDTCSetting.ISOUDS_CntrlDTCSetting.dataBuff[0]:0x2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.default
TEST.UNIT:ISOUDS_CntrlDTCSetting
TEST.SUBPROGRAM:ISOUDS_CntrlDTCSetting
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.srvlen_1
TEST.UNIT:ISOUDS_CntrlDTCSetting
TEST.SUBPROGRAM:ISOUDS_CntrlDTCSetting
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_CntrlDTCSetting.srvlen_1
TEST.NOTES:
Set srvlen =3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_CntrlDTCSetting.ISOUDS_CntrlDTCSetting.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_CntrlDTCSetting.ISOUDS_CntrlDTCSetting.ISOUDSConfPtr[0].srvLen:1
TEST.END

-- Unit: ISOUDS_CommCntrl

-- Subprogram: ISOUDS_CommCntrl

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_CommCntrl.default
TEST.UNIT:ISOUDS_CommCntrl
TEST.SUBPROGRAM:ISOUDS_CommCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_CommCntrl.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_CommCntrl.default.databuff[0]=3
TEST.UNIT:ISOUDS_CommCntrl
TEST.SUBPROGRAM:ISOUDS_CommCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_CommCntrl.default.databuff[0]=3
TEST.NOTES:
set databuff[0] = 3 to make ctrltype =3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_CommCntrl.ISOUDS_CommCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_CommCntrl.ISOUDS_CommCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_CommCntrl.ISOUDS_CommCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_CommCntrl.ISOUDS_CommCntrl.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_CommCntrl.default.servlen_3
TEST.UNIT:ISOUDS_CommCntrl
TEST.SUBPROGRAM:ISOUDS_CommCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_CommCntrl.default.servlen_3
TEST.NOTES:
set srvlen=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_CommCntrl.ISOUDS_CommCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_CommCntrl.ISOUDS_CommCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.END

-- Unit: ISOUDS_ECUReset

-- Subprogram: ISOUDS_ECUReset

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ECUReset.PWMCtrl_bGetMtrState_1
TEST.UNIT:ISOUDS_ECUReset
TEST.SUBPROGRAM:ISOUDS_ECUReset
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ECUReset.PWMCtrl_bGetMtrState_1
TEST.NOTES:
set PWMCtrl_bGetMtrState =1
TEST.END_NOTES:
TEST.VALUE:uut_prototype_stubs.PWMCtrl_bGetMtrState.return:true
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ECUReset.databuff[0]_1
TEST.UNIT:ISOUDS_ECUReset
TEST.SUBPROGRAM:ISOUDS_ECUReset
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ECUReset.databuff[0]_1
TEST.NOTES:
set databuff[0]=1 to make rsttype =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.dataBuff[0]:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ECUReset.databuff[0]_3
TEST.UNIT:ISOUDS_ECUReset
TEST.SUBPROGRAM:ISOUDS_ECUReset
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ECUReset.databuff[0]_3
TEST.NOTES:
set databuff[0]=3 to make rsttype =3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ECUReset.default
TEST.UNIT:ISOUDS_ECUReset
TEST.SUBPROGRAM:ISOUDS_ECUReset
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ECUReset.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ECUReset.srvlen_2
TEST.UNIT:ISOUDS_ECUReset
TEST.SUBPROGRAM:ISOUDS_ECUReset
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ECUReset.srvlen_2
TEST.NOTES:
set srvlen =2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ECUReset.ISOUDS_ECUReset.ISOUDSConfPtr[0].srvLen:2
TEST.END

-- Unit: ISOUDS_IOCtrlByID

-- Subprogram: ISOUDS_IOCtrlByID

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_IOCtrlByID_UDS_lookUpTbl
TEST.UNIT:ISOUDS_IOCtrlByID
TEST.SUBPROGRAM:ISOUDS_IOCtrlByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_IOCtrlByID_UDS_lookUpTbl
TEST.NOTES:
set (boolean)1u == UDS_LookUpTbl(ioDID,PIDDID_IoCtrlbyID,((uint8)13),&idxTbl,((uint8)2)))) 
        {
TEST.END_NOTES:
TEST.STUB:ISOUDS.UDS_LookUpTbl
TEST.VALUE:ISOUDS.UDS_LookUpTbl.numSizeTbl:13
TEST.VALUE:ISOUDS.UDS_LookUpTbl.stAccessType:2
TEST.VALUE:ISOUDS.UDS_LookUpTbl.return:true
TEST.VALUE:ISOUDS_IOCtrlByID.ISOUDS_IOCtrlByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_IOCtrlByID.ISOUDS_IOCtrlByID.ISOUDSConfPtr[0].srvLen:6
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_IOCtrlByID_srvlen_4
TEST.UNIT:ISOUDS_IOCtrlByID
TEST.SUBPROGRAM:ISOUDS_IOCtrlByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_IOCtrlByID_srvlen_4
TEST.NOTES:
set srvlen < 5
TEST.END_NOTES:
TEST.VALUE:ISOUDS_IOCtrlByID.ISOUDS_IOCtrlByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_IOCtrlByID.ISOUDS_IOCtrlByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_IOCtrlByID_srvlen_6
TEST.UNIT:ISOUDS_IOCtrlByID
TEST.SUBPROGRAM:ISOUDS_IOCtrlByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_IOCtrlByID_srvlen_6
TEST.NOTES:
set srvlen > 5
TEST.END_NOTES:
TEST.VALUE:ISOUDS_IOCtrlByID.ISOUDS_IOCtrlByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_IOCtrlByID.ISOUDS_IOCtrlByID.ISOUDSConfPtr[0].srvLen:6
TEST.END

-- Unit: ISOUDS_LinkCntrl

-- Subprogram: ISOUDS_BaudrateCheck

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_default
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_default
TEST.NOTES:
Return val should be 0
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate1
TEST.NOTES:
set link_cntrltype =1 baudrate=1,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:1
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate10
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate10
TEST.NOTES:
set link_cntrltype =1 baudrate=10,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:10
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate11
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate11
TEST.NOTES:
set link_cntrltype =1 baudrate=11,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:11
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate12
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate12
TEST.NOTES:
set link_cntrltype =1 baudrate=12,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:12
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate13
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate13
TEST.NOTES:
set link_cntrltype =1 baudrate=13,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:13
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate2
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate2
TEST.NOTES:
set link_cntrltype =1 baudrate=2,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:2
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate3
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate3
TEST.NOTES:
set link_cntrltype =1 baudrate=3,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:3
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate4
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate4
TEST.NOTES:
set link_cntrltype =1 baudrate=4,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:4
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate5
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_baudrate5
TEST.NOTES:
set link_cntrltype =1 baudrate=5,Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.baudrateID:5
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_cntrltype1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_cntrltype1
TEST.NOTES:
set link_cntrltype =1 Return val should be 2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:1
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_cntrltype2
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_BaudrateCheck
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_BaudrateCheck_link_cntrltype2
TEST.NOTES:
set link_cntrltype =2 Return val should be 0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.link_cntrltype:2
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Subprogram: ISOUDS_ChangeBaudrate

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_ISOUDS_GetBaudrateReq1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_ChangeBaudrate
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_ISOUDS_GetBaudrateReq1
TEST.NOTES:
Set ISOUDS_GetBaudrateReq =1
TEST.END_NOTES:
TEST.STUB:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateReq
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateReq.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_ISOUDS_Sess
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_ChangeBaudrate
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_ISOUDS_Sess
TEST.NOTES:
Set ISOUDS_Sess =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_Sess:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_ISOUDS_Sess_ISOUDS_GetBaudrateSt1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_ChangeBaudrate
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_ISOUDS_Sess_ISOUDS_GetBaudrateSt1
TEST.NOTES:
Set ISOUDS_Sess =1 and ISOUDS_GetBaudrateSt =1
TEST.END_NOTES:
TEST.STUB:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateSt
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_Sess:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateSt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_default
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_ChangeBaudrate
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChangeBaudrate_default
TEST.END

-- Subprogram: ISOUDS_GetBaudrateReq

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetBaudrateReq.ISOUDS_ReqBaudChange0
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_GetBaudrateReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetBaudrateReq.ISOUDS_ReqBaudChange0
TEST.NOTES:
Set ISOUDS_ReqBaudChange =0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_ReqBaudChange:false
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateReq.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetBaudrateReq.ISOUDS_ReqBaudChange1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_GetBaudrateReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetBaudrateReq.ISOUDS_ReqBaudChange1
TEST.NOTES:
Set ISOUDS_ReqBaudChange =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_ReqBaudChange:true
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateReq.return:true
TEST.END

-- Subprogram: ISOUDS_GetBaudrateSt

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetBaudrateSt.ISOUDS_BaudrateChanged0
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_GetBaudrateSt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetBaudrateSt.ISOUDS_BaudrateChanged0
TEST.NOTES:
Set ISOUDS_BaudrateChanged =0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_BaudrateChanged:false
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateSt.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetBaudrateSt.ISOUDS_BaudrateChanged1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_GetBaudrateSt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetBaudrateSt.ISOUDS_BaudrateChanged1
TEST.NOTES:
Set ISOUDS_BaudrateChanged =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_BaudrateChanged:true
TEST.EXPECTED:ISOUDS_LinkCntrl.ISOUDS_GetBaudrateSt.return:true
TEST.END

-- Subprogram: ISOUDS_LinkCntrl

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_ISOUDS_BaudrateCheck_0
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_ISOUDS_BaudrateCheck_0
TEST.NOTES:
Set SOUDS_BaudrateCheck=0 ,databuff[0]=1 to make linkCntrlType=1,I
TEST.END_NOTES:
TEST.STUB:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_ISOUDS_BaudrateCheck_1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_ISOUDS_BaudrateCheck_1
TEST.NOTES:
Set ISOUDS_BaudrateCheck=1,databuff[0]=1 to make linkCntrlType=1
TEST.END_NOTES:
TEST.STUB:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_1
TEST.NOTES:
Set databuff[0]=1 to make linkCntrlType=1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_2
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_2
TEST.NOTES:
Set databuff[0]=2 to make linkCntrlType=2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_2_srvlen_5
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_2_srvlen_5
TEST.NOTES:
Set databuff[0]=2 to make linkCntrlType=2
set srvlen=5
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:5
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_2_srvlen_5_ISOUDS_BaudrateCheck_1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_2_srvlen_5_ISOUDS_BaudrateCheck_1
TEST.NOTES:
Set databuff[0]=2 to make linkCntrlType=2
set srvlen=5 ISOUDS_BaudrateCheck=1
TEST.END_NOTES:
TEST.STUB:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:5
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:2
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_BaudrateCheck.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_3
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_3
TEST.NOTES:
Set databuff[0]=3 to make linkCntrlType=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_3_ISOUDS_TransBaud_1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_3_ISOUDS_TransBaud_1
TEST.NOTES:
Set databuff[0]=3 to make linkCntrlType=3
ISOUDS_TransBaud=1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_TransBaud:1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_3_ISOUDS_TransBaud_2
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_databuff[0]_3_ISOUDS_TransBaud_2
TEST.NOTES:
Set databuff[0]=3 to make linkCntrlType=3
ISOUDS_TransBaud=2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_TransBaud:2
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_srvlen_0
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_srvlen_0
TEST.NOTES:
Set srvlen=0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_srvlen_3
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrl_srvlen_3
TEST.NOTES:
Set srvlen=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_LinkCntrl.ISOUDSConfPtr[0].srvLen:3
TEST.END

-- Subprogram: ISOUDS_LinkCntrlInit

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_LinkCntrlInit.default
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_LinkCntrlInit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_LinkCntrlInit.default
TEST.NOTES:
ISOUDS_TransBaud = (uint8)0;
    /* Hold Baudrate to which transition has to happen */
  ISOUDS_Baudrate = (uint32)0;
    /* Holds whether Baurate has changed or not */
  ISOUDS_BaudrateChanged = (boolean)0u;
    /* request for baudrate change */
  ISOUDS_ReqBaudChange = (boolean)0u;
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_TransBaud:0
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_Baudrate:0
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_BaudrateChanged:false
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_ReqBaudChange:false
TEST.END

-- Subprogram: ISOUDS_SetBaudrateReq

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SetBaudrateReq.ISOUDS_ReqBaudChange_0
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_SetBaudrateReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SetBaudrateReq.ISOUDS_ReqBaudChange_0
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_SetBaudrateReq.value:false
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_ReqBaudChange:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SetBaudrateReq.ISOUDS_ReqBaudChange_1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_SetBaudrateReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SetBaudrateReq.ISOUDS_ReqBaudChange_1
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_SetBaudrateReq.value:true
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_ReqBaudChange:true
TEST.END

-- Subprogram: ISOUDS_SetBaudrateSt

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SetBaudrateSt._ISOUDS_BaudrateChanged_0
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_SetBaudrateSt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SetBaudrateSt._ISOUDS_BaudrateChanged_0
TEST.NOTES:
 ISOUDS_BaudrateChanged =0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_SetBaudrateSt.value:false
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_BaudrateChanged:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SetBaudrateSt._ISOUDS_BaudrateChanged_1
TEST.UNIT:ISOUDS_LinkCntrl
TEST.SUBPROGRAM:ISOUDS_SetBaudrateSt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SetBaudrateSt._ISOUDS_BaudrateChanged_1
TEST.NOTES:
 ISOUDS_BaudrateChanged =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_LinkCntrl.ISOUDS_SetBaudrateSt.value:true
TEST.EXPECTED:ISOUDS_LinkCntrl.<<GLOBAL>>.ISOUDS_BaudrateChanged:true
TEST.END

-- Unit: ISOUDS_RdDTCInf

-- Subprogram: ISOUDS_RdDTCInf

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDTCInf.databuff[0]_2
TEST.UNIT:ISOUDS_RdDTCInf
TEST.SUBPROGRAM:ISOUDS_RdDTCInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDTCInf.databuff[0]_2
TEST.NOTES:
set srvlen=3,databuff[0]=2 to make rdDTCSubFun == (uint8)0x02)

TEST.END_NOTES:
TEST.VALUE:ISOUDS_RdDTCInf.ISOUDS_RdDTCInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdDTCInf.ISOUDS_RdDTCInf.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_RdDTCInf.ISOUDS_RdDTCInf.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_RdDTCInf.ISOUDS_RdDTCInf.dataBuff[0]:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDTCInf.default
TEST.UNIT:ISOUDS_RdDTCInf
TEST.SUBPROGRAM:ISOUDS_RdDTCInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDTCInf.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDTCInf.srvlen_3
TEST.UNIT:ISOUDS_RdDTCInf
TEST.SUBPROGRAM:ISOUDS_RdDTCInf
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDTCInf.srvlen_3
TEST.NOTES:
set srvlen=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_RdDTCInf.ISOUDS_RdDTCInf.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdDTCInf.ISOUDS_RdDTCInf.ISOUDSConfPtr[0].srvLen:3
TEST.END

-- Unit: ISOUDS_RdDaByID

-- Subprogram: ISOUDS_RdDaByID

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDaByID_default
TEST.UNIT:ISOUDS_RdDaByID
TEST.SUBPROGRAM:ISOUDS_RdDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDaByID_default
TEST.NOTES:
txPos = (uint16)0;
    /* Initialize the negative response to 0 (OK value) */
  resNRC = (uint8)(0x00U);
    /* Initialize the unlock status to FALSE (LOCKED) */
  unlock = (boolean)0u;
    /* Initialize the number of supported DIDs to 0 */
  nSupportDids = (uint16)0;
    /* Initialize the Table index to 0 */
  idxTbl = (uint16)0;
TEST.END_NOTES:
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_0
TEST.UNIT:ISOUDS_RdDaByID
TEST.SUBPROGRAM:ISOUDS_RdDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_0
TEST.NOTES:
txPos = (uint16)0;
    /* Initialize the negative response to 0 (OK value) */
  resNRC = (uint8)(0x00U);
    /* Initialize the unlock status to FALSE (LOCKED) */
  unlock = (boolean)0u;
    /* Initialize the number of supported DIDs to 0 */
  nSupportDids = (uint16)0;
    /* Initialize the Table index to 0 */
  idxTbl = (uint16)0;
TEST.END_NOTES:
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr[0].srvLen:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_3
TEST.UNIT:ISOUDS_RdDaByID
TEST.SUBPROGRAM:ISOUDS_RdDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_3
TEST.NOTES:
set srvlen=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr[0].srvLen:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_3_UDS_LookUpTbl_1
TEST.UNIT:ISOUDS_RdDaByID
TEST.SUBPROGRAM:ISOUDS_RdDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_3_UDS_LookUpTbl_1
TEST.NOTES:
set ISOUDS_RdConf.rdDid_funPtr != ((void (*) (uint16,uint8 rdData[],uint8 * retVal))0))&&
				(
      (boolean)1u == UDS_LookUpTbl(locDID,PIDDID_RdWrDID,((uint16)64),&idxTbl,((uint8)0)))) 
TEST.END_NOTES:
TEST.STUB:ISOUDS.UDS_LookUpTbl
TEST.VALUE:ISOUDS.UDS_LookUpTbl.return:true
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr[0].srvLen:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_4
TEST.UNIT:ISOUDS_RdDaByID
TEST.SUBPROGRAM:ISOUDS_RdDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdDaByID_srvlen_4
TEST.NOTES:
set srvlen=4
TEST.END_NOTES:
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdDaByID.ISOUDS_RdDaByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Unit: ISOUDS_RdMemByAddr

-- Subprogram: ISOUDS_RdMemByAddr

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.ISOUDS_GetSAStLevel_1
TEST.UNIT:ISOUDS_RdMemByAddr
TEST.SUBPROGRAM:ISOUDS_RdMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.ISOUDS_GetSAStLevel_1
TEST.NOTES:
set ISOUDS_GetSAStLevel=1
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSAStLevel
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSAStLevel.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.ISOUDS_GetSAStLevel_1.001
TEST.UNIT:ISOUDS_RdMemByAddr
TEST.SUBPROGRAM:ISOUDS_RdMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.ISOUDS_GetSAStLevel_1.001
TEST.NOTES:
set srvlen=1
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSAStLevel
TEST.VALUE:ISOUDS_RdMemByAddr.ISOUDS_RdMemByAddr.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdMemByAddr.ISOUDS_RdMemByAddr.ISOUDSConfPtr[0].srvLen:1
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSAStLevel.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.databuff[0]_6
TEST.UNIT:ISOUDS_RdMemByAddr
TEST.SUBPROGRAM:ISOUDS_RdMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.databuff[0]_6
TEST.NOTES:
set ISOUDS_GetSAStLevel=1
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSAStLevel
TEST.VALUE:ISOUDS_RdMemByAddr.ISOUDS_RdMemByAddr.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdMemByAddr.ISOUDS_RdMemByAddr.ISOUDSConfPtr[0].srvLen:8
TEST.VALUE:ISOUDS_RdMemByAddr.ISOUDS_RdMemByAddr.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_RdMemByAddr.ISOUDS_RdMemByAddr.dataBuff[0]:6
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSAStLevel.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.default
TEST.UNIT:ISOUDS_RdMemByAddr
TEST.SUBPROGRAM:ISOUDS_RdMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdMemByAddr.default
TEST.END

-- Unit: ISOUDS_RdScDaByID

-- Subprogram: ISOUDS_RdScDaByID

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.default
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3
TEST.NOTES:
set srvlen=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr[0].srvLen:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3_ISOUDS_GetSASt_1
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3_ISOUDS_GetSASt_1
TEST.NOTES:
set srvlen=3
(boolean)1u == ISOUDS_RdScLookUp (locDID))
u1 ISOUDS_GetSASt();
            }
    
TEST.END_NOTES:
TEST.STUB:ISOUDS_RdScDaByID.ISOUDS_RdScLookUp
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScLookUp.return:true
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3_ISOUDS_RdScLookUp_1
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3_ISOUDS_RdScLookUp_1
TEST.NOTES:
set srvlen=3
(boolean)1u == ISOUDS_RdScLookUp (locDID))
TEST.END_NOTES:
TEST.STUB:ISOUDS_RdScDaByID.ISOUDS_RdScLookUp
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScLookUp.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3_ISOUDS_RdScidData_1
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScDaByID.srvlen_3_ISOUDS_RdScidData_1
TEST.NOTES:
set srvlen=3
(boolean)1u == ISOUDS_RdScLookUp (locDID))
1 = ISOUDS_RdScidData 
TEST.END_NOTES:
TEST.STUB:ISOUDS_RdScDaByID.ISOUDS_RdScidData
TEST.STUB:ISOUDS_RdScDaByID.ISOUDS_RdScLookUp
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScDaByID.ISOUDSConfPtr[0].srvLen:3
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScidData.return:true
TEST.VALUE:ISOUDS_RdScDaByID.ISOUDS_RdScLookUp.return:true
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Subprogram: ISOUDS_RdScLookUp

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScLookUp.default
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScLookUp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScLookUp.default
TEST.END

-- Subprogram: ISOUDS_RdScidData

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScidData.default
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScidData
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScidData.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RdScidData.default.001
TEST.UNIT:ISOUDS_RdScDaByID
TEST.SUBPROGRAM:ISOUDS_RdScidData
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RdScidData.default.001
TEST.NOTES:
if (
    ISOUDS_RdScConfTab[ISOUDS_RdScTabIdx].rdScid_funPtr != ((uint8 (*) (uint8 srcBuff[], uint8 len))0))
        {
TEST.END_NOTES:
TEST.END

-- Unit: ISOUDS_ReqDwnld

-- Subprogram: ISOUDS_ReqDwnld

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.ISOUDS_GetSASt_1
TEST.UNIT:ISOUDS_ReqDwnld
TEST.SUBPROGRAM:ISOUDS_ReqDwnld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.ISOUDS_GetSASt_1
TEST.NOTES:
set (boolean)1u == ISOUDS_GetSASt())
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.default
TEST.UNIT:ISOUDS_ReqDwnld
TEST.SUBPROGRAM:ISOUDS_ReqDwnld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.srvlen_0
TEST.UNIT:ISOUDS_ReqDwnld
TEST.SUBPROGRAM:ISOUDS_ReqDwnld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.srvlen_0
TEST.NOTES:
set srvlen=0
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.ISOUDSConfPtr[0].srvLen:0
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.dataBuff:<<malloc 2>>
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.dataBuff[1]:6
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.srvlen_36
TEST.UNIT:ISOUDS_ReqDwnld
TEST.SUBPROGRAM:ISOUDS_ReqDwnld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.srvlen_36
TEST.NOTES:
set srvlen=36
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.ISOUDSConfPtr[0].srvLen:36
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.dataBuff:<<malloc 2>>
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.dataBuff[0]:0
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.dataBuff[1]:33
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.srvlen_9
TEST.UNIT:ISOUDS_ReqDwnld
TEST.SUBPROGRAM:ISOUDS_ReqDwnld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqDwnld.srvlen_9
TEST.NOTES:
set srvlen=6
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.ISOUDSConfPtr[0].srvLen:9
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.dataBuff:<<malloc 2>>
TEST.VALUE:ISOUDS_ReqDwnld.ISOUDS_ReqDwnld.dataBuff[1]:6
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Unit: ISOUDS_ReqUpld

-- Subprogram: ISOUDS_ReqUpld

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqUpld.ISOUDS_GetSASt_1
TEST.UNIT:ISOUDS_ReqUpld
TEST.SUBPROGRAM:ISOUDS_ReqUpld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqUpld.ISOUDS_GetSASt_1
TEST.NOTES:
(boolean)1u == ISOUDS_GetSASt()
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqUpld.ISOUDS_GetSASt_1.srvlen_2
TEST.UNIT:ISOUDS_ReqUpld
TEST.SUBPROGRAM:ISOUDS_ReqUpld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqUpld.ISOUDS_GetSASt_1.srvlen_2
TEST.NOTES:
(boolean)1u == ISOUDS_GetSASt()
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqUpld.databuff[1]_6
TEST.UNIT:ISOUDS_ReqUpld
TEST.SUBPROGRAM:ISOUDS_ReqUpld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqUpld.databuff[1]_6
TEST.NOTES:
(boolean)1u == ISOUDS_GetSASt()
databuff[1]=6,srvlen=9
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.ISOUDSConfPtr[0].srvLen:9
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.dataBuff:<<malloc 2>>
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.dataBuff[1]:6
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqUpld.default
TEST.UNIT:ISOUDS_ReqUpld
TEST.SUBPROGRAM:ISOUDS_ReqUpld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqUpld.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqUpld.srvlen_2
TEST.UNIT:ISOUDS_ReqUpld
TEST.SUBPROGRAM:ISOUDS_ReqUpld
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqUpld.srvlen_2
TEST.NOTES:
srvlen=2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_ReqUpld.ISOUDS_ReqUpld.ISOUDSConfPtr[0].srvLen:2
TEST.END

-- Unit: ISOUDS_RtnCntrl

-- Subprogram: ISOUDS_RtnCntrl

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RtnCntrl.default
TEST.UNIT:ISOUDS_RtnCntrl
TEST.SUBPROGRAM:ISOUDS_RtnCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RtnCntrl.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_RtnCntrl.srvlen_1
TEST.UNIT:ISOUDS_RtnCntrl
TEST.SUBPROGRAM:ISOUDS_RtnCntrl
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_RtnCntrl.srvlen_1
TEST.NOTES:
srvlen=5
TEST.END_NOTES:
TEST.VALUE:ISOUDS_RtnCntrl.ISOUDS_RtnCntrl.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_RtnCntrl.ISOUDS_RtnCntrl.ISOUDSConfPtr[0].srvLen:1
TEST.END

-- Unit: ISOUDS_SA

-- Subprogram: ISOUDS_GetSASt

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetSASt.ISOUDS_StECUUnLock_0
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_GetSASt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetSASt.ISOUDS_StECUUnLock_0
TEST.NOTES:
ISOUDS_StECUUnLock -0
TEST.END_NOTES:
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_StECUUnLock:false
TEST.EXPECTED:ISOUDS_SA.ISOUDS_GetSASt.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetSASt.ISOUDS_StECUUnLock_1
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_GetSASt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetSASt.ISOUDS_StECUUnLock_1
TEST.NOTES:
ISOUDS_StECUUnLock -1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_StECUUnLock:true
TEST.EXPECTED:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.END

-- Subprogram: ISOUDS_GetSAStLevel

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetSAStLevel.ISOUDS_StECUUnLock_1
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_GetSAStLevel
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetSAStLevel.ISOUDS_StECUUnLock_1
TEST.NOTES:
ISOUDS_StECUUnLock =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_StECUUnLock:true
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKey:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_GetSAStLevel.default
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_GetSAStLevel
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_GetSAStLevel.default
TEST.END

-- Subprogram: ISOUDS_SA

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SA.ISOUDS_SATimerAttCntExceededRunning_1
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SA
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SA.ISOUDS_SATimerAttCntExceededRunning_1
TEST.NOTES:
ISOUDS_SATimerAttCntExceededRunning == (uint8)1u)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerAttCntExceededRunning:true
TEST.VALUE:ISOUDS_SA.ISOUDS_SA.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_SA.ISOUDS_SA.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SA.dataBuff[0]_3
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SA
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SA.dataBuff[0]_3
TEST.NOTES:
dataBuff[0]=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_SA.ISOUDS_SA.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_SA.ISOUDS_SA.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SA.default
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SA
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SA.default
TEST.END

-- Subprogram: ISOUDS_SAChkTimer

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.ISOUDS_SATimerAttCntExceeded
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SAChkTimer
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.ISOUDS_SATimerAttCntExceeded
TEST.NOTES:
ISOUDS_SATimerAttCntExceeded > 0)
TEST.END_NOTES:
TEST.VALUE:uut_prototype_stubs.TimerHwAbs_GetElapsedTime.return:80001
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKey:1
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerAttCntExceeded:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.ISOUDS_SATimerWaitingForKey
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SAChkTimer
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.ISOUDS_SATimerWaitingForKey
TEST.NOTES:
 ISOUDS_SATimerWaitingForKey > 0)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKey:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.ISOUDS_Sess
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SAChkTimer
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.ISOUDS_Sess
TEST.NOTES:
 ISOUDS_OldSess != ISOUDS_Sess
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:1
TEST.VALUE:uut_prototype_stubs.TimerHwAbs_GetElapsedTime.return:80001
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKey:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.TimerHwAbs_GetElapsedTime
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SAChkTimer
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.TimerHwAbs_GetElapsedTime
TEST.NOTES:
TimerHwAbs_GetElapsedTime(ISOUDS_SATimerWaitingForKey) >= (80000))
TEST.END_NOTES:
TEST.VALUE:uut_prototype_stubs.TimerHwAbs_GetElapsedTime.return:80001
TEST.VALUE:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKey:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.default
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SAChkTimer
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SAChkTimer.default
TEST.END

-- Subprogram: ISOUDS_SAInit

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SAInit.default
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SAInit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SAInit.default
TEST.NOTES:
SOUDS_StECUUnLock = (uint8)0u;
    /* Is seed Static or to be generated each time when Unlock request comes is decided by this variable */
    /* TRUE = Same seed everytime for same level 
       FALSE = Random seed is generated every time */
  ISOUDS_SAStatic_Seed = (uint8)1u;
    /* Status to indicate which security Access level is currently there */
  ISOUDS_SALevel = 0x00;
    /* Attempt counts if Exceeds limit ECU is locked for some time and
    will not accept any request for unlocking */
  ISOUDS_SAAttCnt = 0x00;
    /* Timers */
    /* Security Access Delay timer for Recevie key 
    Under this time the key should be Received for send seed.otherwise it will be count as false attempt*/
  ISOUDS_SATimerWaitingForKey = 0x0000;
    /* Security Access delay timer to lock ECU if attempt count exceeds limit */
  ISOUDS_SATimerAttCntExceeded = 0x0000;
    /* Requested Current Security levels */
  ISOUDS_SACurrentRequestedLevel = 0x00;
    /* To start stop the timers */
  ISOUDS_SATimerWaitingForKeyRunning = (uint8)0u;
  ISOUDS_SATimerAttCntExceededRunning = (uint8)0u;
    /* security access Service State */
  ISOUDS_SAState = ((uint8)1);
    /* Variable to Check ISOUDS Session change */
  ISOUDS_OldSess = (0x01U);
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_StECUUnLock:false
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SAStatic_Seed:true
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SALevel:0
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SAAttCnt:0
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKey:0
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerAttCntExceeded:0
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKeyRunning:false
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerAttCntExceededRunning:false
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SAState:1
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_OldSess:1
TEST.END

-- Subprogram: ISOUDS_SAReset

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SAReset.default
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_SAReset
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SAReset.default
TEST.NOTES:
ISOUDS_SATimerWaitingForKeyRunning = (uint8)0u;
  ISOUDS_SATimerAttCntExceededRunning = (uint8)0u;
    /* Clear all counters */
  ISOUDS_SATimerWaitingForKey = 0;
  ISOUDS_SATimerAttCntExceeded = 0;
    /* Change state to Default */
  ISOUDS_SAState = ((uint8)1);
    /* lock the ECU */
  ISOUDS_StECUUnLock = (uint8)0u;
}
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_StECUUnLock:false
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKey:0
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerAttCntExceeded:0
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerWaitingForKeyRunning:false
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SATimerAttCntExceededRunning:false
TEST.EXPECTED:ISOUDS_SA.<<GLOBAL>>.ISOUDS_SAState:1
TEST.END

-- Subprogram: ISOUDS_Security_CalcKey

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_Security_CalcKey.default
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_Security_CalcKey
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_Security_CalcKey.default
TEST.NOTES:
temp = (((seed >> 1) ^ seed) << 3) ^ (seed >> 2);
  key = temp ^ 0x93;
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_SA.ISOUDS_Security_CalcKey.return:147
TEST.END

-- Subprogram: ISOUDS_Security_CalcKey_EOL

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_Security_CalcKey_EOL.default
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_Security_CalcKey_EOL
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_Security_CalcKey_EOL.default
TEST.VALUE:ISOUDS_SA.ISOUDS_Security_CalcKey_EOL.seed:1
TEST.EXPECTED:ISOUDS_SA.ISOUDS_Security_CalcKey_EOL.return:49
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_Security_CalcKey_EOL.seed
TEST.UNIT:ISOUDS_SA
TEST.SUBPROGRAM:ISOUDS_Security_CalcKey_EOL
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_Security_CalcKey_EOL.seed
TEST.VALUE:ISOUDS_SA.ISOUDS_Security_CalcKey_EOL.seed:4555
TEST.EXPECTED:ISOUDS_SA.ISOUDS_Security_CalcKey_EOL.return:52539
TEST.END

-- Unit: ISOUDS_StrtDiagSess

-- Subprogram: ISOUDS_StrtDiagSess

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_GetSASt
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_GetSASt
TEST.NOTES:
ked */
            if (
            (uint8)1u == ISOUDS_GetSASt
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_Sess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_Sess
TEST.NOTES:
ISOUDS_Sess == (uint8)(0x01U)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:1
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtDfltSess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtDfltSess
TEST.NOTES:
stSessSwt = ISOUDS_SwtDfltSess ();
TEST.END_NOTES:
TEST.STUB:ISOUDS_StrtDiagSess.ISOUDS_SwtDfltSess
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:1
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_SwtDfltSess.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtEolSess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtEolSess
TEST.NOTES:
stSessSwt = ISOUDS_SwtEolSess ();
TEST.END_NOTES:
TEST.STUB:ISOUDS_StrtDiagSess.ISOUDS_SwtEolSess
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:225
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_SwtEolSess.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtExtDiagSess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtExtDiagSess
TEST.NOTES:
stSessSwt = ISOUDS_SwtExtDiagSess ();
TEST.END_NOTES:
TEST.STUB:ISOUDS_StrtDiagSess.ISOUDS_SwtExtDiagSess
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:3
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_SwtExtDiagSess.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtProgSess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.ISOUDS_SwtProgSess
TEST.NOTES:
 stSessSwt = ISOUDS_SwtProgSess ();
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.STUB:ISOUDS_StrtDiagSess.ISOUDS_SwtProgSess
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_SwtProgSess.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_1
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_1
TEST.NOTES:
databuff[0]=1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_2
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_2
TEST.NOTES:
databuff[0]=2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_225
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_225
TEST.NOTES:
databuff[0]=225
TEST.END_NOTES:
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:225
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_3
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.databuff[0]_3
TEST.NOTES:
databuff[0]=3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.dataBuff[0]:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.default
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.srvlen_2
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_StrtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_StrtDiagSess.srvlen_2
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_StrtDiagSess.ISOUDS_StrtDiagSess.ISOUDSConfPtr[0].srvLen:2
TEST.END

-- Subprogram: ISOUDS_SwtDfltSess

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtDfltSess.ISOUDS_Sess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtDfltSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtDfltSess.ISOUDS_Sess
TEST.NOTES:
ISOUDS_Sess == (uint8)(0x01U
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:1
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtDfltSess.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtDfltSess.default
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtDfltSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtDfltSess.default
TEST.NOTES:
Return 1
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtDfltSess.return:true
TEST.END

-- Subprogram: ISOUDS_SwtEolSess

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtEolSess.ISOUDS_Sess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtEolSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtEolSess.ISOUDS_Sess
TEST.NOTES:
ISOUDS_Sess == (uint8)(0x61U)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:0x61
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtEolSess.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtEolSess.default
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtEolSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtEolSess.default
TEST.NOTES:
Return 1
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtEolSess.return:true
TEST.END

-- Subprogram: ISOUDS_SwtExtDiagSess

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtExtDiagSess.ISOUDS_Sess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtExtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtExtDiagSess.ISOUDS_Sess
TEST.NOTES:
ISOUDS_Sess == (uint8)(0x03U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:0x3
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtExtDiagSess.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtExtDiagSess.default
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtExtDiagSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtExtDiagSess.default
TEST.NOTES:
return 1
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtExtDiagSess.return:true
TEST.END

-- Subprogram: ISOUDS_SwtProgSess

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtProgSess.ISOUDS_Sess
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtProgSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtProgSess.ISOUDS_Sess
TEST.NOTES:
ISOUDS_Sess == (uint8)(0x02U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:2
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtProgSess.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_SwtProgSess.default
TEST.UNIT:ISOUDS_StrtDiagSess
TEST.SUBPROGRAM:ISOUDS_SwtProgSess
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_SwtProgSess.default
TEST.NOTES:
return 1
TEST.END_NOTES:
TEST.EXPECTED:ISOUDS_StrtDiagSess.ISOUDS_SwtProgSess.return:true
TEST.END

-- Unit: ISOUDS_TrnsfrDa

-- Subprogram: ISOUDS_ChkTrnsfrExit

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.ISOUDS_DwBlkSeqCntr_1
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ChkTrnsfrExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.ISOUDS_DwBlkSeqCntr_1
TEST.NOTES:
ISOUDS_DwBlkSeqCntr=1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_DwBlkSeqCntr:1
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ChkTrnsfrExit.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.ISOUDS_TrnsfrDaSt_1
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ChkTrnsfrExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.ISOUDS_TrnsfrDaSt_1
TEST.NOTES:
ISOUDS_TrnsfrDaSt=1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:1
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ChkTrnsfrExit.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.ISOUDS_UpBlkSeqCntr_1
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ChkTrnsfrExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.ISOUDS_UpBlkSeqCntr_1
TEST.NOTES:
ISOUDS_UpBlkSeqCntr =1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_UpBlkSeqCntr:1
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ChkTrnsfrExit.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.default
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ChkTrnsfrExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ChkTrnsfrExit.default
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ChkTrnsfrExit.return:false
TEST.END

-- Subprogram: ISOUDS_ReqTrnsfrDa

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_2
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_2
TEST.NOTES:
ISOUDS_TrnsfrDaSt == (uint8)2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:2
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_3
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_3
TEST.NOTES:
ISOUDS_TrnsfrDaSt == (uint8)3
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:3
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_5
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_5
TEST.NOTES:
ISOUDS_TrnsfrDaSt == (uint8)5
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:5
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_6
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_6
TEST.NOTES:
ISOUDS_TrnsfrDaSt == (uint8)6
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:6
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:false
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_7
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.ISOUDS_TrnsfrDaSt_7
TEST.NOTES:
ISOUDS_TrnsfrDaSt == (uint8)7
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:7
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.default
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.default
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.servreq_1
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.servreq_1
TEST.NOTES:
(uint8)1 == servreq)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.servreq:1
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.servreq_4
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_ReqTrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_ReqTrnsfrDa.servreq_4
TEST.NOTES:
(uint8)4 == servreq)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.servreq:4
TEST.EXPECTED:ISOUDS_TrnsfrDa.ISOUDS_ReqTrnsfrDa.return:true
TEST.END

-- Subprogram: ISOUDS_TrnsfrDa

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDaSt_1
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_TrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDaSt_1
TEST.NOTES:
ISOUDS_TrnsfrDaSt=1
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDaSt_2
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_TrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDaSt_2
TEST.NOTES:
ISOUDS_TrnsfrDaSt=2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDaSt_3
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_TrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDaSt_3
TEST.NOTES:
ISOUDS_TrnsfrDaSt=2
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:3
TEST.ATTRIBUTES:ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDa.ISOUDSConfPtr[0].srvNegResp:INPUT_BASE=16
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.default
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_TrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.default
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.srvlen_0
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_TrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.srvlen_0
TEST.NOTES:
ISOUDSConfPtr->srvLen < (uint16)(1))
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDa.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDa.ISOUDSConfPtr[0].srvLen:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.srvst_5
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_TrnsfrDa
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrDa.srvst_5
TEST.NOTES:
ISOUDS_TrnsfrDaSt=3,srvst=5
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TrnsfrDa.<<GLOBAL>>.ISOUDS_TrnsfrDaSt:3
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDa.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDa.dataBuff:<<malloc 2>>
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_TrnsfrDa.dataBuff:"5"
TEST.END

-- Subprogram: ISOUDS_TrnsfrDaExit

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrDaExit.001
TEST.UNIT:ISOUDS_TrnsfrDa
TEST.SUBPROGRAM:ISOUDS_TrnsfrDaExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrDaExit.001
TEST.END

-- Unit: ISOUDS_TrnsfrExit

-- Subprogram: ISOUDS_TrnsfrExit

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrExit.ISOUDS_ChkTrnsfrExit
TEST.UNIT:ISOUDS_TrnsfrExit
TEST.SUBPROGRAM:ISOUDS_TrnsfrExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrExit.ISOUDS_ChkTrnsfrExit
TEST.NOTES:
ISOUDSConfPtr->srvLen == (uint16)(1)
(boolean)1u == ISOUDS_ChkTrnsfrExit()
TEST.END_NOTES:
TEST.STUB:ISOUDS_TrnsfrDa.ISOUDS_ChkTrnsfrExit
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.VALUE:ISOUDS_TrnsfrDa.ISOUDS_ChkTrnsfrExit.return:true
TEST.VALUE:ISOUDS_TrnsfrExit.ISOUDS_TrnsfrExit.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_TrnsfrExit.ISOUDS_TrnsfrExit.ISOUDSConfPtr[0].srvLen:1
TEST.VALUE:LINTP.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.EXPECTED:LINTP.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrExit.default
TEST.UNIT:ISOUDS_TrnsfrExit
TEST.SUBPROGRAM:ISOUDS_TrnsfrExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrExit.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TrnsfrExit.srvLen_1
TEST.UNIT:ISOUDS_TrnsfrExit
TEST.SUBPROGRAM:ISOUDS_TrnsfrExit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TrnsfrExit.srvLen_1
TEST.NOTES:
ISOUDSConfPtr->srvLen == (uint16)(1)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.VALUE:ISOUDS_TrnsfrExit.ISOUDS_TrnsfrExit.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_TrnsfrExit.ISOUDS_TrnsfrExit.ISOUDSConfPtr[0].srvLen:1
TEST.VALUE:LINTP.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.EXPECTED:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.EXPECTED:LINTP.<<GLOBAL>>.ISOUDS_Conf.srvLen:1
TEST.END

-- Unit: ISOUDS_TstrPrsnt

-- Subprogram: ISOUDS_TstrPrsnt

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.databuff[0]_0
TEST.UNIT:ISOUDS_TstrPrsnt
TEST.SUBPROGRAM:ISOUDS_TstrPrsnt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.databuff[0]_0
TEST.NOTES:
ISOUDSConfPtr->srvLen == (uint16)(2))->srvLen == (uint16)(2))

 subFunc == (uint8)0x00)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.dataBuff[0]:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.databuff[0]_80
TEST.UNIT:ISOUDS_TstrPrsnt
TEST.SUBPROGRAM:ISOUDS_TstrPrsnt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.databuff[0]_80
TEST.NOTES:
ISOUDSConfPtr->srvLen == (uint16)(2))->srvLen == (uint16)(2))
subFunc == (uint8)0x80)
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.ISOUDSConfPtr[0].srvLen:2
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.dataBuff[0]:0x80
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.default
TEST.UNIT:ISOUDS_TstrPrsnt
TEST.SUBPROGRAM:ISOUDS_TstrPrsnt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.srvlen
TEST.UNIT:ISOUDS_TstrPrsnt
TEST.SUBPROGRAM:ISOUDS_TstrPrsnt
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_TstrPrsnt.srvlen
TEST.NOTES:
ISOUDSConfPtr->srvLen == (uint16)(2))->srvLen == (uint16)(2))
TEST.END_NOTES:
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_TstrPrsnt.ISOUDS_TstrPrsnt.ISOUDSConfPtr[0].srvLen:2
TEST.END

-- Unit: ISOUDS_WrDaByID

-- Subprogram: ISOUDS_WrDaByID

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_1
TEST.UNIT:ISOUDS_WrDaByID
TEST.SUBPROGRAM:ISOUDS_WrDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_1
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))

ISOUDSConfPtr->srvLen >= (uint16)((uint8)((uint8)(0x01U)+(uint8)(0x02U)+1)))


                (
      (boolean)1u == UDS_LookUpTbl(ISOUDS_RecvWrDid,PIDDID_RdWrDID,((uint16)64),&idxTbl,((uint8)1)))) 

c =1
            {
TEST.END_NOTES:
TEST.STUB:ISOUDS.UDS_LookUpTbl
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:1
TEST.VALUE:ISOUDS.UDS_LookUpTbl.return:true
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_2
TEST.UNIT:ISOUDS_WrDaByID
TEST.SUBPROGRAM:ISOUDS_WrDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_2
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))

ISOUDSConfPtr->srvLen >= (uint16)((uint8)((uint8)(0x01U)+(uint8)(0x02U)+1)))


                (
      (boolean)1u == UDS_LookUpTbl(ISOUDS_RecvWrDid,PIDDID_RdWrDID,((uint16)64),&idxTbl,((uint8)1)))) 

c =2
            {
TEST.END_NOTES:
TEST.STUB:ISOUDS.UDS_LookUpTbl
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:2
TEST.VALUE:ISOUDS.UDS_LookUpTbl.return:true
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_3
TEST.UNIT:ISOUDS_WrDaByID
TEST.SUBPROGRAM:ISOUDS_WrDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_3
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))

ISOUDSConfPtr->srvLen >= (uint16)((uint8)((uint8)(0x01U)+(uint8)(0x02U)+1)))


                (
      (boolean)1u == UDS_LookUpTbl(ISOUDS_RecvWrDid,PIDDID_RdWrDID,((uint16)64),&idxTbl,((uint8)1)))) 

c =3
            {
TEST.END_NOTES:
TEST.STUB:ISOUDS.UDS_LookUpTbl
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:3
TEST.VALUE:ISOUDS.UDS_LookUpTbl.return:true
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_61
TEST.UNIT:ISOUDS_WrDaByID
TEST.SUBPROGRAM:ISOUDS_WrDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrDaByID.ISOUDS_Sess_61
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))

ISOUDSConfPtr->srvLen >= (uint16)((uint8)((uint8)(0x01U)+(uint8)(0x02U)+1)))


                (
      (boolean)1u == UDS_LookUpTbl(ISOUDS_RecvWrDid,PIDDID_RdWrDID,((uint16)64),&idxTbl,((uint8)1)))) 

c =61
            {
TEST.END_NOTES:
TEST.STUB:ISOUDS.UDS_LookUpTbl
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Sess:0x61
TEST.VALUE:ISOUDS.UDS_LookUpTbl.return:true
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrDaByID.UDS_LookUpTbl
TEST.UNIT:ISOUDS_WrDaByID
TEST.SUBPROGRAM:ISOUDS_WrDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrDaByID.UDS_LookUpTbl
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))

ISOUDSConfPtr->srvLen >= (uint16)((uint8)((uint8)(0x01U)+(uint8)(0x02U)+1)))

if ((
      ISOUDS_WrConf.wrDid_funPtr != ((uint8 (*) (uint16,const uint8 wrData[],uint8))0))&&
                (
      (boolean)1u == UDS_LookUpTbl(ISOUDS_RecvWrDid,PIDDID_RdWrDID,((uint16)64),&idxTbl,((uint8)1)))) 
            {
TEST.END_NOTES:
TEST.STUB:ISOUDS.UDS_LookUpTbl
TEST.VALUE:ISOUDS.UDS_LookUpTbl.return:true
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrDaByID.srvlen
TEST.UNIT:ISOUDS_WrDaByID
TEST.SUBPROGRAM:ISOUDS_WrDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrDaByID.srvlen
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))

ISOUDSConfPtr->srvLen >= (uint16)((uint8)((uint8)(0x01U)+(uint8)(0x02U)+1)))
TEST.END_NOTES:
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvLen:4
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrDaByID.srvst_2
TEST.UNIT:ISOUDS_WrDaByID
TEST.SUBPROGRAM:ISOUDS_WrDaByID
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrDaByID.srvst_2
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrDaByID.ISOUDS_WrDaByID.ISOUDSConfPtr[0].srvSt:2
TEST.END

-- Unit: ISOUDS_WrMemByAddr

-- Subprogram: ISOUDS_WrMemByAddr

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.ISOUDS_GetSASt
TEST.UNIT:ISOUDS_WrMemByAddr
TEST.SUBPROGRAM:ISOUDS_WrMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.ISOUDS_GetSASt
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))
(boolean)1u == ISOUDS_GetSASt())
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr[0].srvSt:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.dataBuff[0]_33
TEST.UNIT:ISOUDS_WrMemByAddr
TEST.SUBPROGRAM:ISOUDS_WrMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.dataBuff[0]_33
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))
(boolean)1u == ISOUDS_GetSASt())
\ dataBuff[0] == (uint8)0x33)
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.dataBuff[0]:0x33
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.dataBuff[0]_33.001
TEST.UNIT:ISOUDS_WrMemByAddr
TEST.SUBPROGRAM:ISOUDS_WrMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.dataBuff[0]_33.001
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))
(boolean)1u == ISOUDS_GetSASt())
\ dataBuff[0] == (uint8)0x33)

 ISOUDSConfPtr->srvLen == (uint16)temp)
TEST.END_NOTES:
TEST.STUB:ISOUDS_SA.ISOUDS_GetSASt
TEST.VALUE:ISOUDS_SA.ISOUDS_GetSASt.return:true
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr[0].srvSt:2
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr[0].srvLen:0x33
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.dataBuff:<<malloc 1>>
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.dataBuff[0]:0x33
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.default
TEST.UNIT:ISOUDS_WrMemByAddr
TEST.SUBPROGRAM:ISOUDS_WrMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.srvst_2
TEST.UNIT:ISOUDS_WrMemByAddr
TEST.SUBPROGRAM:ISOUDS_WrMemByAddr
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_ISOUDS_WrMemByAddr.srvst_2
TEST.NOTES:
ISOUDSConfPtr->srvSt == (uint8)(0x02U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr:<<malloc 1>>
TEST.VALUE:ISOUDS_WrMemByAddr.ISOUDS_WrMemByAddr.ISOUDSConfPtr[0].srvSt:2
TEST.END

-- Unit: LINTP

-- Subprogram: LINTP

-- Test Case: DIAGCOMMAN_MAN_LINTP.default
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP.default
TEST.END

-- Subprogram: LINTP_DiagMastReq

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.Lin_iTpTmrCr_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.Lin_iTpTmrCr_1
TEST.NOTES:
arrLin[2U] == (uint8)0xB0U)
(uint8)0x7FU == arrLin[0U])
Lin_iTpTmrCr > ((uint32)0)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[0]:127
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:176
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_127
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_127
TEST.NOTES:
(uint8)0x7FU == arrLin[0U]))
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[0]:127
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:255
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_127!
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_127!
TEST.NOTES:
arrLin[2U] == (uint8)0xB0U)
(uint8)0x7FU == arrLin[0U])
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[0]:127
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:176
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_127.arrlin[1]_6
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_127.arrlin[1]_6
TEST.NOTES:
(uint8)0x7FU == arrLin[0U]))
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[0]:127
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[1]:15
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:255
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_3
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[0]_3
TEST.NOTES:
arrLin[2U] == (uint8)0xB0U)
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[0]:3
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:176
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[1]_240
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[1]_240
TEST.NOTES:
arrLin[2U] == (uint8)0xB0U)
(uint8)0x7FU == arrLin[0U])
(uint8)0x00 == (pci & (uint8)(uint8)0xF0U))
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[0]:127
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[1]:240
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:176
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[1]_240.001
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[1]_240.001
TEST.NOTES:
arrLin[2U] == (uint8)0xB0U)
(uint8)0x7FU == arrLin[0U])
(uint8)0x00 == (pci & (uint8)(uint8)0xF0U))
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq_Srv:1
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[0]:127
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[1]:10
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:176
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_176
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_176
TEST.NOTES:
arrLin[2U] == (uint8)0xB0U)
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:176
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_176.001
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_176.001
TEST.NOTES:
arrLin[2U] == (uint8)0xB0U)
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_255
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_255
TEST.NOTES:
arrLin[2U] <= (uint8)0xB7U) 
TEST.END_NOTES:
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:255
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_255_srvst_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.arrLin[2]_255_srvst_1
TEST.NOTES:
arrLin[2U] <= (uint8)0xB7U) 
 ISOUDS_Conf . srvSt == (0x01U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:1
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin:<<malloc 3>>
TEST.VALUE:LINTP.LINTP_DiagMastReq.arrLin[2]:255
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.default
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastReq.default.001
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastReq
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastReq.default.001
TEST.NOTES:
(uint8)0x7FU == arrLin[0U])) && 
TEST.END_NOTES:
TEST.END

-- Subprogram: LINTP_DiagMastResp

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpNodeNotResp_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpNodeNotResp_1
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
Lin_iTpRespReq_Srv == (uint8)0x01) 
Lin_iTpNodeNotResp == (boolean)1u))
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq_Srv:1
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.Lin_iTpNodeNotResp:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpNodeRespReq
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpNodeRespReq
TEST.NOTES:
Lin_iTpNodeRespReq == (boolean)1u)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq:false
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpNodeRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpRespReq
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpRespReq
TEST.NOTES:
Lin_iTpRespReq == (boolean)1u)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpRespReq_Srv_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpRespReq_Srv_1
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
Lin_iTpRespReq_Srv == (uint8)0x01) 
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq_Srv:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpState
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpState
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
Lin_iTpState == (uint8)0x00)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpTmrP2LimMax
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpTmrP2LimMax
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x001U))


 timer > Lin_iTpTmrP2LimMax)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:1
TEST.VALUE:uut_prototype_stubs.TimerHwAbs_GetElapsedTime.return:2000000
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrP2LimMax:200
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpTmrP2LimMin
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpTmrP2LimMin
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x001U))
     
          timer = TimerHwAbs_GetElapsedTime(Lin_iTpTmrP2);
      
          timer < Lin_iTpTmrP2LimMin)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrP2LimMin:200
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpUDSRespReq
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpUDSRespReq
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpUDSRespReq_0
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.Lin_iTpUDSRespReq_0
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
Lin_iTpRespReq_Srv == (uint8)0x01) 
Lin_iTpUDSRespReq =! (boolean)1u
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:false
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq_Srv:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.default
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.functbl[1]_0
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.functbl[1]_0
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x004U))
((void *)0) != Lin_Uds_Srv_FuncTbl[1])
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.s
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.s
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x004U))
((void *)0) != Lin_Uds_Srv_FuncTbl[0U])
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x001U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_3
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_3
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x03U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:3
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_3.((void_*)0)_!=_Lin_Uds_Srv_FuncTbl[1])
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_3.((void_*)0)_!=_Lin_Uds_Srv_FuncTbl[1])
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x03U))
((void *)0) != Lin_Uds_Srv_FuncTbl[0U])
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:3
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:2
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_3.srvlen_0
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_3.srvlen_0
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x03U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:3
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:2
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_4
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_4
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x004U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_5
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvSt_5
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x03U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:5
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_65535
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_65535
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x001U))
length <= (uint16)(uint16)0x0FFFU)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:1
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:65535
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))
ISOUDS_Conf . srvSt == (0x001U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:1
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:9
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_3
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_3
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x03U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:3
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:9
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_4
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_4
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x04U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:9
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_4.functbl[0]_0
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_4.functbl[0]_0
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x04U))
((void *)0) != Lin_Uds_Srv_FuncTbl[0U])
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:9
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_5
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_DiagMastResp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_DiagMastResp.srvlen_9.srvst_5
TEST.NOTES:
Lin_iTpUDSRespReq == (boolean)1u))

ISOUDS_Conf . srvSt == (0x05U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:5
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvLen:9
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpUDSRespReq:true
TEST.END

-- Subprogram: LINTP_Init

-- Test Case: DIAGCOMMAN_MAN_LINTP_Init.001
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Init
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Init.001
TEST.END

-- Subprogram: LINTP_InitTask

-- Test Case: DIAGCOMMAN_MAN_LINTP_InitTask.001
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_InitTask
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_InitTask.001
TEST.END

-- Subprogram: LINTP_Mon

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.ISOUDS_Conf_srvst_3
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.ISOUDS_Conf_srvst_3
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
((void *)0) != Lin_Uds_Srv_FuncTbl[1U])
 Lin_iTpRespReq == (boolean)1u)


ISOUDS_Conf . srvSt == (0x03U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:3
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq:false
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:UDS_SrvPTask
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.ISOUDS_Conf_srvst_4
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.ISOUDS_Conf_srvst_4
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
((void *)0) != Lin_Uds_Srv_FuncTbl[1U])
 Lin_iTpRespReq == (boolean)1u)


ISOUDS_Conf . srvSt == (0x04U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq:false
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:UDS_SrvPTask
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl[0]_0
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl[0]_0
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
((void *)0) == Lin_Uds_Srv_FuncTbl[0U])
 Lin_iTpRespReq == (boolean)1u)


ISOUDS_Conf . srvSt == (0x04U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq:false
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:UDS_SrvPTask
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl[1]_0
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl[1]_0
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
((void *)0) == Lin_Uds_Srv_FuncTbl[0U])
((void *)0) == Lin_Uds_Srv_FuncTbl[1U])
 Lin_iTpRespReq == (boolean)1u)


ISOUDS_Conf . srvSt == (0x04U))
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:4
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq:false
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl[1]_4
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_Uds_Srv_FuncTbl[1]_4
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
((void *)0) != Lin_Uds_Srv_FuncTbl[1U])
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:UDS_SrvPTask
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpRespReq
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpRespReq
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
((void *)0) != Lin_Uds_Srv_FuncTbl[1U])
 Lin_iTpRespReq == (boolean)1u)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq:true
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:UDS_SrvPTask
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpRespReq_Srv
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpRespReq_Srv
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpRespReq_Srv =! (uint8)0x00)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq_Srv:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrAsLim
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrAsLim
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
TimerHwAbs_GetElapsedTime(Lin_iTpTmrAs) < Lin_iTpTmrAsLim)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAsLim:200
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrAs_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrAs_1
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrCr
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrCr
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrCrLim
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.Lin_iTpTmrCrLim
TEST.NOTES:
Lin_iTpTmrAs > ((uint32)0))
Lin_Uds_Srv_FuncTbl
Lin_iTpTmrCr > ((uint32)0))
TimerHwAbs_GetElapsedTime(Lin_iTpTmrCr) < Lin_iTpTmrCrLim)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrAs:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCr:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrCrLim:200
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.TimerHwAbs_GetElapsedTime
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.TimerHwAbs_GetElapsedTime
TEST.NOTES:
Lin_iTpTmrP2 > ((uint32)0))
timer = TimerHwAbs_GetElapsedTime(Lin_iTpTmrP2);
         
      timer > Lin_iTpTmrP2LimMax)
TEST.END_NOTES:
TEST.VALUE:uut_prototype_stubs.TimerHwAbs_GetElapsedTime.return:200
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrP2:2
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrP2LimMax:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.TimerHwAbs_GetElapsedTime.srvst3
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.TimerHwAbs_GetElapsedTime.srvst3
TEST.NOTES:
Lin_iTpTmrP2 > ((uint32)0))
timer = TimerHwAbs_GetElapsedTime(Lin_iTpTmrP2);
         
      timer > Lin_iTpTmrP2LimMax)
ISOUDS_Conf . srvSt == (0x03U)
TEST.END_NOTES:
TEST.VALUE:ISOUDS.<<GLOBAL>>.ISOUDS_Conf.srvSt:0x3
TEST.VALUE:uut_prototype_stubs.TimerHwAbs_GetElapsedTime.return:600000000
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrP2:2
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrP2LimMax:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.dLin_iTpTmrP2_2
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.dLin_iTpTmrP2_2
TEST.NOTES:
Lin_iTpTmrP2 > ((uint32)0))
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpTmrP2:2
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Mon.default
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_Mon
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Mon.default
TEST.END

-- Subprogram: LINTP_TxConfCbk

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.ISOUDS_GetECUResetReq_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.ISOUDS_GetECUResetReq_1
TEST.NOTES:
Lin_iTpState == (uint8)0x05)
((void *)0) != Lin_Uds_Srv_FuncTbl[1])

 ISOUDS_GetECUResetReq() == (uint8)1 
TEST.END_NOTES:
TEST.STUB:ISOUDS.ISOUDS_GetECUResetReq
TEST.VALUE:ISOUDS.ISOUDS_GetECUResetReq.return:1
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:5
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.ISOUDS_GetECUResetReq_3
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.ISOUDS_GetECUResetReq_3
TEST.NOTES:
Lin_iTpState == (uint8)0x05)
((void *)0) != Lin_Uds_Srv_FuncTbl[1])

 ISOUDS_GetECUResetReq() == (uint8)3 
TEST.END_NOTES:
TEST.STUB:ISOUDS.ISOUDS_GetECUResetReq
TEST.VALUE:ISOUDS.ISOUDS_GetECUResetReq.return:3
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:5
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_Uds_Srv_FuncTbl
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_Uds_Srv_FuncTbl
TEST.NOTES:
Lin_iTpState == (uint8)0x05)
((void *)0) != Lin_Uds_Srv_FuncTbl[1])
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:5
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[0]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_Uds_Srv_FuncTbl[2]
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_Uds_Srv_FuncTbl[2]
TEST.NOTES:
Lin_iTpState == (uint8)0x05)
((void *)0) != Lin_Uds_Srv_FuncTbl[1])

 ISOUDS_GetECUResetReq() == (uint8)3 
((void *)0) != Lin_Uds_Srv_FuncTbl[2])
TEST.END_NOTES:
TEST.STUB:ISOUDS.ISOUDS_GetECUResetReq
TEST.VALUE:ISOUDS.ISOUDS_GetECUResetReq.return:3
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:5
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[1]:<<null>>
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_Uds_Srv_FuncTbl[2]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpLenTx_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpLenTx_1
TEST.NOTES:
Lin_iTpState == (uint8)0x05)

Lin_iTpLenTx =! (uint8)0x00)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:5
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpLenTx:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpRespReq_Srv_1
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpRespReq_Srv_1
TEST.NOTES:
Lin_iTpState == (uint8)0x05)
 Lin_iTpRespReq_Srv == (uint8)0x00)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:5
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpRespReq_Srv:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpState_3
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpState_3
TEST.NOTES:
Lin_iTpState == (uint8)0x03)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:3
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpState_5
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.Lin_iTpState_5
TEST.NOTES:
Lin_iTpState == (uint8)0x05)
TEST.END_NOTES:
TEST.VALUE:LINTP.<<GLOBAL>>.Lin_iTpState:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_TxConfCbk.default
TEST.UNIT:LINTP
TEST.SUBPROGRAM:LINTP_TxConfCbk
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_TxConfCbk.default
TEST.END

-- Unit: LINTP_NM

-- Subprogram: LINTP_NM_GetNAD

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_GetNAD.maxvalue
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_GetNAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_GetNAD.maxvalue
TEST.NOTES:
return(Lin_Srv_Get_NAD()
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:255
TEST.EXPECTED:LINTP_NM.LINTP_NM_GetNAD.return:255
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_GetNAD.minvalue
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_GetNAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_GetNAD.minvalue
TEST.NOTES:
return(Lin_Srv_Get_NAD()
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:1
TEST.EXPECTED:LINTP_NM.LINTP_NM_GetNAD.return:1
TEST.END

-- Subprogram: LINTP_NM_Init

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_Init.default
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_Init
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_Init.default
TEST.NOTES:
LINNM_LinOpMode =  0x01U;
  LINTP_NM_RstTimeout();
  LINTP_isEmergencyMode=0u;
  LINTP_isGotoSleep=0u;
  LINTP_ComErrorCnt = 0;
  LINTP_EmergencyTimeCount = 0;
TEST.END_NOTES:
TEST.STUB:LINTP_NM.LINTP_NM_RstTimeout
TEST.EXPECTED:LINTP_NM.<<GLOBAL>>.LINNM_LinOpMode:1
TEST.EXPECTED:LINTP_NM.<<GLOBAL>>.LINTP_ComErrorCnt:0
TEST.EXPECTED:LINTP_NM.<<GLOBAL>>.LINTP_isGotoSleep:false
TEST.EXPECTED:LINTP_NM.<<GLOBAL>>.LINTP_isEmergencyMode:false
TEST.END

-- Subprogram: LINTP_NM_RstTimeout

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_RstTimeout.LINTP_InactiveTimeCount
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_RstTimeout
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_RstTimeout.LINTP_InactiveTimeCount
TEST.NOTES:
LINTP_InactiveTimeCount = 0x00U;
TEST.END_NOTES:
TEST.EXPECTED:LINTP_NM.<<GLOBAL>>.LINTP_InactiveTimeCount:0
TEST.END

-- Subprogram: LINTP_NM_Task

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_Task.LINNM_LinOpMode
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_Task
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_Task.LINNM_LinOpMode
TEST.NOTES:
LINNM_LinOpMode == (uint8)0x01U)
TEST.END_NOTES:
TEST.VALUE:LINTP_NM.<<GLOBAL>>.LINNM_LinOpMode:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_Task.LINTP_GetCommError
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_Task
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_Task.LINTP_GetCommError
TEST.NOTES:
LINNM_LinOpMode == (uint8)0x01U)

errStatusRtn = LINTP_GetCommError
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.LINTP_GetCommError
TEST.VALUE:LINTP_NM.<<GLOBAL>>.LINNM_LinOpMode:1
TEST.VALUE:LINTP_Srv.LINTP_GetCommError.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_Task.LINTP_GetCommError_0
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_Task
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_Task.LINTP_GetCommError_0
TEST.NOTES:
LINNM_LinOpMode == (uint8)0x01U)

errStatusRtn = LINTP_GetCommError
LINTP_InactiveTimeCount >= (4000u/10))
LINTP_EmergencyTimeCount >= (600000u/10)6 0000
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.LINTP_GetCommError
TEST.VALUE:LINTP_NM.<<GLOBAL>>.LINNM_LinOpMode:1
TEST.VALUE:LINTP_NM.<<GLOBAL>>.LINTP_InactiveTimeCount:400
TEST.VALUE:LINTP_NM.<<GLOBAL>>.LINTP_EmergencyTimeCount:60000
TEST.VALUE:LINTP_Srv.LINTP_GetCommError.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_Task.LINTP_InactiveTimeCount
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_Task
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_Task.LINTP_InactiveTimeCount
TEST.NOTES:
LINNM_LinOpMode == (uint8)0x01U)

errStatusRtn = LINTP_GetCommError
LINTP_InactiveTimeCount >= (4000u/10))
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.LINTP_GetCommError
TEST.VALUE:LINTP_NM.<<GLOBAL>>.LINNM_LinOpMode:1
TEST.VALUE:LINTP_NM.<<GLOBAL>>.LINTP_InactiveTimeCount:400
TEST.VALUE:LINTP_Srv.LINTP_GetCommError.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_NM_Task.default
TEST.UNIT:LINTP_NM
TEST.SUBPROGRAM:LINTP_NM_Task
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_NM_Task.default
TEST.END

-- Unit: LINTP_NodeSrv

-- Subprogram: LIN_Assign_FrameId

-- Test Case: DIAGCOMMAN_MAN_LIN_Assign_FrameId.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Assign_FrameId
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Assign_FrameId.default
TEST.END

-- Subprogram: LIN_Assign_NAD

-- Test Case: DIAGCOMMAN_MAN_LIN_Assign_NAD.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Assign_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Assign_NAD.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Assign_NAD.default.Function_id_0
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Assign_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Assign_NAD.default.Function_id_0
TEST.NOTES:
srvLen == (uint8)(0x05
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.Supplier_id:0
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.Function_id:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff:<<malloc 4>>
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff[0]:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff[1]:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff[2]:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff[3]:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Assign_NAD.default.Supplier_id_0
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Assign_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Assign_NAD.default.Supplier_id_0
TEST.NOTES:
srvLen == (uint8)(0x05
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.Supplier_id:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff:<<malloc 2>>
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff[0]:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.dataBuff[1]:0
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Assign_NAD.default.srvlen_5
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Assign_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Assign_NAD.default.srvlen_5
TEST.NOTES:
srvLen == (uint8)(0x05
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_Assign_NAD.srvLen:5
TEST.END

-- Subprogram: LIN_ConChange_NAD

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.Lin_Srv_Get_NAD_0
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.Lin_Srv_Get_NAD_0
TEST.NOTES:
srvLen == (uint8)(0x05U)
NewNAD != Lin_Srv_Get_NAD(
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 5>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[4]:0
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.Lin_Srv_Get_NAD_1
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.Lin_Srv_Get_NAD_1
TEST.NOTES:
srvLen == (uint8)(0x05U)
NewNAD != Lin_Srv_Get_NAD(
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 5>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[4]:0
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1
TEST.NOTES:
srvLen == (uint8)(0x05U)
databuff[0]=1
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 1>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[0]:1
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_1
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_1
TEST.NOTES:
srvLen == (uint8)(0x05U)
databuff[0]=1
databuff[1]=1
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 2>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[0]:1
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:1
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_2
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_2
TEST.NOTES:
srvLen == (uint8)(0x05U)
databuff[0]=1
databuff[1]=2
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 2>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[0]:1
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:2
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_3
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_3
TEST.NOTES:
srvLen == (uint8)(0x05U)
databuff[0]=1
databuff[1]=3
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 2>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[0]:1
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:3
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_4
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[0]_1_databuff[1]_4
TEST.NOTES:
srvLen == (uint8)(0x05U)
databuff[0]=1
databuff[1]=4
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 2>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[0]:1
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:4
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_1
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_1
TEST.NOTES:
srvLen == (uint8)(0x05U)
NewNAD != Lin_Srv_Get_NAD
databuff[1]=1
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 5>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:1
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[4]:0
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_2
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_2
TEST.NOTES:
srvLen == (uint8)(0x05U)
NewNAD != Lin_Srv_Get_NAD
databuff[1]=2
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 5>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:2
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[4]:0
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_3
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_3
TEST.NOTES:
srvLen == (uint8)(0x05U)
NewNAD != Lin_Srv_Get_NAD
databuff[1]=3
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 5>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:3
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[4]:0
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_4
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.databuff[1]_4
TEST.NOTES:
srvLen == (uint8)(0x05U)
NewNAD != Lin_Srv_Get_NAD
databuff[1]=4
TEST.END_NOTES:
TEST.STUB:LINTP_Srv.Lin_Srv_Get_NAD
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff:<<malloc 5>>
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[1]:4
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.dataBuff[4]:0
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.VALUE:LINTP_Srv.Lin_Srv_Get_NAD.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_ConChange_NAD.srvlen_5
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_ConChange_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_ConChange_NAD.srvlen_5
TEST.NOTES:
srvLen == (uint8)(0x05U)
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_ConChange_NAD.srvLen:5
TEST.END

-- Subprogram: LIN_Data_Dump

-- Test Case: DIAGCOMMAN_MAN_LIN_Data_Dump.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Data_Dump
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Data_Dump.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Data_Dump.srvlen_5
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Data_Dump
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Data_Dump.srvlen_5
TEST.NOTES:
srvLen == (uint8)(uint8)(0x05))
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_Data_Dump.srvLen:5
TEST.END

-- Subprogram: LIN_Node_Serv_Init

-- Test Case: DIAGCOMMAN_MAN_LIN_Node_Serv_Init.maxvalue
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Node_Serv_Init
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Node_Serv_Init.maxvalue
TEST.NOTES:
 Supplier_id = (uint16)u16LinSupplierId;
  Function_id = (uint16)u16LinFuncId;
  Serial_no = (uint32)u32LinSerialNum;
  varient = (uint8)u8LinVariantNum;
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u16LinSupplierId:65535
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u16LinFuncId:65535
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u32LinSerialNum:4294967295
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u8LinVariantNum:255
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.Supplier_id:65535
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.Function_id:65535
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.Serial_no:4294967295
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.varient:255
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Node_Serv_Init.minvalue
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Node_Serv_Init
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Node_Serv_Init.minvalue
TEST.NOTES:
 Supplier_id = (uint16)u16LinSupplierId;
  Function_id = (uint16)u16LinFuncId;
  Serial_no = (uint32)u32LinSerialNum;
  varient = (uint8)u8LinVariantNum;
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u16LinSupplierId:1
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u16LinFuncId:1
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u32LinSerialNum:1
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.u8LinVariantNum:1
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.Supplier_id:1
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.Function_id:1
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.Serial_no:1
TEST.EXPECTED:LINTP_NodeSrv.<<GLOBAL>>.varient:1
TEST.END

-- Subprogram: LIN_Node_Serv_Req

-- Test Case: DIAGCOMMAN_MAN_LIN_Node_Serv_Req.databuf[0]_0
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Node_Serv_Req
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Node_Serv_Req.databuf[0]_0
TEST.NOTES:
data_buf[0U] == LIN_SrvMsgTab[idx].Sid)
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_Node_Serv_Req.data_buf:<<malloc 1>>
TEST.VALUE:LINTP_NodeSrv.LIN_Node_Serv_Req.data_buf[0]:0
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Node_Serv_Req.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Node_Serv_Req
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Node_Serv_Req.default
TEST.END

-- Subprogram: LIN_Node_Serv_Resp

-- Test Case: DIAGCOMMAN_MAN_LIN_Node_Serv_Resp.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Node_Serv_Resp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Node_Serv_Resp.default
TEST.END

-- Subprogram: LIN_RdArrData

-- Test Case: DIAGCOMMAN_MAN_LIN_RdArrData.LIN_RdLookUp_!
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_RdArrData
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_RdArrData.LIN_RdLookUp_!
TEST.NOTES:
(boolean)1u == LIN_RdLookUp((uint16)identifier)
TEST.END_NOTES:
TEST.STUB:LINTP_NodeSrv.LIN_RdLookUp
TEST.VALUE:LINTP_NodeSrv.LIN_RdLookUp.return:true
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_RdArrData.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_RdArrData
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_RdArrData.default
TEST.END

-- Subprogram: LIN_RdLookUp

-- Test Case: DIAGCOMMAN_MAN_LIN_RdLookUp.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_RdLookUp
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_RdLookUp.default
TEST.END

-- Subprogram: LIN_Rd_Data_By_Id

-- Test Case: DIAGCOMMAN_MAN_LIN_Rd_Data_By_Id.001
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Rd_Data_By_Id
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Rd_Data_By_Id.001
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Rd_Data_By_Id.srvlen_5
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Rd_Data_By_Id
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Rd_Data_By_Id.srvlen_5
TEST.NOTES:
srvLen == (uint16)(uint8)(0x05)
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_Rd_Data_By_Id.srvLen:5
TEST.END

-- Subprogram: LIN_Save_Cfg

-- Test Case: DIAGCOMMAN_MAN_LIN_Save_Cfg.ConfigRecv_1
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Save_Cfg
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Save_Cfg.ConfigRecv_1
TEST.NOTES:
srvLen == (uint8)(uint8)(0x05)
ConfigRecv == (boolean)1u)
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.<<GLOBAL>>.ConfigRecv:1
TEST.VALUE:LINTP_NodeSrv.LIN_Save_Cfg.srvLen:5
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Save_Cfg.default
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Save_Cfg
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Save_Cfg.default
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LIN_Save_Cfg.srvLen_5
TEST.UNIT:LINTP_NodeSrv
TEST.SUBPROGRAM:LIN_Save_Cfg
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LIN_Save_Cfg.srvLen_5
TEST.NOTES:
srvLen == (uint8)(uint8)(0x05)
TEST.END_NOTES:
TEST.VALUE:LINTP_NodeSrv.LIN_Save_Cfg.srvLen:5
TEST.END

-- Unit: LINTP_Srv

-- Subprogram: DIAG_COMMAND_RX_Callback

-- Test Case: DIAGCOMMAN_MAN_DIAG_COMMAND_RX_Callback.Lin_TpDiag_FuncTbl_Req
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:DIAG_COMMAND_RX_Callback
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_DIAG_COMMAND_RX_Callback.Lin_TpDiag_FuncTbl_Req
TEST.NOTES:
set Lin_TpDiag_FuncTbl_Req=0
TEST.END_NOTES:
TEST.VALUE:LINTP_Srv.<<GLOBAL>>.Lin_TpDiag_FuncTbl_Req[0]:<<null>>
TEST.END

-- Test Case: DIAGCOMMAN_MAN_DIAG_COMMAND_RX_Callback.default
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:DIAG_COMMAND_RX_Callback
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_DIAG_COMMAND_RX_Callback.default
TEST.END

-- Subprogram: DIAG_RESPONSE_TX_Callback

-- Test Case: DIAGCOMMAN_MAN_DIAG_RESPONSE_TX_Callback.default
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:DIAG_RESPONSE_TX_Callback
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_DIAG_RESPONSE_TX_Callback.default
TEST.END

-- Subprogram: LINTP_CopyData

-- Test Case: DIAGCOMMAN_MAN_LINTP_CopyData.default
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_CopyData
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_CopyData.default
TEST.END

-- Subprogram: LINTP_GetCommError

-- Test Case: DIAGCOMMAN_MAN_LINTP_GetCommError.LINCommError_1
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_GetCommError
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_GetCommError.LINCommError_1
TEST.NOTES:
 return LINCommError;
TEST.END_NOTES:
TEST.VALUE:LINTP_Srv.<<GLOBAL>>.LINCommError:1
TEST.EXPECTED:LINTP_Srv.LINTP_GetCommError.return:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_GetCommError.LINCommError_255
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_GetCommError
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_GetCommError.LINCommError_255
TEST.NOTES:
 return LINCommError;
TEST.END_NOTES:
TEST.VALUE:LINTP_Srv.<<GLOBAL>>.LINCommError:255
TEST.EXPECTED:LINTP_Srv.LINTP_GetCommError.return:255
TEST.END

-- Subprogram: LINTP_SetCommError

-- Test Case: DIAGCOMMAN_MAN_LINTP_SetCommError.CommError_1
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_SetCommError
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_SetCommError.CommError_1
TEST.NOTES:
LINCommError = CommError;
TEST.END_NOTES:
TEST.VALUE:LINTP_Srv.LINTP_SetCommError.CommError:1
TEST.EXPECTED:LINTP_Srv.<<GLOBAL>>.LINCommError:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_SetCommError.CommError_255
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_SetCommError
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_SetCommError.CommError_255
TEST.NOTES:
LINCommError = CommError;
TEST.END_NOTES:
TEST.VALUE:LINTP_Srv.LINTP_SetCommError.CommError:255
TEST.EXPECTED:LINTP_Srv.<<GLOBAL>>.LINCommError:255
TEST.END

-- Subprogram: LINTP_SrvInit

-- Test Case: DIAGCOMMAN_MAN_LINTP_SrvInit.default
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_SrvInit
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_SrvInit.default
TEST.END

-- Subprogram: LINTP_Srv_Set_NAD

-- Test Case: DIAGCOMMAN_MAN_LINTP_Srv_Set_NAD.LIN_NAD_1
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_Srv_Set_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Srv_Set_NAD.LIN_NAD_1
TEST.NOTES:
Lin_Global_Srv_NAD = LIN_NAD;
TEST.END_NOTES:
TEST.VALUE:LINTP_Srv.LINTP_Srv_Set_NAD.LIN_NAD:1
TEST.EXPECTED:LINTP_Srv.<<GLOBAL>>.Lin_Global_Srv_NAD:1
TEST.END

-- Test Case: DIAGCOMMAN_MAN_LINTP_Srv_Set_NAD.LIN_NAD_255
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:LINTP_Srv_Set_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_LINTP_Srv_Set_NAD.LIN_NAD_255
TEST.NOTES:
Lin_Global_Srv_NAD = LIN_NAD;
TEST.END_NOTES:
TEST.VALUE:LINTP_Srv.LINTP_Srv_Set_NAD.LIN_NAD:255
TEST.EXPECTED:LINTP_Srv.<<GLOBAL>>.Lin_Global_Srv_NAD:255
TEST.END

-- Subprogram: Lin_Srv_Get_NAD

-- Test Case: DIAGCOMMAN_MAN_Lin_Srv_Get_NAD.default
TEST.UNIT:LINTP_Srv
TEST.SUBPROGRAM:Lin_Srv_Get_NAD
TEST.NEW
TEST.NAME:DIAGCOMMAN_MAN_Lin_Srv_Get_NAD.default
TEST.NOTES:
Lin_Global_Srv_NAD = LIN_NAD;
TEST.END_NOTES:
TEST.EXPECTED:LINTP_Srv.<<GLOBAL>>.Lin_Global_Srv_NAD:0
TEST.END
