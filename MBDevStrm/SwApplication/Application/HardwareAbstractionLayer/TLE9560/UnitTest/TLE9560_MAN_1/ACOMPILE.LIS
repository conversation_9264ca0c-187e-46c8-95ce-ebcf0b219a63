******************
***Compiling File: ********.c
   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 60'082 bytes of CODE  memory
     86 bytes of CONST memory
    327 bytes of DATA  memory
Errors: none
Warnings: none
******************
***Compiling File: ********.c
   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 12'420 bytes of CODE  memory
  1'082 bytes of CONST memory
    494 bytes of DATA  memory
Errors: none
Warnings: none
******************
***Compiling File: ********.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 9'482 bytes of CODE  memory
   315 bytes of CONST memory
   115 bytes of DATA  memory

Errors: none
Warnings: 6

    int  idx;
         ^
"C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN_1\********.c",1074  Warning[Pe177]: 
          variable "idx" was declared but never referenced

    int  cmd_len;
         ^
"C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN_1\********.c",1075  Warning[Pe550]: 
          variable "cmd_len" was set but never used

    int item_len;
        ^
"C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN_1\********.c",1078  Warning[Pe177]: 
          variable "item_len" was declared but never referenced

     int     i;
             ^
"C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN_1\********.c",2001  Warning[Pe177]: 
          variable "i" was declared but never referenced

     int     HC_INDEX;
             ^
"C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN_1\********.c",2057  Warning[Pe550]: 
          variable "HC_INDEX" was set but never used

     int     PARAM_INDEX;
             ^
"C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN_1\********.c",2060  Warning[Pe550]: 
          variable "PARAM_INDEX" was set but never used
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 188 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 588 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 456 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 424 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 424 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 416 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 420 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 392 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 372 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 432 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 368 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 588 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 456 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 424 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 424 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 416 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 448 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 420 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 440 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 360 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 392 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 356 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 444 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 352 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 364 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 372 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 432 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 368 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
******************
***Compiling File: vcast_stdin_data.c

   IAR ANSI C/C++ Compiler V9.50.1.380/W64 for ARM
   Copyright 1999-2023 IAR Systems AB.
   Mobile license - IAR Embedded Workbench for Arm, Cortex-M edition 9.60
 
 408 bytes of CONST memory

Errors: none
Warnings: none
