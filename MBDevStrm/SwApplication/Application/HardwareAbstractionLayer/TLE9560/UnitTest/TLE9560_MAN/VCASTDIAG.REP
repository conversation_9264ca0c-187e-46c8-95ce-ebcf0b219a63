
###################################
############# MISC ################
###################################
Tool version: 22 (03/17/22)
Current directory: c:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN
Current directory is writable.

--------------------------------------------------------------------------------
Environment Overview
--------------------------------------------------------------------------------
  Environment Name:    TLE9560_MAN
  Environment Status:  Normal
  Environment Version: REVISION_2021_ANON_STRUCT_CTOR_SUPPORT
  Environment Build Type: Full
  Coverage Status:     Statement+MC/DC (enabled)
  Language:            C
  Compiler Name:       IAR_8.x_ARM-Cortex Sim_C
  White Box:           Yes
  Search List:         $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\TLE9560\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\Common\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\TimerHWAbstr\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\TaskSchedulerLayer\TaskSch\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\DiagComMan\UnitTest  (testable)
                       $(WORKSPACE_ROOT)\SwIntegration\00_BuildSetup\StartupFile\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\ADC\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CANPAL\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CLOCK\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CRC\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CRC\Model  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\ERM\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\FLASH\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\FTMIC\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\FTMPWM\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\IM\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LIN\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LPIT\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LPSPI\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LPTMR\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\PDB\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\PINS\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\SCUInit\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ADCMon\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\CANMan\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\CRCMan\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ClockMan\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\DIOCtrl\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ERMHWAbstr\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ExtDev\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ICMon\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\PWMCtrl\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\WDOGMan\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\NVMMan\Code  (testable)
                       $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\LINMiddleware\Code  (testable)
  Units Under Test:    TLE9560
                       TLE9560_ApplLayer
                       TLE9560_FuncLayer
  Stub List:           uut_prototype_stubs
  Stub None (Source Files): None
###################################

###################################
### COMPILER/PREPROCESSOR/LINKER ##
###################################
Found preprocessor C:\Program Files\IAR Systems\Embedded Workbench 9.2\arm\bin\iccarm.exe
Found compiler C:\Program Files\IAR Systems\Embedded Workbench 9.2\arm\bin\iccarm.exe
###################################



###################################
############ ENV. VARS ############
###################################
ALLUSERSPROFILE=C:\ProgramData
APPDATA=C:\Users\<USER>\AppData\Roaming
CLIENTNAME=VDI-EU-TRD-051
COMPUTERNAME=W10-EUNL-EE-04
ComSpec=C:\Windows\system32\cmd.exe
CommonProgramFiles=C:\Program Files\Common Files
CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
CommonProgramW6432=C:\Program Files\Common Files
DriverData=C:\Windows\System32\Drivers\DriverData
FLEXLM_NO_CKOUT_INSTALL_LIC=1
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
HOMEDRIVE=C:
HOMEPATH=\Users\kaleks
LM_APP_DISABLE_CACHE_READ=1
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\SRV-EUNL-DC-06
NUMBER_OF_PROCESSORS=8
OS=Windows_NT
OneDrive=C:\Users\<USER>\OneDrive - inalfa.com
OneDriveCommercial=C:\Users\<USER>\OneDrive - inalfa.com
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 143 Stepping 8, GenuineIntel
PROCESSOR_LEVEL=6
PROCESSOR_REVISION=8f08
PROMPT=$P$G
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\Program Files\IAR Systems\Embedded Workbench 9.2\arm\..\common\bin;C:\Program Files\IAR Systems\Embedded Workbench 9.2\arm\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Python310\Scripts\;C:\Program Files\Python310\;C:\Program Files\Scripts\;C:\Program Files\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\Pulse Secure\VC142.CRT\X64\;C:\Program Files (x86)\Pulse Secure\VC142.CRT\X86\;C:\Program Files (x86)\Common Files\Pulse Secure\TNC Client Plugin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
ProgramData=C:\ProgramData
ProgramFiles=C:\Program Files
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
SESSIONNAME=RDP-Tcp#0
SystemDrive=C:
SystemRoot=C:\Windows
TEMP=C:\Users\<USER>\AppData\Local\Temp\2
TMP=C:\Users\<USER>\AppData\Local\Temp\2
USERDNSDOMAIN=EU.INALFA.LOCAL
USERDOMAIN=EU
USERDOMAIN_ROAMINGPROFILE=EU
USERNAME=kaleks
USERPROFILE=C:\Users\<USER>\Program Files\IAR Systems\Embedded Workbench 9.2\arm
VCAST_OBJECT_FILES=********.o ********.o ********.o ********.o ********.o  ********.o  vcast_stdin_data.o  B1_switch.o  B4_switch.o  S3_switch.o  ********.o  ********.o   ********.o
VCAST_OBJECT_PATH=c:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\UnitTest\\TLE9560_MAN
VCAST_PROG_STARTED_FROM_GUI=true
VCAST_QIK_ASSUME_SRC_HAS_NOT_CHANGED=true
VCAST_SUPPORT_FILES=C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\support_files
VCV_ENVIRONMENT_DIR=c:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN
VCV_ENVIRONMENT_NAME=TLE9560_MAN
VECTORCAST_DIR=C:\VCAST
VECTORCAST_SUB_PROCESS_VERBOSE=TRUE
VECTOR_LICENSE_FILE=@W10-EUNL-EE-04
VSHM_PREFIX=2BD8EC4BDC9840D0A116441CBF0CDFF1
WORKSPACE_ROOT=C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\..\..\..\..\..
WS_DIR=C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\..\..\..\..\..
Z3_LIBRARY_PATH=C:/VCAST/python/3rd_party/z3
_VC_AT_FILE_DETAILS_=@
_VC_SOURCE_EXTENSIONS_=.C .CC .CPP .CU .c .c++ .cc .cp .cpp .cu .cxx .tcc
windir=C:\Windows

###################################


###################################
######## MISSING BINARIES #########
###################################
none
###################################


###################################
########## CONFIG FILES ###########
###################################
CCAST Options read from file:
(exists) c:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\CCAST_.CFG (current dir)
Contents of c:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\CCAST_.CFG:
  C_ALT_COMPILE_CMD: 
  C_ALT_EDG_FLAGS: 
  C_ALT_PREPROCESS_CMD: 
  C_COMPILER_CFG_SOURCE: PY_CONFIGURATOR
  C_COMPILER_FAMILY_NAME: IAR
  C_COMPILER_HIERARCHY_STRING: IAR_8.x_ARM-Cortex Sim_C
  C_COMPILER_OUTPUT_FLAG: -o^
  C_COMPILER_PY_ARGS: --lang c --io stdout --target sim --toolsuite arm --version 8.x --cpu Cortex-M0+ --icfFile '$(VCAST_IAR_INSTALL_DIR)\config\generic.icf' --libConfig '$(VCAST_IAR_INSTALL_DIR)\inc\c\DLib_Config_Full.h'
  C_COMPILER_TAG: IAR_ARM-CORTEX_8X_SIM_C
  C_COMPILER_VERSION_CMD: iccarm --version
  C_COMPILE_CMD: iccarm --debug -e --cpu=Cortex-M0+ --dlib_config "$(VCAST_IAR_INSTALL_DIR)\inc\c\DLib_Config_Full.h" --fpu=none
  C_COMPILE_CMD_FLAG: 
  C_DEBUG_CMD: iaridepm --DBG ARM "$(VCAST_IAR_INSTALL_DIR)\bin\armsim2.dll" $(VCV_EXECUTABLE_NAME) --plugin "$(VCAST_IAR_INSTALL_DIR)\bin\armLibsupportUniversal.dll" --backend -d sim --cpu=Cortex-M0+
  C_DEBUG_HELP_FILE: $(VECTORCAST_DIR)\Compilers\IAR\DebugHelp.txt
  C_DEFINE_LIST: strong_fptr=fake_strong_fptr CPU_S32K118 UNIT_TEST
  C_EDG_FLAGS: -w --c --iar --iar_version=arm --stdarg_builtin --restrict --define_macro=__data= --define_macro=va_list=__Va_list --define_macro=va_start=vc_start --define_macro=va_end=vc_end
  C_EXECUTE_CMD: cspybat "$(VCAST_IAR_INSTALL_DIR)\bin\armproc.dll" "$(VCAST_IAR_INSTALL_DIR)\bin\armsim2.dll" $(VCV_EXECUTABLE_NAME) --plugin "$(VCAST_IAR_INSTALL_DIR)\bin\armLibsupportUniversal.dll" --backend -B -d sim --cpu=Cortex-M0+
  C_LINKER_VERSION_CMD: ilinkarm --version
  C_LINK_CMD: ilinkarm
  C_LINK_OPTIONS: --config "$(VCAST_IAR_INSTALL_DIR)\config\generic.icf" --map "vcast.map" --semihosting --entry __iar_program_start
  C_OUTPUT_FLAG: -o^
  C_PREPROCESS_CMD: iccarm --cpu=Cortex-M0+ -e --preprocess=nlc .
  C_PREPROCESS_FILE: ?.i
  EXECUTABLE_EXTENSION: .out
  SOURCE_EXTENSION: .c
  VCAST_COLLAPSE_STD_HEADERS: COLLAPSE_NONE
  VCAST_COMPILER_SUPPORTS_CPP_CASTS: FALSE
  VCAST_COVERAGE_FOR_HEADERS: FALSE
  VCAST_COVERAGE_SOURCE_FILE_PERSPECTIVE: FALSE
  VCAST_DISABLE_CPP_EXCEPTIONS: FALSE
  VCAST_DISABLE_STD_STRING_DETECTION: FALSE
  VCAST_DISABLE_STD_WSTRING_DETECTION: FALSE
  VCAST_DISPLAY_UNINST_EXPR: FALSE
  VCAST_ENVIRONMENT_FILES: 
  VCAST_EXECUTE_WITH_STDOUT: TRUE
  VCAST_HAS_LONGLONG: TRUE
  VCAST_MAX_STRING_LENGTH: 130
  VCAST_NO_SIGNAL: TRUE
  VCAST_NO_STDIN: TRUE
  VCAST_PREPROCESS_PREINCLUDE: $(VECTORCAST_DIR)/DATA/iar/arm/vcast_intrinsics.h
  VCAST_REMOVE_PREPROCESSOR_COMMENTS: FALSE
  VCAST_SHOW_INLINES_COVERED_IN_ALL_UNITS: TRUE
  VCAST_TEST_VALUES_DICTIONARY: 
  VCAST_UNEXPECTED_SIGNALS_FAIL: TRUE
  VCDB_CMD_VERB: 
  VCDB_FILENAME: 
  WHITEBOX: YES
  BASE_DIRECTORY: WORKSPACE_ROOT=$(WS_DIR)
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\DiagComMan\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\Common\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\TimerHWAbstr\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\TaskSchedulerLayer\TaskSch\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\DiagComMan\UnitTest
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwIntegration\00_BuildSetup\StartupFile\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\ADC\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\CANPAL\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\CLOCK\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\CRC\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\CRC\Model
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\ERM\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\FLASH\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\FTMIC\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\FTMPWM\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\IM\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\LIN\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\LPIT\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\LPSPI\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\LPTMR\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\PDB\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\DriverLayer\PINS\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\SCUInit\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\ADCMon\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\CANMan\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\CRCMan\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\ClockMan\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\DIOCtrl\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\ERMHWAbstr\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\ExtDev\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\ICMon\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\PWMCtrl\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\WDOGMan\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\NVMMan\Code
  TESTABLE_SOURCE_DIR: $(WS_DIR)\SwApplication\Application\HardwareAbstractionLayer\LINMiddleware\Code

ADACAST Options read from file:
( none ) c:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\ADACAST_.CFG (current dir)

###################################


###################################
######## ENV. VAR. CONFIG #########
###################################
These config options are being set by environment vars:
none

These env. vars contain VCAST and could be affecting behavior:
VCAST_IAR_INSTALL_DIR=C:\Program Files\IAR Systems\Embedded Workbench 9.2\arm
VCAST_OBJECT_FILES=********.o ********.o ********.o ********.o ********.o  ********.o  vcast_stdin_data.o  B1_switch.o  B4_switch.o  S3_switch.o  ********.o  ********.o   ********.o
VCAST_OBJECT_PATH=c:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\UnitTest\\TLE9560_MAN
VCAST_PROG_STARTED_FROM_GUI=true
VCAST_QIK_ASSUME_SRC_HAS_NOT_CHANGED=true
VCAST_SUPPORT_FILES=C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\support_files
VECTORCAST_DIR=C:\VCAST
Z3_LIBRARY_PATH=C:/VCAST/python/3rd_party/z3

###################################

###################################
####### ENVIRONMENT SCRIPT ########
###################################
Contents of C:\Users\<USER>\MBVan\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\MBDevStrm\SwApplication\Application\HardwareAbstractionLayer\TLE9560\UnitTest\TLE9560_MAN.env:
  ENVIRO.NEW
  ENVIRO.NAME: TLE9560_MAN
  ENVIRO.BASE_DIRECTORY: WORKSPACE_ROOT=$(WS_DIR)
  ENVIRO.STUB_BY_FUNCTION: TLE9560
  ENVIRO.STUB_BY_FUNCTION: TLE9560_ApplLayer
  ENVIRO.STUB_BY_FUNCTION: TLE9560_FuncLayer
  ENVIRO.WHITE_BOX: YES
  ENVIRO.VCDB_FILENAME: 
  ENVIRO.COVERAGE_TYPE: Statement+MCDC
  ENVIRO.LIBRARY_STUBS:  
  ENVIRO.STUB: ALL_BY_PROTOTYPE
  ENVIRO.COMPILER: CC
  ENVIRO.TYPE_HANDLED_DIRS_ALLOWED: 
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\TLE9560\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\Common\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\TimerHWAbstr\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\TaskSchedulerLayer\TaskSch\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\DiagComMan\UnitTest
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwIntegration\00_BuildSetup\StartupFile\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\ADC\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CANPAL\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CLOCK\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CRC\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\CRC\Model
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\ERM\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\FLASH\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\FTMIC\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\FTMPWM\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\IM\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LIN\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LPIT\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LPSPI\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\LPTMR\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\PDB\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\DriverLayer\PINS\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\SCUInit\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ADCMon\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\CANMan\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\CRCMan\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ClockMan\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\DIOCtrl\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ERMHWAbstr\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ExtDev\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\ICMon\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\PWMCtrl\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\WDOGMan\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\NVMMan\Code
  ENVIRO.SEARCH_LIST: $(WORKSPACE_ROOT)\SwApplication\Application\HardwareAbstractionLayer\LINMiddleware\Code
  ENVIRO.END

###################################


