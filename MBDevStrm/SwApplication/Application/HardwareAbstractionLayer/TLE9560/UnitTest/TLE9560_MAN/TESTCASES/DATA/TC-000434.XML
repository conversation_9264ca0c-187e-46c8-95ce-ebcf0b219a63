<?xml version="1.0" encoding="UTF-8"?>
<testcase xml_testcase_version="2.0">
  <name>TLE9560_FuncLayer_set_CCP_BLK_BNK0.001</name>
  <unique_id>434</unique_id>
  <unit>12</unit>
  <subprogram>266</subprogram>
  <is_code_based_test>FALSE</is_code_based_test>
  <input_exists>TRUE</input_exists>
  <expected_exists>FALSE</expected_exists>
  <results_exists>TRUE</results_exists>
  <creation>
    <month>5</month>
    <day>30</day>
    <year>2025</year>
    <hour>2</hour>
    <minute>28</minute>
    <seconds>30</seconds>
    <microseconds>0</microseconds>
    <ampm>AM</ampm>
  </creation>
  <execution_start>
    <month>5</month>
    <day>30</day>
    <year>2025</year>
    <hour>2</hour>
    <minute>45</minute>
    <seconds>39</seconds>
    <microseconds>0</microseconds>
    <ampm>AM</ampm>
  </execution_start>
  <epoch_time_execution_started>1748598339</epoch_time_execution_started>
  <execution_end>
    <month>5</month>
    <day>30</day>
    <year>2025</year>
    <hour>2</hour>
    <minute>45</minute>
    <seconds>41</seconds>
    <microseconds>0</microseconds>
    <ampm>AM</ampm>
  </execution_end>
  <epoch_time_execution_ended>1748598341</epoch_time_execution_ended>
  <results>
    <pass>1</pass>
    <total>1</total>
  </results>
  <user_code_needs_compile>FALSE</user_code_needs_compile>
  <user_code_needs_instr_compile>FALSE</user_code_needs_instr_compile>
  <for_compound_only>FALSE</for_compound_only>
  <is_specialized>FALSE</is_specialized>
  <covered_by_analysis>FALSE</covered_by_analysis>
  <automatic_initialization>FALSE</automatic_initialization>
  <automatic_finalization>FALSE</automatic_finalization>
  <is_sorted>TRUE</is_sorted>
  <testcase_status>TCR_STATUS_OK</testcase_status>
  <execution_status>TC_EXECUTION_PASSED</execution_status>
  <harness_execution_status>EXEC_SUCCESS_PASS</harness_execution_status>
  <tolerance>0.000000</tolerance>
  <timeout>-1</timeout>
  <parameter_attributes/>
</testcase>
