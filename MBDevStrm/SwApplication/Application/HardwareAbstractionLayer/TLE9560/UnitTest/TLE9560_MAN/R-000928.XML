<?xml version="1.0" encoding="UTF-8"?>
<thistory version="1"><thistory_results><num_expected>1</num_expected><num_matched_expected>1</num_matched_expected></thistory_results><slot index="1" slot_description="#"><thistory_results><num_expected>1</num_expected><num_matched_expected>1</num_matched_expected></thistory_results><unused_expected_values><test_case_data/></unused_expected_values><num_iterations>1</num_iterations><print_flag>1</print_flag><iteration index="1"><thistory_results><num_expected>1</num_expected><num_matched_expected>1</num_matched_expected></thistory_results><range_iteration index="1"><thistory_results><num_expected>1</num_expected><num_matched_expected>1</num_matched_expected></thistory_results><event index="1"><event_kind>TEST_HISTORY_NORMAL_EVENT</event_kind><control_flow><status>CF_NO_COMPARISON_AVAILABLE</status></control_flow><unit_id>12</unit_id><subprogram_id>346</subprogram_id><is_open_uut_call>1</is_open_uut_call><command_data><harness_command>0.12.346.2</harness_command><actual_value>0</actual_value><is_expected_user_code>0</is_expected_user_code><is_original_command>1</is_original_command></command_data><testcase_usercode/><thistory_results/></event><event index="2"><event_kind>TEST_HISTORY_NORMAL_EVENT</event_kind><control_flow><status>CF_NO_COMPARISON_AVAILABLE</status></control_flow><unit_id>12</unit_id><subprogram_id>346</subprogram_id><command_data><harness_command>0.12.346.2</harness_command><actual_value>1</actual_value><is_expected_user_code>0</is_expected_user_code><is_original_command>1</is_original_command><result_data><expected_value>1</expected_value><matched_value>&lt;match&gt;</matched_value><matched>1</matched></result_data></command_data><testcase_usercode/><thistory_results><num_expected>1</num_expected><num_matched_expected>1</num_matched_expected></thistory_results></event></range_iteration></iteration></slot><thistory_data><event_limit>1000</event_limit></thistory_data></thistory>
