#line 1 "vcast_preprocess.7900.2.c"
#line 1 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"





#pragma language=extended
#line 13 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
__intrinsic __interwork __nounwind __softfp float __aeabi_fadd ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fsub ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_frsub( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fmul ( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_fdiv ( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpeq( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmplt( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpgt( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpge( float x, float y );
__intrinsic __interwork __nounwind __softfp int __aeabi_fcmpun( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfcmpeq( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cfrcmple( float x, float y );
__intrinsic __interwork __nounwind __softfp float __aeabi_i2f( int x );
__intrinsic __interwork __nounwind __softfp float __aeabi_ui2f( unsigned int x );
__intrinsic __interwork __nounwind __softfp int __aeabi_f2iz( float x );
__intrinsic __interwork __nounwind __softfp unsigned int __aeabi_f2uiz( float x );
__intrinsic __interwork __nounwind __softfp float __aeabi_l2f( long long x );
__intrinsic __interwork __nounwind __softfp float __aeabi_ul2f( unsigned long long x );
__intrinsic __interwork __nounwind __softfp long long __aeabi_f2lz( float x );
__intrinsic __interwork __nounwind __softfp unsigned long long __aeabi_f2ulz( float x );
__intrinsic __interwork __nounwind __softfp double __aeabi_dadd( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_dsub( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_drsub( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_dmul( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_ddiv( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpeq( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmplt( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpge( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpgt( double x, double y );
__intrinsic __interwork __nounwind __softfp int __aeabi_dcmpun( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdcmpeq( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp void __aeabi_cdrcmple( double x, double y );
__intrinsic __interwork __nounwind __softfp double __aeabi_i2d( int x );
__intrinsic __interwork __nounwind __softfp double __aeabi_ui2d( unsigned int x );
__intrinsic __interwork __nounwind __softfp int __aeabi_d2iz( double d );
__intrinsic __interwork __nounwind __softfp unsigned int __aeabi_d2uiz( double d );
__intrinsic __interwork __nounwind __softfp double __aeabi_f2d( float f );
__intrinsic __interwork __nounwind __softfp float __aeabi_d2f( double d );
__intrinsic __interwork __nounwind __softfp double __aeabi_l2d( long long x );
__intrinsic __interwork __nounwind __softfp double __aeabi_ul2d( unsigned long long x );
__intrinsic __interwork __nounwind __softfp long long __aeabi_d2lz( double d );
__intrinsic __interwork __nounwind __softfp unsigned long long __aeabi_d2ulz( double d );
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_lmul(long long, long long); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_llsl(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_llsr(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_lasr(long long, int); 
__intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_lcmp(long long, long long); 
__intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_ulcmp(unsigned long long, unsigned long long); 
_Pragma("function_effects = no_state, no_write(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_uread4(void *address); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp int __aeabi_uwrite4(int value, void *address); 
_Pragma("function_effects = no_state, no_write(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_uread8(void *address); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp long long __aeabi_uwrite8(long long value, void *address); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy8 (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy4 (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memcpy  (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove8(void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove4(void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), no_write(2), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memmove (void *d, const void *src, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset8 (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset4 (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memset  (void *d, unsigned int n, int c); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr8 (void *d, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr4 (void *d, unsigned int n); 
_Pragma("function_effects = no_state, no_read(1), always_returns") __intrinsic __interwork __nounwind __aapcs_core __softfp void __aeabi_memclr  (void *d, unsigned int n); 
__intrinsic __interwork __nounwind unsigned int __get_SP( void ); 
__intrinsic __interwork __nounwind unsigned int __get_LR( void ); 
__intrinsic __interwork __nounwind unsigned int __get_PC( void ); 
__intrinsic __interwork __nounwind void __set_SP( unsigned int ); 
__intrinsic __interwork __nounwind void __set_LR( unsigned int ); 
__intrinsic __interwork __nounwind unsigned int __get_return_address( void ); 
#pragma no_bounds
__intrinsic int __semihosting( unsigned int id, void *p ); 
__intrinsic int __semihosting2( unsigned int id, unsigned int id2, void *p ); 
#line 109 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind void *__aeabi_read_tp(void);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fp2bits32(float);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned long long __iar_fp2bits64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fpgethi64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind unsigned int __iar_fpgetlow64(double);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_fpsethi64(double, unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_fpsetlow64(double, unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind float __iar_bits2fp32(unsigned int);
_Pragma("function_effects = no_state, always_returns") __intrinsic __interwork __nounwind double __iar_bits2fp64(unsigned long long);
__intrinsic __interwork __nounwind __noreturn void __stack_chk_fail(void);
extern unsigned int __stack_chk_guard;
#line 127 "C:\\VCAST/DATA/iar/arm/vcast_intrinsics.h"
#pragma language=default
#line 2 "vcast_preprocess.7900.2.c"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\UnitTest\\TLE9560_MAN\\vcast_preprocess.7900.0.c"

typedef int VECTORCAST_MARKER__UNIT_PREFIX_START;

typedef int VECTORCAST_MARKER__UNIT_PREFIX_END;
#line 1 "C:/Users/<USER>/MBVan/MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC/MBDevStrm/SwApplication/Application/HardwareAbstractionLayer/TLE9560/Code/TLE9560_ApplLayer.c"
/**
 *	@file TLE9560_ApplLayer.c
 *	<AUTHOR>	@date
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */

/************************************************************************
**                             Includes                                **
************************************************************************/

#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_ApplLayer.h"
/**
 *	@file TLE9560_ApplLayer.h
 *	<AUTHOR>	@date
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */




/************************************************************************
**                             Includes                                **
************************************************************************/

#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_FuncLayer.h"
/**
 *	@file TLE9560_FuncLayer.h
 *	<AUTHOR>	@date 20.01.2023
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */




/************************************************************************
**                             Includes                                **
************************************************************************/
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560.h"
/**
 *	@file TLE9560.h
 *	<AUTHOR>	@date
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 */




/************************************************************************
**                             Includes                                **
************************************************************************/
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"
/* stdint.h standard header */
/* Copyright 2003-2017 IAR Systems AB.  */




  #pragma system_include


#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ycheck.h"
/* ycheck.h internal checking header file. */
/* Copyright 2005-2017 IAR Systems AB. */

/* Note that there is no include guard for this header. This is intentional. */


  #pragma system_include


/* __AEABI_PORTABILITY_INTERNAL_LEVEL
 *
 * Note: Redefined each time ycheck.h is included, i.e. for each
 * system header, to ensure that ABI support could be turned off/on
 * individually for each file.
 *
 * Possible values for this preprocessor symbol:
 *
 * 0 - ABI portability mode is disabled.
 *
 * 1 - ABI portability mode (version 1) is enabled.
 *
 * All other values are reserved for future use.
 */





#line 11 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
/* yvals.h internal configuration header file. */
/* Copyright 2001-2017 IAR Systems AB. */





  #pragma system_include


/* Convenience macros */









/* Used to refer to '__aeabi' symbols in the library. */


/* Dinkum version */


/* DLib version */




/* Module consistency. */
#pragma rtmodel = "__dlib_version", "6"

/* IAR compiler version check */





/* Read configuration */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"
/***************************************************
 *
 * DLib_Defaults.h is the library configuration manager.
 *
 * Copyright 2003-2017 IAR Systems AB.
 *
 * This configuration header file performs the following tasks:
 *
 * 1. Includes the configuration header file, defined by _DLIB_CONFIG_FILE,
 *    that sets up a particular runtime environment.
 *
 * 2. Includes the product configuration header file, DLib_Product.h, that
 *    specifies default values for the product and makes sure that the
 *    configuration is valid.
 *
 * 3. Sets up default values for all remaining configuration symbols.
 *
 * This configuration header file, the one defined by _DLIB_CONFIG_FILE, and
 * DLib_Product.h configures how the runtime environment should behave. This
 * includes all system headers and the library itself, i.e. all system headers
 * includes this configuration header file, and the library has been built
 * using this configuration header file.
 *
 ***************************************************
 *
 * DO NOT MODIFY THIS FILE!
 *
 ***************************************************/





  #pragma system_include


/* Include the main configuration header file. */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Config_Normal.h"
/* DLib configuration. */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include


/* No changes to the defaults. */

#line 40 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"
  /* _DLIB_CONFIG_FILE_STRING is the quoted variant of above */
#line 47 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/* Include the product specific header file. */
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Product.h"
/* Copyright 2017 IAR Systems AB. */





   #pragma system_include



/*********************************************************************
*
*       Configuration
*
*********************************************************************/

/* Wide character and multi byte character support in library.
 * This is not allowed to vary over configurations, since math-library
 * is built with wide character support.
 */


/* This ensures that the standard header file "string.h" includes
 * the Arm-specific file "DLib_Product_string.h". */


/* This ensures that the standard header file "fenv.h" includes
 * the Arm-specific file "DLib_Product_fenv.h". */


/* This ensures that the standard header file "stdlib.h" includes
 * the Arm-specific file "DLib_Product_stdlib.h". */


/* Max buffer used for swap in qsort */









/* Enable AEABI support */


/* Enable rtmodel for setjump buffer size */


/* Enable parsing of hex floats */






/* Special placement for locale structures when building ropi libraries */




/* Use atomic if possible */




/* CPP-library uses software floatingpoint interface (NOT --libc++) */




/* functions for setting errno should be __no_scratch */


/* Use speedy implementation of floats (simple quad). */


/* Configure time types */


/* Configure generic ELF init routines. */
#line 111 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Product.h"













#line 51 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"



/*
 * The remainder of the file sets up defaults for a number of
 * configuration symbols, each corresponds to a feature in the
 * libary.
 *
 * The value of the symbols should either be 1, if the feature should
 * be supported, or 0 if it shouldn't. (Except where otherwise
 * noted.)
 */


/*
 * File handling
 *
 * Determines whether FILE descriptors and related functions exists or not.
 * When this feature is selected, i.e. set to 1, then FILE descriptors and
 * related functions (e.g. fprintf, fopen) exist. All files, even stdin,
 * stdout, and stderr will then be handled with a file system mechanism that
 * buffers files before accessing the lowlevel I/O interface (__open, __read,
 * __write, etc).
 *
 * If not selected, i.e. set to 0, then FILE descriptors and related functions
 * (e.g. fprintf, fopen) does not exist. All functions that normally uses
 * stderr will use stdout instead. Functions that uses stdout and stdin (like
 * printf and scanf) will access the lowlevel I/O interface directly (__open,
 * __read, __write, etc), i.e. there will not be any buffering.
 *
 * The default is not to have support for FILE descriptors.
 */






/*
 * Use static buffers for stdout
 *
 * This setting controls whether the stream stdout uses a static 80 bytes
 * buffer or uses a one byte buffer allocated in the file descriptor. This
 * setting is only applicable if the FILE descriptors are enabled above.
 *
 * Default is to use a static 80 byte buffer.
 */






/*
 * Support of locale interface
 *
 * "Locale" is the system in C that support language- and
 * contry-specific settings for a number of areas, including currency
 * symbols, date and time, and multibyte encodings.
 *
 * This setting determines whether the locale interface exist or not.
 * When this feature is selected, i.e. set to 1, the locale interface exist
 * (setlocale, etc). A number of preselected locales can be activated during
 * runtime. The preselected locales and encodings are choosen at linkage. The
 * application will start with the "C" locale choosen. (Single byte encoding is
 * always supported in this mode.)
 *
 *
 * If not selected, i.e. set to 0, the locale interface (setlocale, etc) does
 * not exist. The C locale is then preset and cannot be changed.
 *
 * The default is not to have support for the locale interface with the "C"
 * locale and the single byte encoding.
 */





/*
 * Define what memory to place the locale table segment (.iar.locale_table)
 * in.
 */




/*
 * Wide character and multi byte character support in library.
 */

#line 153 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Support of multibytes in printf- and scanf-like functions
 *
 * This is the default value for _DLIB_PRINTF_MULTIBYTE and
 * _DLIB_SCANF_MULTIBYTE. See them for a description.
 *
 * Default is to not have support for multibytes in printf- and scanf-like
 * functions.
 */

#line 172 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Hexadecimal floating-point numbers in strtod
 *
 * If selected, i.e. set to 1, strtod supports C99 hexadecimal floating-point
 * numbers. This also enables hexadecimal floating-points in internal functions
 * used for converting strings and wide strings to float, double, and long
 * double.
 *
 * If not selected, i.e. set to 0, C99 hexadecimal floating-point numbers
 * aren't supported.
 *
 * Default is not to support hexadecimal floating-point numbers.
 */






/*
 * Printf configuration symbols.
 *
 * All the configuration symbols described further on controls the behaviour
 * of printf, sprintf, and the other printf variants.
 *
 * The library proves four formatters for printf: 'tiny', 'small',
 * 'large', and 'default'.  The setup in this file controls all except
 * 'tiny'.  Note that both small' and 'large' explicitly removes
 * some features.
 */

/*
 * Handle multibytes in printf
 *
 * This setting controls whether multibytes and wchar_ts are supported in
 * printf. Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default setting.
 */
#line 223 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Support of formatting anything larger than int in printf
 *
 * This setting controls if 'int' should be used internally in printf, rather
 * than the largest existing integer type. If 'int' is used, any integer or
 * pointer type formatting use 'int' as internal type even though the
 * formatted type is larger. Set to 1 to use 'int' as internal type, otherwise
 * set to 0.
 *
 * See also next configuration.
 *
 * Default is to internally use largest existing internally type.
 */




/*
 * Support of formatting anything larger than long in printf
 *
 * This setting controls if 'long' should be used internally in printf, rather
 * than the largest existing integer type. If 'long' is used, any integer or
 * pointer type formatting use 'long' as internal type even though the
 * formatted type is larger. Set to 1 to use 'long' as internal type,
 * otherwise set to 0.
 *
 * See also previous configuration.
 *
 * Default is to internally use largest existing internally type.
 */








/*
 * Emit a char a time in printf
 *
 * This setting controls internal output handling. If selected, i.e. set to 1,
 * then printf emits one character at a time, which requires less code but
 * can be slightly slower for some types of output.
 *
 * If not selected, i.e. set to 0, then printf buffers some outputs.
 *
 * Note that it is recommended to either use full file support (see
 * _DLIB_FILE_DESCRIPTOR) or -- for debug output -- use the linker
 * option "-e__write_buffered=__write" to enable buffered I/O rather
 * than deselecting this feature.
 */





/*
 * Scanf configuration symbols.
 *
 * All the configuration symbols described here controls the
 * behaviour of scanf, sscanf, and the other scanf variants.
 *
 * The library proves three formatters for scanf: 'small', 'large',
 * and 'default'.  The setup in this file controls all, however both
 * 'small' and 'large' explicitly removes some features.
 */

/*
 * Handle multibytes in scanf
 *
 * This setting controls whether multibytes and wchar_t:s are supported in
 * scanf. Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default.
 */
#line 311 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Handle multibytes in asctime and strftime.
 *
 * This setting controls whether multibytes and wchar_ts are
 * supported.Set to 1 to support them, otherwise set to 0.
 *
 * See _DLIB_FORMATTED_MULTIBYTE for the default setting.
 */
#line 331 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Implement "qsort" using a bubble sort algorithm.
 *
 * Bubble sort is less efficient than quick sort but requires less RAM
 * and ROM resources.
 */






/*
 * Set Buffert size used in qsort
 */






/*
 * Use a simple rand implementation to reduce memory footprint.
 *
 * The default "rand" function uses an array of 32 32-bit integers of memory to
 * store the current state.
 *
 * The simple "rand" function uses only a single 32-bit integer. However, the
 * quality of the generated psuedo-random numbers are not as good as
 * the default implementation.
 */






/*
 * Set attributes for the function used by the C-SPY debug interface to stop at.
 */





/*
 * Used by products where one runtime library can be used by applications
 * with different data models, in order to reduce the total number of
 * libraries required. Typically, this is used when the pointer types does
 * not change over the data models used, but the placement of data variables
 * or/and constant variables do.
 *
 * If defined, this symbol is typically defined to the memory attribute that
 * is used by the runtime library. The actual define must use a
 * _Pragma("type_attribute = xxx") construct. In the header files, it is used
 * on all statically linked data objects seen by the application.
 */




#line 400 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Turn on support for the Target-specific ABI. The ABI is based on the
 * ARM AEABI. A target, except ARM, may deviate from it.
 */

#line 414 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


  /* Possible AEABI deviations */
#line 424 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

#line 432 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

  /*
   * The "difunc" table contains information about C++ objects that
   * should be dynamically initialized, where each entry in the table
   * represents an initialization function that should be called. When
   * the symbol _DLIB_AEABI_DIFUNC_CONTAINS_OFFSETS is true, each
   * entry in the table is encoded as an offset from the entry
   * location. When false, the entries contain the actual addresses to
   * call.
   */





/*
 * Only use IA64 functions
 *
 * Remove the C++ __aeabi functions when using the IA64 interface. Used in
 * ARM AARCH64 mode.
 *
 */
#line 461 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"

/*
 * Turn on usage of a pragma to tell the linker the number of elements used
 * in a setjmp jmp_buf.
 */






/*
 * If true, the product supplies a "DLib_Product_string.h" file that
 * is included from "string.h".
 */





/*
 * Determine whether the math fma routines are fast or not.
 */





/*
 * Favor speed versus some size enlargements in floating point functions.
 */





/*
 * Include dlmalloc as an alternative heap manager in the product.
 *
 * Typically, an application will use a "malloc" heap manager that is
 * relatively small but not that efficient. An application can
 * optionally use the "dlmalloc" package, which provides a more
 * effective "malloc" heap manager, if it is included in the product
 * and supported by the settings.
 *
 * See the product documentation on how to use it, and whether or not
 * it is included in the product.
 */


  /* size_t/ptrdiff_t must be a 4 bytes unsigned integer. */
#line 518 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Make sure certain C++ functions use the soft floating point variant.
 */






/*
 * Allow the 64-bit time_t interface?
 *
 * Default is yes if long long is 64 bits.
 */

#line 542 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Is time_t 64 or 32 bits?
 *
 * Default is 64 bits for the platform.
 */






/*
 * Do we include math functions that demands lots of constant bytes?
 * (like erf, erfc, expm1, fma, lgamma, tgamma, and *_accurate)
 *
 */






/*
 * Support of weak.
 *
 * __weak must be supported. Support of weak means that the call to
 * a weak declared function that isn't part of the application will be
 * executed as a nop instruction.
 *
 */

#line 582 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\DLib_Defaults.h"


/*
 * Deleted options
 */












#line 43 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"






/* A definiton for a function of what effects it has.
   NS  = no_state, errno, i.e. it uses no internal or external state. It may
         write to errno though
   NE  = no_state, i.e. it uses no internal or external state, not even
         writing to errno.
   NRx = no_read(x), i.e. it doesn't read through pointer parameter x.
   NWx = no_write(x), i.e. it doesn't write through pointer parameter x.
   Rx  = returns x, i.e. the function will return parameter x.

   All the functions with effects also has "always_returns",
   i.e. the function will always return.
*/

#line 81 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* Common function attribute macros */






/* Extern "C" handling */
#line 99 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"


/*
 * Support for C99/C11 functionality, C99 secure C functionality, and some
 * other functionality.
 *
 * This setting makes available some macros, functions, etc that are
 * either mandatory in C99/C11 or beneficial.
 *
 * Default is to include them.
 *
 * Disabling this in C++ mode will not compile (some C++ functions uses C99
 * functionality).
 */


  /* Default turned on only when compiling C89 (not C++, C99, or C11). */
#line 124 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"





/* Secure C */
#line 142 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"




/* C++ language setup */
#line 191 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 199 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 206 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* MB_LEN_MAX (max for utf-8 is 4) */


/* for parsing numerics */




/* wchar_t setup */





  typedef unsigned int _Wchart;
  typedef unsigned int _Wintt;
#line 238 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

/* POINTER PROPERTIES */


/* size_t setup */
typedef unsigned int     _Sizet;

/* Basic integer sizes */
typedef signed char   __int8_t;
typedef unsigned char  __uint8_t;
typedef signed short int   __int16_t;
typedef unsigned short int  __uint16_t;
typedef signed int   __int32_t;
typedef unsigned int  __uint32_t;

   typedef signed long long int   __int64_t;
   typedef unsigned long long int  __uint64_t;




typedef signed int   __intptr_t;
typedef unsigned int  __uintptr_t;

/* mbstatet setup */
typedef struct _Mbstatet
{ /* state of a multibyte translation */

    unsigned int _Wchar;  /* Used as an intermediary value (up to 32-bits) */
    unsigned int _State;  /* Used as a state value (only some bits used) */
#line 275 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 299 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
} _Mbstatet;






/* printf setup */


/* stdarg PROPERTIES */
#line 321 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"
  typedef struct __va_list __Va_list;














/* File position */
typedef struct
{

    long long _Off;    /* can be system dependent */



  _Mbstatet _Wstate;
} _Fpost;





/* THREAD AND LOCALE CONTROL */


/* MULTITHREAD PROPERTIES */

  
  /* The lock interface for DLib to use. */
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Locksyslock_StaticGuard(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Malloc(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Stream(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_Debug(void);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlocksyslock_StaticGuard(void);

#line 373 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

  typedef void *__iar_Rmtx;

  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Initdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Dstdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Lockdynamiclock(__iar_Rmtx *);
  _Pragma("object_attribute = __weak") __intrinsic __nounwind void __iar_Unlockdynamiclock(__iar_Rmtx *);

  
#line 406 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"

#line 446 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\yvals.h"



/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 12 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdint.h"





/* Fixed size types. These are all optional. */

  typedef signed char          int8_t;
  typedef unsigned char        uint8_t;



  typedef signed short int         int16_t;
  typedef unsigned short int       uint16_t;



  typedef signed int         int32_t;
  typedef unsigned int       uint32_t;



  typedef signed long long int         int64_t;
  typedef unsigned long long int       uint64_t;


/* Types capable of holding at least a certain number of bits.
   These are not optional for the sizes 8, 16, 32, 64. */
typedef signed char      int_least8_t;
typedef unsigned char    uint_least8_t;

typedef signed short int     int_least16_t;
typedef unsigned short int   uint_least16_t;

typedef signed int     int_least32_t;
typedef unsigned int   uint_least32_t;

/* This isn't really optional, but make it so for now. */

  typedef signed long long int   int_least64_t;


  typedef unsigned long long int uint_least64_t;


/* The fastest type holding at least a certain number of bits.
   These are not optional for the size 8, 16, 32, 64.
   For now, the 64 bit size is optional in IAR compilers. */
typedef signed int       int_fast8_t;
typedef unsigned int     uint_fast8_t;

typedef signed int      int_fast16_t;
typedef unsigned int    uint_fast16_t;

typedef signed int      int_fast32_t;
typedef unsigned int    uint_fast32_t;


  typedef signed long long int    int_fast64_t;


  typedef unsigned long long int  uint_fast64_t;


/* The integer type capable of holding the largest number of bits. */
typedef signed long long int          intmax_t;
typedef unsigned long long int        uintmax_t;

/* An integer type large enough to be able to hold a pointer.
   This is optional, but always supported in IAR compilers. */
typedef signed int          intptr_t;
typedef unsigned int        uintptr_t;

/* An integer capable of holding a pointer to a specific memory type. */



typedef int __data_intptr_t; typedef unsigned int __data_uintptr_t;


/* Minimum and maximum limits. */

























































































/* Macros expanding to integer constants. */
































/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 17 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stdbool.h"
/* stdbool.h header */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include













/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 18 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"
/* stddef.h standard header */
/* Copyright 2009-2017 IAR Systems AB. */




  #pragma system_include


#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ycheck.h"
/* ycheck.h internal checking header file. */
/* Copyright 2005-2017 IAR Systems AB. */

/* Note that there is no include guard for this header. This is intentional. */


  #pragma system_include


/* __AEABI_PORTABILITY_INTERNAL_LEVEL
 *
 * Note: Redefined each time ycheck.h is included, i.e. for each
 * system header, to ensure that ABI support could be turned off/on
 * individually for each file.
 *
 * Possible values for this preprocessor symbol:
 *
 * 0 - ABI portability mode is disabled.
 *
 * 1 - ABI portability mode (version 1) is enabled.
 *
 * All other values are reserved for future use.
 */





#line 11 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"
#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ysizet.h"
/* ysizet.h internal header file. */
/* Copyright 2003-2017 IAR Systems AB.  */





  #pragma system_include


#line 1 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ycheck.h"
/* ycheck.h internal checking header file. */
/* Copyright 2005-2017 IAR Systems AB. */

/* Note that there is no include guard for this header. This is intentional. */


  #pragma system_include


/* __AEABI_PORTABILITY_INTERNAL_LEVEL
 *
 * Note: Redefined each time ycheck.h is included, i.e. for each
 * system header, to ensure that ABI support could be turned off/on
 * individually for each file.
 *
 * Possible values for this preprocessor symbol:
 *
 * 0 - ABI portability mode is disabled.
 *
 * 1 - ABI portability mode (version 1) is enabled.
 *
 * All other values are reserved for future use.
 */





#line 12 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\ysizet.h"


/* type definitions */



  typedef _Sizet size_t;




typedef unsigned int __data_size_t;




#line 13 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"

/* macros */








/* type definitions */



  typedef   signed int ptrdiff_t;




  typedef   _Wchart wchar_t;









    typedef union
    {
      long long _ll;
      long double _ld;
      void *_vp;
    } _Max_align_t;
    typedef _Max_align_t max_align_t;



#line 58 "C:\\Program Files\\IAR Systems\\Embedded Workbench 9.2\\arm\\inc\\c\\stddef.h"



/*
 * Copyright (c) by P.J. Plauger. All rights reserved.
 * Consult your license regarding permissions and restrictions.
V6.50:0576 */
#line 19 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560.h"

/************************************************************************
**                      Local Macro Definitions                        **
************************************************************************/

/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/

/**
 * @brief TLE9560 (Module)
 */





typedef struct { /*!< TLE9560 Structure */

	union {
		volatile uint16_t reg; /*!< Register GENCTRL*/

		struct {
			volatile uint16_t FMODE: 1; /*!< [0..0] Frequency modulation of the charge pump */
			volatile uint16_t IHOLD: 1; /*!< [1..1] Gate driver hold current IHOLD */
			volatile uint16_t EN_GEN_CHECK: 1; /*!< [2..2] Detection of active / FW MOSFET */
			volatile uint16_t AGCFILT: 1; /*!< [3..3] Filter for adaptive gate control */
			volatile uint16_t POCHGDIS: 1; /*!< [4..4] Postcharge disable bit */
			volatile uint16_t CPEN: 1; /*!< [5..5] CPEN */
			volatile uint16_t AGC: 2; /*!< [7..6] Adaptive gate control */
			volatile uint16_t IPCHGADT: 1; /*!< [8..8] Adaptation of the pre-charge and pre-discharge current */
			volatile uint16_t BDOV_REC: 1; /*!< [9..9] Bridge driver recover from VS and VSINT  Overvoltage */
			volatile uint16_t CPSTGA: 1; /*!< [10..10] Automatic switchover between dual and single charge pump stage */
			volatile uint16_t FET_LVL: 1; /*!< [11..11] External MOSFET normal / logic level selection */
			volatile uint16_t CPUVTH: 1; /*!< [12..12] Charge pump under voltage (referred to VS) */
			volatile uint16_t PWM1MAP: 1; /*!< [13..13] PWM1MAP */
			uint16_t : 1; /*!< [14..14] Reserved */
			volatile uint16_t BDFREQ: 1; /*!< [15..15] Bridge driver synchronization frequency */
		} bit;
	} GENCTRL;

        union {
        volatile uint8_t byte;                         /*!< (@ 0x00000000) Status Information field byte                              */
        
        struct {
          volatile uint8_t SUPPLY_STAT : 1;            /*!< [0..0] OR of all bits on SUP_STAT register                                */
          volatile uint8_t TEMP_STAT   : 1;            /*!< [1..1] OR of all bits on THERM_STAT register                              */
          volatile uint8_t BUS_STAT    : 1;            /*!< [2..2] OR of all bits on BUS_STAT register                                */
          volatile uint8_t WAKE_UP     : 1;            /*!< [3..3] OR of all bits on WK_STAT register                                 */
          volatile uint8_t HS_STAT     : 1;            /*!< [4..4] OR of all bits on HS_OL_OC_OT_STAT register                        */
          volatile uint8_t DEV_STAT    : 1;            /*!< [5..5] OR of all bits on DEV_STAT except CRC_STAT and SW_DEV              */
          volatile uint8_t BD_STAT     : 1;            /*!< [6..6] OR of all bits on DSOV register                                    */
          volatile uint8_t SPI_CRC_FAIL: 1;            /*!< [7..7] (SPI_FAIL) OR (CRC_FAIL)                                           */
        } bit;
      } SIF;
  
	union {
		volatile uint16_t reg; /*!< Register M_S_CTRL*/

		struct {
			volatile uint16_t VCC1_RT: 2; /*!< [1..0] VCC1 Reset Threshold Control */
			uint16_t : 3; /*!< [2..4] Reserved */
			volatile uint16_t I_PEAK_TH: 1; /*!< [5..5] VCC1 Active Peak Threshold Selection */
			uint16_t : 1; /*!< [6..6] Reserved */
			volatile uint16_t RSTN_HYS: 1; /*!< [7..7] VCC1 Undervoltage Reset Hysteresis Selection (see alsofor more information) */
			uint16_t : 1; /*!< [8..8] Reserved */
			volatile uint16_t VCC1_OV_MOD: 2; /*!< [10..9] Reaction in case of VCC1 Over Voltage */
			uint16_t : 3; /*!< [11..13] Reserved */
			volatile uint16_t MODE: 2; /*!< [15..14] Device Mode Control */
		} bit;
	} M_S_CTRL;

	union {
		volatile uint16_t reg; /*!< Register HW_CTRL*/

		struct {
			uint16_t : 2; /*!< [0..1] Reserved */
			volatile uint16_t WD_STM_EN_1: 1; /*!< [2..2] Watchdog Deactivation during Stop Mode, bit1 */
			uint16_t : 2; /*!< [3..4] Reserved */
			volatile uint16_t FO_ON: 1; /*!< [5..5] Failure Output Activation */
			volatile uint16_t SOFT_RESET_RO: 1; /*!< [6..6] Soft Reset Configuration */
			uint16_t : 2; /*!< [7..8] Reserved */
			volatile uint16_t RSTN_DEL: 1; /*!< [9..9] Reset delay time */
			volatile uint16_t SH_DISABLE: 1; /*!< [10..10] Sample and hold circuitry disable */
			volatile uint16_t VS_OV_SEL: 1; /*!< [11..11] VS OV comparator threshold change */
			volatile uint16_t TSD2_DEL: 1; /*!< [12..12] TSD2 minimum Waiting Time Selection */
			uint16_t : 3; /*!< [13..15] Reserved */
		} bit;
	} HW_CTRL;

	union {
		volatile uint16_t reg; /*!< Register WD_CTRL*/

		struct {
			volatile uint16_t WD_TIMER: 3; /*!< [2..0] Watchdog Timer Period */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t WD_EN_WK_BUS: 1; /*!< [4..4] Watchdog Enable after Bus Wake in Stop Mode */
			volatile uint16_t WD_CFG: 1; /*!< [5..5] Watchdog Configuration */
			volatile uint16_t WD_STM_EN_0: 1; /*!< [6..6] Watchdog Deactivation during Stop Mode, bit0 */
			uint16_t : 8; /*!< [7..14] Reserved */
			volatile uint16_t CHECKSUM: 1; /*!< [15..15] Watchdog Setting Check Sum Bit */
		} bit;
	} WD_CTRL;

	union {
		volatile uint16_t reg; /*!< Register BUS_CTRL*/

		struct {
			volatile uint16_t CAN: 3; /*!< [2..0] HS-CAN Module Modes */
			volatile uint16_t LIN: 2; /*!< [4..3] LIN Module Modes */
			volatile uint16_t LIN_TXD_TO: 1; /*!< [5..5] LIN TXD TO */
			volatile uint16_t LIN_LSM: 1; /*!< [6..6] LIN LSM */
			volatile uint16_t LIN_FLASH: 1; /*!< [7..7] LIN Flash */
			uint16_t : 8; /*!< [8..15] Reserved */
		} bit;
	} BUS_CTRL;

	union {
		volatile uint16_t reg; /*!< Register BUS_STAT*/

		struct {
			volatile uint16_t VCAN_UV: 1; /*!< [0..0] Under Voltage CAN Bus Supply */
			volatile uint16_t CAN_FAIL: 2; /*!< [2..1] CAN failure status */
			volatile uint16_t SYSERR: 1; /*!< [3..3] SWK System Error */
			volatile uint16_t CANTO: 1; /*!< [4..4] CAN Time Out Detection */
			volatile uint16_t LIN_FAIL: 2; /*!< [6..5] LIN failure status */
			uint16_t : 9; /*!< [7..15] Reserved */
		} bit;
	} BUS_STAT;

	union {
		volatile uint16_t reg; /*!< Register WK_CTRL_BNK1*/

		struct {
			volatile uint16_t WK_BNK: 3; /*!< [2..0] WKs input Banking */
			uint16_t : 2; /*!< [3..4] Reserved */
			volatile uint16_t WK_EN: 2; /*!< [6..5] WKx Enable */
			uint16_t : 2; /*!< [7..8] Reserved */
			volatile uint16_t WK_PUPD: 2; /*!< [10..9] WKx Pull-Up/Pull-Down Configuration */
			volatile uint16_t WK_FILT: 3; /*!< [13..11] Wake-up Filter Time Configuration */
			uint16_t : 1; /*!< [14..14] Reserved */
			volatile uint16_t WK2_FO: 1; /*!< [15..15] WK2 / FO configuration */
		} bit;
	} WK_CTRL_BNK1;

	union {
		volatile uint16_t reg; /*!< Register WK_CTRL_BNK2*/

		struct {
			volatile uint16_t WK_BNK: 3; /*!< [2..0] WKs input Banking */
			uint16_t : 2; /*!< [3..4] Reserved */
			volatile uint16_t WK_EN: 2; /*!< [6..5] WKx Enable */
			uint16_t : 2; /*!< [7..8] Reserved */
			volatile uint16_t WK_PUPD: 2; /*!< [10..9] WKx Pull-Up/Pull-Down Configuration */
			volatile uint16_t WK_FILT: 3; /*!< [13..11] Wake-up Filter Time Configuration */
			uint16_t : 1; /*!< [14..14] Reserved */
			volatile uint16_t WK2_FO: 1; /*!< [15..15] WK2 / FO configuration */
		} bit;
	} WK_CTRL_BNK2;

	union {
		volatile uint16_t reg; /*!< Register WK_CTRL_BNK3*/

		struct {
			volatile uint16_t WK_BNK: 3; /*!< [2..0] WKs input Banking */
			uint16_t : 2; /*!< [3..4] Reserved */
			volatile uint16_t WK_EN: 2; /*!< [6..5] WKx Enable */
			uint16_t : 2; /*!< [7..8] Reserved */
			volatile uint16_t WK_PUPD: 2; /*!< [10..9] WKx Pull-Up/Pull-Down Configuration */
			volatile uint16_t WK_FILT: 3; /*!< [13..11] Wake-up Filter Time Configuration */
			uint16_t : 1; /*!< [14..14] Reserved */
			volatile uint16_t WK2_FO: 1; /*!< [15..15] WK2 / FO configuration */
		} bit;
	} WK_CTRL_BNK3;

	union {
		volatile uint16_t reg; /*!< Register WK_CTRL_BNK4*/

		struct {
			volatile uint16_t WK_BNK: 3; /*!< [2..0] WKs input Banking */
			uint16_t : 2; /*!< [3..4] Reserved */
			volatile uint16_t WK_EN: 2; /*!< [6..5] WKx Enable */
			uint16_t : 2; /*!< [7..8] Reserved */
			volatile uint16_t WK_PUPD: 2; /*!< [10..9] WKx Pull-Up/Pull-Down Configuration */
			volatile uint16_t WK_FILT: 3; /*!< [13..11] Wake-up Filter Time Configuration */
			uint16_t : 1; /*!< [14..14] Reserved */
			volatile uint16_t WK2_FO: 1; /*!< [15..15] WK2 / FO configuration */
		} bit;
	} WK_CTRL_BNK4;

	union {
		volatile uint16_t reg; /*!< Register WK_STAT*/

		struct {
			volatile uint16_t WK1_WU: 1; /*!< [0..0] Wake up via WK1 */
			volatile uint16_t WK2_WU: 1; /*!< [1..1] Wake up via WK2 */
			volatile uint16_t WK3_WU: 1; /*!< [2..2] Wake up via WK3 */
			volatile uint16_t WK4_WU: 1; /*!< [3..3] Wake up via WK4 */
			uint16_t : 3; /*!< [4..6] Reserved */
			volatile uint16_t TIMER1_WU: 1; /*!< [7..7] Wake up via Timer1 */
			volatile uint16_t TIMER2_WU: 1; /*!< [8..8] Wake up via Timer2 */
			volatile uint16_t CAN_WU: 1; /*!< [9..9] Wake up via CAN Bus */
			volatile uint16_t LIN_WU: 1; /*!< [10..10] LIN wake up */
			uint16_t : 5; /*!< [11..15] Reserved */
		} bit;
	} WK_STAT;

	union {
		volatile uint16_t reg; /*!< Register WK_LVL_STAT*/

		struct {
			volatile uint16_t WK1_LVL: 1; /*!< [0..0] Status of WK1 */
			volatile uint16_t WK2_LVL: 1; /*!< [1..1] Status of WK2 */
			volatile uint16_t WK3_LVL: 1; /*!< [2..2] Status of WK3 */
			volatile uint16_t WK4_LVL: 1; /*!< [3..3] Status of WK4 */
			uint16_t : 12; /*!< [4..15] Reserved */
		} bit;
	} WK_LVL_STAT;

	union {
		volatile uint16_t reg; /*!< Register TIMER_CTRL*/

		struct {
			volatile uint16_t TIMER1_PER: 3; /*!< [2..0] Timer1 Period Configuration */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t TIMER1_ON: 3; /*!< [6..4] Timer1 On-Time Configuration */
			volatile uint16_t CYCWK: 2; /*!< [8..7] Cyclic Wake Configuration */
			volatile uint16_t TIMER2_PER: 3; /*!< [11..9] Timer2 Period Configuration */
			uint16_t : 1; /*!< [12..12] Reserved */
			volatile uint16_t TIMER2_ON: 3; /*!< [15..13] Timer2 On-Time Configuration */
		} bit;
	} TIMER_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SW_SD_CTRL*/

		struct {
			uint16_t : 3; /*!< [0..2] Reserved */
			volatile uint16_t HS_UV_REC: 1; /*!< [3..3] Switch recovery after removal of Undervoltage for HSx */
			uint16_t : 1; /*!< [4..4] Reserved */
			volatile uint16_t HS_UV_SD_DIS: 1; /*!< [5..5] Shutdown Disabling of HSx in case of input supply undervoltage */
			volatile uint16_t HS_OV_SDS_DIS: 1; /*!< [6..6] Shutdown Disabling of HSx in case of input supply overvoltage in Stop Mode or Sleep Mode */
			volatile uint16_t HS1_OV_SDN_DIS: 1; /*!< [7..7] Shutdown Disabling of HS1 in case of input supply overvoltage in Normal Mode */
			volatile uint16_t HS2_OV_SDN_DIS: 1; /*!< [8..8] Shutdown Disabling of HS2 in case of input supply overvoltage in Normal Mode */
			volatile uint16_t HS3_OV_SDN_DIS: 1; /*!< [9..9] Shutdown Disabling of HS3 in case of input supply overvoltage in Normal Mode */
			volatile uint16_t HS4_OV_SDN_DIS: 1; /*!< [10..10] Shutdown Disabling of HS4 in case of input supply overvoltage in Normal Mode */
			volatile uint16_t HS_OT_SD_DIS: 1; /*!< [11..11] Shutdown Disabling of all HS in case of Overtemperature event */
			volatile uint16_t HS1_OV_REC: 1; /*!< [12..12] Switch recovery after removal of VSHSOvervoltage for HS1 */
			volatile uint16_t HS2_OV_REC: 1; /*!< [13..13] Switch recovery after removal of VSHSOvervoltage for HS2 */
			volatile uint16_t HS3_OV_REC: 1; /*!< [14..14] Switch recovery after removal of VSHSOvervoltage for HS3 */
			volatile uint16_t HS4_OV_REC: 1; /*!< [15..15] Switch recovery after removal of VSHSOvervoltage for HS4 */
		} bit;
	} SW_SD_CTRL;

	union {
		volatile uint16_t reg; /*!< Register HS_CTRL*/

		struct {
			volatile uint16_t HS1: 4; /*!< [3..0] HS1 Configuration */
			volatile uint16_t HS2: 4; /*!< [7..4] HS2 Configuration */
			volatile uint16_t HS3: 4; /*!< [11..8] HS3 Configuration */
			volatile uint16_t HS4: 4; /*!< [15..12] HS4 Configuration */
		} bit;
	} HS_CTRL;

	union {
		volatile uint16_t reg; /*!< Register HS_VDS*/

		struct {
			volatile uint16_t HS1VDSTH: 3; /*!< [2..0] HS1 drain-source overvoltage threshold */
			volatile uint16_t HS2VDSTH: 3; /*!< [5..3] HS2 drain-source overvoltage threshold */
			uint16_t : 6; /*!< [6..11] Reserved */
			volatile uint16_t DEEP_ADAP: 1; /*!< [12..12] Deep adaptation enable */
			uint16_t : 3; /*!< [13..15] Reserved */
		} bit;
	} HS_VDS;

	union {
		volatile uint16_t reg; /*!< Register HS_OL_OC_OT_STAT*/

		struct {
			volatile uint16_t HS1_OC: 1; /*!< [0..0] Overcurrent Detection on HS1 */
			volatile uint16_t HS2_OC: 1; /*!< [1..1] Overcurrent Detection on HS2 */
			volatile uint16_t HS3_OC: 1; /*!< [2..2] Overcurrent Detection on HS3 */
			volatile uint16_t HS4_OC: 1; /*!< [3..3] Overcurrent Detection on HS4 */
			uint16_t : 1; /*!< [4..4] Reserved */
			volatile uint16_t HS1_OL: 1; /*!< [5..5] Open-Load Detection on HS1 */
			volatile uint16_t HS2_OL: 1; /*!< [6..6] Open-Load Detection on HS2 */
			volatile uint16_t HS3_OL: 1; /*!< [7..7] Open-Load Detection on HS3 */
			volatile uint16_t HS4_OL: 1; /*!< [8..8] Open-Load Detection on HS4 */
			uint16_t : 1; /*!< [9..9] Reserved */
			volatile uint16_t HS1_OT: 1; /*!< [10..10] Overtemperature Detection on HS1 */
			volatile uint16_t HS2_OT: 1; /*!< [11..11] Overtemperature Detection on HS2 */
			volatile uint16_t HS3_OT: 1; /*!< [12..12] Overtemperature Detection on HS3 */
			volatile uint16_t HS4_OT: 1; /*!< [13..13] Overtemperature Detection on HS4 */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} HS_OL_OC_OT_STAT;

	union {
		volatile uint16_t reg; /*!< Register INT_MASK*/

		struct {
			volatile uint16_t SUPPLY_STAT: 1; /*!< [0..0] SUPPLY Status Interrupt generation */
			volatile uint16_t TEMP_STAT: 1; /*!< [1..1] Temperature Interrupt generation */
			volatile uint16_t BUS_STAT: 1; /*!< [2..2] BUS Interrupt generation */
			volatile uint16_t HS_STAT: 1; /*!< [3..3] High Side Interrupt generation */
			volatile uint16_t BD_STAT: 1; /*!< [4..4] Bridge Driver Interrupt generation */
			volatile uint16_t SPI_CRC_FAIL: 1; /*!< [5..5] SPI and CRC interrupt generation */
			volatile uint16_t WD_SDM: 1; /*!< [6..6] Watchdog failure in Software Development Mode */
			volatile uint16_t WD_SDM_DISABLE: 1; /*!< [7..7] Disable Watchdog in Software Development Mode */
			volatile uint16_t INTN_CYC_EN: 1; /*!< [8..8] Periodical INTN generation */
			uint16_t : 7; /*!< [9..15] Reserved */
		} bit;
	} INT_MASK;

	union {
		volatile uint16_t reg; /*!< Register PWM_CTRL_BNK0*/

		struct {
			volatile uint16_t PWM_BNK: 3; /*!< [2..0] Internal PWM generator selection */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t PWM_DC: 10; /*!< [13..4] PWM Duty Cycle Setting (bit4 = LSB; bit13 = MSB) */
			volatile uint16_t PWM_FREQ: 1; /*!< [14..14] PWM generator Frequency Setting */
			uint16_t : 1; /*!< [15..15] Reserved */
		} bit;
	} PWM_CTRL_BNK0;

	union {
		volatile uint16_t reg; /*!< Register PWM_CTRL_BNK1*/

		struct {
			volatile uint16_t PWM_BNK: 3; /*!< [2..0] Internal PWM generator selection */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t PWM_DC: 10; /*!< [13..4] PWM Duty Cycle Setting (bit4 = LSB; bit13 = MSB) */
			volatile uint16_t PWM_FREQ: 1; /*!< [14..14] PWM generator Frequency Setting */
			uint16_t : 1; /*!< [15..15] Reserved */
		} bit;
	} PWM_CTRL_BNK1;

	union {
		volatile uint16_t reg; /*!< Register PWM_CTRL_BNK2*/

		struct {
			volatile uint16_t PWM_BNK: 3; /*!< [2..0] Internal PWM generator selection */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t PWM_DC: 10; /*!< [13..4] PWM Duty Cycle Setting (bit4 = LSB; bit13 = MSB) */
			volatile uint16_t PWM_FREQ: 1; /*!< [14..14] PWM generator Frequency Setting */
			uint16_t : 1; /*!< [15..15] Reserved */
		} bit;
	} PWM_CTRL_BNK2;

	union {
		volatile uint16_t reg; /*!< Register PWM_CTRL_BNK3*/

		struct {
			volatile uint16_t PWM_BNK: 3; /*!< [2..0] Internal PWM generator selection */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t PWM_DC: 10; /*!< [13..4] PWM Duty Cycle Setting (bit4 = LSB; bit13 = MSB) */
			volatile uint16_t PWM_FREQ: 1; /*!< [14..14] PWM generator Frequency Setting */
			uint16_t : 1; /*!< [15..15] Reserved */
		} bit;
	} PWM_CTRL_BNK3;

	union {
		volatile uint16_t reg; /*!< Register SYS_STAT_CTRL*/

		struct {
			volatile uint16_t SYS_STAT: 16; /*!< [15..0] System Status Control (bit0=LSB; bit15=MSB) */
		} bit;
	} SYS_STAT_CTRL;

	union {
		volatile uint16_t reg; /*!< Register LS_VDS*/

		struct {
			volatile uint16_t LS1VDSTH: 3; /*!< [2..0] LS1 drain-source overvoltage threshold */
			volatile uint16_t LS2VDSTH: 3; /*!< [5..3] LS2 drain-source overvoltage threshold */
			uint16_t : 6; /*!< [6..11] Reserved */
			volatile uint16_t TFVDS: 2; /*!< [13..12] Filter time of drain-source voltage monitoring */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} LS_VDS;

	union {
		volatile uint16_t reg; /*!< Register CCP_BLK_BNK0*/

		struct {
			volatile uint16_t CCP_BNK: 3; /*!< [2..0] Cross-current and time banking */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TCCP: 4; /*!< [11..8] Cross-current protection time */
			volatile uint16_t TBLANK: 4; /*!< [15..12] Blank time */
		} bit;
	} CCP_BLK_BNK0;

	union {
		volatile uint16_t reg; /*!< Register CCP_BLK_BNK1*/

		struct {
			volatile uint16_t CCP_BNK: 3; /*!< [2..0] Cross-current and time banking */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TCCP: 4; /*!< [11..8] Cross-current protection time */
			volatile uint16_t TBLANK: 4; /*!< [15..12] Blank time */
		} bit;
	} CCP_BLK_BNK1;

	union {
		volatile uint16_t reg; /*!< Register CCP_BLK_BNK4*/

		struct {
			volatile uint16_t CCP_BNK: 3; /*!< [2..0] Cross-current and time banking */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TCCP: 4; /*!< [11..8] Cross-current protection time */
			volatile uint16_t TBLANK: 4; /*!< [15..12] Blank time */
		} bit;
	} CCP_BLK_BNK4;

	union {
		volatile uint16_t reg; /*!< Register CCP_BLK_BNK5*/

		struct {
			volatile uint16_t CCP_BNK: 3; /*!< [2..0] Cross-current and time banking */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TCCP: 4; /*!< [11..8] Cross-current protection time */
			volatile uint16_t TBLANK: 4; /*!< [15..12] Blank time */
		} bit;
	} CCP_BLK_BNK5;

	union {
		volatile uint16_t reg; /*!< Register ST_ICHG*/

		struct {
			volatile uint16_t ICHGST1: 4; /*!< [3..0] Static charge and discharge currents of HB1 */
			volatile uint16_t ICHGST2: 4; /*!< [7..4] Static charge and discharge currents of HB2 */
			uint16_t : 8; /*!< [8..15] Reserved */
		} bit;
	} ST_ICHG;

	union {
		volatile uint16_t reg; /*!< Register HB_ICHG_BNK0*/

		struct {
			volatile uint16_t ICHG_BNK: 3; /*!< [2..0] Banking bits for charge and discharge currents of active MOSFETs */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t ICHG: 6; /*!< [9..4] IfICHG_BNK=0xxB: Charge current of HBx active MOSFET */
			volatile uint16_t IDCHG: 6; /*!< [15..10] IfICHG_BNK=0xxB: Discharge current of HBx active MOSFET */
		} bit;
	} HB_ICHG_BNK0;

	union {
		volatile uint16_t reg; /*!< Register HB_ICHG_BNK1*/

		struct {
			volatile uint16_t ICHG_BNK: 3; /*!< [2..0] Banking bits for charge and discharge currents of active MOSFETs */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t ICHG: 6; /*!< [9..4] IfICHG_BNK=0xxB: Charge current of HBx active MOSFET */
			volatile uint16_t IDCHG: 6; /*!< [15..10] IfICHG_BNK=0xxB: Discharge current of HBx active MOSFET */
		} bit;
	} HB_ICHG_BNK1;

	union {
		volatile uint16_t reg; /*!< Register HB_ICHG_BNK4*/

		struct {
			volatile uint16_t ICHG_BNK: 3; /*!< [2..0] Banking bits for charge and discharge currents of active MOSFETs */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t ICHG: 6; /*!< [9..4] IfICHG_BNK=0xxB: Charge current of HBx active MOSFET */
			volatile uint16_t IDCHG: 6; /*!< [15..10] IfICHG_BNK=0xxB: Discharge current of HBx active MOSFET */
		} bit;
	} HB_ICHG_BNK4;

	union {
		volatile uint16_t reg; /*!< Register HB_ICHG_BNK5*/

		struct {
			volatile uint16_t ICHG_BNK: 3; /*!< [2..0] Banking bits for charge and discharge currents of active MOSFETs */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t ICHG: 6; /*!< [9..4] IfICHG_BNK=0xxB: Charge current of HBx active MOSFET */
			volatile uint16_t IDCHG: 6; /*!< [15..10] IfICHG_BNK=0xxB: Discharge current of HBx active MOSFET */
		} bit;
	} HB_ICHG_BNK5;

	union {
		volatile uint16_t reg; /*!< Register HB_ICHG_MAX*/

		struct {
			volatile uint16_t ICHGMAX1: 2; /*!< [1..0] Maximum drive current of HB1 during the pre-charge and pre-discharge phases */
			volatile uint16_t ICHGMAX2: 2; /*!< [3..2] Maximum drive current of HB2 during the pre-charge phase and pre-discharge phases */
			uint16_t : 8; /*!< [4..11] Reserved */
			volatile uint16_t HB1IDIAG: 1; /*!< [12..12] Control of HB1 pull-down for off-state diagnostic */
			volatile uint16_t HB2IDIAG: 1; /*!< [13..13] Control of HB2 pull-down for off-state diagnostic */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} HB_ICHG_MAX;

	union {
		volatile uint16_t reg; /*!< Register HB_PCHG_INIT_BNK0*/

		struct {
			volatile uint16_t INIT_BNK: 3; /*!< [2..0] Banking bits for Precharge an Predischarge Initial Current */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t PCHGINIT: 6; /*!< [9..4] Initial precharge current of HBx, IPCHGINITx */
			volatile uint16_t PDCHGINIT: 6; /*!< [15..10] Initial predischarge current of HBx, IPDCHGINITx */
		} bit;
	} HB_PCHG_INIT_BNK0;

	union {
		volatile uint16_t reg; /*!< Register HB_PCHG_INIT_BNK1*/

		struct {
			volatile uint16_t INIT_BNK: 3; /*!< [2..0] Banking bits for Precharge an Predischarge Initial Current */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t PCHGINIT: 6; /*!< [9..4] Initial precharge current of HBx, IPCHGINITx */
			volatile uint16_t PDCHGINIT: 6; /*!< [15..10] Initial predischarge current of HBx, IPDCHGINITx */
		} bit;
	} HB_PCHG_INIT_BNK1;

	union {
		volatile uint16_t reg; /*!< Register TDON_HB_CTRL_BNK0*/

		struct {
			volatile uint16_t HB_TDON_BNK: 3; /*!< [2..0] Banking bits for turn-on delay time */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TDON: 6; /*!< [13..8] Turn-on delay time of active MOSFET of HBx */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} TDON_HB_CTRL_BNK0;

	union {
		volatile uint16_t reg; /*!< Register TDON_HB_CTRL_BNK1*/

		struct {
			volatile uint16_t HB_TDON_BNK: 3; /*!< [2..0] Banking bits for turn-on delay time */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TDON: 6; /*!< [13..8] Turn-on delay time of active MOSFET of HBx */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} TDON_HB_CTRL_BNK1;

	union {
		volatile uint16_t reg; /*!< Register TDOFF_HB_CTRL_BNK0*/

		struct {
			volatile uint16_t HB_TDOFF_BNK: 3; /*!< [2..0] Banking bits for turn-off delay time */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TDOFF: 6; /*!< [13..8] Turn-off delay time of active MOSFET of HBx */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} TDOFF_HB_CTRL_BNK0;

	union {
		volatile uint16_t reg; /*!< Register TDOFF_HB_CTRL_BNK1*/

		struct {
			volatile uint16_t HB_TDOFF_BNK: 3; /*!< [2..0] Banking bits for turn-off delay time */
			uint16_t : 5; /*!< [3..7] Reserved */
			volatile uint16_t TDOFF: 6; /*!< [13..8] Turn-off delay time of active MOSFET of HBx */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} TDOFF_HB_CTRL_BNK1;

	union {
		volatile uint16_t reg; /*!< Register SWK_CTRL*/

		struct {
			volatile uint16_t CFG_VAL: 1; /*!< [0..0] SWK Configuration valid */
			uint16_t : 4; /*!< [0..3] Reserved */
			volatile uint16_t CANTO_MASK: 1; /*!< [4..4] CAN Time Out Masking */
			volatile uint16_t TRIM_EN: 2; /*!< [6..5] (Un)locking mechanism of oscillator recalibration */
			volatile uint16_t OSC_CAL: 1; /*!< [7..7] Oscillator Calibration Mode */
			uint16_t : 8; /*!< [8..15] Reserved */
		} bit;
	} SWK_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_BTL1_CTRL*/

		struct {
			volatile uint16_t TBIT: 8; /*!< [7..0] Number of Time Quanta in a Bit Time */
			uint16_t : 2; /*!< [8..9] Reserved */
			volatile uint16_t SP: 6; /*!< [15..10] Sampling Point Position */
		} bit;
	} SWK_BTL1_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_ID1_CTRL*/

		struct {
			volatile uint16_t ID13: 1; /*!< [0..0] WUF Identifier Bit 13 */
			volatile uint16_t ID14: 1; /*!< [1..1] WUF Identifier Bit 14 */
			volatile uint16_t ID15: 1; /*!< [2..2] WUF Identifier Bit 15 */
			volatile uint16_t ID16: 1; /*!< [3..3] WUF Identifier Bit 16 */
			volatile uint16_t ID17: 1; /*!< [4..4] WUF Identifier Bit 17 */
			volatile uint16_t ID18: 1; /*!< [5..5] WUF Identifier Bit 18 */
			volatile uint16_t ID19: 1; /*!< [6..6] WUF Identifier Bit 19 */
			volatile uint16_t ID20: 1; /*!< [7..7] WUF Identifier Bit 20 */
			volatile uint16_t ID21: 1; /*!< [8..8] WUF Identifier Bit 21 */
			volatile uint16_t ID22: 1; /*!< [9..9] WUF Identifier Bit 22 */
			volatile uint16_t ID23: 1; /*!< [10..10] WUF Identifier Bit 23 */
			volatile uint16_t ID24: 1; /*!< [11..11] WUF Identifier Bit 24 */
			volatile uint16_t ID25: 1; /*!< [12..12] WUF Identifier Bit 25 */
			volatile uint16_t ID26: 1; /*!< [13..13] WUF Identifier Bit 26 */
			volatile uint16_t ID27: 1; /*!< [14..14] WUF Identifier Bit 27 */
			volatile uint16_t ID28: 1; /*!< [15..15] WUF Identifier Bit 28 */
		} bit;
	} SWK_ID1_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_ID0_CTRL*/

		struct {
			volatile uint16_t IDE: 1; /*!< [0..0] Identifier Extension Bit */
			volatile uint16_t RTR: 1; /*!< [1..1] Remote Transmission Request Field (acc. ISO11898-2:2016) */
			volatile uint16_t ID0: 1; /*!< [2..2] WUF Identifier Bit 0 */
			volatile uint16_t ID1: 1; /*!< [3..3] WUF Identifier Bit 1 */
			volatile uint16_t ID2: 1; /*!< [4..4] WUF Identifier Bit 2 */
			volatile uint16_t ID3: 1; /*!< [5..5] WUF Identifier Bit 3 */
			volatile uint16_t ID4: 1; /*!< [6..6] WUF Identifier Bit 4 */
			uint16_t : 1; /*!< [7..7] Reserved */
			volatile uint16_t ID5: 1; /*!< [8..8] WUF Identifier Bit 5 */
			volatile uint16_t ID6: 1; /*!< [9..9] WUF Identifier Bit 6 */
			volatile uint16_t ID7: 1; /*!< [10..10] WUF Identifier Bit 7 */
			volatile uint16_t ID8: 1; /*!< [11..11] WUF Identifier Bit 8 */
			volatile uint16_t ID9: 1; /*!< [12..12] WUF Identifier Bit 9 */
			volatile uint16_t ID10: 1; /*!< [13..13] WUF Identifier Bit 10 */
			volatile uint16_t ID11: 1; /*!< [14..14] WUF Identifier Bit 11 */
			volatile uint16_t ID12: 1; /*!< [15..15] WUF Identifier Bit 12 */
		} bit;
	} SWK_ID0_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_MASK_ID1_CTRL*/

		struct {
			volatile uint16_t MASK_ID13: 1; /*!< [0..0] WUF Identifier Mask Bit 13 */
			volatile uint16_t MASK_ID14: 1; /*!< [1..1] WUF Identifier Mask Bit 14 */
			volatile uint16_t MASK_ID15: 1; /*!< [2..2] WUF Identifier Mask Bit 15 */
			volatile uint16_t MASK_ID16: 1; /*!< [3..3] WUF Identifier Mask Bit 16 */
			volatile uint16_t MASK_ID17: 1; /*!< [4..4] WUF Identifier Mask Bit 17 */
			volatile uint16_t MASK_ID18: 1; /*!< [5..5] WUF Identifier Mask Bit 18 */
			volatile uint16_t MASK_ID19: 1; /*!< [6..6] WUF Identifier Mask Bit 19 */
			volatile uint16_t MASK_ID20: 1; /*!< [7..7] WUF Identifier Mask Bit 20 */
			volatile uint16_t MASK_ID21: 1; /*!< [8..8] WUF Identifier Mask Bit 21 */
			volatile uint16_t MASK_ID22: 1; /*!< [9..9] WUF Identifier Mask Bit 22 */
			volatile uint16_t MASK_ID23: 1; /*!< [10..10] WUF Identifier Mask Bit 23 */
			volatile uint16_t MASK_ID24: 1; /*!< [11..11] WUF Identifier Mask Bit 24 */
			volatile uint16_t MASK_ID25: 1; /*!< [12..12] WUF Identifier Mask Bit 25 */
			volatile uint16_t MASK_ID26: 1; /*!< [13..13] WUF Identifier Mask Bit 26 */
			volatile uint16_t MASK_ID27: 1; /*!< [14..14] WUF Identifier Mask Bit 27 */
			volatile uint16_t MASK_ID28: 1; /*!< [15..15] WUF Identifier Mask Bit 28 */
		} bit;
	} SWK_MASK_ID1_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_MASK_ID0_CTRL*/

		struct {
			uint16_t : 2; /*!< [0..1] Reserved */
			volatile uint16_t MASK_ID0: 1; /*!< [2..2] WUF Identifier Mask Bit 0 */
			volatile uint16_t MASK_ID1: 1; /*!< [3..3] WUF Identifier Mask Bit 1 */
			volatile uint16_t MASK_ID2: 1; /*!< [4..4] WUF Identifier Mask Bit 2 */
			volatile uint16_t MASK_ID3: 1; /*!< [5..5] WUF Identifier Mask Bit 3 */
			volatile uint16_t MASK_ID4: 1; /*!< [6..6] WUF Identifier Mask Bit 4 */
			uint16_t : 1; /*!< [7..7] Reserved */
			volatile uint16_t MASK_ID5: 1; /*!< [8..8] WUF Identifier Mask Bit 5 */
			volatile uint16_t MASK_ID6: 1; /*!< [9..9] WUF Identifier Mask Bit 6 */
			volatile uint16_t MASK_ID7: 1; /*!< [10..10] WUF Identifier Mask Bit 7 */
			volatile uint16_t MASK_ID8: 1; /*!< [11..11] WUF Identifier Mask Bit 8 */
			volatile uint16_t MASK_ID9: 1; /*!< [12..12] WUF Identifier Mask Bit 9 */
			volatile uint16_t MASK_ID10: 1; /*!< [13..13] WUF Identifier Mask Bit 10 */
			volatile uint16_t MASK_ID11: 1; /*!< [14..14] WUF Identifier Mask Bit 11 */
			volatile uint16_t MASK_ID12: 1; /*!< [15..15] WUF Identifier Mask Bit 12 */
		} bit;
	} SWK_MASK_ID0_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_DLC_CTRL*/

		struct {
			volatile uint16_t DLC: 4; /*!< [3..0] Payload length in number of bytes */
			uint16_t : 12; /*!< [4..15] Reserved */
		} bit;
	} SWK_DLC_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_DATA3_CTRL*/

		struct {
			volatile uint16_t DATA6: 8; /*!< [7..0] Data6 byte content(bit0=LSB; bit7=MSB) */
			volatile uint16_t DATA7: 8; /*!< [15..8] Data7 byte content(bit0=LSB; bit7=MSB) */
		} bit;
	} SWK_DATA3_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_DATA2_CTRL*/

		struct {
			volatile uint16_t DATA4: 8; /*!< [7..0] Data4 byte content(bit0=LSB; bit7=MSB) */
			volatile uint16_t DATA5: 8; /*!< [15..8] Data5 byte content(bit0=LSB; bit7=MSB) */
		} bit;
	} SWK_DATA2_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_DATA1_CTRL*/

		struct {
			volatile uint16_t DATA2: 8; /*!< [7..0] Data2 byte content(bit0=LSB; bit7=MSB) */
			volatile uint16_t DATA3: 8; /*!< [15..8] Data3 byte content(bit0=LSB; bit7=MSB) */
		} bit;
	} SWK_DATA1_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_DATA0_CTRL*/

		struct {
			volatile uint16_t DATA0: 8; /*!< [7..0] Data0 byte content(bit0=LSB; bit7=MSB) */
			volatile uint16_t DATA1: 8; /*!< [15..8] Data1 byte content(bit0=LSB; bit7=MSB) */
		} bit;
	} SWK_DATA0_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_CAN_FD_CTRL*/

		struct {
			volatile uint16_t CAN_FD_EN: 1; /*!< [0..0] Enable CAN FD Tolerant Mode */
			volatile uint16_t FD_FILTER: 3; /*!< [3..1] CAN FD Dominant Filter Time */
			uint16_t : 1; /*!< [4..4] Reserved */
			volatile uint16_t DIS_ERR_CNT: 1; /*!< [5..5] Error Counter Disable Function */
			uint16_t : 10; /*!< [6..15] Reserved */
		} bit;
	} SWK_CAN_FD_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_OSC_TRIM_CTRL*/

		struct {
			volatile uint16_t TRIM_OSC: 7; /*!< [6..0] Trimming of oscillator (only writable if TRIM_EN = ‘11’) */
			volatile uint16_t TEMP_COEF: 5; /*!< [11..7] Trimming of temp_coef (only writable if TRIM_EN = ‘11’) */
			uint16_t : 2; /*!< [12..13] Reserved */
			volatile uint16_t RX_WK_SEL: 1; /*!< [14..14] SWK Receiver selection (only accessible if TRIM_EN = ‘11’) */
			uint16_t : 1; /*!< [15..15] Reserved */
		} bit;
	} SWK_OSC_TRIM_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_OSC_CAL_STAT*/

		struct {
			volatile uint16_t OSC_CAL_L: 8; /*!< [7..0] Oscillator Calibration Low Register */
			volatile uint16_t OSC_CAL_H: 8; /*!< [15..8] Oscillator Calibration High Register */
		} bit;
	} SWK_OSC_CAL_STAT;

	union {
		volatile uint16_t reg; /*!< Register SWK_CDR_CTRL*/

		struct {
			volatile uint16_t CDR_EN: 1; /*!< [0..0] Enable CDR */
			uint16_t : 2; /*!< [0..1] Reserved */
			volatile uint16_t SELFILT: 2; /*!< [3..2] Select Time Constant of Filter */
			uint16_t : 1; /*!< [4..4] Reserved */
			volatile uint16_t SEL_OSC_CLK: 2; /*!< [6..5] Input Frequency for CDR module */
			uint16_t : 9; /*!< [7..15] Reserved */
		} bit;
	} SWK_CDR_CTRL;

	union {
		volatile uint16_t reg; /*!< Register SWK_CDR_LIMIT*/

		struct {
			volatile uint16_t CDR_LIM_L: 8; /*!< [7..0] Lower Bit Time Detection Range of Clock and Data Recovery */
			volatile uint16_t CDR_LIM_H: 8; /*!< [15..8] Upper Bit Time Detection Range of Clock and Data Recovery */
		} bit;
	} SWK_CDR_LIMIT;

	union {
		volatile uint16_t reg; /*!< Register SWK_STAT*/

		struct {
			uint16_t : 2; /*!< [0..1] Reserved */
			volatile uint16_t SWK_SET: 1; /*!< [2..2] Selective Wake Activity */
			volatile uint16_t CANSIL: 1; /*!< [3..3] CAN Silent Time during SWK operation */
			volatile uint16_t WUF: 1; /*!< [4..4] SWK Wake-up Frame Detection */
			volatile uint16_t WUP: 1; /*!< [5..5] Wake-up Pattern Detection */
			volatile uint16_t SYNC: 1; /*!< [6..6] Synchronisation (at least one CAN frame without fail must have been received) */
			uint16_t : 9; /*!< [7..15] Reserved */
		} bit;
	} SWK_STAT;

	union {
		volatile uint16_t reg; /*!< Register SWK_ECNT_STAT*/

		struct {
			volatile uint16_t ECNT: 6; /*!< [5..0] SWK CAN Frame Error Counter */
			uint16_t : 10; /*!< [6..15] Reserved */
		} bit;
	} SWK_ECNT_STAT;

	union {
		volatile uint16_t reg; /*!< Register SWK_CDR_STAT*/

		struct {
			uint16_t : 4; /*!< [0..3] Reserved */
			volatile uint16_t N_AVG: 12; /*!< [15..4] Output Value from Filter Block */
		} bit;
	} SWK_CDR_STAT;

	union {
		volatile uint16_t reg; /*!< Register SUP_STAT*/

		struct {
			volatile uint16_t VCC1_WARN: 1; /*!< [0..0] VCC1 Undervoltage Prewarning */
			volatile uint16_t VCC1_OV: 1; /*!< [1..1] VCC1 Overvoltage Detection */
			volatile uint16_t VCC1_UV: 1; /*!< [2..2] VCC1 UV-Detection (due to Vrtx reset) */
			volatile uint16_t VCC1_SC: 1; /*!< [3..3] VCC1 SC */
			volatile uint16_t CP_UV: 1; /*!< [4..4] CP_UV */
			volatile uint16_t VS_OV: 1; /*!< [5..5] VS Overvoltage Detection (VS,OV) */
			volatile uint16_t VS_UV: 1; /*!< [6..6] VS Undervoltage Detection (VS,UV) */
			volatile uint16_t VSINT_OV: 1; /*!< [7..7] VSINT OV-Detection */
			volatile uint16_t VSINT_UV: 1; /*!< [8..8] VSINT UV-Detection */
			volatile uint16_t HS_OV: 1; /*!< [9..9] HS Supply OV-Detection */
			volatile uint16_t HS_UV: 1; /*!< [10..10] HS Supply UV-Detection */
			volatile uint16_t VCC1_UV_FS: 1; /*!< [11..11] 4th consecutive VCC1 UV-Detection */
			volatile uint16_t CP_OT: 1; /*!< [12..12] Charge pump overtemperature */
			uint16_t : 2; /*!< [13..14] Reserved */
			volatile uint16_t POR: 1; /*!< [15..15] Power-On reset detection */
		} bit;
	} SUP_STAT;

	union {
		volatile uint16_t reg; /*!< Register THERM_STAT*/

		struct {
			volatile uint16_t TPW: 1; /*!< [0..0] Thermal Pre Warning */
			volatile uint16_t TSD1: 1; /*!< [1..1] TSD1 Thermal Shut-Down Detection */
			volatile uint16_t TSD2: 1; /*!< [2..2] TSD2 Thermal Shut-Down Detection */
			volatile uint16_t TSD2_SAFE: 1; /*!< [3..3] TSD2 Thermal Shut-Down Safe State Detection */
			uint16_t : 12; /*!< [4..15] Reserved */
		} bit;
	} THERM_STAT;

	union {
		volatile uint16_t reg; /*!< Register DEV_STAT*/

		struct {
			volatile uint16_t FAILURE: 1; /*!< [0..0] Failure detection */
			volatile uint16_t SPI_FAIL: 1; /*!< [1..1] SPI Fail Information */
			volatile uint16_t WD_FAIL: 2; /*!< [3..2] Number of WD-Failure Events */
			volatile uint16_t SW_DEV: 1; /*!< [4..4] Status of Operating Mode */
			uint16_t : 1; /*!< [5..5] Reserved */
			volatile uint16_t DEV_STAT: 2; /*!< [7..6] Device Status before Restart Mode */
			volatile uint16_t CRC_FAIL: 1; /*!< [8..8] CRC Fail Information */
			volatile uint16_t CRC_STAT: 1; /*!< [9..9] CRC STAT Information */
			uint16_t : 6; /*!< [10..15] Reserved */
		} bit;
	} DEV_STAT;

	union {
		volatile uint16_t reg; /*!< Register GEN_STAT*/

		struct {
			volatile uint16_t PWM1STAT: 1; /*!< [0..0] PWM1/CRC status */
			uint16_t : 6; /*!< [0..5] Reserved */
			volatile uint16_t HB1VOUT: 1; /*!< [6..6] Voltage level at VSH1 when HB1MODE[1:0] = 11 andCPEN=1 */
			volatile uint16_t HB2VOUT: 1; /*!< [7..7] Voltage level at VSH2 when HB2MODE[1:0] = 11 andCPEN=1 */
			uint16_t : 8; /*!< [8..15] Reserved */
		} bit;
	} GEN_STAT;

	union {
		volatile uint16_t reg; /*!< Register EFF_TDON_OFF1*/

		struct {
			volatile uint16_t TDON1EFF: 6; /*!< [5..0] Effective active MOSFET turn-on delay HB1 */
			uint16_t : 2; /*!< [6..7] Reserved */
			volatile uint16_t TDOFF1EFF: 6; /*!< [13..8] Effective active MOSFET turn-off delay HB1 */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} EFF_TDON_OFF1;

	union {
		volatile uint16_t reg; /*!< Register EFF_TDON_OFF2*/

		struct {
			volatile uint16_t TDON2EFF: 6; /*!< [5..0] Effective active MOSFET turn-on delay HB2 */
			uint16_t : 2; /*!< [6..7] Reserved */
			volatile uint16_t TDOFF2EFF: 6; /*!< [13..8] Effective active MOSFET turn-off delay HB2 */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} EFF_TDON_OFF2;

	union {
		volatile uint16_t reg; /*!< Register TRISE_FALL1*/

		struct {
			volatile uint16_t TRISE1: 6; /*!< [5..0] Active MOSFET rise time HB1 */
			uint16_t : 2; /*!< [6..7] Reserved */
			volatile uint16_t TFALL1: 6; /*!< [13..8] Active MOSFET fall time HB1 */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} TRISE_FALL1;

	union {
		volatile uint16_t reg; /*!< Register TRISE_FALL2*/

		struct {
			volatile uint16_t TRISE2: 6; /*!< [5..0] Active MOSFET rise time HB2 */
			uint16_t : 2; /*!< [6..7] Reserved */
			volatile uint16_t TFALL2: 6; /*!< [13..8] Active MOSFET fall time HB2 */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} TRISE_FALL2;

	union {
		volatile uint16_t reg; /*!< Register HBMODE*/

		struct {
			volatile uint16_t HB1_PWM_EN: 1; /*!< [0..0] PWM mode for half-bridge 1 */
			volatile uint16_t AFW1: 1; /*!< [1..1] Active freewheeling for half-bridge 1 during PWM */
			volatile uint16_t HB1MODE: 2; /*!< [3..2] Half-bridge 1 MODE selection */
			volatile uint16_t HB2_PWM_EN: 1; /*!< [4..4] PWM mode for half-bridge 2 */
			volatile uint16_t AFW2: 1; /*!< [5..5] Active freewheeling for half-bridge 2 during PWM */
			volatile uint16_t HB2MODE: 2; /*!< [7..6] Half-bridge 2 MODE selection */
			uint16_t : 8; /*!< [8..15] Reserved */
		} bit;
	} HBMODE;

	union {
		volatile uint16_t reg; /*!< Register DSOV*/

		struct {
			volatile uint16_t HS1DSOV: 1; /*!< [0..0] Drain-source overvoltage on high-side 1 */
			volatile uint16_t LS1DSOV: 1; /*!< [1..1] Drain-source overvoltage on low-side 1 */
			volatile uint16_t HS2DSOV: 1; /*!< [2..2] Drain-source overvoltage on high-side 2 */
			volatile uint16_t LS2DSOV: 1; /*!< [3..3] Drain-source overvoltage on low-side 2 */
			uint16_t : 4; /*!< [4..7] Reserved */
			volatile uint16_t LS1DSOV_BRK: 1; /*!< [8..8] Drain-source overvoltage on low-side 1 during braking */
			volatile uint16_t LS2DSOV_BRK: 1; /*!< [9..9] Drain-source overvoltage on low-side 2 during braking */
			uint16_t : 2; /*!< [10..11] Reserved */
			volatile uint16_t VSOVBRAKE_ST: 1; /*!< [12..12] VS Brake status */
			volatile uint16_t VSINTOVBRAKE_ST: 1; /*!< [13..13] VSINT Brake status */
			uint16_t : 2; /*!< [14..15] Reserved */
		} bit;
	} DSOV;

	union {
		volatile uint16_t reg; /*!< Register FAM_PROD_STAT*/

		struct {
			volatile uint16_t PROD: 7; /*!< [6..0] Device Product Identifier */
			volatile uint16_t FAM: 4; /*!< [10..7] Device Family Identifier */
			uint16_t : 5; /*!< [11..15] Reserved */
		} bit;
	} FAM_PROD_STAT;

	union {
		volatile uint16_t reg; /*!< Register BRAKE*/

		struct {
			volatile uint16_t OV_BRK_TH: 3; /*!< [2..0] Overvoltage brake threshold */
			uint16_t : 2; /*!< [3..4] Reserved */
			volatile uint16_t OV_BRK_EN: 1; /*!< [5..5] Overvoltage brake enable */
			volatile uint16_t PARK_BRK_EN: 1; /*!< [6..6] Parking brake enable */
			volatile uint16_t TBLK_BRK: 1; /*!< [7..7] Blank time of VDS overvoltage during braking */
			volatile uint16_t VDSTH_BRK: 1; /*!< [8..8] VDS Overvoltage for LS1-2 during braking */
			volatile uint16_t SLAM: 1; /*!< [9..9] SLAM mode */
			volatile uint16_t SLAM_LS1_DIS: 1; /*!< [10..10] LS1 output disable during SLAM mode */
			volatile uint16_t SLAM_LS2_DIS: 1; /*!< [11..11] LS2 output disable during SLAM mode */
			uint16_t : 4; /*!< [12..15] Reserved */
		} bit;
	} BRAKE;

	union {
		volatile uint16_t reg; /*!< Register TPRECHG_BNK0*/

		struct {
			volatile uint16_t TPCHG_BANK: 3; /*!< [2..0] Precharge/predischarge time selection */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t TPCHG1: 3; /*!< [6..4] IF TPCHG_BNK=0: precharge time of HB1, IF TPCHG_BANK=1: predischarge time of HB1 */
			volatile uint16_t TPCHG2: 3; /*!< [9..7] IF TPCHG_BNK=0: precharge time of HB2, IF TPCHG_BANK=1: predischarge time of HB2 */
			uint16_t : 6; /*!< [10..15] Reserved */
		} bit;
	} TPRECHG_BNK0;

	union {
		volatile uint16_t reg; /*!< Register TPRECHG_BNK1*/

		struct {
			volatile uint16_t TPCHG_BANK: 3; /*!< [2..0] Precharge/predischarge time selection */
			uint16_t : 1; /*!< [3..3] Reserved */
			volatile uint16_t TPCHG1: 3; /*!< [6..4] IF TPCHG_BNK=0: precharge time of HB1, IF TPCHG_BANK=1: predischarge time of HB1 */
			volatile uint16_t TPCHG2: 3; /*!< [9..7] IF TPCHG_BNK=0: precharge time of HB2, IF TPCHG_BANK=1: predischarge time of HB2 */
			uint16_t : 6; /*!< [10..15] Reserved */
		} bit;
	} TPRECHG_BNK1;

	union {
		volatile uint16_t reg; /*!< Register TDREG*/

		struct {
			volatile uint16_t TDREG1: 1; /*!< [0..0] HB1 Regulation of turn-on/off delay */
			volatile uint16_t TDREG2: 1; /*!< [1..1] HB2 Regulation of turn-on/off delay */
			uint16_t : 2; /*!< [2..3] Reserved */
			volatile uint16_t IPCHG1_ST: 1; /*!< [4..4] HB1 precharge status */
			volatile uint16_t IPCHG2_ST: 1; /*!< [5..5] HB2 precharge status */
			uint16_t : 2; /*!< [6..7] Reserved */
			volatile uint16_t IPDCHG1_ST: 1; /*!< [8..8] HB1 predischarge status */
			volatile uint16_t IPDCHG2_ST: 1; /*!< [9..9] HB2 predischarge status */
			uint16_t : 6; /*!< [10..15] Reserved */
		} bit;
	} TDREG;
} TLE9560_Type;

extern TLE9560_Type* TLE9560;





#line 20 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_FuncLayer.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_ApplLayer.h"
/**
 *	@file TLE9560_ApplLayer.h
 *	<AUTHOR>	@date
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */

#line 21 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_FuncLayer.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"
/**
 *	@file TLE9560_defines.h
 *	<AUTHOR>	@date 20.01.2023
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */




/************************************************************************
**                             Includes                                **
************************************************************************/

/************************************************************************
**                      Local Macro Definitions                        **
************************************************************************/

/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/

/*
###############
Project configuration
###############
 */
#line 55 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
GENCTRL
###############
*/
#line 89 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
M_S_CTRL
###############
*/

#line 106 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HW_CTRL
###############
*/

#line 127 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
WD_CTRL
###############
*/

#line 144 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
BUS_CTRL
###############
*/
#line 160 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
BUS_STAT
###############
*/

#line 177 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
WK_CTRL_BNK1
###############
*/

#line 194 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
WK_CTRL_BNK2
###############
*/
#line 210 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
WK_CTRL_BNK3
###############
*/

#line 227 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
WK_CTRL_BNK4
###############
*/

#line 244 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
WK_STAT
###############
*/

#line 267 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
WK_LVL_STAT
###############
*/

#line 282 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
TIMER_CTRL
###############
*/

#line 299 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SW_SD_CTRL
###############
*/

#line 330 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HS_CTRL
###############
*/

#line 345 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HS_VDS
###############
*/

#line 358 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HS_OL_OC_OT_STAT
###############
*/

#line 389 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
INT_MASK
###############
*/

#line 414 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
PWM_CTRL_BNK0
###############
*/

#line 427 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
PWM_CTRL_BNK1
###############
*/

#line 440 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
PWM_CTRL_BNK2
###############
*/

#line 453 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
PWM_CTRL_BNK3
###############
*/

#line 466 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SYS_STAT_CTRL
###############
*/




/*
###############
LS_VDS
###############
*/

#line 488 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
CCP_BLK_BNK0
###############
*/

#line 501 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
CCP_BLK_BNK1
###############
*/

#line 514 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
CCP_BLK_BNK4
###############
*/

#line 527 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
CCP_BLK_BNK5
###############
*/

#line 540 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
ST_ICHG
###############
*/






/*
###############
HB_ICHG_BNK0
###############
*/

#line 564 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HB_ICHG_BNK1
###############
*/

#line 577 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HB_ICHG_BNK4
###############
*/

#line 590 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HB_ICHG_BNK5
###############
*/

#line 603 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HB_ICHG_MAX
###############
*/

#line 618 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HB_PCHG_INIT_BNK0
###############
*/

#line 631 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
HB_PCHG_INIT_BNK1
###############
*/

#line 644 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
TDON_HB_CTRL_BNK0
###############
*/






/*
###############
TDON_HB_CTRL_BNK1
###############
*/






/*
###############
TDOFF_HB_CTRL_BNK0
###############
*/






/*
###############
TDOFF_HB_CTRL_BNK1
###############
*/






/*
###############
SWK_CTRL
###############
*/

#line 703 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_BTL1_CTRL
###############
*/






/*
###############
SWK_ID1_CTRL
###############
*/

#line 753 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_ID0_CTRL
###############
*/

#line 790 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_MASK_ID1_CTRL
###############
*/

#line 829 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_MASK_ID0_CTRL
###############
*/

#line 862 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_DLC_CTRL
###############
*/




/*
###############
SWK_DATA3_CTRL
###############
*/






/*
###############
SWK_DATA2_CTRL
###############
*/






/*
###############
SWK_DATA1_CTRL
###############
*/






/*
###############
SWK_DATA0_CTRL
###############
*/






/*
###############
SWK_CAN_FD_CTRL
###############
*/

#line 928 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_OSC_TRIM_CTRL
###############
*/

#line 941 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_OSC_CAL_STAT
###############
*/






/*
###############
SWK_CDR_CTRL
###############
*/

#line 965 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_CDR_LIMIT
###############
*/






/*
###############
SWK_STAT
###############
*/

#line 993 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
SWK_ECNT_STAT
###############
*/




/*
###############
SWK_CDR_STAT
###############
*/




/*
###############
SUP_STAT
###############
*/

#line 1046 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
THERM_STAT
###############
*/

#line 1061 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
DEV_STAT
###############
*/

#line 1082 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
GEN_STAT
###############
*/

#line 1095 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
EFF_TDON_OFF1
###############
*/






/*
###############
EFF_TDON_OFF2
###############
*/






/*
###############
TRISE_FALL1
###############
*/






/*
###############
TRISE_FALL2
###############
*/






/*
###############
HBMODE
###############
*/

#line 1158 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
DSOV
###############
*/

#line 1181 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
FAM_PROD_STAT
###############
*/






/*
###############
BRAKE
###############
*/

#line 1215 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
TPRECHG_BNK0
###############
*/

#line 1228 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
TPRECHG_BNK1
###############
*/

#line 1241 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
TDREG
###############
*/

#line 1260 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_defines.h"

/*
###############
CRC
###############
*/






#line 22 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_FuncLayer.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_RegLayer.h"
/**
 *	@file TLE9560_RegLayer.h
 *	<AUTHOR>	@date 20.01.2023
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */




/************************************************************************
**                             Includes                                **
************************************************************************/


/************************************************************************
**                      Local Macro Definitions                        **
************************************************************************/

#line 103 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_RegLayer.h"

/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

/** \enum tDEVICE_regBank
 * 
 *  \brief Enum for the TLE9562 Resgister Banks
 *  \brief In banked registers, the first 3 bits of the payload select the bank that has to be configured
 */
typedef enum _tDEVICE_regBank
{
  DEVICE_regBank_000 = 0,                                     /*!< Using bank 0 */
  DEVICE_regBank_001 = 1,                                     /*!< Using bank 1 */
  DEVICE_regBank_010 = 2,                                     /*!< Using bank 2 */
  DEVICE_regBank_011 = 3,                                     /*!< Using bank 3 */
  DEVICE_regBank_100 = 4,                                     /*!< Using bank 4 */
  DEVICE_regBank_101 = 5,                                     /*!< Using bank 5 */
  DEVICE_regBank_110 = 6,                                     /*!< Using bank 6 */
  DEVICE_regBank_111 = 7                                      /*!< Using bank 7 */
} tDEVICE_regBank;

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/

void TLE9560_RegLayer_set_GENCTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_GENCTRL(void);
void TLE9560_RegLayer_set_M_S_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_M_S_CTRL(void);
void TLE9560_RegLayer_set_HW_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_HW_CTRL(void);
void TLE9560_RegLayer_set_WD_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_WD_CTRL(void);
void TLE9560_RegLayer_set_BUS_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_BUS_CTRL(void);
void TLE9560_RegLayer_set_BUS_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_BUS_STAT(void);
void TLE9560_RegLayer_set_WK_CTRL_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_WK_CTRL_BNK1(void);
void TLE9560_RegLayer_set_WK_CTRL_BNK2(uint16_t u16_data);
void TLE9560_RegLayer_get_WK_CTRL_BNK2(void);
void TLE9560_RegLayer_set_WK_CTRL_BNK3(uint16_t u16_data);
void TLE9560_RegLayer_get_WK_CTRL_BNK3(void);
void TLE9560_RegLayer_set_WK_CTRL_BNK4(uint16_t u16_data);
void TLE9560_RegLayer_get_WK_CTRL_BNK4(void);
void TLE9560_RegLayer_set_WK_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_WK_STAT(void);
void TLE9560_RegLayer_set_WK_LVL_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_WK_LVL_STAT(void);
void TLE9560_RegLayer_set_TIMER_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_TIMER_CTRL(void);
void TLE9560_RegLayer_set_SW_SD_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SW_SD_CTRL(void);
void TLE9560_RegLayer_set_HS_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_HS_CTRL(void);
void TLE9560_RegLayer_set_HS_VDS(uint16_t u16_data);
void TLE9560_RegLayer_get_HS_VDS(void);
void TLE9560_RegLayer_set_HS_OL_OC_OT_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_HS_OL_OC_OT_STAT(void);
void TLE9560_RegLayer_set_INT_MASK(uint16_t u16_data);
void TLE9560_RegLayer_get_INT_MASK(void);
void TLE9560_RegLayer_set_PWM_CTRL_BNK0(uint16_t u16_data);
void TLE9560_RegLayer_get_PWM_CTRL_BNK0(void);
void TLE9560_RegLayer_set_PWM_CTRL_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_PWM_CTRL_BNK1(void);
void TLE9560_RegLayer_set_PWM_CTRL_BNK2(uint16_t u16_data);
void TLE9560_RegLayer_get_PWM_CTRL_BNK2(void);
void TLE9560_RegLayer_set_PWM_CTRL_BNK3(uint16_t u16_data);
void TLE9560_RegLayer_get_PWM_CTRL_BNK3(void);
void TLE9560_RegLayer_set_SYS_STAT_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SYS_STAT_CTRL(void);
void TLE9560_RegLayer_set_LS_VDS(uint16_t u16_data);
void TLE9560_RegLayer_get_LS_VDS(void);
void TLE9560_RegLayer_set_CCP_BLK_BNK0(uint16_t u16_data);
void TLE9560_RegLayer_get_CCP_BLK_BNK0(void);
void TLE9560_RegLayer_set_CCP_BLK_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_CCP_BLK_BNK1(void);
void TLE9560_RegLayer_set_CCP_BLK_BNK4(uint16_t u16_data);
void TLE9560_RegLayer_get_CCP_BLK_BNK4(void);
void TLE9560_RegLayer_set_CCP_BLK_BNK5(uint16_t u16_data);
void TLE9560_RegLayer_get_CCP_BLK_BNK5(void);
void TLE9560_RegLayer_set_ST_ICHG(uint16_t u16_data);
void TLE9560_RegLayer_get_ST_ICHG(void);
void TLE9560_RegLayer_set_HB_ICHG_BNK0(uint16_t u16_data);
void TLE9560_RegLayer_get_HB_ICHG_BNK0(void);
void TLE9560_RegLayer_set_HB_ICHG_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_HB_ICHG_BNK1(void);
void TLE9560_RegLayer_set_HB_ICHG_BNK4(uint16_t u16_data);
void TLE9560_RegLayer_get_HB_ICHG_BNK4(void);
void TLE9560_RegLayer_set_HB_ICHG_BNK5(uint16_t u16_data);
void TLE9560_RegLayer_get_HB_ICHG_BNK5(void);
void TLE9560_RegLayer_set_HB_ICHG_MAX(uint16_t u16_data);
void TLE9560_RegLayer_get_HB_ICHG_MAX(void);
void TLE9560_RegLayer_set_HB_PCHG_INIT_BNK0(uint16_t u16_data);
void TLE9560_RegLayer_get_HB_PCHG_INIT_BNK0(void);
void TLE9560_RegLayer_set_HB_PCHG_INIT_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_HB_PCHG_INIT_BNK1(void);
void TLE9560_RegLayer_set_TDON_HB_CTRL_BNK0(uint16_t u16_data);
void TLE9560_RegLayer_get_TDON_HB_CTRL_BNK0(void);
void TLE9560_RegLayer_set_TDON_HB_CTRL_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_TDON_HB_CTRL_BNK1(void);
void TLE9560_RegLayer_set_TDOFF_HB_CTRL_BNK0(uint16_t u16_data);
void TLE9560_RegLayer_get_TDOFF_HB_CTRL_BNK0(void);
void TLE9560_RegLayer_set_TDOFF_HB_CTRL_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_TDOFF_HB_CTRL_BNK1(void);
void TLE9560_RegLayer_set_SWK_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_CTRL(void);
void TLE9560_RegLayer_set_SWK_BTL1_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_BTL1_CTRL(void);
void TLE9560_RegLayer_set_SWK_ID1_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_ID1_CTRL(void);
void TLE9560_RegLayer_set_SWK_ID0_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_ID0_CTRL(void);
void TLE9560_RegLayer_set_SWK_MASK_ID1_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_MASK_ID1_CTRL(void);
void TLE9560_RegLayer_set_SWK_MASK_ID0_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_MASK_ID0_CTRL(void);
void TLE9560_RegLayer_set_SWK_DLC_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_DLC_CTRL(void);
void TLE9560_RegLayer_set_SWK_DATA3_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_DATA3_CTRL(void);
void TLE9560_RegLayer_set_SWK_DATA2_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_DATA2_CTRL(void);
void TLE9560_RegLayer_set_SWK_DATA1_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_DATA1_CTRL(void);
void TLE9560_RegLayer_set_SWK_DATA0_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_DATA0_CTRL(void);
void TLE9560_RegLayer_set_SWK_CAN_FD_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_CAN_FD_CTRL(void);
void TLE9560_RegLayer_set_SWK_OSC_TRIM_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_OSC_TRIM_CTRL(void);
void TLE9560_RegLayer_set_SWK_OSC_CAL_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_OSC_CAL_STAT(void);
void TLE9560_RegLayer_set_SWK_CDR_CTRL(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_CDR_CTRL(void);
void TLE9560_RegLayer_set_SWK_CDR_LIMIT(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_CDR_LIMIT(void);
void TLE9560_RegLayer_set_SWK_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_STAT(void);
void TLE9560_RegLayer_set_SWK_ECNT_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_ECNT_STAT(void);
void TLE9560_RegLayer_set_SWK_CDR_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_SWK_CDR_STAT(void);
void TLE9560_RegLayer_set_SUP_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_SUP_STAT(void);
void TLE9560_RegLayer_set_THERM_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_THERM_STAT(void);
void TLE9560_RegLayer_set_DEV_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_DEV_STAT(void);
void TLE9560_RegLayer_set_GEN_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_GEN_STAT(void);
void TLE9560_RegLayer_set_EFF_TDON_OFF1(uint16_t u16_data);
void TLE9560_RegLayer_get_EFF_TDON_OFF1(void);
void TLE9560_RegLayer_set_EFF_TDON_OFF2(uint16_t u16_data);
void TLE9560_RegLayer_get_EFF_TDON_OFF2(void);
void TLE9560_RegLayer_set_TRISE_FALL1(uint16_t u16_data);
void TLE9560_RegLayer_get_TRISE_FALL1(void);
void TLE9560_RegLayer_set_TRISE_FALL2(uint16_t u16_data);
void TLE9560_RegLayer_get_TRISE_FALL2(void);
void TLE9560_RegLayer_set_HBMODE(uint16_t u16_data);
void TLE9560_RegLayer_get_HBMODE(void);
void TLE9560_RegLayer_set_DSOV(uint16_t u16_data);
void TLE9560_RegLayer_get_DSOV(void);
void TLE9560_RegLayer_set_FAM_PROD_STAT(uint16_t u16_data);
void TLE9560_RegLayer_get_FAM_PROD_STAT(void);
void TLE9560_RegLayer_set_BRAKE(uint16_t u16_data);
void TLE9560_RegLayer_get_BRAKE(void);
void TLE9560_RegLayer_set_TPRECHG_BNK0(uint16_t u16_data);
void TLE9560_RegLayer_get_TPRECHG_BNK0(void);
void TLE9560_RegLayer_set_TPRECHG_BNK1(uint16_t u16_data);
void TLE9560_RegLayer_get_TPRECHG_BNK1(void);
void TLE9560_RegLayer_set_TDREG(uint16_t u16_data);
void TLE9560_RegLayer_get_TDREG(void);
void TLE9560_RegLayer_clrSUPSTAT(uint16_t u16_data);
void TLE9560_RegLayer_clrTHERMSTAT(uint16_t u16_data);
void TLE9560_RegLayer_clrDEVSTAT(uint16_t u16_data);
void TLE9560_RegLayer_clrBUSSTAT(uint16_t u16_data);
void TLE9560_RegLayer_clrWKSTAT(uint16_t u16_data);
void TLE9560_RegLayer_clrHSOLOCOTSTAT(uint16_t u16_data);
void TLE9560_RegLayer_clrDSOVSTAT(uint16_t u16_data);
void TLE9560_RegLayer_clrSWKSTAT(uint16_t u16_data);
void TLE9560_RegLayer_resetCrc(uint16_t u16_data);

#line 23 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_FuncLayer.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_ServLayer.h"
/**
 *	@file TLE9560_ServLayer.h
 *	<AUTHOR>	@date 20.01.2023
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */




/************************************************************************
**                             Includes                                **
************************************************************************/






/************************************************************************
**                      Global Macro Definitions                       **
************************************************************************/

/** \brief Indicate that the SPI message is a register-write operation.
*/


/** \brief Indicate that the SPI message is a register-read operation.
*/


/** \brief Start value of the CRC8 calculation.
*/


/** \brief XOR value of the CRC8 calculation.
*/


/** \brief Polynomial used for the CRC8 calculation.
*/


/** \brief If \ref TLE9560_CRC_EN not set, a static pattern is appended on MOSI.
*/


/** \brief If \ref TLE9560_CRC_EN not set, a static pattern is appended on MISO.
*/


/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/
void TLE9560_ServLayer_setReg(uint8_t u8_address, uint16_t u16_data);
void TLE9560_ServLayer_getReg(uint8_t u8_address);
void TLE9560_ServLayer_setBnkReg(uint8_t u8_address, tDEVICE_regBank e_value, uint16_t u16_data);
void TLE9560_ServLayer_getBnkReg(uint8_t u8_address, tDEVICE_regBank e_value);
void TLE9560_ServLayer_InitSpiInterface(void);
void TLE9560_ServLayer_getSpiData(void);
void TLE9560_ServLayer_sendStaticCrcRecovery(void);


#line 24 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_FuncLayer.h"

/************************************************************************
**                      Local Macro Definitions                        **
************************************************************************/

/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/

uint8_t TLE9560_FuncLayer_set_GENCTRL_BDFREQ(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_BDFREQ(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_PWM1MAP(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_PWM1MAP(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_CPUVTH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_CPUVTH(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_FET_LVL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_FET_LVL(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_CPSTGA(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_CPSTGA(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_BDOV_REC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_BDOV_REC(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_IPCHGADT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_IPCHGADT(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_AGC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_AGC(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_CPEN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_CPEN(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_POCHGDIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_POCHGDIS(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_AGCFILT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_AGCFILT(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_EN_GEN_CHECK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_EN_GEN_CHECK(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_IHOLD(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_IHOLD(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL_FMODE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_GENCTRL_FMODE(void);
uint8_t TLE9560_FuncLayer_set_GENCTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_M_S_CTRL_MODE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_M_S_CTRL_MODE(void);
uint8_t TLE9560_FuncLayer_set_M_S_CTRL_VCC1_OV_MOD(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_M_S_CTRL_VCC1_OV_MOD(void);
uint8_t TLE9560_FuncLayer_set_M_S_CTRL_RSTN_HYS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_M_S_CTRL_RSTN_HYS(void);
uint8_t TLE9560_FuncLayer_set_M_S_CTRL_I_PEAK_TH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_M_S_CTRL_I_PEAK_TH(void);
uint8_t TLE9560_FuncLayer_set_M_S_CTRL_VCC1_RT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_M_S_CTRL_VCC1_RT(void);
uint8_t TLE9560_FuncLayer_set_M_S_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HW_CTRL_TSD2_DEL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HW_CTRL_TSD2_DEL(void);
uint8_t TLE9560_FuncLayer_set_HW_CTRL_VS_OV_SEL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HW_CTRL_VS_OV_SEL(void);
uint8_t TLE9560_FuncLayer_set_HW_CTRL_SH_DISABLE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HW_CTRL_SH_DISABLE(void);
uint8_t TLE9560_FuncLayer_set_HW_CTRL_RSTN_DEL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HW_CTRL_RSTN_DEL(void);
uint8_t TLE9560_FuncLayer_set_HW_CTRL_SOFT_RESET_RO(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HW_CTRL_SOFT_RESET_RO(void);
uint8_t TLE9560_FuncLayer_set_HW_CTRL_FO_ON(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HW_CTRL_FO_ON(void);
uint8_t TLE9560_FuncLayer_set_HW_CTRL_WD_STM_EN_1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HW_CTRL_WD_STM_EN_1(void);
uint8_t TLE9560_FuncLayer_set_HW_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_WD_CTRL_CHECKSUM(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WD_CTRL_CHECKSUM(void);
uint8_t TLE9560_FuncLayer_set_WD_CTRL_WD_STM_EN_0(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WD_CTRL_WD_STM_EN_0(void);
uint8_t TLE9560_FuncLayer_set_WD_CTRL_WD_CFG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WD_CTRL_WD_CFG(void);
uint8_t TLE9560_FuncLayer_set_WD_CTRL_WD_EN_WK_BUS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WD_CTRL_WD_EN_WK_BUS(void);
uint8_t TLE9560_FuncLayer_set_WD_CTRL_WD_TIMER(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WD_CTRL_WD_TIMER(void);
uint8_t TLE9560_FuncLayer_set_WD_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_BUS_CTRL_LIN_FLASH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BUS_CTRL_LIN_FLASH(void);
uint8_t TLE9560_FuncLayer_set_BUS_CTRL_LIN_LSM(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BUS_CTRL_LIN_LSM(void);
uint8_t TLE9560_FuncLayer_set_BUS_CTRL_LIN_TXD_TO(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BUS_CTRL_LIN_TXD_TO(void);
uint8_t TLE9560_FuncLayer_set_BUS_CTRL_LIN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BUS_CTRL_LIN(void);
uint8_t TLE9560_FuncLayer_set_BUS_CTRL_CAN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BUS_CTRL_CAN(void);
uint8_t TLE9560_FuncLayer_set_BUS_CTRL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BUS_STAT_LIN_FAIL(void);
uint16_t TLE9560_FuncLayer_get_BUS_STAT_CANTO(void);
uint16_t TLE9560_FuncLayer_get_BUS_STAT_SYSERR(void);
uint16_t TLE9560_FuncLayer_get_BUS_STAT_CAN_FAIL(void);
uint16_t TLE9560_FuncLayer_get_BUS_STAT_VCAN_UV(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK1_WK2_FO(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK1_WK2_FO(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK1_WK_FILT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK1_WK_FILT(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK1_WK_PUPD(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK1_WK_PUPD(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK1_WK_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK1_WK_EN(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK1_WK_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK1_WK_BNK(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK1(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK2_WK2_FO(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK2_WK2_FO(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK2_WK_FILT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK2_WK_FILT(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK2_WK_PUPD(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK2_WK_PUPD(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK2_WK_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK2_WK_EN(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK2_WK_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK2_WK_BNK(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK2(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK3_WK2_FO(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK3_WK2_FO(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK3_WK_FILT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK3_WK_FILT(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK3_WK_PUPD(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK3_WK_PUPD(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK3_WK_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK3_WK_EN(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK3_WK_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK3_WK_BNK(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK3(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK4_WK2_FO(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK4_WK2_FO(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK4_WK_FILT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK4_WK_FILT(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK4_WK_PUPD(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK4_WK_PUPD(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK4_WK_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK4_WK_EN(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK4_WK_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_CTRL_BNK4_WK_BNK(void);
uint8_t TLE9560_FuncLayer_set_WK_CTRL_BNK4(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_WK_STAT_LIN_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_STAT_CAN_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_STAT_TIMER2_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_STAT_TIMER1_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_STAT_WK4_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_STAT_WK3_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_STAT_WK2_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_STAT_WK1_WU(void);
uint16_t TLE9560_FuncLayer_get_WK_LVL_STAT_WK4_LVL(void);
uint16_t TLE9560_FuncLayer_get_WK_LVL_STAT_WK3_LVL(void);
uint16_t TLE9560_FuncLayer_get_WK_LVL_STAT_WK2_LVL(void);
uint16_t TLE9560_FuncLayer_get_WK_LVL_STAT_WK1_LVL(void);
uint8_t TLE9560_FuncLayer_set_TIMER_CTRL_TIMER2_ON(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TIMER_CTRL_TIMER2_ON(void);
uint8_t TLE9560_FuncLayer_set_TIMER_CTRL_TIMER2_PER(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TIMER_CTRL_TIMER2_PER(void);
uint8_t TLE9560_FuncLayer_set_TIMER_CTRL_CYCWK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TIMER_CTRL_CYCWK(void);
uint8_t TLE9560_FuncLayer_set_TIMER_CTRL_TIMER1_ON(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TIMER_CTRL_TIMER1_ON(void);
uint8_t TLE9560_FuncLayer_set_TIMER_CTRL_TIMER1_PER(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TIMER_CTRL_TIMER1_PER(void);
uint8_t TLE9560_FuncLayer_set_TIMER_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS4_OV_REC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS4_OV_REC(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS3_OV_REC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS3_OV_REC(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS2_OV_REC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS2_OV_REC(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS1_OV_REC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS1_OV_REC(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS_OT_SD_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS_OT_SD_DIS(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS4_OV_SDN_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS4_OV_SDN_DIS(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS3_OV_SDN_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS3_OV_SDN_DIS(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS2_OV_SDN_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS2_OV_SDN_DIS(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS1_OV_SDN_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS1_OV_SDN_DIS(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS_OV_SDS_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS_OV_SDS_DIS(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS_UV_SD_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS_UV_SD_DIS(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL_HS_UV_REC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SW_SD_CTRL_HS_UV_REC(void);
uint8_t TLE9560_FuncLayer_set_SW_SD_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HS_CTRL_HS4(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_CTRL_HS4(void);
uint8_t TLE9560_FuncLayer_set_HS_CTRL_HS3(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_CTRL_HS3(void);
uint8_t TLE9560_FuncLayer_set_HS_CTRL_HS2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_CTRL_HS2(void);
uint8_t TLE9560_FuncLayer_set_HS_CTRL_HS1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_CTRL_HS1(void);
uint8_t TLE9560_FuncLayer_set_HS_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HS_VDS_DEEP_ADAP(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_VDS_DEEP_ADAP(void);
uint8_t TLE9560_FuncLayer_set_HS_VDS_HS2VDSTH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_VDS_HS2VDSTH(void);
uint8_t TLE9560_FuncLayer_set_HS_VDS_HS1VDSTH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_VDS_HS1VDSTH(void);
uint8_t TLE9560_FuncLayer_set_HS_VDS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS4_OT(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS3_OT(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS2_OT(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS1_OT(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS4_OL(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS3_OL(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS2_OL(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS1_OL(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS4_OC(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS3_OC(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS2_OC(void);
uint16_t TLE9560_FuncLayer_get_HS_OL_OC_OT_STAT_HS1_OC(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_INTN_CYC_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_INTN_CYC_EN(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_WD_SDM_DISABLE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_WD_SDM_DISABLE(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_WD_SDM(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_WD_SDM(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_SPI_CRC_FAIL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_SPI_CRC_FAIL(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_BD_STAT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_BD_STAT(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_HS_STAT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_HS_STAT(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_BUS_STAT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_BUS_STAT(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_TEMP_STAT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_TEMP_STAT(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK_SUPPLY_STAT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_INT_MASK_SUPPLY_STAT(void);
uint8_t TLE9560_FuncLayer_set_INT_MASK(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK0_PWM_FREQ(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK0_PWM_FREQ(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK0_PWM_DC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK0_PWM_DC(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK0_PWM_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK0_PWM_BNK(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK0(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK1_PWM_FREQ(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK1_PWM_FREQ(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK1_PWM_DC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK1_PWM_DC(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK1_PWM_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK1_PWM_BNK(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK1(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK2_PWM_FREQ(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK2_PWM_FREQ(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK2_PWM_DC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK2_PWM_DC(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK2_PWM_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK2_PWM_BNK(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK2(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK3_PWM_FREQ(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK3_PWM_FREQ(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK3_PWM_DC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK3_PWM_DC(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK3_PWM_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_PWM_CTRL_BNK3_PWM_BNK(void);
uint8_t TLE9560_FuncLayer_set_PWM_CTRL_BNK3(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SYS_STAT_CTRL_SYS_STAT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SYS_STAT_CTRL_SYS_STAT(void);
uint8_t TLE9560_FuncLayer_set_SYS_STAT_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_LS_VDS_TFVDS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_LS_VDS_TFVDS(void);
uint8_t TLE9560_FuncLayer_set_LS_VDS_LS2VDSTH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_LS_VDS_LS2VDSTH(void);
uint8_t TLE9560_FuncLayer_set_LS_VDS_LS1VDSTH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_LS_VDS_LS1VDSTH(void);
uint8_t TLE9560_FuncLayer_set_LS_VDS(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK0_TBLANK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK0_TBLANK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK0_TCCP(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK0_TCCP(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK0_CCP_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK0_CCP_BNK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK0(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK1_TBLANK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK1_TBLANK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK1_TCCP(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK1_TCCP(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK1_CCP_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK1_CCP_BNK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK1(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK4_TBLANK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK4_TBLANK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK4_TCCP(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK4_TCCP(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK4_CCP_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK4_CCP_BNK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK4(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK5_TBLANK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK5_TBLANK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK5_TCCP(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK5_TCCP(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK5_CCP_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_CCP_BLK_BNK5_CCP_BNK(void);
uint8_t TLE9560_FuncLayer_set_CCP_BLK_BNK5(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_ST_ICHG_ICHGST2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_ST_ICHG_ICHGST2(void);
uint8_t TLE9560_FuncLayer_set_ST_ICHG_ICHGST1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_ST_ICHG_ICHGST1(void);
uint8_t TLE9560_FuncLayer_set_ST_ICHG(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK0_IDCHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK0_IDCHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK0_ICHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK0_ICHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK0_ICHG_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK0_ICHG_BNK(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK0(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK1_IDCHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK1_IDCHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK1_ICHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK1_ICHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK1_ICHG_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK1_ICHG_BNK(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK1(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK4_IDCHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK4_IDCHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK4_ICHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK4_ICHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK4_ICHG_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK4_ICHG_BNK(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK4(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK5_IDCHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK5_IDCHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK5_ICHG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK5_ICHG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK5_ICHG_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_BNK5_ICHG_BNK(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_BNK5(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_MAX_HB2IDIAG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_MAX_HB2IDIAG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_MAX_HB1IDIAG(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_MAX_HB1IDIAG(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_MAX_ICHGMAX2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_MAX_ICHGMAX2(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_MAX_ICHGMAX1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_ICHG_MAX_ICHGMAX1(void);
uint8_t TLE9560_FuncLayer_set_HB_ICHG_MAX(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK0_PDCHGINIT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_PCHG_INIT_BNK0_PDCHGINIT(void);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK0_PCHGINIT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_PCHG_INIT_BNK0_PCHGINIT(void);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK0_INIT_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_PCHG_INIT_BNK0_INIT_BNK(void);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK0(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK1_PDCHGINIT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_PCHG_INIT_BNK1_PDCHGINIT(void);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK1_PCHGINIT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_PCHG_INIT_BNK1_PCHGINIT(void);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK1_INIT_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HB_PCHG_INIT_BNK1_INIT_BNK(void);
uint8_t TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK1(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK0_TDON(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDON_HB_CTRL_BNK0_TDON(void);
uint8_t TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK0_HB_TDON_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDON_HB_CTRL_BNK0_HB_TDON_BNK(void);
uint8_t TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK0(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK1_TDON(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDON_HB_CTRL_BNK1_TDON(void);
uint8_t TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK1_HB_TDON_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDON_HB_CTRL_BNK1_HB_TDON_BNK(void);
uint8_t TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK1(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK0_TDOFF(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDOFF_HB_CTRL_BNK0_TDOFF(void);
uint8_t TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK0_HB_TDOFF_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDOFF_HB_CTRL_BNK0_HB_TDOFF_BNK(void);
uint8_t TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK0(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK1_TDOFF(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDOFF_HB_CTRL_BNK1_TDOFF(void);
uint8_t TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK1_HB_TDOFF_BNK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDOFF_HB_CTRL_BNK1_HB_TDOFF_BNK(void);
uint8_t TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK1(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_CTRL_OSC_CAL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CTRL_OSC_CAL(void);
uint8_t TLE9560_FuncLayer_set_SWK_CTRL_TRIM_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CTRL_TRIM_EN(void);
uint8_t TLE9560_FuncLayer_set_SWK_CTRL_CANTO_MASK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CTRL_CANTO_MASK(void);
uint8_t TLE9560_FuncLayer_set_SWK_CTRL_CFG_VAL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CTRL_CFG_VAL(void);
uint8_t TLE9560_FuncLayer_set_SWK_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_BTL1_CTRL_SP(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_BTL1_CTRL_SP(void);
uint8_t TLE9560_FuncLayer_set_SWK_BTL1_CTRL_TBIT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_BTL1_CTRL_TBIT(void);
uint8_t TLE9560_FuncLayer_set_SWK_BTL1_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID28(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID28(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID27(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID27(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID26(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID26(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID25(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID25(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID24(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID24(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID23(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID23(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID22(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID22(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID21(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID21(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID20(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID20(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID19(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID19(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID18(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID18(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID17(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID17(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID16(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID16(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID15(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID15(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID14(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID14(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL_ID13(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID1_CTRL_ID13(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID1_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID12(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID12(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID11(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID11(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID10(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID10(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID9(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID9(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID8(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID8(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID7(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID7(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID6(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID6(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID5(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID5(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID4(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID4(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID3(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID3(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID2(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID1(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_ID0(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_ID0(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_RTR(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_RTR(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL_IDE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_ID0_CTRL_IDE(void);
uint8_t TLE9560_FuncLayer_set_SWK_ID0_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID28(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID28(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID27(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID27(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID26(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID26(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID25(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID25(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID24(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID24(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID23(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID23(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID22(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID22(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID21(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID21(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID20(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID20(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID19(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID19(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID18(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID18(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID17(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID17(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID16(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID16(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID15(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID15(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID14(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID14(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL_MASK_ID13(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID1_CTRL_MASK_ID13(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID12(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID12(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID11(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID11(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID10(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID10(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID9(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID9(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID8(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID8(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID7(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID7(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID6(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID6(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID5(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID5(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID4(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID4(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID3(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID3(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID2(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID1(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL_MASK_ID0(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_MASK_ID0_CTRL_MASK_ID0(void);
uint8_t TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_DLC_CTRL_DLC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DLC_CTRL_DLC(void);
uint8_t TLE9560_FuncLayer_set_SWK_DLC_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_DATA3_CTRL_DATA7(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA3_CTRL_DATA7(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA3_CTRL_DATA6(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA3_CTRL_DATA6(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA3_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_DATA2_CTRL_DATA5(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA2_CTRL_DATA5(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA2_CTRL_DATA4(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA2_CTRL_DATA4(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA2_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_DATA1_CTRL_DATA3(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA1_CTRL_DATA3(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA1_CTRL_DATA2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA1_CTRL_DATA2(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA1_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_DATA0_CTRL_DATA1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA0_CTRL_DATA1(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA0_CTRL_DATA0(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_DATA0_CTRL_DATA0(void);
uint8_t TLE9560_FuncLayer_set_SWK_DATA0_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_CAN_FD_CTRL_DIS_ERR_CNT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CAN_FD_CTRL_DIS_ERR_CNT(void);
uint8_t TLE9560_FuncLayer_set_SWK_CAN_FD_CTRL_FD_FILTER(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CAN_FD_CTRL_FD_FILTER(void);
uint8_t TLE9560_FuncLayer_set_SWK_CAN_FD_CTRL_CAN_FD_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CAN_FD_CTRL_CAN_FD_EN(void);
uint8_t TLE9560_FuncLayer_set_SWK_CAN_FD_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_OSC_TRIM_CTRL_RX_WK_SEL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_OSC_TRIM_CTRL_RX_WK_SEL(void);
uint8_t TLE9560_FuncLayer_set_SWK_OSC_TRIM_CTRL_TEMP_COEF(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_OSC_TRIM_CTRL_TEMP_COEF(void);
uint8_t TLE9560_FuncLayer_set_SWK_OSC_TRIM_CTRL_TRIM_OSC(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_OSC_TRIM_CTRL_TRIM_OSC(void);
uint8_t TLE9560_FuncLayer_set_SWK_OSC_TRIM_CTRL(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_OSC_CAL_STAT_OSC_CAL_H(void);
uint16_t TLE9560_FuncLayer_get_SWK_OSC_CAL_STAT_OSC_CAL_L(void);
uint8_t TLE9560_FuncLayer_set_SWK_CDR_CTRL_SEL_OSC_CLK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CDR_CTRL_SEL_OSC_CLK(void);
uint8_t TLE9560_FuncLayer_set_SWK_CDR_CTRL_SELFILT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CDR_CTRL_SELFILT(void);
uint8_t TLE9560_FuncLayer_set_SWK_CDR_CTRL_CDR_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CDR_CTRL_CDR_EN(void);
uint8_t TLE9560_FuncLayer_set_SWK_CDR_CTRL(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_SWK_CDR_LIMIT_CDR_LIM_H(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CDR_LIMIT_CDR_LIM_H(void);
uint8_t TLE9560_FuncLayer_set_SWK_CDR_LIMIT_CDR_LIM_L(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_CDR_LIMIT_CDR_LIM_L(void);
uint8_t TLE9560_FuncLayer_set_SWK_CDR_LIMIT(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_SWK_STAT_SYNC(void);
uint16_t TLE9560_FuncLayer_get_SWK_STAT_WUP(void);
uint16_t TLE9560_FuncLayer_get_SWK_STAT_WUF(void);
uint16_t TLE9560_FuncLayer_get_SWK_STAT_CANSIL(void);
uint16_t TLE9560_FuncLayer_get_SWK_STAT_SWK_SET(void);
uint16_t TLE9560_FuncLayer_get_SWK_ECNT_STAT_ECNT(void);
uint16_t TLE9560_FuncLayer_get_SWK_CDR_STAT_N_AVG(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_POR(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_CP_OT(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VCC1_UV_FS(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_HS_UV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_HS_OV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VSINT_UV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VSINT_OV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VS_UV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VS_OV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_CP_UV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VCC1_SC(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VCC1_UV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VCC1_OV(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT_VCC1_WARN(void);
uint16_t TLE9560_FuncLayer_get_SUP_STAT(void);
uint16_t TLE9560_FuncLayer_get_THERM_STAT_TSD2_SAFE(void);
uint16_t TLE9560_FuncLayer_get_THERM_STAT_TSD2(void);
uint16_t TLE9560_FuncLayer_get_THERM_STAT_TSD1(void);
uint16_t TLE9560_FuncLayer_get_THERM_STAT_TPW(void);
uint16_t TLE9560_FuncLayer_get_THERM_STAT(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT_CRC_STAT(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT_CRC_FAIL(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT_DEV_STAT(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT_SW_DEV(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT_WD_FAIL(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT_SPI_FAIL(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT_FAILURE(void);
uint16_t TLE9560_FuncLayer_get_DEV_STAT(void);
uint16_t TLE9560_FuncLayer_get_GEN_STAT_HB2VOUT(void);
uint16_t TLE9560_FuncLayer_get_GEN_STAT_HB1VOUT(void);
uint16_t TLE9560_FuncLayer_get_GEN_STAT_PWM1STAT(void);
uint16_t TLE9560_FuncLayer_get_EFF_TDON_OFF1_TDOFF1EFF(void);
uint16_t TLE9560_FuncLayer_get_EFF_TDON_OFF1_TDON1EFF(void);
uint16_t TLE9560_FuncLayer_get_EFF_TDON_OFF2_TDOFF2EFF(void);
uint16_t TLE9560_FuncLayer_get_EFF_TDON_OFF2_TDON2EFF(void);
uint16_t TLE9560_FuncLayer_get_TRISE_FALL1_TFALL1(void);
uint16_t TLE9560_FuncLayer_get_TRISE_FALL1_TRISE1(void);
uint16_t TLE9560_FuncLayer_get_TRISE_FALL2_TFALL2(void);
uint16_t TLE9560_FuncLayer_get_TRISE_FALL2_TRISE2(void);
uint8_t TLE9560_FuncLayer_set_HBMODE_HB2MODE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HBMODE_HB2MODE(void);
uint8_t TLE9560_FuncLayer_set_HBMODE_AFW2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HBMODE_AFW2(void);
uint8_t TLE9560_FuncLayer_set_HBMODE_HB2_PWM_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HBMODE_HB2_PWM_EN(void);
uint8_t TLE9560_FuncLayer_set_HBMODE_HB1MODE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HBMODE_HB1MODE(void);
uint8_t TLE9560_FuncLayer_set_HBMODE_AFW1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HBMODE_AFW1(void);
uint8_t TLE9560_FuncLayer_set_HBMODE_HB1_PWM_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_HBMODE_HB1_PWM_EN(void);
uint8_t TLE9560_FuncLayer_set_HBMODE(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_DSOV_VSINTOVBRAKE_ST(void);
uint16_t TLE9560_FuncLayer_get_DSOV_VSOVBRAKE_ST(void);
uint16_t TLE9560_FuncLayer_get_DSOV_LS2DSOV_BRK(void);
uint16_t TLE9560_FuncLayer_get_DSOV_LS1DSOV_BRK(void);
uint16_t TLE9560_FuncLayer_get_DSOV_LS2DSOV(void);
uint16_t TLE9560_FuncLayer_get_DSOV_HS2DSOV(void);
uint16_t TLE9560_FuncLayer_get_DSOV_LS1DSOV(void);
uint16_t TLE9560_FuncLayer_get_DSOV_HS1DSOV(void);
uint16_t TLE9560_FuncLayer_get_FAM_PROD_STAT_FAM(void);
uint16_t TLE9560_FuncLayer_get_FAM_PROD_STAT_PROD(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_SLAM_LS2_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_SLAM_LS2_DIS(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_SLAM_LS1_DIS(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_SLAM_LS1_DIS(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_SLAM(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_SLAM(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_VDSTH_BRK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_VDSTH_BRK(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_TBLK_BRK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_TBLK_BRK(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_PARK_BRK_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_PARK_BRK_EN(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_OV_BRK_EN(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_OV_BRK_EN(void);
uint8_t TLE9560_FuncLayer_set_BRAKE_OV_BRK_TH(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_BRAKE_OV_BRK_TH(void);
uint8_t TLE9560_FuncLayer_set_BRAKE(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK0_TPCHG2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TPRECHG_BNK0_TPCHG2(void);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK0_TPCHG1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TPRECHG_BNK0_TPCHG1(void);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK0_TPCHG_BANK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TPRECHG_BNK0_TPCHG_BANK(void);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK0(uint16_t u16_data);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK1_TPCHG2(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TPRECHG_BNK1_TPCHG2(void);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK1_TPCHG1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TPRECHG_BNK1_TPCHG1(void);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK1_TPCHG_BANK(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TPRECHG_BNK1_TPCHG_BANK(void);
uint8_t TLE9560_FuncLayer_set_TPRECHG_BNK1(uint16_t u16_data);
uint16_t TLE9560_FuncLayer_get_TDREG_IPDCHG2_ST(void);
uint16_t TLE9560_FuncLayer_get_TDREG_IPDCHG1_ST(void);
uint16_t TLE9560_FuncLayer_get_TDREG_IPCHG2_ST(void);
uint16_t TLE9560_FuncLayer_get_TDREG_IPCHG1_ST(void);
uint16_t TLE9560_FuncLayer_get_TDREG_TDREG2(void);
uint16_t TLE9560_FuncLayer_get_TDREG_TDREG1(void);
uint8_t TLE9560_FuncLayer_clrSupSts(void);
uint8_t TLE9560_FuncLayer_clrThermSts(void);
uint8_t TLE9560_FuncLayer_clrDevSts(void);
uint8_t TLE9560_FuncLayer_clrBusSts(void);
uint8_t TLE9560_FuncLayer_clrWkSts(void);
uint8_t TLE9560_FuncLayer_clrHsOlOcOtSts(void);
uint8_t TLE9560_FuncLayer_clrDsovSts(void);
uint8_t TLE9560_FuncLayer_clrSwkSts(void);
uint8_t TLE9560_FuncLayer_recoverCrc(void);

#line 21 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_ApplLayer.h"
#line 1 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
/**
 *	@file TLE9560_defines.h
 *	<AUTHOR>	@date
 *	@brief TLE9560 specific device driver implementation
 *
 ************************************************************************
 *
 *
 ************************************************************************
 */




/************************************************************************
**                             Includes                                **
************************************************************************/

/************************************************************************
**                      Local Macro Definitions                        **
************************************************************************/

/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/
//#define TLE9560_GENCTRL_Init          (0010 1000 1010 0001)
#line 72 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


//#define TLE9560_LS_VDS_Init           (0010 0000 0001 0001)
#line 81 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

//#define TLE9560_HS_VDS_Init           (0010 0000 0001 0001)          
#line 89 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

//#define TLE9560_CCP_BLK_BNK0_Init 	    (0001 0001 0000 0100) 0x1104 /*!< Init value for register CCP_BLK_BNK0*/
#line 97 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

//#define TLE9560_CCP_BLK_BNK1_Init 		(0001 0001 0000 0101) 0x1105 /*!< Init value for register CCP_BLK_BNK1*/
#line 105 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

//#define TLE9560_CCP_BLK_BNK4_Init 		(0010 1000 0000 0000) 0x2800 /*!< Init value for register CCP_BLK_BNK4*/
#line 113 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

//#define TLE9560_CCP_BLK_BNK5_Init 		(0010 1000 0000 0001) 0x2801 /*!< Init value for register CCP_BLK_BNK5*/
#line 121 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

//#define TLE9560_HBMODE_Init 			    (0000 0000 0010 0010)0x22 /*!< Init value for register HBMODE*/
#line 135 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_TPRECHG_BNK0_Init 		(0000 0000) 0x0 /*!< Init value for register TPRECHG_BNK0*/
#line 143 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
// #define TLE9560_TPRECHG_BNK1_Init 		(0000 0000) 0x0 /*!< Init value for register TPRECHG_BNK1*/
#line 150 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_ST_ICHG_Init 			(0000 0000 0110 0110) 0x0066 /*!< Init value for register ST_ICHG*/






// #define TLE9560_HB_ICHG_BNK0_Init 		(0101 1101 0110 0000) 0x5D60 /*!< Init value for register HB_ICHG_BNK0*/
#line 165 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_HB_ICHG_BNK1_Init 		(0101 1101 0110 0001) 0x5D61 /*!< Init value for register HB_ICHG_BNK1*/
#line 173 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_HB_ICHG_BNK4_Init 		(1111 1111 1111 0100) 0xFFF4 /*!< Init value for register HB_ICHG_BNK4*/
#line 182 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_HB_ICHG_BNK5_Init 		(1111 1111 1111 0101) 0xFFF5 /*!< Init value for register HB_ICHG_BNK5*/
#line 191 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_HB_ICHG_MAX_Init 		(0000 0000) 0x0 /*!< Init value for register HB_ICHG_MAX*/
#line 202 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_HB_PCHG_INIT_BNK0_Init 	    (1010 1001 0110 0000)0xA960 /*!< Init value for register HB_PCHG_INIT_BNK0*/
#line 211 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_HB_PCHG_INIT_BNK1_Init 	    (1010 1001 0110 0001) 0xA961 /*!< Init value for register HB_PCHG_INIT_BNK1*/
#line 219 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_TDON_HB_CTRL_BNK0_Init 	    (0000 0110 0000 0000) 0x0600 /*!< Init value for register TDON_HB_CTRL_BNK0*/





// #define TLE9560_TDON_HB_CTRL_BNK1_Init 	    (0000 0110 0000 0001) 0x0601 /*!< Init value for register TDON_HB_CTRL_BNK1*/





// #define TLE9560_TDOFF_HB_CTRL_BNK0_Init          (0001 0111 0000 0000) 0x1700 /*!< Init value for register TDOFF_HB_CTRL_BNK0*/





// #define TLE9560_TDOFF_HB_CTRL_BNK1_Init          (0001 0111 0000 0001) 0x1701 /*!< Init value for register TDOFF_HB_CTRL_BNK1*/






// #define TLE9560_BRAKE_Init 				0xa0 /*!< Init value for register BRAKE*/
#line 263 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
       
//#define TLE9560_M_S_CTRL_Init 		    (0000 0110 0000 0011) 0x0603 /*!< Init value for register M_S_CTRL*/                                        
#line 275 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


#line 291 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


//#define TLE9560_WD_CTRL_Init 			    (0000 0000 0001 0100) 0x14 /*!< Init value for register WD_CTRL*/
#line 304 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
                                            
                                           

// #define TLE9560_BUS_CTRL_Init 			(0000 0000 0001 1000) 0x0018 /*!< Init value for register BUS_CTRL*/
#line 318 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
                                            
                                            
// #define TLE9560_WK_CTRL_BNK1_Init 		(0000 0000 0010 0000) 0x20 /*!< Init value for register WK_CTRL_BNK1*/ 0010 0000
#line 331 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_WK_CTRL_BNK2_Init 		(0000 0000 0010 0000) 0x20 /*!< Init value for register WK_CTRL_BNK2*/
#line 343 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_WK_CTRL_BNK3_Init 		(0000 0000 0010 0000) 0x20 /*!< Init value for register WK_CTRL_BNK3*/
#line 356 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_WK_CTRL_BNK4_Init 		(0000 0000 0010 0000) 0x20 /*!< Init value for register WK_CTRL_BNK4*/
#line 369 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_TIMER_CTRL_Init 		    (0000) 0x0 /*!< Init value for register TIMER_CTRL*/
#line 382 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"


// #define TLE9560_SW_SD_CTRL_Init 		       (0000) 0x0 /*!< Init value for register SW_SD_CTRL*/
#line 409 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_HS_CTRL_Init 		       (0000) 0x0 /*!< Init value for register HS_CTRL*/
#line 419 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
                                           
// #define TLE9560_INT_MASK_Init 			    (0000 0001 0100 0000) 0x140 /*!< Init value for register INT_MASK*/
#line 439 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
                                           

// #define TLE9560_PWM_CTRL_BNK0_Init 	    	(0000) 0x0 /*!< Init value for register PWM_CTRL_BNK0*/
#line 448 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
// #define TLE9560_PWM_CTRL_BNK1_Init 	    	(0000) 0x0 /*!< Init value for register PWM_CTRL_BNK1*/
#line 455 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_PWM_CTRL_BNK2_Init 	    	(0000) 0x0 /*!< Init value for register PWM_CTRL_BNK2*/
#line 463 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"

// #define TLE9560_PWM_CTRL_BNK3_Init 	    	(0000) 0x0 /*!< Init value for register PWM_CTRL_BNK3*/
#line 471 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"



//#define TLE9560_GENCTRL_Init			0x28A1 /*!< Init value for register GENCTRL*/
//#define TLE9560_M_S_CTRL_Init 		0x0603 /*!< Init value for register M_S_CTRL*/
// #define TLE9560_HW_CTRL_Init 			0x0040 /*!< Init value for register HW_CTRL*/
// #define TLE9560_WD_CTRL_Init 			0x14 /*!< Init value for register WD_CTRL*/
// #define TLE9560_BUS_CTRL_Init 			0x0018 /*!< Init value for register BUS_CTRL*/

// #define TLE9560_WK_CTRL_BNK1_Init 		0x20 /*!< Init value for register WK_CTRL_BNK1*/
// #define TLE9560_WK_CTRL_BNK2_Init 		0x20 /*!< Init value for register WK_CTRL_BNK2*/
// #define TLE9560_WK_CTRL_BNK3_Init 		0x20 /*!< Init value for register WK_CTRL_BNK3*/
// #define TLE9560_WK_CTRL_BNK4_Init 		0x20 /*!< Init value for register WK_CTRL_BNK4*/


// #define TLE9560_TIMER_CTRL_Init 		0x0 /*!< Init value for register TIMER_CTRL*/
// #define TLE9560_SW_SD_CTRL_Init 		0x0 /*!< Init value for register SW_SD_CTRL*/
// #define TLE9560_HS_CTRL_Init 			0x0 /*!< Init value for register HS_CTRL*/
//#define TLE9560_HS_VDS_Init 			0x2011 /*!< Init value for register HS_VDS*/

// #define TLE9560_INT_MASK_Init 			0x140 /*!< Init value for register INT_MASK*/
// #define TLE9560_PWM_CTRL_BNK0_Init 		0x0 /*!< Init value for register PWM_CTRL_BNK0*/
// #define TLE9560_PWM_CTRL_BNK1_Init 		0x0 /*!< Init value for register PWM_CTRL_BNK1*/
// #define TLE9560_PWM_CTRL_BNK2_Init 		0x0 /*!< Init value for register PWM_CTRL_BNK2*/
// #define TLE9560_PWM_CTRL_BNK3_Init 		0x0 /*!< Init value for register PWM_CTRL_BNK3*/

//#define TLE9560_LS_VDS_Init 			0x2011 /*!< Init value for register LS_VDS*/
// #define TLE9560_CCP_BLK_BNK0_Init 		0x1104 /*!< Init value for register CCP_BLK_BNK0*/
// #define TLE9560_CCP_BLK_BNK1_Init 		0x1105 /*!< Init value for register CCP_BLK_BNK1*/
// #define TLE9560_CCP_BLK_BNK4_Init 		0x2800 /*!< Init value for register CCP_BLK_BNK4*/
// #define TLE9560_CCP_BLK_BNK5_Init 		0x2801 /*!< Init value for register CCP_BLK_BNK5*/
// #define TLE9560_ST_ICHG_Init 			0x0066 /*!< Init value for register ST_ICHG*/
// #define TLE9560_HB_ICHG_BNK0_Init 		0x5D60 /*!< Init value for register HB_ICHG_BNK0*/
// #define TLE9560_HB_ICHG_BNK1_Init 		0x5D61 /*!< Init value for register HB_ICHG_BNK1*/
// #define TLE9560_HB_ICHG_BNK4_Init 		0xFFF4 /*!< Init value for register HB_ICHG_BNK4*/
// #define TLE9560_HB_ICHG_BNK5_Init 		0xFFF5 /*!< Init value for register HB_ICHG_BNK5*/
// #define TLE9560_HB_ICHG_MAX_Init 		0x0 /*!< Init value for register HB_ICHG_MAX*/
// #define TLE9560_HB_PCHG_INIT_BNK0_Init 	0xA960 /*!< Init value for register HB_PCHG_INIT_BNK0*/
// #define TLE9560_HB_PCHG_INIT_BNK1_Init 	0xA961 /*!< Init value for register HB_PCHG_INIT_BNK1*/
// #define TLE9560_TDON_HB_CTRL_BNK0_Init 	0x0600 /*!< Init value for register TDON_HB_CTRL_BNK0*/
// #define TLE9560_TDON_HB_CTRL_BNK1_Init 	0x0601 /*!< Init value for register TDON_HB_CTRL_BNK1*/
// #define TLE9560_TDOFF_HB_CTRL_BNK0_Init 0x1700 /*!< Init value for register TDOFF_HB_CTRL_BNK0*/
// #define TLE9560_TDOFF_HB_CTRL_BNK1_Init 0x1701 /*!< Init value for register TDOFF_HB_CTRL_BNK1*/
#line 541 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_init_vals.h"
//#define TLE9560_HBMODE_Init 			0x22 /*!< Init value for register HBMODE*/


// #define TLE9560_BRAKE_Init 				0xa0 /*!< Init value for register BRAKE*/
// #define TLE9560_TPRECHG_BNK0_Init 		0x0 /*!< Init value for register TPRECHG_BNK0*/
// #define TLE9560_TPRECHG_BNK1_Init 		0x0 /*!< Init value for register TPRECHG_BNK1*/
#line 22 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\Code\\TLE9560_ApplLayer.h"

/************************************************************************
**                      Local Macro Definitions                        **
************************************************************************/

/** \brief Indication that the initialization process is currently ongoing.
*/


/** \brief Indication that the cyclic update is currently ongoing.
*/


/** \brief Indication that the error log is initialized.
*/


/** \brief Indication that the error log is started.
*/


/** \brief Indication that the device driver is still waiting for new data.
*/


/** \brief Indication that an SPI error occured (e.g. CRC mismatch).
*/


/** \brief Number of status registers to be updated by the cyclic function.
*/


/** \brief Indication that new SPI data are available.
*/


/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

/** \brief function pointer fpDEVICE_setDeviceReg
*/
typedef void (*fpDEVICE_setDeviceReg)(uint16_t);

/** \brief function pointer fpDEVICE_getDeviceReg
*/
typedef void (*fpDEVICE_getDeviceReg)(void);

/** \brief function pointer fpDEVICE_updateRamReg
*/
typedef void (*fpDEVICE_updateRamReg)(uint16_t);

/** \struct sDEVICE_deviceDriver
 *  \brief Struct for Device Driver cyclic task
 */
typedef struct _sDEVICE_deviceDriver
{
  uint8_t u8_deviceDriverStatus;                              /*!<  device driver status */
  uint8_t u8_deviceDriverErrorLog;                            /*!<  device driver error log */
  uint8_t u8_statusSPI;                                       /*!<  status of the SPI interface */
  uint8_t u8_SpiRx[4];                                        /*!<  content of last received SPI message */
  uint8_t u8_CrcResult;                                       /*!<  expected result for crc of last received SPI message */
  fpDEVICE_setDeviceReg fp_setReg;                            /*!<  function pointer to next set register function */
  fpDEVICE_getDeviceReg fp_getReg;                            /*!<  function pointer to next get register function */
  fpDEVICE_updateRamReg fp_updateRamReg;                      /*!<  function pointer to next update RAM register function */
  uint16_t u16_setBitValue;                                   /*!<  value to be used in next set register function */
} sDEVICE_deviceDriver;

extern sDEVICE_deviceDriver s_deviceDriver;

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/

uint8_t TLE9560_ApplLayer_deviceDriverCyclicTask(void);
uint8_t TLE9560_ApplLayer_Init(void);
uint16_t TLE9560_getSIF(void);
uint8_t TLE9560_ApplLayer_serveWatchdog(void);
_Bool TLE9560_ApplLayer_GetStatus(void);

#line 18 "C:/Users/<USER>/MBVan/MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC/MBDevStrm/SwApplication/Application/HardwareAbstractionLayer/TLE9560/Code/TLE9560_ApplLayer.c"



/************************************************************************
**                      Local Macro Definitions                        **
************************************************************************/

/************************************************************************
**                      Local Type Definitions                         **
************************************************************************/

/************************************************************************
**                    Local Function Definitions                       **
************************************************************************/
void TLE9560_ApplLayer_update_BUS_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_WK_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_WK_LVL_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_HS_OL_OC_OT_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_SWK_OSC_CAL_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_SWK_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_SWK_ECNT_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_SWK_CDR_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_SUP_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_THERM_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_DEV_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_GEN_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_EFF_TD_ON_OFF1(uint16_t u16_data);
void TLE9560_ApplLayer_update_EFF_TD_ON_OFF2(uint16_t u16_data);
void TLE9560_ApplLayer_update_TRISE_FALL1(uint16_t u16_data);
void TLE9560_ApplLayer_update_TRISE_FALL2(uint16_t u16_data);
void TLE9560_ApplLayer_update_TDREG(uint16_t u16_data);
void TLE9560_ApplLayer_update_DSOV(uint16_t u16_data);
void TLE9560_ApplLayer_update_FAM_PROD_STAT(uint16_t u16_data);
void TLE9560_ApplLayer_update_STICHG(uint16_t u16_data);

/************************************************************************
**                    Local Variable Definitions                       **
************************************************************************/
static fpDEVICE_getDeviceReg fp_cyclicStatGetFuncs[] = {  &TLE9560_RegLayer_get_DSOV,
                                                          &TLE9560_RegLayer_get_BUS_STAT,
                                                          &TLE9560_RegLayer_get_WK_STAT,
                                                          &TLE9560_RegLayer_get_WK_LVL_STAT,
                                                          &TLE9560_RegLayer_get_HS_OL_OC_OT_STAT,
                                                          &TLE9560_RegLayer_get_SWK_OSC_CAL_STAT,
                                                          &TLE9560_RegLayer_get_SWK_STAT,
                                                          &TLE9560_RegLayer_get_SWK_ECNT_STAT,
                                                          &TLE9560_RegLayer_get_SWK_CDR_STAT,
                                                          &TLE9560_RegLayer_get_SUP_STAT,
                                                          &TLE9560_RegLayer_get_THERM_STAT,
                                                          &TLE9560_RegLayer_get_DEV_STAT,
                                                          &TLE9560_RegLayer_get_GEN_STAT,
                                                          &TLE9560_RegLayer_get_EFF_TDON_OFF1,
                                                          &TLE9560_RegLayer_get_EFF_TDON_OFF2,
                                                          &TLE9560_RegLayer_get_TRISE_FALL1,
                                                          &TLE9560_RegLayer_get_TRISE_FALL2,
                                                          &TLE9560_RegLayer_get_TDREG,
                                                          &TLE9560_RegLayer_get_FAM_PROD_STAT
                                                          };

static fpDEVICE_updateRamReg fp_cyclicStatUpdateFuncs[] = { &TLE9560_ApplLayer_update_DSOV,
                                                            &TLE9560_ApplLayer_update_BUS_STAT,
                                                            &TLE9560_ApplLayer_update_WK_STAT,
                                                            &TLE9560_ApplLayer_update_WK_LVL_STAT,
                                                            &TLE9560_ApplLayer_update_HS_OL_OC_OT_STAT,
                                                            &TLE9560_ApplLayer_update_SWK_OSC_CAL_STAT,
                                                            &TLE9560_ApplLayer_update_SWK_STAT,
                                                            &TLE9560_ApplLayer_update_SWK_ECNT_STAT,
                                                            &TLE9560_ApplLayer_update_SWK_CDR_STAT,
                                                            &TLE9560_ApplLayer_update_SUP_STAT,
                                                            &TLE9560_ApplLayer_update_THERM_STAT,
                                                            &TLE9560_ApplLayer_update_DEV_STAT,
                                                            &TLE9560_ApplLayer_update_GEN_STAT,
                                                            &TLE9560_ApplLayer_update_EFF_TD_ON_OFF1,
                                                            &TLE9560_ApplLayer_update_EFF_TD_ON_OFF2,
                                                            &TLE9560_ApplLayer_update_TRISE_FALL1,
                                                            &TLE9560_ApplLayer_update_TRISE_FALL2,
                                                            &TLE9560_ApplLayer_update_TDREG,
                                                            &TLE9560_ApplLayer_update_FAM_PROD_STAT
                                                            };

/************************************************************************
**                    Global Variable Definitions                      **
************************************************************************/

sDEVICE_deviceDriver s_deviceDriver = { (0u),
                                               (0u),
                                               0u,
                                               {0, 0, 0, 0},
                                               0u,
                                               0,
                                               0,
                                               0,
                                               0u,
                                             };

typedef uint8_t (*fpDEVICE_initDeviceReg)(uint16_t); /* polyspace MISRA2012:8.2 [Justified: Low] "Given due to function pointer" */ 

/************************************************************************
**                    Global Function Definitions                      **
************************************************************************/

uint8_t TLE9560_ApplLayer_deviceDriverCyclicTask(void)
{ /*vcast_internal_start*/
extern unsigned char R_11_1;
extern unsigned char SBF_11_1;
if(SBF_11_1) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_2201903509
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_2201903509
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_11( 11, 1, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_2201903509
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_2201903509
  vCAST_USER_CODE_TIMER_START();
  return R_11_1;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/
 
  static uint8_t u8_cyclicStatUpdateCount = 0;
  uint16_t u16_tempRegData;
  
  switch(s_deviceDriver.u8_deviceDriverStatus)
  {
  case (0u):
    /* dynamic set register requests to write to control registers have highest priority */
    if(s_deviceDriver.fp_setReg != 0)
    {
      s_deviceDriver.u8_deviceDriverStatus = (1u);
      s_deviceDriver.fp_setReg(s_deviceDriver.u16_setBitValue);
      s_deviceDriver.fp_setReg = 0;
      s_deviceDriver.u8_deviceDriverErrorLog = (1u);
      break;
    }
    /* cyclic get register requests to update status registers have lowest priority */
    else if(s_deviceDriver.fp_getReg != 0)
    {
      s_deviceDriver.u8_deviceDriverStatus = (1u);
      s_deviceDriver.fp_getReg = fp_cyclicStatGetFuncs[u8_cyclicStatUpdateCount];
      s_deviceDriver.fp_updateRamReg = fp_cyclicStatUpdateFuncs[u8_cyclicStatUpdateCount];
      s_deviceDriver.fp_getReg();
      if(u8_cyclicStatUpdateCount < (17u))
      {
        u8_cyclicStatUpdateCount++;
      }
      else
      {
        u8_cyclicStatUpdateCount = 0;
      }
      s_deviceDriver.u8_deviceDriverErrorLog = (1u);
      break;
    }
    else
    {
      s_deviceDriver.u8_deviceDriverErrorLog = (0u);
      s_deviceDriver.u8_deviceDriverStatus = (0u);
      break;
    }
    break;
    
  case (1u):
    if(s_deviceDriver.u8_statusSPI == (1u))
    {
      /* clear statusSPI for next rx sequence */
      s_deviceDriver.u8_statusSPI = 0;
      /* get SPI Data */
      TLE9560_ServLayer_getSpiData();
      /* clear device driver error log */
      s_deviceDriver.u8_deviceDriverErrorLog = (0u);
      /* check CRC of received SPI message*/

      if(s_deviceDriver.u8_SpiRx[3] == s_deviceDriver.u8_CrcResult)



      {
        /* update SIF */
        TLE9560->SIF.byte = s_deviceDriver.u8_SpiRx[0];
        /* update status register, if provided */
        if(s_deviceDriver.fp_updateRamReg != 0)
        {
          u16_tempRegData = (uint16_t)(((uint16_t)s_deviceDriver.u8_SpiRx[2] << 8) | ((uint16_t)s_deviceDriver.u8_SpiRx[1]));
          s_deviceDriver.fp_updateRamReg(u16_tempRegData);
          s_deviceDriver.fp_updateRamReg = 0;
        }
      }
      else
      {
        s_deviceDriver.u8_deviceDriverErrorLog = (3u);
      }

      /* Start next SPI command */
      /* dynamic set register requests to write to control registers have highest priority */
      if(s_deviceDriver.fp_setReg != 0)
      {
        s_deviceDriver.u8_deviceDriverStatus = (1u);
        s_deviceDriver.fp_setReg(s_deviceDriver.u16_setBitValue);
        s_deviceDriver.fp_setReg = 0;
        s_deviceDriver.u8_deviceDriverErrorLog = s_deviceDriver.u8_deviceDriverErrorLog | (1u);
        break;
      }
      /* cyclic get register requests to update status registers have lowest priority */
      else if(s_deviceDriver.fp_getReg != 0)
      {
        s_deviceDriver.u8_deviceDriverStatus = (1u);
        s_deviceDriver.fp_getReg = fp_cyclicStatGetFuncs[u8_cyclicStatUpdateCount];
        s_deviceDriver.fp_updateRamReg = fp_cyclicStatUpdateFuncs[u8_cyclicStatUpdateCount];
        s_deviceDriver.fp_getReg();
        if(u8_cyclicStatUpdateCount < (17u))
        {
          u8_cyclicStatUpdateCount ++;
        }
        else
        {
          u8_cyclicStatUpdateCount = 0;
        }
        s_deviceDriver.u8_deviceDriverErrorLog = s_deviceDriver.u8_deviceDriverErrorLog | (1u);
        break;
      }
      else
      {
        s_deviceDriver.u8_deviceDriverStatus = (0u);
        break;
      }
    }
    else
    {
      s_deviceDriver.u8_deviceDriverErrorLog = (2u);
    }
    break;
    
  default:
    /* invalid device driver status */
    break;
  }
  
  return s_deviceDriver.u8_deviceDriverErrorLog;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}
uint8_t TLE9560_ApplLayer_Init(void)
{ /*vcast_internal_start*/
extern unsigned char R_11_2;
extern unsigned char SBF_11_2;
if(SBF_11_2) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_2241741858
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_2241741858
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_11( 11, 2, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_2241741858
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_2241741858
  vCAST_USER_CODE_TIMER_START();
  return R_11_2;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

    uint8_t u8_success = 0;
    fpDEVICE_initDeviceReg fp_initRegTemp;
    uint8_t u8_initRegisterCounter;
    uint8_t u8_initRegisterNum;
    uint8_t u8_initStatUpdateCount = 0;
    uint16_t u16_initRegValueTemp;
    
    fpDEVICE_initDeviceReg fp_initRegAll[] = {&TLE9560_FuncLayer_set_TPRECHG_BNK0, &TLE9560_FuncLayer_set_TPRECHG_BNK1, &TLE9560_FuncLayer_set_GENCTRL, &TLE9560_FuncLayer_set_M_S_CTRL, &TLE9560_FuncLayer_set_HW_CTRL, /*&TLE9560_FuncLayer_set_WD_CTRL,*/ &TLE9560_FuncLayer_set_BUS_CTRL, &TLE9560_FuncLayer_set_WK_CTRL_BNK1, &TLE9560_FuncLayer_set_WK_CTRL_BNK2,
                                              &TLE9560_FuncLayer_set_WK_CTRL_BNK3, &TLE9560_FuncLayer_set_WK_CTRL_BNK4, &TLE9560_FuncLayer_set_TIMER_CTRL, &TLE9560_FuncLayer_set_SW_SD_CTRL, &TLE9560_FuncLayer_set_HS_CTRL,
                                              &TLE9560_FuncLayer_set_HS_VDS, &TLE9560_FuncLayer_set_INT_MASK, &TLE9560_FuncLayer_set_PWM_CTRL_BNK0, &TLE9560_FuncLayer_set_PWM_CTRL_BNK1, &TLE9560_FuncLayer_set_PWM_CTRL_BNK2, &TLE9560_FuncLayer_set_PWM_CTRL_BNK3,
                                              &TLE9560_FuncLayer_set_SYS_STAT_CTRL, &TLE9560_FuncLayer_set_LS_VDS, &TLE9560_FuncLayer_set_CCP_BLK_BNK0, &TLE9560_FuncLayer_set_CCP_BLK_BNK1, &TLE9560_FuncLayer_set_CCP_BLK_BNK4, &TLE9560_FuncLayer_set_CCP_BLK_BNK5, &TLE9560_FuncLayer_set_ST_ICHG,
                                              &TLE9560_FuncLayer_set_HB_ICHG_BNK0, &TLE9560_FuncLayer_set_HB_ICHG_BNK1, &TLE9560_FuncLayer_set_HB_ICHG_BNK4, &TLE9560_FuncLayer_set_HB_ICHG_BNK5, &TLE9560_FuncLayer_set_HB_ICHG_MAX, &TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK0, &TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK1,
                                              &TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK0, &TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK1, &TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK0, &TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK1, &TLE9560_FuncLayer_set_SWK_CTRL, &TLE9560_FuncLayer_set_SWK_BTL1_CTRL,
                                              &TLE9560_FuncLayer_set_SWK_ID1_CTRL, &TLE9560_FuncLayer_set_SWK_ID0_CTRL, &TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL, &TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL, &TLE9560_FuncLayer_set_SWK_DLC_CTRL, &TLE9560_FuncLayer_set_SWK_DATA3_CTRL, &TLE9560_FuncLayer_set_SWK_DATA2_CTRL,
                                              &TLE9560_FuncLayer_set_SWK_DATA1_CTRL, &TLE9560_FuncLayer_set_SWK_DATA0_CTRL, &TLE9560_FuncLayer_set_SWK_CAN_FD_CTRL, &TLE9560_FuncLayer_set_SWK_OSC_TRIM_CTRL, &TLE9560_FuncLayer_set_SWK_CDR_CTRL, &TLE9560_FuncLayer_set_SWK_CDR_LIMIT};
    
    uint16_t u16_initRegValueAll[] = {(((0x03 << 7u) & 0x380u) | ((0x03 << 4u) & 0x70u) | ((0x00 << 0u) & 0x7u)), (((0x03 << 7u) & 0x380u) | ((0x03 << 4u) & 0x70u) | ((0x00 << 0u) & 0x7u)), (((0x00 << 15u) & 0x8000u) | ((0x01 << 13u) & 0x2000u) | ((0x00 << 12u) & 0x1000u) | ((0x01 << 11u) & 0x800u) | ((0x00 << 10u) & 0x400u) | ((0x00 << 9u) & 0x200u) | ((0x00 << 8u) & 0x100u) | ((0x02 << 6u) & 0xc0u) | ((0x01 << 5u) & 0x20u) | ((0x00 << 4u) & 0x10u) | ((0x00 << 3u) & 0x8u) | ((0x00 << 2u) & 0x4u) | ((0x00 << 1u) & 0x2u) | ((0x01 << 0u) & 0x1u)), (((0x00 << 14u) & 0xc000u)| ((0x03 << 9u) & 0x600u) | ((0x0 << 7u) & 0x80u ) | ((0x0 << 5u) & 0x20u ) | ((0x02 << 0u) & 0x3u )), (((0x01 << 12u) & 0x1000u) | ((0x00 << 11u) & 0x800u) | ((0x00 << 10u) & 0x400u) | ((0x00 << 9u) & 0x200u) | ((0x00 << 6u) & 0x40u) | ((0x00 << 5u) & 0x20u) | ((0x00 << 2u) & 0x4u)), /*TLE9560_WD_CTRL_Init,*/ (((0x00 << 7u) & 0x80u)| ((0x00 << 6u) & 0x40u)| ((0x00 << 5u) & 0x20u)| ((0x03 << 3u) & 0x18u)| ((0x00 << 0u) & 0x7u)), (((0x00 << 15u) & 0x8000u) | ((0x01 << 11u) & 0x3800u) | ((0x00 << 9u) & 0x600u) | ((0x01 << 5u) & 0x60u) | ((0x00 << 0u) & 0x7u)), (((0x00 << 15u) & 0x8000u) | ((0x01 << 11u) & 0x3800u) | ((0x00 << 9u) & 0x600u) | ((0x00 << 5u) & 0x60u) | ((0x00 << 0u) & 0x7u)),
                                              (((0x00 << 15u) & 0x8000u) | ((0x01 << 11u) & 0x3800u) | ((0x00 << 9u) & 0x600u) | ((0x00 << 5u) & 0x60u) | ((0x00 << 0u) & 0x7u)), (((0x00 << 15u) & 0x8000u) | ((0x01 << 11u) & 0x3800u) | ((0x00 << 9u) & 0x600u) | ((0x00 << 5u) & 0x60u) | ((0x00 << 0u) & 0x7u)), (((0 << 13u) & 0xe000u) | ((0 << 9u) & 0xe00u) | ((0 << 7u) & 0x180u) | ((0 << 4u) & 0x70u) | ((0 << 0u) & 0x7u)), (((0x00 << 15u) & 0x8000u) | ((0x00 << 14u) & 0x4000u) | ((0x01 << 13u) & 0x2000u) | ((0x00 << 12u) & 0x1000u) | ((0x01 << 11u) & 0x800u) | ((0x00 << 10u) & 0x400u) | ((0x00 << 9u) & 0x200u) | ((0x00 << 8u) & 0x100u) | ((0x00 << 7u) & 0x80u) | ((0x00 << 6u) & 0x40u) | ((0x01 << 5u) & 0x20u) | ((0x01 << 3u) & 0x8u)), (((0x00 << 12u) & 0xf000u)| ((0x00 << 8u) & 0xf00u)| ((0x00 << 4u) & 0xf0u)| ((0x00 << 0u) & 0xfu)),
                                              (((0x02 << 12u) & 0x1000u) | ((0x02 << 3u) & 0x38u) | ((0x02 << 0u) & 0x7u)), (((0x00 << 8u) & 0x100u) | ((0x00 << 7u) & 0x80u) | ((0x00 << 6u) & 0x40u) | ((0x00 << 5u) & 0x20u) | ((0x00 << 4u) & 0x10u) | ((0x00 << 3u) & 0x8u) | ((0x00 << 2u) & 0x4u) | ((0x00 << 1u) & 0x2u) | ((0x00 << 0u) & 0x1u)), (((0x00 << 14u) & 0x4000u) | ((0x00 << 4u) & 0x3ff0u) | ((0x00 << 0u) & 0x7u)), (((0x00 << 14u) & 0x4000u) | ((0x00 << 4u) & 0x3ff0u) | ((0x00 << 0u) & 0x7u)), (((0x00 << 14u) & 0x4000u) | ((0x00 << 4u) & 0x3ff0u) | ((0x00 << 0u) & 0x7u)), (((0x00 << 14u) & 0x4000u) | ((0x00 << 4u) & 0x3ff0u) | ((0x00 << 0u) & 0x7u)),
                                              0x0, (((0x02 << 12u) & 0x3000u) | ((0x02 << 3u) & 0x38u) | ((0x02 << 0u) & 0x7u)), (((0x02 << 12u) & 0xf000u) | ((0x03 << 8u) & 0xf00u) | ((0x00 << 0u) & 0x7u)), (((0x02 << 12u) & 0xf000u) | ((0x03 << 8u) & 0xf00u) | ((0x00 << 0u) & 0x7u)), (((0x01 << 12u) & 0xf000u) | ((0x01 << 8u) & 0xf00u) | ((0x00 << 0u) & 0x7u)), (((0x01 << 12u) & 0xf000u) | ((0x01 << 8u) & 0xf00u) | ((0x00 << 0u) & 0x7u)), (((0x0F << 4u) & 0xf0u) | ((0x0F << 0u) & 0xfu)),
                                              (((0x11 << 10u) & 0xfc00u) | ((0x0f << 4u) & 0x3f0u) | ((0x00 << 0u) & 0x7u)), (((0x11 << 10u) & 0xfc00u) | ((0x1f << 4u) & 0x3f0u) | ((0x00 << 0u) & 0x7u)), (((0x2A << 10u) & 0xfc00u) | ((0x2A << 4u) & 0x3f0u) | ((0x00 << 0u) & 0x7u)), (((0x2A << 10u) & 0xfc00u) | ((0x2A << 4u) & 0x3f0u) | ((0x00 << 0u) & 0x7u)), (((0x00 << 13u) & 0x2000u) | ((0x00 << 12u) & 0x1000u) | ((0x01 << 2u) & 0xcu) | ((0x01 << 0u) & 0x3u)), (((0x36 << 10u) & 0xfc00u) | ((0x11 << 4u) & 0x3f0u) | ((0x00 << 0u) & 0x7u)), (((0x36 << 10u) & 0xfc00u) | ((0x11 << 4u) & 0x3f0u) | ((0x00 << 0u) & 0x7u)),
                                              (((0x08 << 8u) & 0x3f00u) | ((0x00 << 0u) & 0x7u)), (((0x08 << 8u) & 0x3f00u) | ((0x00 << 0u) & 0x7u)), (((0x09 << 8u) & 0x3f00u) | ((0x00 << 0u) & 0x7u)), (((0x09 << 8u) & 0x3f00u) | ((0x00 << 0u) & 0x7u)), 0x0, 0xcc96,
                                              0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
                                              0x0, 0x0, 0x0, 0x0, 0x4, 0x9d8f};
    
    
    TLE9560_ServLayer_InitSpiInterface();
  
  /* Clear POR */
  (void)TLE9560_FuncLayer_clrSupSts();
  (void)TLE9560_ApplLayer_deviceDriverCyclicTask();
  while(TLE9560_ApplLayer_deviceDriverCyclicTask() == (2u))
  {
    // MISRA
  }
  

  /* set CRC according to users settings */
  (void)TLE9560_FuncLayer_recoverCrc();
  (void)TLE9560_ApplLayer_deviceDriverCyclicTask();
  while(TLE9560_ApplLayer_deviceDriverCyclicTask() == (2u))
  {
    // MISRA
  }
  /* clear DEV_STAT in case there was a CRC mismatch before */
  (void)TLE9560_FuncLayer_clrDevSts();
  (void)TLE9560_ApplLayer_deviceDriverCyclicTask();
  while(TLE9560_ApplLayer_deviceDriverCyclicTask() == (2u))
  {
    // MISRA
  }
  /* Clear POR in case there was a CRC mismatch before and first POR clear cmd was ignored */
  (void)TLE9560_FuncLayer_clrSupSts();
  (void)TLE9560_ApplLayer_deviceDriverCyclicTask();
  while(TLE9560_ApplLayer_deviceDriverCyclicTask() == (2u))
  {
    // MISRA
  }

  
  /* Initialize all control registers */
  u8_initRegisterNum = (uint8_t)(sizeof(u16_initRegValueAll)/sizeof(u16_initRegValueAll[0]));
  for(u8_initRegisterCounter = 0; u8_initRegisterCounter < u8_initRegisterNum; u8_initRegisterCounter++)
  {
    fp_initRegTemp = fp_initRegAll[u8_initRegisterCounter];
    u16_initRegValueTemp = u16_initRegValueAll[u8_initRegisterCounter];
    fp_initRegTemp(u16_initRegValueTemp);
    (void)TLE9560_ApplLayer_deviceDriverCyclicTask();
    while(TLE9560_ApplLayer_deviceDriverCyclicTask() == (2u))
    {
      // MISRA
    }
    if((s_deviceDriver.u8_deviceDriverErrorLog == (3u)) || (TLE9560->SIF.bit.SPI_CRC_FAIL == 1))
    {
      u8_success = 0;
      return u8_success;
    }
  }
  /* Initialize first get-register functionpointer */
  s_deviceDriver.fp_getReg = &TLE9560_RegLayer_get_BUS_STAT;
  s_deviceDriver.fp_updateRamReg = &TLE9560_ApplLayer_update_BUS_STAT;
  /* Read out all status registers */
  for(u8_initStatUpdateCount = 0; u8_initStatUpdateCount < (17u); u8_initStatUpdateCount++)
  {
    (void)TLE9560_ApplLayer_deviceDriverCyclicTask();
    while(TLE9560_ApplLayer_deviceDriverCyclicTask() == (2u))
    {
      // MISRA
    }
    if((s_deviceDriver.u8_deviceDriverErrorLog == (3u)) || (TLE9560->SIF.bit.SPI_CRC_FAIL == 1))
    {
      u8_success = 0;
      return u8_success;
    }
  }
  /* Init Watchdog to close long open window after start up */
//  (void)TLE9560_FuncLayer_set_WD_CTRL((uint16_t)TLE9560_WD_CTRL_Init);
//  (void)TLE9560_ApplLayer_deviceDriverCyclicTask();
//  while(TLE9560_ApplLayer_deviceDriverCyclicTask() == TLE9560_DEVICEDRIVER_ERRORLOG_BUSY)
//  {
//    // MISRA
//  }
  /* Final check for SPI error */
  if((s_deviceDriver.u8_deviceDriverErrorLog == (1u)) && (TLE9560->SIF.bit.SPI_CRC_FAIL == 0))
  {
    u8_success = 1;
  }
  
  return u8_success;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Serve the Watchdog
*
*   \return void
*/
uint8_t TLE9560_ApplLayer_serveWatchdog(void)
{ /*vcast_internal_start*/
extern unsigned char R_11_3;
extern unsigned char SBF_11_3;
if(SBF_11_3) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_2043495055
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_2043495055
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_11( 11, 3, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_2043495055
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_2043495055
  vCAST_USER_CODE_TIMER_START();
  return R_11_3;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  uint8_t u8_success = 0;
  /* if no set-register request is set, start set-register request */
  if(s_deviceDriver.fp_setReg == 0)
  {
    s_deviceDriver.u16_setBitValue = TLE9560->WD_CTRL.reg;
    s_deviceDriver.fp_setReg = &TLE9560_RegLayer_set_WD_CTRL;
    u8_success = 1;
  }
  return u8_success;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

uint16_t TLE9560_getSIF()
{ /*vcast_internal_start*/
extern unsigned short R_11_4;
extern unsigned char SBF_11_4;
if(SBF_11_4) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_3236817531
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_3236817531
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_11( 11, 4, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_3236817531
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_3236817531
  vCAST_USER_CODE_TIMER_START();
  return R_11_4;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  return TLE9560->SIF.byte;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of BUS_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_BUS_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_5_1;
extern unsigned char SBF_11_5;
if(SBF_11_5) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_100476369
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_100476369
  if ( vcast_is_in_driver ) {
    P_11_5_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 5, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_100476369
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_100476369
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->BUS_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of WK_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_WK_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_6_1;
extern unsigned char SBF_11_6;
if(SBF_11_6) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_774055156
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_774055156
  if ( vcast_is_in_driver ) {
    P_11_6_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 6, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_774055156
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_774055156
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->WK_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of WK_LVL_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_WK_LVL_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_7_1;
extern unsigned char SBF_11_7;
if(SBF_11_7) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_3143112598
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_3143112598
  if ( vcast_is_in_driver ) {
    P_11_7_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 7, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_3143112598
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_3143112598
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->WK_LVL_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of HS_OL_OC_OT_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_HS_OL_OC_OT_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_8_1;
extern unsigned char SBF_11_8;
if(SBF_11_8) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_3035804164
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_3035804164
  if ( vcast_is_in_driver ) {
    P_11_8_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 8, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_3035804164
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_3035804164
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->HS_OL_OC_OT_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of SWK_OSC_CAL_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_SWK_OSC_CAL_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_9_1;
extern unsigned char SBF_11_9;
if(SBF_11_9) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_4035516922
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_4035516922
  if ( vcast_is_in_driver ) {
    P_11_9_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 9, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_4035516922
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_4035516922
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->SWK_OSC_CAL_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of SWK_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_SWK_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_10_1;
extern unsigned char SBF_11_10;
if(SBF_11_10) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_3455485371
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_3455485371
  if ( vcast_is_in_driver ) {
    P_11_10_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 10, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_3455485371
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_3455485371
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->SWK_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of SWK_ECNT_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_SWK_ECNT_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_11_1;
extern unsigned char SBF_11_11;
if(SBF_11_11) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_1718208707
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_1718208707
  if ( vcast_is_in_driver ) {
    P_11_11_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 11, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_1718208707
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_1718208707
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->SWK_ECNT_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of SWK_CDR_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_SWK_CDR_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_12_1;
extern unsigned char SBF_11_12;
if(SBF_11_12) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_1969493650
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_1969493650
  if ( vcast_is_in_driver ) {
    P_11_12_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 12, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_1969493650
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_1969493650
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->SWK_CDR_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of SUP_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_SUP_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_13_1;
extern unsigned char SBF_11_13;
if(SBF_11_13) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_863528650
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_863528650
  if ( vcast_is_in_driver ) {
    P_11_13_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 13, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_863528650
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_863528650
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->SUP_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of THERM_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_THERM_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_14_1;
extern unsigned char SBF_11_14;
if(SBF_11_14) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_263855289
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_263855289
  if ( vcast_is_in_driver ) {
    P_11_14_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 14, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_263855289
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_263855289
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->THERM_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of DEV_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_DEV_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_15_1;
extern unsigned char SBF_11_15;
if(SBF_11_15) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_4102103339
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_4102103339
  if ( vcast_is_in_driver ) {
    P_11_15_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 15, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_4102103339
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_4102103339
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->DEV_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of GEN_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_GEN_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_16_1;
extern unsigned char SBF_11_16;
if(SBF_11_16) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_2508957246
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_2508957246
  if ( vcast_is_in_driver ) {
    P_11_16_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 16, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_2508957246
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_2508957246
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->GEN_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of EFF_TDON_OFF1
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_EFF_TD_ON_OFF1(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_17_1;
extern unsigned char SBF_11_17;
if(SBF_11_17) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_1720051831
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_1720051831
  if ( vcast_is_in_driver ) {
    P_11_17_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 17, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_1720051831
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_1720051831
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->EFF_TDON_OFF1.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of EFF_TDON_OFF2
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_EFF_TD_ON_OFF2(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_18_1;
extern unsigned char SBF_11_18;
if(SBF_11_18) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_4287412685
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_4287412685
  if ( vcast_is_in_driver ) {
    P_11_18_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 18, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_4287412685
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_4287412685
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->EFF_TDON_OFF2.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of TRISE_FALL1
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_TRISE_FALL1(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_19_1;
extern unsigned char SBF_11_19;
if(SBF_11_19) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_1570194970
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_1570194970
  if ( vcast_is_in_driver ) {
    P_11_19_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 19, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_1570194970
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_1570194970
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->TRISE_FALL1.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of TRISE_FALL2
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_TRISE_FALL2(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_20_1;
extern unsigned char SBF_11_20;
if(SBF_11_20) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_3298695072
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_3298695072
  if ( vcast_is_in_driver ) {
    P_11_20_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 20, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_3298695072
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_3298695072
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->TRISE_FALL2.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of TDREG
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_TDREG(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_21_1;
extern unsigned char SBF_11_21;
if(SBF_11_21) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_292736761
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_292736761
  if ( vcast_is_in_driver ) {
    P_11_21_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 21, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_292736761
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_292736761
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->TDREG.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of DSOV
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_DSOV(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_22_1;
extern unsigned char SBF_11_22;
if(SBF_11_22) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_1309481490
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_1309481490
  if ( vcast_is_in_driver ) {
    P_11_22_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 22, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_1309481490
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_1309481490
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->DSOV.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of FAM_PROD_STAT
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_FAM_PROD_STAT(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_23_1;
extern unsigned char SBF_11_23;
if(SBF_11_23) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_926310641
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_926310641
  if ( vcast_is_in_driver ) {
    P_11_23_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 23, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_926310641
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_926310641
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->FAM_PROD_STAT.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

/** \brief Update Register Shadow of STICHG
*
*   \param u16_data : value to be written
*
*   \return void
*/
void TLE9560_ApplLayer_update_STICHG(uint16_t u16_data)
{ /*vcast_internal_start*/
extern unsigned short P_11_24_1;
extern unsigned char SBF_11_24;
if(SBF_11_24) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_2267157521
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_2267157521
  if ( vcast_is_in_driver ) {
    P_11_24_1 = u16_data;
    vCAST_COMMON_STUB_PROC_11( 11, 24, 2, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_2267157521
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_2267157521
  vCAST_USER_CODE_TIMER_START();
  return;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

  TLE9560->ST_ICHG.reg = u16_data;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}

_Bool TLE9560_ApplLayer_GetStatus(void){ /*vcast_internal_start*/
extern _Bool R_11_25;
extern unsigned char SBF_11_25;
if(SBF_11_25) {
  vCAST_USER_CODE_TIMER_STOP();
#define BEGINNING_OF_STUB_USER_CODE_11_374344051
#include "vcast_configure_stub.c"
#undef BEGINNING_OF_STUB_USER_CODE_11_374344051
  if ( vcast_is_in_driver ) {
    vCAST_COMMON_STUB_PROC_11( 11, 25, 1, 0 );
  } /* vcast_is_in_driver */
#define END_OF_STUB_USER_CODE_11_374344051
#include "vcast_configure_stub.c"
#undef END_OF_STUB_USER_CODE_11_374344051
  vCAST_USER_CODE_TIMER_START();
  return R_11_25;
}
/*vcast_internal_end*/
 /*vcast_internal_start*/{/*vcast_internal_end*/

	_Bool bRetVal;
	if(s_deviceDriver.fp_setReg == 0){
		bRetVal = 1;
	}else{
		bRetVal = 0;
	}
	return bRetVal;
 /*vcast_internal_start*/}/*vcast_internal_end*/
}
#line 6 "C:\\Users\\<USER>\\MBVan\\MB_ASSY_SCU_SW_DevStrm_shamalisunil.kalekar@tessolve_Workspace_VC\\MBDevStrm\\SwApplication\\Application\\HardwareAbstractionLayer\\TLE9560\\UnitTest\\TLE9560_MAN\\vcast_preprocess.7900.0.c"

 /*vcast_internal_start*/
unsigned char R_11_1;
unsigned char SBF_11_1 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned char R_11_2;
unsigned char SBF_11_2 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned char R_11_3;
unsigned char SBF_11_3 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short R_11_4;
unsigned char SBF_11_4 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_5_1;
unsigned char SBF_11_5 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_6_1;
unsigned char SBF_11_6 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_7_1;
unsigned char SBF_11_7 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_8_1;
unsigned char SBF_11_8 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_9_1;
unsigned char SBF_11_9 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_10_1;
unsigned char SBF_11_10 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_11_1;
unsigned char SBF_11_11 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_12_1;
unsigned char SBF_11_12 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_13_1;
unsigned char SBF_11_13 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_14_1;
unsigned char SBF_11_14 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_15_1;
unsigned char SBF_11_15 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_16_1;
unsigned char SBF_11_16 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_17_1;
unsigned char SBF_11_17 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_18_1;
unsigned char SBF_11_18 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_19_1;
unsigned char SBF_11_19 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_20_1;
unsigned char SBF_11_20 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_21_1;
unsigned char SBF_11_21 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_22_1;
unsigned char SBF_11_22 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_23_1;
unsigned char SBF_11_23 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
unsigned short P_11_24_1;
unsigned char SBF_11_24 = 0;

/*vcast_internal_end*/
 /*vcast_internal_start*/
_Bool R_11_25;
unsigned char SBF_11_25 = 0;

/*vcast_internal_end*/
typedef int VECTORCAST_MARKER__UNIT_APPENDIX_START;

typedef int VECTORCAST_MARKER__UNIT_APPENDIX_END;
#line 3 "vcast_preprocess.7900.2.c"
