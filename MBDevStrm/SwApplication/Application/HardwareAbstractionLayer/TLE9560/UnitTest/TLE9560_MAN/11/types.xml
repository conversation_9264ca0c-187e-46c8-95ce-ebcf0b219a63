<?xml version="1.0" encoding="UTF-8"?>
<typefile version="1">
  <type typeid="1100001" typetype="ACCE_SS" pointer_type="1100002" typemark="TLE9560_Type*"/>
  <type typeid="1100002" typetype="REC_ORD" ada_scope="__T402309048" typemark="TLE9560_Type">
    <field index="1" name="GENCTRL" typeid="1100004"/>
    <field index="2" name="SIF" typeid="1100007"/>
    <field index="3" name="M_S_CTRL" typeid="1100010"/>
    <field index="4" name="HW_CTRL" typeid="1100012"/>
    <field index="5" name="WD_CTRL" typeid="1100014"/>
    <field index="6" name="BUS_CTRL" typeid="1100016"/>
    <field index="7" name="BUS_STAT" typeid="1100018"/>
    <field index="8" name="WK_CTRL_BNK1" typeid="1100020"/>
    <field index="9" name="WK_CTRL_BNK2" typeid="1100022"/>
    <field index="10" name="WK_CTRL_BNK3" typeid="1100024"/>
    <field index="11" name="WK_CTRL_BNK4" typeid="1100026"/>
    <field index="12" name="WK_STAT" typeid="1100028"/>
    <field index="13" name="WK_LVL_STAT" typeid="1100030"/>
    <field index="14" name="TIMER_CTRL" typeid="1100032"/>
    <field index="15" name="SW_SD_CTRL" typeid="1100034"/>
    <field index="16" name="HS_CTRL" typeid="1100036"/>
    <field index="17" name="HS_VDS" typeid="1100038"/>
    <field index="18" name="HS_OL_OC_OT_STAT" typeid="1100040"/>
    <field index="19" name="INT_MASK" typeid="1100042"/>
    <field index="20" name="PWM_CTRL_BNK0" typeid="1100044"/>
    <field index="21" name="PWM_CTRL_BNK1" typeid="1100046"/>
    <field index="22" name="PWM_CTRL_BNK2" typeid="1100048"/>
    <field index="23" name="PWM_CTRL_BNK3" typeid="1100050"/>
    <field index="24" name="SYS_STAT_CTRL" typeid="1100052"/>
    <field index="25" name="LS_VDS" typeid="1100054"/>
    <field index="26" name="CCP_BLK_BNK0" typeid="1100056"/>
    <field index="27" name="CCP_BLK_BNK1" typeid="1100058"/>
    <field index="28" name="CCP_BLK_BNK4" typeid="1100060"/>
    <field index="29" name="CCP_BLK_BNK5" typeid="1100062"/>
    <field index="30" name="ST_ICHG" typeid="1100064"/>
    <field index="31" name="HB_ICHG_BNK0" typeid="1100066"/>
    <field index="32" name="HB_ICHG_BNK1" typeid="1100068"/>
    <field index="33" name="HB_ICHG_BNK4" typeid="1100070"/>
    <field index="34" name="HB_ICHG_BNK5" typeid="1100072"/>
    <field index="35" name="HB_ICHG_MAX" typeid="1100074"/>
    <field index="36" name="HB_PCHG_INIT_BNK0" typeid="1100076"/>
    <field index="37" name="HB_PCHG_INIT_BNK1" typeid="1100078"/>
    <field index="38" name="TDON_HB_CTRL_BNK0" typeid="1100080"/>
    <field index="39" name="TDON_HB_CTRL_BNK1" typeid="1100082"/>
    <field index="40" name="TDOFF_HB_CTRL_BNK0" typeid="1100084"/>
    <field index="41" name="TDOFF_HB_CTRL_BNK1" typeid="1100086"/>
    <field index="42" name="SWK_CTRL" typeid="1100088"/>
    <field index="43" name="SWK_BTL1_CTRL" typeid="1100090"/>
    <field index="44" name="SWK_ID1_CTRL" typeid="1100092"/>
    <field index="45" name="SWK_ID0_CTRL" typeid="1100094"/>
    <field index="46" name="SWK_MASK_ID1_CTRL" typeid="1100096"/>
    <field index="47" name="SWK_MASK_ID0_CTRL" typeid="1100098"/>
    <field index="48" name="SWK_DLC_CTRL" typeid="1100100"/>
    <field index="49" name="SWK_DATA3_CTRL" typeid="1100102"/>
    <field index="50" name="SWK_DATA2_CTRL" typeid="1100104"/>
    <field index="51" name="SWK_DATA1_CTRL" typeid="1100106"/>
    <field index="52" name="SWK_DATA0_CTRL" typeid="1100108"/>
    <field index="53" name="SWK_CAN_FD_CTRL" typeid="1100110"/>
    <field index="54" name="SWK_OSC_TRIM_CTRL" typeid="1100112"/>
    <field index="55" name="SWK_OSC_CAL_STAT" typeid="1100114"/>
    <field index="56" name="SWK_CDR_CTRL" typeid="1100116"/>
    <field index="57" name="SWK_CDR_LIMIT" typeid="1100118"/>
    <field index="58" name="SWK_STAT" typeid="1100120"/>
    <field index="59" name="SWK_ECNT_STAT" typeid="1100122"/>
    <field index="60" name="SWK_CDR_STAT" typeid="1100124"/>
    <field index="61" name="SUP_STAT" typeid="1100126"/>
    <field index="62" name="THERM_STAT" typeid="1100128"/>
    <field index="63" name="DEV_STAT" typeid="1100130"/>
    <field index="64" name="GEN_STAT" typeid="1100132"/>
    <field index="65" name="EFF_TDON_OFF1" typeid="1100134"/>
    <field index="66" name="EFF_TDON_OFF2" typeid="1100136"/>
    <field index="67" name="TRISE_FALL1" typeid="1100138"/>
    <field index="68" name="TRISE_FALL2" typeid="1100140"/>
    <field index="69" name="HBMODE" typeid="1100142"/>
    <field index="70" name="DSOV" typeid="1100144"/>
    <field index="71" name="FAM_PROD_STAT" typeid="1100146"/>
    <field index="72" name="BRAKE" typeid="1100148"/>
    <field index="73" name="TPRECHG_BNK0" typeid="1100150"/>
    <field index="74" name="TPRECHG_BNK1" typeid="1100152"/>
    <field index="75" name="TDREG" typeid="1100154"/>
  </type>
  <type typeid="1100003" typetype="REC_ORD" ada_scope="__T402309048" typemark="CCAST_11_4">
    <field index="1" name="GENCTRL" typeid="1100004"/>
    <field index="2" name="SIF" typeid="1100007"/>
    <field index="3" name="M_S_CTRL" typeid="1100010"/>
    <field index="4" name="HW_CTRL" typeid="1100012"/>
    <field index="5" name="WD_CTRL" typeid="1100014"/>
    <field index="6" name="BUS_CTRL" typeid="1100016"/>
    <field index="7" name="BUS_STAT" typeid="1100018"/>
    <field index="8" name="WK_CTRL_BNK1" typeid="1100020"/>
    <field index="9" name="WK_CTRL_BNK2" typeid="1100022"/>
    <field index="10" name="WK_CTRL_BNK3" typeid="1100024"/>
    <field index="11" name="WK_CTRL_BNK4" typeid="1100026"/>
    <field index="12" name="WK_STAT" typeid="1100028"/>
    <field index="13" name="WK_LVL_STAT" typeid="1100030"/>
    <field index="14" name="TIMER_CTRL" typeid="1100032"/>
    <field index="15" name="SW_SD_CTRL" typeid="1100034"/>
    <field index="16" name="HS_CTRL" typeid="1100036"/>
    <field index="17" name="HS_VDS" typeid="1100038"/>
    <field index="18" name="HS_OL_OC_OT_STAT" typeid="1100040"/>
    <field index="19" name="INT_MASK" typeid="1100042"/>
    <field index="20" name="PWM_CTRL_BNK0" typeid="1100044"/>
    <field index="21" name="PWM_CTRL_BNK1" typeid="1100046"/>
    <field index="22" name="PWM_CTRL_BNK2" typeid="1100048"/>
    <field index="23" name="PWM_CTRL_BNK3" typeid="1100050"/>
    <field index="24" name="SYS_STAT_CTRL" typeid="1100052"/>
    <field index="25" name="LS_VDS" typeid="1100054"/>
    <field index="26" name="CCP_BLK_BNK0" typeid="1100056"/>
    <field index="27" name="CCP_BLK_BNK1" typeid="1100058"/>
    <field index="28" name="CCP_BLK_BNK4" typeid="1100060"/>
    <field index="29" name="CCP_BLK_BNK5" typeid="1100062"/>
    <field index="30" name="ST_ICHG" typeid="1100064"/>
    <field index="31" name="HB_ICHG_BNK0" typeid="1100066"/>
    <field index="32" name="HB_ICHG_BNK1" typeid="1100068"/>
    <field index="33" name="HB_ICHG_BNK4" typeid="1100070"/>
    <field index="34" name="HB_ICHG_BNK5" typeid="1100072"/>
    <field index="35" name="HB_ICHG_MAX" typeid="1100074"/>
    <field index="36" name="HB_PCHG_INIT_BNK0" typeid="1100076"/>
    <field index="37" name="HB_PCHG_INIT_BNK1" typeid="1100078"/>
    <field index="38" name="TDON_HB_CTRL_BNK0" typeid="1100080"/>
    <field index="39" name="TDON_HB_CTRL_BNK1" typeid="1100082"/>
    <field index="40" name="TDOFF_HB_CTRL_BNK0" typeid="1100084"/>
    <field index="41" name="TDOFF_HB_CTRL_BNK1" typeid="1100086"/>
    <field index="42" name="SWK_CTRL" typeid="1100088"/>
    <field index="43" name="SWK_BTL1_CTRL" typeid="1100090"/>
    <field index="44" name="SWK_ID1_CTRL" typeid="1100092"/>
    <field index="45" name="SWK_ID0_CTRL" typeid="1100094"/>
    <field index="46" name="SWK_MASK_ID1_CTRL" typeid="1100096"/>
    <field index="47" name="SWK_MASK_ID0_CTRL" typeid="1100098"/>
    <field index="48" name="SWK_DLC_CTRL" typeid="1100100"/>
    <field index="49" name="SWK_DATA3_CTRL" typeid="1100102"/>
    <field index="50" name="SWK_DATA2_CTRL" typeid="1100104"/>
    <field index="51" name="SWK_DATA1_CTRL" typeid="1100106"/>
    <field index="52" name="SWK_DATA0_CTRL" typeid="1100108"/>
    <field index="53" name="SWK_CAN_FD_CTRL" typeid="1100110"/>
    <field index="54" name="SWK_OSC_TRIM_CTRL" typeid="1100112"/>
    <field index="55" name="SWK_OSC_CAL_STAT" typeid="1100114"/>
    <field index="56" name="SWK_CDR_CTRL" typeid="1100116"/>
    <field index="57" name="SWK_CDR_LIMIT" typeid="1100118"/>
    <field index="58" name="SWK_STAT" typeid="1100120"/>
    <field index="59" name="SWK_ECNT_STAT" typeid="1100122"/>
    <field index="60" name="SWK_CDR_STAT" typeid="1100124"/>
    <field index="61" name="SUP_STAT" typeid="1100126"/>
    <field index="62" name="THERM_STAT" typeid="1100128"/>
    <field index="63" name="DEV_STAT" typeid="1100130"/>
    <field index="64" name="GEN_STAT" typeid="1100132"/>
    <field index="65" name="EFF_TDON_OFF1" typeid="1100134"/>
    <field index="66" name="EFF_TDON_OFF2" typeid="1100136"/>
    <field index="67" name="TRISE_FALL1" typeid="1100138"/>
    <field index="68" name="TRISE_FALL2" typeid="1100140"/>
    <field index="69" name="HBMODE" typeid="1100142"/>
    <field index="70" name="DSOV" typeid="1100144"/>
    <field index="71" name="FAM_PROD_STAT" typeid="1100146"/>
    <field index="72" name="BRAKE" typeid="1100148"/>
    <field index="73" name="TPRECHG_BNK0" typeid="1100150"/>
    <field index="74" name="TPRECHG_BNK1" typeid="1100152"/>
    <field index="75" name="TDREG" typeid="1100154"/>
  </type>
  <type typeid="1100004" typetype="UNION" ada_scope="__T402310056" typemark="CCAST_11_5">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100006"/>
  </type>
  <type typeid="1100005" typetype="UNSIGNED_SHORT" typemark="unsigned short"/>
  <type typeid="1100006" typetype="REC_ORD" ada_scope="__T402311864" typemark="CCAST_11_7">
    <field index="1" name="FMODE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="IHOLD" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="EN_GEN_CHECK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="AGCFILT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="POCHGDIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="CPEN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="AGC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="8" name="IPCHGADT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="BDOV_REC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="CPSTGA" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="FET_LVL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="CPUVTH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="13" name="PWM1MAP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="14" name="BDFREQ" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100007" typetype="UNION" ada_scope="__T402322880" typemark="CCAST_11_8">
    <field index="1" name="byte" typeid="1100008"/>
    <field index="2" name="bit" typeid="1100009"/>
  </type>
  <type typeid="1100008" typetype="UNSIGNED_CHAR" typemark="unsigned char"/>
  <type typeid="1100009" typetype="REC_ORD" ada_scope="__T402324608" typemark="CCAST_11_10">
    <field index="1" name="SUPPLY_STAT" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="TEMP_STAT" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="BUS_STAT" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="WAKE_UP" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="HS_STAT" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="DEV_STAT" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="BD_STAT" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="SPI_CRC_FAIL" typeid="1100008" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100010" typetype="UNION" ada_scope="__T402331136" typemark="CCAST_11_11">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100011"/>
  </type>
  <type typeid="1100011" typetype="REC_ORD" ada_scope="__T402332480" typemark="CCAST_11_12">
    <field index="1" name="VCC1_RT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="2" name="I_PEAK_TH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="RSTN_HYS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="VCC1_OV_MOD" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="5" name="MODE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
  </type>
  <type typeid="1100012" typetype="UNION" ada_scope="__T402339480" typemark="CCAST_11_13">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100013"/>
  </type>
  <type typeid="1100013" typetype="REC_ORD" ada_scope="__T402340824" typemark="CCAST_11_14">
    <field index="1" name="WD_STM_EN_1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="FO_ON" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="SOFT_RESET_RO" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="RSTN_DEL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="SH_DISABLE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="VS_OV_SEL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="TSD2_DEL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100014" typetype="UNION" ada_scope="__T402348992" typemark="CCAST_11_15">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100015"/>
  </type>
  <type typeid="1100015" typetype="REC_ORD" ada_scope="__T402350336" typemark="CCAST_11_16">
    <field index="1" name="WD_TIMER" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="WD_EN_WK_BUS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="WD_CFG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="WD_STM_EN_0" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="CHECKSUM" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100016" typetype="UNION" ada_scope="__T402356232" typemark="CCAST_11_17">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100017"/>
  </type>
  <type typeid="1100017" typetype="REC_ORD" ada_scope="__T402357576" typemark="CCAST_11_18">
    <field index="1" name="CAN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="LIN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="LIN_TXD_TO" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="LIN_LSM" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="LIN_FLASH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100018" typetype="UNION" ada_scope="__T402362912" typemark="CCAST_11_19">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100019"/>
  </type>
  <type typeid="1100019" typetype="REC_ORD" ada_scope="__T402364256" typemark="CCAST_11_20">
    <field index="1" name="VCAN_UV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="CAN_FAIL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="SYSERR" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="CANTO" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="LIN_FAIL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
  </type>
  <type typeid="1100020" typetype="UNION" ada_scope="__T402369560" typemark="CCAST_11_21">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100021"/>
  </type>
  <type typeid="1100021" typetype="REC_ORD" ada_scope="__T402370904" typemark="CCAST_11_22">
    <field index="1" name="WK_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="WK_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="WK_PUPD" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="WK_FILT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="5" name="WK2_FO" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100022" typetype="UNION" ada_scope="__T402442968" typemark="CCAST_11_23">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100023"/>
  </type>
  <type typeid="1100023" typetype="REC_ORD" ada_scope="__T402444312" typemark="CCAST_11_24">
    <field index="1" name="WK_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="WK_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="WK_PUPD" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="WK_FILT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="5" name="WK2_FO" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100024" typetype="UNION" ada_scope="__T402450616" typemark="CCAST_11_25">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100025"/>
  </type>
  <type typeid="1100025" typetype="REC_ORD" ada_scope="__T402451960" typemark="CCAST_11_26">
    <field index="1" name="WK_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="WK_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="WK_PUPD" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="WK_FILT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="5" name="WK2_FO" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100026" typetype="UNION" ada_scope="__T402458264" typemark="CCAST_11_27">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100027"/>
  </type>
  <type typeid="1100027" typetype="REC_ORD" ada_scope="__T402459608" typemark="CCAST_11_28">
    <field index="1" name="WK_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="WK_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="WK_PUPD" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="WK_FILT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="5" name="WK2_FO" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100028" typetype="UNION" ada_scope="__T402465912" typemark="CCAST_11_29">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100029"/>
  </type>
  <type typeid="1100029" typetype="REC_ORD" ada_scope="__T402467256" typemark="CCAST_11_30">
    <field index="1" name="WK1_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="WK2_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="WK3_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="WK4_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="TIMER1_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="TIMER2_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="CAN_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="LIN_WU" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100030" typetype="UNION" ada_scope="__T402474864" typemark="CCAST_11_31">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100031"/>
  </type>
  <type typeid="1100031" typetype="REC_ORD" ada_scope="__T402476208" typemark="CCAST_11_32">
    <field index="1" name="WK1_LVL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="WK2_LVL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="WK3_LVL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="WK4_LVL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100032" typetype="UNION" ada_scope="__T402480952" typemark="CCAST_11_33">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100033"/>
  </type>
  <type typeid="1100033" typetype="REC_ORD" ada_scope="__T402482296" typemark="CCAST_11_34">
    <field index="1" name="TIMER1_PER" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TIMER1_ON" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="3" name="CYCWK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="TIMER2_PER" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="5" name="TIMER2_ON" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
  </type>
  <type typeid="1100034" typetype="UNION" ada_scope="__T402488200" typemark="CCAST_11_35">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100035"/>
  </type>
  <type typeid="1100035" typetype="REC_ORD" ada_scope="__T402489544" typemark="CCAST_11_36">
    <field index="1" name="HS_UV_REC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="HS_UV_SD_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="HS_OV_SDS_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="HS1_OV_SDN_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="HS2_OV_SDN_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="HS3_OV_SDN_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="HS4_OV_SDN_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="HS_OT_SD_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="HS1_OV_REC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="HS2_OV_REC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="HS3_OV_REC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="HS4_OV_REC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100036" typetype="UNION" ada_scope="__T402499544" typemark="CCAST_11_37">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100037"/>
  </type>
  <type typeid="1100037" typetype="REC_ORD" ada_scope="__T402500888" typemark="CCAST_11_38">
    <field index="1" name="HS1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="2" name="HS2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="3" name="HS3" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="4" name="HS4" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100038" typetype="UNION" ada_scope="__T402505072" typemark="CCAST_11_39">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100039"/>
  </type>
  <type typeid="1100039" typetype="REC_ORD" ada_scope="__T402506416" typemark="CCAST_11_40">
    <field index="1" name="HS1VDSTH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="HS2VDSTH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="3" name="DEEP_ADAP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100040" typetype="UNION" ada_scope="__T402511248" typemark="CCAST_11_41">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100041"/>
  </type>
  <type typeid="1100041" typetype="REC_ORD" ada_scope="__T402512592" typemark="CCAST_11_42">
    <field index="1" name="HS1_OC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="HS2_OC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="HS3_OC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="HS4_OC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="HS1_OL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="HS2_OL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="HS3_OL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="HS4_OL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="HS1_OT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="HS2_OT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="HS3_OT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="HS4_OT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100042" typetype="UNION" ada_scope="__T402523056" typemark="CCAST_11_43">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100043"/>
  </type>
  <type typeid="1100043" typetype="REC_ORD" ada_scope="__T402524400" typemark="CCAST_11_44">
    <field index="1" name="SUPPLY_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="TEMP_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="BUS_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="HS_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="BD_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="SPI_CRC_FAIL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="WD_SDM" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="WD_SDM_DISABLE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="INTN_CYC_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100044" typetype="UNION" ada_scope="__T402531896" typemark="CCAST_11_45">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100045"/>
  </type>
  <type typeid="1100045" typetype="REC_ORD" ada_scope="__T402533240" typemark="CCAST_11_46">
    <field index="1" name="PWM_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="PWM_DC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="511" max="1023"/>
    </field>
    <field index="3" name="PWM_FREQ" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100046" typetype="UNION" ada_scope="__T402537968" typemark="CCAST_11_47">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100047"/>
  </type>
  <type typeid="1100047" typetype="REC_ORD" ada_scope="__T402539312" typemark="CCAST_11_48">
    <field index="1" name="PWM_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="PWM_DC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="511" max="1023"/>
    </field>
    <field index="3" name="PWM_FREQ" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100048" typetype="UNION" ada_scope="__T402543960" typemark="CCAST_11_49">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100049"/>
  </type>
  <type typeid="1100049" typetype="REC_ORD" ada_scope="__T402545304" typemark="CCAST_11_50">
    <field index="1" name="PWM_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="PWM_DC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="511" max="1023"/>
    </field>
    <field index="3" name="PWM_FREQ" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100050" typetype="UNION" ada_scope="__T402549952" typemark="CCAST_11_51">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100051"/>
  </type>
  <type typeid="1100051" typetype="REC_ORD" ada_scope="__T402551296" typemark="CCAST_11_52">
    <field index="1" name="PWM_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="PWM_DC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="511" max="1023"/>
    </field>
    <field index="3" name="PWM_FREQ" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100052" typetype="UNION" ada_scope="__T402555944" typemark="CCAST_11_53">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100053"/>
  </type>
  <type typeid="1100053" typetype="REC_ORD" ada_scope="__T402557288" typemark="CCAST_11_54">
    <field index="1" name="SYS_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="32767" max="65535"/>
    </field>
  </type>
  <type typeid="1100054" typetype="UNION" ada_scope="__T402559760" typemark="CCAST_11_55">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100055"/>
  </type>
  <type typeid="1100055" typetype="REC_ORD" ada_scope="__T402561104" typemark="CCAST_11_56">
    <field index="1" name="LS1VDSTH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="LS2VDSTH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="3" name="TFVDS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
  </type>
  <type typeid="1100056" typetype="UNION" ada_scope="__T402565832" typemark="CCAST_11_57">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100057"/>
  </type>
  <type typeid="1100057" typetype="REC_ORD" ada_scope="__T402567176" typemark="CCAST_11_58">
    <field index="1" name="CCP_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TCCP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="3" name="TBLANK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100058" typetype="UNION" ada_scope="__T402571344" typemark="CCAST_11_59">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100059"/>
  </type>
  <type typeid="1100059" typetype="REC_ORD" ada_scope="__T402572688" typemark="CCAST_11_60">
    <field index="1" name="CCP_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TCCP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="3" name="TBLANK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100060" typetype="UNION" ada_scope="__T477171256" typemark="CCAST_11_61">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100061"/>
  </type>
  <type typeid="1100061" typetype="REC_ORD" ada_scope="__T477172600" typemark="CCAST_11_62">
    <field index="1" name="CCP_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TCCP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="3" name="TBLANK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100062" typetype="UNION" ada_scope="__T477176696" typemark="CCAST_11_63">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100063"/>
  </type>
  <type typeid="1100063" typetype="REC_ORD" ada_scope="__T477178040" typemark="CCAST_11_64">
    <field index="1" name="CCP_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TCCP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="3" name="TBLANK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100064" typetype="UNION" ada_scope="__T477182136" typemark="CCAST_11_65">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100065"/>
  </type>
  <type typeid="1100065" typetype="REC_ORD" ada_scope="__T477183480" typemark="CCAST_11_66">
    <field index="1" name="ICHGST1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
    <field index="2" name="ICHGST2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100066" typetype="UNION" ada_scope="__T477187064" typemark="CCAST_11_67">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100067"/>
  </type>
  <type typeid="1100067" typetype="REC_ORD" ada_scope="__T477188408" typemark="CCAST_11_68">
    <field index="1" name="ICHG_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="ICHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="3" name="IDCHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100068" typetype="UNION" ada_scope="__T477192584" typemark="CCAST_11_69">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100069"/>
  </type>
  <type typeid="1100069" typetype="REC_ORD" ada_scope="__T477193928" typemark="CCAST_11_70">
    <field index="1" name="ICHG_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="ICHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="3" name="IDCHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100070" typetype="UNION" ada_scope="__T477198024" typemark="CCAST_11_71">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100071"/>
  </type>
  <type typeid="1100071" typetype="REC_ORD" ada_scope="__T477199368" typemark="CCAST_11_72">
    <field index="1" name="ICHG_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="ICHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="3" name="IDCHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100072" typetype="UNION" ada_scope="__T477203464" typemark="CCAST_11_73">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100073"/>
  </type>
  <type typeid="1100073" typetype="REC_ORD" ada_scope="__T477204808" typemark="CCAST_11_74">
    <field index="1" name="ICHG_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="ICHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="3" name="IDCHG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100074" typetype="UNION" ada_scope="__T477208904" typemark="CCAST_11_75">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100075"/>
  </type>
  <type typeid="1100075" typetype="REC_ORD" ada_scope="__T477210248" typemark="CCAST_11_76">
    <field index="1" name="ICHGMAX1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="2" name="ICHGMAX2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="HB1IDIAG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="HB2IDIAG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100076" typetype="UNION" ada_scope="__T477215576" typemark="CCAST_11_77">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100077"/>
  </type>
  <type typeid="1100077" typetype="REC_ORD" ada_scope="__T477216920" typemark="CCAST_11_78">
    <field index="1" name="INIT_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="PCHGINIT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="3" name="PDCHGINIT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100078" typetype="UNION" ada_scope="__T477221120" typemark="CCAST_11_79">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100079"/>
  </type>
  <type typeid="1100079" typetype="REC_ORD" ada_scope="__T477222464" typemark="CCAST_11_80">
    <field index="1" name="INIT_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="PCHGINIT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="3" name="PDCHGINIT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100080" typetype="UNION" ada_scope="__T477226568" typemark="CCAST_11_81">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100081"/>
  </type>
  <type typeid="1100081" typetype="REC_ORD" ada_scope="__T477227912" typemark="CCAST_11_82">
    <field index="1" name="HB_TDON_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TDON" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100082" typetype="UNION" ada_scope="__T477232072" typemark="CCAST_11_83">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100083"/>
  </type>
  <type typeid="1100083" typetype="REC_ORD" ada_scope="__T477233688" typemark="CCAST_11_84">
    <field index="1" name="HB_TDON_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TDON" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100084" typetype="UNION" ada_scope="__T477237792" typemark="CCAST_11_85">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100085"/>
  </type>
  <type typeid="1100085" typetype="REC_ORD" ada_scope="__T477239136" typemark="CCAST_11_86">
    <field index="1" name="HB_TDOFF_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TDOFF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100086" typetype="UNION" ada_scope="__T477243296" typemark="CCAST_11_87">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100087"/>
  </type>
  <type typeid="1100087" typetype="REC_ORD" ada_scope="__T477244640" typemark="CCAST_11_88">
    <field index="1" name="HB_TDOFF_BNK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TDOFF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100088" typetype="UNION" ada_scope="__T477248744" typemark="CCAST_11_89">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100089"/>
  </type>
  <type typeid="1100089" typetype="REC_ORD" ada_scope="__T477250088" typemark="CCAST_11_90">
    <field index="1" name="CFG_VAL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="CANTO_MASK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="TRIM_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="OSC_CAL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100090" typetype="UNION" ada_scope="__T477255392" typemark="CCAST_11_91">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100091"/>
  </type>
  <type typeid="1100091" typetype="REC_ORD" ada_scope="__T477256736" typemark="CCAST_11_92">
    <field index="1" name="TBIT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
    <field index="2" name="SP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100092" typetype="UNION" ada_scope="__T477260328" typemark="CCAST_11_93">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100093"/>
  </type>
  <type typeid="1100093" typetype="REC_ORD" ada_scope="__T477261672" typemark="CCAST_11_94">
    <field index="1" name="ID13" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="ID14" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="ID15" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="ID16" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="ID17" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="ID18" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="ID19" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="ID20" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="ID21" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="ID22" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="ID23" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="ID24" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="13" name="ID25" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="14" name="ID26" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="15" name="ID27" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="16" name="ID28" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100094" typetype="UNION" ada_scope="__T477272776" typemark="CCAST_11_95">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100095"/>
  </type>
  <type typeid="1100095" typetype="REC_ORD" ada_scope="__T477274120" typemark="CCAST_11_96">
    <field index="1" name="IDE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="RTR" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="ID0" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="ID1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="ID2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="ID3" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="ID4" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="ID5" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="ID6" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="ID7" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="ID8" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="ID9" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="13" name="ID10" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="14" name="ID11" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="15" name="ID12" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100096" typetype="UNION" ada_scope="__T477285200" typemark="CCAST_11_97">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100097"/>
  </type>
  <type typeid="1100097" typetype="REC_ORD" ada_scope="__T477286544" typemark="CCAST_11_98">
    <field index="1" name="MASK_ID13" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="MASK_ID14" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="MASK_ID15" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="MASK_ID16" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="MASK_ID17" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="MASK_ID18" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="MASK_ID19" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="MASK_ID20" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="MASK_ID21" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="MASK_ID22" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="MASK_ID23" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="MASK_ID24" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="13" name="MASK_ID25" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="14" name="MASK_ID26" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="15" name="MASK_ID27" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="16" name="MASK_ID28" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100098" typetype="UNION" ada_scope="__T477297784" typemark="CCAST_11_99">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100099"/>
  </type>
  <type typeid="1100099" typetype="REC_ORD" ada_scope="__T477364792" typemark="CCAST_11_100">
    <field index="1" name="MASK_ID0" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="MASK_ID1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="MASK_ID2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="MASK_ID3" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="MASK_ID4" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="MASK_ID5" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="MASK_ID6" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="MASK_ID7" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="MASK_ID8" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="MASK_ID9" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="MASK_ID10" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="MASK_ID11" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="13" name="MASK_ID12" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100100" typetype="UNION" ada_scope="__T477375384" typemark="CCAST_11_101">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100101"/>
  </type>
  <type typeid="1100101" typetype="REC_ORD" ada_scope="__T477376728" typemark="CCAST_11_102">
    <field index="1" name="DLC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100102" typetype="UNION" ada_scope="__T477379744" typemark="CCAST_11_103">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100103"/>
  </type>
  <type typeid="1100103" typetype="REC_ORD" ada_scope="__T477381088" typemark="CCAST_11_104">
    <field index="1" name="DATA6" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
    <field index="2" name="DATA7" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
  </type>
  <type typeid="1100104" typetype="UNION" ada_scope="__T477384128" typemark="CCAST_11_105">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100105"/>
  </type>
  <type typeid="1100105" typetype="REC_ORD" ada_scope="__T477385472" typemark="CCAST_11_106">
    <field index="1" name="DATA4" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
    <field index="2" name="DATA5" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
  </type>
  <type typeid="1100106" typetype="UNION" ada_scope="__T477388512" typemark="CCAST_11_107">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100107"/>
  </type>
  <type typeid="1100107" typetype="REC_ORD" ada_scope="__T477389856" typemark="CCAST_11_108">
    <field index="1" name="DATA2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
    <field index="2" name="DATA3" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
  </type>
  <type typeid="1100108" typetype="UNION" ada_scope="__T477392896" typemark="CCAST_11_109">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100109"/>
  </type>
  <type typeid="1100109" typetype="REC_ORD" ada_scope="__T477394240" typemark="CCAST_11_110">
    <field index="1" name="DATA0" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
    <field index="2" name="DATA1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
  </type>
  <type typeid="1100110" typetype="UNION" ada_scope="__T477397280" typemark="CCAST_11_111">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100111"/>
  </type>
  <type typeid="1100111" typetype="REC_ORD" ada_scope="__T477398624" typemark="CCAST_11_112">
    <field index="1" name="CAN_FD_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="FD_FILTER" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="3" name="DIS_ERR_CNT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100112" typetype="UNION" ada_scope="__T477403368" typemark="CCAST_11_113">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100113"/>
  </type>
  <type typeid="1100113" typetype="REC_ORD" ada_scope="__T477404712" typemark="CCAST_11_114">
    <field index="1" name="TRIM_OSC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="63" max="127"/>
    </field>
    <field index="2" name="TEMP_COEF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="15" max="31"/>
    </field>
    <field index="3" name="RX_WK_SEL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100114" typetype="UNION" ada_scope="__T477409464" typemark="CCAST_11_115">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100115"/>
  </type>
  <type typeid="1100115" typetype="REC_ORD" ada_scope="__T477410808" typemark="CCAST_11_116">
    <field index="1" name="OSC_CAL_L" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
    <field index="2" name="OSC_CAL_H" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
  </type>
  <type typeid="1100116" typetype="UNION" ada_scope="__T477413872" typemark="CCAST_11_117">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100117"/>
  </type>
  <type typeid="1100117" typetype="REC_ORD" ada_scope="__T477415216" typemark="CCAST_11_118">
    <field index="1" name="CDR_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="SELFILT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="3" name="SEL_OSC_CLK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
  </type>
  <type typeid="1100118" typetype="UNION" ada_scope="__T477420496" typemark="CCAST_11_119">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100119"/>
  </type>
  <type typeid="1100119" typetype="REC_ORD" ada_scope="__T477421840" typemark="CCAST_11_120">
    <field index="1" name="CDR_LIM_L" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
    <field index="2" name="CDR_LIM_H" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="127" max="255"/>
    </field>
  </type>
  <type typeid="1100120" typetype="UNION" ada_scope="__T477424896" typemark="CCAST_11_121">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100121"/>
  </type>
  <type typeid="1100121" typetype="REC_ORD" ada_scope="__T477426240" typemark="CCAST_11_122">
    <field index="1" name="SWK_SET" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="CANSIL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="WUF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="WUP" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="SYNC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100122" typetype="UNION" ada_scope="__T477497904" typemark="CCAST_11_123">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100123"/>
  </type>
  <type typeid="1100123" typetype="REC_ORD" ada_scope="__T477499248" typemark="CCAST_11_124">
    <field index="1" name="ECNT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100124" typetype="UNION" ada_scope="__T477502264" typemark="CCAST_11_125">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100125"/>
  </type>
  <type typeid="1100125" typetype="REC_ORD" ada_scope="__T477503608" typemark="CCAST_11_126">
    <field index="1" name="N_AVG" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="2047" max="4095"/>
    </field>
  </type>
  <type typeid="1100126" typetype="UNION" ada_scope="__T477506624" typemark="CCAST_11_127">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100127"/>
  </type>
  <type typeid="1100127" typetype="REC_ORD" ada_scope="__T477507968" typemark="CCAST_11_128">
    <field index="1" name="VCC1_WARN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="VCC1_OV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="VCC1_UV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="VCC1_SC" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="CP_UV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="VS_OV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="VS_UV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="VSINT_OV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="9" name="VSINT_UV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="10" name="HS_OV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="11" name="HS_UV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="12" name="VCC1_UV_FS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="13" name="CP_OT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="14" name="POR" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100128" typetype="UNION" ada_scope="__T477518504" typemark="CCAST_11_129">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100129"/>
  </type>
  <type typeid="1100129" typetype="REC_ORD" ada_scope="__T477519848" typemark="CCAST_11_130">
    <field index="1" name="TPW" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="TSD1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="TSD2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="TSD2_SAFE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100130" typetype="UNION" ada_scope="__T477524600" typemark="CCAST_11_131">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100131"/>
  </type>
  <type typeid="1100131" typetype="REC_ORD" ada_scope="__T477525944" typemark="CCAST_11_132">
    <field index="1" name="FAILURE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="SPI_FAIL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="WD_FAIL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="SW_DEV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="DEV_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="6" name="CRC_FAIL" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="CRC_STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100132" typetype="UNION" ada_scope="__T477532936" typemark="CCAST_11_133">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100133"/>
  </type>
  <type typeid="1100133" typetype="REC_ORD" ada_scope="__T477534280" typemark="CCAST_11_134">
    <field index="1" name="PWM1STAT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="HB1VOUT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="HB2VOUT" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100134" typetype="UNION" ada_scope="__T477539008" typemark="CCAST_11_135">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100135"/>
  </type>
  <type typeid="1100135" typetype="REC_ORD" ada_scope="__T477540352" typemark="CCAST_11_136">
    <field index="1" name="TDON1EFF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="2" name="TDOFF1EFF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100136" typetype="UNION" ada_scope="__T477544512" typemark="CCAST_11_137">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100137"/>
  </type>
  <type typeid="1100137" typetype="REC_ORD" ada_scope="__T477545856" typemark="CCAST_11_138">
    <field index="1" name="TDON2EFF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="2" name="TDOFF2EFF" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100138" typetype="UNION" ada_scope="__T477550016" typemark="CCAST_11_139">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100139"/>
  </type>
  <type typeid="1100139" typetype="REC_ORD" ada_scope="__T477551360" typemark="CCAST_11_140">
    <field index="1" name="TRISE1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="2" name="TFALL1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100140" typetype="UNION" ada_scope="__T477555504" typemark="CCAST_11_141">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100141"/>
  </type>
  <type typeid="1100141" typetype="REC_ORD" ada_scope="__T477556848" typemark="CCAST_11_142">
    <field index="1" name="TRISE2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
    <field index="2" name="TFALL2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="31" max="63"/>
    </field>
  </type>
  <type typeid="1100142" typetype="UNION" ada_scope="__T477561120" typemark="CCAST_11_143">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100143"/>
  </type>
  <type typeid="1100143" typetype="REC_ORD" ada_scope="__T477562464" typemark="CCAST_11_144">
    <field index="1" name="HB1_PWM_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="AFW1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="HB1MODE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
    <field index="4" name="HB2_PWM_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="AFW2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="HB2MODE" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="1" max="3"/>
    </field>
  </type>
  <type typeid="1100144" typetype="UNION" ada_scope="__T477568368" typemark="CCAST_11_145">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100145"/>
  </type>
  <type typeid="1100145" typetype="REC_ORD" ada_scope="__T477569712" typemark="CCAST_11_146">
    <field index="1" name="HS1DSOV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="LS1DSOV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="HS2DSOV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="LS2DSOV" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="LS1DSOV_BRK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="LS2DSOV_BRK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="VSOVBRAKE_ST" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="VSINTOVBRAKE_ST" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100146" typetype="UNION" ada_scope="__T477577888" typemark="CCAST_11_147">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100147"/>
  </type>
  <type typeid="1100147" typetype="REC_ORD" ada_scope="__T477579232" typemark="CCAST_11_148">
    <field index="1" name="PROD" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="63" max="127"/>
    </field>
    <field index="2" name="FAM" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="7" max="15"/>
    </field>
  </type>
  <type typeid="1100148" typetype="UNION" ada_scope="__T477582824" typemark="CCAST_11_149">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100149"/>
  </type>
  <type typeid="1100149" typetype="REC_ORD" ada_scope="__T477584168" typemark="CCAST_11_150">
    <field index="1" name="OV_BRK_TH" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="OV_BRK_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="PARK_BRK_EN" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="TBLK_BRK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="VDSTH_BRK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="SLAM" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="7" name="SLAM_LS1_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="8" name="SLAM_LS2_DIS" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100150" typetype="UNION" ada_scope="__T477591816" typemark="CCAST_11_151">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100151"/>
  </type>
  <type typeid="1100151" typetype="REC_ORD" ada_scope="__T477593160" typemark="CCAST_11_152">
    <field index="1" name="TPCHG_BANK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TPCHG1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="3" name="TPCHG2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
  </type>
  <type typeid="1100152" typetype="UNION" ada_scope="__T477597888" typemark="CCAST_11_153">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100153"/>
  </type>
  <type typeid="1100153" typetype="REC_ORD" ada_scope="__T477599232" typemark="CCAST_11_154">
    <field index="1" name="TPCHG_BANK" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="2" name="TPCHG1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
    <field index="3" name="TPCHG2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="3" max="7"/>
    </field>
  </type>
  <type typeid="1100154" typetype="UNION" ada_scope="__T477603880" typemark="CCAST_11_155">
    <field index="1" name="reg" typeid="1100005"/>
    <field index="2" name="bit" typeid="1100155"/>
  </type>
  <type typeid="1100155" typetype="REC_ORD" ada_scope="__T477605224" typemark="CCAST_11_156">
    <field index="1" name="TDREG1" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="2" name="TDREG2" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="3" name="IPCHG1_ST" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="4" name="IPCHG2_ST" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="5" name="IPDCHG1_ST" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
    <field index="6" name="IPDCHG2_ST" typeid="1100005" is_bitfield="1">
      <range_data min="0" mid="0" max="1"/>
    </field>
  </type>
  <type typeid="1100156" typetype="AR_RAY" array_type="1100157" typemark="CCAST_11_157">
    <range_data size="19%%"/>
  </type>
  <type typeid="1100157" typetype="PROCEDURE_POINTER" typemark="void (*)(void)">
    <function_candidate index="0" name="TLE9560_RegLayer_get_BUS_STAT"/>
    <function_candidate index="1" name="TLE9560_RegLayer_get_WK_STAT"/>
    <function_candidate index="2" name="TLE9560_RegLayer_get_WK_LVL_STAT"/>
    <function_candidate index="3" name="TLE9560_RegLayer_get_HS_OL_OC_OT_STAT"/>
    <function_candidate index="4" name="TLE9560_RegLayer_get_SWK_OSC_CAL_STAT"/>
    <function_candidate index="5" name="TLE9560_RegLayer_get_SWK_STAT"/>
    <function_candidate index="6" name="TLE9560_RegLayer_get_SWK_ECNT_STAT"/>
    <function_candidate index="7" name="TLE9560_RegLayer_get_SWK_CDR_STAT"/>
    <function_candidate index="8" name="TLE9560_RegLayer_get_SUP_STAT"/>
    <function_candidate index="9" name="TLE9560_RegLayer_get_THERM_STAT"/>
    <function_candidate index="10" name="TLE9560_RegLayer_get_DEV_STAT"/>
    <function_candidate index="11" name="TLE9560_RegLayer_get_GEN_STAT"/>
    <function_candidate index="12" name="TLE9560_RegLayer_get_EFF_TDON_OFF1"/>
    <function_candidate index="13" name="TLE9560_RegLayer_get_EFF_TDON_OFF2"/>
    <function_candidate index="14" name="TLE9560_RegLayer_get_TRISE_FALL1"/>
    <function_candidate index="15" name="TLE9560_RegLayer_get_TRISE_FALL2"/>
    <function_candidate index="16" name="TLE9560_RegLayer_get_DSOV"/>
    <function_candidate index="17" name="TLE9560_RegLayer_get_FAM_PROD_STAT"/>
    <function_candidate index="18" name="TLE9560_RegLayer_get_TDREG"/>
    <function_candidate index="19" name="TLE9560_ServLayer_InitSpiInterface"/>
    <function_candidate index="20" name="TLE9560_ServLayer_getSpiData"/>
  </type>
  <type typeid="1100158" typetype="UNKNOWN" typemark="void (void)"/>
  <type typeid="1100159" typetype="NOT_SUPPORTED" unsupported_type_reason="TI_STATUS_VOID_TYPES_ARE_UNTESTABLE" typemark="void"/>
  <type typeid="1100160" typetype="AR_RAY" array_type="1100161" typemark="CCAST_11_161">
    <range_data size="19%%"/>
  </type>
  <type typeid="1100161" typetype="PROCEDURE_POINTER" typemark="void (*)(uint16_t)">
    <function_candidate index="0" name="TLE9560_RegLayer_set_WD_CTRL"/>
    <function_candidate index="1" name="TLE9560_ApplLayer_update_BUS_STAT"/>
    <function_candidate index="2" name="TLE9560_ApplLayer_update_WK_STAT"/>
    <function_candidate index="3" name="TLE9560_ApplLayer_update_WK_LVL_STAT"/>
    <function_candidate index="4" name="TLE9560_ApplLayer_update_HS_OL_OC_OT_STAT"/>
    <function_candidate index="5" name="TLE9560_ApplLayer_update_SWK_OSC_CAL_STAT"/>
    <function_candidate index="6" name="TLE9560_ApplLayer_update_SWK_STAT"/>
    <function_candidate index="7" name="TLE9560_ApplLayer_update_SWK_ECNT_STAT"/>
    <function_candidate index="8" name="TLE9560_ApplLayer_update_SWK_CDR_STAT"/>
    <function_candidate index="9" name="TLE9560_ApplLayer_update_SUP_STAT"/>
    <function_candidate index="10" name="TLE9560_ApplLayer_update_THERM_STAT"/>
    <function_candidate index="11" name="TLE9560_ApplLayer_update_DEV_STAT"/>
    <function_candidate index="12" name="TLE9560_ApplLayer_update_GEN_STAT"/>
    <function_candidate index="13" name="TLE9560_ApplLayer_update_EFF_TD_ON_OFF1"/>
    <function_candidate index="14" name="TLE9560_ApplLayer_update_EFF_TD_ON_OFF2"/>
    <function_candidate index="15" name="TLE9560_ApplLayer_update_TRISE_FALL1"/>
    <function_candidate index="16" name="TLE9560_ApplLayer_update_TRISE_FALL2"/>
    <function_candidate index="17" name="TLE9560_ApplLayer_update_TDREG"/>
    <function_candidate index="18" name="TLE9560_ApplLayer_update_DSOV"/>
    <function_candidate index="19" name="TLE9560_ApplLayer_update_FAM_PROD_STAT"/>
    <function_candidate index="20" name="TLE9560_ApplLayer_update_STICHG"/>
  </type>
  <type typeid="1100162" typetype="UNKNOWN" typemark="void (uint16_t)"/>
  <type typeid="1100163" typetype="REC_ORD" ada_scope="_sDEVICE_deviceDriver" typemark="_sDEVICE_deviceDriver">
    <field index="1" name="u8_deviceDriverStatus" typeid="1100008"/>
    <field index="2" name="u8_deviceDriverErrorLog" typeid="1100008"/>
    <field index="3" name="u8_statusSPI" typeid="1100008"/>
    <field index="4" name="u8_SpiRx" typeid="1100164"/>
    <field index="5" name="u8_CrcResult" typeid="1100008"/>
    <field index="6" name="fp_setReg" typeid="1100161"/>
    <field index="7" name="fp_getReg" typeid="1100157"/>
    <field index="8" name="fp_updateRamReg" typeid="1100161"/>
    <field index="9" name="u16_setBitValue" typeid="1100005"/>
  </type>
  <type typeid="1100164" typetype="STR_ING" array_type="1100008" string_type_type="AR_RAY" typemark="CCAST_11_165">
    <range_data size="4%%"/>
  </type>
  <type typeid="1100165" typetype="BOOL_EAN" typemark="_Bool"/>
</typefile>
