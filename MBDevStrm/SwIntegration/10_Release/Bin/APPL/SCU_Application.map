###############################################################################
#
# IAR ELF Linker V9.50.1.380/W64 for ARM                  14/Jun/2024  13:37:00
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =
#        A:\INEOSDevStrm\SwIntegration\10_Release\Bin\APPL\SCU_Application.out
#    Map file     =
#        A:\INEOSDevStrm\SwIntegration\10_Release\Bin\APPL\SCU_Application.map
#    Command line =
#        SCUMain.o main.o conversion.o adc_driver.o peripherals_adc_config_1.o
#        clock_S32K1xx.o clock_config.o crc_driver.o crc_hw_access.o
#        peripherals_crc_1.o erm_driver.o erm_hw_access.o
#        peripherals_erm_config_1.o flash_driver.o peripherals_flash_1.o
#        ftm_ic_driver.o peripherals_flexTimer_ic_1.o ftm_common.o
#        ftm_hw_access.o ftm_pwm_driver.o peripherals_flexTimer_pwm_1.o
#        interrupt_manager.o lpit_driver.o peripherals_lpit_config_1.o
#        edma_driver.o edma_hw_access.o edma_irq.o lpspi_hw_access.o
#        lpspi_irq.o lpspi_master_driver.o lpspi_shared_function.o
#        lpspi_slave_driver.o osif_baremetal.o peripherals_lpspi_1.o
#        lptmr_driver.o lptmr_hw_access.o peripherals_lptmr_1.o pdb_driver.o
#        pdb_hw_access.o peripherals_pdb_config_1.o pin_mux.o pins_driver.o
#        pins_port_hw_access.o ADCMon_Man.o CRCMan.o ClockMan.o DioCtrl_Man.o
#        ERMHwAbs.o ExtDev.o ICMon.o nvmman.o PWMCtrl_Man.o SCUInit.o
#        TimerHwAbs.o WDOGMan.o startup.o system_S32K118.o startup_S32K118.o
#        -lADCMon_Mdl_rtwlib -lAPDet_Mdl_rtwlib -lAmbTempMon_Mdl_rtwlib
#        -lBlockDet_Mdl_rtwlib -lCfgParam_Mdl_rtwlib -lComLog_Mdl_rtwlib
#        -lCtrlLog_Mdl_rtwlib -lDIOCtrl_Mdl_rtwlib -lLearnAdap_Mdl_rtwlib
#        -lMOSFETTempMon_Mdl_rtwlib -lMtrCtrl_Mdl_rtwlib -lMtrMdl_Mdl_rtwlib
#        -lPWMCtrl_Mdl_rtwlib -lPnlOp_Mdl_rtwlib -lPosMon_Mdl_rtwlib
#        -lRefField_Mdl_rtwlib -lRefForce_Mdl_rtwlib -lStateMach_Mdl_rtwlib
#        -lSwitchLog_Mdl_rtwlib -lTaskSch_Mdl_rtwlib -lThermProt_Mdl_rtwlib
#        -lTheshForce_Mdl_rtwlib -lUsgHis_Mdl_rtwlib -lVehCom_Mdl_rtwlib
#        -lVoltMon_Mdl_rtwlib
#        -LA:\INEOSDevStrm\SwIntegration\10_Release\APPL_Lib\Lib
#        -LA:\INEOSDevStrm\SwIntegration\10_Release\APPL_Lib\LibExter -o
#        A:\INEOSDevStrm\SwIntegration\10_Release\Bin\APPL\SCU_Application.out
#        --map
#        A:\INEOSDevStrm\SwIntegration\10_Release\Bin\APPL\SCU_Application.map
#        --fpu None --no_wrap_diagnostics --entry Reset_Handler --no_bom
#        --text_out locale --config
#        A:\INEOSDevStrm\SwIntegration\00_BuildSetup\Linker\S32K118.icf
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

__CPP_Runtime   = 1
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x0 { ro section .intvec };
"P1":  place in [from 0x400 to 0x40f] { section FlashConfig };
"P2":  place in [from 0x0 to 0xbf] |
                [from 0x410 to 0x3'ffff] { ro };
define block __CODE_ROM { section .textrw_init };
"P3":  place in [from 0x0 to 0xbf] |
                [from 0x410 to 0x3'ffff] { block __CODE_ROM };
define block RW { rw };
"P4":  place in [from 0x2000'00c0 to 0x2000'30bf] { block RW };
define block __CODE_RAM { section .textrw };
"P5":  place in [from 0x2000'00c0 to 0x2000'30bf] { block __CODE_RAM };
define block ZI { zi };
"P7":  place in [from 0x2000'30c0 to 0x2000'54ff] { block ZI };
define block CSTACK with size = 768, alignment = 8 { };
"P9":  place in [from 0x2000'5500 to 0x2000'57ff] { block CSTACK };
do not initialize {
   section .noinit, section .bss, section .data, section __DLIB_PERTHREAD,
   section .customSection };
initialize manually with packing = none { section .textrw };
initialize manually with packing = none { section .data };

No sections matched the following patterns:

  section .customSection    in block customSectionBlock
  section m_interrupts_ram  in "P10"


  Section              Kind         Address  Aligment    Size  Object
  -------              ----         -------  --------    ----  ------
"A0":                                                    0xc0
  .intvec                               0x0              0xc0  <Block>
    .intvec            ro code          0x0         4    0xc0  startup_S32K118.o [1]
                                     - 0xc0              0xc0

"P1":                                                    0x10
  FlashConfig          ro code        0x400         4    0x10  startup_S32K118.o [1]
                                    - 0x410              0x10

"P2-P3":                                               0x64da
  .text                ro code        0x410         4  0x1746  clock_S32K1xx.o [1]
  .rodata              const         0x1b56         2     0x2  peripherals_erm_config_1.o [1]
  .text                ro code       0x1b58         4    0x14  I32DivMod.o [28]
  .text                ro code       0x1b6c         4   0xbe4  ftm_pwm_driver.o [1]
  .text                ro code       0x2750         4   0x190  ftm_hw_access.o [1]
  .text                ro code       0x28e0         4   0x614  ftm_common.o [1]
  .text                ro code       0x2ef4         4    0x9c  interrupt_manager.o [1]
  .text                ro code       0x2f90         4   0x684  ftm_ic_driver.o [1]
  .data_init                         0x3614             0x568  <Block>
    Initializer bytes  const         0x3614         4   0x568  <for .data-1>
  .rodata              const         0x3b7c         2     0x2  ftm_common.o [1]
  .text                ro code       0x3b80         4   0x4c8  lpit_driver.o [1]
  .text                ro code       0x4048         4    0x10  I64Mul.o [28]
  .text                ro code       0x4058         4   0x230  I64DivMod.o [28]
  .text                ro code       0x4288         4     0x2  I64DivZer.o [28]
  .rodata              const         0x428a         2     0x2  ftm_common.o [1]
  .text                ro code       0x428c         4   0x490  adc_driver.o [1]
  .text                ro code       0x471c         4    0x66  ABImemset.o [28]
  .rodata              const         0x4782         2     0x2  peripherals_lpit_config_1.o [1]
  .text                ro code       0x4784         4   0x37c  lpspi_master_driver.o [1]
  .text                ro code       0x4b00         4   0x28c  lpspi_hw_access.o [1]
  .text                ro code       0x4d8c         4    0x60  osif_baremetal.o [1]
  .text                ro code       0x4dec         4     0x6  ABImemclr4.o [28]
  .text                ro code       0x4df2         2     0x2  startup_S32K118.o [1]
  .text                ro code       0x4df4         4   0x1ec  lpspi_shared_function.o [1]
  .text                ro code       0x4fe0         4    0x32  ABImemset48.o [28]
  .text                ro code       0x5012         2     0x2  startup_S32K118.o [1]
  .text                ro code       0x5014         4   0x1c0  lpspi_slave_driver.o [1]
  .text                ro code       0x51d4         4   0x358  lptmr_driver.o [1]
  .text                ro code       0x552c         4    0x22  I64Shl.o [28]
  .text                ro code       0x554e         2    0x12  lptmr_hw_access.o [1]
  Veneer               ro code       0x5560         4     0x8  - Linker created -
  .text                ro code       0x5568         4   0x238  flash_driver.o [1]
  .text                ro code       0x57a0         4   0x1bc  erm_driver.o [1]
  .text                ro code       0x595c         2     0xc  erm_hw_access.o [1]
  .text                ro code       0x5968         4   0x1a2  pdb_hw_access.o [1]
  .text                ro code       0x5b0a         2     0x2  startup_S32K118.o [1]
  .text                ro code       0x5b0c         4   0x118  pins_port_hw_access.o [1]
  .text                ro code       0x5c24         4   0x104  crc_hw_access.o [1]
  .text                ro code       0x5d28         4    0xf4  ADCMon_Man.o [1]
  .text                ro code       0x5e1c         4    0xcc  pdb_driver.o [1]
  .text                ro code       0x5ee8         4    0xdc  edma_driver.o [1]
  .text                ro code       0x5fc4         4    0x10  edma_hw_access.o [1]
  .text                ro code       0x5fd4         4    0xd8  startup.o [1]
  .text                ro code       0x60ac         4    0xd4  crc_driver.o [1]
  .text                ro code       0x6180         4    0xb8  nvmman.o [1]
  .text                ro code       0x6238         2    0x2a  conversion.o [1]
  .text                ro code       0x6262         2     0x2  startup_S32K118.o [1]
  .text                ro code       0x6264         4    0x90  ICMon.o [1]
  .rodata              const         0x62f4         4    0x8c  clock_S32K1xx.o [1]
  .text                ro code       0x6380         4    0x74  TimerHwAbs.o [1]
  .text                ro code       0x63f4         2    0x64  SCUInit.o [1]
  .text                ro code       0x6458         4    0x18  WDOGMan.o [1]
  .text                ro code       0x6470         4    0x50  ClockMan.o [1]
  .text                ro code       0x64c0         4    0x5c  ERMHwAbs.o [1]
  .text                ro code       0x651c         4    0x18  DioCtrl_Man.o [1]
  .text                ro code       0x6534         4    0x30  PWMCtrl_Man.o [1]
  .text                ro code       0x6564         4    0x22  ExtDev.o [1]
  .rodata              const         0x6586               0x1  adc_driver.o [1]
  .rodata              const         0x6587               0x1  adc_driver.o [1]
  .text                ro code       0x6588         4    0x18  CRCMan.o [1]
  .text                ro code       0x65a0         4    0x20  system_S32K118.o [1]
  .text                ro code       0x65c0         4    0x1e  pins_driver.o [1]
  .text                ro code       0x65de         2    0x5a  edma_irq.o [1]
  .rodata              const         0x6638         4    0x54  clock_S32K1xx.o [1]
  .rodata              const         0x668c         4    0x54  clock_S32K1xx.o [1]
  .rodata              const         0x66e0         4    0x48  clock_S32K1xx.o [1]
  .text                ro code       0x6728         4    0x3c  startup_S32K118.o [1]
  .text                ro code       0x6764         2     0xc  main.o [1]
  .text                ro code       0x6770         4     0x2  SCUMain.o [1]
  .rodata              const         0x6772               0x1  lpit_driver.o [1]
  .rodata              const         0x6773               0x1  lptmr_driver.o [1]
  __CODE_ROM                         0x6774              0x3c  <Block>
    Initializer bytes  const         0x6774         4    0x3c  <for __CODE_RAM-1>
  .rodata              const         0x67b0         4    0x20  lpspi_hw_access.o [1]
  .rodata              const         0x67d0         4    0x20  peripherals_lpspi_1.o [1]
  .rodata              const         0x67f0         4    0x14  peripherals_flash_1.o [1]
  .text                ro code       0x6804         2    0x14  lpspi_irq.o [1]
  .rodata              const         0x6818         4    0x10  peripherals_crc_1.o [1]
  .rodata              const         0x6828         4    0x10  ftm_common.o [1]
  .rodata              const         0x6838         4    0x10  peripherals_lptmr_1.o [1]
  .rodata              const         0x6848         4     0xc  peripherals_adc_config_1.o [1]
  .rodata              const         0x6854         4     0xc  clock_S32K1xx.o [1]
  .rodata              const         0x6860         4     0xc  clock_S32K1xx.o [1]
  .rodata              const         0x686c         4     0x8  peripherals_erm_config_1.o [1]
  .rodata              const         0x6874         4     0x8  ftm_common.o [1]
  .rodata              const         0x687c         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x6884         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x688c         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x6894         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x689c         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x68a4         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x68ac         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x68b4         4     0x8  peripherals_pdb_config_1.o [1]
  .rodata              const         0x68bc         4     0x4  adc_driver.o [1]
  .rodata              const         0x68c0         4     0x4  clock_S32K1xx.o [1]
  .rodata              const         0x68c4         4     0x4  crc_driver.o [1]
  .rodata              const         0x68c8         4     0x4  erm_driver.o [1]
  .rodata              const         0x68cc         4     0x4  lpit_driver.o [1]
  .rodata              const         0x68d0         4     0x4  lpit_driver.o [1]
  .rodata              const         0x68d4         4     0x4  edma_driver.o [1]
  .rodata              const         0x68d8         4     0x4  lptmr_driver.o [1]
  .rodata              const         0x68dc         4     0x4  pdb_driver.o [1]
  .rodata              const         0x68e0         4     0x4  startup.o [1]
  .text                ro code       0x68e4         2     0x4  startup_S32K118.o [1]
  .rodata              const         0x68e8               0x1  pdb_driver.o [1]
  .rodata              const         0x68e9               0x1  pdb_driver.o [1]
                                   - 0x68ea            0x64da

"P4-P5":                                                0x5a4
  RW                            0x2000'00c0             0x568  <Block>
    .data                       0x2000'00c0             0x568  <Block>
      .data-1                   0x2000'00c0         4   0x566  <Init block>
        .data          inited   0x2000'00c0         4     0x4  peripherals_adc_config_1.o [1]
        .data          inited   0x2000'00c4         4     0x4  peripherals_adc_config_1.o [1]
        .data          inited   0x2000'00c8         4     0x4  peripherals_adc_config_1.o [1]
        .data          inited   0x2000'00cc         4     0x4  peripherals_adc_config_1.o [1]
        .data          inited   0x2000'00d0         4     0x4  peripherals_adc_config_1.o [1]
        .data          inited   0x2000'00d4         4     0x4  peripherals_adc_config_1.o [1]
        .data          inited   0x2000'00d8         4    0x74  clock_S32K1xx.o [1]
        .data          inited   0x2000'014c         4    0x50  clock_config.o [1]
        .data          inited   0x2000'019c         4    0x80  clock_config.o [1]
        .data          inited   0x2000'021c         4    0x14  peripherals_flexTimer_ic_1.o [1]
        .data          inited   0x2000'0230         4     0x8  peripherals_flexTimer_ic_1.o [1]
        .data          inited   0x2000'0238         4    0x20  peripherals_flexTimer_ic_1.o [1]
        .data          inited   0x2000'0258         4    0x14  peripherals_flexTimer_pwm_1.o [1]
        .data          inited   0x2000'026c         4    0x20  peripherals_flexTimer_pwm_1.o [1]
        .data          inited   0x2000'028c         4    0x18  peripherals_flexTimer_pwm_1.o [1]
        .data          inited   0x2000'02a4         4    0x18  peripherals_lpit_config_1.o [1]
        .data          inited   0x2000'02bc         4     0x8  lpspi_shared_function.o [1]
        .data          inited   0x2000'02c4         4   0x360  pin_mux.o [1]
        .data          inited   0x2000'0624         2     0x2  lpspi_shared_function.o [1]
  __CODE_RAM                    0x2000'0628              0x3c  <Block>
    __CODE_RAM-1                0x2000'0628         4    0x3c  <Init block>
      .textrw          inited   0x2000'0628         4    0x3c  flash_driver.o [1]
                              - 0x2000'0664             0x5a4

"P7":                                                   0x172
  ZI                            0x2000'30c0             0x172  <Block>
    .bss                        0x2000'30c0             0x172  <Block>
      .bss             uninit   0x2000'30c0         4     0x4  peripherals_adc_config_1.o [1]
      .bss             uninit   0x2000'30c4         4     0xc  clock_S32K1xx.o [1]
      .bss             uninit   0x2000'30d0         4     0x4  clock_S32K1xx.o [1]
      .bss             uninit   0x2000'30d4         4     0x4  clock_S32K1xx.o [1]
      .bss             uninit   0x2000'30d8         4     0x8  ftm_common.o [1]
      .bss             uninit   0x2000'30e0         4    0x10  peripherals_flexTimer_pwm_1.o [1]
      .bss             uninit   0x2000'30f0         4     0x4  interrupt_manager.o [1]
      .bss             uninit   0x2000'30f4         4     0x4  lpit_driver.o [1]
      .bss             uninit   0x2000'30f8         4     0x4  edma_driver.o [1]
      .bss             uninit   0x2000'30fc         4     0x8  lpspi_shared_function.o [1]
      .bss             uninit   0x2000'3104         4     0x4  osif_baremetal.o [1]
      .bss             uninit   0x2000'3108         4    0x34  peripherals_lpspi_1.o [1]
      .bss             uninit   0x2000'313c         4     0x4  ERMHwAbs.o [1]
      .bss             uninit   0x2000'3140         4     0x4  ICMon.o [1]
      .bss             uninit   0x2000'3144         4    0x64  ICMon.o [1]
      .bss             uninit   0x2000'31a8         4     0x4  ICMon.o [1]
      .bss             uninit   0x2000'31ac         4    0x1c  nvmman.o [1]
      .bss             uninit   0x2000'31c8         4    0x64  PWMCtrl_Man.o [1]
      .bss             uninit   0x2000'322c         4     0x4  TimerHwAbs.o [1]
      .bss             uninit   0x2000'3230               0x1  ICMon.o [1]
      .bss             uninit   0x2000'3231               0x1  nvmman.o [1]
                              - 0x2000'3232             0x172

"P9":                                                   0x300
  CSTACK                        0x2000'5500         8   0x300  <Block>
    CSTACK             uninit   0x2000'5500             0x300  <Block tail>
                              - 0x2000'5800             0x300

Unused ranges:

         From           To      Size
         ----           --      ----
       0x68ea     0x3'ffff  0x3'9716
  0x2000'0664  0x2000'30bf    0x2a5c
  0x2000'3232  0x2000'54ff    0x22ce



*******************************************************************************
*** MODULE SUMMARY
***

    Module                         ro code  rw code  ro data  rw data
    ------                         -------  -------  -------  -------
command line/config:
    -----------------------------------------------------------------
    Total:

A:\INEOSDevStrm\SCUMain_ert_rtw: [1]
    ADCMon_Man.o                       244
    CRCMan.o                            24
    ClockMan.o                          80
    DioCtrl_Man.o                       24
    ERMHwAbs.o                          92                          4
    ExtDev.o                            34
    ICMon.o                            144                        109
    PWMCtrl_Man.o                       48                        100
    SCUInit.o                          100
    SCUMain.o                            2
    TimerHwAbs.o                       116                          4
    WDOGMan.o                           24
    adc_driver.o                     1'168                 6
    clock_S32K1xx.o                  5'958               524      136
    clock_config.o                                       208      208
    conversion.o                        42
    crc_driver.o                       212                 4
    crc_hw_access.o                    260
    edma_driver.o                      220                 4        4
    edma_hw_access.o                    16
    edma_irq.o                          90
    erm_driver.o                       444                 4
    erm_hw_access.o                     12
    flash_driver.o                     568       60       60
    ftm_common.o                     1'556                28        8
    ftm_hw_access.o                    400
    ftm_ic_driver.o                  1'668
    ftm_pwm_driver.o                 3'044
    interrupt_manager.o                156                          4
    lpit_driver.o                    1'224                 9        4
    lpspi_hw_access.o                  652                32
    lpspi_irq.o                         20
    lpspi_master_driver.o              892
    lpspi_shared_function.o            492                10       18
    lpspi_slave_driver.o               448
    lptmr_driver.o                     856                 5
    lptmr_hw_access.o                   18
    main.o                              12
    nvmman.o                           184                         29
    osif_baremetal.o                    96                          4
    pdb_driver.o                       204                 6
    pdb_hw_access.o                    418
    peripherals_adc_config_1.o                            36       28
    peripherals_crc_1.o                                   16
    peripherals_erm_config_1.o                            10
    peripherals_flash_1.o                                 20
    peripherals_flexTimer_ic_1.o                          60       60
    peripherals_flexTimer_pwm_1.o                         76       92
    peripherals_lpit_config_1.o                           26       24
    peripherals_lpspi_1.o                                 32       52
    peripherals_lptmr_1.o                                 16
    peripherals_pdb_config_1.o                            64
    pin_mux.o                                            864      864
    pins_driver.o                       30
    pins_port_hw_access.o              280
    startup.o                          216                 4
    startup_S32K118.o                  280
    system_S32K118.o                    32
    -----------------------------------------------------------------
    Total:                          23'100       60    2'124    1'752

libADCMon_Mdl_rtwlib.a: [2]
    -----------------------------------------------------------------
    Total:

libAPDet_Mdl_rtwlib.a: [3]
    -----------------------------------------------------------------
    Total:

libAmbTempMon_Mdl_rtwlib.a: [4]
    -----------------------------------------------------------------
    Total:

libBlockDet_Mdl_rtwlib.a: [5]
    -----------------------------------------------------------------
    Total:

libCfgParam_Mdl_rtwlib.a: [6]
    -----------------------------------------------------------------
    Total:

libComLog_Mdl_rtwlib.a: [7]
    -----------------------------------------------------------------
    Total:

libCtrlLog_Mdl_rtwlib.a: [8]
    -----------------------------------------------------------------
    Total:

libDIOCtrl_Mdl_rtwlib.a: [9]
    -----------------------------------------------------------------
    Total:

libLearnAdap_Mdl_rtwlib.a: [10]
    -----------------------------------------------------------------
    Total:

libMOSFETTempMon_Mdl_rtwlib.a: [11]
    -----------------------------------------------------------------
    Total:

libMtrCtrl_Mdl_rtwlib.a: [12]
    -----------------------------------------------------------------
    Total:

libMtrMdl_Mdl_rtwlib.a: [13]
    -----------------------------------------------------------------
    Total:

libPWMCtrl_Mdl_rtwlib.a: [14]
    -----------------------------------------------------------------
    Total:

libPnlOp_Mdl_rtwlib.a: [15]
    -----------------------------------------------------------------
    Total:

libPosMon_Mdl_rtwlib.a: [16]
    -----------------------------------------------------------------
    Total:

libRefField_Mdl_rtwlib.a: [17]
    -----------------------------------------------------------------
    Total:

libRefForce_Mdl_rtwlib.a: [18]
    -----------------------------------------------------------------
    Total:

libStateMach_Mdl_rtwlib.a: [19]
    -----------------------------------------------------------------
    Total:

libSwitchLog_Mdl_rtwlib.a: [20]
    -----------------------------------------------------------------
    Total:

libTaskSch_Mdl_rtwlib.a: [21]
    -----------------------------------------------------------------
    Total:

libThermProt_Mdl_rtwlib.a: [22]
    -----------------------------------------------------------------
    Total:

libTheshForce_Mdl_rtwlib.a: [23]
    -----------------------------------------------------------------
    Total:

libUsgHis_Mdl_rtwlib.a: [24]
    -----------------------------------------------------------------
    Total:

libVehCom_Mdl_rtwlib.a: [25]
    -----------------------------------------------------------------
    Total:

libVoltMon_Mdl_rtwlib.a: [26]
    -----------------------------------------------------------------
    Total:

m7M_tl.a: [27]
    -----------------------------------------------------------------
    Total:

rt7M_tl.a: [28]
    ABImemclr4.o                         6
    ABImemset.o                        102
    ABImemset48.o                       50
    I32DivMod.o                         20
    I64DivMod.o                        560
    I64DivZer.o                          2
    I64Mul.o                            16
    I64Shl.o                            34
    -----------------------------------------------------------------
    Total:                             790

    Gaps                                 2
    Linker created                       8                 2      768
---------------------------------------------------------------------
    Grand Total:                    23'900       60    2'126    2'520


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address   Size  Type      Object
-----                       -------   ----  ----      ------
.bss$$Base              0x2000'30c0          --   ??  - Linker created -
.bss$$Limit             0x2000'3232          --   ??  - Linker created -
.customSection$$Base            0x0          --   ??  - Linker created -
.customSection_init$$Base
                                0x0          --   ??  - Linker created -
.customSection_init$$Limit
                                0x0          --   ??  - Linker created -
.data$$Base             0x2000'00c0          --   ??  - Linker created -
.data$$Limit            0x2000'0628          --   Gb  - Linker created -
.data_init$$Base             0x3614          --   ??  - Linker created -
.data_init$$Limit            0x3b7c          --   ??  - Linker created -
.intvec$$Base                   0x0          --   ??  - Linker created -
.intvec$$Limit                 0xc0          --   Gb  - Linker created -
ADC0_IRQHandler              0x68e5         Code  Wk  startup_S32K118.o [1]
ADCMon_vInit                 0x5d29   0xb2  Code  ??  ADCMon_Man.o [1]
ADC_DRV_AutoCalibration
                             0x45f1  0x11e  Code  ??  adc_driver.o [1]
ADC_DRV_ConfigChan           0x45c5   0x18  Code  ??  adc_driver.o [1]
ADC_DRV_ConfigConverter
                             0x4531   0x90  Code  ??  adc_driver.o [1]
ADC_GetCalibrationActiveFlag
                             0x4491   0x16  Code  Lc  adc_driver.o [1]
ADC_GetClockDivide           0x428d    0xc  Code  Lc  adc_driver.o [1]
ADC_GetHwAverageEnableFlag
                             0x4435   0x16  Code  Lc  adc_driver.o [1]
ADC_GetHwAverageMode         0x446f    0xc  Code  Lc  adc_driver.o [1]
ADC_GetSampleTime            0x42ad    0x8  Code  Lc  adc_driver.o [1]
ADC_GetTriggerMode           0x42f5    0xe  Code  Lc  adc_driver.o [1]
ADC_SetCalibrationActiveFlag
                             0x44a7   0x24  Code  Lc  adc_driver.o [1]
ADC_SetClockDivide           0x4299   0x14  Code  Lc  adc_driver.o [1]
ADC_SetContinuousConvFlag
                             0x43ef   0x24  Code  Lc  adc_driver.o [1]
ADC_SetDMAEnableFlag         0x43b5   0x24  Code  Lc  adc_driver.o [1]
ADC_SetHwAverageEnableFlag
                             0x444b   0x24  Code  Lc  adc_driver.o [1]
ADC_SetHwAverageMode         0x447b   0x16  Code  Lc  adc_driver.o [1]
ADC_SetInputChannel          0x44cb   0x66  Code  Lc  adc_driver.o [1]
ADC_SetInputClock            0x42e3   0x12  Code  Lc  adc_driver.o [1]
ADC_SetPretriggerSelect
                             0x431b   0x4e  Code  Lc  adc_driver.o [1]
ADC_SetResolution            0x42cf   0x14  Code  Lc  adc_driver.o [1]
ADC_SetSampleTime            0x42b5   0x1a  Code  Lc  adc_driver.o [1]
ADC_SetSupplyMonitoringEnableFlag
                             0x4413   0x1e  Code  Lc  adc_driver.o [1]
ADC_SetTriggerMode           0x4303   0x18  Code  Lc  adc_driver.o [1]
ADC_SetTriggerSelect         0x4369   0x4c  Code  Lc  adc_driver.o [1]
ADC_SetVoltageReference
                             0x43d9   0x16  Code  Lc  adc_driver.o [1]
CAN0_ORed_0_31_MB_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
CAN0_ORed_Err_Wakeup_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
CLOCK_DRV_GetFreq            0x1245   0x38  Code  ??  clock_S32K1xx.o [1]
CLOCK_DRV_Init                0xb03   0x42  Code  ??  clock_S32K1xx.o [1]
CLOCK_SYS_CheckPCCClock
                             0x1103   0x54  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_ConfigureFIRC
                             0x1567   0x8a  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_ConfigureModulesFromScg
                             0x1721  0x10c  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_ConfigureModulesFromScg::tmpSysClk
                             0x6860    0xc  Data  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_ConfigureSIRC
                             0x14d7   0x90  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_ConfigureSOSC
                             0x15fd   0xd6  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_ConfigureTemporarySystemClock
                             0x16d3   0x4a  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_ConfigureTemporarySystemClock::tmpSysClk
                             0x6854    0xc  Data  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetCurrentRunMode
                             0x1341   0x26  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetDefaultConfiguration
                              0xd4d  0x15e  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetDefaultConfiguration::peripheralClockConfig
                        0x2000'00d8   0x74  Data  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetFircFreq        0x1aaf   0x22  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetFircFreq::fircFreq
                             0x68c0    0x4  Data  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetFreq            0x1b4d    0xa  Code  ??  clock_S32K1xx.o [1]
CLOCK_SYS_GetFtmOptionFreq
                             0x127d   0x26  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetLpoFreq         0x1ad1   0x5e  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetPccClockFreq
                             0x115d   0xda  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetPeripheralClock
                             0x12a3   0x9e  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetScgClkOutFreq
                             0x1455   0x36  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetScgClockFreq
                              0xebd   0xd0  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSimClkOutFreq
                             0x13a9   0xa8  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSimClockFreq
                              0xf9d  0x166  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSimRtcClkFreq
                             0x148b   0x4c  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSircFreq        0x1a8f   0x20  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSrcFreq         0x18c1   0x2c  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSysAsyncFreq
                             0x19d5   0x9e  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSysOscFreq
                             0x1a79   0x16  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_GetSystemClockFreq
                             0x182d   0x84  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_SetPccConfiguration
                              0xbbf   0x68  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_SetPmcConfiguration
                              0xd27   0x20  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_SetScgConfiguration
                              0xb45   0x7a  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_SetSimConfiguration
                              0xc2d   0xfa  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_SetSystemClockConfig
                             0x18ed   0xe4  Code  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_SetSystemClockConfig::maxSysClksInRUN
                             0x668c   0x54  Data  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_SetSystemClockConfig::maxSysClksInVLPR
                             0x6638   0x54  Data  Lc  clock_S32K1xx.o [1]
CLOCK_SYS_TransitionSystemClock
                             0x1367   0x3c  Code  Lc  clock_S32K1xx.o [1]
CMP0_IRQHandler              0x68e5         Code  Wk  startup_S32K118.o [1]
CRCMan_enInit                0x6589   0x18  Code  ??  CRCMan.o [1]
CRC_DRV_Configure            0x612f   0x40  Code  ??  crc_driver.o [1]
CRC_DRV_Init                 0x610f   0x20  Code  ??  crc_driver.o [1]
CRC_Init                     0x5cad   0x46  Code  ??  crc_hw_access.o [1]
CRC_SetDataReg               0x5c25    0x4  Code  Lc  crc_hw_access.o [1]
CRC_SetFXorMode              0x60b1   0x22  Code  Lc  crc_driver.o [1]
CRC_SetFXorMode              0x5c4f   0x22  Code  Lc  crc_hw_access.o [1]
CRC_SetPolyReg               0x60ad    0x4  Code  Lc  crc_driver.o [1]
CRC_SetPolyReg               0x5c29    0x4  Code  Lc  crc_hw_access.o [1]
CRC_SetProtocolWidth         0x60d3   0x16  Code  Lc  crc_driver.o [1]
CRC_SetProtocolWidth         0x5c71   0x16  Code  Lc  crc_hw_access.o [1]
CRC_SetReadTranspose         0x60f9   0x16  Code  Lc  crc_driver.o [1]
CRC_SetReadTranspose         0x5c97   0x16  Code  Lc  crc_hw_access.o [1]
CRC_SetSeedOrDataMode        0x5c2d   0x22  Code  Lc  crc_hw_access.o [1]
CRC_SetSeedReg               0x5d09   0x20  Code  ??  crc_hw_access.o [1]
CRC_SetWriteTranspose        0x60e9   0x10  Code  Lc  crc_driver.o [1]
CRC_SetWriteTranspose        0x5c87   0x10  Code  Lc  crc_hw_access.o [1]
CSTACK$$Base            0x2000'5500          --   Gb  - Linker created -
CSTACK$$Limit           0x2000'5800          --   ??  - Linker created -
ClockMan_enInit              0x6471   0x12  Code  ??  ClockMan.o [1]
ClockMan_vInitCMU            0x6483   0x26  Code  ??  ClockMan.o [1]
DIOCtrl_enInit               0x651d   0x14  Code  ??  DioCtrl_Man.o [1]
DMA0_IRQHandler              0x65e3    0xa  Code  ??  edma_irq.o [1]
DMA1_IRQHandler              0x65ed    0xa  Code  ??  edma_irq.o [1]
DMA2_IRQHandler              0x65f7    0xa  Code  ??  edma_irq.o [1]
DMA3_IRQHandler              0x6601    0xa  Code  ??  edma_irq.o [1]
DMA_Error_IRQHandler         0x660b   0x2e  Code  ??  edma_irq.o [1]
DefaultISR                   0x68e5         Code  Wk  startup_S32K118.o [1]
EDMA_ClearDoneStatusFlag
                             0x5eed    0x4  Code  Lc  edma_driver.o [1]
EDMA_ClearErrorIntStatusFlag
                             0x5ee9    0x4  Code  Lc  edma_driver.o [1]
EDMA_ClearIntStatusFlag
                             0x5ef1    0x4  Code  Lc  edma_driver.o [1]
EDMA_DRV_ClearIntStatus
                             0x5ef5   0x2c  Code  Lc  edma_driver.o [1]
EDMA_DRV_ErrorIRQHandler
                             0x5f4b   0x5c  Code  ??  edma_driver.o [1]
EDMA_DRV_GetDmaRegBaseAddr
                             0x5fb5    0xa  Code  ??  edma_driver.o [1]
EDMA_DRV_IRQHandler          0x5f21   0x2a  Code  ??  edma_driver.o [1]
EDMA_GetErrorIntStatusFlag
                             0x65df    0x4  Code  Lc  edma_irq.o [1]
EDMA_SetDmaRequestCmd        0x5fc5   0x10  Code  ??  edma_hw_access.o [1]
ERMHwAbs_ISR                 0x64c1   0x30  Code  ??  ERMHwAbs.o [1]
ERMHwAbs_vInit               0x64f1   0x1e  Code  ??  ERMHwAbs.o [1]
ERM_ClearEventDouble         0x584b    0xe  Code  Lc  erm_driver.o [1]
ERM_ClearEventSingle         0x583d    0xe  Code  Lc  erm_driver.o [1]
ERM_DRV_ClearEvent           0x58e7   0x28  Code  ??  erm_driver.o [1]
ERM_DRV_GetErrorDetail       0x590f   0x4a  Code  ??  erm_driver.o [1]
ERM_DRV_Init                 0x5869   0x50  Code  ??  erm_driver.o [1]
ERM_DRV_SetInterruptConfig
                             0x58b9   0x2e  Code  ??  erm_driver.o [1]
ERM_EnableEventInterrupt
                             0x57a1   0x5c  Code  Lc  erm_driver.o [1]
ERM_GetLastErrorAddress
                             0x5859   0x10  Code  Lc  erm_driver.o [1]
ERM_Init                     0x595d    0xc  Code  ??  erm_hw_access.o [1]
ERM_IsEventDetected          0x57fd   0x40  Code  Lc  erm_driver.o [1]
ERM_fault_IRQHandler         0x68e5         Code  Wk  startup_S32K118.o [1]
ExtDev_enInit                0x6565   0x16  Code  ??  ExtDev.o [1]
FLASH_DRV_CommandSequence
                        0x2000'0629   0x3c  Code  Lc  flash_driver.o [1]
FLASH_DRV_DEFlashPartition
                             0x5709   0x50  Code  ??  flash_driver.o [1]
FLASH_DRV_EnableDoubleBitFaultInterupt
                             0x5759   0x16  Code  ??  flash_driver.o [1]
FLASH_DRV_GetDEPartitionCode
                             0x5569   0xb6  Code  Lc  flash_driver.o [1]
FLASH_DRV_Init               0x561f   0x48  Code  ??  flash_driver.o [1]
FLASH_DRV_SetFlexRamFunction
                             0x566d   0x86  Code  ??  flash_driver.o [1]
FLEXIO_IRQHandler            0x68e5         Code  Wk  startup_S32K118.o [1]
FTFC_IRQHandler              0x68e5         Code  Wk  startup_S32K118.o [1]
FTM0_Ch0_7_IRQHandler        0x3459   0x54  Code  ??  ftm_ic_driver.o [1]
FTM0_Fault_IRQHandler        0x68e5         Code  Wk  startup_S32K118.o [1]
FTM0_Ovf_Reload_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
FTM1_Ch0_7_IRQHandler        0x34ad   0x54  Code  ??  ftm_ic_driver.o [1]
FTM1_Fault_IRQHandler        0x68e5         Code  Wk  startup_S32K118.o [1]
FTM1_Ovf_Reload_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
FTM_DRV_ClearChnEventFlag
                             0x30a5   0x1c  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_ClearTimerOverflow
                             0x1ba7    0xa  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_ConvertFreqToPeriodTicks
                             0x2ed3   0x14  Code  ??  ftm_common.o [1]
FTM_DRV_Enable               0x2763    0xe  Code  Lc  ftm_hw_access.o [1]
FTM_DRV_Enable               0x1d3b    0xe  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_EnableChnInt         0x3089   0x1c  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_EnablePwmChannelOutputs
                             0x1b87   0x20  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_GetChnCountVal       0x2f97    0xe  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_GetClockPs           0x28e1    0x8  Code  Lc  ftm_common.o [1]
FTM_DRV_GetClockSource       0x1bcd    0xa  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_GetCpwms             0x1bc1    0xc  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_GetDetectedFaultInput
                             0x295d    0xc  Code  Lc  ftm_common.o [1]
FTM_DRV_GetDualChnCombineCmd
                             0x1e81   0x10  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_GetDualChnMofCombineCmd
                             0x1e91   0x1c  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_GetDualEdgeCaptureBit
                             0x3125   0x1c  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_GetEnabledInterrupts
                             0x2d85   0x6a  Code  ??  ftm_common.o [1]
FTM_DRV_GetFrequency         0x2e75   0x5e  Code  ??  ftm_common.o [1]
FTM_DRV_GetMod               0x2f91    0x6  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_GetMod               0x1b6d    0x6  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_GetReloadFlag        0x2911    0xc  Code  Lc  ftm_common.o [1]
FTM_DRV_GetStatusFlags       0x2def   0x7e  Code  ??  ftm_common.o [1]
FTM_DRV_HasChnEventOccurred
                             0x30c1   0x14  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_HasChnEventOccurred
                             0x293d   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_HasTimerOverflowed
                             0x2905    0xc  Code  Lc  ftm_common.o [1]
FTM_DRV_Init                 0x2b31  0x102  Code  ??  ftm_common.o [1]
FTM_DRV_InitInputCapture
                             0x323b  0x20e  Code  ??  ftm_ic_driver.o [1]
FTM_DRV_InitMeasurement
                             0x3141   0xfa  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_InitModule           0x2771   0x1a  Code  ??  ftm_hw_access.o [1]
FTM_DRV_InitPwm              0x23f7  0x1d2  Code  ??  ftm_pwm_driver.o [1]
FTM_DRV_InitPwmCombinedChannel
                             0x2139  0x222  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_InitPwmDutyCycleChannel
                             0x2361   0x96  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_InitPwmIndependentChannel
                             0x1f4f  0x1de  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_InputCaptureHandler
                             0x351f   0xec  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_IrqHandler           0x3501   0x1e  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_IsChnIntEnabled
                             0x2929   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_IsChnTriggerGenerated
                             0x29c7    0xc  Code  Lc  ftm_common.o [1]
FTM_DRV_IsFaultIntEnabled
                             0x2951    0xc  Code  Lc  ftm_common.o [1]
FTM_DRV_IsOverflowIntEnabled
                             0x28f9    0xc  Code  Lc  ftm_common.o [1]
FTM_DRV_IsReloadIntEnabled
                             0x291d    0xc  Code  Lc  ftm_common.o [1]
FTM_DRV_Reset                0x278b   0xa2  Code  ??  ftm_hw_access.o [1]
FTM_DRV_SetBdmMode           0x29d3   0x18  Code  Lc  ftm_common.o [1]
FTM_DRV_SetChnCountVal       0x1c97    0xe  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetChnEdgeLevel
                             0x3037   0x52  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetChnEdgeLevel
                             0x1c45   0x52  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetChnFaultInputPolarityCmd
                             0x1cc5   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetChnInputCaptureFilter
                             0x286d   0x68  Code  ??  ftm_hw_access.o [1]
FTM_DRV_SetChnMSnBAMode
                             0x2fe5   0x52  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetChnMSnBAMode
                             0x1bf3   0x52  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetChnOutputPolarityCmd
                             0x1ca5   0x20  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetChnTriggerCmd
                             0x282d   0x40  Code  ??  ftm_hw_access.o [1]
FTM_DRV_SetClockPs           0x2751   0x12  Code  Lc  ftm_hw_access.o [1]
FTM_DRV_SetClockSource       0x2fa5   0x14  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetClockSource       0x1b73   0x14  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetCntinPwmSyncModeCmd
                             0x2b19   0x18  Code  Lc  ftm_common.o [1]
FTM_DRV_SetCounterHardwareSyncModeCmd
                             0x2a3b   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetCounterInitVal
                             0x2fd7    0xe  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetCounterInitVal
                             0x1be5    0xe  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetCounterSoftwareSyncModeCmd
                             0x2ab1   0x1a  Code  Lc  ftm_common.o [1]
FTM_DRV_SetCpwms             0x2fb9   0x10  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetCpwms             0x1bb1   0x10  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDeadtimeCount
                             0x1ec1   0x12  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDeadtimePrescale
                             0x1ead   0x14  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDualChnCombineCmd
                             0x1e59   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDualChnCompCmd
                             0x1e31   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDualChnDeadtimeCmd
                             0x1de1   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDualChnDecapCmd
                             0x30d5   0x28  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetDualChnFaultCmd
                             0x1d91   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDualChnMofCombineCmd
                             0x1d69   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDualChnPwmSyncCmd
                             0x1db9   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetDualEdgeCaptureCmd
                             0x30fd   0x28  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetDualEdgeCaptureCmd
                             0x1e09   0x28  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetFaultControlMode
                             0x1cfd   0x12  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetFaultInputCmd
                             0x1f11   0x20  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetFaultInputFilterCmd
                             0x1eed   0x24  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetFaultInputFilterVal
                             0x1ed3   0x14  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetFaultInt          0x1ced   0x10  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetHardwareSyncTriggerSrc
                             0x2969   0x20  Code  Lc  ftm_common.o [1]
FTM_DRV_SetHwTriggerSyncModeCmd
                             0x2a9f   0x12  Code  Lc  ftm_common.o [1]
FTM_DRV_SetInitChnOutputCmd
                             0x1d0f   0x10  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetInitTriggerCmd
                             0x29b7   0x10  Code  Lc  ftm_common.o [1]
FTM_DRV_SetInvctrlHardwareSyncModeCmd
                             0x29ff   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetInvctrlPwmSyncModeCmd
                             0x2b01   0x18  Code  Lc  ftm_common.o [1]
FTM_DRV_SetInvctrlSoftwareSyncModeCmd
                             0x2a63   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetMaxLoadingCmd
                             0x2999   0x10  Code  Lc  ftm_common.o [1]
FTM_DRV_SetMinLoadingCmd
                             0x29a9    0xe  Code  Lc  ftm_common.o [1]
FTM_DRV_SetMod               0x2fc9    0xe  Code  Lc  ftm_ic_driver.o [1]
FTM_DRV_SetMod               0x1bd7    0xe  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetModCntinCvHardwareSyncModeCmd
                             0x2a27   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetModCntinCvSoftwareSyncModeCmd
                             0x2a8b   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetOutmaskHardwareSyncModeCmd
                             0x2a13   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetOutmaskPwmSyncModeCmd
                             0x2989   0x10  Code  Lc  ftm_common.o [1]
FTM_DRV_SetOutmaskSoftwareSyncModeCmd
                             0x2a77   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetPwmFaultBehavior
                             0x1f31   0x1e  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetPwmSyncMode       0x1d49   0x10  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetPwmSyncModeCmd
                             0x2ad5   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetSoftwareTriggerCmd
                             0x1d59   0x10  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_SetSwoctrlHardwareSyncModeCmd
                             0x29eb   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetSwoctrlPwmSyncModeCmd
                             0x2ae9   0x18  Code  Lc  ftm_common.o [1]
FTM_DRV_SetSwoctrlSoftwareSyncModeCmd
                             0x2a4f   0x14  Code  Lc  ftm_common.o [1]
FTM_DRV_SetSync              0x2c5d  0x124  Code  ??  ftm_common.o [1]
FTM_DRV_SetTimerOverflowInt
                             0x28e9   0x10  Code  Lc  ftm_common.o [1]
FTM_DRV_SetWriteProtectionCmd
                             0x1d1f   0x1c  Code  Lc  ftm_pwm_driver.o [1]
FTM_DRV_UpdatePwmChannel
                             0x25cd  0x17a  Code  ??  ftm_pwm_driver.o [1]
Flash_InitConfig0            0x67f0   0x14  Data  ??  peripherals_flash_1.o [1]
HardFault_Handler            0x5013         Code  Wk  startup_S32K118.o [1]
ICMntr_enInit                0x6265   0x22  Code  ??  ICMon.o [1]
ICMntr_vFTM0CH2RisingEdgeCallback
                             0x6287   0x34  Code  ??  ICMon.o [1]
ICMntr_vFTM0CH2RisingEdgeCallback::u32PeriodStart
                        0x2000'31a8    0x4  Data  Lc  ICMon.o [1]
ICMntr_vFTM0CH6RisingEdgeCallback
                             0x62bb    0x8  Code  ??  ICMon.o [1]
INT_SYS_DisableIRQ           0x2f3b   0x16  Code  ??  interrupt_manager.o [1]
INT_SYS_DisableIRQGlobal
                             0x2f6d    0xc  Code  ??  interrupt_manager.o [1]
INT_SYS_EnableIRQ            0x2f25   0x16  Code  ??  interrupt_manager.o [1]
INT_SYS_EnableIRQGlobal
                             0x2f51   0x1a  Code  ??  interrupt_manager.o [1]
INT_SYS_InstallHandler       0x2ef5   0x30  Code  ??  interrupt_manager.o [1]
LPI2C0_Master_Slave_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
LPIT0_IRQHandler             0x68e5         Code  Wk  startup_S32K118.o [1]
LPIT_DRV_GetInterruptFlagTimerChannels
                             0x4031   0x10  Code  ??  lpit_driver.o [1]
LPIT_DRV_Init                0x3e05   0x5e  Code  ??  lpit_driver.o [1]
LPIT_DRV_InitChannel         0x3e63   0xc0  Code  ??  lpit_driver.o [1]
LPIT_DRV_SetTimerPeriodByCount
                             0x4015   0x10  Code  ??  lpit_driver.o [1]
LPIT_DRV_SetTimerPeriodByUs
                             0x3f45   0xb2  Code  ??  lpit_driver.o [1]
LPIT_DRV_StartTimerChannels
                             0x3f23   0x10  Code  ??  lpit_driver.o [1]
LPIT_DisableInterruptTimerChannels
                             0x3be3    0x8  Code  Lc  lpit_driver.o [1]
LPIT_Enable                  0x3b81   0x20  Code  Lc  lpit_driver.o [1]
LPIT_EnableInterruptTimerChannels
                             0x3bdb    0x8  Code  Lc  lpit_driver.o [1]
LPIT_GetInterruptFlagTimerChannels
                             0x3beb    0x8  Code  Lc  lpit_driver.o [1]
LPIT_GetTimerChannelModeCmd
                             0x3c27   0x36  Code  Lc  lpit_driver.o [1]
LPIT_Reset                   0x3ba1   0x28  Code  Lc  lpit_driver.o [1]
LPIT_SetReloadOnTriggerCmd
                             0x3cc7   0x3e  Code  Lc  lpit_driver.o [1]
LPIT_SetStartOnTriggerCmd
                             0x3d43   0x3e  Code  Lc  lpit_driver.o [1]
LPIT_SetStopOnInterruptCmd
                             0x3d05   0x3e  Code  Lc  lpit_driver.o [1]
LPIT_SetTimerChannelChainCmd
                             0x3d81   0x3c  Code  Lc  lpit_driver.o [1]
LPIT_SetTimerChannelModeCmd
                             0x3bf3   0x34  Code  Lc  lpit_driver.o [1]
LPIT_SetTimerPeriodByCount
                             0x3bd1    0xa  Code  Lc  lpit_driver.o [1]
LPIT_SetTimerRunInDebugCmd
                             0x3dbd   0x24  Code  Lc  lpit_driver.o [1]
LPIT_SetTimerRunInDozeCmd
                             0x3de1   0x24  Code  Lc  lpit_driver.o [1]
LPIT_SetTriggerSelectCmd
                             0x3c5d   0x34  Code  Lc  lpit_driver.o [1]
LPIT_SetTriggerSourceCmd
                             0x3c91   0x36  Code  Lc  lpit_driver.o [1]
LPIT_StartTimerChannels
                             0x3bc9    0x8  Code  Lc  lpit_driver.o [1]
LPSPI0_IRQHandler            0x6805    0xa  Code  ??  lpspi_irq.o [1]
LPSPI1_IRQHandler            0x680f    0xa  Code  ??  lpspi_irq.o [1]
LPSPI_ClearContCBit          0x4e39    0xa  Code  Lc  lpspi_shared_function.o [1]
LPSPI_ClearStatusFlag        0x4b65   0x28  Code  ??  lpspi_hw_access.o [1]
LPSPI_DRV_DisableTEIEInterrupts
                             0x4fa3   0x30  Code  ??  lpspi_shared_function.o [1]
LPSPI_DRV_FillupTxBuffer
                             0x4e6b   0xb4  Code  ??  lpspi_shared_function.o [1]
LPSPI_DRV_IRQHandler         0x4e43   0x28  Code  ??  lpspi_shared_function.o [1]
LPSPI_DRV_MasterAbortTransfer
                             0x496d   0x26  Code  ??  lpspi_master_driver.o [1]
LPSPI_DRV_MasterCompleteTransfer
                             0x49a5   0x82  Code  Lc  lpspi_master_driver.o [1]
LPSPI_DRV_MasterConfigureBus
                             0x4873   0xf8  Code  ??  lpspi_master_driver.o [1]
LPSPI_DRV_MasterIRQHandler
                             0x4a31   0xc8  Code  ??  lpspi_master_driver.o [1]
LPSPI_DRV_MasterInit         0x47f9   0x7a  Code  ??  lpspi_master_driver.o [1]
LPSPI_DRV_ReadRXBuffer       0x4f1f   0x84  Code  ??  lpspi_shared_function.o [1]
LPSPI_DRV_SlaveAbortTransfer
                             0x5155   0x76  Code  ??  lpspi_slave_driver.o [1]
LPSPI_DRV_SlaveIRQHandler
                             0x505f   0xec  Code  ??  lpspi_slave_driver.o [1]
LPSPI_Disable                0x4b23   0x1e  Code  ??  lpspi_hw_access.o [1]
LPSPI_Enable                 0x4785    0xa  Code  Lc  lpspi_master_driver.o [1]
LPSPI_GetFifoSizes           0x478f   0x10  Code  Lc  lpspi_master_driver.o [1]
LPSPI_GetStatusFlag          0x479f    0xc  Code  Lc  lpspi_master_driver.o [1]
LPSPI_GetStatusFlag          0x5015    0xc  Code  Lc  lpspi_slave_driver.o [1]
LPSPI_Init                   0x4b19    0xa  Code  ??  lpspi_hw_access.o [1]
LPSPI_IsMaster               0x4df5    0xa  Code  Lc  lpspi_shared_function.o [1]
LPSPI_ReadData               0x4e23    0x4  Code  Lc  lpspi_shared_function.o [1]
LPSPI_ReadRxCount            0x4e2f    0xa  Code  Lc  lpspi_shared_function.o [1]
LPSPI_ReadTxCount            0x4e27    0x8  Code  Lc  lpspi_shared_function.o [1]
LPSPI_SetBaudRate            0x4beb  0x13e  Code  ??  lpspi_hw_access.o [1]
LPSPI_SetBaudRateDivisor
                             0x4d29   0x10  Code  ??  lpspi_hw_access.o [1]
LPSPI_SetDelay               0x4b01   0x18  Code  Lc  lpspi_hw_access.o [1]
LPSPI_SetFlushFifoCmd        0x4b51   0x14  Code  ??  lpspi_hw_access.o [1]
LPSPI_SetIntMode             0x47ab   0x20  Code  Lc  lpspi_master_driver.o [1]
LPSPI_SetIntMode             0x4dff   0x20  Code  Lc  lpspi_shared_function.o [1]
LPSPI_SetIntMode             0x5021   0x20  Code  Lc  lpspi_slave_driver.o [1]
LPSPI_SetMasterSlaveMode
                             0x4b41   0x10  Code  ??  lpspi_hw_access.o [1]
LPSPI_SetPcsPolarityMode
                             0x4b8d   0x22  Code  ??  lpspi_hw_access.o [1]
LPSPI_SetPinConfigMode       0x4baf   0x2e  Code  ??  lpspi_hw_access.o [1]
LPSPI_SetRxDmaCmd            0x47d9   0x10  Code  Lc  lpspi_master_driver.o [1]
LPSPI_SetRxDmaCmd            0x504f   0x10  Code  Lc  lpspi_slave_driver.o [1]
LPSPI_SetSamplingPoint       0x47e9   0x10  Code  Lc  lpspi_master_driver.o [1]
LPSPI_SetTxCommandReg        0x4d39   0x4a  Code  ??  lpspi_hw_access.o [1]
LPSPI_SetTxDmaCmd            0x47cb    0xe  Code  Lc  lpspi_master_driver.o [1]
LPSPI_SetTxDmaCmd            0x5041    0xe  Code  Lc  lpspi_slave_driver.o [1]
LPSPI_WriteData              0x4e1f    0x4  Code  Lc  lpspi_shared_function.o [1]
LPTMR0_IRQHandler            0x68e5         Code  Wk  startup_S32K118.o [1]
LPTMR_DRV_GetCompareValueByCount
                             0x54fd   0x14  Code  ??  lptmr_driver.o [1]
LPTMR_DRV_Init               0x5417   0x1e  Code  ??  lptmr_driver.o [1]
LPTMR_DRV_SetConfig          0x5435   0xbc  Code  ??  lptmr_driver.o [1]
LPTMR_DRV_StartCounter       0x5519   0x10  Code  ??  lptmr_driver.o [1]
LPTMR_Enable                 0x5273    0xe  Code  Lc  lptmr_driver.o [1]
LPTMR_GetCompareValue        0x52c7    0x8  Code  Lc  lptmr_driver.o [1]
LPTMR_Init                   0x554f   0x12  Code  ??  lptmr_hw_access.o [1]
LPTMR_SetBypass              0x5295   0x20  Code  Lc  lptmr_driver.o [1]
LPTMR_SetClockSelect         0x52b5   0x12  Code  Lc  lptmr_driver.o [1]
LPTMR_SetCompareValue        0x52cf    0xe  Code  Lc  lptmr_driver.o [1]
LPTMR_SetDmaRequest          0x51d5   0x22  Code  Lc  lptmr_driver.o [1]
LPTMR_SetFreeRunning         0x523f   0x20  Code  Lc  lptmr_driver.o [1]
LPTMR_SetInterrupt           0x51f7   0x20  Code  Lc  lptmr_driver.o [1]
LPTMR_SetPinPolarity         0x522b   0x14  Code  Lc  lptmr_driver.o [1]
LPTMR_SetPinSelect           0x5217   0x14  Code  Lc  lptmr_driver.o [1]
LPTMR_SetPrescaler           0x5281   0x14  Code  Lc  lptmr_driver.o [1]
LPTMR_SetWorkMode            0x525f   0x14  Code  Lc  lptmr_driver.o [1]
LPUART0_RxTx_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
LPUART1_RxTx_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
NMI_Handler                  0x4df3         Code  Wk  startup_S32K118.o [1]
NVMMan_enInit                0x618d   0x9c  Code  ??  nvmman.o [1]
OSIF_SemaCreate              0x4dd9   0x14  Code  ??  osif_baremetal.o [1]
OSIF_SemaPost                0x4db5   0x24  Code  ??  osif_baremetal.o [1]
PCC_GetClockMode              0xa37   0x22  Code  Lc  clock_S32K1xx.o [1]
PCC_GetClockSourceSel         0xa59   0x18  Code  Lc  clock_S32K1xx.o [1]
PCC_GetDividerSel             0xa89   0x16  Code  Lc  clock_S32K1xx.o [1]
PCC_GetFracValueSel           0xa71   0x18  Code  Lc  clock_S32K1xx.o [1]
PCC_SetClockMode              0x9e1   0x56  Code  Lc  clock_S32K1xx.o [1]
PCC_SetPeripheralClockControl
                              0x99d   0x44  Code  Lc  clock_S32K1xx.o [1]
PDB0_IRQHandler              0x68e5         Code  Wk  startup_S32K118.o [1]
PDB_ConfigTimer              0x5a0b   0x6e  Code  ??  pdb_hw_access.o [1]
PDB_DRV_ConfigAdcPreTrigger
                             0x5e99   0x42  Code  ??  pdb_driver.o [1]
PDB_DRV_Enable               0x5e79   0x10  Code  ??  pdb_driver.o [1]
PDB_DRV_Init                 0x5e31   0x48  Code  ??  pdb_driver.o [1]
PDB_DRV_Init::s_pdbClkNames
                             0x68e9    0x1  Data  Lc  pdb_driver.o [1]
PDB_DRV_LoadValuesCmd        0x5e89   0x10  Code  ??  pdb_driver.o [1]
PDB_Disable                  0x5973    0xa  Code  Lc  pdb_hw_access.o [1]
PDB_Enable                   0x5e1d    0xa  Code  Lc  pdb_driver.o [1]
PDB_Enable                   0x5969    0xa  Code  Lc  pdb_hw_access.o [1]
PDB_Init                     0x599d   0x6e  Code  ??  pdb_hw_access.o [1]
PDB_SetAdcPreTriggerBackToBackEnable
                             0x5a81   0x32  Code  ??  pdb_hw_access.o [1]
PDB_SetAdcPreTriggerDelayValue
                             0x5987   0x16  Code  Lc  pdb_hw_access.o [1]
PDB_SetAdcPreTriggerEnable
                             0x5ae5   0x26  Code  ??  pdb_hw_access.o [1]
PDB_SetAdcPreTriggerOutputEnable
                             0x5ab3   0x32  Code  ??  pdb_hw_access.o [1]
PDB_SetLoadValuesCmd         0x5e27    0xa  Code  Lc  pdb_driver.o [1]
PDB_SetLoadValuesCmd         0x597d    0xa  Code  Lc  pdb_hw_access.o [1]
PINS_DRV_Init                0x65c1   0x1e  Code  ??  pins_driver.o [1]
PINS_GPIO_WritePin           0x5b0d   0x16  Code  Lc  pins_port_hw_access.o [1]
PINS_Init                    0x5b23   0xf2  Code  ??  pins_port_hw_access.o [1]
PMC_GetLpoMode                0xac5   0x18  Code  Lc  clock_S32K1xx.o [1]
PMC_SetLpoMode                0xaa9   0x1c  Code  Lc  clock_S32K1xx.o [1]
PMC_SetLpoTrimValue           0xadd   0x20  Code  Lc  clock_S32K1xx.o [1]
PORT_IRQHandler              0x68e5         Code  Wk  startup_S32K118.o [1]
PWMCtrl_enInit               0x6535   0x22  Code  ??  PWMCtrl_Man.o [1]
PendSV_Handler               0x6263         Code  Wk  startup_S32K118.o [1]
RCM_IRQHandler               0x68e5         Code  Wk  startup_S32K118.o [1]
RTC_IRQHandler               0x68e5         Code  Wk  startup_S32K118.o [1]
RTC_Seconds_IRQHandler       0x68e5         Code  Wk  startup_S32K118.o [1]
RW$$Base                0x2000'00c0          --   Gb  - Linker created -
RW$$Limit               0x2000'0628          --   Gb  - Linker created -
Reset_Handler                0x6729         Code  Wk  startup_S32K118.o [1]
SCG_CMU_LVD_LVWSCG_IRQHandler
                             0x68e5         Code  Wk  startup_S32K118.o [1]
SCG_ClearFircControl          0x885    0xc  Code  Lc  clock_S32K1xx.o [1]
SCG_ClearFircLock             0x86f   0x12  Code  Lc  clock_S32K1xx.o [1]
SCG_ClearSircControl          0x7d7    0xa  Code  Lc  clock_S32K1xx.o [1]
SCG_ClearSircLock             0x7c5   0x12  Code  Lc  clock_S32K1xx.o [1]
SCG_ClearSoscControl          0x921    0xc  Code  Lc  clock_S32K1xx.o [1]
SCG_ClearSoscLock             0x8fd   0x12  Code  Lc  clock_S32K1xx.o [1]
SCG_GetClockoutSourceSel
                              0x667    0xa  Code  Lc  clock_S32K1xx.o [1]
SCG_GetCurrentBusClockDividerRatio
                              0x685    0xa  Code  Lc  clock_S32K1xx.o [1]
SCG_GetCurrentCoreClockDividerRatio
                              0x67b    0xa  Code  Lc  clock_S32K1xx.o [1]
SCG_GetCurrentSlowClockDividerRatio
                              0x68f    0x8  Code  Lc  clock_S32K1xx.o [1]
SCG_GetCurrentSystemClockSource
                              0x671    0xa  Code  Lc  clock_S32K1xx.o [1]
SCG_GetFircFirstAsyncDivider
                              0x6eb    0xc  Code  Lc  clock_S32K1xx.o [1]
SCG_GetFircRange              0x863    0xc  Code  Lc  clock_S32K1xx.o [1]
SCG_GetFircSecondAsyncDivider
                              0x6f7    0xe  Code  Lc  clock_S32K1xx.o [1]
SCG_GetFircStatus             0x853   0x10  Code  Lc  clock_S32K1xx.o [1]
SCG_GetFircSystemClockMode
                              0x83b   0x18  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSircFirstAsyncDivider
                              0x71b    0xc  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSircRange              0x7b7    0xe  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSircSecondAsyncDivider
                              0x727    0xe  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSircStatus             0x7a7   0x10  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSircSystemClockMode
                              0x78f   0x18  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSoscFirstAsyncDivider
                              0x74b    0xc  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSoscSecondAsyncDivider
                              0x757    0xe  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSoscStatus             0x8ed   0x10  Code  Lc  clock_S32K1xx.o [1]
SCG_GetSoscSystemClockMode
                              0x8d5   0x18  Code  Lc  clock_S32K1xx.o [1]
SCG_SetClockoutSourceSel
                              0x77b   0x14  Code  Lc  clock_S32K1xx.o [1]
SCG_SetFircAsyncConfig        0x705   0x16  Code  Lc  clock_S32K1xx.o [1]
SCG_SetFircConfiguration
                              0x891    0xe  Code  Lc  clock_S32K1xx.o [1]
SCG_SetFircControl            0x89f   0x36  Code  Lc  clock_S32K1xx.o [1]
SCG_SetRunClockControl        0x697   0x2a  Code  Lc  clock_S32K1xx.o [1]
SCG_SetSircAsyncConfig        0x735   0x16  Code  Lc  clock_S32K1xx.o [1]
SCG_SetSircConfiguration
                              0x7e1   0x10  Code  Lc  clock_S32K1xx.o [1]
SCG_SetSircControl            0x7f1   0x4a  Code  Lc  clock_S32K1xx.o [1]
SCG_SetSoscAsyncConfig        0x765   0x16  Code  Lc  clock_S32K1xx.o [1]
SCG_SetSoscConfiguration
                              0x92d   0x22  Code  Lc  clock_S32K1xx.o [1]
SCG_SetSoscControl            0x94f   0x4e  Code  Lc  clock_S32K1xx.o [1]
SCG_SetVlprClockControl
                              0x6c1   0x2a  Code  Lc  clock_S32K1xx.o [1]
SCUInit_enInitHW             0x63f5   0x64  Code  ??  SCUInit.o [1]
SCUMain_vTrigger             0x6771    0x2  Code  ??  SCUMain.o [1]
SIM_ClearTraceClockConfig
                              0x635    0x8  Code  Lc  clock_S32K1xx.o [1]
SIM_GetClockoutDividerValue
                              0x59d    0xa  Code  Lc  clock_S32K1xx.o [1]
SIM_GetClockoutSelectorValue
                              0x5a7    0xa  Code  Lc  clock_S32K1xx.o [1]
SIM_GetClockoutStatus         0x591    0xc  Code  Lc  clock_S32K1xx.o [1]
SIM_GetDmaClockGate           0x4e3   0x14  Code  Lc  clock_S32K1xx.o [1]
SIM_GetEimClockGate           0x47b   0x14  Code  Lc  clock_S32K1xx.o [1]
SIM_GetErmClockGate           0x4af   0x14  Code  Lc  clock_S32K1xx.o [1]
SIM_GetFtm0ExternalClkPinMode
                              0x55d    0xa  Code  Lc  clock_S32K1xx.o [1]
SIM_GetFtm1ExternalClkPinMode
                              0x567    0xa  Code  Lc  clock_S32K1xx.o [1]
SIM_GetLpo1KStatus            0x57d    0xa  Code  Lc  clock_S32K1xx.o [1]
SIM_GetLpo32KStatus           0x571    0xc  Code  Lc  clock_S32K1xx.o [1]
SIM_GetLpoClkSelectorValue
                              0x587    0xa  Code  Lc  clock_S32K1xx.o [1]
SIM_GetMpuClockGate           0x517   0x14  Code  Lc  clock_S32K1xx.o [1]
SIM_GetMscmClockGate          0x54b   0x12  Code  Lc  clock_S32K1xx.o [1]
SIM_GetRtcClkSrc              0x411    0xa  Code  Lc  clock_S32K1xx.o [1]
SIM_SetClockout               0x5e5   0x3c  Code  Lc  clock_S32K1xx.o [1]
SIM_SetDmaClockGate           0x4c3   0x20  Code  Lc  clock_S32K1xx.o [1]
SIM_SetEimClockGate           0x45b   0x20  Code  Lc  clock_S32K1xx.o [1]
SIM_SetErmClockGate           0x48f   0x20  Code  Lc  clock_S32K1xx.o [1]
SIM_SetExtPinSourceFtm        0x5b1   0x34  Code  Lc  clock_S32K1xx.o [1]
SIM_SetLpoClocks              0x41b   0x40  Code  Lc  clock_S32K1xx.o [1]
SIM_SetMpuClockGate           0x4f7   0x20  Code  Lc  clock_S32K1xx.o [1]
SIM_SetMscmClockGate          0x52b   0x20  Code  Lc  clock_S32K1xx.o [1]
SIM_SetTraceClockConfig
                              0x63d   0x2a  Code  Lc  clock_S32K1xx.o [1]
SIM_SetTraceClockSource
                              0x621   0x14  Code  Lc  clock_S32K1xx.o [1]
SMC_GetCurrentRunningMode
                              0xafd    0x6  Code  Lc  clock_S32K1xx.o [1]
ST_bNVMManReady         0x2000'3231    0x1  Data  Lc  nvmman.o [1]
ST_bRisingEdgeStatus    0x2000'3230    0x1  Data  Lc  ICMon.o [1]
ST_enFtm0StateStruct    0x2000'3144   0x64  Data  Lc  ICMon.o [1]
ST_enFtm1StateStruct    0x2000'31c8   0x64  Data  Lc  PWMCtrl_Man.o [1]
ST_stFlashSSDConfig     0x2000'31ac   0x1c  Data  Lc  nvmman.o [1]
ST_u32TP1Freq           0x2000'3140    0x4  Data  Lc  ICMon.o [1]
SVC_Handler                  0x5b0b         Code  Wk  startup_S32K118.o [1]
SysTick_Handler              0x4d97    0x8  Code  ??  osif_baremetal.o [1]
SystemInit                   0x65a1    0x2  Code  ??  system_S32K118.o [1]
SystemSoftwareReset          0x65a3   0x12  Code  ??  system_S32K118.o [1]
TimerHwAbs_enInitLPIT        0x6399   0x34  Code  ??  TimerHwAbs.o [1]
TimerHwAbs_u32Counter1ms
                        0x2000'322c    0x4  Data  ??  TimerHwAbs.o [1]
TimerHwAbs_vInitLPTMR        0x63cd   0x14  Code  ??  TimerHwAbs.o [1]
TimerHwAbs_vLPITCallback
                             0x6381   0x18  Code  ??  TimerHwAbs.o [1]
U32_CalculateLPTimerFrequency
                             0x62dd   0x18  Code  Lc  ICMon.o [1]
WDOGMan_bStatus              0x6459   0x18  Code  ??  WDOGMan.o [1]
WDOG_IRQHandler              0x68e5         Code  Wk  startup_S32K118.o [1]
ZI$$Base                0x2000'30c0          --   Gb  - Linker created -
ZI$$Limit               0x2000'3232          --   Gb  - Linker created -
__CODE_RAM$$Base        0x2000'0628          --   ??  - Linker created -
__CODE_RAM$$Limit       0x2000'0664          --   Gb  - Linker created -
__CODE_ROM$$Base             0x6774          --   ??  - Linker created -
__CODE_ROM$$Limit            0x67b0          --   ??  - Linker created -
__RAM_END {Abs}         0x2000'57ff         Data  ??  <internal module>
__RAM_START {Abs}       0x2000'0000         Data  ??  <internal module>
__RAM_VECTOR_TABLE_SIZE {Abs}
                               0xc0         Data  ??  <internal module>
__VECTOR_RAM {Abs}      0x2000'0000         Data  ??  <internal module>
__VECTOR_TABLE {Abs}            0x0         Data  ??  <internal module>
__Vectors                       0x0          --   ??  startup_S32K118.o [1]
__Vectors_End                  0xc0         Data  ??  startup_S32K118.o [1]
__Vectors_Size {Abs}           0xc0          --   ??  startup_S32K118.o [1]
__aeabi_idivmod              0x1b59         Code  ??  I32DivMod.o [28]
__aeabi_ldiv0                0x4289         Code  ??  I64DivZer.o [28]
__aeabi_llsl                 0x552d         Code  ??  I64Shl.o [28]
__aeabi_lmul                 0x4049         Code  ??  I64Mul.o [28]
__aeabi_memclr4              0x4ded         Code  ??  ABImemclr4.o [28]
__aeabi_memset               0x471d         Code  ??  ABImemset.o [28]
__aeabi_uidiv                0x1b65         Code  ??  I32DivMod.o [28]
__aeabi_uldivmod             0x4059         Code  ??  I64DivMod.o [28]
__iar_Memset                 0x471d         Code  ??  ABImemset.o [28]
__iar_Memset4_word           0x4fe1         Code  ??  ABImemset48.o [28]
__iar_Memset8_word           0x4fe1         Code  ??  ABImemset48.o [28]
__iar_Memset_word            0x4725         Code  ??  ABImemset.o [28]
__vector_table                  0x0         Data  ??  startup_S32K118.o [1]
__vector_table_0x1c            0x1c         Data  ??  startup_S32K118.o [1]
abs_dif                      0x4bdd    0xe  Code  Lc  lpspi_hw_access.o [1]
adc_config_1_ChnConfig0
                        0x2000'30c0    0x4  Data  ??  peripherals_adc_config_1.o [1]
adc_config_1_ChnConfig1
                        0x2000'00c0    0x4  Data  ??  peripherals_adc_config_1.o [1]
adc_config_1_ChnConfig2
                        0x2000'00c4    0x4  Data  ??  peripherals_adc_config_1.o [1]
adc_config_1_ChnConfig3
                        0x2000'00c8    0x4  Data  ??  peripherals_adc_config_1.o [1]
adc_config_1_ChnConfig4
                        0x2000'00cc    0x4  Data  ??  peripherals_adc_config_1.o [1]
adc_config_1_ChnConfig5
                        0x2000'00d0    0x4  Data  ??  peripherals_adc_config_1.o [1]
adc_config_1_ChnConfig6
                        0x2000'00d4    0x4  Data  ??  peripherals_adc_config_1.o [1]
adc_config_1_ConvConfig0
                             0x6848    0xc  Data  ??  peripherals_adc_config_1.o [1]
addr                    0x2000'313c    0x4  Data  ??  ERMHwAbs.o [1]
clockMan1_InitConfig0   0x2000'019c   0x80  Data  ??  clock_config.o [1]
clockNameMappings            0x62f4   0x8c  Data  ??  clock_S32K1xx.o [1]
crc_1_Cfg0                   0x6818   0x10  Data  ??  peripherals_crc_1.o [1]
enConvertErrorCode           0x6239   0x2a  Code  ??  conversion.o [1]
erm_InitConfig0              0x686c    0x8  Data  ??  peripherals_erm_config_1.o [1]
erm_Interrupt0               0x1b56    0x2  Data  ??  peripherals_erm_config_1.o [1]
flexTimer_ic_1_InitConfig
                        0x2000'021c   0x14  Data  ??  peripherals_flexTimer_ic_1.o [1]
flexTimer_ic_1_InputCaptureChannelConfig
                        0x2000'0238   0x20  Data  ??  peripherals_flexTimer_ic_1.o [1]
flexTimer_ic_1_InputCaptureConfig
                        0x2000'0230    0x8  Data  ??  peripherals_flexTimer_ic_1.o [1]
flexTimer_pwm_1_FaultConfig
                        0x2000'30e0   0x10  Data  ??  peripherals_flexTimer_pwm_1.o [1]
flexTimer_pwm_1_IndependentChannelsConfig
                        0x2000'026c   0x20  Data  ??  peripherals_flexTimer_pwm_1.o [1]
flexTimer_pwm_1_InitConfig
                        0x2000'0258   0x14  Data  ??  peripherals_flexTimer_pwm_1.o [1]
flexTimer_pwm_1_PwmConfig
                        0x2000'028c   0x18  Data  ??  peripherals_flexTimer_pwm_1.o [1]
ftmStatePtr             0x2000'30d8    0x8  Data  ??  ftm_common.o [1]
g_RtcClkInFreq          0x2000'30d0    0x4  Data  ??  clock_S32K1xx.o [1]
g_TClkFreq              0x2000'30c4    0xc  Data  ??  clock_S32K1xx.o [1]
g_ftmBase                    0x6874    0x8  Data  ??  ftm_common.o [1]
g_ftmExtClockSel             0x428a    0x2  Data  Lc  ftm_common.o [1]
g_ftmIrqId                   0x6828   0x10  Data  ??  ftm_common.o [1]
g_ftmOverflowIrqId           0x3b7c    0x2  Data  ??  ftm_common.o [1]
g_interruptDisableCount
                        0x2000'30f0    0x4  Data  Lc  interrupt_manager.o [1]
g_lpspiBase             0x2000'02bc    0x8  Data  ??  lpspi_shared_function.o [1]
g_lpspiIrqId            0x2000'0624    0x2  Data  ??  lpspi_shared_function.o [1]
g_lpspiStatePtr         0x2000'30fc    0x8  Data  ??  lpspi_shared_function.o [1]
g_lptmrBase                  0x68d8    0x4  Data  Lc  lptmr_driver.o [1]
g_pin_mux_InitConfigArr0
                        0x2000'02c4  0x360  Data  ??  pin_mux.o [1]
g_xtal0ClkFreq          0x2000'30d4    0x4  Data  ??  clock_S32K1xx.o [1]
init_data_bss                0x5fd5   0xd8  Code  ??  startup.o [1]
is_EEEMemoryReady            0x6181    0xc  Code  Lc  nvmman.o [1]
lpit1_ChnConfig0        0x2000'02a4   0x18  Data  ??  peripherals_lpit_config_1.o [1]
lpit1_InitConfig             0x4782    0x2  Data  ??  peripherals_lpit_config_1.o [1]
lpspi0_master_complete       0x6585    0x2  Code  ??  ExtDev.o [1]
lpspi_0_MasterConfig0        0x67d0   0x20  Data  ??  peripherals_lpspi_1.o [1]
lpspi_1State            0x2000'3108   0x34  Data  ??  peripherals_lpspi_1.o [1]
lptmr_1_config0              0x6838   0x10  Data  ??  peripherals_lptmr_1.o [1]
lptmr_ChooseClkConfig        0x53ad   0x6a  Code  Lc  lptmr_driver.o [1]
lptmr_GetClkFreq             0x535d   0x4a  Code  Lc  lptmr_driver.o [1]
lptmr_GetClkFreq::lptmrPccClockName
                             0x6773    0x1  Data  Lc  lptmr_driver.o [1]
lptmr_compute_nticks         0x5301   0x28  Code  Lc  lptmr_driver.o [1]
lptmr_us2nn                  0x52dd   0x24  Code  Lc  lptmr_driver.o [1]
main                         0x6765    0xc  Code  ??  main.o [1]
nticks2compare_ticks         0x5329   0x34  Code  Lc  lptmr_driver.o [1]
osif_DisableIrqGlobal        0x4da5    0x8  Code  Lc  osif_baremetal.o [1]
osif_EnableIrqGlobal         0x4dad    0x8  Code  Lc  osif_baremetal.o [1]
osif_Tick                    0x4d8d    0xa  Code  Lc  osif_baremetal.o [1]
pdb_config_1_adcTrigConfig0
                             0x687c    0x8  Data  ??  peripherals_pdb_config_1.o [1]
pdb_config_1_adcTrigConfig1
                             0x6884    0x8  Data  ??  peripherals_pdb_config_1.o [1]
pdb_config_1_adcTrigConfig2
                             0x688c    0x8  Data  ??  peripherals_pdb_config_1.o [1]
pdb_config_1_adcTrigConfig3
                             0x6894    0x8  Data  ??  peripherals_pdb_config_1.o [1]
pdb_config_1_adcTrigConfig4
                             0x689c    0x8  Data  ??  peripherals_pdb_config_1.o [1]
pdb_config_1_adcTrigConfig5
                             0x68a4    0x8  Data  ??  peripherals_pdb_config_1.o [1]
pdb_config_1_adcTrigConfig6
                             0x68ac    0x8  Data  ??  peripherals_pdb_config_1.o [1]
pdb_config_1_timerConfig0
                             0x68b4    0x8  Data  ??  peripherals_pdb_config_1.o [1]
peripheralClockConfig0  0x2000'014c   0x50  Data  ??  clock_config.o [1]
peripheralFeaturesList       0x66e0   0x48  Data  ??  clock_S32K1xx.o [1]
s_adcBase                    0x68bc    0x4  Data  Lc  adc_driver.o [1]
s_baudratePrescaler          0x67b0   0x20  Data  Lc  lpspi_hw_access.o [1]
s_crcBase                    0x68c4    0x4  Data  Lc  crc_driver.o [1]
s_edmaBase                   0x68d4    0x4  Data  Lc  edma_driver.o [1]
s_ermBase                    0x68c8    0x4  Data  Lc  erm_driver.o [1]
s_lpitBase                   0x68d0    0x4  Data  Lc  lpit_driver.o [1]
s_lpitClkNames               0x6772    0x1  Data  Lc  lpit_driver.o [1]
s_lpitSourceClockFrequency
                        0x2000'30f4    0x4  Data  Lc  lpit_driver.o [1]
s_osif_tick_cnt         0x2000'3104    0x4  Data  Lc  osif_baremetal.o [1]
s_pdbBase                    0x68dc    0x4  Data  Lc  pdb_driver.o [1]
s_pdbIrqId                   0x68e8    0x1  Data  Lc  pdb_driver.o [1]
s_vectors                    0x68e0    0x4  Data  Lc  startup.o [1]
s_virtEdmaState         0x2000'30f8    0x4  Data  Lc  edma_driver.o [1]


[1] = A:\INEOSDevStrm\SCUMain_ert_rtw
[2] = libADCMon_Mdl_rtwlib.a
[3] = libAPDet_Mdl_rtwlib.a
[4] = libAmbTempMon_Mdl_rtwlib.a
[5] = libBlockDet_Mdl_rtwlib.a
[6] = libCfgParam_Mdl_rtwlib.a
[7] = libComLog_Mdl_rtwlib.a
[8] = libCtrlLog_Mdl_rtwlib.a
[9] = libDIOCtrl_Mdl_rtwlib.a
[10] = libLearnAdap_Mdl_rtwlib.a
[11] = libMOSFETTempMon_Mdl_rtwlib.a
[12] = libMtrCtrl_Mdl_rtwlib.a
[13] = libMtrMdl_Mdl_rtwlib.a
[14] = libPWMCtrl_Mdl_rtwlib.a
[15] = libPnlOp_Mdl_rtwlib.a
[16] = libPosMon_Mdl_rtwlib.a
[17] = libRefField_Mdl_rtwlib.a
[18] = libRefForce_Mdl_rtwlib.a
[19] = libStateMach_Mdl_rtwlib.a
[20] = libSwitchLog_Mdl_rtwlib.a
[21] = libTaskSch_Mdl_rtwlib.a
[22] = libThermProt_Mdl_rtwlib.a
[23] = libTheshForce_Mdl_rtwlib.a
[24] = libUsgHis_Mdl_rtwlib.a
[25] = libVehCom_Mdl_rtwlib.a
[26] = libVoltMon_Mdl_rtwlib.a
[27] = m7M_tl.a
[28] = rt7M_tl.a

  23'900 bytes of readonly  code memory
      60 bytes of readwrite code memory
   2'126 bytes of readonly  data memory
   2'520 bytes of readwrite data memory

Errors: none
Warnings: none
