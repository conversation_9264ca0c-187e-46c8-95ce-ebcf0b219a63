/* generated by ASAP2 Studio 2.3.10.8851 */

ASAP2_VERSION 1 70
/begin PROJECT XCP_SCU ""

  /begin MODULE CPP ""

    /begin A2ML

        struct Protocol_Layer {
          uint;  /* XCP protocol layer version, current 0x100 */
          uint;  /* T1 [ms] */
          uint;  /* T2 [ms] */
          uint;  /* T3 [ms] */
          uint;  /* T4 [ms] */
          uint;  /* T5 [ms] */
          uint;  /* T6 [ms] */
          uint;  /* T7 [ms] */
          uchar;  /* MAX_CTO */
          uint;  /* MAX_DTO */
          enum {
            "BYTE_ORDER_MSB_LAST" = 0,
            "BYTE_ORDER_MSB_FIRST" = 1
          };
          enum {
            "ADDRESS_GRANULARITY_BYTE" = 1,
            "ADDRESS_GRANULARITY_WORD" = 2,
            "ADDRESS_GRANULARITY_DWORD" = 4
          };
          taggedstruct {
            ("OPTIONAL_CMD" enum {
              "GET_COMM_MODE_INFO" = 251,
              "GET_ID" = 250,
              "SET_REQUEST" = 249,
              "GET_SEED" = 248,
              "UNLOCK" = 247,
              "SET_MTA" = 246,
              "UPLOAD" = 245,
              "SHORT_UPLOAD" = 244,
              "BUILD_CHECKSUM" = 243,
              "TRANSPORT_LAYER_CMD" = 242,
              "USER_CMD" = 241,
              "DOWNLOAD" = 240,
              "DOWNLOAD_NEXT" = 239,
              "DOWNLOAD_MAX" = 238,
              "SHORT_DOWNLOAD" = 237,
              "MODIFY_BITS" = 236,
              "SET_CAL_PAGE" = 235,
              "GET_CAL_PAGE" = 234,
              "GET_PAG_PROCESSOR_INFO" = 233,
              "GET_SEGMENT_INFO" = 232,
              "GET_PAGE_INFO" = 231,
              "SET_SEGMENT_MODE" = 230,
              "GET_SEGMENT_MODE" = 229,
              "COPY_CAL_PAGE" = 228,
              "CLEAR_DAQ_LIST" = 227,
              "SET_DAQ_PTR" = 226,
              "WRITE_DAQ" = 225,
              "SET_DAQ_LIST_MODE" = 224,
              "GET_DAQ_LIST_MODE" = 223,
              "START_STOP_DAQ_LIST" = 222,
              "START_STOP_SYNCH" = 221,
              "GET_DAQ_CLOCK" = 220,
              "READ_DAQ" = 219,
              "GET_DAQ_PROCESSOR_INFO" = 218,
              "GET_DAQ_RESOLUTION_INFO" = 217,
              "GET_DAQ_LIST_INFO" = 216,
              "GET_DAQ_EVENT_INFO" = 215,
              "FREE_DAQ" = 214,
              "ALLOC_DAQ" = 213,
              "ALLOC_ODT" = 212,
              "ALLOC_ODT_ENTRY" = 211,
              "PROGRAM_START" = 210,
              "PROGRAM_CLEAR" = 209,
              "PROGRAM" = 208,
              "PROGRAM_RESET" = 207,
              "GET_PGM_PROCESSOR_INFO" = 206,
              "GET_SECTOR_INFO" = 205,
              "PROGRAM_PREPARE" = 204,
              "PROGRAM_FORMAT" = 203,
              "PROGRAM_NEXT" = 202,
              "PROGRAM_MAX" = 201,
              "PROGRAM_VERIFY" = 200
            })*;
            "COMMUNICATION_MODE_SUPPORTED" taggedunion {
              "BLOCK" taggedstruct {
                "SLAVE" ;
                "MASTER" struct {
                  uchar;  /* MAX_BS */
                  uchar;  /* MIN_ST */
                };
              };
              "INTERLEAVED" uchar;  /* QUEUE_SIZE */
            };
            "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];  /* Name of the Seed&Key function */
          };
        };

        struct Daq {
          enum {
            "STATIC" = 0,
            "DYNAMIC" = 1
          };
          uint;  /* MAX_DAQ */
          uint;  /* MAX_EVENT_CHANNEL */
          uchar;  /* MIN_DAQ */
          enum {
            "OPTIMISATION_TYPE_DEFAULT" = 0,
            "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
            "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
            "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
            "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
            "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
          };
          enum {
            "ADDRESS_EXTENSION_FREE" = 0,
            "ADDRESS_EXTENSION_ODT" = 1,
            "ADDRESS_EXTENSION_DAQ" = 3
          };
          enum {
            "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
            "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
            "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
            "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
          };
          enum {
            "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
            "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
            "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
            "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
          };
          uchar;  /* MAX_ODT_ENTRY_SIZE_DAQ */
          enum {
            "NO_OVERLOAD_INDICATION" = 0,
            "OVERLOAD_INDICATION_PID" = 1,
            "OVERLOAD_INDICATION_EVENT" = 2
          };
          taggedstruct {
            "PRESCALER_SUPPORTED" ;
            "RESUME_SUPPORTED" ;
            block "STIM" struct {
              enum {
                "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
              };
              uchar;  /* MAX_ODT_ENTRY_SIZE_STIM */
              taggedstruct {
                "BIT_STIM_SUPPORTED" ;
              };
            };
            block "TIMESTAMP_SUPPORTED" struct {
              uint;  /* TIMESTAMP_TICKS */
              enum {
                "NO_TIME_STAMP" = 0,
                "SIZE_BYTE" = 1,
                "SIZE_WORD" = 2,
                "SIZE_DWORD" = 4
              };
              enum {
                "UNIT_1NS" = 0,
                "UNIT_10NS" = 1,
                "UNIT_100NS" = 2,
                "UNIT_1US" = 3,
                "UNIT_10US" = 4,
                "UNIT_100US" = 5,
                "UNIT_1MS" = 6,
                "UNIT_10MS" = 7,
                "UNIT_100MS" = 8,
                "UNIT_1S" = 9
              };
              taggedstruct {
                "TIMESTAMP_FIXED" ;
              };
            };
            "PID_OFF_SUPPORTED" ;
            (block "DAQ_LIST" struct {
              uint;  /* DAQ_LIST_NUMBER */
              taggedstruct {
                "DAQ_LIST_TYPE" enum {
                  "DAQ" = 1,
                  "STIM" = 2,
                  "DAQ_STIM" = 3
                };
                "MAX_ODT" uchar;
                "MAX_ODT_ENTRIES" uchar;
                "FIRST_PID" uchar;
                "EVENT_FIXED" uint;
                block "PREDEFINED" taggedstruct {
                  (block "ODT" struct {
                    uchar;  /* ODT number */
                    taggedstruct {
                      ("ODT_ENTRY" struct {
                        uchar;  /* ODT_ENTRY number */
                        ulong;  /* address of element */
                        uchar;  /* address extension of element */
                        uchar;  /* size of element [AG] */
                        uchar;  /* BIT_OFFSET */
                      })*;
                    };
                  })*;
                };
              };
            })*;
            (block "EVENT" struct {
              char[101];  /* EVENT_CHANNEL_NAME       */
              char[9];  /* EVENT_CHANNEL_SHORT_NAME */
              uint;  /* EVENT_CHANNEL_NUMBER     */
              enum {
                "DAQ" = 1,
                "STIM" = 2,
                "DAQ_STIM" = 3
              };
              uchar;  /* MAX_DAQ_LIST */
              uchar;  /* TIME_CYCLE   */
              uchar;  /* TIME_UNIT    */
              uchar;  /* PRIORITY     */
            })*;
          };
        };

        taggedunion Daq_Event {
          "FIXED_EVENT_LIST" taggedstruct {
            ("EVENT" uint)*;
          };
          "VARIABLE" taggedstruct {
            block "AVAILABLE_EVENT_LIST" taggedstruct {
              ("EVENT" uint)*;
            };
            block "DEFAULT_EVENT_LIST" taggedstruct {
              ("EVENT" uint)*;
            };
          };
        };

        struct Pag {
          uchar;  /* MAX_SEGMENTS */
          taggedstruct {
            "FREEZE_SUPPORTED" ;
          };
        };

        struct Pgm {
          enum {
            "PGM_MODE_ABSOLUTE" = 1,
            "PGM_MODE_FUNCTIONAL" = 2,
            "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
          };
          uchar;  /* MAX_SECTORS */
          uchar;  /* MAX_CTO_PGM */
          taggedstruct {
            (block "SECTOR" struct {
              char[101];  /* SECTOR_NAME */
              uchar;  /* SECTOR_NUMBER */
              ulong;  /* Address */
              ulong;  /* Length  */
              uchar;  /* CLEAR_SEQUENCE_NUMBER */
              uchar;  /* PROGRAM_SEQUENCE_NUMBER */
              uchar;  /* PROGRAM_METHOD */
            })*;
            "COMMUNICATION_MODE_SUPPORTED" taggedunion {
              "BLOCK" taggedstruct {
                "SLAVE" ;
                "MASTER" struct {
                  uchar;  /* MAX_BS_PGM */
                  uchar;  /* MIN_ST_PGM */
                };
              };
              "INTERLEAVED" uchar;  /* QUEUE_SIZE_PGM */
            };
          };
        };

        struct Segment {
          uchar;  /* SEGMENT_NUMBER */
          uchar;  /* number of pages */
          uchar;  /* ADDRESS_EXTENSION */
          uchar;  /* COMPRESSION_METHOD */
          uchar;  /* ENCRYPTION_METHOD */
          taggedstruct {
            block "CHECKSUM" struct {
              enum {
                "XCP_ADD_11" = 1,
                "XCP_ADD_12" = 2,
                "XCP_ADD_14" = 3,
                "XCP_ADD_22" = 4,
                "XCP_ADD_24" = 5,
                "XCP_ADD_44" = 6,
                "XCP_CRC_16" = 7,
                "XCP_CRC_16_CITT" = 8,
                "XCP_CRC_32" = 9,
                "XCP_USER_DEFINED" = 255
              };
              taggedstruct {
                "MAX_BLOCK_SIZE" ulong;
                "EXTERNAL_FUNCTION" char[256];  /* Name of the Checksum.DLL */
              };
            };
            (block "PAGE" struct {
              uchar;  /* PAGE_NUMBER */
              enum {
                "ECU_ACCESS_NOT_ALLOWED" = 0,
                "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                "ECU_ACCESS_DONT_CARE" = 3
              };
              enum {
                "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                "XCP_READ_ACCESS_DONT_CARE" = 3
              };
              enum {
                "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                "XCP_WRITE_ACCESS_DONT_CARE" = 3
              };
              taggedstruct {
                "INIT_SEGMENT" uchar;  /* references segment that initialises this page */
              };
            })*;
            (block "ADDRESS_MAPPING" struct {
              ulong;  /* source address */
              ulong;  /* destination address */
              ulong;  /* length */
            })*;
            "PGM_VERIFY" ulong;  /* verification value for PGM */
          };
        };

        taggedstruct Common_Parameters {
          block "PROTOCOL_LAYER" struct Protocol_Layer;
          block "SEGMENT" struct Segment;
          block "DAQ" struct Daq;
          block "PAG" struct Pag;
          block "PGM" struct Pgm;
          block "DAQ_EVENT" taggedunion Daq_Event;
        };

        struct CAN_Parameters {
          uint;  /* XCP on CAN version, currentl 0x0100 */
          taggedstruct {
            "CAN_ID_BROADCAST" ulong;  /* Auto-detection CAN-ID */
            "CAN_ID_MASTER" ulong;  /* CMD/STIM CAN-ID */
            "CAN_ID_SLAVE" ulong;  /* RES/ERR/EV/SERV/DAQ CAN-ID */
            "BAUDRATE" ulong;  /* Baudrate in Hz */
            "SAMPLE_POINT" uchar;  /* Sample point in % of bit time */
            "SAMPLE_RATE" enum {
              "SINGLE" = 1,
              "TRIPLE" = 3
            };
            "BTL_CYCLES" uchar;  /* slots per bit time */
            "SJW" uchar;
            "SYNC_EDGE" enum {
              "SINGLE" = 1,
              "DUAL" = 2
            };
            "MAX_DLC_REQUIRED" ;  /* master to slave frames */
            (block "DAQ_LIST_CAN_ID" struct {
              uint;  /* reference to DAQ_LIST_NUMBER */
              taggedstruct {
                "VARIABLE" ;
                "FIXED" ulong;  /* this DAQ_LIST always on this CAN_ID */
              };
            })*;
          };
        };

        struct SxI_Parameters {
          uint;  /* XCP on SxI version, currently 0x0100 */
          ulong;  /* BAUDRATE [Hz] */
          taggedstruct {
            "ASYNCH_FULL_DUPLEX_MODE" struct {
              enum {
                "PARITY_NONE" = 0,
                "PARITY_ODD" = 1,
                "PARITY_EVEN" = 2
              };
              enum {
                "ONE_STOP_BIT" = 1,
                "TWO_STOP_BITS" = 2
              };
            };
            "SYNCH_FULL_DUPLEX_MODE_BYTE" ;
            "SYNCH_FULL_DUPLEX_MODE_WORD" ;
            "SYNCH_FULL_DUPLEX_MODE_DWORD" ;
            "SYNCH_MASTER_SLAVE_MODE_BYTE" ;
            "SYNCH_MASTER_SLAVE_MODE_WORD" ;
            "SYNCH_MASTER_SLAVE_MODE_DWORD" ;
          };
          enum {
            "HEADER_LEN_BYTE" = 0,
            "HEADER_LEN_CTR_BYTE" = 1,
            "HEADER_LEN_FILL_BYTE" = 2,
            "HEADER_LEN_WORD" = 3,
            "HEADER_LEN_CTR_WORD" = 4,
            "HEADER_LEN_FILL_WORD" = 5
          };
          enum {
            "NO_CHECKSUM" = 0,
            "CHECKSUM_BYTE" = 1,
            "CHECKSUM_WORD" = 2
          };
        };

        struct TCP_IP_Parameters {
          uint;  /* XCP on TCP_IP version, currently 0x0100 */
          uint;  /* PORT */
          taggedunion {
            "HOST_NAME" char[256];
            "ADDRESS" char[15];
          };
        };

        struct UDP_Parameters {
          uint;  /* XCP on UDP version, currently 0x0100 */
          uint;  /* PORT */
          taggedunion {
            "HOST_NAME" char[256];
            "ADDRESS" char[15];
          };
        };

        struct ep_parameters {
          uchar;  /* ENDPOINT_NUMBER, not endpoint address */
          enum {
            "BULK_TRANSFER" = 2,
            "INTERRUPT_TRANSFER" = 3
          };
          uint;  /* wMaxPacketSize: Maximum packet  
 size of endpoint in bytes       */
          uchar;  /* bInterval: polling of endpoint  */
          enum {
            "MESSAGE_PACKING_SINGLE" = 0,
            "MESSAGE_PACKING_MULTIPLE" = 1,
            "MESSAGE_PACKING_STREAMING" = 2
          };
          enum {
            "ALIGNMENT_8_BIT" = 0,
            "ALIGNMENT_16_BIT" = 1,
            "ALIGNMENT_32_BIT" = 2,
            "ALIGNMENT_64_BIT" = 3
          };
          taggedstruct {
            "RECOMMENDED_HOST_BUFSIZE" uint;  /* Recommended size for the host 
 buffer size. The size is defined
 as multiple of wMaxPacketSize.  */
          };
        };  /* end of ep_parameters */

        struct USB_Parameters {
          uint;  /* XCP on USB version  
 e.g. "1.0" = 0x0100 */
          uint;  /* Vendor ID                       */
          uint;  /* Product ID                      */
          uchar;  /* Number of interface             */
          enum {
            "HEADER_LEN_BYTE" = 0,
            "HEADER_LEN_CTR_BYTE" = 1,
            "HEADER_LEN_FILL_BYTE" = 2,
            "HEADER_LEN_WORD" = 3,
            "HEADER_LEN_CTR_WORD" = 4,
            "HEADER_LEN_FILL_WORD" = 5
          };
          taggedunion {
            block "OUT_EP_CMD_STIM" struct ep_parameters;
          };
          taggedunion {
            block "IN_EP_RESERR_DAQ_EVSERV" struct ep_parameters;
          };
          taggedstruct {
            "ALTERNATE_SETTING_NO" uchar;  /* Number of alternate setting   */
            "INTERFACE_STRING_DESCRIPTOR" char[101];
            (block "OUT_EP_ONLY_STIM" struct ep_parameters)*;
            (block "IN_EP_ONLY_DAQ" struct ep_parameters)*;
            block "IN_EP_ONLY_EVSERV" struct ep_parameters;
            (block "DAQ_LIST_USB_ENDPOINT" struct {
              uint;  /* reference to DAQ_LIST_NUMBER          */
              taggedstruct {
                "FIXED_IN" uchar;  /* this DAQ list always                
 ENDPOINT_NUMBER, not endpoint address */
                "FIXED_OUT" uchar;  /* this STIM list always               
 ENDPOINT_NUMBER, not endpoint address */
              };
            })*;  /* end of DAQ_LIST_USB_ENDPOINT */
          };  /* end of optional */
        };

        struct SIMULINK_Parameters {
          taggedstruct {
            "MODEL_NAME" char[64];
          };
        };

        enum packet_assignment_type {
          "NOT_ALLOWED" = 0,
          "FIXED" = 1,
          "VARIABLE_INITIALISED" = 2,
          "VARIABLE" = 3
        };  /* end of packet_assignment_type */

        struct buffer {
          uchar;  /* FLX_BUF */
          taggedstruct {
            "MAX_FLX_LEN_BUF" taggedunion {
              "FIXED" uchar;  /* constant value */
              "VARIABLE" uchar;  /* initial value */
            };  /* end of MAX_FLX_LEN_BUF */
            block "LPDU_ID" taggedstruct {
              "FLX_SLOT_ID" taggedunion {
                "FIXED" uint;
                "VARIABLE" taggedstruct {
                  "INITIAL_VALUE" uint;
                };
              };  /* end of FLX_SLOT_ID */
              "OFFSET" taggedunion {
                "FIXED" uchar;
                "VARIABLE" taggedstruct {
                  "INITIAL_VALUE" uchar;
                };
              };  /* end of OFFSET */
              "CYCLE_REPETITION" taggedunion {
                "FIXED" uchar;
                "VARIABLE" taggedstruct {
                  "INITIAL_VALUE" uchar;
                };
              };  /* end of CYCLE_REPETITION */
              "CHANNEL" taggedunion {
                "FIXED" enum {
                  "A" = 0,
                  "B" = 1
                };
                "VARIABLE" taggedstruct {
                  "INITIAL_VALUE" enum {
                    "A" = 0,
                    "B" = 1
                  };
                };
              };  /* end of CHANNEL */
            };  /* end of LPDU_ID */
            block "XCP_PACKET" taggedstruct {
              "CMD" enum packet_assignment_type;  /* end of CMD     */
              "RES_ERR" enum packet_assignment_type;  /* end of RES_ERR */
              "EV_SERV" enum packet_assignment_type;  /* end of EV_SERV */
              "DAQ" enum packet_assignment_type;  /* end of DAQ     */
              "STIM" enum packet_assignment_type;  /* end of STIM    */
            };  /* end of XCP_PACKET */
          };
        };  /* end of buffer */

        struct FLX_Parameters {
          uint;  /* XCP on FlexRay version  
 e.g. "1.0" = 0x0100 */
          uint;  /* T1_FLX [ms] */
          char[256];  /* FIBEX-file 
 including CHI information 
 including extension 
 without path */
          char[256];  /* Cluster-ID */
          uchar;  /* NAX */
          enum {
            "HEADER_NAX" = 0,
            "HEADER_NAX_FILL" = 1,
            "HEADER_NAX_CTR" = 2,
            "HEADER_NAX_FILL3" = 3,
            "HEADER_NAX_CTR_FILL2" = 4,
            "HEADER_NAX_LEN" = 5,
            "HEADER_NAX_CTR_LEN" = 6,
            "HEADER_NAX_FILL2_LEN" = 7,
            "HEADER_NAX_CTR_FILL_LEN" = 8
          };
          enum {
            "PACKET_ALIGNMENT_8" = 0,
            "PACKET_ALIGNMENT_16" = 1,
            "PACKET_ALIGNMENT_32" = 2
          };
          taggedunion {
            block "INITIAL_CMD_BUFFER" struct buffer;
          };
          taggedunion {
            block "INITIAL_RES_ERR_BUFFER" struct buffer;
          };
          taggedstruct {
            (block "POOL_BUFFER" struct buffer)*;
          };
        };
      block "IF_DATA" taggedunion if_data {
        "CANAPE_EXT" struct {
          int;  /* version number */
          taggedstruct {
            "LINK_MAP" struct {
              char[256];  /* segment name */
              long;  /* base address of the segment */
              uint;  /* address extension of the segment */
              uint;  /* flag: address is relative to DS */
              long;  /* offset of the segment address */
              uint;  /* datatypValid */
              uint;  /* enum datatyp */
              uint;  /* bit offset of the segment */
            };
            "DISPLAY" struct {
              long;  /* display color */
              double;  /* minimal display value (phys)*/
              double;  /* maximal display value (phys)*/
            };
            "VIRTUAL_CONVERSION" struct {
              char[256];  /* name of the conversion formula */
            };
          };
        };
        "CANAPE_MODULE" struct {
          taggedstruct {
            ("RECORD_LAYOUT_STEPSIZE" struct {
              char[256];  /* name of record layout*/
              uint;  /* stepsize for FNC_VALUES */
              uint;  /* stepsize for AXIS_PTS_X */
              uint;  /* stepsize for AXIS_PTS_Y */
              uint;  /* stepsize for AXIS_PTS_Z */
              uint;  /* stepsize for AXIS_PTS_4 */
              uint;  /* stepsize for AXIS_PTS_5 */
            })*;
          };
        };
        "CANAPE_ADDRESS_UPDATE" taggedstruct {
          ("EPK_ADDRESS" struct {
            char[1024];  /* name of the corresponding symbol in MAP file */
            long;  /* optional address offset */
          })*;
          "ECU_CALIBRATION_OFFSET" struct {
            char[1024];  /* name of the corresponding symbol in MAP file */
            long;  /* optional address offset */
          };
          (block "CALIBRATION_METHOD" taggedunion {
            "AUTOSAR_SINGLE_POINTERED" struct {
              char[1024];  /* MAP symbol name for pointer table in RAM */
              long;  /* optional address offset */
              taggedstruct {
                "ORIGINAL_POINTER_TABLE" struct {
                  char[1024];  /* MAP symbol name for pointer table in FLASH */
                  long;  /* optional address offset */
                };
              };
            };
            "InCircuit2" struct {
              char[1024];  /* MAP symbol name for pointer table in RAM */
              long;  /* optional address offset */
              taggedstruct {
                "ORIGINAL_POINTER_TABLE" struct {
                  char[1024];  /* MAP symbol name for pointer table in FLASH */
                  long;  /* optional address offset */
                };
                "FLASH_SECTION" struct {
                  ulong;  /* start address of flash section */
                  ulong;  /* length of flash section */
                };
              };
            };
          })*;
          block "MAP_SYMBOL" taggedstruct {
            "FIRST" struct {
              char[1024];  /* symbol name of the corresponding segment in MAP file */
              long;  /* offset */
            };
            "LAST" struct {
              char[1024];  /* symbol name of the corresponding segment in MAP file */
              long;  /* offset */
            };
            ("ADDRESS_MAPPING_XCP" struct {
              char[1024];  /* symbol name of source range in MAP file */
              char[1024];  /* symbol name of destination range in MAP file */
            })*;
          };
          (block "MEMORY_SEGMENT" struct {
            char[1024];  /* name of the memory segment */
            taggedstruct {
              "FIRST" struct {
                char[1024];  /* symbol name of the corresponding segment in MAP file */
                long;  /* offset */
              };
              "LAST" struct {
                char[1024];  /* symbol name of the corresponding segment in MAP file */
                long;  /* offset */
              };
              ("ADDRESS_MAPPING_XCP" struct {
                char[1024];  /* symbol name of source range in MAP file */
                char[1024];  /* symbol name of destination range in MAP file */
              })*;
            };
          })*;
        };
        "CANAPE_GROUP" taggedstruct {
          block "STRUCTURE_LIST" (char[1024])*;
        };
        "XCP" struct {
          taggedstruct Common_Parameters;  /* default parameters */
          taggedstruct {
            block "XCP_ON_CAN" struct {
              struct CAN_Parameters;  /* specific for CAN */
              taggedstruct Common_Parameters;  /* overruling of default */
            };
            block "XCP_ON_SxI" struct {
              struct SxI_Parameters;  /* specific for SxI */
              taggedstruct Common_Parameters;  /* overruling of default */
            };
            block "XCP_ON_TCP_IP" struct {
              struct TCP_IP_Parameters;  /* specific for TCP_IP */
              taggedstruct Common_Parameters;  /* overruling of default */
            };
            block "XCP_ON_UDP_IP" struct {
              struct UDP_Parameters;  /* specific for UDP */
              taggedstruct Common_Parameters;  /* overruling of default */
            };
            block "XCP_ON_USB" struct {
              struct USB_Parameters;  /* specific for USB      */
              taggedstruct Common_Parameters;  /* overruling of default */
            };
            block "XCP_ON_FLX" struct {
              struct FLX_Parameters;  /* specific for FlexRay  */
              taggedstruct Common_Parameters;  /* overruling of default */
            };
          };
        };
        "XCPplus" struct {
          uint;
          taggedstruct {
            block "PROTOCOL_LAYER" struct {
              uint;
              uint;
              uint;
              uint;
              uint;
              uint;
              uint;
              uint;
              uchar;
              uint;
              enum {
                "BYTE_ORDER_MSB_LAST" = 0,
                "BYTE_ORDER_MSB_FIRST" = 1
              };
              enum {
                "ADDRESS_GRANULARITY_BYTE" = 1,
                "ADDRESS_GRANULARITY_WORD" = 2,
                "ADDRESS_GRANULARITY_DWORD" = 4
              };
              taggedstruct {
                ("OPTIONAL_CMD" enum {
                  "GET_COMM_MODE_INFO" = 251,
                  "GET_ID" = 250,
                  "SET_REQUEST" = 249,
                  "GET_SEED" = 248,
                  "UNLOCK" = 247,
                  "SET_MTA" = 246,
                  "UPLOAD" = 245,
                  "SHORT_UPLOAD" = 244,
                  "BUILD_CHECKSUM" = 243,
                  "TRANSPORT_LAYER_CMD" = 242,
                  "USER_CMD" = 241,
                  "DOWNLOAD" = 240,
                  "DOWNLOAD_NEXT" = 239,
                  "DOWNLOAD_MAX" = 238,
                  "SHORT_DOWNLOAD" = 237,
                  "MODIFY_BITS" = 236,
                  "SET_CAL_PAGE" = 235,
                  "GET_CAL_PAGE" = 234,
                  "GET_PAG_PROCESSOR_INFO" = 233,
                  "GET_SEGMENT_INFO" = 232,
                  "GET_PAGE_INFO" = 231,
                  "SET_SEGMENT_MODE" = 230,
                  "GET_SEGMENT_MODE" = 229,
                  "COPY_CAL_PAGE" = 228,
                  "CLEAR_DAQ_LIST" = 227,
                  "SET_DAQ_PTR" = 226,
                  "WRITE_DAQ" = 225,
                  "SET_DAQ_LIST_MODE" = 224,
                  "GET_DAQ_LIST_MODE" = 223,
                  "START_STOP_DAQ_LIST" = 222,
                  "START_STOP_SYNCH" = 221,
                  "GET_DAQ_CLOCK" = 220,
                  "READ_DAQ" = 219,
                  "GET_DAQ_PROCESSOR_INFO" = 218,
                  "GET_DAQ_RESOLUTION_INFO" = 217,
                  "GET_DAQ_LIST_INFO" = 216,
                  "GET_DAQ_EVENT_INFO" = 215,
                  "FREE_DAQ" = 214,
                  "ALLOC_DAQ" = 213,
                  "ALLOC_ODT" = 212,
                  "ALLOC_ODT_ENTRY" = 211,
                  "PROGRAM_START" = 210,
                  "PROGRAM_CLEAR" = 209,
                  "PROGRAM" = 208,
                  "PROGRAM_RESET" = 207,
                  "GET_PGM_PROCESSOR_INFO" = 206,
                  "GET_SECTOR_INFO" = 205,
                  "PROGRAM_PREPARE" = 204,
                  "PROGRAM_FORMAT" = 203,
                  "PROGRAM_NEXT" = 202,
                  "PROGRAM_MAX" = 201,
                  "PROGRAM_VERIFY" = 200,
                  "WRITE_DAQ_MULTIPLE" = 199
                })*;
                "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                  "BLOCK" taggedstruct {
                    "SLAVE" ;
                    "MASTER" struct {
                      uchar;
                      uchar;
                    };
                  };
                  "INTERLEAVED" uchar;
                };
                "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];
              };
            };
            block "SEGMENT" struct {
              uchar;
              uchar;
              uchar;
              uchar;
              uchar;
              taggedstruct {
                block "CHECKSUM" struct {
                  enum {
                    "XCP_ADD_11" = 1,
                    "XCP_ADD_12" = 2,
                    "XCP_ADD_14" = 3,
                    "XCP_ADD_22" = 4,
                    "XCP_ADD_24" = 5,
                    "XCP_ADD_44" = 6,
                    "XCP_CRC_16" = 7,
                    "XCP_CRC_16_CITT" = 8,
                    "XCP_CRC_32" = 9,
                    "XCP_USER_DEFINED" = 255
                  };
                  taggedstruct {
                    "MAX_BLOCK_SIZE" ulong;
                    "EXTERNAL_FUNCTION" char[256];
                  };
                };
                (block "PAGE" struct {
                  uchar;
                  enum {
                    "ECU_ACCESS_NOT_ALLOWED" = 0,
                    "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                    "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                    "ECU_ACCESS_DONT_CARE" = 3
                  };
                  enum {
                    "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                    "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                    "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                    "XCP_READ_ACCESS_DONT_CARE" = 3
                  };
                  enum {
                    "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                    "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                    "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                    "XCP_WRITE_ACCESS_DONT_CARE" = 3
                  };
                  taggedstruct {
                    "INIT_SEGMENT" uchar;
                  };
                })*;
                (block "ADDRESS_MAPPING" struct {
                  ulong;
                  ulong;
                  ulong;
                })*;
                "PGM_VERIFY" ulong;
              };
            };
            block "DAQ" struct {
              enum {
                "STATIC" = 0,
                "DYNAMIC" = 1
              };
              uint;
              uint;
              uchar;
              enum {
                "OPTIMISATION_TYPE_DEFAULT" = 0,
                "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
                "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
                "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
                "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
                "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
              };
              enum {
                "ADDRESS_EXTENSION_FREE" = 0,
                "ADDRESS_EXTENSION_ODT" = 1,
                "ADDRESS_EXTENSION_DAQ" = 3
              };
              enum {
                "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
                "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
                "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
                "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
              };
              enum {
                "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
                "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
                "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
                "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
              };
              uchar;
              enum {
                "NO_OVERLOAD_INDICATION" = 0,
                "OVERLOAD_INDICATION_PID" = 1,
                "OVERLOAD_INDICATION_EVENT" = 2
              };
              taggedstruct {
                "DAQ_ALTERNATING_SUPPORTED" uint;
                "PRESCALER_SUPPORTED" ;
                "RESUME_SUPPORTED" ;
                "STORE_DAQ_SUPPORTED" ;
                block "STIM" struct {
                  enum {
                    "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                    "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                    "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                    "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
                  };
                  uchar;
                  taggedstruct {
                    "BIT_STIM_SUPPORTED" ;
                    "MIN_ST_STIM" uchar;
                  };
                };
                block "TIMESTAMP_SUPPORTED" struct {
                  uint;
                  enum {
                    "NO_TIME_STAMP" = 0,
                    "SIZE_BYTE" = 1,
                    "SIZE_WORD" = 2,
                    "SIZE_DWORD" = 4
                  };
                  enum {
                    "UNIT_1NS" = 0,
                    "UNIT_10NS" = 1,
                    "UNIT_100NS" = 2,
                    "UNIT_1US" = 3,
                    "UNIT_10US" = 4,
                    "UNIT_100US" = 5,
                    "UNIT_1MS" = 6,
                    "UNIT_10MS" = 7,
                    "UNIT_100MS" = 8,
                    "UNIT_1S" = 9,
                    "UNIT_1PS" = 10,
                    "UNIT_10PS" = 11,
                    "UNIT_100PS" = 12
                  };
                  taggedstruct {
                    "TIMESTAMP_FIXED" ;
                  };
                };
                "PID_OFF_SUPPORTED" ;
                (block "DAQ_LIST" struct {
                  uint;
                  taggedstruct {
                    "DAQ_LIST_TYPE" enum {
                      "DAQ" = 1,
                      "STIM" = 2,
                      "DAQ_STIM" = 3
                    };
                    "MAX_ODT" uchar;
                    "MAX_ODT_ENTRIES" uchar;
                    "FIRST_PID" uchar;
                    "EVENT_FIXED" uint;
                    block "PREDEFINED" taggedstruct {
                      (block "ODT" struct {
                        uchar;
                        taggedstruct {
                          ("ODT_ENTRY" struct {
                            uchar;
                            ulong;
                            uchar;
                            uchar;
                            uchar;
                          })*;
                        };
                      })*;
                    };
                  };
                })*;
                (block "EVENT" struct {
                  char[101];
                  char[9];
                  uint;
                  enum {
                    "DAQ" = 1,
                    "STIM" = 2,
                    "DAQ_STIM" = 3
                  };
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  taggedstruct {
                    "COMPLEMENTARY_BYPASS_EVENT_CHANNEL_NUMBER" uint;
                    "CONSISTENCY" enum {
                      "DAQ" = 0,
                      "EVENT" = 1
                    };
                  };
                })*;
              };
            };
            block "PAG" struct {
              uchar;
              taggedstruct {
                "FREEZE_SUPPORTED" ;
              };
            };
            block "PGM" struct {
              enum {
                "PGM_MODE_ABSOLUTE" = 1,
                "PGM_MODE_FUNCTIONAL" = 2,
                "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
              };
              uchar;
              uchar;
              taggedstruct {
                (block "SECTOR" struct {
                  char[101];
                  uchar;
                  ulong;
                  ulong;
                  uchar;
                  uchar;
                  uchar;
                })*;
                "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                  "BLOCK" taggedstruct {
                    "SLAVE" ;
                    "MASTER" struct {
                      uchar;
                      uchar;
                    };
                  };
                  "INTERLEAVED" uchar;
                };
              };
            };
            block "DAQ_EVENT" taggedunion {
              "FIXED_EVENT_LIST" taggedstruct {
                ("EVENT" uint)*;
              };
              "VARIABLE" taggedstruct {
                block "AVAILABLE_EVENT_LIST" taggedstruct {
                  ("EVENT" uint)*;
                };
                block "DEFAULT_EVENT_LIST" taggedstruct {
                  ("EVENT" uint)*;
                };
              };
            };
          };
          taggedstruct {
            (block "XCP_ON_CAN" struct {
              struct {
                uint;
                taggedstruct {
                  "CAN_ID_BROADCAST" ulong;
                  "CAN_ID_MASTER" ulong;
                  "CAN_ID_MASTER_INCREMENTAL" ;
                  "CAN_ID_SLAVE" ulong;
                  "BAUDRATE" ulong;
                  "SAMPLE_POINT" uchar;
                  "SAMPLE_RATE" enum {
                    "SINGLE" = 1,
                    "TRIPLE" = 3
                  };
                  "BTL_CYCLES" uchar;
                  "SJW" uchar;
                  "SYNC_EDGE" enum {
                    "SINGLE" = 1,
                    "DUAL" = 2
                  };
                  "MAX_DLC_REQUIRED" ;
                  (block "DAQ_LIST_CAN_ID" struct {
                    uint;
                    taggedstruct {
                      "VARIABLE" ;
                      "FIXED" ulong;
                    };
                  })*;
                };
              };
              taggedstruct {
                block "PROTOCOL_LAYER" struct {
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uchar;
                  uint;
                  enum {
                    "BYTE_ORDER_MSB_LAST" = 0,
                    "BYTE_ORDER_MSB_FIRST" = 1
                  };
                  enum {
                    "ADDRESS_GRANULARITY_BYTE" = 1,
                    "ADDRESS_GRANULARITY_WORD" = 2,
                    "ADDRESS_GRANULARITY_DWORD" = 4
                  };
                  taggedstruct {
                    ("OPTIONAL_CMD" enum {
                      "GET_COMM_MODE_INFO" = 251,
                      "GET_ID" = 250,
                      "SET_REQUEST" = 249,
                      "GET_SEED" = 248,
                      "UNLOCK" = 247,
                      "SET_MTA" = 246,
                      "UPLOAD" = 245,
                      "SHORT_UPLOAD" = 244,
                      "BUILD_CHECKSUM" = 243,
                      "TRANSPORT_LAYER_CMD" = 242,
                      "USER_CMD" = 241,
                      "DOWNLOAD" = 240,
                      "DOWNLOAD_NEXT" = 239,
                      "DOWNLOAD_MAX" = 238,
                      "SHORT_DOWNLOAD" = 237,
                      "MODIFY_BITS" = 236,
                      "SET_CAL_PAGE" = 235,
                      "GET_CAL_PAGE" = 234,
                      "GET_PAG_PROCESSOR_INFO" = 233,
                      "GET_SEGMENT_INFO" = 232,
                      "GET_PAGE_INFO" = 231,
                      "SET_SEGMENT_MODE" = 230,
                      "GET_SEGMENT_MODE" = 229,
                      "COPY_CAL_PAGE" = 228,
                      "CLEAR_DAQ_LIST" = 227,
                      "SET_DAQ_PTR" = 226,
                      "WRITE_DAQ" = 225,
                      "SET_DAQ_LIST_MODE" = 224,
                      "GET_DAQ_LIST_MODE" = 223,
                      "START_STOP_DAQ_LIST" = 222,
                      "START_STOP_SYNCH" = 221,
                      "GET_DAQ_CLOCK" = 220,
                      "READ_DAQ" = 219,
                      "GET_DAQ_PROCESSOR_INFO" = 218,
                      "GET_DAQ_RESOLUTION_INFO" = 217,
                      "GET_DAQ_LIST_INFO" = 216,
                      "GET_DAQ_EVENT_INFO" = 215,
                      "FREE_DAQ" = 214,
                      "ALLOC_DAQ" = 213,
                      "ALLOC_ODT" = 212,
                      "ALLOC_ODT_ENTRY" = 211,
                      "PROGRAM_START" = 210,
                      "PROGRAM_CLEAR" = 209,
                      "PROGRAM" = 208,
                      "PROGRAM_RESET" = 207,
                      "GET_PGM_PROCESSOR_INFO" = 206,
                      "GET_SECTOR_INFO" = 205,
                      "PROGRAM_PREPARE" = 204,
                      "PROGRAM_FORMAT" = 203,
                      "PROGRAM_NEXT" = 202,
                      "PROGRAM_MAX" = 201,
                      "PROGRAM_VERIFY" = 200,
                      "WRITE_DAQ_MULTIPLE" = 199
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                    "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];
                  };
                };
                block "SEGMENT" struct {
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  taggedstruct {
                    block "CHECKSUM" struct {
                      enum {
                        "XCP_ADD_11" = 1,
                        "XCP_ADD_12" = 2,
                        "XCP_ADD_14" = 3,
                        "XCP_ADD_22" = 4,
                        "XCP_ADD_24" = 5,
                        "XCP_ADD_44" = 6,
                        "XCP_CRC_16" = 7,
                        "XCP_CRC_16_CITT" = 8,
                        "XCP_CRC_32" = 9,
                        "XCP_USER_DEFINED" = 255
                      };
                      taggedstruct {
                        "MAX_BLOCK_SIZE" ulong;
                        "EXTERNAL_FUNCTION" char[256];
                      };
                    };
                    (block "PAGE" struct {
                      uchar;
                      enum {
                        "ECU_ACCESS_NOT_ALLOWED" = 0,
                        "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                        "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                        "ECU_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_READ_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_WRITE_ACCESS_DONT_CARE" = 3
                      };
                      taggedstruct {
                        "INIT_SEGMENT" uchar;
                      };
                    })*;
                    (block "ADDRESS_MAPPING" struct {
                      ulong;
                      ulong;
                      ulong;
                    })*;
                    "PGM_VERIFY" ulong;
                  };
                };
                block "DAQ" struct {
                  enum {
                    "STATIC" = 0,
                    "DYNAMIC" = 1
                  };
                  uint;
                  uint;
                  uchar;
                  enum {
                    "OPTIMISATION_TYPE_DEFAULT" = 0,
                    "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
                    "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
                    "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
                    "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
                    "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
                  };
                  enum {
                    "ADDRESS_EXTENSION_FREE" = 0,
                    "ADDRESS_EXTENSION_ODT" = 1,
                    "ADDRESS_EXTENSION_DAQ" = 3
                  };
                  enum {
                    "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
                  };
                  enum {
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
                  };
                  uchar;
                  enum {
                    "NO_OVERLOAD_INDICATION" = 0,
                    "OVERLOAD_INDICATION_PID" = 1,
                    "OVERLOAD_INDICATION_EVENT" = 2
                  };
                  taggedstruct {
                    "DAQ_ALTERNATING_SUPPORTED" uint;
                    "PRESCALER_SUPPORTED" ;
                    "RESUME_SUPPORTED" ;
                    "STORE_DAQ_SUPPORTED" ;
                    block "STIM" struct {
                      enum {
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
                      };
                      uchar;
                      taggedstruct {
                        "BIT_STIM_SUPPORTED" ;
                        "MIN_ST_STIM" uchar;
                      };
                    };
                    block "TIMESTAMP_SUPPORTED" struct {
                      uint;
                      enum {
                        "NO_TIME_STAMP" = 0,
                        "SIZE_BYTE" = 1,
                        "SIZE_WORD" = 2,
                        "SIZE_DWORD" = 4
                      };
                      enum {
                        "UNIT_1NS" = 0,
                        "UNIT_10NS" = 1,
                        "UNIT_100NS" = 2,
                        "UNIT_1US" = 3,
                        "UNIT_10US" = 4,
                        "UNIT_100US" = 5,
                        "UNIT_1MS" = 6,
                        "UNIT_10MS" = 7,
                        "UNIT_100MS" = 8,
                        "UNIT_1S" = 9,
                        "UNIT_1PS" = 10,
                        "UNIT_10PS" = 11,
                        "UNIT_100PS" = 12
                      };
                      taggedstruct {
                        "TIMESTAMP_FIXED" ;
                      };
                    };
                    "PID_OFF_SUPPORTED" ;
                    (block "DAQ_LIST" struct {
                      uint;
                      taggedstruct {
                        "DAQ_LIST_TYPE" enum {
                          "DAQ" = 1,
                          "STIM" = 2,
                          "DAQ_STIM" = 3
                        };
                        "MAX_ODT" uchar;
                        "MAX_ODT_ENTRIES" uchar;
                        "FIRST_PID" uchar;
                        "EVENT_FIXED" uint;
                        block "PREDEFINED" taggedstruct {
                          (block "ODT" struct {
                            uchar;
                            taggedstruct {
                              ("ODT_ENTRY" struct {
                                uchar;
                                ulong;
                                uchar;
                                uchar;
                                uchar;
                              })*;
                            };
                          })*;
                        };
                      };
                    })*;
                    (block "EVENT" struct {
                      char[101];
                      char[9];
                      uint;
                      enum {
                        "DAQ" = 1,
                        "STIM" = 2,
                        "DAQ_STIM" = 3
                      };
                      uchar;
                      uchar;
                      uchar;
                      uchar;
                      taggedstruct {
                        "COMPLEMENTARY_BYPASS_EVENT_CHANNEL_NUMBER" uint;
                        "CONSISTENCY" enum {
                          "DAQ" = 0,
                          "EVENT" = 1
                        };
                      };
                    })*;
                  };
                };
                block "PAG" struct {
                  uchar;
                  taggedstruct {
                    "FREEZE_SUPPORTED" ;
                  };
                };
                block "PGM" struct {
                  enum {
                    "PGM_MODE_ABSOLUTE" = 1,
                    "PGM_MODE_FUNCTIONAL" = 2,
                    "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
                  };
                  uchar;
                  uchar;
                  taggedstruct {
                    (block "SECTOR" struct {
                      char[101];
                      uchar;
                      ulong;
                      ulong;
                      uchar;
                      uchar;
                      uchar;
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                  };
                };
                block "DAQ_EVENT" taggedunion {
                  "FIXED_EVENT_LIST" taggedstruct {
                    ("EVENT" uint)*;
                  };
                  "VARIABLE" taggedstruct {
                    block "AVAILABLE_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                    block "DEFAULT_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                  };
                };
              };
              taggedstruct {
                "TRANSPORT_LAYER_INSTANCE" char[101];
              };
            })*;
            (block "XCP_ON_SxI" struct {
              struct {
                uint;
                ulong;
                taggedstruct {
                  "ASYNCH_FULL_DUPLEX_MODE" struct {
                    enum {
                      "PARITY_NONE" = 0,
                      "PARITY_ODD" = 1,
                      "PARITY_EVEN" = 2
                    };
                    enum {
                      "ONE_STOP_BIT" = 1,
                      "TWO_STOP_BITS" = 2
                    };
                    taggedstruct {
                      block "FRAMING" struct {
                        uchar;
                        uchar;
                      };
                    };
                  };
                  "SYNCH_FULL_DUPLEX_MODE_BYTE" ;
                  "SYNCH_FULL_DUPLEX_MODE_WORD" ;
                  "SYNCH_FULL_DUPLEX_MODE_DWORD" ;
                  "SYNCH_MASTER_SLAVE_MODE_BYTE" ;
                  "SYNCH_MASTER_SLAVE_MODE_WORD" ;
                  "SYNCH_MASTER_SLAVE_MODE_DWORD" ;
                };
                enum {
                  "HEADER_LEN_BYTE" = 0,
                  "HEADER_LEN_CTR_BYTE" = 1,
                  "HEADER_LEN_FILL_BYTE" = 2,
                  "HEADER_LEN_WORD" = 3,
                  "HEADER_LEN_CTR_WORD" = 4,
                  "HEADER_LEN_FILL_WORD" = 5
                };
                enum {
                  "NO_CHECKSUM" = 0,
                  "CHECKSUM_BYTE" = 1,
                  "CHECKSUM_WORD" = 2
                };
              };
              taggedstruct {
                block "PROTOCOL_LAYER" struct {
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uchar;
                  uint;
                  enum {
                    "BYTE_ORDER_MSB_LAST" = 0,
                    "BYTE_ORDER_MSB_FIRST" = 1
                  };
                  enum {
                    "ADDRESS_GRANULARITY_BYTE" = 1,
                    "ADDRESS_GRANULARITY_WORD" = 2,
                    "ADDRESS_GRANULARITY_DWORD" = 4
                  };
                  taggedstruct {
                    ("OPTIONAL_CMD" enum {
                      "GET_COMM_MODE_INFO" = 251,
                      "GET_ID" = 250,
                      "SET_REQUEST" = 249,
                      "GET_SEED" = 248,
                      "UNLOCK" = 247,
                      "SET_MTA" = 246,
                      "UPLOAD" = 245,
                      "SHORT_UPLOAD" = 244,
                      "BUILD_CHECKSUM" = 243,
                      "TRANSPORT_LAYER_CMD" = 242,
                      "USER_CMD" = 241,
                      "DOWNLOAD" = 240,
                      "DOWNLOAD_NEXT" = 239,
                      "DOWNLOAD_MAX" = 238,
                      "SHORT_DOWNLOAD" = 237,
                      "MODIFY_BITS" = 236,
                      "SET_CAL_PAGE" = 235,
                      "GET_CAL_PAGE" = 234,
                      "GET_PAG_PROCESSOR_INFO" = 233,
                      "GET_SEGMENT_INFO" = 232,
                      "GET_PAGE_INFO" = 231,
                      "SET_SEGMENT_MODE" = 230,
                      "GET_SEGMENT_MODE" = 229,
                      "COPY_CAL_PAGE" = 228,
                      "CLEAR_DAQ_LIST" = 227,
                      "SET_DAQ_PTR" = 226,
                      "WRITE_DAQ" = 225,
                      "SET_DAQ_LIST_MODE" = 224,
                      "GET_DAQ_LIST_MODE" = 223,
                      "START_STOP_DAQ_LIST" = 222,
                      "START_STOP_SYNCH" = 221,
                      "GET_DAQ_CLOCK" = 220,
                      "READ_DAQ" = 219,
                      "GET_DAQ_PROCESSOR_INFO" = 218,
                      "GET_DAQ_RESOLUTION_INFO" = 217,
                      "GET_DAQ_LIST_INFO" = 216,
                      "GET_DAQ_EVENT_INFO" = 215,
                      "FREE_DAQ" = 214,
                      "ALLOC_DAQ" = 213,
                      "ALLOC_ODT" = 212,
                      "ALLOC_ODT_ENTRY" = 211,
                      "PROGRAM_START" = 210,
                      "PROGRAM_CLEAR" = 209,
                      "PROGRAM" = 208,
                      "PROGRAM_RESET" = 207,
                      "GET_PGM_PROCESSOR_INFO" = 206,
                      "GET_SECTOR_INFO" = 205,
                      "PROGRAM_PREPARE" = 204,
                      "PROGRAM_FORMAT" = 203,
                      "PROGRAM_NEXT" = 202,
                      "PROGRAM_MAX" = 201,
                      "PROGRAM_VERIFY" = 200,
                      "WRITE_DAQ_MULTIPLE" = 199
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                    "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];
                  };
                };
                block "SEGMENT" struct {
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  taggedstruct {
                    block "CHECKSUM" struct {
                      enum {
                        "XCP_ADD_11" = 1,
                        "XCP_ADD_12" = 2,
                        "XCP_ADD_14" = 3,
                        "XCP_ADD_22" = 4,
                        "XCP_ADD_24" = 5,
                        "XCP_ADD_44" = 6,
                        "XCP_CRC_16" = 7,
                        "XCP_CRC_16_CITT" = 8,
                        "XCP_CRC_32" = 9,
                        "XCP_USER_DEFINED" = 255
                      };
                      taggedstruct {
                        "MAX_BLOCK_SIZE" ulong;
                        "EXTERNAL_FUNCTION" char[256];
                      };
                    };
                    (block "PAGE" struct {
                      uchar;
                      enum {
                        "ECU_ACCESS_NOT_ALLOWED" = 0,
                        "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                        "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                        "ECU_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_READ_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_WRITE_ACCESS_DONT_CARE" = 3
                      };
                      taggedstruct {
                        "INIT_SEGMENT" uchar;
                      };
                    })*;
                    (block "ADDRESS_MAPPING" struct {
                      ulong;
                      ulong;
                      ulong;
                    })*;
                    "PGM_VERIFY" ulong;
                  };
                };
                block "DAQ" struct {
                  enum {
                    "STATIC" = 0,
                    "DYNAMIC" = 1
                  };
                  uint;
                  uint;
                  uchar;
                  enum {
                    "OPTIMISATION_TYPE_DEFAULT" = 0,
                    "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
                    "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
                    "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
                    "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
                    "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
                  };
                  enum {
                    "ADDRESS_EXTENSION_FREE" = 0,
                    "ADDRESS_EXTENSION_ODT" = 1,
                    "ADDRESS_EXTENSION_DAQ" = 3
                  };
                  enum {
                    "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
                  };
                  enum {
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
                  };
                  uchar;
                  enum {
                    "NO_OVERLOAD_INDICATION" = 0,
                    "OVERLOAD_INDICATION_PID" = 1,
                    "OVERLOAD_INDICATION_EVENT" = 2
                  };
                  taggedstruct {
                    "DAQ_ALTERNATING_SUPPORTED" uint;
                    "PRESCALER_SUPPORTED" ;
                    "RESUME_SUPPORTED" ;
                    "STORE_DAQ_SUPPORTED" ;
                    block "STIM" struct {
                      enum {
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
                      };
                      uchar;
                      taggedstruct {
                        "BIT_STIM_SUPPORTED" ;
                        "MIN_ST_STIM" uchar;
                      };
                    };
                    block "TIMESTAMP_SUPPORTED" struct {
                      uint;
                      enum {
                        "NO_TIME_STAMP" = 0,
                        "SIZE_BYTE" = 1,
                        "SIZE_WORD" = 2,
                        "SIZE_DWORD" = 4
                      };
                      enum {
                        "UNIT_1NS" = 0,
                        "UNIT_10NS" = 1,
                        "UNIT_100NS" = 2,
                        "UNIT_1US" = 3,
                        "UNIT_10US" = 4,
                        "UNIT_100US" = 5,
                        "UNIT_1MS" = 6,
                        "UNIT_10MS" = 7,
                        "UNIT_100MS" = 8,
                        "UNIT_1S" = 9,
                        "UNIT_1PS" = 10,
                        "UNIT_10PS" = 11,
                        "UNIT_100PS" = 12
                      };
                      taggedstruct {
                        "TIMESTAMP_FIXED" ;
                      };
                    };
                    "PID_OFF_SUPPORTED" ;
                    (block "DAQ_LIST" struct {
                      uint;
                      taggedstruct {
                        "DAQ_LIST_TYPE" enum {
                          "DAQ" = 1,
                          "STIM" = 2,
                          "DAQ_STIM" = 3
                        };
                        "MAX_ODT" uchar;
                        "MAX_ODT_ENTRIES" uchar;
                        "FIRST_PID" uchar;
                        "EVENT_FIXED" uint;
                        block "PREDEFINED" taggedstruct {
                          (block "ODT" struct {
                            uchar;
                            taggedstruct {
                              ("ODT_ENTRY" struct {
                                uchar;
                                ulong;
                                uchar;
                                uchar;
                                uchar;
                              })*;
                            };
                          })*;
                        };
                      };
                    })*;
                    (block "EVENT" struct {
                      char[101];
                      char[9];
                      uint;
                      enum {
                        "DAQ" = 1,
                        "STIM" = 2,
                        "DAQ_STIM" = 3
                      };
                      uchar;
                      uchar;
                      uchar;
                      uchar;
                      taggedstruct {
                        "COMPLEMENTARY_BYPASS_EVENT_CHANNEL_NUMBER" uint;
                        "CONSISTENCY" enum {
                          "DAQ" = 0,
                          "EVENT" = 1
                        };
                      };
                    })*;
                  };
                };
                block "PAG" struct {
                  uchar;
                  taggedstruct {
                    "FREEZE_SUPPORTED" ;
                  };
                };
                block "PGM" struct {
                  enum {
                    "PGM_MODE_ABSOLUTE" = 1,
                    "PGM_MODE_FUNCTIONAL" = 2,
                    "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
                  };
                  uchar;
                  uchar;
                  taggedstruct {
                    (block "SECTOR" struct {
                      char[101];
                      uchar;
                      ulong;
                      ulong;
                      uchar;
                      uchar;
                      uchar;
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                  };
                };
                block "DAQ_EVENT" taggedunion {
                  "FIXED_EVENT_LIST" taggedstruct {
                    ("EVENT" uint)*;
                  };
                  "VARIABLE" taggedstruct {
                    block "AVAILABLE_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                    block "DEFAULT_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                  };
                };
              };
              taggedstruct {
                "TRANSPORT_LAYER_INSTANCE" char[101];
              };
            })*;
            (block "XCP_ON_TCP_IP" struct {
              struct {
                uint;
                uint;
                taggedunion {
                  "HOST_NAME" char[256];
                  "ADDRESS" char[15];
                };
              };
              taggedstruct {
                block "PROTOCOL_LAYER" struct {
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uchar;
                  uint;
                  enum {
                    "BYTE_ORDER_MSB_LAST" = 0,
                    "BYTE_ORDER_MSB_FIRST" = 1
                  };
                  enum {
                    "ADDRESS_GRANULARITY_BYTE" = 1,
                    "ADDRESS_GRANULARITY_WORD" = 2,
                    "ADDRESS_GRANULARITY_DWORD" = 4
                  };
                  taggedstruct {
                    ("OPTIONAL_CMD" enum {
                      "GET_COMM_MODE_INFO" = 251,
                      "GET_ID" = 250,
                      "SET_REQUEST" = 249,
                      "GET_SEED" = 248,
                      "UNLOCK" = 247,
                      "SET_MTA" = 246,
                      "UPLOAD" = 245,
                      "SHORT_UPLOAD" = 244,
                      "BUILD_CHECKSUM" = 243,
                      "TRANSPORT_LAYER_CMD" = 242,
                      "USER_CMD" = 241,
                      "DOWNLOAD" = 240,
                      "DOWNLOAD_NEXT" = 239,
                      "DOWNLOAD_MAX" = 238,
                      "SHORT_DOWNLOAD" = 237,
                      "MODIFY_BITS" = 236,
                      "SET_CAL_PAGE" = 235,
                      "GET_CAL_PAGE" = 234,
                      "GET_PAG_PROCESSOR_INFO" = 233,
                      "GET_SEGMENT_INFO" = 232,
                      "GET_PAGE_INFO" = 231,
                      "SET_SEGMENT_MODE" = 230,
                      "GET_SEGMENT_MODE" = 229,
                      "COPY_CAL_PAGE" = 228,
                      "CLEAR_DAQ_LIST" = 227,
                      "SET_DAQ_PTR" = 226,
                      "WRITE_DAQ" = 225,
                      "SET_DAQ_LIST_MODE" = 224,
                      "GET_DAQ_LIST_MODE" = 223,
                      "START_STOP_DAQ_LIST" = 222,
                      "START_STOP_SYNCH" = 221,
                      "GET_DAQ_CLOCK" = 220,
                      "READ_DAQ" = 219,
                      "GET_DAQ_PROCESSOR_INFO" = 218,
                      "GET_DAQ_RESOLUTION_INFO" = 217,
                      "GET_DAQ_LIST_INFO" = 216,
                      "GET_DAQ_EVENT_INFO" = 215,
                      "FREE_DAQ" = 214,
                      "ALLOC_DAQ" = 213,
                      "ALLOC_ODT" = 212,
                      "ALLOC_ODT_ENTRY" = 211,
                      "PROGRAM_START" = 210,
                      "PROGRAM_CLEAR" = 209,
                      "PROGRAM" = 208,
                      "PROGRAM_RESET" = 207,
                      "GET_PGM_PROCESSOR_INFO" = 206,
                      "GET_SECTOR_INFO" = 205,
                      "PROGRAM_PREPARE" = 204,
                      "PROGRAM_FORMAT" = 203,
                      "PROGRAM_NEXT" = 202,
                      "PROGRAM_MAX" = 201,
                      "PROGRAM_VERIFY" = 200,
                      "WRITE_DAQ_MULTIPLE" = 199
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                    "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];
                  };
                };
                block "SEGMENT" struct {
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  taggedstruct {
                    block "CHECKSUM" struct {
                      enum {
                        "XCP_ADD_11" = 1,
                        "XCP_ADD_12" = 2,
                        "XCP_ADD_14" = 3,
                        "XCP_ADD_22" = 4,
                        "XCP_ADD_24" = 5,
                        "XCP_ADD_44" = 6,
                        "XCP_CRC_16" = 7,
                        "XCP_CRC_16_CITT" = 8,
                        "XCP_CRC_32" = 9,
                        "XCP_USER_DEFINED" = 255
                      };
                      taggedstruct {
                        "MAX_BLOCK_SIZE" ulong;
                        "EXTERNAL_FUNCTION" char[256];
                      };
                    };
                    (block "PAGE" struct {
                      uchar;
                      enum {
                        "ECU_ACCESS_NOT_ALLOWED" = 0,
                        "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                        "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                        "ECU_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_READ_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_WRITE_ACCESS_DONT_CARE" = 3
                      };
                      taggedstruct {
                        "INIT_SEGMENT" uchar;
                      };
                    })*;
                    (block "ADDRESS_MAPPING" struct {
                      ulong;
                      ulong;
                      ulong;
                    })*;
                    "PGM_VERIFY" ulong;
                  };
                };
                block "DAQ" struct {
                  enum {
                    "STATIC" = 0,
                    "DYNAMIC" = 1
                  };
                  uint;
                  uint;
                  uchar;
                  enum {
                    "OPTIMISATION_TYPE_DEFAULT" = 0,
                    "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
                    "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
                    "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
                    "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
                    "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
                  };
                  enum {
                    "ADDRESS_EXTENSION_FREE" = 0,
                    "ADDRESS_EXTENSION_ODT" = 1,
                    "ADDRESS_EXTENSION_DAQ" = 3
                  };
                  enum {
                    "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
                  };
                  enum {
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
                  };
                  uchar;
                  enum {
                    "NO_OVERLOAD_INDICATION" = 0,
                    "OVERLOAD_INDICATION_PID" = 1,
                    "OVERLOAD_INDICATION_EVENT" = 2
                  };
                  taggedstruct {
                    "DAQ_ALTERNATING_SUPPORTED" uint;
                    "PRESCALER_SUPPORTED" ;
                    "RESUME_SUPPORTED" ;
                    "STORE_DAQ_SUPPORTED" ;
                    block "STIM" struct {
                      enum {
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
                      };
                      uchar;
                      taggedstruct {
                        "BIT_STIM_SUPPORTED" ;
                        "MIN_ST_STIM" uchar;
                      };
                    };
                    block "TIMESTAMP_SUPPORTED" struct {
                      uint;
                      enum {
                        "NO_TIME_STAMP" = 0,
                        "SIZE_BYTE" = 1,
                        "SIZE_WORD" = 2,
                        "SIZE_DWORD" = 4
                      };
                      enum {
                        "UNIT_1NS" = 0,
                        "UNIT_10NS" = 1,
                        "UNIT_100NS" = 2,
                        "UNIT_1US" = 3,
                        "UNIT_10US" = 4,
                        "UNIT_100US" = 5,
                        "UNIT_1MS" = 6,
                        "UNIT_10MS" = 7,
                        "UNIT_100MS" = 8,
                        "UNIT_1S" = 9,
                        "UNIT_1PS" = 10,
                        "UNIT_10PS" = 11,
                        "UNIT_100PS" = 12
                      };
                      taggedstruct {
                        "TIMESTAMP_FIXED" ;
                      };
                    };
                    "PID_OFF_SUPPORTED" ;
                    (block "DAQ_LIST" struct {
                      uint;
                      taggedstruct {
                        "DAQ_LIST_TYPE" enum {
                          "DAQ" = 1,
                          "STIM" = 2,
                          "DAQ_STIM" = 3
                        };
                        "MAX_ODT" uchar;
                        "MAX_ODT_ENTRIES" uchar;
                        "FIRST_PID" uchar;
                        "EVENT_FIXED" uint;
                        block "PREDEFINED" taggedstruct {
                          (block "ODT" struct {
                            uchar;
                            taggedstruct {
                              ("ODT_ENTRY" struct {
                                uchar;
                                ulong;
                                uchar;
                                uchar;
                                uchar;
                              })*;
                            };
                          })*;
                        };
                      };
                    })*;
                    (block "EVENT" struct {
                      char[101];
                      char[9];
                      uint;
                      enum {
                        "DAQ" = 1,
                        "STIM" = 2,
                        "DAQ_STIM" = 3
                      };
                      uchar;
                      uchar;
                      uchar;
                      uchar;
                      taggedstruct {
                        "COMPLEMENTARY_BYPASS_EVENT_CHANNEL_NUMBER" uint;
                        "CONSISTENCY" enum {
                          "DAQ" = 0,
                          "EVENT" = 1
                        };
                      };
                    })*;
                  };
                };
                block "PAG" struct {
                  uchar;
                  taggedstruct {
                    "FREEZE_SUPPORTED" ;
                  };
                };
                block "PGM" struct {
                  enum {
                    "PGM_MODE_ABSOLUTE" = 1,
                    "PGM_MODE_FUNCTIONAL" = 2,
                    "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
                  };
                  uchar;
                  uchar;
                  taggedstruct {
                    (block "SECTOR" struct {
                      char[101];
                      uchar;
                      ulong;
                      ulong;
                      uchar;
                      uchar;
                      uchar;
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                  };
                };
                block "DAQ_EVENT" taggedunion {
                  "FIXED_EVENT_LIST" taggedstruct {
                    ("EVENT" uint)*;
                  };
                  "VARIABLE" taggedstruct {
                    block "AVAILABLE_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                    block "DEFAULT_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                  };
                };
              };
              taggedstruct {
                "TRANSPORT_LAYER_INSTANCE" char[101];
              };
            })*;
            (block "XCP_ON_UDP_IP" struct {
              struct {
                uint;
                uint;
                taggedunion {
                  "HOST_NAME" char[256];
                  "ADDRESS" char[15];
                };
              };
              taggedstruct {
                block "PROTOCOL_LAYER" struct {
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uchar;
                  uint;
                  enum {
                    "BYTE_ORDER_MSB_LAST" = 0,
                    "BYTE_ORDER_MSB_FIRST" = 1
                  };
                  enum {
                    "ADDRESS_GRANULARITY_BYTE" = 1,
                    "ADDRESS_GRANULARITY_WORD" = 2,
                    "ADDRESS_GRANULARITY_DWORD" = 4
                  };
                  taggedstruct {
                    ("OPTIONAL_CMD" enum {
                      "GET_COMM_MODE_INFO" = 251,
                      "GET_ID" = 250,
                      "SET_REQUEST" = 249,
                      "GET_SEED" = 248,
                      "UNLOCK" = 247,
                      "SET_MTA" = 246,
                      "UPLOAD" = 245,
                      "SHORT_UPLOAD" = 244,
                      "BUILD_CHECKSUM" = 243,
                      "TRANSPORT_LAYER_CMD" = 242,
                      "USER_CMD" = 241,
                      "DOWNLOAD" = 240,
                      "DOWNLOAD_NEXT" = 239,
                      "DOWNLOAD_MAX" = 238,
                      "SHORT_DOWNLOAD" = 237,
                      "MODIFY_BITS" = 236,
                      "SET_CAL_PAGE" = 235,
                      "GET_CAL_PAGE" = 234,
                      "GET_PAG_PROCESSOR_INFO" = 233,
                      "GET_SEGMENT_INFO" = 232,
                      "GET_PAGE_INFO" = 231,
                      "SET_SEGMENT_MODE" = 230,
                      "GET_SEGMENT_MODE" = 229,
                      "COPY_CAL_PAGE" = 228,
                      "CLEAR_DAQ_LIST" = 227,
                      "SET_DAQ_PTR" = 226,
                      "WRITE_DAQ" = 225,
                      "SET_DAQ_LIST_MODE" = 224,
                      "GET_DAQ_LIST_MODE" = 223,
                      "START_STOP_DAQ_LIST" = 222,
                      "START_STOP_SYNCH" = 221,
                      "GET_DAQ_CLOCK" = 220,
                      "READ_DAQ" = 219,
                      "GET_DAQ_PROCESSOR_INFO" = 218,
                      "GET_DAQ_RESOLUTION_INFO" = 217,
                      "GET_DAQ_LIST_INFO" = 216,
                      "GET_DAQ_EVENT_INFO" = 215,
                      "FREE_DAQ" = 214,
                      "ALLOC_DAQ" = 213,
                      "ALLOC_ODT" = 212,
                      "ALLOC_ODT_ENTRY" = 211,
                      "PROGRAM_START" = 210,
                      "PROGRAM_CLEAR" = 209,
                      "PROGRAM" = 208,
                      "PROGRAM_RESET" = 207,
                      "GET_PGM_PROCESSOR_INFO" = 206,
                      "GET_SECTOR_INFO" = 205,
                      "PROGRAM_PREPARE" = 204,
                      "PROGRAM_FORMAT" = 203,
                      "PROGRAM_NEXT" = 202,
                      "PROGRAM_MAX" = 201,
                      "PROGRAM_VERIFY" = 200,
                      "WRITE_DAQ_MULTIPLE" = 199
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                    "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];
                  };
                };
                block "SEGMENT" struct {
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  taggedstruct {
                    block "CHECKSUM" struct {
                      enum {
                        "XCP_ADD_11" = 1,
                        "XCP_ADD_12" = 2,
                        "XCP_ADD_14" = 3,
                        "XCP_ADD_22" = 4,
                        "XCP_ADD_24" = 5,
                        "XCP_ADD_44" = 6,
                        "XCP_CRC_16" = 7,
                        "XCP_CRC_16_CITT" = 8,
                        "XCP_CRC_32" = 9,
                        "XCP_USER_DEFINED" = 255
                      };
                      taggedstruct {
                        "MAX_BLOCK_SIZE" ulong;
                        "EXTERNAL_FUNCTION" char[256];
                      };
                    };
                    (block "PAGE" struct {
                      uchar;
                      enum {
                        "ECU_ACCESS_NOT_ALLOWED" = 0,
                        "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                        "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                        "ECU_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_READ_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_WRITE_ACCESS_DONT_CARE" = 3
                      };
                      taggedstruct {
                        "INIT_SEGMENT" uchar;
                      };
                    })*;
                    (block "ADDRESS_MAPPING" struct {
                      ulong;
                      ulong;
                      ulong;
                    })*;
                    "PGM_VERIFY" ulong;
                  };
                };
                block "DAQ" struct {
                  enum {
                    "STATIC" = 0,
                    "DYNAMIC" = 1
                  };
                  uint;
                  uint;
                  uchar;
                  enum {
                    "OPTIMISATION_TYPE_DEFAULT" = 0,
                    "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
                    "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
                    "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
                    "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
                    "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
                  };
                  enum {
                    "ADDRESS_EXTENSION_FREE" = 0,
                    "ADDRESS_EXTENSION_ODT" = 1,
                    "ADDRESS_EXTENSION_DAQ" = 3
                  };
                  enum {
                    "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
                  };
                  enum {
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
                  };
                  uchar;
                  enum {
                    "NO_OVERLOAD_INDICATION" = 0,
                    "OVERLOAD_INDICATION_PID" = 1,
                    "OVERLOAD_INDICATION_EVENT" = 2
                  };
                  taggedstruct {
                    "DAQ_ALTERNATING_SUPPORTED" uint;
                    "PRESCALER_SUPPORTED" ;
                    "RESUME_SUPPORTED" ;
                    "STORE_DAQ_SUPPORTED" ;
                    block "STIM" struct {
                      enum {
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
                      };
                      uchar;
                      taggedstruct {
                        "BIT_STIM_SUPPORTED" ;
                        "MIN_ST_STIM" uchar;
                      };
                    };
                    block "TIMESTAMP_SUPPORTED" struct {
                      uint;
                      enum {
                        "NO_TIME_STAMP" = 0,
                        "SIZE_BYTE" = 1,
                        "SIZE_WORD" = 2,
                        "SIZE_DWORD" = 4
                      };
                      enum {
                        "UNIT_1NS" = 0,
                        "UNIT_10NS" = 1,
                        "UNIT_100NS" = 2,
                        "UNIT_1US" = 3,
                        "UNIT_10US" = 4,
                        "UNIT_100US" = 5,
                        "UNIT_1MS" = 6,
                        "UNIT_10MS" = 7,
                        "UNIT_100MS" = 8,
                        "UNIT_1S" = 9,
                        "UNIT_1PS" = 10,
                        "UNIT_10PS" = 11,
                        "UNIT_100PS" = 12
                      };
                      taggedstruct {
                        "TIMESTAMP_FIXED" ;
                      };
                    };
                    "PID_OFF_SUPPORTED" ;
                    (block "DAQ_LIST" struct {
                      uint;
                      taggedstruct {
                        "DAQ_LIST_TYPE" enum {
                          "DAQ" = 1,
                          "STIM" = 2,
                          "DAQ_STIM" = 3
                        };
                        "MAX_ODT" uchar;
                        "MAX_ODT_ENTRIES" uchar;
                        "FIRST_PID" uchar;
                        "EVENT_FIXED" uint;
                        block "PREDEFINED" taggedstruct {
                          (block "ODT" struct {
                            uchar;
                            taggedstruct {
                              ("ODT_ENTRY" struct {
                                uchar;
                                ulong;
                                uchar;
                                uchar;
                                uchar;
                              })*;
                            };
                          })*;
                        };
                      };
                    })*;
                    (block "EVENT" struct {
                      char[101];
                      char[9];
                      uint;
                      enum {
                        "DAQ" = 1,
                        "STIM" = 2,
                        "DAQ_STIM" = 3
                      };
                      uchar;
                      uchar;
                      uchar;
                      uchar;
                      taggedstruct {
                        "COMPLEMENTARY_BYPASS_EVENT_CHANNEL_NUMBER" uint;
                        "CONSISTENCY" enum {
                          "DAQ" = 0,
                          "EVENT" = 1
                        };
                      };
                    })*;
                  };
                };
                block "PAG" struct {
                  uchar;
                  taggedstruct {
                    "FREEZE_SUPPORTED" ;
                  };
                };
                block "PGM" struct {
                  enum {
                    "PGM_MODE_ABSOLUTE" = 1,
                    "PGM_MODE_FUNCTIONAL" = 2,
                    "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
                  };
                  uchar;
                  uchar;
                  taggedstruct {
                    (block "SECTOR" struct {
                      char[101];
                      uchar;
                      ulong;
                      ulong;
                      uchar;
                      uchar;
                      uchar;
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                  };
                };
                block "DAQ_EVENT" taggedunion {
                  "FIXED_EVENT_LIST" taggedstruct {
                    ("EVENT" uint)*;
                  };
                  "VARIABLE" taggedstruct {
                    block "AVAILABLE_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                    block "DEFAULT_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                  };
                };
              };
              taggedstruct {
                "TRANSPORT_LAYER_INSTANCE" char[101];
              };
            })*;
            (block "XCP_ON_USB" struct {
              struct {
                uint;
                uint;
                uint;
                uchar;
                enum {
                  "HEADER_LEN_BYTE" = 0,
                  "HEADER_LEN_CTR_BYTE" = 1,
                  "HEADER_LEN_FILL_BYTE" = 2,
                  "HEADER_LEN_WORD" = 3,
                  "HEADER_LEN_CTR_WORD" = 4,
                  "HEADER_LEN_FILL_WORD" = 5
                };
                taggedunion {
                  block "OUT_EP_CMD_STIM" struct {
                    uchar;
                    enum {
                      "BULK_TRANSFER" = 2,
                      "INTERRUPT_TRANSFER" = 3
                    };
                    uint;
                    uchar;
                    enum {
                      "MESSAGE_PACKING_SINGLE" = 0,
                      "MESSAGE_PACKING_MULTIPLE" = 1,
                      "MESSAGE_PACKING_STREAMING" = 2
                    };
                    enum {
                      "ALIGNMENT_8_BIT" = 0,
                      "ALIGNMENT_16_BIT" = 1,
                      "ALIGNMENT_32_BIT" = 2,
                      "ALIGNMENT_64_BIT" = 3
                    };
                    taggedstruct {
                      "RECOMMENDED_HOST_BUFSIZE" uint;
                    };
                  };
                };
                taggedunion {
                  block "IN_EP_RESERR_DAQ_EVSERV" struct {
                    uchar;
                    enum {
                      "BULK_TRANSFER" = 2,
                      "INTERRUPT_TRANSFER" = 3
                    };
                    uint;
                    uchar;
                    enum {
                      "MESSAGE_PACKING_SINGLE" = 0,
                      "MESSAGE_PACKING_MULTIPLE" = 1,
                      "MESSAGE_PACKING_STREAMING" = 2
                    };
                    enum {
                      "ALIGNMENT_8_BIT" = 0,
                      "ALIGNMENT_16_BIT" = 1,
                      "ALIGNMENT_32_BIT" = 2,
                      "ALIGNMENT_64_BIT" = 3
                    };
                    taggedstruct {
                      "RECOMMENDED_HOST_BUFSIZE" uint;
                    };
                  };
                };
                taggedstruct {
                  "ALTERNATE_SETTING_NO" uchar;
                  "INTERFACE_STRING_DESCRIPTOR" char[101];
                  (block "OUT_EP_ONLY_STIM" struct {
                    uchar;
                    enum {
                      "BULK_TRANSFER" = 2,
                      "INTERRUPT_TRANSFER" = 3
                    };
                    uint;
                    uchar;
                    enum {
                      "MESSAGE_PACKING_SINGLE" = 0,
                      "MESSAGE_PACKING_MULTIPLE" = 1,
                      "MESSAGE_PACKING_STREAMING" = 2
                    };
                    enum {
                      "ALIGNMENT_8_BIT" = 0,
                      "ALIGNMENT_16_BIT" = 1,
                      "ALIGNMENT_32_BIT" = 2,
                      "ALIGNMENT_64_BIT" = 3
                    };
                    taggedstruct {
                      "RECOMMENDED_HOST_BUFSIZE" uint;
                    };
                  })*;
                  (block "IN_EP_ONLY_DAQ" struct {
                    uchar;
                    enum {
                      "BULK_TRANSFER" = 2,
                      "INTERRUPT_TRANSFER" = 3
                    };
                    uint;
                    uchar;
                    enum {
                      "MESSAGE_PACKING_SINGLE" = 0,
                      "MESSAGE_PACKING_MULTIPLE" = 1,
                      "MESSAGE_PACKING_STREAMING" = 2
                    };
                    enum {
                      "ALIGNMENT_8_BIT" = 0,
                      "ALIGNMENT_16_BIT" = 1,
                      "ALIGNMENT_32_BIT" = 2,
                      "ALIGNMENT_64_BIT" = 3
                    };
                    taggedstruct {
                      "RECOMMENDED_HOST_BUFSIZE" uint;
                    };
                  })*;
                  block "IN_EP_ONLY_EVSERV" struct {
                    uchar;
                    enum {
                      "BULK_TRANSFER" = 2,
                      "INTERRUPT_TRANSFER" = 3
                    };
                    uint;
                    uchar;
                    enum {
                      "MESSAGE_PACKING_SINGLE" = 0,
                      "MESSAGE_PACKING_MULTIPLE" = 1,
                      "MESSAGE_PACKING_STREAMING" = 2
                    };
                    enum {
                      "ALIGNMENT_8_BIT" = 0,
                      "ALIGNMENT_16_BIT" = 1,
                      "ALIGNMENT_32_BIT" = 2,
                      "ALIGNMENT_64_BIT" = 3
                    };
                    taggedstruct {
                      "RECOMMENDED_HOST_BUFSIZE" uint;
                    };
                  };
                  (block "DAQ_LIST_USB_ENDPOINT" struct {
                    uint;
                    taggedstruct {
                      "FIXED_IN" uchar;
                      "FIXED_OUT" uchar;
                    };
                  })*;
                };
              };
              taggedstruct {
                block "PROTOCOL_LAYER" struct {
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uchar;
                  uint;
                  enum {
                    "BYTE_ORDER_MSB_LAST" = 0,
                    "BYTE_ORDER_MSB_FIRST" = 1
                  };
                  enum {
                    "ADDRESS_GRANULARITY_BYTE" = 1,
                    "ADDRESS_GRANULARITY_WORD" = 2,
                    "ADDRESS_GRANULARITY_DWORD" = 4
                  };
                  taggedstruct {
                    ("OPTIONAL_CMD" enum {
                      "GET_COMM_MODE_INFO" = 251,
                      "GET_ID" = 250,
                      "SET_REQUEST" = 249,
                      "GET_SEED" = 248,
                      "UNLOCK" = 247,
                      "SET_MTA" = 246,
                      "UPLOAD" = 245,
                      "SHORT_UPLOAD" = 244,
                      "BUILD_CHECKSUM" = 243,
                      "TRANSPORT_LAYER_CMD" = 242,
                      "USER_CMD" = 241,
                      "DOWNLOAD" = 240,
                      "DOWNLOAD_NEXT" = 239,
                      "DOWNLOAD_MAX" = 238,
                      "SHORT_DOWNLOAD" = 237,
                      "MODIFY_BITS" = 236,
                      "SET_CAL_PAGE" = 235,
                      "GET_CAL_PAGE" = 234,
                      "GET_PAG_PROCESSOR_INFO" = 233,
                      "GET_SEGMENT_INFO" = 232,
                      "GET_PAGE_INFO" = 231,
                      "SET_SEGMENT_MODE" = 230,
                      "GET_SEGMENT_MODE" = 229,
                      "COPY_CAL_PAGE" = 228,
                      "CLEAR_DAQ_LIST" = 227,
                      "SET_DAQ_PTR" = 226,
                      "WRITE_DAQ" = 225,
                      "SET_DAQ_LIST_MODE" = 224,
                      "GET_DAQ_LIST_MODE" = 223,
                      "START_STOP_DAQ_LIST" = 222,
                      "START_STOP_SYNCH" = 221,
                      "GET_DAQ_CLOCK" = 220,
                      "READ_DAQ" = 219,
                      "GET_DAQ_PROCESSOR_INFO" = 218,
                      "GET_DAQ_RESOLUTION_INFO" = 217,
                      "GET_DAQ_LIST_INFO" = 216,
                      "GET_DAQ_EVENT_INFO" = 215,
                      "FREE_DAQ" = 214,
                      "ALLOC_DAQ" = 213,
                      "ALLOC_ODT" = 212,
                      "ALLOC_ODT_ENTRY" = 211,
                      "PROGRAM_START" = 210,
                      "PROGRAM_CLEAR" = 209,
                      "PROGRAM" = 208,
                      "PROGRAM_RESET" = 207,
                      "GET_PGM_PROCESSOR_INFO" = 206,
                      "GET_SECTOR_INFO" = 205,
                      "PROGRAM_PREPARE" = 204,
                      "PROGRAM_FORMAT" = 203,
                      "PROGRAM_NEXT" = 202,
                      "PROGRAM_MAX" = 201,
                      "PROGRAM_VERIFY" = 200,
                      "WRITE_DAQ_MULTIPLE" = 199
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                    "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];
                  };
                };
                block "SEGMENT" struct {
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  taggedstruct {
                    block "CHECKSUM" struct {
                      enum {
                        "XCP_ADD_11" = 1,
                        "XCP_ADD_12" = 2,
                        "XCP_ADD_14" = 3,
                        "XCP_ADD_22" = 4,
                        "XCP_ADD_24" = 5,
                        "XCP_ADD_44" = 6,
                        "XCP_CRC_16" = 7,
                        "XCP_CRC_16_CITT" = 8,
                        "XCP_CRC_32" = 9,
                        "XCP_USER_DEFINED" = 255
                      };
                      taggedstruct {
                        "MAX_BLOCK_SIZE" ulong;
                        "EXTERNAL_FUNCTION" char[256];
                      };
                    };
                    (block "PAGE" struct {
                      uchar;
                      enum {
                        "ECU_ACCESS_NOT_ALLOWED" = 0,
                        "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                        "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                        "ECU_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_READ_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_WRITE_ACCESS_DONT_CARE" = 3
                      };
                      taggedstruct {
                        "INIT_SEGMENT" uchar;
                      };
                    })*;
                    (block "ADDRESS_MAPPING" struct {
                      ulong;
                      ulong;
                      ulong;
                    })*;
                    "PGM_VERIFY" ulong;
                  };
                };
                block "DAQ" struct {
                  enum {
                    "STATIC" = 0,
                    "DYNAMIC" = 1
                  };
                  uint;
                  uint;
                  uchar;
                  enum {
                    "OPTIMISATION_TYPE_DEFAULT" = 0,
                    "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
                    "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
                    "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
                    "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
                    "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
                  };
                  enum {
                    "ADDRESS_EXTENSION_FREE" = 0,
                    "ADDRESS_EXTENSION_ODT" = 1,
                    "ADDRESS_EXTENSION_DAQ" = 3
                  };
                  enum {
                    "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
                  };
                  enum {
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
                  };
                  uchar;
                  enum {
                    "NO_OVERLOAD_INDICATION" = 0,
                    "OVERLOAD_INDICATION_PID" = 1,
                    "OVERLOAD_INDICATION_EVENT" = 2
                  };
                  taggedstruct {
                    "DAQ_ALTERNATING_SUPPORTED" uint;
                    "PRESCALER_SUPPORTED" ;
                    "RESUME_SUPPORTED" ;
                    "STORE_DAQ_SUPPORTED" ;
                    block "STIM" struct {
                      enum {
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
                      };
                      uchar;
                      taggedstruct {
                        "BIT_STIM_SUPPORTED" ;
                        "MIN_ST_STIM" uchar;
                      };
                    };
                    block "TIMESTAMP_SUPPORTED" struct {
                      uint;
                      enum {
                        "NO_TIME_STAMP" = 0,
                        "SIZE_BYTE" = 1,
                        "SIZE_WORD" = 2,
                        "SIZE_DWORD" = 4
                      };
                      enum {
                        "UNIT_1NS" = 0,
                        "UNIT_10NS" = 1,
                        "UNIT_100NS" = 2,
                        "UNIT_1US" = 3,
                        "UNIT_10US" = 4,
                        "UNIT_100US" = 5,
                        "UNIT_1MS" = 6,
                        "UNIT_10MS" = 7,
                        "UNIT_100MS" = 8,
                        "UNIT_1S" = 9,
                        "UNIT_1PS" = 10,
                        "UNIT_10PS" = 11,
                        "UNIT_100PS" = 12
                      };
                      taggedstruct {
                        "TIMESTAMP_FIXED" ;
                      };
                    };
                    "PID_OFF_SUPPORTED" ;
                    (block "DAQ_LIST" struct {
                      uint;
                      taggedstruct {
                        "DAQ_LIST_TYPE" enum {
                          "DAQ" = 1,
                          "STIM" = 2,
                          "DAQ_STIM" = 3
                        };
                        "MAX_ODT" uchar;
                        "MAX_ODT_ENTRIES" uchar;
                        "FIRST_PID" uchar;
                        "EVENT_FIXED" uint;
                        block "PREDEFINED" taggedstruct {
                          (block "ODT" struct {
                            uchar;
                            taggedstruct {
                              ("ODT_ENTRY" struct {
                                uchar;
                                ulong;
                                uchar;
                                uchar;
                                uchar;
                              })*;
                            };
                          })*;
                        };
                      };
                    })*;
                    (block "EVENT" struct {
                      char[101];
                      char[9];
                      uint;
                      enum {
                        "DAQ" = 1,
                        "STIM" = 2,
                        "DAQ_STIM" = 3
                      };
                      uchar;
                      uchar;
                      uchar;
                      uchar;
                      taggedstruct {
                        "COMPLEMENTARY_BYPASS_EVENT_CHANNEL_NUMBER" uint;
                        "CONSISTENCY" enum {
                          "DAQ" = 0,
                          "EVENT" = 1
                        };
                      };
                    })*;
                  };
                };
                block "PAG" struct {
                  uchar;
                  taggedstruct {
                    "FREEZE_SUPPORTED" ;
                  };
                };
                block "PGM" struct {
                  enum {
                    "PGM_MODE_ABSOLUTE" = 1,
                    "PGM_MODE_FUNCTIONAL" = 2,
                    "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
                  };
                  uchar;
                  uchar;
                  taggedstruct {
                    (block "SECTOR" struct {
                      char[101];
                      uchar;
                      ulong;
                      ulong;
                      uchar;
                      uchar;
                      uchar;
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                  };
                };
                block "DAQ_EVENT" taggedunion {
                  "FIXED_EVENT_LIST" taggedstruct {
                    ("EVENT" uint)*;
                  };
                  "VARIABLE" taggedstruct {
                    block "AVAILABLE_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                    block "DEFAULT_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                  };
                };
              };
              taggedstruct {
                "TRANSPORT_LAYER_INSTANCE" char[101];
              };
            })*;
            (block "XCP_ON_FLX" struct {
              struct {
                uint;
                uint;
                char[256];
                char[256];
                uchar;
                enum {
                  "HEADER_NAX" = 0,
                  "HEADER_NAX_FILL" = 1,
                  "HEADER_NAX_CTR" = 2,
                  "HEADER_NAX_FILL3" = 3,
                  "HEADER_NAX_CTR_FILL2" = 4,
                  "HEADER_NAX_LEN" = 5,
                  "HEADER_NAX_CTR_LEN" = 6,
                  "HEADER_NAX_FILL2_LEN" = 7,
                  "HEADER_NAX_CTR_FILL_LEN" = 8
                };
                enum {
                  "PACKET_ALIGNMENT_8" = 0,
                  "PACKET_ALIGNMENT_16" = 1,
                  "PACKET_ALIGNMENT_32" = 2
                };
                taggedunion {
                  block "INITIAL_CMD_BUFFER" struct {
                    uchar;
                    taggedstruct {
                      "MAX_FLX_LEN_BUF" taggedunion {
                        "FIXED" uchar;
                        "VARIABLE" uchar;
                      };
                      block "LPDU_ID" taggedstruct {
                        "FLX_SLOT_ID" taggedunion {
                          "FIXED" uint;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uint;
                          };
                        };
                        "OFFSET" taggedunion {
                          "FIXED" uchar;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uchar;
                          };
                        };
                        "CYCLE_REPETITION" taggedunion {
                          "FIXED" uchar;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uchar;
                          };
                        };
                        "CHANNEL" taggedunion {
                          "FIXED" enum {
                            "A" = 0,
                            "B" = 1
                          };
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" enum {
                              "A" = 0,
                              "B" = 1
                            };
                          };
                        };
                      };
                      block "XCP_PACKET" taggedstruct {
                        "CMD" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "RES_ERR" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "EV_SERV" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "DAQ" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "STIM" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                      };
                    };
                  };
                };
                taggedunion {
                  block "INITIAL_RES_ERR_BUFFER" struct {
                    uchar;
                    taggedstruct {
                      "MAX_FLX_LEN_BUF" taggedunion {
                        "FIXED" uchar;
                        "VARIABLE" uchar;
                      };
                      block "LPDU_ID" taggedstruct {
                        "FLX_SLOT_ID" taggedunion {
                          "FIXED" uint;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uint;
                          };
                        };
                        "OFFSET" taggedunion {
                          "FIXED" uchar;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uchar;
                          };
                        };
                        "CYCLE_REPETITION" taggedunion {
                          "FIXED" uchar;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uchar;
                          };
                        };
                        "CHANNEL" taggedunion {
                          "FIXED" enum {
                            "A" = 0,
                            "B" = 1
                          };
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" enum {
                              "A" = 0,
                              "B" = 1
                            };
                          };
                        };
                      };
                      block "XCP_PACKET" taggedstruct {
                        "CMD" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "RES_ERR" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "EV_SERV" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "DAQ" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "STIM" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                      };
                    };
                  };
                };
                taggedstruct {
                  (block "POOL_BUFFER" struct {
                    uchar;
                    taggedstruct {
                      "MAX_FLX_LEN_BUF" taggedunion {
                        "FIXED" uchar;
                        "VARIABLE" uchar;
                      };
                      block "LPDU_ID" taggedstruct {
                        "FLX_SLOT_ID" taggedunion {
                          "FIXED" uint;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uint;
                          };
                        };
                        "OFFSET" taggedunion {
                          "FIXED" uchar;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uchar;
                          };
                        };
                        "CYCLE_REPETITION" taggedunion {
                          "FIXED" uchar;
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" uchar;
                          };
                        };
                        "CHANNEL" taggedunion {
                          "FIXED" enum {
                            "A" = 0,
                            "B" = 1
                          };
                          "VARIABLE" taggedstruct {
                            "INITIAL_VALUE" enum {
                              "A" = 0,
                              "B" = 1
                            };
                          };
                        };
                      };
                      block "XCP_PACKET" taggedstruct {
                        "CMD" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "RES_ERR" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "EV_SERV" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "DAQ" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                        "STIM" enum {
                          "NOT_ALLOWED" = 0,
                          "FIXED" = 1,
                          "VARIABLE_INITIALISED" = 2,
                          "VARIABLE" = 3
                        };
                      };
                    };
                  })*;
                };
              };
              taggedstruct {
                block "PROTOCOL_LAYER" struct {
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uint;
                  uchar;
                  uint;
                  enum {
                    "BYTE_ORDER_MSB_LAST" = 0,
                    "BYTE_ORDER_MSB_FIRST" = 1
                  };
                  enum {
                    "ADDRESS_GRANULARITY_BYTE" = 1,
                    "ADDRESS_GRANULARITY_WORD" = 2,
                    "ADDRESS_GRANULARITY_DWORD" = 4
                  };
                  taggedstruct {
                    ("OPTIONAL_CMD" enum {
                      "GET_COMM_MODE_INFO" = 251,
                      "GET_ID" = 250,
                      "SET_REQUEST" = 249,
                      "GET_SEED" = 248,
                      "UNLOCK" = 247,
                      "SET_MTA" = 246,
                      "UPLOAD" = 245,
                      "SHORT_UPLOAD" = 244,
                      "BUILD_CHECKSUM" = 243,
                      "TRANSPORT_LAYER_CMD" = 242,
                      "USER_CMD" = 241,
                      "DOWNLOAD" = 240,
                      "DOWNLOAD_NEXT" = 239,
                      "DOWNLOAD_MAX" = 238,
                      "SHORT_DOWNLOAD" = 237,
                      "MODIFY_BITS" = 236,
                      "SET_CAL_PAGE" = 235,
                      "GET_CAL_PAGE" = 234,
                      "GET_PAG_PROCESSOR_INFO" = 233,
                      "GET_SEGMENT_INFO" = 232,
                      "GET_PAGE_INFO" = 231,
                      "SET_SEGMENT_MODE" = 230,
                      "GET_SEGMENT_MODE" = 229,
                      "COPY_CAL_PAGE" = 228,
                      "CLEAR_DAQ_LIST" = 227,
                      "SET_DAQ_PTR" = 226,
                      "WRITE_DAQ" = 225,
                      "SET_DAQ_LIST_MODE" = 224,
                      "GET_DAQ_LIST_MODE" = 223,
                      "START_STOP_DAQ_LIST" = 222,
                      "START_STOP_SYNCH" = 221,
                      "GET_DAQ_CLOCK" = 220,
                      "READ_DAQ" = 219,
                      "GET_DAQ_PROCESSOR_INFO" = 218,
                      "GET_DAQ_RESOLUTION_INFO" = 217,
                      "GET_DAQ_LIST_INFO" = 216,
                      "GET_DAQ_EVENT_INFO" = 215,
                      "FREE_DAQ" = 214,
                      "ALLOC_DAQ" = 213,
                      "ALLOC_ODT" = 212,
                      "ALLOC_ODT_ENTRY" = 211,
                      "PROGRAM_START" = 210,
                      "PROGRAM_CLEAR" = 209,
                      "PROGRAM" = 208,
                      "PROGRAM_RESET" = 207,
                      "GET_PGM_PROCESSOR_INFO" = 206,
                      "GET_SECTOR_INFO" = 205,
                      "PROGRAM_PREPARE" = 204,
                      "PROGRAM_FORMAT" = 203,
                      "PROGRAM_NEXT" = 202,
                      "PROGRAM_MAX" = 201,
                      "PROGRAM_VERIFY" = 200,
                      "WRITE_DAQ_MULTIPLE" = 199
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                    "SEED_AND_KEY_EXTERNAL_FUNCTION" char[256];
                  };
                };
                block "SEGMENT" struct {
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  uchar;
                  taggedstruct {
                    block "CHECKSUM" struct {
                      enum {
                        "XCP_ADD_11" = 1,
                        "XCP_ADD_12" = 2,
                        "XCP_ADD_14" = 3,
                        "XCP_ADD_22" = 4,
                        "XCP_ADD_24" = 5,
                        "XCP_ADD_44" = 6,
                        "XCP_CRC_16" = 7,
                        "XCP_CRC_16_CITT" = 8,
                        "XCP_CRC_32" = 9,
                        "XCP_USER_DEFINED" = 255
                      };
                      taggedstruct {
                        "MAX_BLOCK_SIZE" ulong;
                        "EXTERNAL_FUNCTION" char[256];
                      };
                    };
                    (block "PAGE" struct {
                      uchar;
                      enum {
                        "ECU_ACCESS_NOT_ALLOWED" = 0,
                        "ECU_ACCESS_WITHOUT_XCP_ONLY" = 1,
                        "ECU_ACCESS_WITH_XCP_ONLY" = 2,
                        "ECU_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_READ_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_READ_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_READ_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_READ_ACCESS_DONT_CARE" = 3
                      };
                      enum {
                        "XCP_WRITE_ACCESS_NOT_ALLOWED" = 0,
                        "XCP_WRITE_ACCESS_WITHOUT_ECU_ONLY" = 1,
                        "XCP_WRITE_ACCESS_WITH_ECU_ONLY" = 2,
                        "XCP_WRITE_ACCESS_DONT_CARE" = 3
                      };
                      taggedstruct {
                        "INIT_SEGMENT" uchar;
                      };
                    })*;
                    (block "ADDRESS_MAPPING" struct {
                      ulong;
                      ulong;
                      ulong;
                    })*;
                    "PGM_VERIFY" ulong;
                  };
                };
                block "DAQ" struct {
                  enum {
                    "STATIC" = 0,
                    "DYNAMIC" = 1
                  };
                  uint;
                  uint;
                  uchar;
                  enum {
                    "OPTIMISATION_TYPE_DEFAULT" = 0,
                    "OPTIMISATION_TYPE_ODT_TYPE_16" = 1,
                    "OPTIMISATION_TYPE_ODT_TYPE_32" = 2,
                    "OPTIMISATION_TYPE_ODT_TYPE_64" = 3,
                    "OPTIMISATION_TYPE_ODT_TYPE_ALIGNMENT" = 4,
                    "OPTIMISATION_TYPE_MAX_ENTRY_SIZE" = 5
                  };
                  enum {
                    "ADDRESS_EXTENSION_FREE" = 0,
                    "ADDRESS_EXTENSION_ODT" = 1,
                    "ADDRESS_EXTENSION_DAQ" = 3
                  };
                  enum {
                    "IDENTIFICATION_FIELD_TYPE_ABSOLUTE" = 0,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE" = 1,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD" = 2,
                    "IDENTIFICATION_FIELD_TYPE_RELATIVE_WORD_ALIGNED" = 3
                  };
                  enum {
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE" = 1,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_WORD" = 2,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DWORD" = 4,
                    "GRANULARITY_ODT_ENTRY_SIZE_DAQ_DLONG" = 8
                  };
                  uchar;
                  enum {
                    "NO_OVERLOAD_INDICATION" = 0,
                    "OVERLOAD_INDICATION_PID" = 1,
                    "OVERLOAD_INDICATION_EVENT" = 2
                  };
                  taggedstruct {
                    "DAQ_ALTERNATING_SUPPORTED" uint;
                    "PRESCALER_SUPPORTED" ;
                    "RESUME_SUPPORTED" ;
                    "STORE_DAQ_SUPPORTED" ;
                    block "STIM" struct {
                      enum {
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE" = 1,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_WORD" = 2,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DWORD" = 4,
                        "GRANULARITY_ODT_ENTRY_SIZE_STIM_DLONG" = 8
                      };
                      uchar;
                      taggedstruct {
                        "BIT_STIM_SUPPORTED" ;
                        "MIN_ST_STIM" uchar;
                      };
                    };
                    block "TIMESTAMP_SUPPORTED" struct {
                      uint;
                      enum {
                        "NO_TIME_STAMP" = 0,
                        "SIZE_BYTE" = 1,
                        "SIZE_WORD" = 2,
                        "SIZE_DWORD" = 4
                      };
                      enum {
                        "UNIT_1NS" = 0,
                        "UNIT_10NS" = 1,
                        "UNIT_100NS" = 2,
                        "UNIT_1US" = 3,
                        "UNIT_10US" = 4,
                        "UNIT_100US" = 5,
                        "UNIT_1MS" = 6,
                        "UNIT_10MS" = 7,
                        "UNIT_100MS" = 8,
                        "UNIT_1S" = 9,
                        "UNIT_1PS" = 10,
                        "UNIT_10PS" = 11,
                        "UNIT_100PS" = 12
                      };
                      taggedstruct {
                        "TIMESTAMP_FIXED" ;
                      };
                    };
                    "PID_OFF_SUPPORTED" ;
                    (block "DAQ_LIST" struct {
                      uint;
                      taggedstruct {
                        "DAQ_LIST_TYPE" enum {
                          "DAQ" = 1,
                          "STIM" = 2,
                          "DAQ_STIM" = 3
                        };
                        "MAX_ODT" uchar;
                        "MAX_ODT_ENTRIES" uchar;
                        "FIRST_PID" uchar;
                        "EVENT_FIXED" uint;
                        block "PREDEFINED" taggedstruct {
                          (block "ODT" struct {
                            uchar;
                            taggedstruct {
                              ("ODT_ENTRY" struct {
                                uchar;
                                ulong;
                                uchar;
                                uchar;
                                uchar;
                              })*;
                            };
                          })*;
                        };
                      };
                    })*;
                    (block "EVENT" struct {
                      char[101];
                      char[9];
                      uint;
                      enum {
                        "DAQ" = 1,
                        "STIM" = 2,
                        "DAQ_STIM" = 3
                      };
                      uchar;
                      uchar;
                      uchar;
                      uchar;
                      taggedstruct {
                        "COMPLEMENTARY_BYPASS_EVENT_CHANNEL_NUMBER" uint;
                        "CONSISTENCY" enum {
                          "DAQ" = 0,
                          "EVENT" = 1
                        };
                      };
                    })*;
                  };
                };
                block "PAG" struct {
                  uchar;
                  taggedstruct {
                    "FREEZE_SUPPORTED" ;
                  };
                };
                block "PGM" struct {
                  enum {
                    "PGM_MODE_ABSOLUTE" = 1,
                    "PGM_MODE_FUNCTIONAL" = 2,
                    "PGM_MODE_ABSOLUTE_AND_FUNCTIONAL" = 3
                  };
                  uchar;
                  uchar;
                  taggedstruct {
                    (block "SECTOR" struct {
                      char[101];
                      uchar;
                      ulong;
                      ulong;
                      uchar;
                      uchar;
                      uchar;
                    })*;
                    "COMMUNICATION_MODE_SUPPORTED" taggedunion {
                      "BLOCK" taggedstruct {
                        "SLAVE" ;
                        "MASTER" struct {
                          uchar;
                          uchar;
                        };
                      };
                      "INTERLEAVED" uchar;
                    };
                  };
                };
                block "DAQ_EVENT" taggedunion {
                  "FIXED_EVENT_LIST" taggedstruct {
                    ("EVENT" uint)*;
                  };
                  "VARIABLE" taggedstruct {
                    block "AVAILABLE_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                    block "DEFAULT_EVENT_LIST" taggedstruct {
                      ("EVENT" uint)*;
                    };
                  };
                };
              };
              taggedstruct {
                "TRANSPORT_LAYER_INSTANCE" char[101];
              };
            })*;
          };
        };
      };
    /end A2ML

    /begin MOD_PAR ""
    /end MOD_PAR

    /begin MOD_COMMON ""
      BYTE_ORDER MSB_LAST
      ALIGNMENT_BYTE 1
      ALIGNMENT_WORD 1
      ALIGNMENT_LONG 4
      ALIGNMENT_FLOAT32_IEEE 4
      ALIGNMENT_FLOAT64_IEEE 8
      ALIGNMENT_INT64 8
    /end MOD_COMMON

    /begin IF_DATA XCP
      /begin PROTOCOL_LAYER
        0x0100
        0x07D0
        0x2710
        0x00
        0x00
        0x00
        0x00
        0x00
        0x08
        0x08
        BYTE_ORDER_MSB_FIRST
        ADDRESS_GRANULARITY_BYTE
        OPTIONAL_CMD ALLOC_ODT_ENTRY
        OPTIONAL_CMD ALLOC_ODT
        OPTIONAL_CMD ALLOC_DAQ
        OPTIONAL_CMD FREE_DAQ
        OPTIONAL_CMD GET_DAQ_EVENT_INFO
        OPTIONAL_CMD GET_DAQ_RESOLUTION_INFO
        OPTIONAL_CMD GET_DAQ_PROCESSOR_INFO
        OPTIONAL_CMD START_STOP_SYNCH
        OPTIONAL_CMD START_STOP_DAQ_LIST
        OPTIONAL_CMD GET_DAQ_LIST_MODE
        OPTIONAL_CMD SET_DAQ_LIST_MODE
        OPTIONAL_CMD WRITE_DAQ
        OPTIONAL_CMD SET_DAQ_PTR
        OPTIONAL_CMD CLEAR_DAQ_LIST
        OPTIONAL_CMD DOWNLOAD
        OPTIONAL_CMD SHORT_UPLOAD
        OPTIONAL_CMD UPLOAD
        OPTIONAL_CMD SET_MTA
        OPTIONAL_CMD UNLOCK
        OPTIONAL_CMD GET_SEED
        OPTIONAL_CMD GET_COMM_MODE_INFO
        SEED_AND_KEY_EXTERNAL_FUNCTION "..\SeedKey_3.skb"
      /end PROTOCOL_LAYER
      /begin DAQ
        DYNAMIC
        0x00
        0x01
        0x00
        OPTIMISATION_TYPE_DEFAULT
        ADDRESS_EXTENSION_ODT
        IDENTIFICATION_FIELD_TYPE_RELATIVE_BYTE
        GRANULARITY_ODT_ENTRY_SIZE_DAQ_BYTE
        0x04
        NO_OVERLOAD_INDICATION
        /begin STIM
          GRANULARITY_ODT_ENTRY_SIZE_STIM_BYTE
          0x01
        /end STIM
        /begin EVENT
          "LINtx"
          "LINtx"
          0x00
          DAQ
          0x00
          0x00
          0x0A
          0x00
        /end EVENT
      /end DAQ
      /begin PAG
        0x00
      /end PAG
      /begin PGM
        PGM_MODE_ABSOLUTE
        0x00
        0x00
      /end PGM
      /begin XCP_ON_CAN
        0x0101
        CAN_ID_MASTER 0x0600
        CAN_ID_SLAVE 0x0601
        BAUDRATE 0x07A120
        SAMPLE_POINT 0x4B
        SAMPLE_RATE SINGLE
        BTL_CYCLES 0x08
        SJW 0x02
        SYNC_EDGE SINGLE
      /end XCP_ON_CAN
    /end IF_DATA

    /begin IF_DATA CANAPE_ADDRESS_UPDATE
    /end IF_DATA

    /begin TYPEDEF_STRUCTURE CfgParam_TThermThreshBus_MosfetLevels ""
      0x5
      /begin STRUCTURE_COMPONENT
        MosfetNoMove Parameter_UByte
        0
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetATSAllowed Parameter_UByte
        1
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetOngoingOpen Parameter_UByte
        2
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetOnlyOpen Parameter_UByte
        3
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetTempHystersis Parameter_UByte
        4
      /end STRUCTURE_COMPONENT
    /end TYPEDEF_STRUCTURE

    /begin TYPEDEF_STRUCTURE CfgParam_TThermThreshBus_t ""
      0x6
      /begin STRUCTURE_COMPONENT
        MotorNoMove Parameter_UByte
        0
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MotorATSAllowed Parameter_UByte
        1
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MotorOngoingOpen Parameter_UByte
        2
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MotorOnlyOpen Parameter_UByte
        3
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MotorSleepInhibit Parameter_UByte
        4
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MotorTempHystersis Parameter_UByte
        5
      /end STRUCTURE_COMPONENT
    /end TYPEDEF_STRUCTURE

    /begin TYPEDEF_STRUCTURE CfgParam_hPosBus_t ""
      0xC
      /begin STRUCTURE_COMPONENT
        hHardStopCls Parameter_UWord
        0
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hSoftStopCls Parameter_UWord
        2
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hNearClsSld Parameter_UWord
        4
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hComfortMidStop Parameter_UWord
        6
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hSoftStopOpn Parameter_UWord
        8
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hHardStopOpn Parameter_UWord
        10
      /end STRUCTURE_COMPONENT
    /end TYPEDEF_STRUCTURE

    /begin TYPEDEF_CHARACTERISTIC Parameter_SByte ""
      VALUE __SByte_Value 0 NO_COMPU_METHOD -128 127
    /end TYPEDEF_CHARACTERISTIC

    /begin TYPEDEF_CHARACTERISTIC Parameter_UByte ""
      VALUE __UByte_Value 0 NO_COMPU_METHOD 0 255
    /end TYPEDEF_CHARACTERISTIC

    /begin TYPEDEF_CHARACTERISTIC Parameter_ULong ""
      VALUE __ULong_Value 0 NO_COMPU_METHOD 0 4294967295
    /end TYPEDEF_CHARACTERISTIC

    /begin TYPEDEF_CHARACTERISTIC Parameter_UWord ""
      VALUE __UWord_Value 0 NO_COMPU_METHOD 0 65535
    /end TYPEDEF_CHARACTERISTIC

    /begin TYPEDEF_STRUCTURE _unnamed_struct_ ""
      0x108
      /begin STRUCTURE_COMPONENT
        CALVerMajor Parameter_UByte
        0
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        CALVerMinor Parameter_UByte
        1
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hPosTbl CfgParam_hPosBus_t
        2
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        typeMtrDir Parameter_UByte
        14
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        typeHallDir Parameter_UByte
        15
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hMovPosErr Parameter_UByte
        16
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hStallRevDist Parameter_UByte
        17
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hRefForceStartPos Parameter_UWord
        18
        MATRIX_DIM 4
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hRefForceEndPos Parameter_UWord
        26
        MATRIX_DIM 4
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hRefForceNumEl Parameter_UByte
        34
        MATRIX_DIM 4
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        TAmbMaxLearnAdp Parameter_SByte
        38
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        TAmbMinLearnAdp Parameter_SByte
        39
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        vMaxVehSpdLearnAdp Parameter_UByte
        40
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMaxDeltaLearnAdp Parameter_UByte
        41
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        rMinMtrSpdLearnAdp Parameter_UByte
        42
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        AdpPercent Parameter_UByte
        43
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        tMaxLearn Parameter_UByte
        44
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMaxVoltClassA Parameter_UByte
        45
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMinVoltClassA Parameter_UByte
        46
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMaxVoltClassB Parameter_UByte
        47
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMinVoltClassB Parameter_UByte
        48
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMaxVoltClassC Parameter_UByte
        49
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMinVoltClassC Parameter_UByte
        50
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMaxVoltClassD Parameter_UByte
        51
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMaxVoltClassE Parameter_UByte
        52
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uFlctnDet Parameter_UByte
        53
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FTrkPosForceDir Parameter_UByte
        54
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FTrkPosForceDirLT Parameter_UByte
        55
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FTrkNegForceDir Parameter_UByte
        56
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FTrkNegForceDirLT Parameter_UByte
        57
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FMaxForceDifTrkAct Parameter_UByte
        58
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FMaxInclAbove0ForceTrkAct Parameter_UByte
        59
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FMaxInclBelow0ForceTrkAct Parameter_UByte
        60
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        rudimentaryATSclose Parameter_UWord
        62
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        baselineATS_slide_open Parameter_UByte
        64
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        mtrStartupThreshold Parameter_UByte
        65
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hMtrStartupDist Parameter_UByte
        66
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        mfgATSThreshold Parameter_UByte
        67
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        mfgIncrAdp Parameter_UByte
        68
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        vVehSpdExitMfg Parameter_UByte
        69
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        vMinVehSpdRR Parameter_UByte
        70
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        FDifMtrSpike Parameter_UByte
        71
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        RRThreshold Parameter_UWord
        72
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        tDifMtrSpike Parameter_UByte
        74
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        tRRDeactive Parameter_UByte
        75
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        lowSpdATSThreshold Parameter_UWord
        76
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        vVehSpdATSLow Parameter_UByte
        78
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        vVehSpdATSHigh Parameter_UByte
        79
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        overrideThreshold_stg1 Parameter_UByte
        80
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hSlideRevClsDist Parameter_UWord
        82
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hSlideRevOpnPos Parameter_UWord
        84
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hSlideRevOpnDist Parameter_UWord
        86
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        TThermProtThresholds CfgParam_TThermThreshBus_t
        88
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        nRPRelearnCycle Parameter_UWord
        94
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        tOverride Parameter_UByte
        96
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        tATSRevNoPos Parameter_UByte
        97
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        hDirChangeMtrStartupDist Parameter_UByte
        98
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        tEnterUnlearnModeMfg Parameter_UByte
        99
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        CALChecksum Parameter_UWord
        100
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        TThermProtThresholds_MosfetLevels CfgParam_TThermThreshBus_MosfetLevels
        102
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        overrideThreshold_stg2 Parameter_UByte
        107
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMinVoltClassD Parameter_UByte
        108
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        uMinVoltClassE Parameter_UByte
        109
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        rudimentaryATSopen Parameter_UWord
        110
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        highSpdATSThreshold_slide Parameter_UWord
        112
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        baselineATS_slide_close Parameter_UByte
        114
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        AmbTempUpperLimRun Parameter_UByte
        115
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        AmbTempLowerLimRun Parameter_UByte
        116
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        AmbTempUpperLimInit Parameter_UByte
        117
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        AmbTempLowerLimInit Parameter_UByte
        118
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetTempUpperLimInit Parameter_UByte
        119
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetTempUpperLimRun Parameter_UByte
        120
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetTempLowerLimRun Parameter_UByte
        121
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetTempLowerLimInit Parameter_UByte
        122
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MtrSpeedautoOpen Parameter_UWord
        124
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MtrSpeedautoClose Parameter_UWord
        126
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MtrSpeedmanOpen Parameter_UWord
        128
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MtrSpeedmanClose Parameter_UWord
        130
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        SoftStartPoint1Perc Parameter_UByte
        132
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        SoftStartPoint1Time Parameter_UWord
        134
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        SoftStartPoint2Perc Parameter_UByte
        136
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        SoftStartPoint2Time Parameter_UWord
        138
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        SoftStopPointPerc Parameter_UByte
        140
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        SoftStopPointTime Parameter_UWord
        142
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        SoftStartSoftStopEnable Parameter_UByte
        144
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        TightenClothDis Parameter_UWord
        146
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        PARA_NOSYNC_Stepsize Parameter_UWord
        148
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        PARA_NOAPP_Stepsize Parameter_UWord
        150
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        OtherCALData Parameter_UByte
        152
        MATRIX_DIM 85
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        PARA_NOAPP_Operation Parameter_UByte
        237
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        PARA_NOSYNC_Operation Parameter_UByte
        238
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        AmbTempSlopeLimit Parameter_UByte
        239
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        MosfetTempSlopeLimit Parameter_UByte
        240
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        BlockDet_posSoftStallThreshold Parameter_UWord
        242
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        BlockDet_thresholdValueDetection Parameter_ULong
        244
        MATRIX_DIM 2
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        PARA_NOSYNC_Steptime Parameter_UWord
        252
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        Voltage_Drop_Difference Parameter_UWord
        254
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        Voltage_Drop_Stabilization_Time Parameter_UWord
        256
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        Voltage_Drop_Time Parameter_UWord
        258
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        Voltage_Fluctuation_Stabilization_Time Parameter_UWord
        260
      /end STRUCTURE_COMPONENT
      /begin STRUCTURE_COMPONENT
        Voltage_Fluctuation_Time Parameter_UWord
        262
      /end STRUCTURE_COMPONENT
    /end TYPEDEF_STRUCTURE

    /begin CHARACTERISTIC CfgParam_CAL.AdpPercent ""
      VALUE 0x200031BB __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.AdpPercent" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.AmbTempLowerLimInit ""
      VALUE 0x20003205 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.AmbTempLowerLimInit" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.AmbTempLowerLimRun ""
      VALUE 0x20003203 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.AmbTempLowerLimRun" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.AmbTempSlopeLimit ""
      VALUE 0x20003206 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.AmbTempSlopeLimit" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.AmbTempUpperLimInit ""
      VALUE 0x20003204 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.AmbTempUpperLimInit" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.AmbTempUpperLimRun ""
      VALUE 0x20003202 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.AmbTempUpperLimRun" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.BlockDet_posSoftStallThreshold ""
      VALUE 0x20003228 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.BlockDet_posSoftStallThreshold" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.BlockDet_thresholdValueDetection ""
      VALUE 0x20003236 __UByte_Value 0 NO_COMPU_METHOD 0 4294967295
      SYMBOL_LINK "CfgParam_CAL.BlockDet_thresholdValueDetection" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.CALChecksum ""
      VALUE 0x2000323A __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.CALChecksum" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.CALVerMajor ""
      VALUE 0x20003190 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.CALVerMajor" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.CALVerMinor ""
      VALUE 0x20003191 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.CALVerMinor" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FDifMtrSpike ""
      VALUE 0x200031DC __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FDifMtrSpike" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FMaxForceDifTrkAct ""
      VALUE 0x200031CC __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FMaxForceDifTrkAct" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FMaxInclAbove0ForceTrkAct ""
      VALUE 0x200031CD __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FMaxInclAbove0ForceTrkAct" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FMaxInclBelow0ForceTrkAct ""
      VALUE 0x200031CE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FMaxInclBelow0ForceTrkAct" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FTrkNegForceDir ""
      VALUE 0x200031CA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FTrkNegForceDir" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FTrkNegForceDirLT ""
      VALUE 0x200031CB __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FTrkNegForceDirLT" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FTrkPosForceDir ""
      VALUE 0x200031C8 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FTrkPosForceDir" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.FTrkPosForceDirLT ""
      VALUE 0x200031C9 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.FTrkPosForceDirLT" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MosfetTempLowerLimInit ""
      VALUE 0x2000320A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.MosfetTempLowerLimInit" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MosfetTempLowerLimRun ""
      VALUE 0x20003208 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.MosfetTempLowerLimRun" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MosfetTempSlopeLimit ""
      VALUE 0x2000320B __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.MosfetTempSlopeLimit" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MosfetTempUpperLimInit ""
      VALUE 0x20003209 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.MosfetTempUpperLimInit" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MosfetTempUpperLimRun ""
      VALUE 0x20003207 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.MosfetTempUpperLimRun" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MtrSpeedautoClose ""
      VALUE 0x2000320E __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.MtrSpeedautoClose" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MtrSpeedautoOpen ""
      VALUE 0x2000320C __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.MtrSpeedautoOpen" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MtrSpeedmanClose ""
      VALUE 0x20003212 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.MtrSpeedmanClose" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.MtrSpeedmanOpen ""
      VALUE 0x20003210 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.MtrSpeedmanOpen" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.PARA_NOAPP_Operation ""
      VALUE 0x20003221 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.PARA_NOAPP_Operation" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.PARA_NOAPP_Stepsize ""
      VALUE 0x20003224 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.PARA_NOAPP_Stepsize" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.PARA_NOSYNC_Operation ""
      VALUE 0x20003220 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.PARA_NOSYNC_Operation" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.PARA_NOSYNC_Stepsize ""
      VALUE 0x20003222 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.PARA_NOSYNC_Stepsize" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.PARA_NOSYNC_Steptime ""
      VALUE 0x20003226 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.PARA_NOSYNC_Steptime" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.RRThreshold ""
      VALUE 0x200031DE __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.RRThreshold" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.SoftStartPoint1Perc ""
      VALUE 0x20003214 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.SoftStartPoint1Perc" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.SoftStartPoint1Time ""
      VALUE 0x20003216 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.SoftStartPoint1Time" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.SoftStartPoint2Perc ""
      VALUE 0x20003215 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.SoftStartPoint2Perc" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.SoftStartPoint2Time ""
      VALUE 0x20003218 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.SoftStartPoint2Time" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.SoftStartSoftStopEnable ""
      VALUE 0x2000321B __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.SoftStartSoftStopEnable" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.SoftStopPointPerc ""
      VALUE 0x2000321A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.SoftStopPointPerc" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.SoftStopPointTime ""
      VALUE 0x2000321C __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.SoftStopPointTime" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TAmbMaxLearnAdp ""
      VALUE 0x200031B6 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CAL.TAmbMaxLearnAdp" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TAmbMinLearnAdp ""
      VALUE 0x200031B7 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CAL.TAmbMinLearnAdp" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds ""
      VALUE 0x200031F0 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds.MotorATSAllowed ""
      VALUE 0x200031F1 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds.MotorATSAllowed" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds.MotorNoMove ""
      VALUE 0x200031F0 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds.MotorNoMove" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds.MotorOngoingOpen ""
      VALUE 0x200031F2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds.MotorOngoingOpen" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds.MotorOnlyOpen ""
      VALUE 0x200031F3 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds.MotorOnlyOpen" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds.MotorSleepInhibit ""
      VALUE 0x200031F4 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds.MotorSleepInhibit" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds.MotorTempHystersis ""
      VALUE 0x200031F5 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds.MotorTempHystersis" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds_MosfetLevels ""
      VALUE 0x200031F6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds_MosfetLevels" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetATSAllowed ""
      VALUE 0x200031F7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetATSAllowed" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetNoMove ""
      VALUE 0x200031F6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetNoMove" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetOngoingOpen ""
      VALUE 0x200031F8 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetOngoingOpen" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetOnlyOpen ""
      VALUE 0x200031F9 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetOnlyOpen" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetTempHystersis ""
      VALUE 0x200031FA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetTempHystersis" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.TightenClothDis ""
      VALUE 0x2000321E __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.TightenClothDis" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.Voltage_Drop_Difference ""
      VALUE 0x2000322A __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.Voltage_Drop_Difference" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.Voltage_Drop_Stabilization_Time ""
      VALUE 0x2000322C __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.Voltage_Drop_Stabilization_Time" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.Voltage_Drop_Time ""
      VALUE 0x2000322E __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.Voltage_Drop_Time" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.Voltage_Fluctuation_Stabilization_Time ""
      VALUE 0x20003230 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.Voltage_Fluctuation_Stabilization_Time" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.Voltage_Fluctuation_Time ""
      VALUE 0x20003232 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.Voltage_Fluctuation_Time" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.baselineATS_slide_close ""
      VALUE 0x200031D5 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.baselineATS_slide_close" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.baselineATS_slide_open ""
      VALUE 0x200031D4 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.baselineATS_slide_open" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hDirChangeMtrStartupDist ""
      VALUE 0x20003200 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.hDirChangeMtrStartupDist" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hMovPosErr ""
      VALUE 0x200031A0 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.hMovPosErr" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hMtrStartupDist ""
      VALUE 0x200031D7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.hMtrStartupDist" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hPosTbl ""
      VALUE 0x20003192 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.hPosTbl" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hPosTbl.hComfortMidStop ""
      VALUE 0x20003198 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hPosTbl.hComfortMidStop" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hPosTbl.hHardStopCls ""
      VALUE 0x20003192 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hPosTbl.hHardStopCls" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hPosTbl.hHardStopOpn ""
      VALUE 0x2000319C __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hPosTbl.hHardStopOpn" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hPosTbl.hNearClsSld ""
      VALUE 0x20003196 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hPosTbl.hNearClsSld" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hPosTbl.hSoftStopCls ""
      VALUE 0x20003194 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hPosTbl.hSoftStopCls" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hPosTbl.hSoftStopOpn ""
      VALUE 0x2000319A __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hPosTbl.hSoftStopOpn" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hRefForceEndPos ""
      VALUE 0x200031AA __UByte_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hRefForceEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hRefForceNumEl ""
      VALUE 0x200031B2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.hRefForceNumEl" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hRefForceStartPos ""
      VALUE 0x200031A2 __UByte_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hRefForceStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hSlideRevClsDist ""
      VALUE 0x200031EA __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hSlideRevClsDist" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hSlideRevOpnDist ""
      VALUE 0x200031EE __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hSlideRevOpnDist" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hSlideRevOpnPos ""
      VALUE 0x200031EC __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.hSlideRevOpnPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.hStallRevDist ""
      VALUE 0x200031A1 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.hStallRevDist" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.highSpdATSThreshold_slide ""
      VALUE 0x200031E4 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.highSpdATSThreshold_slide" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.lowSpdATSThreshold ""
      VALUE 0x200031E2 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.lowSpdATSThreshold" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.mfgATSThreshold ""
      VALUE 0x200031D8 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.mfgATSThreshold" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.mfgIncrAdp ""
      VALUE 0x200031D9 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.mfgIncrAdp" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.mtrStartupThreshold ""
      VALUE 0x200031D6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.mtrStartupThreshold" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.nRPRelearnCycle ""
      VALUE 0x200031FC __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.nRPRelearnCycle" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.overrideThreshold_stg1 ""
      VALUE 0x200031E8 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.overrideThreshold_stg1" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.overrideThreshold_stg2 ""
      VALUE 0x200031E9 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.overrideThreshold_stg2" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.rMinMtrSpdLearnAdp ""
      VALUE 0x200031BA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.rMinMtrSpdLearnAdp" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.rudimentaryATSclose ""
      VALUE 0x200031D2 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.rudimentaryATSclose" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.rudimentaryATSopen ""
      VALUE 0x200031D0 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CAL.rudimentaryATSopen" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.tATSRevNoPos ""
      VALUE 0x200031FF __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.tATSRevNoPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.tDifMtrSpike ""
      VALUE 0x200031E0 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.tDifMtrSpike" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.tEnterUnlearnModeMfg ""
      VALUE 0x20003201 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.tEnterUnlearnModeMfg" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.tMaxLearn ""
      VALUE 0x200031BC __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.tMaxLearn" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.tOverride ""
      VALUE 0x200031FE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.tOverride" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.tRRDeactive ""
      VALUE 0x200031E1 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.tRRDeactive" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.typeHallDir ""
      VALUE 0x2000319F __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.typeHallDir" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.typeMtrDir ""
      VALUE 0x2000319E __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.typeMtrDir" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uFlctnDet ""
      VALUE 0x200031C7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uFlctnDet" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMaxDeltaLearnAdp ""
      VALUE 0x200031B9 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMaxDeltaLearnAdp" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMaxVoltClassA ""
      VALUE 0x200031BD __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMaxVoltClassA" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMaxVoltClassB ""
      VALUE 0x200031BF __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMaxVoltClassB" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMaxVoltClassC ""
      VALUE 0x200031C1 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMaxVoltClassC" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMaxVoltClassD ""
      VALUE 0x200031C3 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMaxVoltClassD" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMaxVoltClassE ""
      VALUE 0x200031C5 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMaxVoltClassE" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMinVoltClassA ""
      VALUE 0x200031BE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMinVoltClassA" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMinVoltClassB ""
      VALUE 0x200031C0 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMinVoltClassB" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMinVoltClassC ""
      VALUE 0x200031C2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMinVoltClassC" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMinVoltClassD ""
      VALUE 0x200031C4 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMinVoltClassD" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.uMinVoltClassE ""
      VALUE 0x200031C6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.uMinVoltClassE" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.vMaxVehSpdLearnAdp ""
      VALUE 0x200031B8 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.vMaxVehSpdLearnAdp" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.vMinVehSpdRR ""
      VALUE 0x200031DB __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.vMinVehSpdRR" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.vVehSpdATSHigh ""
      VALUE 0x200031E7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.vVehSpdATSHigh" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.vVehSpdATSLow ""
      VALUE 0x200031E6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.vVehSpdATSLow" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CAL.vVehSpdExitMfg ""
      VALUE 0x200031DA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CAL.vVehSpdExitMfg" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.CALChecksum ""
      VALUE 0x20003306 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.CALChecksum" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.CALExtMinorVersion ""
      VALUE 0x2000323C __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.CALExtMinorVersion" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_ ""
      VALUE 0x20003246 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.TAmbMax ""
      VALUE 0x2000324B __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.TAmbMin ""
      VALUE 0x2000324A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.hEndPos ""
      VALUE 0x20003246 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.hStartPos ""
      VALUE 0x20003248 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.thresholdEndPos ""
      VALUE 0x20003250 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.thresholdStartPos ""
      VALUE 0x20003251 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.u12vBattMax ""
      VALUE 0x2000324D __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.u12vBattMin ""
      VALUE 0x2000324C __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.vVehSpdMax ""
      VALUE 0x2000324F __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._0_.vVehSpdMin ""
      VALUE 0x2000324E __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._0_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_ ""
      VALUE 0x200032BE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.TAmbMax ""
      VALUE 0x200032C3 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.TAmbMin ""
      VALUE 0x200032C2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.hEndPos ""
      VALUE 0x200032BE __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.hStartPos ""
      VALUE 0x200032C0 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.thresholdEndPos ""
      VALUE 0x200032C8 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.thresholdStartPos ""
      VALUE 0x200032C9 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.u12vBattMax ""
      VALUE 0x200032C5 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.u12vBattMin ""
      VALUE 0x200032C4 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.vVehSpdMax ""
      VALUE 0x200032C7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._10_.vVehSpdMin ""
      VALUE 0x200032C6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._10_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_ ""
      VALUE 0x200032CA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.TAmbMax ""
      VALUE 0x200032CF __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.TAmbMin ""
      VALUE 0x200032CE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.hEndPos ""
      VALUE 0x200032CA __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.hStartPos ""
      VALUE 0x200032CC __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.thresholdEndPos ""
      VALUE 0x200032D4 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.thresholdStartPos ""
      VALUE 0x200032D5 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.u12vBattMax ""
      VALUE 0x200032D1 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.u12vBattMin ""
      VALUE 0x200032D0 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.vVehSpdMax ""
      VALUE 0x200032D3 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._11_.vVehSpdMin ""
      VALUE 0x200032D2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._11_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_ ""
      VALUE 0x200032D6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.TAmbMax ""
      VALUE 0x200032DB __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.TAmbMin ""
      VALUE 0x200032DA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.hEndPos ""
      VALUE 0x200032D6 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.hStartPos ""
      VALUE 0x200032D8 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.thresholdEndPos ""
      VALUE 0x200032E0 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.thresholdStartPos ""
      VALUE 0x200032E1 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.u12vBattMax ""
      VALUE 0x200032DD __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.u12vBattMin ""
      VALUE 0x200032DC __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.vVehSpdMax ""
      VALUE 0x200032DF __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._12_.vVehSpdMin ""
      VALUE 0x200032DE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._12_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_ ""
      VALUE 0x200032E2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.TAmbMax ""
      VALUE 0x200032E7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.TAmbMin ""
      VALUE 0x200032E6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.hEndPos ""
      VALUE 0x200032E2 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.hStartPos ""
      VALUE 0x200032E4 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.thresholdEndPos ""
      VALUE 0x200032EC __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.thresholdStartPos ""
      VALUE 0x200032ED __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.u12vBattMax ""
      VALUE 0x200032E9 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.u12vBattMin ""
      VALUE 0x200032E8 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.vVehSpdMax ""
      VALUE 0x200032EB __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._13_.vVehSpdMin ""
      VALUE 0x200032EA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._13_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_ ""
      VALUE 0x200032EE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.TAmbMax ""
      VALUE 0x200032F3 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.TAmbMin ""
      VALUE 0x200032F2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.hEndPos ""
      VALUE 0x200032EE __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.hStartPos ""
      VALUE 0x200032F0 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.thresholdEndPos ""
      VALUE 0x200032F8 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.thresholdStartPos ""
      VALUE 0x200032F9 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.u12vBattMax ""
      VALUE 0x200032F5 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.u12vBattMin ""
      VALUE 0x200032F4 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.vVehSpdMax ""
      VALUE 0x200032F7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._14_.vVehSpdMin ""
      VALUE 0x200032F6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._14_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_ ""
      VALUE 0x200032FA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.TAmbMax ""
      VALUE 0x200032FF __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.TAmbMin ""
      VALUE 0x200032FE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.hEndPos ""
      VALUE 0x200032FA __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.hStartPos ""
      VALUE 0x200032FC __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.thresholdEndPos ""
      VALUE 0x20003304 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.thresholdStartPos ""
      VALUE 0x20003305 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.u12vBattMax ""
      VALUE 0x20003301 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.u12vBattMin ""
      VALUE 0x20003300 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.vVehSpdMax ""
      VALUE 0x20003303 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._15_.vVehSpdMin ""
      VALUE 0x20003302 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._15_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_ ""
      VALUE 0x20003252 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.TAmbMax ""
      VALUE 0x20003257 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.TAmbMin ""
      VALUE 0x20003256 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.hEndPos ""
      VALUE 0x20003252 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.hStartPos ""
      VALUE 0x20003254 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.thresholdEndPos ""
      VALUE 0x2000325C __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.thresholdStartPos ""
      VALUE 0x2000325D __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.u12vBattMax ""
      VALUE 0x20003259 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.u12vBattMin ""
      VALUE 0x20003258 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.vVehSpdMax ""
      VALUE 0x2000325B __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._1_.vVehSpdMin ""
      VALUE 0x2000325A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._1_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_ ""
      VALUE 0x2000325E __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.TAmbMax ""
      VALUE 0x20003263 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.TAmbMin ""
      VALUE 0x20003262 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.hEndPos ""
      VALUE 0x2000325E __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.hStartPos ""
      VALUE 0x20003260 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.thresholdEndPos ""
      VALUE 0x20003268 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.thresholdStartPos ""
      VALUE 0x20003269 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.u12vBattMax ""
      VALUE 0x20003265 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.u12vBattMin ""
      VALUE 0x20003264 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.vVehSpdMax ""
      VALUE 0x20003267 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._2_.vVehSpdMin ""
      VALUE 0x20003266 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._2_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_ ""
      VALUE 0x2000326A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.TAmbMax ""
      VALUE 0x2000326F __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.TAmbMin ""
      VALUE 0x2000326E __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.hEndPos ""
      VALUE 0x2000326A __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.hStartPos ""
      VALUE 0x2000326C __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.thresholdEndPos ""
      VALUE 0x20003274 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.thresholdStartPos ""
      VALUE 0x20003275 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.u12vBattMax ""
      VALUE 0x20003271 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.u12vBattMin ""
      VALUE 0x20003270 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.vVehSpdMax ""
      VALUE 0x20003273 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._3_.vVehSpdMin ""
      VALUE 0x20003272 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._3_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_ ""
      VALUE 0x20003276 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.TAmbMax ""
      VALUE 0x2000327B __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.TAmbMin ""
      VALUE 0x2000327A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.hEndPos ""
      VALUE 0x20003276 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.hStartPos ""
      VALUE 0x20003278 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.thresholdEndPos ""
      VALUE 0x20003280 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.thresholdStartPos ""
      VALUE 0x20003281 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.u12vBattMax ""
      VALUE 0x2000327D __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.u12vBattMin ""
      VALUE 0x2000327C __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.vVehSpdMax ""
      VALUE 0x2000327F __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._4_.vVehSpdMin ""
      VALUE 0x2000327E __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._4_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_ ""
      VALUE 0x20003282 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.TAmbMax ""
      VALUE 0x20003287 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.TAmbMin ""
      VALUE 0x20003286 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.hEndPos ""
      VALUE 0x20003282 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.hStartPos ""
      VALUE 0x20003284 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.thresholdEndPos ""
      VALUE 0x2000328C __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.thresholdStartPos ""
      VALUE 0x2000328D __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.u12vBattMax ""
      VALUE 0x20003289 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.u12vBattMin ""
      VALUE 0x20003288 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.vVehSpdMax ""
      VALUE 0x2000328B __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._5_.vVehSpdMin ""
      VALUE 0x2000328A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._5_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_ ""
      VALUE 0x2000328E __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.TAmbMax ""
      VALUE 0x20003293 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.TAmbMin ""
      VALUE 0x20003292 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.hEndPos ""
      VALUE 0x2000328E __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.hStartPos ""
      VALUE 0x20003290 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.thresholdEndPos ""
      VALUE 0x20003298 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.thresholdStartPos ""
      VALUE 0x20003299 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.u12vBattMax ""
      VALUE 0x20003295 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.u12vBattMin ""
      VALUE 0x20003294 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.vVehSpdMax ""
      VALUE 0x20003297 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._6_.vVehSpdMin ""
      VALUE 0x20003296 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._6_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_ ""
      VALUE 0x2000329A __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.TAmbMax ""
      VALUE 0x2000329F __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.TAmbMin ""
      VALUE 0x2000329E __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.hEndPos ""
      VALUE 0x2000329A __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.hStartPos ""
      VALUE 0x2000329C __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.thresholdEndPos ""
      VALUE 0x200032A4 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.thresholdStartPos ""
      VALUE 0x200032A5 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.u12vBattMax ""
      VALUE 0x200032A1 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.u12vBattMin ""
      VALUE 0x200032A0 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.vVehSpdMax ""
      VALUE 0x200032A3 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._7_.vVehSpdMin ""
      VALUE 0x200032A2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._7_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_ ""
      VALUE 0x200032A6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.TAmbMax ""
      VALUE 0x200032AB __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.TAmbMin ""
      VALUE 0x200032AA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.hEndPos ""
      VALUE 0x200032A6 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.hStartPos ""
      VALUE 0x200032A8 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.thresholdEndPos ""
      VALUE 0x200032B0 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.thresholdStartPos ""
      VALUE 0x200032B1 __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.u12vBattMax ""
      VALUE 0x200032AD __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.u12vBattMin ""
      VALUE 0x200032AC __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.vVehSpdMax ""
      VALUE 0x200032AF __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._8_.vVehSpdMin ""
      VALUE 0x200032AE __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._8_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_ ""
      VALUE 0x200032B2 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.TAmbMax ""
      VALUE 0x200032B7 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.TAmbMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.TAmbMin ""
      VALUE 0x200032B6 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.TAmbMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.hEndPos ""
      VALUE 0x200032B2 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.hEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.hStartPos ""
      VALUE 0x200032B4 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.hStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.thresholdEndPos ""
      VALUE 0x200032BC __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.thresholdEndPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.thresholdStartPos ""
      VALUE 0x200032BD __SByte_Value 0 NO_COMPU_METHOD -128 127
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.thresholdStartPos" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.u12vBattMax ""
      VALUE 0x200032B9 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.u12vBattMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.u12vBattMin ""
      VALUE 0x200032B8 __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.u12vBattMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.vVehSpdMax ""
      VALUE 0x200032BB __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.vVehSpdMax" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.adjustATS._9_.vVehSpdMin ""
      VALUE 0x200032BA __UByte_Value 0 NO_COMPU_METHOD 0 255
      SYMBOL_LINK "CfgParam_CALExt.adjustATS._9_.vVehSpdMin" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.isAdjBlockApply ""
      VALUE 0x2000323E __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.isAdjBlockApply" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.isThresholdAmbTempDep ""
      VALUE 0x20003240 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.isThresholdAmbTempDep" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.isThresholdVehSpdDep ""
      VALUE 0x20003244 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.isThresholdVehSpdDep" 0
    /end CHARACTERISTIC

    /begin CHARACTERISTIC CfgParam_CALExt.isThresholdVoltDep ""
      VALUE 0x20003242 __UWord_Value 0 NO_COMPU_METHOD 0 65535
      SYMBOL_LINK "CfgParam_CALExt.isThresholdVoltDep" 0
    /end CHARACTERISTIC

    /begin MEASUREMENT Config_isCALFileVld ""
      UBYTE NO_COMPU_METHOD 0 0 0 255
      ECU_ADDRESS 0x20004623
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "Config_isCALFileVld" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "Config_isCALFileVld" 0x20004623 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end MEASUREMENT

    /begin MEASUREMENT Config_stCALFileFlt ""
      UBYTE NO_COMPU_METHOD 0 0 0 255
      ECU_ADDRESS 0x20004626
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "Config_stCALFileFlt" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "Config_stCALFileFlt" 0x20004626 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end MEASUREMENT

    /begin CHARACTERISTIC Function_id ""
      VALUE 0x200045B2 __UWord_Value_ColumnDir 0 NO_COMPU_METHOD 0 65535
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 65535
      FORMAT "%.15"
      SYMBOL_LINK "Function_id" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "Function_id" 0x200045B2 0 0 0 1 0x8F 0
        DISPLAY 0x0 0 65535
      /end IF_DATA
    /end CHARACTERISTIC

    /begin MEASUREMENT ISOUDS_Conf.srvFrame ""
      UBYTE NO_COMPU_METHOD 0 0 0 255
      ECU_ADDRESS 0x20003493
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvFrame" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvFrame" 0x20003493 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end MEASUREMENT

    /begin CHARACTERISTIC ISOUDS_Conf.srvFrame_Copy1 ""
      VALUE 0x20003493 __UByte_Value_ColumnDir 0 NO_COMPU_METHOD 0 255
      READ_ONLY
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 255
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvFrame" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvFrame" 0x20003493 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end CHARACTERISTIC

    /begin MEASUREMENT ISOUDS_Conf.srvId ""
      UBYTE NO_COMPU_METHOD 0 0 0 255
      ECU_ADDRESS 0x20003491
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvId" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvId" 0x20003491 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end MEASUREMENT

    /begin CHARACTERISTIC ISOUDS_Conf.srvId_Copy1 ""
      VALUE 0x20003491 __UByte_Value_ColumnDir 0 NO_COMPU_METHOD 0 255
      READ_ONLY
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 255
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvId" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvId" 0x20003491 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end CHARACTERISTIC

    /begin MEASUREMENT ISOUDS_Conf.srvLen ""
      UWORD NO_COMPU_METHOD 0 0 0 65535
      ECU_ADDRESS 0x20003494
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvLen" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvLen" 0x20003494 0 0 0 1 0x8F 0
        DISPLAY 0x0 0 65535
      /end IF_DATA
    /end MEASUREMENT

    /begin CHARACTERISTIC ISOUDS_Conf.srvLen_Copy1 ""
      VALUE 0x20003494 __UWord_Value_ColumnDir 0 NO_COMPU_METHOD 0 65535
      READ_ONLY
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 65535
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvLen" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvLen" 0x20003494 0 0 0 1 0x8F 0
        DISPLAY 0x0 0 65535
      /end IF_DATA
    /end CHARACTERISTIC

    /begin MEASUREMENT ISOUDS_Conf.srvNegResp ""
      UBYTE NO_COMPU_METHOD 0 0 0 255
      ECU_ADDRESS 0x20003492
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvNegResp" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvNegResp" 0x20003492 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end MEASUREMENT

    /begin CHARACTERISTIC ISOUDS_Conf.srvNegResp_Copy1 ""
      VALUE 0x20003492 __UByte_Value_ColumnDir 0 NO_COMPU_METHOD 0 255
      READ_ONLY
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 255
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvNegResp" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvNegResp" 0x20003492 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end CHARACTERISTIC

    /begin MEASUREMENT ISOUDS_Conf.srvSt ""
      UBYTE NO_COMPU_METHOD 0 0 0 255
      ECU_ADDRESS 0x20003490
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvSt" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvSt" 0x20003490 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end MEASUREMENT

    /begin CHARACTERISTIC ISOUDS_Conf.srvSt_Copy1 ""
      VALUE 0x20003490 __UByte_Value_ColumnDir 0 NO_COMPU_METHOD 0 255
      READ_ONLY
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 255
      FORMAT "%.15"
      SYMBOL_LINK "ISOUDS_Conf.srvSt" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "ISOUDS_Conf.srvSt" 0x20003490 0 0 0 1 0x87 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end CHARACTERISTIC

    /begin MEASUREMENT MtrMdl_TEstCaseTemp ""
      SWORD NO_COMPU_METHOD 0 0 -32768 32767
      ECU_ADDRESS 0x200045B6
      SYMBOL_LINK "MtrMdl_TEstCaseTemp" 0
    /end MEASUREMENT

    /begin MEASUREMENT MtrMdl_TEstMtrTemp ""
      SWORD NO_COMPU_METHOD 0 0 -32768 32767
      ECU_ADDRESS 0x200045B8
      SYMBOL_LINK "MtrMdl_TEstMtrTemp" 0
    /end MEASUREMENT

    /begin MEASUREMENT SCUMain_B.ADCMon_TAmbTemp ""
      SWORD NO_COMPU_METHOD 0 0 -32768 32767
      ECU_ADDRESS 0x20003F4E
      SYMBOL_LINK "SCUMain_B.ADCMon_TAmbTemp" 0
    /end MEASUREMENT

    /begin MEASUREMENT SCUMain_B.ADCMon_TMOSFETTemp ""
      SWORD NO_COMPU_METHOD 0 0 -32768 32767
      ECU_ADDRESS 0x20003F50
      SYMBOL_LINK "SCUMain_B.ADCMon_TMOSFETTemp" 0
    /end MEASUREMENT

    /begin MEASUREMENT SCUMain_B.MtrCtrlBus_e.IoHwAb_SetMtrDir ""
      UBYTE NO_COMPU_METHOD 0 0 0 255
      ECU_ADDRESS 0x20003E74
      SYMBOL_LINK "SCUMain_B.MtrCtrlBus_e.IoHwAb_SetMtrDir" 0
    /end MEASUREMENT

    /begin MEASUREMENT SCUMain_B.MtrCtrlBus_e.IoHwAb_SetMtrDutyCycle ""
      SWORD NO_COMPU_METHOD 0 0 -32768 32767
      ECU_ADDRESS 0x20003E76
      SYMBOL_LINK "SCUMain_B.MtrCtrlBus_e.IoHwAb_SetMtrDutyCycle" 0
    /end MEASUREMENT

    /begin MEASUREMENT SCUMain_B.PosMon_hCurPosSigned ""
      SWORD NO_COMPU_METHOD 0 0 -32768 32767
      ECU_ADDRESS 0x20003F5E
      SYMBOL_LINK "SCUMain_B.PosMon_hCurPosSigned" 0
    /end MEASUREMENT

    /begin MEASUREMENT ScuVoltage ""
      UWORD ScuVoltage.CONVERSION 0 0 0 255
      ECU_ADDRESS 0x2000460A
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      PHYS_UNIT "V"
      SYMBOL_LINK "VoltMon_u12VBatt" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "VoltMon_u12VBatt" 0x2000460A 0 0 0 1 0x8F 0
        DISPLAY 0x0 0 255
      /end IF_DATA
    /end MEASUREMENT

    /begin CHARACTERISTIC Serial_no ""
      VALUE 0x2000368C __ULong_Value_ColumnDir 0 NO_COMPU_METHOD 0 4294967295
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 4294967295
      FORMAT "%.15"
      SYMBOL_LINK "Serial_no" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "Serial_no" 0x2000368C 0 0 0 1 0x9F 0
        DISPLAY 0x0 0 4294967295
      /end IF_DATA
    /end CHARACTERISTIC

    /begin CHARACTERISTIC Supplier_id ""
      VALUE 0x200045B0 __UWord_Value_ColumnDir 0 NO_COMPU_METHOD 0 65535
      ECU_ADDRESS_EXTENSION 0x0
      EXTENDED_LIMITS 0 65535
      FORMAT "%.15"
      SYMBOL_LINK "Supplier_id" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "Supplier_id" 0x200045B0 0 0 0 1 0x8F 0
        DISPLAY 0x0 0 65535
      /end IF_DATA
    /end CHARACTERISTIC

    /begin MEASUREMENT VoltMon_u12VBatt ""
      UWORD NO_COMPU_METHOD 0 0 0 65535
      BYTE_ORDER MSB_LAST
      ECU_ADDRESS 0x2000460A
      ECU_ADDRESS_EXTENSION 0x0
      FORMAT "%.15"
      SYMBOL_LINK "VoltMon_u12VBatt" 0
      /begin IF_DATA CANAPE_EXT
        100
        LINK_MAP "VoltMon_u12VBatt" 0x2000460A 0 0 0 1 0x8F 0
        DISPLAY 0x0 0 65535
      /end IF_DATA
    /end MEASUREMENT

    /begin COMPU_METHOD NVMMgmt_stReqSt.CONVERSION "@@@@RuleName created by CANape"
      TAB_VERB "%3.1" ""
      COMPU_TAB_REF NVMMgmt_stReqSt.CONVERSION
    /end COMPU_METHOD

    /begin COMPU_METHOD RPvalid1_modify.CONVERSION "@@@@RuleName created by CANape"
      TAB_VERB "%3.1" ""
      COMPU_TAB_REF RPvalid1_modify.CONVERSION
      STATUS_STRING_REF TrueFalse
    /end COMPU_METHOD

    /begin COMPU_METHOD ScuVoltage.CONVERSION "@@@@RuleName created by CANape"
      LINEAR "%3.1" ""
      COEFFS_LINEAR 0.001 0
    /end COMPU_METHOD

    /begin COMPU_METHOD ThermalProtectionStatus0.CONVERSION "@@@@RuleName created by CANape"
      TAB_VERB "%3.1" ""
      COMPU_TAB_REF ThermalProtectionStatus0.CONVERSION
      STATUS_STRING_REF ThermalStatus
    /end COMPU_METHOD

    /begin COMPU_METHOD ThermalProtectionStatus1.CONVERSION "@@@@RuleName created by CANape"
      TAB_VERB "%3.1" ""
      COMPU_TAB_REF ThermalProtectionStatus1.CONVERSION
      STATUS_STRING_REF ThermalStatus
    /end COMPU_METHOD

    /begin COMPU_VTAB NVMMgmt_stReqSt.CONVERSION "@@@@RuleName created by CANape" TAB_VERB 6
      0 "No request for action"
      1 "Request is queued"
      2 "Request is active"
      3 "Request is done"
      4 "Queue is Full"
      5 "Request failed"
      DEFAULT_VALUE ""
    /end COMPU_VTAB

    /begin COMPU_VTAB RPvalid1_modify.CONVERSION "@@@@RuleName created by CANape" TAB_VERB 2
      0 "False"
      1 "True"
      DEFAULT_VALUE ""
    /end COMPU_VTAB

    /begin COMPU_VTAB ThermalProtectionStatus0.CONVERSION "@@@@RuleName created by CANape" TAB_VERB 5
      0 "Sleeping allowed"
      8 "Free Moving"
      10 "OnlyClose"
      15 "NoMove"
      16 "StopMotion"
      DEFAULT_VALUE "0"
    /end COMPU_VTAB

    /begin COMPU_VTAB ThermalProtectionStatus1.CONVERSION "@@@@RuleName created by CANape" TAB_VERB 5
      0 "Sleeping"
      8 "Free"
      10 "OnlyClose"
      15 "NoMove"
      16 "StopMotion"
      DEFAULT_VALUE ""
    /end COMPU_VTAB

    /begin COMPU_VTAB ThermalStatus "" TAB_VERB 5
      0 "Sleeping Allowed"
      8 "Sleeping Inhibited"
      10 "Only Close"
      15 "No Move"
      16 "Stop Motion"
      DEFAULT_VALUE ""
    /end COMPU_VTAB

    /begin COMPU_VTAB TrueFalse "" TAB_VERB 2
      0 "False"
      1 "True"
      DEFAULT_VALUE ""
    /end COMPU_VTAB

    /begin RECORD_LAYOUT __SByte_Value
      FNC_VALUES 1 SBYTE ROW_DIR DIRECT
    /end RECORD_LAYOUT

    /begin RECORD_LAYOUT __UByte_Value
      FNC_VALUES 1 UBYTE ROW_DIR DIRECT
    /end RECORD_LAYOUT

    /begin RECORD_LAYOUT __UByte_Value_ColumnDir
      FNC_VALUES 1 UBYTE COLUMN_DIR DIRECT
    /end RECORD_LAYOUT

    /begin RECORD_LAYOUT __ULong_Value
      FNC_VALUES 1 ULONG ROW_DIR DIRECT
    /end RECORD_LAYOUT

    /begin RECORD_LAYOUT __ULong_Value_ColumnDir
      FNC_VALUES 1 ULONG COLUMN_DIR DIRECT
    /end RECORD_LAYOUT

    /begin RECORD_LAYOUT __UWord_Value
      FNC_VALUES 1 UWORD ROW_DIR DIRECT
    /end RECORD_LAYOUT

    /begin RECORD_LAYOUT __UWord_Value_ColumnDir
      FNC_VALUES 1 UWORD COLUMN_DIR DIRECT
    /end RECORD_LAYOUT

    /begin GROUP ATS ""
      ROOT
    /end GROUP

    /begin GROUP CalExt ""
      /begin REF_CHARACTERISTIC
        CfgParam_CALExt.CALChecksum CfgParam_CALExt.CALExtMinorVersion CfgParam_CALExt.adjustATS._0_ CfgParam_CALExt.adjustATS._0_.TAmbMax CfgParam_CALExt.adjustATS._0_.TAmbMin CfgParam_CALExt.adjustATS._0_.hEndPos CfgParam_CALExt.adjustATS._0_.hStartPos
        CfgParam_CALExt.adjustATS._0_.thresholdEndPos CfgParam_CALExt.adjustATS._0_.thresholdStartPos CfgParam_CALExt.adjustATS._0_.u12vBattMax CfgParam_CALExt.adjustATS._0_.u12vBattMin CfgParam_CALExt.adjustATS._0_.vVehSpdMax CfgParam_CALExt.adjustATS._0_.vVehSpdMin CfgParam_CALExt.adjustATS._10_ CfgParam_CALExt.adjustATS._10_.TAmbMax
        CfgParam_CALExt.adjustATS._10_.TAmbMin CfgParam_CALExt.adjustATS._10_.hEndPos CfgParam_CALExt.adjustATS._10_.hStartPos CfgParam_CALExt.adjustATS._10_.thresholdEndPos CfgParam_CALExt.adjustATS._10_.thresholdStartPos CfgParam_CALExt.adjustATS._10_.u12vBattMax CfgParam_CALExt.adjustATS._10_.u12vBattMin CfgParam_CALExt.adjustATS._10_.vVehSpdMax
        CfgParam_CALExt.adjustATS._10_.vVehSpdMin CfgParam_CALExt.adjustATS._11_ CfgParam_CALExt.adjustATS._11_.TAmbMax CfgParam_CALExt.adjustATS._11_.TAmbMin CfgParam_CALExt.adjustATS._11_.hEndPos CfgParam_CALExt.adjustATS._11_.hStartPos CfgParam_CALExt.adjustATS._11_.thresholdEndPos CfgParam_CALExt.adjustATS._11_.thresholdStartPos
        CfgParam_CALExt.adjustATS._11_.u12vBattMax CfgParam_CALExt.adjustATS._11_.u12vBattMin CfgParam_CALExt.adjustATS._11_.vVehSpdMax CfgParam_CALExt.adjustATS._11_.vVehSpdMin CfgParam_CALExt.adjustATS._12_ CfgParam_CALExt.adjustATS._12_.TAmbMax CfgParam_CALExt.adjustATS._12_.TAmbMin CfgParam_CALExt.adjustATS._12_.hEndPos
        CfgParam_CALExt.adjustATS._12_.hStartPos CfgParam_CALExt.adjustATS._12_.thresholdEndPos CfgParam_CALExt.adjustATS._12_.thresholdStartPos CfgParam_CALExt.adjustATS._12_.u12vBattMax CfgParam_CALExt.adjustATS._12_.u12vBattMin CfgParam_CALExt.adjustATS._12_.vVehSpdMax CfgParam_CALExt.adjustATS._12_.vVehSpdMin CfgParam_CALExt.adjustATS._13_
        CfgParam_CALExt.adjustATS._13_.TAmbMax CfgParam_CALExt.adjustATS._13_.TAmbMin CfgParam_CALExt.adjustATS._13_.hEndPos CfgParam_CALExt.adjustATS._13_.hStartPos CfgParam_CALExt.adjustATS._13_.thresholdEndPos CfgParam_CALExt.adjustATS._13_.thresholdStartPos CfgParam_CALExt.adjustATS._13_.u12vBattMax CfgParam_CALExt.adjustATS._13_.u12vBattMin
        CfgParam_CALExt.adjustATS._13_.vVehSpdMax CfgParam_CALExt.adjustATS._13_.vVehSpdMin CfgParam_CALExt.adjustATS._14_ CfgParam_CALExt.adjustATS._14_.TAmbMax CfgParam_CALExt.adjustATS._14_.TAmbMin CfgParam_CALExt.adjustATS._14_.hEndPos CfgParam_CALExt.adjustATS._14_.hStartPos CfgParam_CALExt.adjustATS._14_.thresholdEndPos
        CfgParam_CALExt.adjustATS._14_.thresholdStartPos CfgParam_CALExt.adjustATS._14_.u12vBattMax CfgParam_CALExt.adjustATS._14_.u12vBattMin CfgParam_CALExt.adjustATS._14_.vVehSpdMax CfgParam_CALExt.adjustATS._14_.vVehSpdMin CfgParam_CALExt.adjustATS._15_ CfgParam_CALExt.adjustATS._15_.TAmbMax CfgParam_CALExt.adjustATS._15_.TAmbMin
        CfgParam_CALExt.adjustATS._15_.hEndPos CfgParam_CALExt.adjustATS._15_.hStartPos CfgParam_CALExt.adjustATS._15_.thresholdEndPos CfgParam_CALExt.adjustATS._15_.thresholdStartPos CfgParam_CALExt.adjustATS._15_.u12vBattMax CfgParam_CALExt.adjustATS._15_.u12vBattMin CfgParam_CALExt.adjustATS._15_.vVehSpdMax CfgParam_CALExt.adjustATS._15_.vVehSpdMin
        CfgParam_CALExt.adjustATS._1_ CfgParam_CALExt.adjustATS._1_.TAmbMax CfgParam_CALExt.adjustATS._1_.TAmbMin CfgParam_CALExt.adjustATS._1_.hEndPos CfgParam_CALExt.adjustATS._1_.hStartPos CfgParam_CALExt.adjustATS._1_.thresholdEndPos CfgParam_CALExt.adjustATS._1_.thresholdStartPos CfgParam_CALExt.adjustATS._1_.u12vBattMax
        CfgParam_CALExt.adjustATS._1_.u12vBattMin CfgParam_CALExt.adjustATS._1_.vVehSpdMax CfgParam_CALExt.adjustATS._1_.vVehSpdMin CfgParam_CALExt.adjustATS._2_ CfgParam_CALExt.adjustATS._2_.TAmbMax CfgParam_CALExt.adjustATS._2_.TAmbMin CfgParam_CALExt.adjustATS._2_.hEndPos CfgParam_CALExt.adjustATS._2_.hStartPos
        CfgParam_CALExt.adjustATS._2_.thresholdEndPos CfgParam_CALExt.adjustATS._2_.thresholdStartPos CfgParam_CALExt.adjustATS._2_.u12vBattMax CfgParam_CALExt.adjustATS._2_.u12vBattMin CfgParam_CALExt.adjustATS._2_.vVehSpdMax CfgParam_CALExt.adjustATS._2_.vVehSpdMin CfgParam_CALExt.adjustATS._3_ CfgParam_CALExt.adjustATS._3_.TAmbMax
        CfgParam_CALExt.adjustATS._3_.TAmbMin CfgParam_CALExt.adjustATS._3_.hEndPos CfgParam_CALExt.adjustATS._3_.hStartPos CfgParam_CALExt.adjustATS._3_.thresholdEndPos CfgParam_CALExt.adjustATS._3_.thresholdStartPos CfgParam_CALExt.adjustATS._3_.u12vBattMax CfgParam_CALExt.adjustATS._3_.u12vBattMin CfgParam_CALExt.adjustATS._3_.vVehSpdMax
        CfgParam_CALExt.adjustATS._3_.vVehSpdMin CfgParam_CALExt.adjustATS._4_ CfgParam_CALExt.adjustATS._4_.TAmbMax CfgParam_CALExt.adjustATS._4_.TAmbMin CfgParam_CALExt.adjustATS._4_.hEndPos CfgParam_CALExt.adjustATS._4_.hStartPos CfgParam_CALExt.adjustATS._4_.thresholdEndPos CfgParam_CALExt.adjustATS._4_.thresholdStartPos
        CfgParam_CALExt.adjustATS._4_.u12vBattMax CfgParam_CALExt.adjustATS._4_.u12vBattMin CfgParam_CALExt.adjustATS._4_.vVehSpdMax CfgParam_CALExt.adjustATS._4_.vVehSpdMin CfgParam_CALExt.adjustATS._5_ CfgParam_CALExt.adjustATS._5_.TAmbMax CfgParam_CALExt.adjustATS._5_.TAmbMin CfgParam_CALExt.adjustATS._5_.hEndPos
        CfgParam_CALExt.adjustATS._5_.hStartPos CfgParam_CALExt.adjustATS._5_.thresholdEndPos CfgParam_CALExt.adjustATS._5_.thresholdStartPos CfgParam_CALExt.adjustATS._5_.u12vBattMax CfgParam_CALExt.adjustATS._5_.u12vBattMin CfgParam_CALExt.adjustATS._5_.vVehSpdMax CfgParam_CALExt.adjustATS._5_.vVehSpdMin CfgParam_CALExt.adjustATS._6_
        CfgParam_CALExt.adjustATS._6_.TAmbMax CfgParam_CALExt.adjustATS._6_.TAmbMin CfgParam_CALExt.adjustATS._6_.hEndPos CfgParam_CALExt.adjustATS._6_.hStartPos CfgParam_CALExt.adjustATS._6_.thresholdEndPos CfgParam_CALExt.adjustATS._6_.thresholdStartPos CfgParam_CALExt.adjustATS._6_.u12vBattMax CfgParam_CALExt.adjustATS._6_.u12vBattMin
        CfgParam_CALExt.adjustATS._6_.vVehSpdMax CfgParam_CALExt.adjustATS._6_.vVehSpdMin CfgParam_CALExt.adjustATS._7_ CfgParam_CALExt.adjustATS._7_.TAmbMax CfgParam_CALExt.adjustATS._7_.TAmbMin CfgParam_CALExt.adjustATS._7_.hEndPos CfgParam_CALExt.adjustATS._7_.hStartPos CfgParam_CALExt.adjustATS._7_.thresholdEndPos
        CfgParam_CALExt.adjustATS._7_.thresholdStartPos CfgParam_CALExt.adjustATS._7_.u12vBattMax CfgParam_CALExt.adjustATS._7_.u12vBattMin CfgParam_CALExt.adjustATS._7_.vVehSpdMax CfgParam_CALExt.adjustATS._7_.vVehSpdMin CfgParam_CALExt.adjustATS._8_ CfgParam_CALExt.adjustATS._8_.TAmbMax CfgParam_CALExt.adjustATS._8_.TAmbMin
        CfgParam_CALExt.adjustATS._8_.hEndPos CfgParam_CALExt.adjustATS._8_.hStartPos CfgParam_CALExt.adjustATS._8_.thresholdEndPos CfgParam_CALExt.adjustATS._8_.thresholdStartPos CfgParam_CALExt.adjustATS._8_.u12vBattMax CfgParam_CALExt.adjustATS._8_.u12vBattMin CfgParam_CALExt.adjustATS._8_.vVehSpdMax CfgParam_CALExt.adjustATS._8_.vVehSpdMin
        CfgParam_CALExt.adjustATS._9_ CfgParam_CALExt.adjustATS._9_.TAmbMax CfgParam_CALExt.adjustATS._9_.TAmbMin CfgParam_CALExt.adjustATS._9_.hEndPos CfgParam_CALExt.adjustATS._9_.hStartPos CfgParam_CALExt.adjustATS._9_.thresholdEndPos CfgParam_CALExt.adjustATS._9_.thresholdStartPos CfgParam_CALExt.adjustATS._9_.u12vBattMax
        CfgParam_CALExt.adjustATS._9_.u12vBattMin CfgParam_CALExt.adjustATS._9_.vVehSpdMax CfgParam_CALExt.adjustATS._9_.vVehSpdMin CfgParam_CALExt.isAdjBlockApply CfgParam_CALExt.isThresholdAmbTempDep CfgParam_CALExt.isThresholdVehSpdDep CfgParam_CALExt.isThresholdVoltDep
      /end REF_CHARACTERISTIC
    /end GROUP

    /begin GROUP Calibration ""
      /begin REF_CHARACTERISTIC
        CfgParam_CAL.AdpPercent CfgParam_CAL.AmbTempLowerLimInit CfgParam_CAL.AmbTempLowerLimRun CfgParam_CAL.AmbTempSlopeLimit CfgParam_CAL.AmbTempUpperLimInit CfgParam_CAL.AmbTempUpperLimRun CfgParam_CAL.BlockDet_posSoftStallThreshold
        CfgParam_CAL.BlockDet_thresholdValueDetection CfgParam_CAL.CALChecksum CfgParam_CAL.CALVerMajor CfgParam_CAL.CALVerMinor CfgParam_CAL.FDifMtrSpike CfgParam_CAL.FMaxForceDifTrkAct CfgParam_CAL.FMaxInclAbove0ForceTrkAct CfgParam_CAL.FMaxInclBelow0ForceTrkAct
        CfgParam_CAL.FTrkNegForceDir CfgParam_CAL.FTrkNegForceDirLT CfgParam_CAL.FTrkPosForceDir CfgParam_CAL.FTrkPosForceDirLT CfgParam_CAL.MosfetTempLowerLimInit CfgParam_CAL.MosfetTempLowerLimRun CfgParam_CAL.MosfetTempSlopeLimit CfgParam_CAL.MosfetTempUpperLimInit
        CfgParam_CAL.MosfetTempUpperLimRun CfgParam_CAL.MtrSpeedautoClose CfgParam_CAL.MtrSpeedautoOpen CfgParam_CAL.MtrSpeedmanClose CfgParam_CAL.MtrSpeedmanOpen CfgParam_CAL.PARA_NOAPP_Operation CfgParam_CAL.PARA_NOAPP_Stepsize CfgParam_CAL.PARA_NOSYNC_Operation
        CfgParam_CAL.PARA_NOSYNC_Stepsize CfgParam_CAL.PARA_NOSYNC_Steptime CfgParam_CAL.RRThreshold CfgParam_CAL.SoftStartPoint1Perc CfgParam_CAL.SoftStartPoint1Time CfgParam_CAL.SoftStartPoint2Perc CfgParam_CAL.SoftStartPoint2Time CfgParam_CAL.SoftStartSoftStopEnable
        CfgParam_CAL.SoftStopPointPerc CfgParam_CAL.SoftStopPointTime CfgParam_CAL.TAmbMaxLearnAdp CfgParam_CAL.TAmbMinLearnAdp CfgParam_CAL.TThermProtThresholds CfgParam_CAL.TThermProtThresholds.MotorATSAllowed CfgParam_CAL.TThermProtThresholds.MotorNoMove CfgParam_CAL.TThermProtThresholds.MotorOngoingOpen
        CfgParam_CAL.TThermProtThresholds.MotorOnlyOpen CfgParam_CAL.TThermProtThresholds.MotorSleepInhibit CfgParam_CAL.TThermProtThresholds.MotorTempHystersis CfgParam_CAL.TThermProtThresholds_MosfetLevels CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetATSAllowed CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetNoMove CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetOngoingOpen CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetOnlyOpen
        CfgParam_CAL.TThermProtThresholds_MosfetLevels.MosfetTempHystersis CfgParam_CAL.TightenClothDis CfgParam_CAL.Voltage_Drop_Difference CfgParam_CAL.Voltage_Drop_Stabilization_Time CfgParam_CAL.Voltage_Drop_Time CfgParam_CAL.Voltage_Fluctuation_Stabilization_Time CfgParam_CAL.Voltage_Fluctuation_Time CfgParam_CAL.baselineATS_slide_close
        CfgParam_CAL.baselineATS_slide_open CfgParam_CAL.hDirChangeMtrStartupDist CfgParam_CAL.hMovPosErr CfgParam_CAL.hMtrStartupDist CfgParam_CAL.hPosTbl CfgParam_CAL.hPosTbl.hComfortMidStop CfgParam_CAL.hPosTbl.hHardStopCls CfgParam_CAL.hPosTbl.hHardStopOpn
        CfgParam_CAL.hPosTbl.hNearClsSld CfgParam_CAL.hPosTbl.hSoftStopCls CfgParam_CAL.hPosTbl.hSoftStopOpn CfgParam_CAL.hRefForceEndPos CfgParam_CAL.hRefForceNumEl CfgParam_CAL.hRefForceStartPos CfgParam_CAL.hSlideRevClsDist CfgParam_CAL.hSlideRevOpnDist
        CfgParam_CAL.hSlideRevOpnPos CfgParam_CAL.hStallRevDist CfgParam_CAL.highSpdATSThreshold_slide CfgParam_CAL.lowSpdATSThreshold CfgParam_CAL.mfgATSThreshold CfgParam_CAL.mfgIncrAdp CfgParam_CAL.mtrStartupThreshold CfgParam_CAL.nRPRelearnCycle
        CfgParam_CAL.overrideThreshold_stg1 CfgParam_CAL.overrideThreshold_stg2 CfgParam_CAL.rMinMtrSpdLearnAdp CfgParam_CAL.rudimentaryATSclose CfgParam_CAL.rudimentaryATSopen CfgParam_CAL.tATSRevNoPos CfgParam_CAL.tDifMtrSpike CfgParam_CAL.tEnterUnlearnModeMfg
        CfgParam_CAL.tMaxLearn CfgParam_CAL.tOverride CfgParam_CAL.tRRDeactive CfgParam_CAL.typeHallDir CfgParam_CAL.typeMtrDir CfgParam_CAL.uFlctnDet CfgParam_CAL.uMaxDeltaLearnAdp CfgParam_CAL.uMaxVoltClassA
        CfgParam_CAL.uMaxVoltClassB CfgParam_CAL.uMaxVoltClassC CfgParam_CAL.uMaxVoltClassD CfgParam_CAL.uMaxVoltClassE CfgParam_CAL.uMinVoltClassA CfgParam_CAL.uMinVoltClassB CfgParam_CAL.uMinVoltClassC CfgParam_CAL.uMinVoltClassD
        CfgParam_CAL.uMinVoltClassE CfgParam_CAL.vMaxVehSpdLearnAdp CfgParam_CAL.vMinVehSpdRR CfgParam_CAL.vVehSpdATSHigh CfgParam_CAL.vVehSpdATSLow CfgParam_CAL.vVehSpdExitMfg
      /end REF_CHARACTERISTIC
      /begin SUB_GROUP
        CalExt
      /end SUB_GROUP
    /end GROUP

    /begin GROUP Digital_IO ""
      /begin SUB_GROUP
        PortJ PortM PortP PortS PortT
      /end SUB_GROUP
    /end GROUP

    /begin GROUP ElapsedTime ""
      ROOT
    /end GROUP

    /begin GROUP NVM ""
      ROOT
    /end GROUP

    /begin GROUP OperatingConditions ""
      ROOT
      /begin REF_MEASUREMENT
        MtrMdl_TEstCaseTemp MtrMdl_TEstMtrTemp SCUMain_B.ADCMon_TAmbTemp SCUMain_B.ADCMon_TMOSFETTemp SCUMain_B.MtrCtrlBus_e.IoHwAb_SetMtrDir SCUMain_B.MtrCtrlBus_e.IoHwAb_SetMtrDutyCycle SCUMain_B.PosMon_hCurPosSigned
        VoltMon_u12VBatt
      /end REF_MEASUREMENT
    /end GROUP

    /begin GROUP Override "Temporary override values"
    /end GROUP

    /begin GROUP Overwrite "Comment"
      /begin REF_CHARACTERISTIC
        ISOUDS_Conf.srvFrame_Copy1 ISOUDS_Conf.srvId_Copy1 ISOUDS_Conf.srvLen_Copy1 ISOUDS_Conf.srvNegResp_Copy1 ISOUDS_Conf.srvSt_Copy1
      /end REF_CHARACTERISTIC
    /end GROUP

    /begin GROUP PortJ "Comment"
    /end GROUP

    /begin GROUP PortM ""
    /end GROUP

    /begin GROUP PortP ""
    /end GROUP

    /begin GROUP PortS ""
    /end GROUP

    /begin GROUP PortT ""
    /end GROUP

    /begin GROUP SCU_Identification ""
      ROOT
      /begin REF_CHARACTERISTIC
        Serial_no
      /end REF_CHARACTERISTIC
    /end GROUP

    /begin GROUP SCU_Status "Comment"
      ROOT
      /begin REF_MEASUREMENT
        ScuVoltage
      /end REF_MEASUREMENT
      /begin SUB_GROUP
        Digital_IO Override
      /end SUB_GROUP
    /end GROUP

    /begin GROUP UDS ""
      ROOT
      /begin REF_MEASUREMENT
        ISOUDS_Conf.srvFrame ISOUDS_Conf.srvId ISOUDS_Conf.srvLen ISOUDS_Conf.srvNegResp ISOUDS_Conf.srvSt
      /end REF_MEASUREMENT
      /begin SUB_GROUP
        Overwrite
      /end SUB_GROUP
    /end GROUP

    /begin FUNCTION Calibration ""
      /begin LOC_MEASUREMENT
        Config_isCALFileVld Config_stCALFileFlt
      /end LOC_MEASUREMENT
    /end FUNCTION

  /end MODULE

/end PROJECT
