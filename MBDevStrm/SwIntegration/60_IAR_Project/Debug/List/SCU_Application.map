###############################################################################
#
# IAR ELF Linker V9.50.1.380/W64 for ARM                  24/Nov/2024  21:52:39
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Exe\SCU_Application.out
#    Map file     =
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\List\SCU_Application.map
#    Command line =
#        -f
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Exe\SCU_Application.out.rsp
#        (C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\adc_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ADCMon_Man.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ADCMon_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\AmbTempMon_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\AntiPinch_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\APDet_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\BlockDet_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\can_pal.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\CANMan_man.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\CfgParam_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\clock_config.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\clock_S32K1xx.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ClockMan.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ComLog_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\Config_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\const_params.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\conversion.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\crc_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\crc_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\CRCMan.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\CtrlLog_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\CtrlLog_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\DiagComMan.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\DioCtrl_Man.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\DIOCtrl_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\div_nde_s32_floor.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\div_s32_sat.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\div_u32_round.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\DTC_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\edma_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\edma_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\edma_irq.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\erm_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\erm_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ERMHwAbs.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ExtDev.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ExtDev_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\flash_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\flexcan_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\flexcan_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\flexcan_irq.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ftm_common.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ftm_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ftm_ic_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ftm_pwm_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\HALLDeco_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\HALLDeco_Mdl_ISR.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\HALLDeco_Mdl_ISR_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ICMon.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\interrupt_manager.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\intrp1d_s16s32s32u32u32n16l_n.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\IoSigIf_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\irs_lib.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_ClrDTC.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_CntrlDTCSetting.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_CommCntrl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_ECUReset.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_IoCfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_IOCtrlByID.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_LinkCntrl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RdCfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RdDaByID.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RdDTCInf.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RdMemByAddr.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RdScCfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RdScDaByID.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_ReqDwnld.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_ReqUpld.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RtnCfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_RtnCntrl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_SA.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_StrtDiagSess.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_TrnsfrDa.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_TrnsfrExit.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_TstrPrsnt.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_WrCfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_WrDaByID.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ISOUDS_WrMemByAddr.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LearnAdap_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LearnAdap_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LearnAdap_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\Lib_CycleCounter_AZtqVsyP.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\Lib_DTCDet_t33BAz08.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_cfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_common.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_common_api.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_common_proto.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_commontl_api.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_commontl_proto.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_diagnostic_service.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_irq.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_j2602_proto.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_lin21_proto.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lin_lpuart_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LINMiddleware.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LINTP.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LINTP_Event.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LINTP_NM.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LINTP_NodeSrv.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LINTP_NodeSrv_Cfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\LINTP_Srv.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpit_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpspi_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpspi_irq.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpspi_master_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpspi_shared_function.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpspi_slave_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lptmr_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lptmr_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpuart_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpuart_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\lpuart_irq.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\main.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\MOSFETTempMon_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\MtrCtrl_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\MtrCtrl_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\MtrMdl_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\MtrMdl_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\MtrMdl_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\nvmman.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\nvmman_conf.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\osif_baremetal.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\pdb_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\pdb_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_adc_config_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_can_pal_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_crc_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_erm_config_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_flash_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_flexTimer_ic_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_flexTimer_pwm_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_lin_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_linstack_config_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_lpit_config_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_lpspi_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_lptmr_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_osif_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\peripherals_pdb_config_1.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_Cfg.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_IoCtrl_Func.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_IoCtrl_Func_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_ReadDID_Func.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_RoutineCtrl_Func.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PIDDID_WriteDID_Func.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\pin_mux.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\pins_driver.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\pins_port_hw_access.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\plook_u32u16u32n16_even7c_gn.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PnlOp_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PosMon_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PosMon_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PosMon_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PWMCtrl_Man.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\PWMCtrl_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\RefField_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\RefField_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\RefForce_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\SCUInit.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\SCUMain.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\SCUMain_TaskSchedular_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\startup.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedASMs_13015082826985747244.dir\startup_S32K118.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\StateMach_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\SwitchLog_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\SysFaultReac_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\SysFltRctn_Man.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\system_S32K118.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TaskSch_Man.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ThermProt_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\ThermProt_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TheshForce_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TimerHwAbs.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TLE9560.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TLE9560_ApplLayer.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TLE9560_FuncLayer.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TLE9560_RegLayer.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\TLE9560_ServLayer.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\UsgHis_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\UsgHis_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\UsgHist_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\VehCom_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\VehCom_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\VoltMon_Exp.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\VoltMon_Mdl.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\VoltMon_Mdl_data.o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir\WDOGMan.o
#        --no_out_extension -o
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Exe\SCU_Application.out
#        --map
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\List\SCU_Application.map
#        --config
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\..\..\SwTools\S32DS_project_configuration\Project_Settings\Linker_Files\S32K118_25_flash.icf
#        --semihosting --entry __iar_program_start --vfe --text_out locale
#        --cpu=Cortex-M0+ --fpu=None) --dependencies=n
#        C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Exe\SCU_Application.out.iar_deps
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor       = *
__CPP_Runtime   = 1
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x0 { ro section .intvec };
"P1":  place in [from 0x400 to 0x40f] { section FlashConfig };
"P2":  place in [from 0x0 to 0xbf] |
                [from 0x410 to 0x3'ffff] { ro };
define block __CODE_ROM { section .textrw_init };
"P3":  place in [from 0x0 to 0xbf] |
                [from 0x410 to 0x3'ffff] { block __CODE_ROM };
define block RW { rw };
"P4":  place in [from 0x2000'00c0 to 0x2000'30bf] { block RW };
define block __CODE_RAM { section .textrw };
"P5":  place in [from 0x2000'00c0 to 0x2000'30bf] { block __CODE_RAM };
define block ZI { zi };
"P7":  place in [from 0x2000'30c0 to 0x2000'54ff] { block ZI };
define block CSTACK with size = 768, alignment = 8 { };
"P9":  place in [from 0x2000'5500 to 0x2000'57ff] { block CSTACK };
do not initialize {
   section .noinit, section .bss, section .data, section __DLIB_PERTHREAD,
   section .customSection };
initialize manually with packing = none { section .textrw };
initialize manually with packing = none { section .data };

No sections matched the following patterns:

  section .customSection    in block customSectionBlock
  section m_interrupts_ram  in "P10"


  Section              Kind         Address  Aligment      Size  Object
  -------              ----         -------  --------      ----  ------
"A0":                                                      0xc0
  .intvec                               0x0                0xc0  <Block>
    .intvec            ro code          0x0         4      0xc0  startup_S32K118.o [1]
                                     - 0xc0                0xc0

"P1":                                                      0x10
  FlashConfig          ro code        0x400         4      0x10  startup_S32K118.o [1]
                                    - 0x410                0x10

"P2-P3":                                               0x1'8e6a
  .text                ro code        0x410         4    0x188e  clock_S32K1xx.o [2]
  .text                ro code       0x1c9e         2      0xe0  I32DivModFast.o [4]
  .rodata              const         0x1d7e         2       0x2  ExtDev.o [2]
  .text                ro code       0x1d80         4       0x2  IntDivZer.o [4]
  .rodata              const         0x1d82         2       0x2  ExtDev.o [2]
  .text                ro code       0x1d84         4     0xf14  RefField_Mdl.o [2]
  .text                ro code       0x2c98         4     0x370  nvmman.o [2]
  .text                ro code       0x3008         4      0x88  div_s32_sat.o [2]
  .text                ro code       0x3090         4      0x1c  SysFltRctn_Man.o [2]
  .text                ro code       0x30ac         4      0x4e  ABImemcpy.o [4]
  .rodata              const         0x30fa         2       0x2  ExtDev.o [2]
  .text                ro code       0x30fc         4      0x3c  CRCMan.o [2]
  Veneer               ro code       0x3138         4      0x10  - Linker created -
  .text                ro code       0x3148         4     0x3e0  flash_driver.o [2]
  .text                ro code       0x3528         2      0x3e  conversion.o [2]
  .rodata              const         0x3566         2       0x2  ftm_common.o [2]
  .text                ro code       0x3568         4     0x1d8  crc_driver.o [2]
  .text                ro code       0x3740         4     0x154  interrupt_manager.o [2]
  .text                ro code       0x3894         4     0x18c  crc_hw_access.o [2]
  .text                ro code       0x3a20         4     0xe50  ftm_pwm_driver.o [2]
  .text                ro code       0x4870         4     0x1d4  ftm_hw_access.o [2]
  .text                ro code       0x4a44         4     0x78c  ftm_common.o [2]
  .text                ro code       0x51d0         4     0xce8  lin_lpuart_driver.o [2]
  .text                ro code       0x5eb8         4     0x2c0  lpuart_hw_access.o [2]
  .text                ro code       0x6178         4     0x180  osif_baremetal.o [2]
  .text                ro code       0x62f8         4     0x1b4  lpuart_driver.o [2]
  .text                ro code       0x64ac         2     0x114  lin_common.o [2]
  .text                ro code       0x65c0         4     0xb00  LINTP.o [2]
  .text                ro code       0x70c0         4     0x5cc  LINTP_NodeSrv.o [2]
  .text                ro code       0x768c         4      0x8c  LINTP_Srv.o [2]
  .text                ro code       0x7718         4      0xe8  TimerHwAbs.o [2]
  .text                ro code       0x7800         4      0xb0  LINTP_NM.o [2]
  .text                ro code       0x78b0         4     0x5f0  lin.o [2]
  .text                ro code       0x7ea0         4     0x65c  lpit_driver.o [2]
  .text                ro code       0x84fc         4      0x30  TaskSch_Man.o [2]
  .text                ro code       0x852c         4     0x6c4  lptmr_driver.o [2]
  .text                ro code       0x8bf0         2      0x7a  lin_driver.o [2]
  .rodata              const         0x8c6a         2       0x2  ftm_common.o [2]
  .text                ro code       0x8c6c         4     0x210  lin_common_api.o [2]
  .text                ro code       0x8e7c         4     0x720  lin_common_proto.o [2]
  .text                ro code       0x959c         4      0x32  I64Mul.o [4]
  .text                ro code       0x95ce         2      0x7c  I64DivMod.o [4]
  .rodata              const         0x964a         2       0x2  LearnAdap_Mdl_data.o [2]
  .text                ro code       0x964c         4      0x22  I64Shl.o [4]
  .rodata              const         0x966e         2       0x2  lin_cfg.o [2]
  .text                ro code       0x9670         4      0x36  lptmr_hw_access.o [2]
  .rodata              const         0x96a6         2       0x2  lin_cfg.o [2]
  .text                ro code       0x96a8         4     0x204  lin_lin21_proto.o [2]
  .text                ro code       0x98ac         4      0x88  lin_j2602_proto.o [2]
  .text                ro code       0x9934         4       0x2  I64DivZer.o [4]
  .rodata              const         0x9936         2       0x2  lin_lpuart_driver.o [2]
  .data_init                         0x9938               0xaf4  <Block>
    Initializer bytes  const         0x9938         4     0xaf4  <for .data-1>
  .rodata              const         0xa42c                 0x1  adc_driver.o [2]
  .text                ro code       0xa430         4     0xac4  SCUMain_TaskSchedular_Mdl.o [2]
  .text                ro code       0xaef4         4      0x68  PWMCtrl_Mdl.o [2]
  .text                ro code       0xaf5c         4     0x268  StateMach_Mdl.o [2]
  .text                ro code       0xb1c4         4     0x538  APDet_Mdl.o [2]
  .text                ro code       0xb6fc         4     0x800  MtrMdl_Mdl.o [2]
  .text                ro code       0xbefc         4     0x2a8  AmbTempMon_Mdl.o [2]
  .text                ro code       0xc1a4         4     0x540  BlockDet_Mdl.o [2]
  .text                ro code       0xc6e4         4     0x358  CfgParam_Mdl.o [2]
  .text                ro code       0xca3c         4     0x9bc  CtrlLog_Mdl.o [2]
  .text                ro code       0xd3f8         4      0x28  DiagComMan.o [2]
  .text                ro code       0xd420         4     0x368  HALLDeco_Mdl.o [2]
  .text                ro code       0xd788         4     0x204  LearnAdap_Mdl.o [2]
  .text                ro code       0xd98c         4     0x2a0  MOSFETTempMon_Mdl.o [2]
  .text                ro code       0xdc2c         4     0x684  PosMon_Mdl.o [2]
  .text                ro code       0xe2b0         4     0x3a8  SysFaultReac_Mdl.o [2]
  .text                ro code       0xe658         4     0x31c  ThermProt_Mdl.o [2]
  .text                ro code       0xe974         4     0x274  UsgHis_Mdl.o [2]
  .text                ro code       0xebe8         4     0x5ec  VehCom_Mdl.o [2]
  .text                ro code       0xf1d4         4     0x658  VoltMon_Mdl.o [2]
  .text                ro code       0xf82c         4      0x10  DIOCtrl_Mdl.o [2]
  .text                ro code       0xf83c         4     0x22c  ADCMon_Mdl.o [2]
  .text                ro code       0xfa68         4       0xc  SwitchLog_Mdl.o [2]
  .text                ro code       0xfa74         4     0x134  ComLog_Mdl.o [2]
  .text                ro code       0xfba8         4      0x14  RefForce_Mdl.o [2]
  .text                ro code       0xfbbc         4      0x14  TheshForce_Mdl.o [2]
  .text                ro code       0xfbd0         4      0xb0  PnlOp_Mdl.o [2]
  .text                ro code       0xfc80         4     0x118  MtrCtrl_Mdl.o [2]
  .text                ro code       0xfd98         4     0x3a4  PIDDID_Mdl.o [2]
  .text                ro code     0x1'013c         4      0x18  ExtDev_Mdl.o [2]
  .text                ro code     0x1'0154         4     0x47c  ExtDev.o [2]
  .text                ro code     0x1'05d0         4      0x88  PWMCtrl_Man.o [2]
  .text                ro code     0x1'0658         4      0x7c  SCUInit.o [2]
  .text                ro code     0x1'06d4         2      0x42  div_nde_s32_floor.o [2]
  .text                ro code     0x1'0716         2      0x3a  div_u32_round.o [2]
  .text                ro code     0x1'0750         4     0x284  ISOUDS.o [2]
  .text                ro code     0x1'09d4         4      0x7c  Lib_CycleCounter_AZtqVsyP.o [2]
  .text                ro code     0x1'0a50         2      0x54  LINMiddleware.o [2]
  .text                ro code     0x1'0aa4         2     0x152  Lib_DTCDet_t33BAz08.o [2]
  .text                ro code     0x1'0bf6         2      0x2e  plook_u32u16u32n16_even7c_gn.o [2]
  .text                ro code     0x1'0c24         2      0x2e  intrp1d_s16s32s32u32u32n16l_n.o [2]
  .rodata              const       0x1'0c52         2       0x2  lin_lpuart_driver.o [2]
  .text                ro code     0x1'0c54         4     0x368  ADCMon_Man.o [2]
  .text                ro code     0x1'0fbc         4     0x358  TLE9560_ApplLayer.o [2]
  .text                ro code     0x1'1314         4     0xa34  TLE9560_FuncLayer.o [2]
  .text                ro code     0x1'1d48         4      0x18  WDOGMan.o [2]
  .text                ro code     0x1'1d60         4      0x50  ClockMan.o [2]
  .text                ro code     0x1'1db0         4      0x28  ERMHwAbs.o [2]
  .text                ro code     0x1'1dd8         4      0x58  DioCtrl_Man.o [2]
  .text                ro code     0x1'1e30         4      0xa4  ICMon.o [2]
  .text                ro code     0x1'1ed4         4     0x458  ISOUDS_SA.o [2]
  .text                ro code     0x1'232c         4     0x20c  pdb_driver.o [2]
  .text                ro code     0x1'2538         4     0x6cc  adc_driver.o [2]
  .text                ro code     0x1'2c04         4      0xd8  TLE9560_ServLayer.o [2]
  .text                ro code     0x1'2cdc         4     0x164  erm_driver.o [2]
  .text                ro code     0x1'2e40         4      0x44  pins_driver.o [2]
  .text                ro code     0x1'2e84         4     0x7f0  ftm_ic_driver.o [2]
  .text                ro code     0x1'3674         4     0x2d2  pdb_hw_access.o [2]
  .rodata              const       0x1'3946         2       0x2  lpuart_driver.o [2]
  .text                ro code     0x1'3948         4      0x52  ABImemset.o [4]
  .rodata              const       0x1'399a         2       0x2  MtrCtrl_Mdl_data.o [2]
  .text                ro code     0x1'399c         4     0x81c  lpspi_master_driver.o [2]
  .text                ro code     0x1'41b8         2       0xc  erm_hw_access.o [2]
  .text                ro code     0x1'41c4         4     0x170  pins_port_hw_access.o [2]
  .text                ro code     0x1'4334         4     0x28c  lpspi_hw_access.o [2]
  .text                ro code     0x1'45c0         4     0x1ec  lpspi_shared_function.o [2]
  .text                ro code     0x1'47ac         4     0x76c  edma_driver.o [2]
  .text                ro code     0x1'4f18         4     0x1f0  lpspi_slave_driver.o [2]
  .text                ro code     0x1'5108         4     0x2c8  edma_hw_access.o [2]
  .text                ro code     0x1'53d0         4     0x964  PIDDID_ReadDID_Func.o [2]
  .text                ro code     0x1'5d34         4      0x24  irs_lib.o [2]
  .text                ro code     0x1'5d58         4     0x600  flexcan_driver.o [2]
  .text                ro code     0x1'6358         4     0x318  flexcan_hw_access.o [2]
  .text                ro code     0x1'6670         4     0x5a2  TLE9560_RegLayer.o [2]
  .rodata              const       0x1'6c12         2       0x2  peripherals_adc_config_1.o [2]
  .text                ro code     0x1'6c14         4     0x4a4  PIDDID_RoutineCtrl_Func.o [2]
  .text                ro code     0x1'70b8         4     0x458  SCUMain.o [2]
  .text                ro code     0x1'7510         4     0x11c  PIDDID_IoCtrl_Func.o [2]
  .text                ro code     0x1'762c         4     0x390  PIDDID_WriteDID_Func.o [2]
  .text                ro code     0x1'79bc         4     0x1d0  HALLDeco_Mdl_ISR.o [2]
  .text                ro code     0x1'7b8c         4     0x270  ISOUDS_RdDaByID.o [2]
  .text                ro code     0x1'7dfc         4     0x218  ISOUDS_RtnCntrl.o [2]
  .text                ro code     0x1'8014         4     0x20c  ISOUDS_WrDaByID.o [2]
  .text                ro code     0x1'8220         4     0x1b0  ISOUDS_IOCtrlByID.o [2]
  .rodata              const       0x1'83d0         4     0x190  PIDDID_Cfg.o [2]
  .text                ro code     0x1'8560         4     0x160  ISOUDS_StrtDiagSess.o [2]
  .text                ro code     0x1'86c0         4      0xd8  startup.o [2]
  .rodata              const       0x1'8798         4      0xc8  TLE9560_ApplLayer.o [2]
  .text                ro code     0x1'8860         2      0xae  ISOUDS_RdMemByAddr.o [2]
  .rodata              const       0x1'890e         2       0x2  peripherals_erm_config_1.o [2]
  .rodata              const       0x1'8910         4      0x8c  clock_S32K1xx.o [2]
  .text                ro code     0x1'899c         4      0x8c  main.o [2]
  .text                ro code     0x1'8a28         4      0x4c  system_S32K118.o [2]
  .rodata              const       0x1'8a74         4      0x78  ISOUDS.o [2]
  .rodata              const       0x1'8aec         4      0x64  TLE9560_ApplLayer.o [2]
  .text                ro code     0x1'8b50         4      0x64  ISOUDS_ECUReset.o [2]
  .rodata              const       0x1'8bb4         4      0x5c  PIDDID_Cfg.o [2]
  .text                ro code     0x1'8c10         2      0x5a  edma_irq.o [2]
  .rodata              const       0x1'8c6a         2       0x2  peripherals_lpit_config_1.o [2]
  .text                ro code     0x1'8c6c         4      0x58  startup_S32K118.o [1]
  .text                ro code     0x1'8cc4         2      0x56  ISOUDS_ClrDTC.o [2]
  .rodata              const       0x1'8d1a         2       0x2  PosMon_Mdl_data.o [2]
  .rodata              const       0x1'8d1c         4      0x54  clock_S32K1xx.o [2]
  .rodata              const       0x1'8d70         4      0x54  clock_S32K1xx.o [2]
  .rodata              const       0x1'8dc4         4      0x48  clock_S32K1xx.o [2]
  .rodata              const       0x1'8e0c         4      0x44  const_params.o [2]
  .rodata              const       0x1'8e50         4      0x40  lin_cfg.o [2]
  .text                ro code     0x1'8e90         2      0x3e  ISOUDS_TstrPrsnt.o [2]
  .text                ro code     0x1'8ece         2       0x2  startup_S32K118.o [1]
  .rodata              const       0x1'8ed0         4      0x3c  lin_cfg.o [2]
  __CODE_ROM                       0x1'8f0c                0x3c  <Block>
    Initializer bytes  const       0x1'8f0c         4      0x3c  <for __CODE_RAM-1>
  .rodata              const       0x1'8f48         4      0x30  LINTP_NodeSrv_Cfg.o [2]
  .rodata              const       0x1'8f78         4      0x24  lin_cfg.o [2]
  .rodata              const       0x1'8f9c         4      0x24  LINTP_NodeSrv_Cfg.o [2]
  .rodata              const       0x1'8fc0         4      0x24  PIDDID_Cfg.o [2]
  .rodata              const       0x1'8fe4         4      0x20  lpspi_hw_access.o [2]
  .rodata              const       0x1'9004         4      0x20  peripherals_lpspi_1.o [2]
  .text                ro code     0x1'9024         4      0x1e  cmain.o [4]
  .text                ro code     0x1'9042         2       0x4  low_level_init.o [3]
  .text                ro code     0x1'9046         2       0x8  exit.o [3]
  .text                ro code     0x1'904e         2       0x2  startup_S32K118.o [1]
  .text                ro code     0x1'9050         4       0xa  cexit.o [4]
  .text                ro code     0x1'905a         2       0x2  startup_S32K118.o [1]
  .text                ro code     0x1'905c         4      0x14  exit.o [5]
  .rodata              const       0x1'9070         4      0x1c  VoltMon_Mdl_data.o [2]
  .text                ro code     0x1'908c         4      0x1c  cstartup_M.o [4]
  .rodata              const       0x1'90a8         4      0x14  peripherals_flash_1.o [2]
  .text                ro code     0x1'90bc         2      0x14  flexcan_irq.o [2]
  .text                ro code     0x1'90d0         2      0x14  lin_irq.o [2]
  .text                ro code     0x1'90e4         2      0x14  lpspi_irq.o [2]
  .rodata              const       0x1'90f8         4      0x10  ftm_common.o [2]
  .rodata              const       0x1'9108         4      0x10  lin_cfg.o [2]
  .rodata              const       0x1'9118         4      0x10  main.o [2]
  .rodata              const       0x1'9128         4      0x10  peripherals_crc_1.o [2]
  .rodata              const       0x1'9138         4      0x10  peripherals_lptmr_1.o [2]
  .rodata              const       0x1'9148         4       0xc  clock_S32K1xx.o [2]
  .rodata              const       0x1'9154         4       0xc  clock_S32K1xx.o [2]
  .rodata              const       0x1'9160         4       0xc  peripherals_adc_config_1.o [2]
  .rodata              const       0x1'916c         4       0xc  UsgHis_Mdl_data.o [2]
  .rodata              const       0x1'9178         4       0x8  ftm_common.o [2]
  .rodata              const       0x1'9180         4       0x8  lin_lpuart_driver.o [2]
  .rodata              const       0x1'9188         4       0x8  LINTP_NodeSrv_Cfg.o [2]
  .rodata              const       0x1'9190         4       0x8  LINTP_NodeSrv_Cfg.o [2]
  .rodata              const       0x1'9198         4       0x8  lpuart_driver.o [2]
  .rodata              const       0x1'91a0         4       0x8  peripherals_erm_config_1.o [2]
  .rodata              const       0x1'91a8         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91b0         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91b8         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91c0         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91c8         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91d0         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91d8         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91e0         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91e8         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91f0         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'91f8         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'9200         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'9208         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'9210         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'9218         4       0x8  peripherals_pdb_config_1.o [2]
  .rodata              const       0x1'9220         4       0x4  adc_driver.o [2]
  .rodata              const       0x1'9224         4       0x4  clock_S32K1xx.o [2]
  .rodata              const       0x1'9228         4       0x4  const_params.o [2]
  .rodata              const       0x1'922c         4       0x4  crc_driver.o [2]
  .rodata              const       0x1'9230         4       0x4  CtrlLog_Mdl_data.o [2]
  .rodata              const       0x1'9234         4       0x4  edma_driver.o [2]
  .rodata              const       0x1'9238         4       0x4  erm_driver.o [2]
  .rodata              const       0x1'923c         4       0x4  flexcan_driver.o [2]
  .rodata              const       0x1'9240         4       0x4  ISOUDS_IoCfg.o [2]
  .rodata              const       0x1'9244         4       0x4  ISOUDS_RdCfg.o [2]
  .rodata              const       0x1'9248         4       0x4  ISOUDS_RtnCfg.o [2]
  .rodata              const       0x1'924c         4       0x4  ISOUDS_WrCfg.o [2]
  .rodata              const       0x1'9250         4       0x4  lin_cfg.o [2]
  .rodata              const       0x1'9254         4       0x4  lpit_driver.o [2]
  .rodata              const       0x1'9258         4       0x4  lpit_driver.o [2]
  .rodata              const       0x1'925c         4       0x4  lptmr_driver.o [2]
  .rodata              const       0x1'9260         4       0x4  pdb_driver.o [2]
  .rodata              const       0x1'9264         4       0x4  SCUMain.o [2]
  .rodata              const       0x1'9268         4       0x4  startup.o [2]
  .rodata              const       0x1'926c         4       0x4  VehCom_Mdl_data.o [2]
  .text                ro code     0x1'9270         2       0x2  startup_S32K118.o [1]
  .rodata              const       0x1'9272                 0x1  adc_driver.o [2]
  .rodata              const       0x1'9273                 0x1  const_params.o [2]
  .rodata              const       0x1'9274                 0x1  HALLDeco_Mdl_ISR_data.o [2]
  .rodata              const       0x1'9275                 0x1  lpit_driver.o [2]
  .rodata              const       0x1'9276                 0x1  lptmr_driver.o [2]
  .rodata              const       0x1'9277                 0x1  MtrMdl_Mdl_data.o [2]
  .rodata              const       0x1'9278                 0x1  pdb_driver.o [2]
  .rodata              const       0x1'9279                 0x1  pdb_driver.o [2]
                                 - 0x1'927a            0x1'8e6a

"P4-P5":                                                  0xb30
  RW                            0x2000'00c0               0xaf4  <Block>
    .data                       0x2000'00c0               0xaf4  <Block>
      .data-1                   0x2000'00c0         4     0xaf3  <Init block>
        .data          inited   0x2000'00c0         4      0x50  clock_config.o [2]
        .data          inited   0x2000'0110         4      0x80  clock_config.o [2]
        .data          inited   0x2000'0190         4      0x74  clock_S32K1xx.o [2]
        .data          inited   0x2000'0204         4      0xcc  Config_Exp.o [2]
        .data          inited   0x2000'02d0         4     0x11c  Config_Exp.o [2]
        .data          inited   0x2000'03ec         4       0xc  Config_Exp.o [2]
        .data          inited   0x2000'03f8         4     0x114  ExtDev.o [2]
        .data          inited   0x2000'050c         4       0x8  ICMon.o [2]
        .data          inited   0x2000'0514         4      0x10  lin_cfg.o [2]
        .data          inited   0x2000'0524         4       0x4  lin_cfg.o [2]
        .data          inited   0x2000'0528         4       0x8  lin_cfg.o [2]
        .data          inited   0x2000'0530         4       0x8  lin_irq.o [2]
        .data          inited   0x2000'0538         4       0x8  LINTP.o [2]
        .data          inited   0x2000'0540         4       0x4  LINTP.o [2]
        .data          inited   0x2000'0544         4       0x8  LINTP_Srv.o [2]
        .data          inited   0x2000'054c         4       0x8  LINTP_Srv.o [2]
        .data          inited   0x2000'0554         4       0x4  LINTP_Srv.o [2]
        .data          inited   0x2000'0558         4       0x8  lpspi_shared_function.o [2]
        .data          inited   0x2000'0560         4     0x230  nvmman_conf.o [2]
        .data          inited   0x2000'0790         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'0794         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'0798         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'079c         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07a0         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07a4         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07a8         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07ac         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07b0         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07b4         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07b8         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07bc         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07c0         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07c4         4       0x4  peripherals_adc_config_1.o [2]
        .data          inited   0x2000'07c8         4      0x14  peripherals_flexTimer_ic_1.o [2]
        .data          inited   0x2000'07dc         4       0x8  peripherals_flexTimer_ic_1.o [2]
        .data          inited   0x2000'07e4         4      0x20  peripherals_flexTimer_ic_1.o [2]
        .data          inited   0x2000'0804         4      0x14  peripherals_flexTimer_pwm_1.o [2]
        .data          inited   0x2000'0818         4      0x20  peripherals_flexTimer_pwm_1.o [2]
        .data          inited   0x2000'0838         4      0x18  peripherals_flexTimer_pwm_1.o [2]
        .data          inited   0x2000'0850         4      0x14  peripherals_lin_1.o [2]
        .data          inited   0x2000'0864         4      0x18  peripherals_lpit_config_1.o [2]
        .data          inited   0x2000'087c         4       0x8  peripherals_pdb_config_1.o [2]
        .data          inited   0x2000'0884         4     0x288  pin_mux.o [2]
        .data          inited   0x2000'0b0c         4       0x4  TLE9560.o [2]
        .data          inited   0x2000'0b10         4      0x4c  TLE9560_ApplLayer.o [2]
        .data          inited   0x2000'0b5c         4      0x4c  TLE9560_ApplLayer.o [2]
        .data          inited   0x2000'0ba8         2       0x2  lin_cfg.o [2]
        .data          inited   0x2000'0baa         2       0x2  lpspi_shared_function.o [2]
        .data          inited   0x2000'0bac         2       0x2  SCUMain.o [2]
        .data          inited   0x2000'0bae                 0x1  lin_cfg.o [2]
        .data          inited   0x2000'0baf                 0x1  lin_cfg.o [2]
        .data          inited   0x2000'0bb0                 0x1  lin_cfg.o [2]
        .data          inited   0x2000'0bb1                 0x1  osif_baremetal.o [2]
        .data          inited   0x2000'0bb2                 0x1  SCUInit.o [2]
  __CODE_RAM                    0x2000'0bb4                0x3c  <Block>
    __CODE_RAM-1                0x2000'0bb4         4      0x3c  <Init block>
      .textrw          inited   0x2000'0bb4         4      0x3c  flash_driver.o [2]
                              - 0x2000'0bf0               0xb30

"P7":                                                    0x125c
  ZI                            0x2000'30c0              0x125c  <Block>
    .bss                        0x2000'30c0              0x125c  <Block>
      .bss             uninit   0x2000'30c0         4      0x1c  ADCMon_Man.o [2]
      .bss             uninit   0x2000'30dc         4       0x4  ADCMon_Mdl.o [2]
      .bss             uninit   0x2000'30e0         4      0x18  ADCMon_Mdl.o [2]
      .bss             uninit   0x2000'30f8         4       0x4  AmbTempMon_Mdl.o [2]
      .bss             uninit   0x2000'30fc         4      0xd4  AmbTempMon_Mdl.o [2]
      .bss             uninit   0x2000'31d0         4       0x4  APDet_Mdl.o [2]
      .bss             uninit   0x2000'31d4         4      0x18  APDet_Mdl.o [2]
      .bss             uninit   0x2000'31ec         4      0x2c  APDet_Mdl.o [2]
      .bss             uninit   0x2000'3218         4       0x4  BlockDet_Mdl.o [2]
      .bss             uninit   0x2000'321c         4       0x8  BlockDet_Mdl.o [2]
      .bss             uninit   0x2000'3224         4      0x1c  BlockDet_Mdl.o [2]
      .bss             uninit   0x2000'3240         4       0x4  CfgParam_Mdl.o [2]
      .bss             uninit   0x2000'3244         4      0x14  CfgParam_Mdl.o [2]
      .bss             uninit   0x2000'3258         4       0xc  CfgParam_Mdl.o [2]
      .bss             uninit   0x2000'3264         4       0xc  clock_S32K1xx.o [2]
      .bss             uninit   0x2000'3270         4       0x4  clock_S32K1xx.o [2]
      .bss             uninit   0x2000'3274         4       0x4  clock_S32K1xx.o [2]
      .bss             uninit   0x2000'3278         4       0x4  ComLog_Mdl.o [2]
      .bss             uninit   0x2000'327c         4     0x11c  Config_Exp.o [2]
      .bss             uninit   0x2000'3398         4      0xcc  Config_Exp.o [2]
      .bss             uninit   0x2000'3464         4       0x8  Config_Exp.o [2]
      .bss             uninit   0x2000'346c         4       0xc  Config_Exp.o [2]
      .bss             uninit   0x2000'3478         4       0xc  Config_Exp.o [2]
      .bss             uninit   0x2000'3484         4       0x4  Config_Exp.o [2]
      .bss             uninit   0x2000'3488         4       0x4  CtrlLog_Mdl.o [2]
      .bss             uninit   0x2000'348c         4       0xc  CtrlLog_Mdl.o [2]
      .bss             uninit   0x2000'3498         4      0x14  CtrlLog_Mdl.o [2]
      .bss             uninit   0x2000'34ac         4       0x4  DiagComMan.o [2]
      .bss             uninit   0x2000'34b0         4       0x4  DIOCtrl_Mdl.o [2]
      .bss             uninit   0x2000'34b4         4       0x4  edma_driver.o [2]
      .bss             uninit   0x2000'34b8         4       0x4  ExtDev_Mdl.o [2]
      .bss             uninit   0x2000'34bc         4       0x4  flexcan_driver.o [2]
      .bss             uninit   0x2000'34c0         4       0x8  ftm_common.o [2]
      .bss             uninit   0x2000'34c8         4       0x4  HALLDeco_Mdl.o [2]
      .bss             uninit   0x2000'34cc         4      0x10  HALLDeco_Mdl.o [2]
      .bss             uninit   0x2000'34dc         4       0x4  HALLDeco_Mdl_ISR.o [2]
      .bss             uninit   0x2000'34e0         4       0x8  HALLDeco_Mdl_ISR.o [2]
      .bss             uninit   0x2000'34e8         4      0x1c  HALLDeco_Mdl_ISR.o [2]
      .bss             uninit   0x2000'3504         4       0x4  HALLDeco_Mdl_ISR.o [2]
      .bss             uninit   0x2000'3508         4      0x64  ICMon.o [2]
      .bss             uninit   0x2000'356c         4       0x4  interrupt_manager.o [2]
      .bss             uninit   0x2000'3570         4       0x8  ISOUDS.o [2]
      .bss             uninit   0x2000'3578         4       0x4  ISOUDS.o [2]
      .bss             uninit   0x2000'357c         4       0x4  ISOUDS.o [2]
      .bss             uninit   0x2000'3580         4       0x4  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'3584         4       0x4  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'3588         4       0x4  LearnAdap_Mdl.o [2]
      .bss             uninit   0x2000'358c         4       0x8  LearnAdap_Mdl.o [2]
      .bss             uninit   0x2000'3594         4       0x4  LearnAdap_Mdl.o [2]
      .bss             uninit   0x2000'3598         4      0x20  lin.o [2]
      .bss             uninit   0x2000'35b8         4      0x2c  lin.o [2]
      .bss             uninit   0x2000'35e4         4      0x10  lin.o [2]
      .bss             uninit   0x2000'35f4         4       0xc  lin.o [2]
      .bss             uninit   0x2000'3600         4       0x8  lin_cfg.o [2]
      .bss             uninit   0x2000'3608         4       0x4  lin_cfg.o [2]
      .bss             uninit   0x2000'360c         4       0x4  lin_cfg.o [2]
      .bss             uninit   0x2000'3610         4       0x8  lin_common_api.o [2]
      .bss             uninit   0x2000'3618         4       0x8  lin_common_api.o [2]
      .bss             uninit   0x2000'3620         4       0x8  lin_lpuart_driver.o [2]
      .bss             uninit   0x2000'3628         4       0x8  lin_lpuart_driver.o [2]
      .bss             uninit   0x2000'3630         4       0x8  lin_lpuart_driver.o [2]
      .bss             uninit   0x2000'3638         4       0x8  lin_lpuart_driver.o [2]
      .bss             uninit   0x2000'3640         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'3644         4     0x104  LINTP.o [2]
      .bss             uninit   0x2000'3748         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'374c         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'3750         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'3754         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'3758         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'375c         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'3760         4       0x4  LINTP.o [2]
      .bss             uninit   0x2000'3764         4       0x4  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'3768         4       0x8  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'3770         4       0x8  LINTP_Srv.o [2]
      .bss             uninit   0x2000'3778         4       0x8  LINTP_Srv.o [2]
      .bss             uninit   0x2000'3780         4       0x4  lpit_driver.o [2]
      .bss             uninit   0x2000'3784         4       0x8  lpspi_shared_function.o [2]
      .bss             uninit   0x2000'378c         4       0x8  lpuart_driver.o [2]
      .bss             uninit   0x2000'3794         4      0x64  main.o [2]
      .bss             uninit   0x2000'37f8         4       0x4  MOSFETTempMon_Mdl.o [2]
      .bss             uninit   0x2000'37fc         4      0xd4  MOSFETTempMon_Mdl.o [2]
      .bss             uninit   0x2000'38d0         4       0x4  MtrCtrl_Mdl.o [2]
      .bss             uninit   0x2000'38d4         4       0x4  MtrCtrl_Mdl.o [2]
      .bss             uninit   0x2000'38d8         4       0x4  MtrCtrl_Mdl.o [2]
      .bss             uninit   0x2000'38dc         4       0x4  MtrMdl_Exp.o [2]
      .bss             uninit   0x2000'38e0         4       0x4  MtrMdl_Mdl.o [2]
      .bss             uninit   0x2000'38e4         4      0x20  MtrMdl_Mdl.o [2]
      .bss             uninit   0x2000'3904         4      0x6c  MtrMdl_Mdl.o [2]
      .bss             uninit   0x2000'3970         4      0x1c  nvmman.o [2]
      .bss             uninit   0x2000'398c         4     0x12c  nvmman.o [2]
      .bss             uninit   0x2000'3ab8         4       0x4  nvmman_conf.o [2]
      .bss             uninit   0x2000'3abc         4      0xfc  nvmman_conf.o [2]
      .bss             uninit   0x2000'3bb8         4       0x4  osif_baremetal.o [2]
      .bss             uninit   0x2000'3bbc         4       0x4  peripherals_adc_config_1.o [2]
      .bss             uninit   0x2000'3bc0         4       0x4  peripherals_adc_config_1.o [2]
      .bss             uninit   0x2000'3bc4         4      0x10  peripherals_flexTimer_pwm_1.o [2]
      .bss             uninit   0x2000'3bd4         4      0x34  peripherals_lpspi_1.o [2]
      .bss             uninit   0x2000'3c08         4       0x8  peripherals_pdb_config_1.o [2]
      .bss             uninit   0x2000'3c10         4       0xc  PIDDID_Cfg.o [2]
      .bss             uninit   0x2000'3c1c         4       0x4  PIDDID_IoCtrl_Func.o [2]
      .bss             uninit   0x2000'3c20         4       0x4  PIDDID_Mdl.o [2]
      .bss             uninit   0x2000'3c24         4       0x8  PIDDID_Mdl.o [2]
      .bss             uninit   0x2000'3c2c         4      0x10  PIDDID_Mdl.o [2]
      .bss             uninit   0x2000'3c3c         4       0x4  PIDDID_ReadDID_Func.o [2]
      .bss             uninit   0x2000'3c40         4       0x4  PIDDID_RoutineCtrl_Func.o [2]
      .bss             uninit   0x2000'3c44         4       0x4  PIDDID_RoutineCtrl_Func.o [2]
      .bss             uninit   0x2000'3c48         4       0x4  PIDDID_WriteDID_Func.o [2]
      .bss             uninit   0x2000'3c4c         4       0xc  PIDDID_WriteDID_Func.o [2]
      .bss             uninit   0x2000'3c58         4       0x4  PnlOp_Mdl.o [2]
      .bss             uninit   0x2000'3c5c         4       0x4  PosMon_Exp.o [2]
      .bss             uninit   0x2000'3c60         4       0x4  PosMon_Mdl.o [2]
      .bss             uninit   0x2000'3c64         4       0xc  PosMon_Mdl.o [2]
      .bss             uninit   0x2000'3c70         4      0x10  PosMon_Mdl.o [2]
      .bss             uninit   0x2000'3c80         4      0x64  PWMCtrl_Man.o [2]
      .bss             uninit   0x2000'3ce4         4       0x4  PWMCtrl_Mdl.o [2]
      .bss             uninit   0x2000'3ce8         4       0x8  PWMCtrl_Mdl.o [2]
      .bss             uninit   0x2000'3cf0         4      0xfc  RefField_Exp.o [2]
      .bss             uninit   0x2000'3dec         4       0x4  RefField_Mdl.o [2]
      .bss             uninit   0x2000'3df0         4      0x28  RefField_Mdl.o [2]
      .bss             uninit   0x2000'3e18         4      0x1c  RefField_Mdl.o [2]
      .bss             uninit   0x2000'3e34         4       0x4  RefForce_Mdl.o [2]
      .bss             uninit   0x2000'3e38         4      0x14  SCUMain.o [2]
      .bss             uninit   0x2000'3e4c         4       0x8  SCUMain.o [2]
      .bss             uninit   0x2000'3e54         4       0x4  SCUMain.o [2]
      .bss             uninit   0x2000'3e58         4     0x228  SCUMain.o [2]
      .bss             uninit   0x2000'4080         4       0x4  SCUMain.o [2]
      .bss             uninit   0x2000'4084         4       0x4  StateMach_Mdl.o [2]
      .bss             uninit   0x2000'4088         4       0x4  StateMach_Mdl.o [2]
      .bss             uninit   0x2000'408c         4       0x8  StateMach_Mdl.o [2]
      .bss             uninit   0x2000'4094         4       0x4  SwitchLog_Mdl.o [2]
      .bss             uninit   0x2000'4098         4       0x4  SysFaultReac_Mdl.o [2]
      .bss             uninit   0x2000'409c         4       0x8  SysFaultReac_Mdl.o [2]
      .bss             uninit   0x2000'40a4         4       0x4  ThermProt_Mdl.o [2]
      .bss             uninit   0x2000'40a8         4       0x4  ThermProt_Mdl.o [2]
      .bss             uninit   0x2000'40ac         4       0x4  TheshForce_Mdl.o [2]
      .bss             uninit   0x2000'40b0         4       0x4  TimerHwAbs.o [2]
      .bss             uninit   0x2000'40b4         4       0x4  TimerHwAbs.o [2]
      .bss             uninit   0x2000'40b8         4      0x9c  TLE9560.o [2]
      .bss             uninit   0x2000'4154         4      0x18  TLE9560_ApplLayer.o [2]
      .bss             uninit   0x2000'416c         4       0x4  TLE9560_ServLayer.o [2]
      .bss             uninit   0x2000'4170         4       0x4  TLE9560_ServLayer.o [2]
      .bss             uninit   0x2000'4174         4       0x4  UsgHis_Mdl.o [2]
      .bss             uninit   0x2000'4178         4       0x4  UsgHis_Mdl.o [2]
      .bss             uninit   0x2000'417c         4       0x8  UsgHis_Mdl.o [2]
      .bss             uninit   0x2000'4184         4       0x4  UsgHist_Exp.o [2]
      .bss             uninit   0x2000'4188         4       0x4  UsgHist_Exp.o [2]
      .bss             uninit   0x2000'418c         4      0x10  UsgHist_Exp.o [2]
      .bss             uninit   0x2000'419c         4       0x4  VehCom_Mdl.o [2]
      .bss             uninit   0x2000'41a0         4       0x4  VehCom_Mdl.o [2]
      .bss             uninit   0x2000'41a4         4       0x8  VehCom_Mdl.o [2]
      .bss             uninit   0x2000'41ac         4       0x4  VoltMon_Mdl.o [2]
      .bss             uninit   0x2000'41b0         4       0x8  VoltMon_Mdl.o [2]
      .bss             uninit   0x2000'41b8         4      0xa8  VoltMon_Mdl.o [2]
      .bss             uninit   0x2000'4260         2       0x2  AmbTempMon_Mdl.o [2]
      .bss             uninit   0x2000'4262         2       0x2  AntiPinch_Exp.o [2]
      .bss             uninit   0x2000'4264         2       0x2  AntiPinch_Exp.o [2]
      .bss             uninit   0x2000'4266         2       0x2  CtrlLog_Mdl.o [2]
      .bss             uninit   0x2000'4268         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'426a         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'426c         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'426e         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'4270         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'4272         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'4274         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'4276         2       0x2  ExtDev.o [2]
      .bss             uninit   0x2000'4278         2       0x2  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'427a         2       0x2  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'427c         2       0x2  ISOUDS_WrDaByID.o [2]
      .bss             uninit   0x2000'427e         2       0x2  LearnAdap_Exp.o [2]
      .bss             uninit   0x2000'4280         2       0x2  lin.o [2]
      .bss             uninit   0x2000'4282         2       0x2  lin_cfg.o [2]
      .bss             uninit   0x2000'4284         2       0x2  lin_lpuart_driver.o [2]
      .bss             uninit   0x2000'4286         2       0x2  lin_lpuart_driver.o [2]
      .bss             uninit   0x2000'4288         2       0x2  LINTP.o [2]
      .bss             uninit   0x2000'428a         2       0x2  LINTP.o [2]
      .bss             uninit   0x2000'428c         2       0x2  LINTP.o [2]
      .bss             uninit   0x2000'428e         2       0x2  LINTP.o [2]
      .bss             uninit   0x2000'4290         2       0x2  LINTP.o [2]
      .bss             uninit   0x2000'4292         2       0x2  LINTP.o [2]
      .bss             uninit   0x2000'4294         2       0x2  LINTP_NM.o [2]
      .bss             uninit   0x2000'4296         2       0x2  LINTP_NM.o [2]
      .bss             uninit   0x2000'4298         2       0x2  LINTP_NM.o [2]
      .bss             uninit   0x2000'429a         2       0x2  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'429c         2       0x2  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'429e         2       0x2  MOSFETTempMon_Mdl.o [2]
      .bss             uninit   0x2000'42a0         2       0x2  MtrMdl_Exp.o [2]
      .bss             uninit   0x2000'42a2         2       0x2  MtrMdl_Exp.o [2]
      .bss             uninit   0x2000'42a4         2       0x2  nvmman_conf.o [2]
      .bss             uninit   0x2000'42a6         2       0x2  nvmman_conf.o [2]
      .bss             uninit   0x2000'42a8         2       0x2  nvmman_conf.o [2]
      .bss             uninit   0x2000'42aa         2       0x2  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'42ac         2       0x2  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'42ae         2       0x2  SCUMain.o [2]
      .bss             uninit   0x2000'42b0         2       0x2  SCUMain.o [2]
      .bss             uninit   0x2000'42b2         2       0x2  SCUMain.o [2]
      .bss             uninit   0x2000'42b4         2       0x2  SCUMain.o [2]
      .bss             uninit   0x2000'42b6         2       0x2  StateMach_Mdl.o [2]
      .bss             uninit   0x2000'42b8         2       0x2  SysFltRctn_Man.o [2]
      .bss             uninit   0x2000'42ba         2       0x2  TimerHwAbs.o [2]
      .bss             uninit   0x2000'42bc         2       0x2  TimerHwAbs.o [2]
      .bss             uninit   0x2000'42be         2       0x2  UsgHist_Exp.o [2]
      .bss             uninit   0x2000'42c0         2       0x2  UsgHist_Exp.o [2]
      .bss             uninit   0x2000'42c2         2       0x2  VoltMon_Exp.o [2]
      .bss             uninit   0x2000'42c4                 0x1  AntiPinch_Exp.o [2]
      .bss             uninit   0x2000'42c5                 0x1  APDet_Mdl.o [2]
      .bss             uninit   0x2000'42c6                 0x1  BlockDet_Mdl.o [2]
      .bss             uninit   0x2000'42c7                 0x1  CfgParam_Mdl.o [2]
      .bss             uninit   0x2000'42c8                 0x1  Config_Exp.o [2]
      .bss             uninit   0x2000'42c9                 0x1  Config_Exp.o [2]
      .bss             uninit   0x2000'42ca                 0x1  Config_Exp.o [2]
      .bss             uninit   0x2000'42cb                 0x1  Config_Exp.o [2]
      .bss             uninit   0x2000'42cc                 0x1  DTC_Exp.o [2]
      .bss             uninit   0x2000'42cd                 0x1  DTC_Exp.o [2]
      .bss             uninit   0x2000'42ce                 0x1  ExtDev.o [2]
      .bss             uninit   0x2000'42cf                 0x1  ExtDev.o [2]
      .bss             uninit   0x2000'42d0                 0x1  ExtDev.o [2]
      .bss             uninit   0x2000'42d1                 0x1  HALLDeco_Mdl.o [2]
      .bss             uninit   0x2000'42d2                 0x1  HALLDeco_Mdl_ISR.o [2]
      .bss             uninit   0x2000'42d3                 0x1  HALLDeco_Mdl_ISR.o [2]
      .bss             uninit   0x2000'42d4                 0x1  IoSigIf_Exp.o [2]
      .bss             uninit   0x2000'42d5                 0x1  ISOUDS.o [2]
      .bss             uninit   0x2000'42d6                 0x1  ISOUDS.o [2]
      .bss             uninit   0x2000'42d7                 0x1  ISOUDS.o [2]
      .bss             uninit   0x2000'42d8                 0x1  ISOUDS.o [2]
      .bss             uninit   0x2000'42d9                 0x1  ISOUDS.o [2]
      .bss             uninit   0x2000'42da                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42db                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42dc                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42dd                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42de                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42df                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42e0                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42e1                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42e2                 0x1  ISOUDS_SA.o [2]
      .bss             uninit   0x2000'42e3                 0x1  lin.o [2]
      .bss             uninit   0x2000'42e4                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42e5                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42e6                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42e7                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42e8                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42e9                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42ea                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42eb                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42ec                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42ed                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42ee                 0x1  LINTP.o [2]
      .bss             uninit   0x2000'42ef                 0x1  LINTP_NM.o [2]
      .bss             uninit   0x2000'42f0                 0x1  LINTP_NM.o [2]
      .bss             uninit   0x2000'42f1                 0x1  LINTP_NM.o [2]
      .bss             uninit   0x2000'42f2                 0x1  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'42f3                 0x1  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'42f4                 0x1  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'42f5                 0x1  LINTP_NodeSrv.o [2]
      .bss             uninit   0x2000'42f6                 0x1  LINTP_Srv.o [2]
      .bss             uninit   0x2000'42f7                 0x1  LINTP_Srv.o [2]
      .bss             uninit   0x2000'42f8                 0x1  main.o [2]
      .bss             uninit   0x2000'42f9                 0x1  main.o [2]
      .bss             uninit   0x2000'42fa                 0x1  MtrMdl_Exp.o [2]
      .bss             uninit   0x2000'42fb                 0x1  MtrMdl_Exp.o [2]
      .bss             uninit   0x2000'42fc                 0x1  nvmman.o [2]
      .bss             uninit   0x2000'42fd                 0x1  nvmman_conf.o [2]
      .bss             uninit   0x2000'42fe                 0x1  nvmman_conf.o [2]
      .bss             uninit   0x2000'42ff                 0x1  nvmman_conf.o [2]
      .bss             uninit   0x2000'4300                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4301                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4302                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4303                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4304                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4305                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4306                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4307                 0x1  PIDDID_Exp.o [2]
      .bss             uninit   0x2000'4308                 0x1  PIDDID_WriteDID_Func.o [2]
      .bss             uninit   0x2000'4309                 0x1  RefField_Exp.o [2]
      .bss             uninit   0x2000'430a                 0x1  RefField_Exp.o [2]
      .bss             uninit   0x2000'430b                 0x1  RefField_Exp.o [2]
      .bss             uninit   0x2000'430c                 0x1  RefField_Exp.o [2]
      .bss             uninit   0x2000'430d                 0x1  SCUMain.o [2]
      .bss             uninit   0x2000'430e                 0x1  SCUMain.o [2]
      .bss             uninit   0x2000'430f                 0x1  SCUMain.o [2]
      .bss             uninit   0x2000'4310                 0x1  SCUMain.o [2]
      .bss             uninit   0x2000'4311                 0x1  SCUMain.o [2]
      .bss             uninit   0x2000'4312                 0x1  SCUMain.o [2]
      .bss             uninit   0x2000'4313                 0x1  SysFaultReac_Mdl.o [2]
      .bss             uninit   0x2000'4314                 0x1  TaskSch_Man.o [2]
      .bss             uninit   0x2000'4315                 0x1  ThermProt_Exp.o [2]
      .bss             uninit   0x2000'4316                 0x1  ThermProt_Exp.o [2]
      .bss             uninit   0x2000'4317                 0x1  ThermProt_Mdl.o [2]
      .bss             uninit   0x2000'4318                 0x1  TLE9560_ApplLayer.o [2]
      .bss             uninit   0x2000'4319                 0x1  TLE9560_ServLayer.o [2]
      .bss             uninit   0x2000'431a                 0x1  UsgHist_Exp.o [2]
      .bss             uninit   0x2000'431b                 0x1  VoltMon_Exp.o [2]
                              - 0x2000'431c              0x125c

"P9":                                                     0x300
  CSTACK                        0x2000'5500         8     0x300  <Block>
    CSTACK             uninit   0x2000'5500               0x300  <Block tail>
                              - 0x2000'5800               0x300

Unused ranges:

         From           To      Size
         ----           --      ----
     0x1'927a     0x3'ffff  0x2'6d86
  0x2000'0bf0  0x2000'30bf    0x24d0
  0x2000'431c  0x2000'54ff    0x11e4



*******************************************************************************
*** MODULE SUMMARY
***

    Module                           ro code  rw code  ro data  rw data
    ------                           -------  -------  -------  -------
command line/config:
    -------------------------------------------------------------------
    Total:

C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedASMs_13015082826985747244.dir: [1]
    startup_S32K118.o                    304
    -------------------------------------------------------------------
    Total:                               304

C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir: [2]
    ADCMon_Man.o                         872                         28
    ADCMon_Mdl.o                         556                         28
    APDet_Mdl.o                        1'336                         73
    AmbTempMon_Mdl.o                     680                        218
    AntiPinch_Exp.o                                                   5
    BlockDet_Mdl.o                     1'344                         41
    CRCMan.o                              60
    CfgParam_Mdl.o                       856                         37
    ClockMan.o                            80
    ComLog_Mdl.o                         308                          4
    Config_Exp.o                                           500    1'028
    CtrlLog_Mdl.o                      2'492                         38
    CtrlLog_Mdl_data.o                                       4
    DIOCtrl_Mdl.o                         16                          4
    DTC_Exp.o                                                         2
    DiagComMan.o                          40                          4
    DioCtrl_Man.o                         88
    ERMHwAbs.o                            40
    ExtDev.o                           1'148               282      295
    ExtDev_Mdl.o                          24                          4
    HALLDeco_Mdl.o                       872                         21
    HALLDeco_Mdl_ISR.o                   464                         46
    HALLDeco_Mdl_ISR_data.o                                  1
    ICMon.o                              164                 8      108
    ISOUDS.o                             644               120       21
    ISOUDS_ClrDTC.o                       86
    ISOUDS_ECUReset.o                    100
    ISOUDS_IOCtrlByID.o                  432
    ISOUDS_IoCfg.o                                           4
    ISOUDS_RdCfg.o                                           4
    ISOUDS_RdDaByID.o                    624
    ISOUDS_RdMemByAddr.o                 174
    ISOUDS_RtnCfg.o                                          4
    ISOUDS_RtnCntrl.o                    536
    ISOUDS_SA.o                        1'112                         21
    ISOUDS_StrtDiagSess.o                352
    ISOUDS_TstrPrsnt.o                    62
    ISOUDS_WrCfg.o                                           4
    ISOUDS_WrDaByID.o                    524                          2
    IoSigIf_Exp.o                                                     1
    LINMiddleware.o                       84
    LINTP.o                            2'816                12      327
    LINTP_NM.o                           176                          9
    LINTP_NodeSrv.o                    1'484                         20
    LINTP_NodeSrv_Cfg.o                                    100
    LINTP_Srv.o                          140                20       38
    LearnAdap_Exp.o                                                   2
    LearnAdap_Mdl.o                      516                         16
    LearnAdap_Mdl_data.o                                     2
    Lib_CycleCounter_AZtqVsyP.o          124
    Lib_DTCDet_t33BAz08.o                338
    MOSFETTempMon_Mdl.o                  672                        218
    MtrCtrl_Mdl.o                        280                         12
    MtrCtrl_Mdl_data.o                                       2
    MtrMdl_Exp.o                                                     10
    MtrMdl_Mdl.o                       2'048                        144
    MtrMdl_Mdl_data.o                                        1
    PIDDID_Cfg.o                                           528       12
    PIDDID_Exp.o                                                     12
    PIDDID_IoCtrl_Func.o                 284                          4
    PIDDID_Mdl.o                         932                         28
    PIDDID_ReadDID_Func.o              2'404                          4
    PIDDID_RoutineCtrl_Func.o          1'188                          8
    PIDDID_WriteDID_Func.o               912                         17
    PWMCtrl_Man.o                        136                        100
    PWMCtrl_Mdl.o                        104                         12
    PnlOp_Mdl.o                          176                          4
    PosMon_Exp.o                                                      4
    PosMon_Mdl.o                       1'668                         32
    PosMon_Mdl_data.o                                        2
    RefField_Exp.o                                                  256
    RefField_Mdl.o                     3'860                         72
    RefForce_Mdl.o                        20                          4
    SCUInit.o                            124                 1        1
    SCUMain.o                          1'112                 6      604
    SCUMain_TaskSchedular_Mdl.o        2'756
    StateMach_Mdl.o                      616                         18
    SwitchLog_Mdl.o                       12                          4
    SysFaultReac_Mdl.o                   936                         13
    SysFltRctn_Man.o                      28                          2
    TLE9560.o                                                4      160
    TLE9560_ApplLayer.o                  856               452      177
    TLE9560_FuncLayer.o                2'612
    TLE9560_RegLayer.o                 1'442
    TLE9560_ServLayer.o                  216                          9
    TaskSch_Man.o                         48                          1
    ThermProt_Exp.o                                                   2
    ThermProt_Mdl.o                      796                          9
    TheshForce_Mdl.o                      20                          4
    TimerHwAbs.o                         232                         12
    UsgHis_Mdl.o                         628                         16
    UsgHis_Mdl_data.o                                       12
    UsgHist_Exp.o                                                    29
    VehCom_Mdl.o                       1'516                         16
    VehCom_Mdl_data.o                                        4
    VoltMon_Exp.o                                                     3
    VoltMon_Mdl.o                      1'624                        180
    VoltMon_Mdl_data.o                                      28
    WDOGMan.o                             24
    adc_driver.o                       1'740                 6
    clock_S32K1xx.o                    6'286               524      136
    clock_config.o                                         208      208
    const_params.o                                          73
    conversion.o                          62
    crc_driver.o                         472                 4
    crc_hw_access.o                      396
    div_nde_s32_floor.o                   66
    div_s32_sat.o                        136
    div_u32_round.o                       58
    edma_driver.o                      1'900                 4        4
    edma_hw_access.o                     712
    edma_irq.o                            90
    erm_driver.o                         356                 4
    erm_hw_access.o                       12
    flash_driver.o                       992       60       60
    flexcan_driver.o                   1'536                 4        4
    flexcan_hw_access.o                  792
    flexcan_irq.o                         20
    ftm_common.o                       1'932                28        8
    ftm_hw_access.o                      468
    ftm_ic_driver.o                    2'032
    ftm_pwm_driver.o                   3'664
    interrupt_manager.o                  340                          4
    intrp1d_s16s32s32u32u32n16l_n.o       46
    irs_lib.o                             36
    lin.o                              1'520                        107
    lin_cfg.o                                              217       51
    lin_common.o                         276
    lin_common_api.o                     528                         16
    lin_common_proto.o                 1'824
    lin_driver.o                         122
    lin_irq.o                             20                 8        8
    lin_j2602_proto.o                    136
    lin_lin21_proto.o                    516
    lin_lpuart_driver.o                3'304                12       36
    lpit_driver.o                      1'628                 9        4
    lpspi_hw_access.o                    652                32
    lpspi_irq.o                           20
    lpspi_master_driver.o              2'076
    lpspi_shared_function.o              492                10       18
    lpspi_slave_driver.o                 496
    lptmr_driver.o                     1'732                 5
    lptmr_hw_access.o                     54
    lpuart_driver.o                      436                10        8
    lpuart_hw_access.o                   704
    main.o                               140                16      102
    nvmman.o                             880                        329
    nvmman_conf.o                                          560      825
    osif_baremetal.o                     384                 1        5
    pdb_driver.o                         524                 6
    pdb_hw_access.o                      722
    peripherals_adc_config_1.o                              70       64
    peripherals_crc_1.o                                     16
    peripherals_erm_config_1.o                              10
    peripherals_flash_1.o                                   20
    peripherals_flexTimer_ic_1.o                            60       60
    peripherals_flexTimer_pwm_1.o                           76       92
    peripherals_lin_1.o                                     20       20
    peripherals_lpit_config_1.o                             26       24
    peripherals_lpspi_1.o                                   32       52
    peripherals_lptmr_1.o                                   16
    peripherals_pdb_config_1.o                             128       16
    pin_mux.o                                              648      648
    pins_driver.o                         68
    pins_port_hw_access.o                368
    plook_u32u16u32n16_even7c_gn.o        46
    startup.o                            216                 4
    system_S32K118.o                      76
    -------------------------------------------------------------------
    Total:                            96'150       60    5'032    7'503

dl6M_tln.a: [3]
    exit.o                                 8
    low_level_init.o                       4
    -------------------------------------------------------------------
    Total:                                12

rt6M_tl.a: [4]
    ABImemcpy.o                           78
    ABImemset.o                           82
    I32DivModFast.o                      224
    I64DivMod.o                          124
    I64DivZer.o                            2
    I64Mul.o                              50
    I64Shl.o                              34
    IntDivZer.o                            2
    cexit.o                               10
    cmain.o                               30
    cstartup_M.o                          28
    -------------------------------------------------------------------
    Total:                               664

shb_l.a: [5]
    exit.o                                20
    -------------------------------------------------------------------
    Total:                                20

    Gaps                                   3
    Linker created                        16                 1      768
-----------------------------------------------------------------------
    Grand Total:                      97'169       60    5'033    8'271


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address   Size  Type      Object
-----                       -------   ----  ----      ------
.bss$$Base              0x2000'30c0          --   ??  - Linker created -
.bss$$Limit             0x2000'431c          --   ??  - Linker created -
.customSection$$Base            0x0          --   ??  - Linker created -
.customSection_init$$Base
                                0x0          --   ??  - Linker created -
.customSection_init$$Limit
                                0x0          --   ??  - Linker created -
.data$$Base             0x2000'00c0          --   ??  - Linker created -
.data$$Limit            0x2000'0bb4          --   Gb  - Linker created -
.data_init$$Base             0x9938          --   ??  - Linker created -
.data_init$$Limit            0xa42c          --   ??  - Linker created -
.intvec$$Base                   0x0          --   ??  - Linker created -
.intvec$$Limit                 0xc0          --   Gb  - Linker created -
?main                      0x1'9025         Code  ??  cmain.o [4]
ADC0_IRQHandler            0x1'9271         Code  Wk  startup_S32K118.o [1]
ADCMon_Mdl                   0xf98f   0xb8  Code  ??  ADCMon_Mdl.o [2]
ADCMon_Mdl_ADCCount_to_degC_for_AmbTemp
                             0xf83d   0x2a  Code  ??  ADCMon_Mdl.o [2]
ADCMon_Mdl_ADCCount_to_degC_for_MOSFETTemp
                             0xf867   0x2a  Code  ??  ADCMon_Mdl.o [2]
ADCMon_Mdl_ADCCount_to_mV
                             0xf891   0xfe  Code  ??  ADCMon_Mdl.o [2]
ADCMon_Mdl_B            0x2000'30e0   0x18  Data  ??  ADCMon_Mdl.o [2]
ADCMon_Mdl_MdlrefDW     0x2000'30dc    0x4  Data  ??  ADCMon_Mdl.o [2]
ADCMon_Mdl_initialize        0xfa47    0x6  Code  ??  ADCMon_Mdl.o [2]
ADCMon_u16GetADCValue      0x1'0e2d   0x1c  Code  ??  ADCMon_Man.o [2]
ADCMon_vInit               0x1'0cb9  0x174  Code  ??  ADCMon_Man.o [2]
ADCMon_vStep               0x1'0e51   0xe2  Code  ??  ADCMon_Man.o [2]
ADC_DRV_AutoCalibration
                           0x1'2a99  0x15c  Code  ??  adc_driver.o [2]
ADC_DRV_ConfigChan         0x1'297d   0x72  Code  ??  adc_driver.o [2]
ADC_DRV_ConfigConverter
                           0x1'2815  0x106  Code  ??  adc_driver.o [2]
ADC_DRV_ConfigHwAverage
                           0x1'2931   0x40  Code  ??  adc_driver.o [2]
ADC_DRV_GetChanResult      0x1'2a39   0x56  Code  ??  adc_driver.o [2]
ADC_DRV_GetConvCompleteFlag
                           0x1'29ef   0x4a  Code  ??  adc_driver.o [2]
ADC_GetCalibrationActiveFlag
                           0x1'2759   0x16  Code  Lc  adc_driver.o [2]
ADC_GetClockDivide         0x1'2549    0xc  Code  Lc  adc_driver.o [2]
ADC_GetHwAverageEnableFlag
                           0x1'26fd   0x16  Code  Lc  adc_driver.o [2]
ADC_GetHwAverageMode       0x1'2737    0xc  Code  Lc  adc_driver.o [2]
ADC_GetSampleTime          0x1'2569    0x8  Code  Lc  adc_driver.o [2]
ADC_GetTriggerMode         0x1'25b1    0xe  Code  Lc  adc_driver.o [2]
ADC_SetCalibrationActiveFlag
                           0x1'276f   0x24  Code  Lc  adc_driver.o [2]
ADC_SetClockDivide         0x1'2555   0x14  Code  Lc  adc_driver.o [2]
ADC_SetContinuousConvFlag
                           0x1'26b7   0x24  Code  Lc  adc_driver.o [2]
ADC_SetDMAEnableFlag       0x1'267d   0x24  Code  Lc  adc_driver.o [2]
ADC_SetHwAverageEnableFlag
                           0x1'2713   0x24  Code  Lc  adc_driver.o [2]
ADC_SetHwAverageMode       0x1'2743   0x16  Code  Lc  adc_driver.o [2]
ADC_SetInputChannel        0x1'2793   0x7c  Code  Lc  adc_driver.o [2]
ADC_SetInputClock          0x1'259f   0x12  Code  Lc  adc_driver.o [2]
ADC_SetPretriggerSelect
                           0x1'25d7   0x54  Code  Lc  adc_driver.o [2]
ADC_SetResolution          0x1'258b   0x14  Code  Lc  adc_driver.o [2]
ADC_SetSampleTime          0x1'2571   0x1a  Code  Lc  adc_driver.o [2]
ADC_SetSupplyMonitoringEnableFlag
                           0x1'26db   0x1e  Code  Lc  adc_driver.o [2]
ADC_SetTriggerMode         0x1'25bf   0x18  Code  Lc  adc_driver.o [2]
ADC_SetTriggerSelect       0x1'262b   0x52  Code  Lc  adc_driver.o [2]
ADC_SetVoltageReference
                           0x1'26a1   0x16  Code  Lc  adc_driver.o [2]
APDet_Mdl                    0xb1e1  0x4e2  Code  ??  APDet_Mdl.o [2]
APDet_Mdl_B             0x2000'31d4   0x18  Data  ??  APDet_Mdl.o [2]
APDet_Mdl_DW            0x2000'31ec   0x2c  Data  ??  APDet_Mdl.o [2]
APDet_Mdl_Init               0xb1c5   0x16  Code  ??  APDet_Mdl.o [2]
APDet_Mdl_MdlrefDW      0x2000'31d0    0x4  Data  ??  APDet_Mdl.o [2]
APDet_Mdl_PrevZCX       0x2000'42c5    0x1  Data  ??  APDet_Mdl.o [2]
APDet_Mdl_initialize         0xb6c9    0xc  Code  ??  APDet_Mdl.o [2]
AmbTempMon_Mdl               0xbf85  0x202  Code  ??  AmbTempMon_Mdl.o [2]
AmbTempMon_Mdl_B        0x2000'4260    0x2  Data  ??  AmbTempMon_Mdl.o [2]
AmbTempMon_Mdl_DW       0x2000'30fc   0xd4  Data  ??  AmbTempMon_Mdl.o [2]
AmbTempMon_Mdl_Init          0xbf7d    0x8  Code  ??  AmbTempMon_Mdl.o [2]
AmbTempMon_Mdl_MdlrefDW
                        0x2000'30f8    0x4  Data  ??  AmbTempMon_Mdl.o [2]
AmbTempMon_Mdl_determineAmbTemp
                             0xbefd   0x80  Code  ??  AmbTempMon_Mdl.o [2]
AmbTempMon_Mdl_initialize
                             0xc187    0x6  Code  ??  AmbTempMon_Mdl.o [2]
AntiPinch_FDifference   0x2000'4262    0x2  Data  ??  AntiPinch_Exp.o [2]
AntiPinch_FTracking     0x2000'4264    0x2  Data  ??  AntiPinch_Exp.o [2]
AntiPinch_isAtsDetected
                        0x2000'42c4    0x1  Data  ??  AntiPinch_Exp.o [2]
BIT                          0x64ad    0xe  Code  Lc  lin_common.o [2]
BOOT_versionNumber      0x2000'3c10    0xc  Data  ??  PIDDID_Cfg.o [2]
BlockDet_Mdl                 0xc615   0x94  Code  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_B          0x2000'321c    0x8  Data  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_BlockFault_Determination
                             0xc2d7   0xcc  Code  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_BlockFault_Determination_Init
                             0xc2cd    0xa  Code  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_Block_Det       0xc3b9  0x250  Code  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_ConsecutiveMotorblocksDetection
                             0xc1b3  0x11a  Code  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_ConsecutiveMotorblocksDetection_Init
                             0xc1a5    0xe  Code  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_DW         0x2000'3224   0x1c  Data  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_Init            0xc60d    0x8  Code  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_MdlrefDW   0x2000'3218    0x4  Data  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_PrevZCX    0x2000'42c6    0x1  Data  ??  BlockDet_Mdl.o [2]
BlockDet_Mdl_initialize
                             0xc6a9    0xc  Code  ??  BlockDet_Mdl.o [2]
BlockDet_posSoftStallThreshold
                        0x2000'42ae    0x2  Data  ??  SCUMain.o [2]
BlockDet_tBlockSimulateTimer
                        0x2000'0bac    0x2  Data  ??  SCUMain.o [2]
BlockDet_tBlockedTime_P
                        0x2000'42b0    0x2  Data  ??  SCUMain.o [2]
BlockDet_thresholdValueDetection
                        0x2000'3e4c    0x8  Data  ??  SCUMain.o [2]
CAN0_ORed_0_31_MB_IRQHandler
                           0x1'90c7    0xa  Code  ??  flexcan_irq.o [2]
CAN0_ORed_Err_Wakeup_IRQHandler
                           0x1'90bd    0xa  Code  ??  flexcan_irq.o [2]
CLOCK_DRV_GetFreq            0x12c5   0x38  Code  ??  clock_S32K1xx.o [2]
CLOCK_DRV_Init                0xb2b   0x58  Code  ??  clock_S32K1xx.o [2]
CLOCK_SYS_CheckPCCClock
                             0x117f   0x54  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_ConfigureFIRC
                             0x1629   0x8a  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_ConfigureModulesFromScg
                             0x17e9  0x112  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_ConfigureModulesFromScg::tmpSysClk
                           0x1'9154    0xc  Data  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_ConfigureSIRC
                             0x1593   0x90  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_ConfigureSOSC
                             0x16bd   0xdc  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_ConfigureTemporarySystemClock
                             0x1799   0x4a  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_ConfigureTemporarySystemClock::tmpSysClk
                           0x1'9148    0xc  Data  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetCurrentRunMode
                             0x13c1   0x26  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetDefaultConfiguration
                              0xdcd  0x15e  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetDefaultConfiguration::peripheralClockConfig
                        0x2000'0190   0x74  Data  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetFircFreq        0x1bf1   0x22  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetFircFreq::fircFreq
                           0x1'9224    0x4  Data  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetFreq            0x1c95    0xa  Code  ??  clock_S32K1xx.o [2]
CLOCK_SYS_GetFtmOptionFreq
                             0x12fd   0x26  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetLpoFreq         0x1c13   0x64  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetPccClockFreq
                             0x11d9   0xe0  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetPeripheralClock
                             0x1323   0x9e  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetScgClkOutFreq
                             0x1511   0x36  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetScgClockFreq
                              0xf39   0xd0  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSimClkOutFreq
                             0x1469   0xa8  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSimClockFreq
                             0x1019  0x166  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSimRtcClkFreq
                             0x1547   0x4c  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSircFreq        0x1bd1   0x20  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSrcFreq         0x19a9   0x2c  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSysAsyncFreq
                             0x1b01   0xba  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSysOscFreq
                             0x1bbb   0x16  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_GetSystemClockFreq
                             0x18fb   0x98  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_SetPccConfiguration
                              0xc0d   0x78  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_SetPmcConfiguration
                              0xd97   0x30  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_SetScgConfiguration
                              0xb83   0x8a  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_SetSimConfiguration
                              0xc8d  0x10a  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_SetSystemClockConfig
                             0x19d5  0x126  Code  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_SetSystemClockConfig::maxSysClksInRUN
                           0x1'8d70   0x54  Data  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_SetSystemClockConfig::maxSysClksInVLPR
                           0x1'8d1c   0x54  Data  Lc  clock_S32K1xx.o [2]
CLOCK_SYS_TransitionSystemClock
                             0x13e7   0x78  Code  Lc  clock_S32K1xx.o [2]
CMP0_IRQHandler            0x1'9271         Code  Wk  startup_S32K118.o [1]
CRCMan_CalculateCRC          0x3111   0x24  Code  ??  CRCMan.o [2]
CRCMan_enInit                0x30fd   0x14  Code  ??  CRCMan.o [2]
CRC_DRV_Configure            0x36cb   0x62  Code  ??  crc_driver.o [2]
CRC_DRV_GetCrc16             0x3623   0x42  Code  ??  crc_driver.o [2]
CRC_DRV_GetCrcResult         0x36a9   0x22  Code  ??  crc_driver.o [2]
CRC_DRV_Init                 0x35e3   0x40  Code  ??  crc_driver.o [2]
CRC_DRV_WriteData            0x3665   0x44  Code  ??  crc_driver.o [2]
CRC_GetCrcResult             0x39e5   0x3c  Code  ??  crc_hw_access.o [2]
CRC_GetDataHReg              0x389d    0x4  Code  Lc  crc_hw_access.o [2]
CRC_GetDataLReg              0x38a1    0x4  Code  Lc  crc_hw_access.o [2]
CRC_GetDataReg               0x3895    0x4  Code  Lc  crc_hw_access.o [2]
CRC_GetProtocolWidth         0x38ed   0x18  Code  Lc  crc_hw_access.o [2]
CRC_GetReadTranspose         0x392b   0x2a  Code  Lc  crc_hw_access.o [2]
CRC_Init                     0x396b   0x46  Code  ??  crc_hw_access.o [2]
CRC_SetDataLLReg             0x357d    0x4  Code  Lc  crc_driver.o [2]
CRC_SetDataLReg              0x3579    0x4  Code  Lc  crc_driver.o [2]
CRC_SetDataReg               0x3899    0x4  Code  Lc  crc_hw_access.o [2]
CRC_SetFXorMode              0x3585   0x22  Code  Lc  crc_driver.o [2]
CRC_SetFXorMode              0x38cb   0x22  Code  Lc  crc_hw_access.o [2]
CRC_SetPolyReg               0x3581    0x4  Code  Lc  crc_driver.o [2]
CRC_SetPolyReg               0x38a5    0x4  Code  Lc  crc_hw_access.o [2]
CRC_SetProtocolWidth         0x35a7   0x16  Code  Lc  crc_driver.o [2]
CRC_SetProtocolWidth         0x3905   0x16  Code  Lc  crc_hw_access.o [2]
CRC_SetReadTranspose         0x35cd   0x16  Code  Lc  crc_driver.o [2]
CRC_SetReadTranspose         0x3955   0x16  Code  Lc  crc_hw_access.o [2]
CRC_SetSeedOrDataMode        0x38a9   0x22  Code  Lc  crc_hw_access.o [2]
CRC_SetSeedReg               0x39c5   0x20  Code  ??  crc_hw_access.o [2]
CRC_SetWriteTranspose        0x35bd   0x10  Code  Lc  crc_driver.o [2]
CRC_SetWriteTranspose        0x391b   0x10  Code  Lc  crc_hw_access.o [2]
CSTACK$$Base            0x2000'5500          --   Gb  - Linker created -
CSTACK$$Limit           0x2000'5800          --   ??  - Linker created -
CallbackHandler              0x7d45  0x140  Code  Lc  lin.o [2]
CfgParam_Bus            0x2000'3e38   0x14  Data  ??  SCUMain.o [2]
CfgParam_CAL            0x2000'327c  0x11c  Data  ??  Config_Exp.o [2]
CfgParam_CALExt         0x2000'3398   0xcc  Data  ??  Config_Exp.o [2]
CfgParam_CALExt_SWConst_C
                        0x2000'0204   0xcc  Data  ??  Config_Exp.o [2]
CfgParam_CAL_SWConst_C  0x2000'02d0  0x11c  Data  ??  Config_Exp.o [2]
CfgParam_Mdl                 0xc711  0x2dc  Code  ??  CfgParam_Mdl.o [2]
CfgParam_Mdl_B          0x2000'3244   0x14  Data  ??  CfgParam_Mdl.o [2]
CfgParam_Mdl_DW         0x2000'3258    0xc  Data  ??  CfgParam_Mdl.o [2]
CfgParam_Mdl_Init            0xc6fb    0xe  Code  ??  CfgParam_Mdl.o [2]
CfgParam_Mdl_MdlrefDW   0x2000'3240    0x4  Data  ??  CfgParam_Mdl.o [2]
CfgParam_Mdl_PrevZCX    0x2000'42c7    0x1  Data  ??  CfgParam_Mdl.o [2]
CfgParam_Mdl_Subsystem       0xc6e5   0x16  Code  ??  CfgParam_Mdl.o [2]
CfgParam_Mdl_initialize
                             0xc9fd    0x6  Code  ??  CfgParam_Mdl.o [2]
CfgParam_SCUInfo_C      0x2000'03ec    0xc  Data  ??  Config_Exp.o [2]
ClockMan_enInit            0x1'1d61   0x12  Code  ??  ClockMan.o [2]
ClockMan_vInitCMU          0x1'1d73   0x26  Code  ??  ClockMan.o [2]
ComLog_Mdl                   0xfa75  0x128  Code  ??  ComLog_Mdl.o [2]
ComLog_Mdl_MdlrefDW     0x2000'3278    0x4  Data  ??  ComLog_Mdl.o [2]
ComLog_Mdl_initialize        0xfb9d    0x6  Code  ??  ComLog_Mdl.o [2]
ConfigRecv              0x2000'42f5    0x1  Data  ??  LINTP_NodeSrv.o [2]
Config_HWVersion        0x2000'3464    0x8  Data  ??  Config_Exp.o [2]
Config_SCUInfo          0x2000'346c    0xc  Data  ??  Config_Exp.o [2]
Config_SCUPartNum       0x2000'3478    0xc  Data  ??  Config_Exp.o [2]
Config_SWVersion        0x2000'3484    0x4  Data  ??  Config_Exp.o [2]
Config_isCALFileVld     0x2000'42c8    0x1  Data  ??  Config_Exp.o [2]
Config_isPosTblVld      0x2000'42c9    0x1  Data  ??  Config_Exp.o [2]
Config_isStartupRdy     0x2000'42ca    0x1  Data  ??  Config_Exp.o [2]
Config_stCALFileFlt     0x2000'42cb    0x1  Data  ??  Config_Exp.o [2]
CtrlLog_Mdl                  0xca51  0x986  Code  ??  CtrlLog_Mdl.o [2]
CtrlLog_Mdl_B           0x2000'348c    0xc  Data  ??  CtrlLog_Mdl.o [2]
CtrlLog_Mdl_ConstB         0x1'9230    0x4  Data  ??  CtrlLog_Mdl_data.o [2]
CtrlLog_Mdl_DW          0x2000'3498   0x14  Data  ??  CtrlLog_Mdl.o [2]
CtrlLog_Mdl_Init             0xca3d    0xc  Code  ??  CtrlLog_Mdl.o [2]
CtrlLog_Mdl_MdlrefDW    0x2000'3488    0x4  Data  ??  CtrlLog_Mdl.o [2]
CtrlLog_Mdl_PrevZCX     0x2000'4266    0x2  Data  ??  CtrlLog_Mdl.o [2]
CtrlLog_Mdl_initialize       0xd3d7   0x10  Code  ??  CtrlLog_Mdl.o [2]
CtrlLog_isInterlockEnbl
                        0x2000'430e    0x1  Data  ??  SCUMain.o [2]
CtrlLog_stRelearnMode   0x2000'4312    0x1  Data  ??  SCUMain.o [2]
DIAG_COMMAND_RX_Callback
                             0x768d   0x1c  Code  ??  LINTP_Srv.o [2]
DIAG_RESPONSE_TX_Callback
                             0x76a9   0x1a  Code  ??  LINTP_Srv.o [2]
DIOCtrl_Mdl                  0xf82d    0x6  Code  ??  DIOCtrl_Mdl.o [2]
DIOCtrl_Mdl_MdlrefDW    0x2000'34b0    0x4  Data  ??  DIOCtrl_Mdl.o [2]
DIOCtrl_Mdl_initialize       0xf833    0x6  Code  ??  DIOCtrl_Mdl.o [2]
DIOCtrl_bReadPin           0x1'1df1   0x1e  Code  ??  DioCtrl_Man.o [2]
DIOCtrl_enInit             0x1'1dd9   0x14  Code  ??  DioCtrl_Man.o [2]
DIOCtrl_vWritePin          0x1'1e0f   0x22  Code  ??  DioCtrl_Man.o [2]
DMA0_IRQHandler            0x1'8c15    0xa  Code  ??  edma_irq.o [2]
DMA1_IRQHandler            0x1'8c1f    0xa  Code  ??  edma_irq.o [2]
DMA2_IRQHandler            0x1'8c29    0xa  Code  ??  edma_irq.o [2]
DMA3_IRQHandler            0x1'8c33    0xa  Code  ??  edma_irq.o [2]
DMA_Error_IRQHandler       0x1'8c3d   0x2e  Code  ??  edma_irq.o [2]
DTC_isDTCEnbl           0x2000'42cc    0x1  Data  ??  DTC_Exp.o [2]
DTC_isFltClr            0x2000'42cd    0x1  Data  ??  DTC_Exp.o [2]
DataCopy                   0x1'5d35   0x24  Code  ??  irs_lib.o [2]
DefaultISR                 0x1'9271         Code  Wk  startup_S32K118.o [1]
DevAssert                  0x1'2539   0x10  Code  Lc  adc_driver.o [2]
DevAssert                     0x411   0x10  Code  Lc  clock_S32K1xx.o [2]
DevAssert                    0x3569   0x10  Code  Lc  crc_driver.o [2]
DevAssert                  0x1'47ad   0x10  Code  Lc  edma_driver.o [2]
DevAssert                  0x1'5109   0x10  Code  Lc  edma_hw_access.o [2]
DevAssert                  0x1'2cdd   0x10  Code  Lc  erm_driver.o [2]
DevAssert                    0x3149   0x10  Code  Lc  flash_driver.o [2]
DevAssert                  0x1'5d59   0x10  Code  Lc  flexcan_driver.o [2]
DevAssert                  0x1'6359   0x10  Code  Lc  flexcan_hw_access.o [2]
DevAssert                    0x4a45   0x10  Code  Lc  ftm_common.o [2]
DevAssert                    0x4871   0x10  Code  Lc  ftm_hw_access.o [2]
DevAssert                  0x1'2e85   0x10  Code  Lc  ftm_ic_driver.o [2]
DevAssert                    0x3a21   0x10  Code  Lc  ftm_pwm_driver.o [2]
DevAssert                    0x3741   0x10  Code  Lc  interrupt_manager.o [2]
DevAssert                    0x78b1   0x10  Code  Lc  lin.o [2]
DevAssert                    0x8c6d   0x10  Code  Lc  lin_common_api.o [2]
DevAssert                    0x51d1   0x10  Code  Lc  lin_lpuart_driver.o [2]
DevAssert                    0x7ea1   0x10  Code  Lc  lpit_driver.o [2]
DevAssert                  0x1'399d   0x10  Code  Lc  lpspi_master_driver.o [2]
DevAssert                  0x1'4f19   0x10  Code  Lc  lpspi_slave_driver.o [2]
DevAssert                    0x852d   0x10  Code  Lc  lptmr_driver.o [2]
DevAssert                    0x9671   0x10  Code  Lc  lptmr_hw_access.o [2]
DevAssert                    0x62f9   0x10  Code  Lc  lpuart_driver.o [2]
DevAssert                    0x2c99   0x10  Code  Lc  nvmman.o [2]
DevAssert                    0x6179   0x10  Code  Lc  osif_baremetal.o [2]
DevAssert                  0x1'232d   0x10  Code  Lc  pdb_driver.o [2]
DevAssert                  0x1'3675   0x10  Code  Lc  pdb_hw_access.o [2]
DevAssert                  0x1'41c5   0x10  Code  Lc  pins_port_hw_access.o [2]
DiagComMan                   0xd405   0x10  Code  ??  DiagComMan.o [2]
DiagComMan_Init              0xd3f9    0xc  Code  ??  DiagComMan.o [2]
DiagComMan_MdlrefDW     0x2000'34ac    0x4  Data  ??  DiagComMan.o [2]
DiagComMan_initialize        0xd415    0x6  Code  ??  DiagComMan.o [2]
EDMA_ClearDoneStatusFlag
                           0x1'47fb   0x1e  Code  Lc  edma_driver.o [2]
EDMA_ClearErrorIntStatusFlag
                           0x1'47dd   0x1e  Code  Lc  edma_driver.o [2]
EDMA_ClearIntStatusFlag
                           0x1'4819   0x1e  Code  Lc  edma_driver.o [2]
EDMA_DRV_ClearIntStatus
                           0x1'4a61   0x2c  Code  Lc  edma_driver.o [2]
EDMA_DRV_ConfigMultiBlockTransfer
                           0x1'4d15   0xe8  Code  ??  edma_driver.o [2]
EDMA_DRV_ConfigSingleBlockTransfer
                           0x1'4b19  0x1f6  Code  ??  edma_driver.o [2]
EDMA_DRV_ErrorIRQHandler
                           0x1'4ab7   0x5c  Code  ??  edma_driver.o [2]
EDMA_DRV_GetDmaRegBaseAddr
                           0x1'4ef5   0x1e  Code  ??  edma_driver.o [2]
EDMA_DRV_IRQHandler        0x1'4a8d   0x2a  Code  ??  edma_driver.o [2]
EDMA_DRV_InstallCallback
                           0x1'49f1   0x6a  Code  ??  edma_driver.o [2]
EDMA_DRV_StartChannel      0x1'4e05   0x68  Code  ??  edma_driver.o [2]
EDMA_DRV_StopChannel       0x1'4e6d   0x68  Code  ??  edma_driver.o [2]
EDMA_DRV_ValidTransferSize
                           0x1'4ee1   0x14  Code  Lc  edma_driver.o [2]
EDMA_GetErrorIntStatusFlag
                           0x1'8c11    0x4  Code  Lc  edma_irq.o [2]
EDMA_SetDmaRequestCmd      0x1'5119   0x10  Code  ??  edma_hw_access.o [2]
EDMA_SetMinorLoopMappingCmd
                           0x1'47bd   0x20  Code  Lc  edma_driver.o [2]
EDMA_TCDClearReg           0x1'5129   0xac  Code  ??  edma_hw_access.o [2]
EDMA_TCDSetAttribute       0x1'51d5   0x58  Code  ??  edma_hw_access.o [2]
EDMA_TCDSetDestAddr        0x1'48d5   0x2c  Code  Lc  edma_driver.o [2]
EDMA_TCDSetDestOffset      0x1'4901   0x2c  Code  Lc  edma_driver.o [2]
EDMA_TCDSetDisableDmaRequestAfterTCDDoneCmd
                           0x1'4931   0x52  Code  Lc  edma_driver.o [2]
EDMA_TCDSetEngineStall     0x1'4837   0x44  Code  Lc  edma_driver.o [2]
EDMA_TCDSetMajorCompleteIntCmd
                           0x1'4989   0x52  Code  Lc  edma_driver.o [2]
EDMA_TCDSetMajorCount      0x1'52f9   0xce  Code  ??  edma_hw_access.o [2]
EDMA_TCDSetNbytes          0x1'522d   0xa4  Code  ??  edma_hw_access.o [2]
EDMA_TCDSetSrcAddr         0x1'487b   0x2e  Code  Lc  edma_driver.o [2]
EDMA_TCDSetSrcOffset       0x1'48a9   0x2c  Code  Lc  edma_driver.o [2]
ERMHwAbs_ISR               0x1'1db1    0x2  Code  ??  ERMHwAbs.o [2]
ERMHwAbs_vInit             0x1'1db3   0x1e  Code  ??  ERMHwAbs.o [2]
ERM_DRV_Init               0x1'2d49   0x9a  Code  ??  erm_driver.o [2]
ERM_DRV_SetInterruptConfig
                           0x1'2de3   0x58  Code  ??  erm_driver.o [2]
ERM_EnableEventInterrupt
                           0x1'2ced   0x5c  Code  Lc  erm_driver.o [2]
ERM_Init                   0x1'41b9    0xc  Code  ??  erm_hw_access.o [2]
ERM_fault_IRQHandler       0x1'9271         Code  Wk  startup_S32K118.o [1]
ExtDev_Mdl                 0x1'013d    0xc  Code  ??  ExtDev_Mdl.o [2]
ExtDev_Mdl_MdlrefDW     0x2000'34b8    0x4  Data  ??  ExtDev_Mdl.o [2]
ExtDev_Mdl_initialize      0x1'0149    0x6  Code  ??  ExtDev_Mdl.o [2]
ExtDev_enInit              0x1'0155   0x14  Code  ??  ExtDev.o [2]
ExtDev_enSBCSleep          0x1'02fd   0x3c  Code  ??  ExtDev.o [2]
ExtDev_u16GetSBCFault      0x1'022d    0x6  Code  ??  ExtDev.o [2]
ExtDev_u8DevStatus      0x2000'426a    0x2  Data  ??  ExtDev.o [2]
ExtDev_u8DevStatus_Const
                             0x1d82    0x2  Data  ??  ExtDev.o [2]
ExtDev_u8Motor1StopMovement
                           0x1'0233   0x30  Code  ??  ExtDev.o [2]
ExtDev_u8SetMotorDirection
                           0x1'0263   0x60  Code  ??  ExtDev.o [2]
ExtDev_u8SupStatus      0x2000'426c    0x2  Data  ??  ExtDev.o [2]
ExtDev_u8SupStatus_Const
                             0x30fa    0x2  Data  ??  ExtDev.o [2]
ExtDev_u8ThermStatus    0x2000'4268    0x2  Data  ??  ExtDev.o [2]
ExtDev_u8ThermStatus_Const
                             0x1d7e    0x2  Data  ??  ExtDev.o [2]
ExtDev_vStep               0x1'0169   0xc4  Code  ??  ExtDev.o [2]
FLASH_DRV_CommandSequence
                        0x2000'0bb5   0x3c  Code  Lc  flash_driver.o [2]
FLASH_DRV_DEFlashPartition
                             0x345d   0x7e  Code  ??  flash_driver.o [2]
FLASH_DRV_EEEWrite           0x33ad   0xb0  Code  ??  flash_driver.o [2]
FLASH_DRV_EnableDoubleBitFaultInterupt
                             0x34fd   0x16  Code  ??  flash_driver.o [2]
FLASH_DRV_GetCmdCompleteFlag
                             0x2ca9   0x18  Code  Lc  nvmman.o [2]
FLASH_DRV_GetDEPartitionCode
                             0x3159   0xb6  Code  Lc  flash_driver.o [2]
FLASH_DRV_Init               0x320f   0x6a  Code  ??  flash_driver.o [2]
FLASH_DRV_SetFlexRamFunction
                             0x327d   0x9c  Code  ??  flash_driver.o [2]
FLASH_DRV_WaitEEWriteToFinish
                             0x331d   0x8c  Code  Lc  flash_driver.o [2]
FLEXCAN_ClearErrIntStatusFlag
                           0x1'657d   0x10  Code  ??  flexcan_hw_access.o [2]
FLEXCAN_ClearMsgBuffIntStatusFlag
                           0x1'5d99   0x14  Code  Lc  flexcan_driver.o [2]
FLEXCAN_ClearWTOF          0x1'5dcb   0x10  Code  Lc  flexcan_driver.o [2]
FLEXCAN_ClearWUMF          0x1'5deb   0x10  Code  Lc  flexcan_driver.o [2]
FLEXCAN_CompleteRxMessageFifoData
                           0x1'6261   0xee  Code  Lc  flexcan_driver.o [2]
FLEXCAN_CompleteTransfer
                           0x1'6205   0x5c  Code  Lc  flexcan_driver.o [2]
FLEXCAN_ComputePayloadSize
                           0x1'6375   0x4e  Code  Lc  flexcan_hw_access.o [2]
FLEXCAN_Error_IRQHandler
                           0x1'610d   0x50  Code  ??  flexcan_driver.o [2]
FLEXCAN_GetErrorCounters
                           0x1'5dad    0x4  Code  Lc  flexcan_driver.o [2]
FLEXCAN_GetMsgBuff         0x1'6411  0x136  Code  ??  flexcan_hw_access.o [2]
FLEXCAN_GetMsgBuffIntStatusFlag
                           0x1'5d79   0x20  Code  Lc  flexcan_driver.o [2]
FLEXCAN_GetMsgBuffRegion
                           0x1'63c3   0x4e  Code  ??  flexcan_hw_access.o [2]
FLEXCAN_GetPayloadSize     0x1'6649   0x28  Code  ??  flexcan_hw_access.o [2]
FLEXCAN_GetWTOF            0x1'5dbd    0xe  Code  Lc  flexcan_driver.o [2]
FLEXCAN_GetWUMF            0x1'5ddb   0x10  Code  Lc  flexcan_driver.o [2]
FLEXCAN_IRQHandler         0x1'5f81  0x18c  Code  ??  flexcan_driver.o [2]
FLEXCAN_IRQHandlerRxFIFO
                           0x1'5e01   0xbc  Code  Lc  flexcan_driver.o [2]
FLEXCAN_IRQHandlerRxMB     0x1'5ec5   0xb8  Code  Lc  flexcan_driver.o [2]
FLEXCAN_IsFDEnabled        0x1'6369    0xc  Code  Lc  flexcan_hw_access.o [2]
FLEXCAN_IsPNEnabled        0x1'5db1    0xc  Code  Lc  flexcan_driver.o [2]
FLEXCAN_IsRxFifoEnabled
                           0x1'5d6d    0xc  Code  Lc  flexcan_driver.o [2]
FLEXCAN_LockRxMsgBuff      0x1'6547    0xa  Code  ??  flexcan_hw_access.o [2]
FLEXCAN_ReadRxFifo         0x1'658d   0xb8  Code  ??  flexcan_hw_access.o [2]
FLEXCAN_SetMsgBuffIntCmd
                           0x1'6551   0x2c  Code  ??  flexcan_hw_access.o [2]
FLEXCAN_UnlockRxMsgBuff
                           0x1'5d69    0x4  Code  Lc  flexcan_driver.o [2]
FLEXCAN_WakeUpHandler      0x1'6161   0x9a  Code  ??  flexcan_driver.o [2]
FLEXIO_IRQHandler          0x1'9271         Code  Wk  startup_S32K118.o [1]
FTFC_IRQHandler            0x1'9271         Code  Wk  startup_S32K118.o [1]
FTM0_Ch0_7_IRQHandler      0x1'348d   0x54  Code  ??  ftm_ic_driver.o [2]
FTM0_Fault_IRQHandler      0x1'9271         Code  Wk  startup_S32K118.o [1]
FTM0_Ovf_Reload_IRQHandler
                           0x1'9271         Code  Wk  startup_S32K118.o [1]
FTM1_Ch0_7_IRQHandler      0x1'34e1   0x54  Code  ??  ftm_ic_driver.o [2]
FTM1_Fault_IRQHandler      0x1'9271         Code  Wk  startup_S32K118.o [1]
FTM1_Ovf_Reload_IRQHandler
                           0x1'9271         Code  Wk  startup_S32K118.o [1]
FTM_DRV_ClearChnEventFlag
                           0x1'3011   0x36  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_ClearTimerOverflow
                             0x3a6b    0xa  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_ConvertFreqToPeriodTicks
                             0x5189   0x3a  Code  ??  ftm_common.o [2]
FTM_DRV_Enable               0x4893    0xe  Code  Lc  ftm_hw_access.o [2]
FTM_DRV_Enable               0x3c8d    0xe  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_EnableChnInt       0x1'2fdb   0x36  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_EnablePwmChannelOutputs
                             0x3a4b   0x20  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_GetChnCountVal     0x1'2e9b   0x28  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_GetClockPs           0x4a55    0x8  Code  Lc  ftm_common.o [2]
FTM_DRV_GetClockSource       0x3a99    0xa  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_GetCpwms             0x3a8d    0xc  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_GetDetectedFaultInput
                             0x4b05    0xc  Code  Lc  ftm_common.o [2]
FTM_DRV_GetDualChnCombineCmd
                             0x3e89   0x2a  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_GetDualChnMofCombineCmd
                             0x3eb3   0x34  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_GetDualEdgeCaptureBit
                           0x1'30f9   0x34  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_GetEnabledInterrupts
                             0x4fd5   0x7c  Code  ??  ftm_common.o [2]
FTM_DRV_GetFrequency         0x50e1   0xa6  Code  ??  ftm_common.o [2]
FTM_DRV_GetMod             0x1'2e95    0x6  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_GetMod               0x3a31    0x6  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_GetReloadFlag        0x4a85    0xc  Code  Lc  ftm_common.o [2]
FTM_DRV_GetStatusFlags       0x5051   0x90  Code  ??  ftm_common.o [2]
FTM_DRV_HasChnEventOccurred
                             0x4acb   0x2e  Code  Lc  ftm_common.o [2]
FTM_DRV_HasChnEventOccurred
                           0x1'3047   0x2e  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_HasTimerOverflowed
                             0x4a79    0xc  Code  Lc  ftm_common.o [2]
FTM_DRV_Init                 0x4cf5  0x164  Code  ??  ftm_common.o [2]
FTM_DRV_InitInputCapture
                           0x1'3237  0x246  Code  ??  ftm_ic_driver.o [2]
FTM_DRV_InitMeasurement
                           0x1'312d  0x10a  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_InitModule           0x48a1   0x1a  Code  ??  ftm_hw_access.o [2]
FTM_DRV_InitPwm              0x44c7  0x1f4  Code  ??  ftm_pwm_driver.o [2]
FTM_DRV_InitPwmCombinedChannel
                             0x41bd  0x26e  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_InitPwmDutyCycleChannel
                             0x4431   0x96  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_InitPwmIndependentChannel
                             0x3fd1  0x1de  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_InputCaptureHandler
                           0x1'3555  0x116  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_IrqHandler         0x1'3535   0x1e  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_IsChnIntEnabled
                             0x4a9d   0x2e  Code  Lc  ftm_common.o [2]
FTM_DRV_IsChnTriggerGenerated
                             0x4b89    0xc  Code  Lc  ftm_common.o [2]
FTM_DRV_IsFaultIntEnabled
                             0x4af9    0xc  Code  Lc  ftm_common.o [2]
FTM_DRV_IsOverflowIntEnabled
                             0x4a6d    0xc  Code  Lc  ftm_common.o [2]
FTM_DRV_IsReloadIntEnabled
                             0x4a91    0xc  Code  Lc  ftm_common.o [2]
FTM_DRV_Reset                0x48bb   0xa2  Code  ??  ftm_hw_access.o [2]
FTM_DRV_SetBdmMode           0x4b95   0x18  Code  Lc  ftm_common.o [2]
FTM_DRV_SetChnCountVal       0x3b97   0x2c  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetChnEdgeLevel
                           0x1'2f6f   0x6c  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetChnEdgeLevel
                             0x3b2b   0x6c  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetChnFaultInputPolarityCmd
                             0x3bfd   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetChnInputCaptureFilter
                             0x49b7   0x82  Code  ??  ftm_hw_access.o [2]
FTM_DRV_SetChnMSnBAMode
                           0x1'2f03   0x6c  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetChnMSnBAMode
                             0x3abf   0x6c  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetChnOutputPolarityCmd
                             0x3bc3   0x3a  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetChnTriggerCmd
                             0x495d   0x5a  Code  ??  ftm_hw_access.o [2]
FTM_DRV_SetClockPs           0x4881   0x12  Code  Lc  ftm_hw_access.o [2]
FTM_DRV_SetClockSource     0x1'2ec3   0x14  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetClockSource       0x3a37   0x14  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetCntinPwmSyncModeCmd
                             0x4cdd   0x18  Code  Lc  ftm_common.o [2]
FTM_DRV_SetCounterHardwareSyncModeCmd
                             0x4bfd   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetCounterInitVal
                           0x1'2ef5    0xe  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetCounterInitVal
                             0x3ab1    0xe  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetCounterSoftwareSyncModeCmd
                             0x4c7b   0x1a  Code  Lc  ftm_common.o [2]
FTM_DRV_SetCpwms           0x1'2ed7   0x10  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetCpwms             0x3a7d   0x10  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDeadtimeCount
                             0x3efb   0x2c  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDeadtimePrescale
                             0x3ee7   0x14  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDualChnCombineCmd
                             0x3e47   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDualChnCompCmd
                             0x3e05   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDualChnDeadtimeCmd
                             0x3d81   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDualChnDecapCmd
                           0x1'3075   0x42  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetDualChnFaultCmd
                             0x3cfd   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDualChnMofCombineCmd
                             0x3cbb   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDualChnPwmSyncCmd
                             0x3d3f   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetDualEdgeCaptureCmd
                           0x1'30b7   0x42  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetDualEdgeCaptureCmd
                             0x3dc3   0x42  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetFaultControlMode
                             0x3c4f   0x12  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetFaultInputCmd
                             0x3f79   0x3a  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetFaultInputFilterCmd
                             0x3f3b   0x3e  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetFaultInputFilterVal
                             0x3f27   0x14  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetFaultInt          0x3c3f   0x10  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetHardwareSyncTriggerSrc
                             0x4b11   0x3a  Code  Lc  ftm_common.o [2]
FTM_DRV_SetHwTriggerSyncModeCmd
                             0x4c69   0x12  Code  Lc  ftm_common.o [2]
FTM_DRV_SetInitChnOutputCmd
                             0x3c61   0x10  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetInitTriggerCmd
                             0x4b79   0x10  Code  Lc  ftm_common.o [2]
FTM_DRV_SetInvctrlHardwareSyncModeCmd
                             0x4bc1   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetInvctrlPwmSyncModeCmd
                             0x4cc5   0x18  Code  Lc  ftm_common.o [2]
FTM_DRV_SetInvctrlSoftwareSyncModeCmd
                             0x4c2d   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetMaxLoadingCmd
                             0x4b5b   0x10  Code  Lc  ftm_common.o [2]
FTM_DRV_SetMinLoadingCmd
                             0x4b6b    0xe  Code  Lc  ftm_common.o [2]
FTM_DRV_SetMod             0x1'2ee7    0xe  Code  Lc  ftm_ic_driver.o [2]
FTM_DRV_SetMod               0x3aa3    0xe  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetModCntinCvHardwareSyncModeCmd
                             0x4be9   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetModCntinCvSoftwareSyncModeCmd
                             0x4c55   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetOutmaskHardwareSyncModeCmd
                             0x4bd5   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetOutmaskPwmSyncModeCmd
                             0x4b4b   0x10  Code  Lc  ftm_common.o [2]
FTM_DRV_SetOutmaskSoftwareSyncModeCmd
                             0x4c41   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetPwmFaultBehavior
                             0x3fb3   0x1e  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetPwmSyncMode       0x3c9b   0x10  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetPwmSyncModeCmd
                             0x4c99   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetSoftwareTriggerCmd
                             0x3cab   0x10  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_SetSwoctrlHardwareSyncModeCmd
                             0x4bad   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetSwoctrlPwmSyncModeCmd
                             0x4cad   0x18  Code  Lc  ftm_common.o [2]
FTM_DRV_SetSwoctrlSoftwareSyncModeCmd
                             0x4c19   0x14  Code  Lc  ftm_common.o [2]
FTM_DRV_SetSync              0x4e89  0x146  Code  ??  ftm_common.o [2]
FTM_DRV_SetTimerOverflowInt
                             0x4a5d   0x10  Code  Lc  ftm_common.o [2]
FTM_DRV_SetWriteProtectionCmd
                             0x3c71   0x1c  Code  Lc  ftm_pwm_driver.o [2]
FTM_DRV_UpdatePwmChannel
                             0x46c1  0x1a6  Code  ??  ftm_pwm_driver.o [2]
Flash_InitConfig0          0x1'90a8   0x14  Data  ??  peripherals_flash_1.o [2]
Function_id             0x2000'429c    0x2  Data  ??  LINTP_NodeSrv.o [2]
HALLDeco_Mdl                 0xd575  0x1f0  Code  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_B          0x2000'42d1    0x1  Data  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_DW         0x2000'34cc   0x10  Data  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_Dir_Timeout
                             0xd429   0x4c  Code  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_Dir_Timeout_Init
                             0xd421    0x8  Code  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_HallDeco_Spd
                             0xd47f   0xe6  Code  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_HallDeco_Spd_Init
                             0xd475    0xa  Code  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_ISR           0x1'7b19   0x40  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_B      0x2000'34e0    0x8  Data  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_Capture_Diff
                           0x1'79db   0xbc  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_Capture_Diff_Init
                           0x1'79bd   0x1e  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_ConstB
                           0x1'9274    0x1  Data  ??  HALLDeco_Mdl_ISR_data.o [2]
HALLDeco_Mdl_ISR_CountPulse
                           0x1'7a97   0x1a  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_DW     0x2000'34e8   0x1c  Data  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_Determine_Dir
                           0x1'7ab1   0x4a  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_Init      0x1'7b11    0x8  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_MdlrefDW
                        0x2000'34dc    0x4  Data  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_PrevZCX
                        0x2000'42d2    0x1  Data  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_Spd_Snsr_Detection
                           0x1'7afb   0x16  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_initialize
                           0x1'7b59    0xc  Code  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_rtp_ChannelIdSensor1
                        0x2000'42d3    0x1  Data  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_ISR_rtu_inputCaptureValue
                        0x2000'3504    0x4  Data  ??  HALLDeco_Mdl_ISR.o [2]
HALLDeco_Mdl_Init            0xd569    0xc  Code  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_MdlrefDW   0x2000'34c8    0x4  Data  ??  HALLDeco_Mdl.o [2]
HALLDeco_Mdl_initialize
                             0xd765    0x6  Code  ??  HALLDeco_Mdl.o [2]
HallDeco_sumFilterdPulseTicks
                        0x2000'3e54    0x4  Data  ??  SCUMain.o [2]
HardFault_Handler            0x61a1    0x2  Code  ??  osif_baremetal.o [2]
ICMntr_enInit              0x1'1e31   0x22  Code  ??  ICMon.o [2]
ICMntr_vFTM0CH6RisingEdgeCallback
                           0x1'1e53   0x40  Code  ??  ICMon.o [2]
ICMntr_vFTM0CH7RisingEdgeCallback
                           0x1'1e93   0x2a  Code  ??  ICMon.o [2]
INT_SYS_DisableIRQ           0x3811   0x42  Code  ??  interrupt_manager.o [2]
INT_SYS_DisableIRQGlobal
                             0x3871    0xc  Code  ??  interrupt_manager.o [2]
INT_SYS_EnableIRQ            0x37cf   0x42  Code  ??  interrupt_manager.o [2]
INT_SYS_EnableIRQGlobal
                             0x3855   0x1a  Code  ??  interrupt_manager.o [2]
INT_SYS_InstallHandler       0x3751   0x7e  Code  ??  interrupt_manager.o [2]
ISOUDS_Buff             0x2000'3578    0x4  Data  ??  ISOUDS.o [2]
ISOUDS_ClrDiagInf          0x1'8cc5   0x56  Code  ??  ISOUDS_ClrDTC.o [2]
ISOUDS_Conf             0x2000'3570    0x8  Data  ??  ISOUDS.o [2]
ISOUDS_ECUReset            0x1'8b51   0x64  Code  ??  ISOUDS_ECUReset.o [2]
ISOUDS_EcuRstReq        0x2000'42d7    0x1  Data  ??  ISOUDS.o [2]
ISOUDS_GetSASt             0x1'21c7    0x6  Code  ??  ISOUDS_SA.o [2]
ISOUDS_GetSAStLevel        0x1'22a5   0x22  Code  ??  ISOUDS_SA.o [2]
ISOUDS_IOCtrlByID          0x1'8221  0x1b0  Code  ??  ISOUDS_IOCtrlByID.o [2]
ISOUDS_IoConf              0x1'9240    0x4  Data  ??  ISOUDS_IoCfg.o [2]
ISOUDS_OldSess          0x2000'42e2    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_RdConf              0x1'9244    0x4  Data  ??  ISOUDS_RdCfg.o [2]
ISOUDS_RdDaByID            0x1'7b8d  0x270  Code  ??  ISOUDS_RdDaByID.o [2]
ISOUDS_RdMemByAddr         0x1'8861   0xae  Code  ??  ISOUDS_RdMemByAddr.o [2]
ISOUDS_RecvWrDid        0x2000'427c    0x2  Data  Lc  ISOUDS_WrDaByID.o [2]
ISOUDS_ReqECUReset         0x1'0907    0x8  Code  ??  ISOUDS.o [2]
ISOUDS_Rst                 0x1'08d5   0x2a  Code  ??  ISOUDS.o [2]
ISOUDS_RtnCntrl            0x1'7dfd  0x218  Code  ??  ISOUDS_RtnCntrl.o [2]
ISOUDS_RtnConf             0x1'9248    0x4  Data  ??  ISOUDS_RtnCfg.o [2]
ISOUDS_S3ServerTimer    0x2000'357c    0x4  Data  ??  ISOUDS.o [2]
ISOUDS_S3ServerTimer_Running
                        0x2000'42d6    0x1  Data  ??  ISOUDS.o [2]
ISOUDS_SA                  0x1'1f39  0x28e  Code  ??  ISOUDS_SA.o [2]
ISOUDS_SA::seed         0x2000'427a    0x2  Data  Lc  ISOUDS_SA.o [2]
ISOUDS_SAAttCnt         0x2000'42dd    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_SAChkTimer          0x1'21dd   0x94  Code  ??  ISOUDS_SA.o [2]
ISOUDS_SACurrentRequestedLevel
                        0x2000'42de    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_SAInit              0x1'1ed5   0x3c  Code  ??  ISOUDS_SA.o [2]
ISOUDS_SALevel          0x2000'42dc    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_SAReset             0x1'2279   0x22  Code  ??  ISOUDS_SA.o [2]
ISOUDS_SAState          0x2000'42e1    0x1  Data  Lc  ISOUDS_SA.o [2]
ISOUDS_SAStatic_Seed    0x2000'42db    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_SATimerAttCntExceeded
                        0x2000'3584    0x4  Data  ??  ISOUDS_SA.o [2]
ISOUDS_SATimerAttCntExceededRunning
                        0x2000'42e0    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_SATimerWaitingForKey
                        0x2000'3580    0x4  Data  ??  ISOUDS_SA.o [2]
ISOUDS_SATimerWaitingForKeyRunning
                        0x2000'42df    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_Security_CalcKey
                           0x1'22f9   0x1a  Code  ??  ISOUDS_SA.o [2]
ISOUDS_Security_CalcKey_EOL
                           0x1'2313   0x1a  Code  ??  ISOUDS_SA.o [2]
ISOUDS_Sess             0x2000'42d5    0x1  Data  ??  ISOUDS.o [2]
ISOUDS_StECUUnLock      0x2000'42da    0x1  Data  ??  ISOUDS_SA.o [2]
ISOUDS_StrtDiagSess        0x1'8561   0xfa  Code  ??  ISOUDS_StrtDiagSess.o [2]
ISOUDS_SwtDfltSess         0x1'865b   0x16  Code  Lc  ISOUDS_StrtDiagSess.o [2]
ISOUDS_SwtEolSess          0x1'86a1   0x18  Code  Lc  ISOUDS_StrtDiagSess.o [2]
ISOUDS_SwtExtDiagSess      0x1'8671   0x18  Code  Lc  ISOUDS_StrtDiagSess.o [2]
ISOUDS_SwtProgSess         0x1'8689   0x18  Code  Lc  ISOUDS_StrtDiagSess.o [2]
ISOUDS_TstrPrsnt           0x1'8e91   0x3e  Code  ??  ISOUDS_TstrPrsnt.o [2]
ISOUDS_WrConf              0x1'924c    0x4  Data  ??  ISOUDS_WrCfg.o [2]
ISOUDS_WrDaByID            0x1'8015  0x20c  Code  ??  ISOUDS_WrDaByID.o [2]
ISOUDS_iTab                0x1'8a74   0x78  Data  Lc  ISOUDS.o [2]
IoHwAb_GetHallInput     0x2000'42d4    0x1  Data  ??  IoSigIf_Exp.o [2]
LI0_lin_configuration_RAM
                        0x2000'0528    0x8  Data  Lc  lin_cfg.o [2]
LI0_lin_configuration_ROM
                           0x1'9108   0x10  Data  Lc  lin_cfg.o [2]
LI0_lin_configured_NAD  0x2000'0bae    0x1  Data  Lc  lin_cfg.o [2]
LI0_lin_diag_services_flag
                        0x2000'4282    0x2  Data  Lc  lin_cfg.o [2]
LI0_lin_diag_services_supported
                             0x966e    0x2  Data  Lc  lin_cfg.o [2]
LI0_lin_frm_err_resp_sig
                        0x2000'0baf    0x1  Data  Lc  lin_cfg.o [2]
LI0_lin_response_error_bit_offset
                        0x2000'0bb0    0x1  Data  Lc  lin_cfg.o [2]
LI0_lin_response_error_byte_offset
                        0x2000'0ba8    0x2  Data  Lc  lin_cfg.o [2]
LINCommError            0x2000'42f7    0x1  Data  ??  LINTP_Srv.o [2]
LINMiddleware_bGetLINBusInActivity
                           0x1'0a81   0x24  Code  ??  LINMiddleware.o [2]
LINMiddleware_enInit       0x1'0a51   0x30  Code  ??  LINMiddleware.o [2]
LINNM_LinOpMode         0x2000'42ef    0x1  Data  Lc  LINTP_NM.o [2]
LINSTACK_ECUSt          0x2000'42d8    0x1  Data  ??  ISOUDS.o [2]
LINTP                        0x70b5    0xc  Code  ??  LINTP.o [2]
LINTP_ComErrorCnt       0x2000'4294    0x2  Data  Lc  LINTP_NM.o [2]
LINTP_CopyData               0x76cf   0x24  Code  Lc  LINTP_Srv.o [2]
LINTP_DiagMastReq            0x6715  0x370  Code  ??  LINTP.o [2]
LINTP_DiagMastReq::Lin_Diag_NewMsg_FuncTbl
                        0x2000'0540    0x4  Data  Lc  LINTP.o [2]
LINTP_DiagMastResp           0x6abd  0x4a8  Code  ??  LINTP.o [2]
LINTP_EmergencyTimeCount
                        0x2000'4298    0x2  Data  ??  LINTP_NM.o [2]
LINTP_GetCommError           0x76f9    0x6  Code  ??  LINTP_Srv.o [2]
LINTP_InactiveTimeCount
                        0x2000'4296    0x2  Data  ??  LINTP_NM.o [2]
LINTP_Init                   0x70a9    0xc  Code  ??  LINTP.o [2]
LINTP_InitTask               0x65c1   0xb2  Code  ??  LINTP.o [2]
LINTP_Mon                    0x6f75   0xec  Code  ??  LINTP.o [2]
LINTP_NM_Init                0x7801   0x24  Code  ??  LINTP_NM.o [2]
LINTP_NM_RstTimeout          0x788b    0x8  Code  ??  LINTP_NM.o [2]
LINTP_NM_Task                0x7825   0x66  Code  ??  LINTP_NM.o [2]
LINTP_SetCommError           0x76f3    0x6  Code  ??  LINTP_Srv.o [2]
LINTP_Srv_Set_NAD            0x76c9    0x6  Code  ??  LINTP_Srv.o [2]
LINTP_TxConfCbk              0x6673   0x38  Code  ??  LINTP.o [2]
LINTP_TxConfCbk_FuncTbl
                        0x2000'0554    0x4  Data  ??  LINTP_Srv.o [2]
LINTP_isEmergencyMode   0x2000'42f1    0x1  Data  ??  LINTP_NM.o [2]
LINTP_isGotoSleep       0x2000'42f0    0x1  Data  ??  LINTP_NM.o [2]
LIN_Assign_FrameId           0x722b   0x1a  Code  ??  LINTP_NodeSrv.o [2]
LIN_Assign_NAD               0x7245   0x62  Code  ??  LINTP_NodeSrv.o [2]
LIN_ConChange_NAD            0x72c9   0xfa  Code  ??  LINTP_NodeSrv.o [2]
LIN_DRV_AbortTransferData
                             0x8c23    0xc  Code  ??  lin_driver.o [2]
LIN_DRV_GetReceiveStatus
                             0x8c2f    0xc  Code  ??  lin_driver.o [2]
LIN_DRV_GoToSleepMode        0x8c3b    0xc  Code  ??  lin_driver.o [2]
LIN_DRV_GotoIdleState        0x8c47    0xc  Code  ??  lin_driver.o [2]
LIN_DRV_IRQHandler           0x8c63    0x8  Code  ??  lin_driver.o [2]
LIN_DRV_Init                 0x8bf1    0xc  Code  ??  lin_driver.o [2]
LIN_DRV_InstallCallback
                             0x8bfd    0xa  Code  ??  lin_driver.o [2]
LIN_DRV_MakeChecksumByte
                             0x6571   0x50  Code  ??  lin_common.o [2]
LIN_DRV_ProcessParity        0x64bb   0xb6  Code  ??  lin_common.o [2]
LIN_DRV_ReceiveFrameData
                             0x8c15    0xe  Code  ??  lin_driver.o [2]
LIN_DRV_SendFrameData        0x8c07    0xe  Code  ??  lin_driver.o [2]
LIN_DRV_SetTimeoutCounter
                             0x8c5b    0x8  Code  ??  lin_driver.o [2]
LIN_DRV_TimeoutService       0x8c53    0x8  Code  ??  lin_driver.o [2]
LIN_Data_Dump                0x73d1   0x4e  Code  ??  LINTP_NodeSrv.o [2]
LIN_LPUART0_RxTx_IRQHandler
                           0x1'90d1    0xa  Code  Lc  lin_irq.o [2]
LIN_LPUART1_RxTx_IRQHandler
                           0x1'90db    0xa  Code  Lc  lin_irq.o [2]
LIN_LPUART_DRV_AbortTransferData
                             0x56d5   0x32  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_CheckWakeupSignal
                             0x5a9d   0x6a  Code  Lc  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_GetCurrentNodeState
                             0x57bd   0x28  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_GetReceiveStatus
                             0x5707   0x46  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_GoToSleepMode
                             0x574d   0x70  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_GotoIdleState
                             0x58b9   0x5a  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_IRQHandler
                             0x591d  0x106  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_Init          0x52bb  0x1c8  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_InstallCallback
                             0x5491   0x24  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_MakeChecksumByte
                             0x54c9   0x6c  Code  Lc  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_ProcessBreakDetect
                             0x5a23   0x70  Code  Lc  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_ProcessFrame
                             0x5b15   0x36  Code  Lc  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_ProcessFrameHeader
                             0x5b59  0x154  Code  Lc  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_ProcessReceiveFrameData
                             0x5cb9   0xba  Code  Lc  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_ProcessSendFrameData
                             0x5d7d  0x12c  Code  Lc  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_RecvFrmData
                             0x5639   0x96  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_SendFrameData
                             0x5565   0xd0  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_SetTimeoutCounter
                             0x5877   0x28  Code  ??  lin_lpuart_driver.o [2]
LIN_LPUART_DRV_TimeoutService
                             0x57e5   0x92  Code  ??  lin_lpuart_driver.o [2]
LIN_Node_Serv_Init           0x70c1   0x22  Code  ??  LINTP_NodeSrv.o [2]
LIN_Node_Serv_Req            0x70e3   0x4c  Code  ??  LINTP_NodeSrv.o [2]
LIN_Node_Serv_Resp           0x712f   0x22  Code  ??  LINTP_NodeSrv.o [2]
LIN_RdArrData                0x7573   0x48  Code  Lc  LINTP_NodeSrv.o [2]
LIN_RdConfTab              0x1'8f9c   0x24  Data  ??  LINTP_NodeSrv_Cfg.o [2]
LIN_RdLookUp                 0x75c5   0x4a  Code  Lc  LINTP_NodeSrv.o [2]
LIN_RdTabIdx            0x2000'42f2    0x1  Data  Lc  LINTP_NodeSrv.o [2]
LIN_Rd_Data_By_Id            0x742d  0x146  Code  ??  LINTP_NodeSrv.o [2]
LIN_Save_Cfg                 0x7615   0x52  Code  ??  LINTP_NodeSrv.o [2]
LIN_SrvMsgTab              0x1'8f48   0x30  Data  ??  LINTP_NodeSrv_Cfg.o [2]
LPI2C0_Master_Slave_IRQHandler
                           0x1'9271         Code  Wk  startup_S32K118.o [1]
LPIT0_IRQHandler           0x1'9271         Code  Wk  startup_S32K118.o [1]
LPIT_ClearInterruptFlagTimerChannels
                             0x7f23    0x4  Code  Lc  lpit_driver.o [2]
LPIT_DRV_ClearInterruptFlagTimerChannels
                             0x84c1   0x36  Code  ??  lpit_driver.o [2]
LPIT_DRV_GetInterruptFlagTimerChannels
                             0x848b   0x36  Code  ??  lpit_driver.o [2]
LPIT_DRV_Init                0x8139   0xa6  Code  ??  lpit_driver.o [2]
LPIT_DRV_InitChannel         0x81ed   0xf4  Code  ??  lpit_driver.o [2]
LPIT_DRV_SetTimerPeriodByCount
                             0x8451   0x3a  Code  ??  lpit_driver.o [2]
LPIT_DRV_SetTimerPeriodByUs
                             0x8329  0x102  Code  ??  lpit_driver.o [2]
LPIT_DRV_StartTimerChannels
                             0x82e5   0x36  Code  ??  lpit_driver.o [2]
LPIT_DisableInterruptTimerChannels
                             0x7f13    0x8  Code  Lc  lpit_driver.o [2]
LPIT_Enable                  0x7eb1   0x20  Code  Lc  lpit_driver.o [2]
LPIT_EnableInterruptTimerChannels
                             0x7f0b    0x8  Code  Lc  lpit_driver.o [2]
LPIT_GetInterruptFlagTimerChannels
                             0x7f1b    0x8  Code  Lc  lpit_driver.o [2]
LPIT_GetTimerChannelModeCmd
                             0x7f5b   0x36  Code  Lc  lpit_driver.o [2]
LPIT_Reset                   0x7ed1   0x28  Code  Lc  lpit_driver.o [2]
LPIT_SetReloadOnTriggerCmd
                             0x7ffb   0x3e  Code  Lc  lpit_driver.o [2]
LPIT_SetStartOnTriggerCmd
                             0x8077   0x3e  Code  Lc  lpit_driver.o [2]
LPIT_SetStopOnInterruptCmd
                             0x8039   0x3e  Code  Lc  lpit_driver.o [2]
LPIT_SetTimerChannelChainCmd
                             0x80b5   0x3c  Code  Lc  lpit_driver.o [2]
LPIT_SetTimerChannelModeCmd
                             0x7f27   0x34  Code  Lc  lpit_driver.o [2]
LPIT_SetTimerPeriodByCount
                             0x7f01    0xa  Code  Lc  lpit_driver.o [2]
LPIT_SetTimerRunInDebugCmd
                             0x80f1   0x24  Code  Lc  lpit_driver.o [2]
LPIT_SetTimerRunInDozeCmd
                             0x8115   0x24  Code  Lc  lpit_driver.o [2]
LPIT_SetTriggerSelectCmd
                             0x7f91   0x34  Code  Lc  lpit_driver.o [2]
LPIT_SetTriggerSourceCmd
                             0x7fc5   0x36  Code  Lc  lpit_driver.o [2]
LPIT_StartTimerChannels
                             0x7ef9    0x8  Code  Lc  lpit_driver.o [2]
LPSPI0_IRQHandler          0x1'90e5    0xa  Code  ??  lpspi_irq.o [2]
LPSPI1_IRQHandler          0x1'90ef    0xa  Code  ??  lpspi_irq.o [2]
LPSPI_ClearContCBit        0x1'3a41    0xa  Code  Lc  lpspi_master_driver.o [2]
LPSPI_ClearContCBit        0x1'4605    0xa  Code  Lc  lpspi_shared_function.o [2]
LPSPI_ClearRxmaskBit       0x1'3a2b    0xa  Code  Lc  lpspi_master_driver.o [2]
LPSPI_ClearStatusFlag      0x1'4399   0x28  Code  ??  lpspi_hw_access.o [2]
LPSPI_DRV_DisableTEIEInterrupts
                           0x1'476f   0x30  Code  ??  lpspi_shared_function.o [2]
LPSPI_DRV_FillupTxBuffer
                           0x1'4637   0xb4  Code  ??  lpspi_shared_function.o [2]
LPSPI_DRV_IRQHandler       0x1'460f   0x28  Code  ??  lpspi_shared_function.o [2]
LPSPI_DRV_MasterAbortTransfer
                           0x1'3d6d   0x52  Code  ??  lpspi_master_driver.o [2]
LPSPI_DRV_MasterCompleteDMATransfer
                           0x1'407f   0x42  Code  Lc  lpspi_master_driver.o [2]
LPSPI_DRV_MasterCompleteRX
                           0x1'40c1   0x1c  Code  Lc  lpspi_master_driver.o [2]
LPSPI_DRV_MasterCompleteTransfer
                           0x1'3ffd   0x82  Code  Lc  lpspi_master_driver.o [2]
LPSPI_DRV_MasterConfigureBus
                           0x1'3b35  0x130  Code  ??  lpspi_master_driver.o [2]
LPSPI_DRV_MasterIRQHandler
                           0x1'40e1   0xc8  Code  ??  lpspi_master_driver.o [2]
LPSPI_DRV_MasterInit       0x1'3a67   0xc0  Code  ??  lpspi_master_driver.o [2]
LPSPI_DRV_MasterStartTransfer
                           0x1'3dcd  0x22a  Code  Lc  lpspi_master_driver.o [2]
LPSPI_DRV_MasterTransferBlocking
                           0x1'3c71   0xf8  Code  ??  lpspi_master_driver.o [2]
LPSPI_DRV_ReadRXBuffer     0x1'46eb   0x84  Code  ??  lpspi_shared_function.o [2]
LPSPI_DRV_SlaveAbortTransfer
                           0x1'5065   0x9c  Code  ??  lpspi_slave_driver.o [2]
LPSPI_DRV_SlaveIRQHandler
                           0x1'4f73   0xec  Code  ??  lpspi_slave_driver.o [2]
LPSPI_Disable              0x1'4357   0x1e  Code  ??  lpspi_hw_access.o [2]
LPSPI_Enable               0x1'39ad    0xa  Code  Lc  lpspi_master_driver.o [2]
LPSPI_GetFifoSizes         0x1'39b7   0x10  Code  Lc  lpspi_master_driver.o [2]
LPSPI_GetStatusFlag        0x1'39e1    0xc  Code  Lc  lpspi_master_driver.o [2]
LPSPI_GetStatusFlag        0x1'4f29    0xc  Code  Lc  lpspi_slave_driver.o [2]
LPSPI_Init                 0x1'434d    0xa  Code  ??  lpspi_hw_access.o [2]
LPSPI_IsMaster             0x1'45c1    0xa  Code  Lc  lpspi_shared_function.o [2]
LPSPI_ReadData             0x1'45ef    0x4  Code  Lc  lpspi_shared_function.o [2]
LPSPI_ReadRxCount          0x1'45fb    0xa  Code  Lc  lpspi_shared_function.o [2]
LPSPI_ReadTxCount          0x1'45f3    0x8  Code  Lc  lpspi_shared_function.o [2]
LPSPI_SetBaudRate          0x1'441f  0x13e  Code  ??  lpspi_hw_access.o [2]
LPSPI_SetBaudRateDivisor
                           0x1'455d   0x10  Code  ??  lpspi_hw_access.o [2]
LPSPI_SetContCBit          0x1'3a4b    0xc  Code  Lc  lpspi_master_driver.o [2]
LPSPI_SetDelay             0x1'4335   0x18  Code  Lc  lpspi_hw_access.o [2]
LPSPI_SetFlushFifoCmd      0x1'4385   0x14  Code  ??  lpspi_hw_access.o [2]
LPSPI_SetIntMode           0x1'39ed   0x20  Code  Lc  lpspi_master_driver.o [2]
LPSPI_SetIntMode           0x1'45cb   0x20  Code  Lc  lpspi_shared_function.o [2]
LPSPI_SetIntMode           0x1'4f35   0x20  Code  Lc  lpspi_slave_driver.o [2]
LPSPI_SetMasterSlaveMode
                           0x1'4375   0x10  Code  ??  lpspi_hw_access.o [2]
LPSPI_SetPcsPolarityMode
                           0x1'43c1   0x22  Code  ??  lpspi_hw_access.o [2]
LPSPI_SetPinConfigMode     0x1'43e3   0x2e  Code  ??  lpspi_hw_access.o [2]
LPSPI_SetRxDmaCmd          0x1'3a1b   0x10  Code  Lc  lpspi_master_driver.o [2]
LPSPI_SetRxDmaCmd          0x1'4f63   0x10  Code  Lc  lpspi_slave_driver.o [2]
LPSPI_SetRxWatermarks      0x1'39c7    0xe  Code  Lc  lpspi_master_driver.o [2]
LPSPI_SetRxmskBit          0x1'3a35    0xc  Code  Lc  lpspi_master_driver.o [2]
LPSPI_SetSamplingPoint     0x1'3a57   0x10  Code  Lc  lpspi_master_driver.o [2]
LPSPI_SetTxCommandReg      0x1'456d   0x4a  Code  ??  lpspi_hw_access.o [2]
LPSPI_SetTxDmaCmd          0x1'3a0d    0xe  Code  Lc  lpspi_master_driver.o [2]
LPSPI_SetTxDmaCmd          0x1'4f55    0xe  Code  Lc  lpspi_slave_driver.o [2]
LPSPI_SetTxWatermarks      0x1'39d5    0xc  Code  Lc  lpspi_master_driver.o [2]
LPSPI_WriteData            0x1'45eb    0x4  Code  Lc  lpspi_shared_function.o [2]
LPTMR0_IRQHandler          0x1'9271         Code  Wk  startup_S32K118.o [1]
LPTMR_DRV_GetCounterValueByCount
                             0x8b61   0x24  Code  ??  lptmr_driver.o [2]
LPTMR_DRV_Init               0x894f   0x46  Code  ??  lptmr_driver.o [2]
LPTMR_DRV_SetConfig          0x89ad  0x1ae  Code  ??  lptmr_driver.o [2]
LPTMR_DRV_StartCounter       0x8b8d   0x5e  Code  ??  lptmr_driver.o [2]
LPTMR_Enable                 0x8687   0x22  Code  Lc  lptmr_driver.o [2]
LPTMR_GetBypass              0x86d3   0x26  Code  Lc  lptmr_driver.o [2]
LPTMR_GetClockSelect         0x872d   0x1e  Code  Lc  lptmr_driver.o [2]
LPTMR_GetCounterValue        0x8797   0x1e  Code  Lc  lptmr_driver.o [2]
LPTMR_GetWorkMode            0x8635   0x28  Code  Lc  lptmr_driver.o [2]
LPTMR_Init                   0x9681   0x26  Code  ??  lptmr_hw_access.o [2]
LPTMR_SetBypass              0x86f9   0x34  Code  Lc  lptmr_driver.o [2]
LPTMR_SetClockSelect         0x874b   0x28  Code  Lc  lptmr_driver.o [2]
LPTMR_SetCompareValue        0x8773   0x24  Code  Lc  lptmr_driver.o [2]
LPTMR_SetDmaRequest          0x853d   0x36  Code  Lc  lptmr_driver.o [2]
LPTMR_SetFreeRunning         0x8601   0x34  Code  Lc  lptmr_driver.o [2]
LPTMR_SetInterrupt           0x8579   0x34  Code  Lc  lptmr_driver.o [2]
LPTMR_SetPinPolarity         0x85d7   0x2a  Code  Lc  lptmr_driver.o [2]
LPTMR_SetPinSelect           0x85ad   0x2a  Code  Lc  lptmr_driver.o [2]
LPTMR_SetPrescaler           0x86a9   0x2a  Code  Lc  lptmr_driver.o [2]
LPTMR_SetWorkMode            0x865d   0x2a  Code  Lc  lptmr_driver.o [2]
LPUART0_RxTx_IRQHandler
                           0x1'9271         Code  Wk  startup_S32K118.o [1]
LPUART1_RxTx_IRQHandler
                           0x1'9271         Code  Wk  startup_S32K118.o [1]
LPUART_ClearStatusFlag       0x6065  0x100  Code  ??  lpuart_hw_access.o [2]
LPUART_DRV_SetBaudRate       0x6371  0x130  Code  ??  lpuart_driver.o [2]
LPUART_EnableBothEdgeSamplingCmd
                             0x6361    0xc  Code  Lc  lpuart_driver.o [2]
LPUART_GetIntMode            0x5fc9   0x4e  Code  ??  lpuart_hw_access.o [2]
LPUART_GetRxDataPolarity
                             0x5273    0xc  Code  Lc  lin_lpuart_driver.o [2]
LPUART_GetStatusFlag         0x6017   0x4e  Code  ??  lpuart_hw_access.o [2]
LPUART_Getchar               0x5257   0x1c  Code  Lc  lin_lpuart_driver.o [2]
LPUART_Init                  0x5eb9   0x1a  Code  ??  lpuart_hw_access.o [2]
LPUART_Putchar               0x5251    0x6  Code  Lc  lin_lpuart_driver.o [2]
LPUART_SetBaudRateDivisor
                             0x6309   0x2e  Code  Lc  lpuart_driver.o [2]
LPUART_SetBitCountPerChar
                             0x5ed3   0x32  Code  ??  lpuart_hw_access.o [2]
LPUART_SetBreakCharDetectLength
                             0x52ab   0x10  Code  Lc  lin_lpuart_driver.o [2]
LPUART_SetBreakCharTransmitLength
                             0x529b   0x10  Code  Lc  lin_lpuart_driver.o [2]
LPUART_SetIntMode            0x5f2f   0x8e  Code  ??  lpuart_hw_access.o [2]
LPUART_SetOversamplingRatio
                             0x6337   0x2a  Code  Lc  lpuart_driver.o [2]
LPUART_SetParityMode         0x5f05   0x2a  Code  ??  lpuart_hw_access.o [2]
LPUART_SetReceiverCmd        0x5211   0x30  Code  Lc  lin_lpuart_driver.o [2]
LPUART_SetRxDataPolarity
                             0x527f   0x1c  Code  Lc  lin_lpuart_driver.o [2]
LPUART_SetStopBitCount       0x5241   0x10  Code  Lc  lin_lpuart_driver.o [2]
LPUART_SetTransmitterCmd
                             0x51e1   0x30  Code  Lc  lin_lpuart_driver.o [2]
LTRXT_RxDiag_DataPtr    0x2000'3770    0x8  Data  ??  LINTP_Srv.o [2]
LTRXT_TxDiag_DataPtr    0x2000'3778    0x8  Data  ??  LINTP_Srv.o [2]
LearnAdap_Mdl                0xd79d  0x1ce  Code  ??  LearnAdap_Mdl.o [2]
LearnAdap_Mdl_B         0x2000'358c    0x8  Data  ??  LearnAdap_Mdl.o [2]
LearnAdap_Mdl_ConstB         0x964a    0x2  Data  ??  LearnAdap_Mdl_data.o [2]
LearnAdap_Mdl_DW        0x2000'3594    0x4  Data  ??  LearnAdap_Mdl.o [2]
LearnAdap_Mdl_Init           0xd795    0x8  Code  ??  LearnAdap_Mdl.o [2]
LearnAdap_Mdl_MdlrefDW  0x2000'3588    0x4  Data  ??  LearnAdap_Mdl.o [2]
LearnAdap_Mdl_Subsystem
                             0xd789    0xc  Code  ??  LearnAdap_Mdl.o [2]
LearnAdap_Mdl_initialize
                             0xd96b    0x6  Code  ??  LearnAdap_Mdl.o [2]
LearnAdap_newSoftStop   0x2000'427e    0x2  Data  ??  LearnAdap_Exp.o [2]
LearnAdap_newSoftStop_Const
                        0x2000'42a4    0x2  Data  Lc  nvmman_conf.o [2]
Lib_CycleCounter_AZtqVsyP
                           0x1'09db   0x72  Code  ??  Lib_CycleCounter_AZtqVsyP.o [2]
Lib_CycleCounter_AZtqVsyP_Init
                           0x1'09d5    0x6  Code  ??  Lib_CycleCounter_AZtqVsyP.o [2]
Lib_DTCDet_t33BAz08        0x1'0abd  0x13a  Code  ??  Lib_DTCDet_t33BAz08.o [2]
Lib_DTCDet_t33BAz08_Init
                           0x1'0aa5   0x18  Code  ??  Lib_DTCDet_t33BAz08.o [2]
Lin_Global_Srv_NAD      0x2000'42f6    0x1  Data  Lc  LINTP_Srv.o [2]
Lin_Srv_Get_NAD              0x76c3    0x6  Code  ??  LINTP_Srv.o [2]
Lin_TpDiag_FuncTbl_Req  0x2000'0544    0x8  Data  Lc  LINTP_Srv.o [2]
Lin_TpDiag_FuncTbl_Res  0x2000'054c    0x8  Data  Lc  LINTP_Srv.o [2]
Lin_Uds_Srv_FuncTbl     0x2000'0538    0x8  Data  Lc  LINTP.o [2]
Lin_iTpBuff             0x2000'3644  0x104  Data  Lc  LINTP.o [2]
Lin_iTpCFSqNum          0x2000'42eb    0x1  Data  Lc  LINTP.o [2]
Lin_iTpLen              0x2000'428c    0x2  Data  Lc  LINTP.o [2]
Lin_iTpLenRx            0x2000'428e    0x2  Data  Lc  LINTP.o [2]
Lin_iTpLenTx            0x2000'4290    0x2  Data  Lc  LINTP.o [2]
Lin_iTpNodeNotResp      0x2000'42ea    0x1  Data  ??  LINTP.o [2]
Lin_iTpNodeRespReq      0x2000'42e8    0x1  Data  Lc  LINTP.o [2]
Lin_iTpRespReq          0x2000'42e6    0x1  Data  Lc  LINTP.o [2]
Lin_iTpRespReq_Enabled  0x2000'42ee    0x1  Data  ??  LINTP.o [2]
Lin_iTpRespReq_Srv      0x2000'42ed    0x1  Data  Lc  LINTP.o [2]
Lin_iTpState            0x2000'42e7    0x1  Data  ??  LINTP.o [2]
Lin_iTpTmrAs            0x2000'3748    0x4  Data  Lc  LINTP.o [2]
Lin_iTpTmrAsLim         0x2000'374c    0x4  Data  Lc  LINTP.o [2]
Lin_iTpTmrCr            0x2000'3750    0x4  Data  Lc  LINTP.o [2]
Lin_iTpTmrCrLim         0x2000'3754    0x4  Data  Lc  LINTP.o [2]
Lin_iTpTmrP2            0x2000'3758    0x4  Data  Lc  LINTP.o [2]
Lin_iTpTmrP2LimMax      0x2000'3760    0x4  Data  Lc  LINTP.o [2]
Lin_iTpTmrP2LimMin      0x2000'375c    0x4  Data  Lc  LINTP.o [2]
Lin_iTpTxCFSqNum        0x2000'42ec    0x1  Data  Lc  LINTP.o [2]
Lin_iTpTxIdx            0x2000'4292    0x2  Data  Lc  LINTP.o [2]
Lin_iTpUDSRespReq       0x2000'42e9    0x1  Data  Lc  LINTP.o [2]
MOSFETTempMon_Mdl            0xda15  0x1f8  Code  ??  MOSFETTempMon_Mdl.o [2]
MOSFETTempMon_Mdl_B     0x2000'429e    0x2  Data  ??  MOSFETTempMon_Mdl.o [2]
MOSFETTempMon_Mdl_DW    0x2000'37fc   0xd4  Data  ??  MOSFETTempMon_Mdl.o [2]
MOSFETTempMon_Mdl_Init       0xda0d    0x8  Code  ??  MOSFETTempMon_Mdl.o [2]
MOSFETTempMon_Mdl_MdlrefDW
                        0x2000'37f8    0x4  Data  ??  MOSFETTempMon_Mdl.o [2]
MOSFETTempMon_Mdl_determineMOSFETTemp
                             0xd98d   0x80  Code  ??  MOSFETTempMon_Mdl.o [2]
MOSFETTempMon_Mdl_initialize
                             0xdc0d    0x6  Code  ??  MOSFETTempMon_Mdl.o [2]
MtrCtrl_Mdl                  0xfc9f   0xde  Code  ??  MtrCtrl_Mdl.o [2]
MtrCtrl_Mdl_ActivateMotor
                             0xfc81   0x1e  Code  Lc  MtrCtrl_Mdl.o [2]
MtrCtrl_Mdl_B           0x2000'38d4    0x4  Data  ??  MtrCtrl_Mdl.o [2]
MtrCtrl_Mdl_ConstB         0x1'399a    0x2  Data  ??  MtrCtrl_Mdl_data.o [2]
MtrCtrl_Mdl_DW          0x2000'38d8    0x4  Data  ??  MtrCtrl_Mdl.o [2]
MtrCtrl_Mdl_MdlrefDW    0x2000'38d0    0x4  Data  ??  MtrCtrl_Mdl.o [2]
MtrCtrl_Mdl_initialize       0xfd7d    0x6  Code  ??  MtrCtrl_Mdl.o [2]
MtrMdl_Mdl                   0xb861  0x684  Code  ??  MtrMdl_Mdl.o [2]
MtrMdl_Mdl_B            0x2000'38e4   0x20  Data  ??  MtrMdl_Mdl.o [2]
MtrMdl_Mdl_ConstB          0x1'9277    0x1  Data  ??  MtrMdl_Mdl_data.o [2]
MtrMdl_Mdl_DW           0x2000'3904   0x6c  Data  ??  MtrMdl_Mdl.o [2]
MtrMdl_Mdl_Init              0xb805   0x38  Code  ??  MtrMdl_Mdl.o [2]
MtrMdl_Mdl_MdlrefDW     0x2000'38e0    0x4  Data  ??  MtrMdl_Mdl.o [2]
MtrMdl_Mdl_calculateTemperature
                             0xb6fd   0xde  Code  ??  MtrMdl_Mdl.o [2]
MtrMdl_Mdl_determineStartUpDistance
                             0xb7db   0x2a  Code  Lc  MtrMdl_Mdl.o [2]
MtrMdl_Mdl_initialize        0xbee5    0x6  Code  ??  MtrMdl_Mdl.o [2]
MtrMdl_TEstCaseTemp     0x2000'42a0    0x2  Data  ??  MtrMdl_Exp.o [2]
MtrMdl_TEstMtrTemp      0x2000'42a2    0x2  Data  ??  MtrMdl_Exp.o [2]
MtrMdl_isEstCaseTempVld
                        0x2000'42fa    0x1  Data  ??  MtrMdl_Exp.o [2]
MtrMdl_isEstMtrTempVld  0x2000'42fb    0x1  Data  ??  MtrMdl_Exp.o [2]
MtrMdl_pwmVoltage       0x2000'42b2    0x2  Data  ??  SCUMain.o [2]
MtrMdl_stCalcForTorq    0x2000'38dc    0x4  Data  ??  MtrMdl_Exp.o [2]
NMI_Handler                0x1'8ecf         Code  Wk  startup_S32K118.o [1]
NVMAccessState          0x2000'430d    0x1  Data  ??  SCUMain.o [2]
NVMMan_bGetCRCBlockStatus
                             0x2f8d   0x18  Code  ??  nvmman.o [2]
NVMMan_enInit                0x2ee1   0xa8  Code  ??  nvmman.o [2]
NVMMan_enInitDataFromNVM
                             0x2df3   0x8c  Code  Lc  nvmman.o [2]
NVMMan_enRequestNVMWrite_new
                             0x2e7f   0x62  Code  ??  nvmman.o [2]
NVMMan_enSaveAllNVMData
                             0x2fa9   0x46  Code  ??  nvmman.o [2]
NVMMan_stConfig         0x2000'0560  0x230  Data  ??  nvmman_conf.o [2]
OSIF_SemaCreate              0x62d5   0x24  Code  ??  osif_baremetal.o [2]
OSIF_SemaPost                0x62a1   0x34  Code  ??  osif_baremetal.o [2]
OSIF_SemaWait                0x6225   0x7c  Code  ??  osif_baremetal.o [2]
PCC_GetClockMode              0xa47   0x22  Code  Lc  clock_S32K1xx.o [2]
PCC_GetClockSourceSel         0xa69   0x18  Code  Lc  clock_S32K1xx.o [2]
PCC_GetDividerSel             0xa99   0x16  Code  Lc  clock_S32K1xx.o [2]
PCC_GetFracValueSel           0xa81   0x18  Code  Lc  clock_S32K1xx.o [2]
PCC_SetClockMode              0x9f1   0x56  Code  Lc  clock_S32K1xx.o [2]
PCC_SetPeripheralClockControl
                              0x9ad   0x44  Code  Lc  clock_S32K1xx.o [2]
PDB0_IRQHandler            0x1'9271         Code  Wk  startup_S32K118.o [1]
PDB_ConfigTimer            0x1'37a9   0x92  Code  ??  pdb_hw_access.o [2]
PDB_DRV_ConfigAdcPreTrigger
                           0x1'24b9   0x74  Code  ??  pdb_driver.o [2]
PDB_DRV_Disable            0x1'2453   0x22  Code  ??  pdb_driver.o [2]
PDB_DRV_Enable             0x1'2431   0x22  Code  ??  pdb_driver.o [2]
PDB_DRV_Init               0x1'23b7   0x7a  Code  ??  pdb_driver.o [2]
PDB_DRV_Init::s_pdbClkNames
                           0x1'9279    0x1  Data  Lc  pdb_driver.o [2]
PDB_DRV_LoadValuesCmd      0x1'2497   0x22  Code  ??  pdb_driver.o [2]
PDB_DRV_SoftTriggerCmd     0x1'2475   0x22  Code  ??  pdb_driver.o [2]
PDB_Disable                0x1'237b   0x1e  Code  Lc  pdb_driver.o [2]
PDB_Disable                0x1'36a3   0x1e  Code  Lc  pdb_hw_access.o [2]
PDB_Enable                 0x1'235d   0x1e  Code  Lc  pdb_driver.o [2]
PDB_Enable                 0x1'3685   0x1e  Code  Lc  pdb_hw_access.o [2]
PDB_Init                   0x1'372b   0x7e  Code  ??  pdb_hw_access.o [2]
PDB_SetAdcPreTriggerBackToBackEnable
                           0x1'3845   0x5a  Code  ??  pdb_hw_access.o [2]
PDB_SetAdcPreTriggerDelayValue
                           0x1'36df   0x4c  Code  Lc  pdb_hw_access.o [2]
PDB_SetAdcPreTriggerEnable
                           0x1'38f9   0x4e  Code  ??  pdb_hw_access.o [2]
PDB_SetAdcPreTriggerOutputEnable
                           0x1'389f   0x5a  Code  ??  pdb_hw_access.o [2]
PDB_SetLoadValuesCmd       0x1'2399   0x1e  Code  Lc  pdb_driver.o [2]
PDB_SetLoadValuesCmd       0x1'36c1   0x1e  Code  Lc  pdb_hw_access.o [2]
PDB_SetSoftTriggerCmd      0x1'233d   0x20  Code  Lc  pdb_driver.o [2]
PIDDID_IoCtrl              0x1'70b9   0x5a  Code  ??  SCUMain.o [2]
PIDDID_IoCtrl_Func         0x1'7531   0xe6  Code  ??  PIDDID_IoCtrl_Func.o [2]
PIDDID_IoCtrl_Func_IOControlShell
                           0x1'7511   0x20  Code  Lc  PIDDID_IoCtrl_Func.o [2]
PIDDID_IoCtrl_Func_MdlrefDW
                        0x2000'3c1c    0x4  Data  ??  PIDDID_IoCtrl_Func.o [2]
PIDDID_IoCtrl_Func_initialize
                           0x1'7617    0x6  Code  ??  PIDDID_IoCtrl_Func.o [2]
PIDDID_IoCtrlbyID          0x1'8fc0   0x24  Data  ??  PIDDID_Cfg.o [2]
PIDDID_Mdl                   0xfd99  0x356  Code  ??  PIDDID_Mdl.o [2]
PIDDID_Mdl_B            0x2000'3c24    0x8  Data  ??  PIDDID_Mdl.o [2]
PIDDID_Mdl_DW           0x2000'3c2c   0x10  Data  ??  PIDDID_Mdl.o [2]
PIDDID_Mdl_MdlrefDW     0x2000'3c20    0x4  Data  ??  PIDDID_Mdl.o [2]
PIDDID_Mdl_initialize      0x1'00f9    0x6  Code  ??  PIDDID_Mdl.o [2]
PIDDID_RdWrDID             0x1'83d0  0x190  Data  ??  PIDDID_Cfg.o [2]
PIDDID_ReadDID             0x1'7113  0x134  Code  ??  SCUMain.o [2]
PIDDID_ReadDID_Func        0x1'53e1  0x934  Code  ??  PIDDID_ReadDID_Func.o [2]
PIDDID_ReadDID_Func_Init
                           0x1'53d1   0x10  Code  ??  PIDDID_ReadDID_Func.o [2]
PIDDID_ReadDID_Func_MdlrefDW
                        0x2000'3c3c    0x4  Data  ??  PIDDID_ReadDID_Func.o [2]
PIDDID_ReadDID_Func_initialize
                           0x1'5d15    0x6  Code  ??  PIDDID_ReadDID_Func.o [2]
PIDDID_RoutineCtrl         0x1'7247   0x5a  Code  ??  SCUMain.o [2]
PIDDID_RoutineCtrl_Func
                           0x1'6c8d  0x406  Code  ??  PIDDID_RoutineCtrl_Func.o [2]
PIDDID_RoutineCtrl_Func_DW
                        0x2000'3c44    0x4  Data  ??  PIDDID_RoutineCtrl_Func.o [2]
PIDDID_RoutineCtrl_Func_Init
                           0x1'6c79   0x14  Code  ??  PIDDID_RoutineCtrl_Func.o [2]
PIDDID_RoutineCtrl_Func_MdlrefDW
                        0x2000'3c40    0x4  Data  ??  PIDDID_RoutineCtrl_Func.o [2]
PIDDID_RoutineCtrl_Func_RoutineCtrlShell
                           0x1'6c15   0x60  Code  Lc  PIDDID_RoutineCtrl_Func.o [2]
PIDDID_RoutineCtrl_Func_initialize
                           0x1'7093    0x6  Code  ??  PIDDID_RoutineCtrl_Func.o [2]
PIDDID_Rtn                 0x1'8bb4   0x5c  Data  ??  PIDDID_Cfg.o [2]
PIDDID_WrResp           0x2000'4300    0x1  Data  ??  PIDDID_Exp.o [2]
PIDDID_WriteDID            0x1'72a1   0xa0  Code  ??  SCUMain.o [2]
PIDDID_WriteDID_Func       0x1'76b1  0x2aa  Code  ??  PIDDID_WriteDID_Func.o [2]
PIDDID_WriteDID_Func_B  0x2000'3c4c    0xc  Data  ??  PIDDID_WriteDID_Func.o [2]
PIDDID_WriteDID_Func_DW
                        0x2000'4308    0x1  Data  ??  PIDDID_WriteDID_Func.o [2]
PIDDID_WriteDID_Func_Init
                           0x1'762d   0x7e  Code  ??  PIDDID_WriteDID_Func.o [2]
PIDDID_WriteDID_Func_MdlrefDW
                        0x2000'3c48    0x4  Data  ??  PIDDID_WriteDID_Func.o [2]
PIDDID_WriteDID_Func_initialize
                           0x1'7969    0x6  Code  ??  PIDDID_WriteDID_Func.o [2]
PIDDID_WriteID          0x2000'42aa    0x2  Data  ??  PIDDID_Exp.o [2]
PIDDID_hDiagToPos       0x2000'42ac    0x2  Data  ??  PIDDID_Exp.o [2]
PIDDID_isReqUnlearn     0x2000'4301    0x1  Data  ??  PIDDID_Exp.o [2]
PIDDID_isRtnLearnBusy   0x2000'4302    0x1  Data  ??  PIDDID_Exp.o [2]
PIDDID_isRtnToPosBusy   0x2000'4303    0x1  Data  ??  PIDDID_Exp.o [2]
PIDDID_stDiagHallPwrCmd
                        0x2000'4304    0x1  Data  ??  PIDDID_Exp.o [2]
PIDDID_stDiagRlyCmd     0x2000'4305    0x1  Data  ??  PIDDID_Exp.o [2]
PIDDID_stDiagSwchCmd    0x2000'4306    0x1  Data  ??  PIDDID_Exp.o [2]
PIDDID_stLearnResult    0x2000'4307    0x1  Data  ??  PIDDID_Exp.o [2]
PINS_DRV_ClearPins         0x1'2e75    0x8  Code  ??  pins_driver.o [2]
PINS_DRV_Init              0x1'2e4f   0x1e  Code  ??  pins_driver.o [2]
PINS_DRV_ReadPins          0x1'2e7d    0x8  Code  ??  pins_driver.o [2]
PINS_DRV_SetPins           0x1'2e6d    0x8  Code  ??  pins_driver.o [2]
PINS_GPIO_ClearPins        0x1'2e45    0x4  Code  Lc  pins_driver.o [2]
PINS_GPIO_ReadPins         0x1'2e49    0x6  Code  Lc  pins_driver.o [2]
PINS_GPIO_SetPins          0x1'2e41    0x4  Code  Lc  pins_driver.o [2]
PINS_GPIO_WritePin         0x1'41d5   0x16  Code  Lc  pins_port_hw_access.o [2]
PINS_Init                  0x1'41eb  0x13a  Code  ??  pins_port_hw_access.o [2]
PMC_GetLpoMode                0xad5   0x18  Code  Lc  clock_S32K1xx.o [2]
PMC_SetLpoMode                0xab9   0x1c  Code  Lc  clock_S32K1xx.o [2]
PMC_SetLpoTrimValue           0xaed   0x38  Code  Lc  clock_S32K1xx.o [2]
PORT_IRQHandler            0x1'9271         Code  Wk  startup_S32K118.o [1]
PWMCtrl_Mdl                  0xaf09   0x44  Code  ??  PWMCtrl_Mdl.o [2]
PWMCtrl_Mdl_B           0x2000'3ce8    0x8  Data  ??  PWMCtrl_Mdl.o [2]
PWMCtrl_Mdl_Init             0xaef5   0x14  Code  ??  PWMCtrl_Mdl.o [2]
PWMCtrl_Mdl_MdlrefDW    0x2000'3ce4    0x4  Data  ??  PWMCtrl_Mdl.o [2]
PWMCtrl_Mdl_initialize       0xaf4d    0x6  Code  ??  PWMCtrl_Mdl.o [2]
PWMCtrl_enInit             0x1'05d1   0x22  Code  ??  PWMCtrl_Man.o [2]
PWMCtrl_enUpdateDutyCycle
                           0x1'05f3   0x52  Code  ??  PWMCtrl_Man.o [2]
PWMCtrl_vStep              0x1'0655    0x4  Code  ??  PWMCtrl_Man.o [2]
PendSV_Handler             0x1'905b         Code  Wk  startup_S32K118.o [1]
PnlOp_Mdl                    0xfbd1   0x8e  Code  ??  PnlOp_Mdl.o [2]
PnlOp_Mdl_MdlrefDW      0x2000'3c58    0x4  Data  ??  PnlOp_Mdl.o [2]
PnlOp_Mdl_initialize         0xfc5f    0x6  Code  ??  PnlOp_Mdl.o [2]
PosMon_Mdl                   0xe10d  0x178  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_B            0x2000'3c64    0xc  Data  ??  PosMon_Mdl.o [2]
PosMon_Mdl_CalculatePosition
                             0xdc2d   0x76  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_ConstB          0x1'8d1a    0x2  Data  ??  PosMon_Mdl_data.o [2]
PosMon_Mdl_DW           0x2000'3c70   0x10  Data  ??  PosMon_Mdl.o [2]
PosMon_Mdl_DetermineAndWriteState
                             0xdcc1  0x354  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_DetermineAndWriteState_Init
                             0xdcb9    0x8  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_Init              0xe0f7    0xe  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_MdlrefDW     0x2000'3c60    0x4  Data  ??  PosMon_Mdl.o [2]
PosMon_Mdl_PositionOutOfRange
                             0xe015   0x68  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_ShutdownReadyDetermination
                             0xe07d   0x28  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_UpdatePosition
                             0xe0a5   0x52  Code  ??  PosMon_Mdl.o [2]
PosMon_Mdl_exit_internal_NORMAL_OPERATION
                             0xdca3    0xa  Code  Lc  PosMon_Mdl.o [2]
PosMon_Mdl_initialize        0xe285    0x6  Code  ??  PosMon_Mdl.o [2]
PosMon_RP_AND_VLDTY     0x2000'3c5c    0x4  Data  ??  PosMon_Exp.o [2]
PosMon_RP_AND_VLDTY_Const
                        0x2000'3ab8    0x4  Data  Lc  nvmman_conf.o [2]
PosMon_isCurPosVldNvm   0x2000'430f    0x1  Data  ??  SCUMain.o [2]
PosMon_isRelearn        0x2000'4310    0x1  Data  ??  SCUMain.o [2]
RCM_IRQHandler             0x1'9271         Code  Wk  startup_S32K118.o [1]
RFDet_FRFTbl            0x2000'3cf0   0xfc  Data  ??  RefField_Exp.o [2]
RFDet_FRFTbl_Const      0x2000'3abc   0xfc  Data  Lc  nvmman_conf.o [2]
RFDet_isRFTblVld        0x2000'4309    0x1  Data  ??  RefField_Exp.o [2]
RFDet_isRFTblVldNvm     0x2000'430a    0x1  Data  ??  RefField_Exp.o [2]
RFDet_isRFTblVldNvmBak  0x2000'430b    0x1  Data  ??  RefField_Exp.o [2]
RFDet_isRFTblVldNvmBak_Const
                        0x2000'42fe    0x1  Data  Lc  nvmman_conf.o [2]
RFDet_isRFTblVldNvmSync
                        0x2000'430c    0x1  Data  ??  RefField_Exp.o [2]
RFDet_isRFTblVldNvmSync_Const
                        0x2000'42ff    0x1  Data  Lc  nvmman_conf.o [2]
RFDet_isRFTblVldNvm_Const
                        0x2000'42fd    0x1  Data  Lc  nvmman_conf.o [2]
RTC_IRQHandler             0x1'9271         Code  Wk  startup_S32K118.o [1]
RTC_Seconds_IRQHandler     0x1'9271         Code  Wk  startup_S32K118.o [1]
RW$$Base                0x2000'00c0          --   Gb  - Linker created -
RW$$Limit               0x2000'0bb4          --   Gb  - Linker created -
RefField_Mdl                 0x289d  0x3da  Code  ??  RefField_Mdl.o [2]
RefField_Mdl_B          0x2000'3df0   0x28  Data  ??  RefField_Mdl.o [2]
RefField_Mdl_DW         0x2000'3e18   0x1c  Data  ??  RefField_Mdl.o [2]
RefField_Mdl_DataWriting
                             0x20eb  0x1f4  Code  Lc  RefField_Mdl.o [2]
RefField_Mdl_Init            0x2857   0x3e  Code  ??  RefField_Mdl.o [2]
RefField_Mdl_MdlrefDW   0x2000'3dec    0x4  Data  ??  RefField_Mdl.o [2]
RefField_Mdl_NVMRW           0x1d9b  0x10a  Code  ??  RefField_Mdl.o [2]
RefField_Mdl_NVM_RF          0x1d85   0x16  Code  ??  RefField_Mdl.o [2]
RefField_Mdl_Running         0x22df  0x240  Code  Lc  RefField_Mdl.o [2]
RefField_Mdl_SyncData        0x1ee3  0x208  Code  Lc  RefField_Mdl.o [2]
RefField_Mdl_exit_internal_DataWriting
                             0x1eb3   0x30  Code  Lc  RefField_Mdl.o [2]
RefField_Mdl_exit_internal_SyncData
                             0x1ea9    0xa  Code  Lc  RefField_Mdl.o [2]
RefField_Mdl_initialize
                             0x2c77    0x6  Code  ??  RefField_Mdl.o [2]
RefField_Mdl_referenceFieldState
                             0x254f  0x308  Code  ??  RefField_Mdl.o [2]
RefField_Mdl_referenceFieldState_Init
                             0x251f   0x30  Code  ??  RefField_Mdl.o [2]
RefForce_Mdl                 0xfba9    0x8  Code  ??  RefForce_Mdl.o [2]
RefForce_Mdl_MdlrefDW   0x2000'3e34    0x4  Data  ??  RefForce_Mdl.o [2]
RefForce_Mdl_initialize
                             0xfbb1    0x6  Code  ??  RefForce_Mdl.o [2]
Region$$Table$$Base             0x0          --   ??  - Linker created -
Region$$Table$$Limit            0x0          --   ??  - Linker created -
Reset_Handler              0x1'8c6d         Code  Wk  startup_S32K118.o [1]
RespBuff                0x2000'3768    0x8  Data  ??  LINTP_NodeSrv.o [2]
RespLen                 0x2000'42f4    0x1  Data  ??  LINTP_NodeSrv.o [2]
SCG_CMU_LVD_LVWSCG_IRQHandler
                           0x1'9271         Code  Wk  startup_S32K118.o [1]
SCG_ClearFircControl          0x895    0xc  Code  Lc  clock_S32K1xx.o [2]
SCG_ClearFircLock             0x87f   0x12  Code  Lc  clock_S32K1xx.o [2]
SCG_ClearSircControl          0x7e7    0xa  Code  Lc  clock_S32K1xx.o [2]
SCG_ClearSircLock             0x7d5   0x12  Code  Lc  clock_S32K1xx.o [2]
SCG_ClearSoscControl          0x931    0xc  Code  Lc  clock_S32K1xx.o [2]
SCG_ClearSoscLock             0x90d   0x12  Code  Lc  clock_S32K1xx.o [2]
SCG_GetClockoutSourceSel
                              0x677    0xa  Code  Lc  clock_S32K1xx.o [2]
SCG_GetCurrentBusClockDividerRatio
                              0x695    0xa  Code  Lc  clock_S32K1xx.o [2]
SCG_GetCurrentCoreClockDividerRatio
                              0x68b    0xa  Code  Lc  clock_S32K1xx.o [2]
SCG_GetCurrentSlowClockDividerRatio
                              0x69f    0x8  Code  Lc  clock_S32K1xx.o [2]
SCG_GetCurrentSystemClockSource
                              0x681    0xa  Code  Lc  clock_S32K1xx.o [2]
SCG_GetFircFirstAsyncDivider
                              0x6fb    0xc  Code  Lc  clock_S32K1xx.o [2]
SCG_GetFircRange              0x873    0xc  Code  Lc  clock_S32K1xx.o [2]
SCG_GetFircSecondAsyncDivider
                              0x707    0xe  Code  Lc  clock_S32K1xx.o [2]
SCG_GetFircStatus             0x863   0x10  Code  Lc  clock_S32K1xx.o [2]
SCG_GetFircSystemClockMode
                              0x84b   0x18  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSircFirstAsyncDivider
                              0x72b    0xc  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSircRange              0x7c7    0xe  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSircSecondAsyncDivider
                              0x737    0xe  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSircStatus             0x7b7   0x10  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSircSystemClockMode
                              0x79f   0x18  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSoscFirstAsyncDivider
                              0x75b    0xc  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSoscSecondAsyncDivider
                              0x767    0xe  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSoscStatus             0x8fd   0x10  Code  Lc  clock_S32K1xx.o [2]
SCG_GetSoscSystemClockMode
                              0x8e5   0x18  Code  Lc  clock_S32K1xx.o [2]
SCG_SetClockoutSourceSel
                              0x78b   0x14  Code  Lc  clock_S32K1xx.o [2]
SCG_SetFircAsyncConfig        0x715   0x16  Code  Lc  clock_S32K1xx.o [2]
SCG_SetFircConfiguration
                              0x8a1    0xe  Code  Lc  clock_S32K1xx.o [2]
SCG_SetFircControl            0x8af   0x36  Code  Lc  clock_S32K1xx.o [2]
SCG_SetRunClockControl        0x6a7   0x2a  Code  Lc  clock_S32K1xx.o [2]
SCG_SetSircAsyncConfig        0x745   0x16  Code  Lc  clock_S32K1xx.o [2]
SCG_SetSircConfiguration
                              0x7f1   0x10  Code  Lc  clock_S32K1xx.o [2]
SCG_SetSircControl            0x801   0x4a  Code  Lc  clock_S32K1xx.o [2]
SCG_SetSoscAsyncConfig        0x775   0x16  Code  Lc  clock_S32K1xx.o [2]
SCG_SetSoscConfiguration
                              0x93d   0x22  Code  Lc  clock_S32K1xx.o [2]
SCG_SetSoscControl            0x95f   0x4e  Code  Lc  clock_S32K1xx.o [2]
SCG_SetVlprClockControl
                              0x6d1   0x2a  Code  Lc  clock_S32K1xx.o [2]
SCUInit_enGetStatus        0x1'06c9    0x6  Code  ??  SCUInit.o [2]
SCUInit_enInitHW           0x1'0659   0x70  Code  ??  SCUInit.o [2]
SCUMain_B               0x2000'3e58  0x228  Data  ??  SCUMain.o [2]
SCUMain_DW              0x2000'42b4    0x2  Data  ??  SCUMain.o [2]
SCUMain_M                  0x1'9264    0x4  Data  ??  SCUMain.o [2]
SCUMain_M_              0x2000'4080    0x4  Data  Lc  SCUMain.o [2]
SCUMain_TaskSchedular_Mdl
                             0xa489  0xa18  Code  ??  SCUMain_TaskSchedular_Mdl.o [2]
SCUMain_TaskSchedular_Mdl_Init
                             0xa431   0x52  Code  ??  SCUMain_TaskSchedular_Mdl.o [2]
SCUMain_hallInterrupt      0x1'7349   0x32  Code  ??  SCUMain.o [2]
SCUMain_initialize         0x1'738d  0x136  Code  ??  SCUMain.o [2]
SCUMain_vTrigger           0x1'7341    0x8  Code  ??  SCUMain.o [2]
SIM_ClearTraceClockConfig
                              0x645    0x8  Code  Lc  clock_S32K1xx.o [2]
SIM_GetClockoutDividerValue
                              0x5ad    0xa  Code  Lc  clock_S32K1xx.o [2]
SIM_GetClockoutSelectorValue
                              0x5b7    0xa  Code  Lc  clock_S32K1xx.o [2]
SIM_GetClockoutStatus         0x5a1    0xc  Code  Lc  clock_S32K1xx.o [2]
SIM_GetDmaClockGate           0x4f3   0x14  Code  Lc  clock_S32K1xx.o [2]
SIM_GetEimClockGate           0x48b   0x14  Code  Lc  clock_S32K1xx.o [2]
SIM_GetErmClockGate           0x4bf   0x14  Code  Lc  clock_S32K1xx.o [2]
SIM_GetFtm0ExternalClkPinMode
                              0x56d    0xa  Code  Lc  clock_S32K1xx.o [2]
SIM_GetFtm1ExternalClkPinMode
                              0x577    0xa  Code  Lc  clock_S32K1xx.o [2]
SIM_GetLpo1KStatus            0x58d    0xa  Code  Lc  clock_S32K1xx.o [2]
SIM_GetLpo32KStatus           0x581    0xc  Code  Lc  clock_S32K1xx.o [2]
SIM_GetLpoClkSelectorValue
                              0x597    0xa  Code  Lc  clock_S32K1xx.o [2]
SIM_GetMpuClockGate           0x527   0x14  Code  Lc  clock_S32K1xx.o [2]
SIM_GetMscmClockGate          0x55b   0x12  Code  Lc  clock_S32K1xx.o [2]
SIM_GetRtcClkSrc              0x421    0xa  Code  Lc  clock_S32K1xx.o [2]
SIM_SetClockout               0x5f5   0x3c  Code  Lc  clock_S32K1xx.o [2]
SIM_SetDmaClockGate           0x4d3   0x20  Code  Lc  clock_S32K1xx.o [2]
SIM_SetEimClockGate           0x46b   0x20  Code  Lc  clock_S32K1xx.o [2]
SIM_SetErmClockGate           0x49f   0x20  Code  Lc  clock_S32K1xx.o [2]
SIM_SetExtPinSourceFtm        0x5c1   0x34  Code  Lc  clock_S32K1xx.o [2]
SIM_SetLpoClocks              0x42b   0x40  Code  Lc  clock_S32K1xx.o [2]
SIM_SetMpuClockGate           0x507   0x20  Code  Lc  clock_S32K1xx.o [2]
SIM_SetMscmClockGate          0x53b   0x20  Code  Lc  clock_S32K1xx.o [2]
SIM_SetTraceClockConfig
                              0x64d   0x2a  Code  Lc  clock_S32K1xx.o [2]
SIM_SetTraceClockSource
                              0x631   0x14  Code  Lc  clock_S32K1xx.o [2]
SMC_GetCurrentRunningMode
                              0xb25    0x6  Code  Lc  clock_S32K1xx.o [2]
ST_bNVMManReady         0x2000'42fc    0x1  Data  Lc  nvmman.o [2]
ST_enFtm0StateStruct    0x2000'3508   0x64  Data  Lc  ICMon.o [2]
ST_enFtm1StateStruct    0x2000'3c80   0x64  Data  Lc  PWMCtrl_Man.o [2]
ST_enIniHWStatus        0x2000'0bb2    0x1  Data  Lc  SCUInit.o [2]
ST_stFlashSSDConfig     0x2000'3970   0x1c  Data  Lc  nvmman.o [2]
ST_u16AdcResults        0x2000'30c0   0x1c  Data  ??  ADCMon_Man.o [2]
ST_u32Counter1ms        0x2000'40b0    0x4  Data  Lc  TimerHwAbs.o [2]
ST_u8_1msCntr           0x2000'4314    0x1  Data  Lc  TaskSch_Man.o [2]
SVC_Handler                0x1'904f         Code  Wk  startup_S32K118.o [1]
SecuritySeedRandom      0x2000'4278    0x2  Data  Lc  ISOUDS_SA.o [2]
Serial_no               0x2000'3764    0x4  Data  ??  LINTP_NodeSrv.o [2]
StateMach_Mdl                0xaf73  0x230  Code  ??  StateMach_Mdl.o [2]
StateMach_Mdl_B         0x2000'4088    0x4  Data  ??  StateMach_Mdl.o [2]
StateMach_Mdl_DW        0x2000'408c    0x8  Data  ??  StateMach_Mdl.o [2]
StateMach_Mdl_Init           0xaf5d   0x16  Code  ??  StateMach_Mdl.o [2]
StateMach_Mdl_MdlrefDW  0x2000'4084    0x4  Data  ??  StateMach_Mdl.o [2]
StateMach_Mdl_PrevZCX   0x2000'42b6    0x2  Data  ??  StateMach_Mdl.o [2]
StateMach_Mdl_initialize
                             0xb1a3   0x10  Code  ??  StateMach_Mdl.o [2]
StateMach_isATSEnabled  0x2000'4311    0x1  Data  ??  SCUMain.o [2]
Supplier_id             0x2000'429a    0x2  Data  ??  LINTP_NodeSrv.o [2]
SwitchLog_Mdl                0xfa69    0x2  Code  ??  SwitchLog_Mdl.o [2]
SwitchLog_Mdl_MdlrefDW  0x2000'4094    0x4  Data  ??  SwitchLog_Mdl.o [2]
SwitchLog_Mdl_initialize
                             0xfa6b    0x6  Code  ??  SwitchLog_Mdl.o [2]
SysFaultReac_Mdl             0xe5bf   0x5e  Code  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_B      0x2000'409c    0x8  Data  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_CalDataFault
                             0xe2b1   0x26  Code  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_Init        0xe5b5    0xa  Code  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_MdlrefDW
                        0x2000'4098    0x4  Data  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_PrevZCX
                        0x2000'4313    0x1  Data  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_SysFaultRct
                             0xe389  0x228  Code  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_SysFaultRct_Init
                             0xe381    0x8  Code  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_determineUnlearnedReason
                             0xe2dd   0x9a  Code  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_determineUnlearnedReason_Init
                             0xe2d7    0x6  Code  ??  SysFaultReac_Mdl.o [2]
SysFaultReac_Mdl_initialize
                             0xe61d    0xc  Code  ??  SysFaultReac_Mdl.o [2]
SysFltRctn_bGetSystemFaultReaction
                             0x3097   0x10  Code  ??  SysFltRctn_Man.o [2]
SysFltRctn_stsSysFaultReaction
                        0x2000'42b8    0x2  Data  Lc  SysFltRctn_Man.o [2]
SysFltRctn_vSetSystemFaultReaction
                             0x3091    0x6  Code  ??  SysFltRctn_Man.o [2]
SysTick_Handler              0x6199    0x8  Code  ??  osif_baremetal.o [2]
SystemInit                 0x1'8a29   0x16  Code  ??  system_S32K118.o [2]
SystemSoftwareReset        0x1'8a3f   0x12  Code  ??  system_S32K118.o [2]
TLE9560                 0x2000'0b0c    0x4  Data  ??  TLE9560.o [2]
TLE9560SetFuncArray     0x2000'03f8  0x114  Data  ??  ExtDev.o [2]
TLE9560_ApplLayer_GetStatus
                           0x1'12e7   0x14  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_Init     0x1'10db  0x12e  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_deviceDriverCyclicTask
                           0x1'0fbd  0x11e  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_deviceDriverCyclicTask::u8_cyclicStatUpdateCount
                        0x2000'4318    0x1  Data  Lc  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_BUS_STAT
                           0x1'1213    0x8  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_DEV_STAT
                           0x1'1275    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_DSOV
                           0x1'12cd    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_EFF_TD_ON_OFF1
                           0x1'1295    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_EFF_TD_ON_OFF2
                           0x1'129f    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_FAM_PROD_STAT
                           0x1'12dd    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_GEN_STAT
                           0x1'1285    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_HS_OL_OC_OT_STAT
                           0x1'122b    0x8  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_SUP_STAT
                           0x1'1261    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_SWK_CDR_STAT
                           0x1'1257    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_SWK_ECNT_STAT
                           0x1'124d    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_SWK_OSC_CAL_STAT
                           0x1'1233    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_SWK_STAT
                           0x1'123d    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_TDREG
                           0x1'12bd    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_THERM_STAT
                           0x1'126b    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_TRISE_FALL1
                           0x1'12a9    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_TRISE_FALL2
                           0x1'12b3    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_WK_LVL_STAT
                           0x1'1223    0x8  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ApplLayer_update_WK_STAT
                           0x1'121b    0x8  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_FuncLayer_clrDevSts
                           0x1'1ca7   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_clrDsovSts
                           0x1'1cf7   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_clrHsOlOcOtSts
                           0x1'1cdb   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_clrSupSts
                           0x1'1c71   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_clrThermSts
                           0x1'1c8d   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_clrWkSts
                           0x1'1cc1   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_DEV_STAT
                           0x1'1be7    0xa  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_DEV_STAT_CRC_FAIL
                           0x1'1bb7   0x16  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_DEV_STAT_SPI_FAIL
                           0x1'1bd1   0x16  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_GENCTRL_CPEN
                           0x1'1349   0x14  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_SUP_STAT
                           0x1'1bf1    0xa  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_SUP_STAT_VCC1_WARN
                           0x1'1b55   0x10  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_THERM_STAT
                           0x1'1bfb    0xa  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_THERM_STAT_TPW
                           0x1'1ba7   0x10  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_THERM_STAT_TSD1
                           0x1'1b91   0x16  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_THERM_STAT_TSD2
                           0x1'1b7b   0x16  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_get_THERM_STAT_TSD2_SAFE
                           0x1'1b65   0x16  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_recoverCrc
                           0x1'1d11   0x16  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_BRAKE_PARK_BRK_EN
                           0x1'1c31   0x30  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_BUS_CTRL
                           0x1'148b   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_BUS_CTRL_LIN
                           0x1'145d   0x2e  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_CCP_BLK_BNK0
                           0x1'1745   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_CCP_BLK_BNK1
                           0x1'1769   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_CCP_BLK_BNK4
                           0x1'178d   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_CCP_BLK_BNK5
                           0x1'17ad   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_GENCTRL
                           0x1'1365   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_GENCTRL_CPEN
                           0x1'1315   0x2e  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HBMODE
                           0x1'1c0d   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HB_ICHG_BNK0
                           0x1'17ed   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HB_ICHG_BNK1
                           0x1'180d   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HB_ICHG_BNK4
                           0x1'1835   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HB_ICHG_BNK5
                           0x1'1855   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HB_ICHG_MAX
                           0x1'1879   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK0
                           0x1'1899   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HB_PCHG_INIT_BNK1
                           0x1'18b9   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HS_CTRL
                           0x1'1613   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HS_CTRL_HS1
                           0x1'15e7   0x2c  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HS_CTRL_HS2
                           0x1'15b9   0x2e  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HS_CTRL_HS3
                           0x1'1585   0x30  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HS_VDS
                           0x1'162f   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_HW_CTRL
                           0x1'13dd   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_INT_MASK
                           0x1'1669   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_LS_VDS
                           0x1'1725   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_M_S_CTRL
                           0x1'13b5   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_M_S_CTRL_MODE
                           0x1'137f   0x2a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_PWM_CTRL_BNK0
                           0x1'1689   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_PWM_CTRL_BNK1
                           0x1'16a5   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_PWM_CTRL_BNK2
                           0x1'16c5   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_PWM_CTRL_BNK3
                           0x1'16e5   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_ST_ICHG
                           0x1'17d1   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_BTL1_CTRL
                           0x1'1979   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_CAN_FD_CTRL
                           0x1'1ac5   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_CDR_CTRL
                           0x1'1b0d   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_CDR_LIMIT
                           0x1'1b31   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_CTRL
                           0x1'1959   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_DATA0_CTRL
                           0x1'1aa1   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_DATA1_CTRL
                           0x1'1a83   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_DATA2_CTRL
                           0x1'1a69   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_DATA3_CTRL
                           0x1'1a49   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_DLC_CTRL
                           0x1'1a2f   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_ID0_CTRL
                           0x1'19cd   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_ID1_CTRL
                           0x1'19a9   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_MASK_ID0_CTRL
                           0x1'1a15   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_MASK_ID1_CTRL
                           0x1'19f1   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SWK_OSC_TRIM_CTRL
                           0x1'1ae9   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SW_SD_CTRL
                           0x1'1565   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_SYS_STAT_CTRL
                           0x1'16ff   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK0
                           0x1'1915   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_TDOFF_HB_CTRL_BNK1
                           0x1'1939   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK0
                           0x1'18dd   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_TDON_HB_CTRL_BNK1
                           0x1'18f7   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_TIMER_CTRL
                           0x1'1541   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_WD_CTRL
                           0x1'1401   0x56  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_WK_CTRL_BNK1
                           0x1'14b1   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_WK_CTRL_BNK2
                           0x1'14d5   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_WK_CTRL_BNK3
                           0x1'14f5   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_FuncLayer_set_WK_CTRL_BNK4
                           0x1'1519   0x1a  Code  ??  TLE9560_FuncLayer.o [2]
TLE9560_RegLayer_clrDEVSTAT
                           0x1'6bdb    0xc  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_clrDSOVSTAT
                           0x1'6bff    0xc  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_clrHSOLOCOTSTAT
                           0x1'6bf3    0xc  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_clrSUPSTAT
                           0x1'6bc3    0xc  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_clrTHERMSTAT
                           0x1'6bcf    0xc  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_clrWKSTAT
                           0x1'6be7    0xc  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_BUS_STAT
                           0x1'66d5    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_DEV_STAT
                           0x1'6b39    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_DSOV
                           0x1'6b8b    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_EFF_TDON_OFF1
                           0x1'6b4d    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_EFF_TDON_OFF2
                           0x1'6b57    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_FAM_PROD_STAT
                           0x1'6b95    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_GEN_STAT
                           0x1'6b43    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_HS_OL_OC_OT_STAT
                           0x1'679b    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_SUP_STAT
                           0x1'6b25    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_SWK_CDR_STAT
                           0x1'6b1b    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_SWK_ECNT_STAT
                           0x1'6b11    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_SWK_OSC_CAL_STAT
                           0x1'6ad1    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_SWK_STAT
                           0x1'6b07    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_TDREG
                           0x1'6bb9    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_THERM_STAT
                           0x1'6b2f    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_TRISE_FALL1
                           0x1'6b61    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_TRISE_FALL2
                           0x1'6b6b    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_WK_LVL_STAT
                           0x1'6741    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_get_WK_STAT
                           0x1'6737    0xa  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_resetCrc
                           0x1'6c0b    0x8  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_BRAKE
                           0x1'6b9f   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_BUS_CTRL
                           0x1'66c1   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_CCP_BLK_BNK0
                           0x1'6839   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_CCP_BLK_BNK1
                           0x1'6855   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_CCP_BLK_BNK4
                           0x1'686b   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_CCP_BLK_BNK5
                           0x1'6881   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_GENCTRL
                           0x1'6671   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HBMODE
                           0x1'6b75   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HB_ICHG_BNK0
                           0x1'68ab   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HB_ICHG_BNK1
                           0x1'68c1   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HB_ICHG_BNK4
                           0x1'68d7   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HB_ICHG_BNK5
                           0x1'68ef   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HB_ICHG_MAX
                           0x1'6907   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HB_PCHG_INIT_BNK0
                           0x1'691d   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HB_PCHG_INIT_BNK1
                           0x1'6935   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HS_CTRL
                           0x1'6773   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HS_VDS
                           0x1'6787   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_HW_CTRL
                           0x1'6699   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_INT_MASK
                           0x1'67a5   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_LS_VDS
                           0x1'6825   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_M_S_CTRL
                           0x1'6685   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_PWM_CTRL_BNK0
                           0x1'67b9   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_PWM_CTRL_BNK1
                           0x1'67cf   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_PWM_CTRL_BNK2
                           0x1'67e5   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_PWM_CTRL_BNK3
                           0x1'67fb   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_ST_ICHG
                           0x1'6897   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_BTL1_CTRL
                           0x1'69c3   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_CAN_FD_CTRL
                           0x1'6aa5   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_CDR_CTRL
                           0x1'6adb   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_CDR_LIMIT
                           0x1'6af1   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_CTRL
                           0x1'69ad   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_DATA0_CTRL
                           0x1'6a8f   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_DATA1_CTRL
                           0x1'6a79   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_DATA2_CTRL
                           0x1'6a5d   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_DATA3_CTRL
                           0x1'6a47   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_DLC_CTRL
                           0x1'6a31   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_ID0_CTRL
                           0x1'69ef   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_ID1_CTRL
                           0x1'69d9   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_MASK_ID0_CTRL
                           0x1'6a1b   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_MASK_ID1_CTRL
                           0x1'6a05   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SWK_OSC_TRIM_CTRL
                           0x1'6abb   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SW_SD_CTRL
                           0x1'675f   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_SYS_STAT_CTRL
                           0x1'6811   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_TDOFF_HB_CTRL_BNK0
                           0x1'697d   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_TDOFF_HB_CTRL_BNK1
                           0x1'6995   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_TDON_HB_CTRL_BNK0
                           0x1'694d   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_TDON_HB_CTRL_BNK1
                           0x1'6965   0x18  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_TIMER_CTRL
                           0x1'674b   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_WD_CTRL
                           0x1'66ad   0x14  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_WK_CTRL_BNK1
                           0x1'66df   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_WK_CTRL_BNK2
                           0x1'66f5   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_WK_CTRL_BNK3
                           0x1'670b   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_RegLayer_set_WK_CTRL_BNK4
                           0x1'6721   0x16  Code  ??  TLE9560_RegLayer.o [2]
TLE9560_ServLayer_InitSpiInterface
                           0x1'2c05    0xe  Code  ??  TLE9560_ServLayer.o [2]
TLE9560_ServLayer_SendSpiData
                           0x1'2c13   0x28  Code  Lc  TLE9560_ServLayer.o [2]
TLE9560_ServLayer_getReg
                           0x1'2c71   0x16  Code  ??  TLE9560_ServLayer.o [2]
TLE9560_ServLayer_getSpiData
                           0x1'2c3b   0x16  Code  ??  TLE9560_ServLayer.o [2]
TLE9560_ServLayer_sendStaticCrcRecovery
                           0x1'2ca9   0x18  Code  ??  TLE9560_ServLayer.o [2]
TLE9560_ServLayer_setBnkReg
                           0x1'2c87   0x22  Code  ??  TLE9560_ServLayer.o [2]
TLE9560_ServLayer_setReg
                           0x1'2c51   0x20  Code  ??  TLE9560_ServLayer.o [2]
TLE9560_getSIF             0x1'1209    0xa  Code  ??  TLE9560_ApplLayer.o [2]
TLE9560_ram_copy        0x2000'40b8   0x9c  Data  Lc  TLE9560.o [2]
TaskSch_u8Get1msCntr         0x84fd    0x6  Code  ??  TaskSch_Man.o [2]
TaskSch_vDec1msCntr          0x8515   0x12  Code  ??  TaskSch_Man.o [2]
TaskSch_vInc1msCntr          0x8503   0x12  Code  ??  TaskSch_Man.o [2]
ThermProt_Mdl                0xe923   0x3a  Code  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_B         0x2000'4317    0x1  Data  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_DW        0x2000'40a8    0x4  Data  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_Init           0xe90f   0x14  Code  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_MdlrefDW  0x2000'40a4    0x4  Data  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_TherprotMosfetTempClass
                             0xe65f  0x130  Code  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_TherprotMosfetTempClass_Init
                             0xe659    0x6  Code  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_TherprotmotorTempcClass
                             0xe797  0x178  Code  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_TherprotmotorTempcClass_Init
                             0xe78f    0x8  Code  ??  ThermProt_Mdl.o [2]
ThermProt_Mdl_initialize
                             0xe95d    0x6  Code  ??  ThermProt_Mdl.o [2]
ThermProt_isOverride    0x2000'4315    0x1  Data  ??  ThermProt_Exp.o [2]
ThermProt_stFltType     0x2000'4316    0x1  Data  ??  ThermProt_Exp.o [2]
TheshForce_Mdl               0xfbbd    0xa  Code  ??  TheshForce_Mdl.o [2]
TheshForce_Mdl_MdlrefDW
                        0x2000'40ac    0x4  Data  ??  TheshForce_Mdl.o [2]
TheshForce_Mdl_initialize
                             0xfbc7    0x6  Code  ??  TheshForce_Mdl.o [2]
TimerHwAbs_GetElapsedTime
                             0x7799   0x18  Code  ??  TimerHwAbs.o [2]
TimerHwAbs_enInitLPIT        0x7751   0x34  Code  ??  TimerHwAbs.o [2]
TimerHwAbs_u32Get1msCtr
                             0x774b    0x6  Code  ??  TimerHwAbs.o [2]
TimerHwAbs_vInitLPTMR        0x7785   0x14  Code  ??  TimerHwAbs.o [2]
TimerHwAbs_vLPITCallback
                             0x7719   0x32  Code  ??  TimerHwAbs.o [2]
UDS                        0x1'0945    0x8  Code  ??  ISOUDS.o [2]
UDS_Init                   0x1'0939    0xc  Code  ??  ISOUDS.o [2]
UDS_LookUpTbl              0x1'094d   0x88  Code  ??  ISOUDS.o [2]
UDS_SrvPIndTxMsg           0x1'08ff    0x8  Code  ??  ISOUDS.o [2]
UDS_SrvPInit               0x1'0751   0x28  Code  ??  ISOUDS.o [2]
UDS_SrvPNewMsgInd          0x1'0779   0x40  Code  ??  ISOUDS.o [2]
UDS_SrvPTask               0x1'07b9  0x11c  Code  ??  ISOUDS.o [2]
UDS_SrvPTask::ISOUDS_iTabIdx
                        0x2000'42d9    0x1  Data  Lc  ISOUDS.o [2]
UsgHis_Mdl                   0xeb3f   0x5a  Code  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_B            0x2000'4178    0x4  Data  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_CheckManufacturingConditions
                             0xe97d   0x56  Code  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_CheckManufacturingConditions_Init
                             0xe975    0x8  Code  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_ConstB          0x1'916c    0xc  Data  ??  UsgHis_Mdl_data.o [2]
UsgHis_Mdl_DW           0x2000'417c    0x8  Data  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_Init              0xeb31    0xe  Code  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_MdlrefDW     0x2000'4174    0x4  Data  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_TravelCycleCounter
                             0xe9db   0x30  Code  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_TravelCycleCounter_Init
                             0xe9d3    0x8  Code  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_UpdateNVM         0xea0b  0x126  Code  ??  UsgHis_Mdl.o [2]
UsgHis_Mdl_initialize        0xeb99    0x6  Code  ??  UsgHis_Mdl.o [2]
UsgHist_CaseTemp        0x2000'4184    0x4  Data  ??  UsgHist_Exp.o [2]
UsgHist_MtrTemp         0x2000'4188    0x4  Data  ??  UsgHist_Exp.o [2]
UsgHist_nCycle          0x2000'42be    0x2  Data  ??  UsgHist_Exp.o [2]
UsgHist_nCycle_Const    0x2000'42a8    0x2  Data  Lc  nvmman_conf.o [2]
UsgHist_resetReason     0x2000'418c   0x10  Data  ??  UsgHist_Exp.o [2]
UsgHist_stHallState     0x2000'431a    0x1  Data  ??  UsgHist_Exp.o [2]
UsgHist_unlearnReason   0x2000'42c0    0x2  Data  ??  UsgHist_Exp.o [2]
UsgHist_unlearnReason_Const
                        0x2000'42a6    0x2  Data  Lc  nvmman_conf.o [2]
VehCom_Mdl                   0xef81  0x21a  Code  ??  VehCom_Mdl.o [2]
VehCom_Mdl_B            0x2000'41a0    0x4  Data  ??  VehCom_Mdl.o [2]
VehCom_Mdl_ConstB          0x1'926c    0x4  Data  ??  VehCom_Mdl_data.o [2]
VehCom_Mdl_DW           0x2000'41a4    0x8  Data  ??  VehCom_Mdl.o [2]
VehCom_Mdl_Init              0xef71    0xa  Code  ??  VehCom_Mdl.o [2]
VehCom_Mdl_MdlrefDW     0x2000'419c    0x4  Data  ??  VehCom_Mdl.o [2]
VehCom_Mdl_PublishedFrames
                             0xebe9  0x27c  Code  ??  VehCom_Mdl.o [2]
VehCom_Mdl_SubscribedFrames
                             0xee7b   0xf6  Code  ??  VehCom_Mdl.o [2]
VehCom_Mdl_SubscribedFrames_Init
                             0xee75    0x6  Code  ??  VehCom_Mdl.o [2]
VehCom_Mdl_initialize        0xf19b    0x6  Code  ??  VehCom_Mdl.o [2]
VoltMon_Mdl                  0xf745   0xba  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_B           0x2000'41b0    0x8  Data  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_ConstB         0x1'9070   0x1c  Data  ??  VoltMon_Mdl_data.o [2]
VoltMon_Mdl_DW          0x2000'41b8   0xa8  Data  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_Init             0xf729   0x14  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_MdlrefDW    0x2000'41ac    0x4  Data  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_PowerFailureDetect
                             0xf1ef  0x2ae  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_PowerFailureDetect_Init
                             0xf1d5   0x1a  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_RollingAverageFilter
                             0xf4a1   0xa4  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_StartupCheck
                             0xf557   0x6a  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_StartupCheck_Init
                             0xf54d    0xa  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_VoltageClassMonitor
                             0xf5c9  0x160  Code  ??  VoltMon_Mdl.o [2]
VoltMon_Mdl_initialize       0xf7ff    0x6  Code  ??  VoltMon_Mdl.o [2]
VoltMon_is12VBattVld    0x2000'431b    0x1  Data  ??  VoltMon_Exp.o [2]
VoltMon_u12VBatt        0x2000'42c2    0x2  Data  ??  VoltMon_Exp.o [2]
WDOGMan_bStatus            0x1'1d49   0x18  Code  ??  WDOGMan.o [2]
WDOG_IRQHandler            0x1'9271         Code  Wk  startup_S32K118.o [1]
ZI$$Base                0x2000'30c0          --   Gb  - Linker created -
ZI$$Limit               0x2000'431c          --   Gb  - Linker created -
__CODE_RAM$$Base        0x2000'0bb4          --   ??  - Linker created -
__CODE_RAM$$Limit       0x2000'0bf0          --   Gb  - Linker created -
__CODE_ROM$$Base           0x1'8f0c          --   ??  - Linker created -
__CODE_ROM$$Limit          0x1'8f48          --   ??  - Linker created -
__RAM_END {Abs}         0x2000'57ff         Data  ??  <internal module>
__RAM_START {Abs}       0x2000'0000         Data  ??  <internal module>
__RAM_VECTOR_TABLE_SIZE {Abs}
                               0xc0         Data  ??  <internal module>
__VECTOR_RAM {Abs}      0x2000'0000         Data  ??  <internal module>
__VECTOR_TABLE {Abs}            0x0         Data  ??  <internal module>
__Vectors                       0x0          --   ??  startup_S32K118.o [1]
__Vectors_End                  0xc0         Data  ??  startup_S32K118.o [1]
__Vectors_Size {Abs}           0xc0          --   ??  startup_S32K118.o [1]
__aeabi_idiv                 0x1ccb         Code  ??  I32DivModFast.o [4]
__aeabi_idiv0                0x1d81         Code  ??  IntDivZer.o [4]
__aeabi_idivmod              0x1ccb         Code  ??  I32DivModFast.o [4]
__aeabi_ldiv0                0x9935         Code  ??  I64DivZer.o [4]
__aeabi_llsl                 0x964d         Code  ??  I64Shl.o [4]
__aeabi_lmul                 0x959d         Code  ??  I64Mul.o [4]
__aeabi_memclr4            0x1'3969         Code  ??  ABImemset.o [4]
__aeabi_memclr8            0x1'3969         Code  ??  ABImemset.o [4]
__aeabi_memcpy               0x30e1         Code  ??  ABImemcpy.o [4]
__aeabi_memcpy4              0x30ad         Code  ??  ABImemcpy.o [4]
__aeabi_memcpy8              0x30ad         Code  ??  ABImemcpy.o [4]
__aeabi_memset             0x1'3949         Code  ??  ABImemset.o [4]
__aeabi_uidiv                0x1cd1         Code  ??  I32DivModFast.o [4]
__aeabi_uidivmod             0x1cd1         Code  ??  I32DivModFast.o [4]
__aeabi_uldivmod             0x95cf         Code  ??  I64DivMod.o [4]
__cmain                    0x1'9025         Code  ??  cmain.o [4]
__exit                     0x1'905d   0x14  Code  ??  exit.o [5]
__iar_Memcpy                 0x30ed         Code  ??  ABImemcpy.o [4]
__iar_Memset4_word         0x1'396d         Code  ??  ABImemset.o [4]
__iar_Memset_word          0x1'3955         Code  ??  ABImemset.o [4]
__iar_program_start        0x1'908d         Code  ??  cstartup_M.o [4]
__low_level_init           0x1'9043    0x4  Code  ??  low_level_init.o [3]
__vector_table                  0x0         Data  ??  startup_S32K118.o [1]
__vector_table_0x1c            0x1c         Data  ??  startup_S32K118.o [1]
_call_main                 0x1'9031         Code  ??  cmain.o [4]
_exit                      0x1'9051         Code  ??  cexit.o [4]
abs_dif                    0x1'4411    0xe  Code  Lc  lpspi_hw_access.o [2]
adc_config_1_ChnConfig0
                        0x2000'3bbc    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig1
                        0x2000'0790    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig10
                        0x2000'07b0    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig11
                        0x2000'07b4    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig12
                        0x2000'07b8    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig13
                        0x2000'07bc    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig14
                        0x2000'07c0    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig15
                        0x2000'07c4    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig2
                        0x2000'0794    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig3
                        0x2000'0798    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig4
                        0x2000'079c    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig5
                        0x2000'07a0    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig6
                        0x2000'07a4    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig7
                        0x2000'07a8    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig8
                        0x2000'3bc0    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ChnConfig9
                        0x2000'07ac    0x4  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_ConvConfig0
                           0x1'9160    0xc  Data  ??  peripherals_adc_config_1.o [2]
adc_config_1_HwAvgConfig0
                           0x1'6c12    0x2  Data  ??  peripherals_adc_config_1.o [2]
apfICMon_CallbackFunction
                        0x2000'050c    0x8  Data  ??  ICMon.o [2]
bGetBitFromByte              0x3555   0x12  Code  ??  conversion.o [2]
bIsSetFuncInQueueStatus
                           0x1'0339   0x34  Code  ??  ExtDev.o [2]
bPrepareSleep           0x2000'42d0    0x1  Data  Lc  ExtDev.o [2]
b_spi_error             0x2000'4319    0x1  Data  Lc  TLE9560_ServLayer.o [2]
capturedValue           0x2000'42bc    0x2  Data  ??  TimerHwAbs.o [2]
clockMan1_InitConfig0   0x2000'0110   0x80  Data  ??  clock_config.o [2]
clockNameMappings          0x1'8910   0x8c  Data  ??  clock_S32K1xx.o [2]
crc_1_Cfg0                 0x1'9128   0x10  Data  ??  peripherals_crc_1.o [2]
data_1                     0x1'9188    0x8  Data  Lc  LINTP_NodeSrv_Cfg.o [2]
data_2                     0x1'9190    0x8  Data  Lc  LINTP_NodeSrv_Cfg.o [2]
div_nde_s32_floor          0x1'06d5   0x42  Code  ??  div_nde_s32_floor.o [2]
div_s32_sat                  0x3009   0x88  Code  ??  div_s32_sat.o [2]
div_u32_round              0x1'0717   0x3a  Code  ??  div_u32_round.o [2]
enAppendCRCToBuffer          0x2d09   0x4c  Code  Lc  nvmman.o [2]
enConvertErrorCode           0x3529   0x2c  Code  ??  conversion.o [2]
enRequestNVMRead             0x2d55   0x9e  Code  Lc  nvmman.o [2]
erm_InitConfig0            0x1'91a0    0x8  Data  ??  peripherals_erm_config_1.o [2]
erm_Interrupt0             0x1'890e    0x2  Data  ??  peripherals_erm_config_1.o [2]
exit                       0x1'9047    0x8  Code  ??  exit.o [3]
flexTimer_ic_1_InitConfig
                        0x2000'07c8   0x14  Data  ??  peripherals_flexTimer_ic_1.o [2]
flexTimer_ic_1_InputCaptureChannelConfig
                        0x2000'07e4   0x20  Data  ??  peripherals_flexTimer_ic_1.o [2]
flexTimer_ic_1_InputCaptureConfig
                        0x2000'07dc    0x8  Data  ??  peripherals_flexTimer_ic_1.o [2]
flexTimer_pwm_1_FaultConfig
                        0x2000'3bc4   0x10  Data  ??  peripherals_flexTimer_pwm_1.o [2]
flexTimer_pwm_1_IndependentChannelsConfig
                        0x2000'0818   0x20  Data  ??  peripherals_flexTimer_pwm_1.o [2]
flexTimer_pwm_1_InitConfig
                        0x2000'0804   0x14  Data  ??  peripherals_flexTimer_pwm_1.o [2]
flexTimer_pwm_1_PwmConfig
                        0x2000'0838   0x18  Data  ??  peripherals_flexTimer_pwm_1.o [2]
fp_cyclicStatGetFuncs   0x2000'0b10   0x4c  Data  Lc  TLE9560_ApplLayer.o [2]
fp_cyclicStatUpdateFuncs
                        0x2000'0b5c   0x4c  Data  Lc  TLE9560_ApplLayer.o [2]
ftmStatePtr             0x2000'34c0    0x8  Data  ??  ftm_common.o [2]
g_RtcClkInFreq          0x2000'3270    0x4  Data  ??  clock_S32K1xx.o [2]
g_TClkFreq              0x2000'3264    0xc  Data  ??  clock_S32K1xx.o [2]
g_buffer_backup_data    0x2000'3600    0x8  Data  ??  lin_cfg.o [2]
g_flexcanBase              0x1'923c    0x4  Data  Lc  flexcan_driver.o [2]
g_flexcanStatePtr       0x2000'34bc    0x4  Data  Lc  flexcan_driver.o [2]
g_ftmBase                  0x1'9178    0x8  Data  ??  ftm_common.o [2]
g_ftmExtClockSel             0x8c6a    0x2  Data  Lc  ftm_common.o [2]
g_ftmIrqId                 0x1'90f8   0x10  Data  ??  ftm_common.o [2]
g_ftmOverflowIrqId           0x3566    0x2  Data  ??  ftm_common.o [2]
g_interruptDisableCount
                        0x2000'356c    0x4  Data  Lc  interrupt_manager.o [2]
g_linLpuartBase            0x1'9180    0x8  Data  ??  lin_lpuart_driver.o [2]
g_linLpuartIsrs         0x2000'0530    0x8  Data  ??  lin_irq.o [2]
g_linLpuartRxTxIrqId         0x9936    0x2  Data  ??  lin_lpuart_driver.o [2]
g_linStatePtr           0x2000'3620    0x8  Data  ??  lin_lpuart_driver.o [2]
g_linUserconfigPtr      0x2000'3628    0x8  Data  ??  lin_lpuart_driver.o [2]
g_lin_flag_handle_tbl   0x2000'0524    0x4  Data  ??  lin_cfg.o [2]
g_lin_frame_data_buffer
                        0x2000'0514   0x10  Data  ??  lin_cfg.o [2]
g_lin_frame_flag_handle_tbl
                        0x2000'3608    0x4  Data  ??  lin_cfg.o [2]
g_lin_frame_updating_flag_tbl
                        0x2000'360c    0x4  Data  ??  lin_cfg.o [2]
g_lin_hardware_ifc           0x96a6    0x2  Data  ??  lin_cfg.o [2]
g_lin_node_attribute_array
                           0x1'8ed0   0x3c  Data  ??  lin_cfg.o [2]
g_lin_protocol_state_array
                        0x2000'3598   0x20  Data  ??  lin.o [2]
g_lin_protocol_user_cfg_array
                           0x1'8f78   0x24  Data  ??  lin_cfg.o [2]
g_lin_virtual_ifc          0x1'9250    0x4  Data  ??  lin_cfg.o [2]
g_lpspiBase             0x2000'0558    0x8  Data  ??  lpspi_shared_function.o [2]
g_lpspiIrqId            0x2000'0baa    0x2  Data  ??  lpspi_shared_function.o [2]
g_lpspiStatePtr         0x2000'3784    0x8  Data  ??  lpspi_shared_function.o [2]
g_lptmrBase                0x1'925c    0x4  Data  Lc  lptmr_driver.o [2]
g_pin_mux_InitConfigArr0
                        0x2000'0884  0x288  Data  ??  pin_mux.o [2]
g_xtal0ClkFreq          0x2000'3274    0x4  Data  ??  clock_S32K1xx.o [2]
gs_stBootInfo              0x1'9118   0x10  Data  Lc  main.o [2]
init_data_bss              0x1'86c1   0xd8  Code  ??  startup.o [2]
intrp1d_s16s32s32u32u32n16l_n
                           0x1'0c25   0x2e  Code  ??  intrp1d_s16s32s32u32u32n16l_n.o [2]
is_EEEMemoryReady            0x2cc1    0xc  Code  Lc  nvmman.o [2]
is_WriteAllowed              0x2ccd   0x3c  Code  Lc  nvmman.o [2]
l_ifc_init                   0x8c81   0x34  Code  ??  lin_common_api.o [2]
l_ifc_read_status            0x8cb5  0x11e  Code  ??  lin_common_api.o [2]
l_sys_init                   0x8c7d    0x4  Code  ??  lin_common_api.o [2]
lin0_InitConfig0        0x2000'0850   0x14  Data  ??  peripherals_lin_1.o [2]
lin1TimerGetTimeIntervalCallback0
                             0x77b1   0x2e  Code  ??  TimerHwAbs.o [2]
lin1TimerGetTimeIntervalCallback0::u32previousCountValue
                        0x2000'40b4    0x4  Data  Lc  TimerHwAbs.o [2]
lin_bus_activity_timeout
                             0x9363   0x36  Code  Lc  lin_common_proto.o [2]
lin_calc_max_header_timeout_cnt
                             0x78c1   0x16  Code  Lc  lin.o [2]
lin_calc_max_res_timeout_cnt
                             0x78d7   0x24  Code  Lc  lin.o [2]
lin_diagservice_assign_frame_id_range
                             0x7151   0xda  Code  ??  LINTP_NodeSrv.o [2]
lin_frame_tbl              0x1'8e50   0x40  Data  Lc  lin_cfg.o [2]
lin_get_frame_index          0x9431   0x38  Code  ??  lin_common_proto.o [2]
lin_handle_error             0x9295   0xce  Code  Lc  lin_common_proto.o [2]
lin_lld_ignore_response
                             0x7a55   0x38  Code  ??  lin.o [2]
lin_lld_init                 0x7907  0x14a  Code  ??  lin.o [2]
lin_lld_init::lin_lld_response_buffer
                        0x2000'35f4    0xc  Data  Lc  lin.o [2]
lin_lld_rx_response          0x7b79   0x7e  Code  ??  lin.o [2]
lin_lld_set_low_power_mode
                             0x7aa1   0x38  Code  ??  lin.o [2]
lin_lld_set_response         0x7ae1   0x8c  Code  ??  lin.o [2]
lin_lld_timeout_service
                             0x7bf7  0x142  Code  ??  lin.o [2]
lin_make_res_evnt_frame
                             0x9869   0x2e  Code  ??  lin_lin21_proto.o [2]
lin_pid_resp_callback_handler
                             0x8e7d   0x58  Code  ??  lin_common_proto.o [2]
lin_process_id               0x8ed5  0x172  Code  Lc  lin_common_proto.o [2]
lin_process_parity           0x78fb    0xc  Code  ??  lin.o [2]
lin_process_uncd_frame       0x946d  0x114  Code  ??  lin_common_proto.o [2]
lin_tl_callback_handler
                             0x8dd3   0x96  Code  ??  lin_common_api.o [2]
lin_update_err_signal        0x9769   0x80  Code  ??  lin_lin21_proto.o [2]
lin_update_rx                0x9047  0x15e  Code  Lc  lin_common_proto.o [2]
lin_update_rx_evnt_frame
                             0x97e9   0x80  Code  ??  lin_lin21_proto.o [2]
lin_update_tx                0x91a9   0xe6  Code  Lc  lin_common_proto.o [2]
lin_update_tx_flags          0x9399   0x92  Code  Lc  lin_common_proto.o [2]
lin_update_word_status_j2602
                             0x98ad   0x88  Code  ??  lin_j2602_proto.o [2]
lin_update_word_status_lin21
                             0x96a9   0xc0  Code  ??  lin_lin21_proto.o [2]
lpit1_ChnConfig0        0x2000'0864   0x18  Data  ??  peripherals_lpit_config_1.o [2]
lpit1_InitConfig           0x1'8c6a    0x2  Data  ??  peripherals_lpit_config_1.o [2]
lpspi_0_MasterConfig0      0x1'9004   0x20  Data  ??  peripherals_lpspi_1.o [2]
lpspi_1State            0x2000'3bd4   0x34  Data  ??  peripherals_lpspi_1.o [2]
lptmr_1_config0            0x1'9138   0x10  Data  ??  peripherals_lptmr_1.o [2]
lptmr_ChooseClkConfig        0x88e5   0x6a  Code  Lc  lptmr_driver.o [2]
lptmr_GetClkFreq             0x884d   0x98  Code  Lc  lptmr_driver.o [2]
lptmr_GetClkFreq::lptmrPccClockName
                           0x1'9276    0x1  Data  Lc  lptmr_driver.o [2]
lptmr_compute_nticks         0x87d9   0x40  Code  Lc  lptmr_driver.o [2]
lptmr_us2nn                  0x87b5   0x24  Code  Lc  lptmr_driver.o [2]
main                       0x1'899d   0x8c  Code  ??  main.o [2]
nticks2compare_ticks         0x8819   0x34  Code  Lc  lptmr_driver.o [2]
osif_DisableIrqGlobal        0x6215    0x8  Code  Lc  osif_baremetal.o [2]
osif_EnableIrqGlobal         0x621d    0x8  Code  Lc  osif_baremetal.o [2]
osif_GetCurrentTickCount
                             0x6193    0x6  Code  Lc  osif_baremetal.o [2]
osif_Tick                    0x6189    0xa  Code  Lc  osif_baremetal.o [2]
osif_UpdateTickConfig        0x61a3   0x5e  Code  Lc  osif_baremetal.o [2]
osif_UpdateTickConfig::first_init
                        0x2000'0bb1    0x1  Data  Lc  osif_baremetal.o [2]
pdb_config_1_adcTrigConfig0_0
                        0x2000'087c    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig0_1
                           0x1'91a8    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig0_2
                           0x1'91b0    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig0_3
                           0x1'91b8    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig0_4
                           0x1'91c0    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig0_5
                           0x1'91c8    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig0_6
                           0x1'91d0    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig0_7
                           0x1'91d8    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_0
                        0x2000'3c08    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_1
                           0x1'91e0    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_2
                           0x1'91e8    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_3
                           0x1'91f0    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_4
                           0x1'91f8    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_5
                           0x1'9200    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_6
                           0x1'9208    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_adcTrigConfig1_7
                           0x1'9210    0x8  Data  ??  peripherals_pdb_config_1.o [2]
pdb_config_1_timerConfig0
                           0x1'9218    0x8  Data  ??  peripherals_pdb_config_1.o [2]
peripheralClockConfig0  0x2000'00c0   0x50  Data  ??  clock_config.o [2]
peripheralFeaturesList     0x1'8dc4   0x48  Data  ??  clock_S32K1xx.o [2]
plook_u32u16u32n16_even7c_gn
                           0x1'0bf7   0x2e  Code  ??  plook_u32u16u32n16_even7c_gn.o [2]
rtCP_pooled_WV2vRI6z3aVL
                           0x1'9228    0x4  Data  ??  const_params.o [2]
rtCP_pooled_kLyzadZemPPm
                           0x1'8e0c   0x44  Data  ??  const_params.o [2]
rtCP_pooled_rpnYm2uaWZ1g
                           0x1'9273    0x1  Data  ??  const_params.o [2]
s_adcBase                  0x1'9220    0x4  Data  Lc  adc_driver.o [2]
s_baudratePrescaler        0x1'8fe4   0x20  Data  Lc  lpspi_hw_access.o [2]
s_baudrate_adjusted_flg
                        0x2000'42e3    0x1  Data  Lc  lin.o [2]
s_countMeasure          0x2000'4286    0x2  Data  Lc  lin_lpuart_driver.o [2]
s_crcBase                  0x1'922c    0x4  Data  Lc  crc_driver.o [2]
s_deviceDriver          0x2000'4154   0x18  Data  ??  TLE9560_ApplLayer.o [2]
s_edmaBase                 0x1'9234    0x4  Data  Lc  edma_driver.o [2]
s_ermBase                  0x1'9238    0x4  Data  Lc  erm_driver.o [2]
s_linLpuartClkName         0x1'0c52    0x2  Data  Lc  lin_lpuart_driver.o [2]
s_lin_max_frame_res_timeout_val
                        0x2000'35e4   0x10  Data  Lc  lin.o [2]
s_lin_max_header_timeout
                        0x2000'4280    0x2  Data  Lc  lin.o [2]
s_lin_state_array       0x2000'35b8   0x2c  Data  Lc  lin.o [2]
s_lpitBase                 0x1'9258    0x4  Data  Lc  lpit_driver.o [2]
s_lpitClkNames             0x1'9275    0x1  Data  Lc  lpit_driver.o [2]
s_lpitSourceClockFrequency
                        0x2000'3780    0x4  Data  Lc  lpit_driver.o [2]
s_lpuartBase               0x1'9198    0x8  Data  Lc  lpuart_driver.o [2]
s_lpuartClkNames           0x1'3946    0x2  Data  Lc  lpuart_driver.o [2]
s_lpuartStatePtr        0x2000'378c    0x8  Data  Lc  lpuart_driver.o [2]
s_osif_tick_cnt         0x2000'3bb8    0x4  Data  Lc  osif_baremetal.o [2]
s_pdbBase                  0x1'9260    0x4  Data  Lc  pdb_driver.o [2]
s_pdbIrqId                 0x1'9278    0x1  Data  Lc  pdb_driver.o [2]
s_previousTwoBitTimeLength
                        0x2000'3630    0x8  Data  Lc  lin_lpuart_driver.o [2]
s_timeMeasure           0x2000'3638    0x8  Data  Lc  lin_lpuart_driver.o [2]
s_vectors                  0x1'9268    0x4  Data  Lc  startup.o [2]
s_virtEdmaState         0x2000'34b4    0x4  Data  Lc  edma_driver.o [2]
s_wakeupSignal          0x2000'4284    0x2  Data  Lc  lin_lpuart_driver.o [2]
tempDataBuff            0x2000'398c  0x12c  Data  Lc  nvmman.o [2]
u16LinFuncId            0x2000'428a    0x2  Data  ??  LINTP.o [2]
u16LinSupplierId        0x2000'4288    0x2  Data  ??  LINTP.o [2]
u16SBCFaultStatus       0x2000'426e    0x2  Data  Lc  ExtDev.o [2]
u16TLE9560_SIF          0x2000'4270    0x2  Data  Lc  ExtDev.o [2]
u16TimerBuf             0x2000'3794   0x64  Data  ??  main.o [2]
u16timerOverflowInterruptCount
                        0x2000'42ba    0x2  Data  ??  TimerHwAbs.o [2]
u32LinSerialNum         0x2000'3640    0x4  Data  ??  LINTP.o [2]
u8AppReqEnterBootloaderFlag
                        0x2000'42f8    0x1  Data  ??  main.o [2]
u8BufCnt                0x2000'42f9    0x1  Data  ??  main.o [2]
u8DEV_STAT_SPI_CRC_failCtr
                        0x2000'42ce    0x1  Data  Lc  ExtDev.o [2]
u8InitialNAD            0x2000'42e5    0x1  Data  ??  LINTP.o [2]
u8LinVariantNum         0x2000'42e4    0x1  Data  ??  LINTP.o [2]
u8RxBuffer              0x2000'3610    0x8  Data  ??  lin_common_api.o [2]
u8SetFuncCtr            0x2000'42cf    0x1  Data  Lc  ExtDev.o [2]
u8TxBuffer              0x2000'3618    0x8  Data  ??  lin_common_api.o [2]
u8_rxBuffer             0x2000'4170    0x4  Data  Lc  TLE9560_ServLayer.o [2]
u8_txBuffer             0x2000'416c    0x4  Data  Lc  TLE9560_ServLayer.o [2]
vFaultReaction             0x1'059b    0x2  Code  ??  ExtDev.o [2]
vHandleSPI_CRC_Failure     0x1'0391   0x8c  Code  ??  ExtDev.o [2]
vHandleSPI_CRC_Failure::u16PrevDevStatus
                        0x2000'4272    0x2  Data  Lc  ExtDev.o [2]
vHandleSUP_STAT_Failure
                           0x1'0429   0x9a  Code  ??  ExtDev.o [2]
vHandleSUP_STAT_Failure::u16PrevSupStatus
                        0x2000'4274    0x2  Data  Lc  ExtDev.o [2]
vHandleTHERM_STAT_Failure
                           0x1'04cd   0xce  Code  ??  ExtDev.o [2]
vHandleTHERM_STAT_Failure::u16PrevThermStatus
                        0x2000'4276    0x2  Data  Lc  ExtDev.o [2]
vPrepareSleep              0x1'02c3   0x3a  Code  ??  ExtDev.o [2]
vSetClearBitSBCFault       0x1'036d   0x24  Code  ??  ExtDev.o [2]
vSetFunction               0x1'059d   0x10  Code  ??  ExtDev.o [2]
vStartConv                 0x1'0c55   0x64  Code  Lc  ADCMon_Man.o [2]
varient                 0x2000'42f3    0x1  Data  ??  LINTP_NodeSrv.o [2]


[1] = C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedASMs_13015082826985747244.dir
[2] = C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_Workspace_S2\MBDevStrm\SwIntegration\60_IAR_Project\Debug\Obj\GeneratedCs_1389668238906185961.dir
[3] = dl6M_tln.a
[4] = rt6M_tl.a
[5] = shb_l.a

  97'169 bytes of readonly  code memory
      60 bytes of readwrite code memory
   5'033 bytes of readonly  data memory
   8'271 bytes of readwrite data memory

Errors: none
Warnings: none
