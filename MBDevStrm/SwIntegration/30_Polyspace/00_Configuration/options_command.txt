# Options file
-prog Gen2_INEOS
-verif-version 1.0
-author srv_build
-lang C
-dos
-misra3 from-file
-custom-rules from-file
-main-generator-writes-variables public
-main-generator-calls unused
-float-rounding-mode to-nearest
-signed-integer-overflows forbid
-unsigned-integer-overflows allow
-uncalled-function-checks none
-check-subnormal allow
-O2
-to Software Safety Analysis level 2
                        
-I SwApplication\SourceCode\GenCode\Inc
-sources-list-file D:\Hedi\Gen2BMW_ASSY_SCU_SW_DevStrm_kortam_Workspace\BMWDevStrm\SwIntegration\30_Polyspace\00_Configuration\source_command.txt
-checkers-selection-file .\SwIntegration\30_Polyspace\00_Configuration\std_config.xml
-import-comments .\SwIntegration\30_Polyspace\01_Result
