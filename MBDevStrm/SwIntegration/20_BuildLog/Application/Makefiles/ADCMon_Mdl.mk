# Copyright 2024 Inalfa Roof Systems, LLC.
#
# File    : ert_lcc64_Nxp_S1K.tmf   
#
# Abstract:
#       Template makefile for building a PC-based stand-alone embedded real-time 
#       version of Simulink model using generated C code and 
#			IAR Compiler Version 9.50.1
#	Base Template	- ert_lcc64.tmf - Matlab2022b
#	Maker		- gmake
#
#       This makefile attempts to conform to the guidelines specified in the
#       IEEE Std 1003.2-1992 (POSIX) standard. It is designed to be used
#       with GNU Make (gmake) which is located in matlabroot/bin/win64.
#
#       Note that this template is automatically customized by the build 
#       procedure to create "<model>.mk"
#
#       The following defines can be used to modify the behavior of the
#       build:
#         OPT_OPTS       - Optimization options. Default is none. To enable 
#                          debugging specify as OPT_OPTS=-g4. 
#         OPTS           - User specific compile options.
#         USER_SRCS      - Additional user sources, such as files needed by
#                          S-functions.
#         USER_INCLUDES  - Additional include paths 
#                          (i.e. USER_INCLUDES="-Iwhere-ever -Iwhere-ever2")
#                          (For Lcc, have a '/'as file separator before the 
#                          file name instead of a '\' . 
#                          i.e.,  d:\work\proj1/myfile.c - reqd for 'gmake')
#       This template makefile is designed to be used with a system target
#       file that contains 'rtwgensettings.BuildDirSuffix' see ert.tlc

#------------------------ Macros read by make_rtw ------------------------------
#
# The following macros are read by the build procedure:
#
#  MAKECMD         - This is the command used to invoke the make utility
#  HOST            - What platform this template makefile is targeted for
#                    (i.e. PC or UNIX)
#  BUILD           - Invoke make from the build procedure (yes/no)?
#  SYS_TARGET_FILE - Name of system target file.

MAKECMD         = "%MATLAB%\bin\win64\gmake"
SHELL           = cmd
HOST            = PC
BUILD           = yes
SYS_TARGET_FILE = any

# Opt in to simplified format by specifying compatible Toolchain
#TOOLCHAIN_NAME = "LCC-win64 v2.4.1 | gmake (64-bit Windows)"

#MAKEFILE_FILESEP = /

#---------------------- Tokens expanded by make_rtw ----------------------------
#
# The following tokens, when wrapped with "|>" and "<|" are expanded by the
# build procedure.
#
#  MODEL_NAME          - Name of the Simulink block diagram
#  MODEL_MODULES       - Any additional generated source modules
#  MAKEFILE_NAME       - Name of makefile created from template makefile <model>.mk
#  MATLAB_ROOT         - Path to where MATLAB is installed.
#  MATLAB_BIN          - Path to MATLAB executable.
#  S_FUNCTIONS_LIB     - List of S-functions libraries to link. 
#  NUMST               - Number of sample times
#  NCSTATES            - Number of continuous states
#  BUILDARGS           - Options passed in at the command line.
#  MULTITASKING        - yes (1) or no (0): Is solver mode multitasking
#  INTEGER_CODE        - yes (1) or no (0): Is generated code purely integer
#  MAT_FILE            - yes (1) or no (0): Should mat file logging be done,
#                        if 0, the generated code runs indefinitely
#  MULTI_INSTANCE_CODE - Is the generated code multi instantiable (1/0)?
#  SHRLIBTARGET        - Is this build intended for generation of a shared library instead 
#                        of executable (1/0)?
#  MAKEFILEBUILDER_TGT - Is this build performed by the MakefileBuilder class
#                        e.g. to create a PIL executable?
#  STANDALONE_SUPPRESS_EXE - Build the standalone target but only create object code modules 
#                            and do not build an executable

MODEL                = ADCMon_Mdl
MODULES              = ADCMon_Mdl.c
PRODUCT              = ADCMon_Mdl_rtwlib.lib
MAKEFILE             = ADCMon_Mdl.mk
MATLAB_ROOT          = C:\Program Files\MATLAB\R2022b
ALT_MATLAB_ROOT      = C:\PROGRA~1\MATLAB\R2022b
MATLAB_BIN           = C:\Program Files\MATLAB\R2022b\bin
ALT_MATLAB_BIN       = C:\PROGRA~1\MATLAB\R2022b\bin
START_DIR            = A:\INEOSDevStrm
S_FUNCTIONS_LIB      = 
NUMST                = 1
NCSTATES             = 0
BUILDARGS            = "USE_TMF=1" GENERATE_ERT_S_FUNCTION=0 GENERATE_ASAP2=0 EXT_MODE=0 EXTMODE_STATIC_ALLOC=0 EXTMODE_STATIC_ALLOC_SIZE=1000000 EXTMODE_TRANSPORT=0 TMW_EXTMODE_TESTING=0 OPTS="-DTID01EQ=0"
MULTITASKING         = 0
INTEGER_CODE         = 0
MAT_FILE             = 0
ALLOCATIONFCN        = 0
ONESTEPFCN           = 1
TERMFCN              = 1
MULTI_INSTANCE_CODE  = 0
CLASSIC_INTERFACE    = 0
MODELREFS            = 
SHRLIBTARGET         = 0
MAKEFILEBUILDER_TGT  = 0
ENABLE_SLEXEC_SSBRIDGE  = 0
STANDALONE_SUPPRESS_EXE = 0
OPTIMIZATION_FLAGS      = 
ADDITIONAL_LDFLAGS      = 
DEFINES_CUSTOM          = 
DEFINES_OTHER           = -DHAVESTDIO
COMPILE_FLAGS_OTHER     = 
SYSTEM_LIBS             = 
MODEL_HAS_DYNAMICALLY_LOADED_SFCNS = 0

#--------------------------- Model and reference models -----------------------
MODELLIB                  = ADCMon_Mdl_rtwlib.lib
MODELREF_LINK_LIBS        = 
MODELREF_LINK_RSPFILE     = ADCMon_Mdl_ref.rsp
RELATIVE_PATH_TO_ANCHOR   = ..\..\..
FMT_RELATIVE_PATH_TO_ANCHOR   = $(subst /,\,$(RELATIVE_PATH_TO_ANCHOR))
# NONE: standalone, SIM: modelref sim, RTW: modelref coder target
MODELREF_TARGET_TYPE       = RTW
MODELREF_SFCN_SUFFIX       = _msf


#-- In the case when directory name contains space ---
ifneq ($(MATLAB_ROOT),$(ALT_MATLAB_ROOT))
MATLAB_ROOT := $(ALT_MATLAB_ROOT)
endif
ifneq ($(MATLAB_BIN),$(ALT_MATLAB_BIN))
MATLAB_BIN := $(ALT_MATLAB_BIN)
endif

#--------------------------- Tool Specifications -------------------------------

#LCC = $(MATLAB_ROOT)\sys\lcc64\lcc64
#include $(MATLAB_ROOT)\rtw\c\tools\lcc64tools.mak

CMD_FILE             = $(MODEL).rsp
PROJ_PATH	= $(START_DIR)
#------------------------ NXP S32/K118 Configuration -----------------------------
#
# Set the following variables to reflect the target processor that you are
# using.  Refer to the Freescale Documents for information on
# appropriate values.
#
#  CPU_TYPE	- Processor Type
#  TGT_TYPE	- Memory size of processor
#------------------------ NXP S32/K118 Configuration -----------------------------
CPU_TYPE	= S32_K118
TGT_TYPE	= 2 MB

#------------------------ NXP S32/K118 Compiler Tools ----------------------------------
#
# The section has to be updated if the tool install path is different with belows:
#
#  COMPILER_ROOT	- Compiler Path 
#  AUTOSAR_VERSION	- Autosar version 
#  CC				- IAR Compiler Driver for compiling source files
#  AS				- IAR Compiler Driver for assembling the startup files
#  LIBCMD       	- IAR Compiler Driver for creating the libraries
#  LINKER      		- IAR Compiler Driver for linking the object files and startup code
#  DBLINKER    		- IAR Linker for linking the object files without startup code
#  CONVERTER       	- IAR Code converter to generate motorola S-Record file
#  INC_OPT			- Compiler Include Option
#  COMPILE_TOOL_OPTS	- Compile Tool Options 
#------------------------ NXP S32/K118 Compiler Tools ----------------------------------

COMPILER_ROOT	= C:\IAR\comp10012024\arm\bin
AUTOSAR_VERSION = 4.2.2

CC 			= "$(COMPILER_ROOT)\iccarm.exe"
AS			= "$(COMPILER_ROOT)\iasmarm.exe"
LIBCMD 		= "$(COMPILER_ROOT)\iarchive.exe"
LINKER		= "$(COMPILER_ROOT)\ilinkarm.exe"
CONVERTER	= "$(COMPILER_ROOT)\ielftool.exe"

INC_OPT 	= I
INC_LIB_OPT = l

# Not use the -object_dir option to avoid the too long command line

COMPILE_TOOL_OPTS 	= -o $@

#------------------------ SCU Project Configuration -----------------------------
#
# Project Configuration:
#
#  SRC_MAINPATH		- Source Path 
#  ASWSRC_PATH		- Location of Application Software(Matlab Generated) Source C files
#  ASWINC_PATH		- Location of Application Software(Matlab Generated) Header files
#  BSWSRC_PATH		- Location of Basic Software(Hand written) Source C files
#  BSWINC_PATH		- Location of Basic Software(Hand written) Header files
#  WA_PATH			- Work Path 
#  LINK_FILE		- Name of Project Linker Parameter file with path
#  LINK_FILE_DB		- Name of Project Linker Data Base Parameter file with path
#  OBJ_PATH 		- Location of Compiled Object files
#  MKFILE_PATH		- Location of Make files
#  LIB_PATH 		- Location of Library files
#  EXLIB_PATH		- Location of External Component Library files
#  BIN_PATH     	- Location of Generated Executable file
#  EXEFILE_NAME 	- Name of Generated Executable file with path
#  MODELREFS_PATH 	- Location of refence models generated code  
#  INIT_CONFIG_PATH	- Location of Init model generated code 
#  			  (It has to be included during compiling, as _P header files are located there)
#------------------------ SCU Project Configuration -----------------------------

SRC_MAINPATH	= $(PROJ_PATH)\SwApplication\SourceCode
PRODUCT_NAME	= SCU_Application
ASWSRC_PATH	= $(SRC_MAINPATH)\GenCode\Src
ASWINC_PATH	= $(SRC_MAINPATH)\GenCode\Inc
BSWSRC_PATH	= $(SRC_MAINPATH)\ManCode\Src
BSWINC_PATH	= $(SRC_MAINPATH)\ManCode\Inc
WA_PATH		= $(PROJ_PATH)\SCUMain_ert_rtw

LINK_FILE	= $(PROJ_PATH)\SwIntegration\00_BuildSetup\Linker\S32K118.icf
STARTUP_FILE_PATH = $(PROJ_PATH)\SwIntegration\00_BuildSetup\StartupFile

OBJ_PATH	= $(PROJ_PATH)\SwIntegration\20_BuildLog\Application\Objs
MKFILE_PATH	= $(PROJ_PATH)\SwIntegration\20_BuildLog\Application\Makefiles

LIB_PATH	= $(PROJ_PATH)\SwIntegration\10_Release\APPL_Lib\Lib
EXLIB_PATH	= $(PROJ_PATH)\SwIntegration\10_Release\APPL_Lib\LibExter
BIN_PATH  	= $(PROJ_PATH)\SwIntegration\10_Release\Bin\APPL
EXEFILE_NAME= $(BIN_PATH)\SCU_Application$(EXE_EXT)


MODELREFS_PATH		= ..\slprj\ert
INIT_CONFIG_PATH	= $(PROJ_PATH)\slprj\ert\SCUInit

LIB_PREFIX_IAR = lib
MODELLIB_IAR = $(addprefix $(LIB_PREFIX_IAR), $(MODELLIB:.$(MATLABLIB_FILE_SUFFIX)=.$(LIB_FILE_SUFFIX)))
MODELREF_LINK_LIBS_IAR_NAME = $(addprefix $(LIB_PREFIX_IAR), $(notdir $(MODELREF_LINK_LIBS:.$(MATLABLIB_FILE_SUFFIX)=.$(LIB_FILE_SUFFIX))))
MODELREF_LINK_LIBS_IAR = $(join $(dir $(MODELREF_LINK_LIBS)), $(MODELREF_LINK_LIBS_IAR_NAME))
MODELREF_LINK_LIBS_NAMEONLY = $(notdir $(basename $(MODELREF_LINK_LIBS)))
INC_MODELREF_LIB  = $(addprefix -$(INC_LIB_OPT), $(MODELREF_LINK_LIBS_NAMEONLY))


#------------------------------ Include Path -----------------------------------

# Additional includes 
#  USER_INCLUDES   	- Project includes
#  EXLIB_INCLUDES   - External Component Library includes

ADD_INCLUDES = \
	-$(INC_OPT)$(START_DIR) \
	-$(INC_OPT)$(START_DIR)\slprj\ert\ADCMon_Mdl \
	-$(INC_OPT)$(MATLAB_ROOT)\extern\include \
	-$(INC_OPT)$(MATLAB_ROOT)\simulink\include \
	-$(INC_OPT)$(MATLAB_ROOT)\rtw\c\src \
	-$(INC_OPT)$(MATLAB_ROOT)\rtw\c\src\ext_mode\common \
	-$(INC_OPT)$(MATLAB_ROOT)\rtw\c\ert \
	-$(INC_OPT)$(START_DIR)\slprj\ert\_sharedutils \


# see COMPILER_INCLUDES from lcctool.mak

USER_INCLUDES	= -$(INC_OPT)$(ASWINC_PATH) \
				  -$(INC_OPT)$(BSWINC_PATH) \
				  -$(INC_OPT)$(WA_PATH)		\
				  -$(INC_OPT)$(INIT_CONFIG_PATH)

EXLIB_INCLUDES 	=-$(INC_OPT)$(EXLIB_PATH) 

INCLUDES 	= -$(INC_OPT). \
			-$(INC_OPT)$(RELATIVE_PATH_TO_ANCHOR) \
			$(ADD_INCLUDES) \
	    	$(USER_INCLUDES) \
			$(EXLIB_INCLUDES)
				
#------------------------ File extensions -----------------------------
#
#  C_FILE_SUFFIX_UPPERCASE 	- Source File Suffix  
#  C_FILE_SUFFIX_LOWERCASE 	- Source File Suffix  
#  C_FILE_SUFFIX		 	- Source File Suffix  
#  H_FILE_SUFFIX 			- Header File Suffix  
#  ASM_FILE_SUFFIX			- Assembly File Suffix  
#  OBJ_FILE_SUFFIX 			- Object File Suffix  
#  LIB_FILE_SUFFIX 			- Library File Suffix  
#  MATLABLIB_FILE_SUFFIX	- Matlab Generated Library File Suffix  
#  MAKE_FILE_SUFFIX			- Make File Suffix  
#  LST_FILE_SUFFIX 			- Assembler Source List File Suffix  
#  MAP_FILE_SUFFIX 			- Map File Suffix  
#  S_RECORD_SUFFIX 			- S-Record File Suffix  
#  EXE_FILE_SUFFIX 			- Executable File Suffix  
#------------------------ File extensions -----------------------------
C_FILE_SUFFIX_UPPERCASE = C
C_FILE_SUFFIX_LOWERCASE = c
C_FILE_SUFFIX = $(C_FILE_SUFFIX_LOWERCASE)
H_FILE_SUFFIX = h
ASM_FILE_SUFFIX = s
OBJ_FILE_SUFFIX = o
MATLABLIB_FILE_SUFFIX = lib
LIB_FILE_SUFFIX = a
MAKE_FILE_SUFFIX = mk
LST_FILE_SUFFIX = lst
MAP_FILE_SUFFIX = map
S_RECORD_SUFFIX = s3
HEX_RECORD_SUFFIX = hex
EXE_FILE_SUFFIX = out

#-------------------------------- C Flags --------------------------------------
#------------------------ Compiler and Linker Options -----------------------------
#
# Compiler, Linker Option Configuration:
#
#
#  Compiler Options:		
#  DEFAULT_OPT_OPTS	- Default Optimization Options 
#  OPT_OPTS		- Optimization Options 
#  OPTS			- General User Options 
#  AS_C_COMM_OPTS	- Common Compiler Options for Assember and C Compiler 
#  ASFLAGS		- Assember Compiler Options 
#  CFLAGS		- C Compiler Options for Application Software(Matlab Generated) and Basic Software(Hand Written)  
#
#  Library Options:		
#  LIBFLAGS			- Library Options
#  SLIB_LDFLAGS		- Shared Library Options 
#
#  Linker and other Options:		
#  LINKOPT			- Search Path for Linker(Lib Path)
#  LDFLAGS			- Linker Options
#------------------------ Compiler and Linker Options -----------------------------
CPU_CORE = Cortex-M0+

# Optimization Options
DEFAULT_OPT_OPTS =  
OPT_OPTS = $(DEFAULT_OPT_OPTS)

# General User Options for NXP
OPTS 			= \
				--cpu $(CPU_CORE)\
				-e \
				--fpu None \
				--debug \
				--dlib_config "C:\IAR\comp10012024\arm\inc\c\DLib_Config_Normal.h" \
				--endian little \
				--cpu_mode thumb \
				-Ol \
				--no_cse \
				--no_unroll \
				--no_inline \
				--no_code_motion \
				--no_tbaa \
				--no_clustering \
				--no_wrap_diagnostics \
				-DCPU_S32K118 \
				--no_scheduling 
#Commented temporarely
#				--enable_stack_usage
# Inalfa added options
INALFA_OPTS	= \
			-c

# Compiler options, etc:
ifneq ($(OPTIMIZATION_FLAGS),)
CC_OPTS = $(OPTS) $(ANSI_OPTS) $(COMPILE_FLAGS_OTHER) $(OPTIMIZATION_FLAGS)
else
CC_OPTS = $(OPTS) $(ANSI_OPTS) $(COMPILE_FLAGS_OTHER) $(OPT_OPTS) 
endif
AS_C_COMM_OPTS	= \
				$(CC_OPTS)		\
				$(INALFA_OPTS)

# These flags to be reviewed  
ASFLAGS		= 

CPP_REQ_DEFINES =

DEFINES = $(DEFINES_CUSTOM) $(CPP_REQ_DEFINES) $(DEFINES_OTHER)

CFLAGS			= \
				$(AS_C_COMM_OPTS)		\
				$(DEFINES)				\
				$(INCLUDES)				
				
LIBFLAGS		= 
				

# These flags to be reviewed  
SLIB_LDFLAGS	= 

LINKOPT			= 

INC_LIB_PATH = L

# $(CC_OPTS) $(INALFA_OPTS) -L(INCLUDES) 
LDFLAGS			= \
				--fpu None \
				--no_wrap_diagnostics \
				--entry Reset_Handler \
				--no_bom \
				--text_out locale \
				--config

SFLAGS= --srec
HEXFLAGS= --ihex

#-------------------------- Additional Libraries ------------------------------

LIBS =


LIBS +=  $(S_FUNCTIONS_LIB)
S_FUNCTIONS_LIB_IAR_NAME = $(addprefix $(LIB_PREFIX_IAR), $(notdir $(S_FUNCTIONS_LIB:.$(MATLABLIB_FILE_SUFFIX)=.$(LIB_FILE_SUFFIX))))
S_FUNCTIONS_LIB_IAR = $(join $(dir $(S_FUNCTIONS_LIB)), $(S_FUNCTIONS_LIB_IAR_NAME))

LIBS_IAR =
LIBS_IAR +=  $(S_FUNCTIONS_LIB_IAR)

LIBS_IAR_NAMEONLY = 
LIBS_IAR_NAMEONLY +=  $(notdir $(basename $(S_FUNCTIONS_LIB)))
INC_LIBS  = $(addprefix -$(INC_LIB_OPT), $(LIBS_IAR_NAMEONLY))

EXTERNAL_LIBS = $(notdir $(basename $(wildcard $(EXLIB_PATH)/*.$(LIB_FILE_SUFFIX))))
INC_EXT_LIB  = $(addprefix -$(INC_LIB_OPT), $(EXTERNAL_LIBS))

#----------------------------- Source Files ------------------------------------
#  ORIGINAL_BSWSRC_PATH		- Original the path of Basic Software(Hand Written)
#  EXCLUDE_PATH             - Exclude the path of Basic Software(Hand Written)
#  EXCLUDE_SRCS             - Exclude the source files of Basic Software(Hand Written)
#  STUBS_PATH               - The path of Stubs(Hand Written)
#  EXCLUDE_STUBS_SRCS		- Exclude the source files from Stubs path(Hand Written)
#  EXCLUDE_STUBS_INCS		- Exclude the header files from Stubs path(Hand Written)
#  STUBS_SRCS               - Include all the source files from Stubs path with excluded files(Hand Written)
#  STUBS_INCS               - Include all the header files from Stubs path with excluded files(Hand Written)
#  BSW_SRCS                 - Include all the source files of Basic Software(Hand Written)
#  BSW_ASM_SRCS             - Include all the assembly source files of Basic Software(Hand Written)
#  ADD_SRCS                 - Additional source files 
#  SRCS                     - Full set of source files
#  USER_SRCS                - User source files
#  USER_OBJS                - Object files of user source files
#  LOCAL_USER_OBJS          - Object files without directory of user source files
#  OBJS                     - Object files of all source files
#----------------------------- Source Files -----------------------------------
APP_PATH = $(PROJ_PATH)\SwApplication
#EXCLUDE_PATH = $(APP_PATH)\DriverLayer
#EXCLUDE_SRCS = $(notdir $(wildcard $(EXCLUDE_PATH)/*.$(C_FILE_SUFFIX)))
STUBS_PATH = $(APP_PATH)\Application\Common\Code\
#EXCLUDE_STUBS_SRCS = $(notdir $(wildcard $(STUBS_PATH)/Can*.$(C_FILE_SUFFIX)))
#EXCLUDE_STUBS_INCS = $(notdir $(wildcard $(STUBS_PATH)/Can*.$(H_FILE_SUFFIX)))
STUBS_SRCS = $(filter-out $(EXCLUDE_STUBS_SRCS), $(notdir $(wildcard $(APP_PATH)/Application/Common/Code/*.$(C_FILE_SUFFIX))))
STUBS_INCS = $(filter-out $(EXCLUDE_STUBS_INCS), $(notdir $(wildcard $(APP_PATH)/Application/Common/Code/*.$(H_FILE_SUFFIX))))
BSW_SRCS = $(filter-out $(EXCLUDE_SRCS) $(EXCLUDE_STUBS_SRCS), $(notdir $(wildcard $(APP_PATH)/*/Code/*.$(C_FILE_SUFFIX) $(APP_PATH)/Application/*.$(C_FILE_SUFFIX) $(APP_PATH)/*/*/Code/*.$(C_FILE_SUFFIX)  $(APP_PATH)/*/*/*/Code/*.$(C_FILE_SUFFIX) $(STARTUP_FILE_PATH)/*/*.$(C_FILE_SUFFIX))))
BSW_ASM_SRCS = $(notdir $(wildcard $(STARTUP_FILE_PATH)/*/*.$(ASM_FILE_SUFFIX)))
ADD_SRCS =

SRCS = $(ADD_SRCS) $(MODULES)

USER_SRCS =
ifeq ($(MODELREF_TARGET_TYPE), NONE)
ifneq ($(STANDALONE_SUPPRESS_EXE),1)
SRCS += $(MODEL).$(C_FILE_SUFFIX)
USER_SRCS += $(BSW_SRCS)
#PRODUCT	= $(RELATIVE_PATH_TO_ANCHOR)\$(MODEL)
PRODUCT	= $(MODEL)
endif
endif

USER_OBJS       = $(USER_SRCS:.$(C_FILE_SUFFIX)=.$(OBJ_FILE_SUFFIX))
USER_OBJS       += $(BSW_ASM_SRCS:.$(ASM_FILE_SUFFIX)=.$(OBJ_FILE_SUFFIX))
LOCAL_USER_OBJS = $(notdir $(USER_OBJS))

OBJS      = $(SRCS:.$(C_FILE_SUFFIX)=.$(OBJ_FILE_SUFFIX)) $(USER_OBJS)
OBJS_WITH_PATH  = $(addprefix $(OBJ_PATH)\, $(OBJS))


DEF_FILE = $(MODEL).def

#--------------------------------- Rules ( already updated )---------------------------------------
all: debug_make clean prebuild $(PRODUCT) postCompile
#BIN_SETTING        = $(LINKER) $(LINK_FILE) $(LDFLAGS) $(ADDITIONAL_LDFLAGS)  
ifeq ($(MODELREF_TARGET_TYPE),NONE)
  ifneq ($(SHRLIBTARGET),1)

    ifeq ($(MAKEFILEBUILDER_TGT),1)
#--- Stand-alone model Makefile Target ---
	@cmd /C "echo ### S32K Stand-along Makefile Target ..."
	@cmd /C "echo ### Created executable $(BUILD_PRODUCT_TYPE):  $@"
   else
      ifneq ($(STANDALONE_SUPPRESS_EXE), 1)
#--- Stand-alone model ---
$(PRODUCT) : $(LINK_FILE) $(OBJS) $(LIBS_IAR) $(MODELREF_LINK_LIBS_IAR)  
	@cmd /C "echo ### S32K Stand-along Linking $(PRODUCT)..."
	@cmd /C "echo ### S32K Stand-along Linking $(INC_LIBS)..."
	@cmd /C "echo ### S32K Stand-along Linking $(INC_MODELREF_LIB)..."
	@cmd /C "echo ### S32K Stand-along Linking $(INC_EXT_LIB)..."
	@cmd /C "echo ### S32K Stand-along Linking $(SYSTEM_LIBS)..."
	@cmd /C "echo ### S32K Stand-along Linking $(MODELREF_LINK_LIBS_IAR)..."
	@cmd /C "echo ### S32K Stand-along Linking $(LIBS_IAR)..."
	$(LINKER) $(OBJS) $(INC_LIBS) $(INC_MODELREF_LIB) $(INC_EXT_LIB) $(SYSTEM_LIBS) $(LIBS_IAR) -$(INC_LIB_PATH)$(LIB_PATH) -$(INC_LIB_PATH)$(EXLIB_PATH) \
	-o "$(BIN_PATH)\$(PRODUCT_NAME).$(EXE_FILE_SUFFIX)" \
	--map "$(BIN_PATH)\$(PRODUCT_NAME).$(MAP_FILE_SUFFIX)"		\
	$(LDFLAGS) $(LINK_FILE) 	
	@cmd /C "echo ### Generating Motorola S-Record file ..."
	$(CONVERTER) $(SFLAGS) "$(BIN_PATH)\$(PRODUCT_NAME).$(EXE_FILE_SUFFIX)" \
	"$(BIN_PATH)\$(PRODUCT_NAME).$(S_RECORD_SUFFIX)"
	@cmd /C "echo ### Generating HEX file ..."
	$(CONVERTER) $(HEXFLAGS) "$(BIN_PATH)\$(PRODUCT_NAME).$(EXE_FILE_SUFFIX)" \
	"$(BIN_PATH)\$(PRODUCT_NAME).$(HEX_RECORD_SUFFIX)"
	@cmd /C "echo ### Created executable $(BUILD_PRODUCT_TYPE):  $@"
      endif
    endif
  endif
else
#--- Model reference Coder Target ---
 ifeq ($(MODELREF_TARGET_TYPE),SIM)  
  $(PRODUCT) : $(OBJS) $(LIBS)
	@if exist $(MODELLIB_IAR) del "$(MODELLIB_IAR)"
	@cmd /C "echo ### Created $(MODELLIB_IAR)"  
 else
  $(PRODUCT) : $(OBJS)
	@if exist $(MODELLIB_IAR) del "$(MODELLIB_IAR)"
	@cmd /C "echo ### Libmaking ..."
	$(LIBCMD) $(LIBFLAGS) $(MODELLIB_IAR) $(OBJS) 
	-copy $(MODELLIB_IAR) $(LIB_PATH) 
	@cmd /C "echo ### Created library: $(MODELLIB_IAR)"
 endif
endif

%.$(OBJ_FILE_SUFFIX) : $(BSWSRC_PATH)/%.$(C_FILE_SUFFIX_LOWERCASE)
	@cmd /C "echo ### BSW Compiling c $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(BSWSRC_PATH)/%.$(C_FILE_SUFFIX_UPPERCASE)
	@cmd /C "echo ### BSW Compiling C $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX): $(BSWSRC_PATH)/%.$(ASM_FILE_SUFFIX)
	@cmd /C "echo ### BSW Compiling asm $<"
	$(AS) $(ASFLAGS) $< 

%.$(OBJ_FILE_SUFFIX) : %.$(C_FILE_SUFFIX_LOWERCASE)
	@cmd /C "echo ### Current Path Compiling c $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : %.$(C_FILE_SUFFIX_UPPERCASE)
	@cmd /C "echo ### Current Path Compiling C $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(RELATIVE_PATH_TO_ANCHOR)/%.$(C_FILE_SUFFIX_LOWERCASE)
	@cmd /C "echo ### Relative Path Compiling c $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(RELATIVE_PATH_TO_ANCHOR)/%.$(C_FILE_SUFFIX_UPPERCASE)
	@cmd /C "echo ### Relative Path Compiling C $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(MATLAB_ROOT)/rtw/c/ert/%.$(C_FILE_SUFFIX_UPPERCASE)
	@cmd /C "echo ### Matlab root ert Compiling  C $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(MATLAB_ROOT)/rtw/c/src/%.$(C_FILE_SUFFIX_LOWERCASE)
	@cmd /C "echo ### Matlab root src Compiling  c $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(MATLAB_ROOT)/rtw/c/src/%.$(C_FILE_SUFFIX_UPPERCASE)
	@cmd /C "echo ### Matlab root src Compiling  C $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)


%.$(OBJ_FILE_SUFFIX) : $(MATLAB_ROOT)\rtw\c\src/%.$(C_FILE_SUFFIX_LOWERCASE)
	@cmd /C "echo ### Additional sources Compiling c $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(MATLAB_ROOT)\simulink\src/%.$(C_FILE_SUFFIX_LOWERCASE)
	@cmd /C "echo ### Additional sources Compiling c $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)



%.$(OBJ_FILE_SUFFIX) : $(MATLAB_ROOT)\rtw\c\src/%.$(C_FILE_SUFFIX_UPPERCASE)
	@cmd /C "echo ### Additional sources Compiling C $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)

%.$(OBJ_FILE_SUFFIX) : $(MATLAB_ROOT)\simulink\src/%.$(C_FILE_SUFFIX_UPPERCASE)
	@cmd /C "echo ### Additional sources Compiling C $<"
	$(CC) $(CFLAGS) $< $(COMPILE_TOOL_OPTS)




# Libraries  ( already updated )  :





#----------------------------- Dependencies ------------------------------------
$(OBJS) : $(MAKEFILE) rtw_proj.tmw
prebuild : 
ifeq ($(MODELREF_TARGET_TYPE), NONE)
  ifneq ($(SHRLIBTARGET),1)
    ifneq ($(MAKEFILEBUILDER_TGT),1)
      ifneq ($(STANDALONE_SUPPRESS_EXE), 1)
#--- Stand-alone model ---
	@cmd /C "echo ### Copying manual source code to $(BSWSRC_PATH) and $(BSWINC_PATH) ..."
	for /r $(APP_PATH) %%f in (Code\*.$(C_FILE_SUFFIX)) do @( if "%%~dpf"=="$(EXCLUDE_PATH)\" (echo skip %%f ) else (copy %%f $(BSWSRC_PATH) && echo %%f) )
	for /r $(APP_PATH) %%f in (Code\*.$(ASM_FILE_SUFFIX)) do @(copy %%f $(BSWSRC_PATH) && echo %%f)
	for /r $(APP_PATH) %%f in (Application\*.$(C_FILE_SUFFIX)) do @(copy %%f $(BSWSRC_PATH) && echo %%f)
	for /r $(APP_PATH) %%f in (Code\*.$(H_FILE_SUFFIX)) do @( if "%%~dpf"=="$(EXCLUDE_PATH)\" (echo skip %%f ) else (copy %%f $(BSWINC_PATH) && echo %%f) )
	for /r $(STARTUP_FILE_PATH) %%f in (Code\*.$(ASM_FILE_SUFFIX)) do @(copy %%f $(BSWSRC_PATH) && echo %%f)
	for /r $(STARTUP_FILE_PATH) %%f in (Code\*.$(C_FILE_SUFFIX)) do @(copy %%f $(BSWSRC_PATH) && echo %%f)
	for /r $(STARTUP_FILE_PATH) %%f in (Code\*.$(H_FILE_SUFFIX)) do @(copy %%f $(BSWINC_PATH) && echo %%f)
	@cmd /C "echo ### Copying Top model source code to $(ASWSRC_PATH) and $(ASWINC_PATH) ..."
	-copy *.$(C_FILE_SUFFIX) $(ASWSRC_PATH) 
	-copy *.$(H_FILE_SUFFIX) $(ASWINC_PATH) 
	-copy *.$(MAKE_FILE_SUFFIX) $(MKFILE_PATH) 
	-copy *.bat $(MKFILE_PATH) 
     endif
    endif
  endif
else
 ifneq ($(MODELREF_TARGET_TYPE),SIM)
	@cmd /C "echo ### Copying model reference source code to $(ASWSRC_PATH) and $(ASWINC_PATH) ..."
	-copy *.$(C_FILE_SUFFIX) $(ASWSRC_PATH) 
	-copy *.$(H_FILE_SUFFIX) $(ASWINC_PATH) 
	-copy *.$(MAKE_FILE_SUFFIX) $(MKFILE_PATH) 
	-copy $(MODEL).bat $(MKFILE_PATH) 
 endif
endif  

RM = del /F /Q
$(MODELREF_LINK_LIBS_IAR) : 
	@cmd /C "echo ### Creating Model Reference library $@ "
	@if exist $@ del "$@"
	@cmd /C "echo ### Deleted Model Reference library $@ "
	@cmd /C "echo ### Calling Model Reference Makefile $(patsubst %\,%,$(@D)) $(patsubst %_rtwlib,%,$(basename $(@F))).$(MAKE_FILE_SUFFIX) "
	$(MAKE) -C $(patsubst %\,%,$(@D)) -f $(patsubst lib%,%,$(patsubst %_rtwlib,%,$(basename $(@F)))).$(MAKE_FILE_SUFFIX)
	@cmd /C "echo ### $@ Created Model Reference library"
	
$(S_FUNCTIONS_LIB_IAR) : 
	@cmd /C "echo ### Creating S-functions library $@ "
	@cmd /C "echo ### Calling S-functions Makefile $(patsubst %\,%,$(@D)) $(basename $(@F)).$(MAKE_FILE_SUFFIX)"
	$(MAKE) -C $(patsubst %\,%,$(@D)) -f $(patsubst lib%,%,$(basename $(@F))).$(MAKE_FILE_SUFFIX)
	@cmd /C "echo ### $@ Created S-functions library"

postCompile :
	@cmd /C "echo ### Copying object files to $(OBJ_PATH) ..."
	-copy *.$(OBJ_FILE_SUFFIX) $(OBJ_PATH) 

#------------------------ clean -----------------------------
#
# clean Rules:
#
#  		   - 
#------------------------ clean -----------------------------
$(MODELREFS) : 
	@cmd /C "echo ### Deleting Model Reference library $@ "
	@if exist $(MODELREFS_PATH)\$@\*.$(LIB_FILE_SUFFIX) $(RM) "$(MODELREFS_PATH)\$@\*.$(LIB_FILE_SUFFIX)" 
	@cmd /C "echo ### $@ Deleted Model Reference library"

clean :$(MODELREFS)
ifeq ($(MODELREF_TARGET_TYPE), NONE)
  ifneq ($(SHRLIBTARGET),1)
    ifneq ($(MAKEFILEBUILDER_TGT),1)
      ifneq ($(STANDALONE_SUPPRESS_EXE), 1)
	@cmd /C "echo ### Deleting the generated object files for Top model $(MODEL)"
	@if exist $(ASWSRC_PATH)\*.$(C_FILE_SUFFIX) $(RM) "$(ASWSRC_PATH)\*.$(C_FILE_SUFFIX)"
	@if exist $(ASWINC_PATH)\*.* $(RM) "$(ASWINC_PATH)\*.*"
	@if exist $(BSWSRC_PATH)\*.$(C_FILE_SUFFIX) $(RM) "$(BSWSRC_PATH)\*.$(C_FILE_SUFFIX)"
	@if exist $(BSWSRC_PATH)\*.$(ASM_FILE_SUFFIX) $(RM) "$(BSWSRC_PATH)\*.$(ASM_FILE_SUFFIX)"
	@if exist $(BSWINC_PATH)\*.* $(RM) "$(BSWINC_PATH)\*.*"
	@if exist $(MKFILE_PATH)\*.* $(RM) "$(MKFILE_PATH)\*.*" 
	@if exist $(OBJ_PATH)\*.* $(RM) "$(OBJ_PATH)\*.*" 
	@if exist $(LIB_PATH)\*.* $(RM) "$(LIB_PATH)\*.*" 
	@if exist $(BIN_PATH)\*.* $(RM) "$(BIN_PATH)\*.*" 
	@if exist *.$(OBJ_FILE_SUFFIX) $(RM) "*.$(OBJ_FILE_SUFFIX)"  
	@if exist $(S_FUNCTIONS_LIB_IAR) $(RM) "$(S_FUNCTIONS_LIB_IAR)" 
	@cmd /C "echo ### Deleted the objects files for Top model $(MODEL)"
      endif
    endif
  endif
else
 ifneq ($(MODELREF_TARGET_TYPE),SIM)
	@cmd /C "echo ### Deleting the generated object files for Reference Model $(MODEL)"
	@if exist *.$(OBJ_FILE_SUFFIX) $(RM) "*.$(OBJ_FILE_SUFFIX)"  
	@if exist $(OBJ_PATH)\$(MODEL).$(OBJ_FILE_SUFFIX) $(RM) "$(OBJ_PATH)\$(MODEL).$(OBJ_FILE_SUFFIX)"
	@if exist $(MODELLIB_IAR) $(RM) "$(MODELLIB_IAR)" 
	@if exist $(LIB_PATH)\$(MODELLIB_IAR) $(RM) "$(LIB_PATH)\$(MODELLIB_IAR)" 
	@cmd /C "echo ### Deleted the object files for Reference Model $(MODEL)"
 endif
endif

debug_make:
	@echo Project root = $(PROJ_PATH)
	@echo Link file = $(LINK_FILE)
	@echo Object output path = $(OBJ_PATH)
	@echo Objects to build = $(OBJS)
	@echo Objects to build with path = $(OBJS_WITH_PATH)
	@echo Shared libraries to build = $(LIBS_IAR)
	@echo Model reference libraries to build = $(MODELREF_LINK_LIBS_IAR)
                           
# EOF: ert_lcc64_Nxp_S1K.tmf

	
		
