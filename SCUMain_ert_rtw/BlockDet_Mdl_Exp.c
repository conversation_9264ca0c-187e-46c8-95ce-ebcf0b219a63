/*
 * File: BlockDet_Mdl_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "BlockDet_Mdl_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint16_T BlockDet_hStallPos;           /*  Motor Stall Position- Hall
                                        */
BlockDet_Stall_dir BlockDet_stStallDir;/*  Motor Stall Position- Hall
                                        */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
