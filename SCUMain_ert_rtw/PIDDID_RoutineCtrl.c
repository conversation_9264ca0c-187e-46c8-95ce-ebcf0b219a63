/*
 * File: PIDDID_RoutineCtrl.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "SCUMain_private.h"

/* Includes for objects with custom storage classes */
#include "SCUMain.h"
#define PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_
#include "PIDDID_RoutineCtrl_Func.h"

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
