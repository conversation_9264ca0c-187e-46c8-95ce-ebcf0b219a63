/*
 * File: IoSigIf_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "IoSigIf_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
boolean_T IoHwAb_GetHallInput;
          /* Actual Hall signal status (M1_HALL_A/M1_HALL_B/M2_HALL_A/M2_HALL_B)
             0: Low
             1: High */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
