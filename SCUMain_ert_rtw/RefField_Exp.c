/*
 * File: RefField_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "RefField_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int8_T RFDet_FRFTbl[250];              /* Reference field for each panel
                                          [-46  21  18 -14  26 -17  21 -11   1  -4  -8  16  -4   6 -13   3  -5   3   1  -9   4  -8   3   2   3   1  -1   9   2  -4   5   6 -13   0   5  -2  -6  -3   4  -1  -2  -1   5  -5   1   1  -1   0   6   0   0  -4  -1  -2   3   6  -1  -9  -4   2  -1  -3   1   1  -1  -3   2   3  -3   3   4  -2   1   0  -2   0   5  -1  -1  -3  -2  -2  -1   6  -4   0  -1   0  -3   6   1  -2   3   2   1  -1  -1   0  -4  -1   2  -3  -3   4   0  -4  -3  -1   1  -3   1   5  -1   0   3   8  -2   5   1  -4   2  11  13   4   0   3  -3  -3  -3  -2  -2  -3  -6  -1   5   2   0  -5  -1   1  -5  -4   1  -1   2  -3  -8  -4  -7  -1   4  -1  -5  -1   0   1  -2  -6  -1   0   6   1  -4  -3   0   4  -1   3  -4  -6   2   4   1  -4   3   2   1   2  -2  -3   5  10   4   7  12   4   2  13  -2 -21  -7   0  -3  -7  -8  -6 -15  -5  -6  -7 -11   0   0  -7 -11  10  15  10  -6 -10  -6 -10 -21  -6   1   5  18  21   0  -3   8  11 -18 -26   2  -7 -13  -9   4   4   3   0 -13  22  29   4  44  21 0 0 0 0 0 0 0 0 0 0 0 0; 6  0  1  1  4 -4  5 -2 -1  2  1 -2 -3  0  2  2 -1  1  1  1 -1  2 -1  1 -3  1  0  2  1  5 -1 -1  2 -1  0  2 -1 -3  5  2 -5  3  2 -2  3 -4  1  0  3  0  0 -1  0 -2  2  1 -1  1  1  0  4  1 -3 -2  3 -1  1 -3  0 -1  2  1  1 -1 -2  1 -1  4 -3  2 -1  1 -1  1  1  1  1 -4 -1  4 -1  3 -3  0 -3  2 -2  2  1 -4  2  3  0 -3 -3  4  1  1 -1  3  4  3 -3 -1  1 -1  3  1  1 -1 -2  0  2 -2 -1 5  0  2  4  4 -3  0 -2 -1  0  0  2 -3  4 -5  1  1 -1  4  1  1  0  3 -4 -1 -1  0  4 -2 -1 -1  1  3  2  0  4  1  3  0  0 -3  5 -2  1  0  2  0  0  0  0  3 -3  4  0 -1  2  1  2 -4  4 -2 -1  1 -1  3  0  6  2  1  1 -2 -3  0  0  2  1  1 -1  3 -7  2  0  4  8  1 -4  4  0 -2  2 -1  0  3  1  2  0  1  3  7  1 -3  3  1  1  2 -1 -2 -1  3 -2  4  7  7  1  0  2 -5 -1  1  2  5 -7 0 0 0]
                                        */
boolean_T RFDet_isRFTblVld;
                   /* Calibrated Position Table Validity Status(Glass and Rollo)
                      0: Invalid
                      1: Valid */
boolean_T RFDet_isRFTblVldNvm;
boolean_T RFDet_isRFTblVldNvmBak;
boolean_T RFDet_isRFTblVldNvmSync;

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
