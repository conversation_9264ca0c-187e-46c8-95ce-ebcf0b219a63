/*
 * File: DTC_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "DTC_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
boolean_T DTC_isDTCEnbl;               /* Fault Detection Enable Status
                                          0: DTC Detection Disabled
                                          1: DTC Detection Enabled */
boolean_T DTC_isFltClr;                /* Fault Clear Command
                                          0: No Fault Clear
                                          1: Fault Clear */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
