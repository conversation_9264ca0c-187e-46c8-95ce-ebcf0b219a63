/*
 * File: SCUMain.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "SCUMain.h"
#include "rtwtypes.h"
#include "BlockDet_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "CtrlLogic_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "SCUMain_types.h"
#include "StateMach_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include <string.h>
#include "SCUMain_TaskSchedular_Mdl.h"
#include "PIDDID_ExpTypes.h"
#include "irs_std_types.h"
#include "SCUMain_private.h"
#include "DTC_Exp.h"
#include "ISOUDS.h"
#include "PIDDID_Exp.h"
#include "VoltMon_Exp.h"
#include "HALLDeco_Mdl_ISR.h"
#include "PIDDID_IoCtrl_Func.h"
#include "PIDDID_ReadDID_Func.h"
#include "PIDDID_RoutineCtrl_Func.h"
#include "PIDDID_WriteDID_Func.h"
#include "VehCom_Mdl.h"
#include "CfgParam_Mdl.h"
#include "RefField_Mdl.h"
#include "UsgHis_Mdl.h"
#include "PIDDID_Mdl.h"
#include "SysFaultReac_Mdl.h"
#include "APDet_Mdl.h"
#include "BlockDet_Mdl.h"
#include "LearnAdap_Mdl.h"
#include "MtrMdl_Mdl.h"
#include "RefForce_Mdl.h"
#include "TheshForce_Mdl.h"
#include "ComLog_Mdl.h"
#include "CtrlLog_Mdl.h"
#include "MtrCtrl_Mdl.h"
#include "PnlOp_Mdl.h"
#include "ThermProt_Mdl.h"
#include "AmbTempMon_Mdl.h"
#include "MOSFETTempMon_Mdl.h"
#include "PosMon_Mdl.h"
#include "SwitchLog_Mdl.h"
#include "VoltMon_Mdl.h"
#include "StateMach_Mdl.h"
#include "ADCMon_Mdl.h"
#include "DIOCtrl_Mdl.h"
#include "DiagComMan.h"
#include "ExtDev_Mdl.h"
#include "HALLDeco_Mdl.h"
#include "PWMCtrl_Mdl.h"
#include "PIDDID_IoCtrl.h"
#include "PIDDID_ReadDID.h"
#include "PIDDID_RoutineCtrl.h"
#include "PIDDID_WriteDID.h"
#include "SCUMain_hallInterrupt.h"

/* Exported block states */
CfgParam_ActiveDiagInfoBus_t CfgParam_ActiveDiagInfo;
                          /* Simulink.Signal object 'CfgParam_ActiveDiagInfo' */
CfgParam_DDSPackageRelBus_t CfgParam_DDSPackageRel;
                           /* Simulink.Signal object 'CfgParam_DDSPackageRel' */
CfgParam_HWVerInfoBus_t CfgParam_HWVerInfo;
                               /* Simulink.Signal object 'CfgParam_HWVerInfo' */
CfgParam_SWVerInfoBus_t CfgParam_SWVerInfo;
                               /* Simulink.Signal object 'CfgParam_SWVerInfo' */
PIDDID_PCB_SensorBus PIDDID_PCB_Sensor;
                                /* Simulink.Signal object 'PIDDID_PCB_Sensor' */
uint32_T HallDeco_sumFilterdPulseTicks;
                    /* Simulink.Signal object 'HallDeco_sumFilterdPulseTicks' */
stdReturn_t NVMAccessState;        /* Simulink.Signal object 'NVMAccessState' */
int16_T LearnAdap_TLastNormTemp;
                          /* Simulink.Signal object 'LearnAdap_TLastNormTemp' */
int16_T PIDDID_IOCtrl_TAmbTemp;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TAmbTemp' */
int16_T PIDDID_IOCtrl_TMosfetTemp;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_TMosfetTemp' */
uint16_T BlockDet_tBlockedTime_P;
                          /* Simulink.Signal object 'BlockDet_tBlockedTime_P' */
uint16_T LearnAdap_renormCycleCounter;
                     /* Simulink.Signal object 'LearnAdap_renormCycleCounter' */
uint16_T MtrMdl_pwmVoltage;     /* Simulink.Signal object 'MtrMdl_pwmVoltage' */
uint16_T PIDDID_ControlIntermediatePos;
                    /* Simulink.Signal object 'PIDDID_ControlIntermediatePos' */
uint16_T PIDDID_IOCtrl_TBattVtg;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TBattVtg' */
uint16_T PIDDID_IOCtrl_THallSuppVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_THallSuppVtg' */
uint16_T PIDDID_IOCtrl_TMtrCurrA;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrCurrA' */
uint16_T PIDDID_IOCtrl_TMtrCurrB;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrCurrB' */
uint16_T PIDDID_IOCtrl_TMtrFdbkAVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkAVtg' */
uint16_T PIDDID_IOCtrl_TMtrFdbkBVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkBVtg' */
uint16_T PIDDID_IOCtrl_TSwtchIpA;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TSwtchIpA' */
uint16_T PIDDID_IOCtrl_TSwtchIpB;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TSwtchIpB' */
uint16_T PIDDID_IOcontrolMaskRecord;
                       /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecord' */
uint16_T PIDDID_IOcontrolMaskRecordDigiOut;
                /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordDigiOut' */
uint16_T PIDDID_IOcontrolMaskRecordSysICOut;
               /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordSysICOut' */
uint16_T PIDDID_SoftClosePos; /* Simulink.Signal object 'PIDDID_SoftClosePos' */
uint16_T PIDDID_SoftOpenPos;   /* Simulink.Signal object 'PIDDID_SoftOpenPos' */
uint16_T PIDDID_SoftStartPoint1Time;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Time' */
uint16_T PIDDID_SoftStopPoint1Time;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Time' */
uint16_T PIDDID_uAutoCyclePauseTime[2];
                       /* Simulink.Signal object 'PIDDID_uAutoCyclePauseTime' */
uint16_T SysFltRctn_stLearnFail_Reason;
                    /* Simulink.Signal object 'SysFltRctn_stLearnFail_Reason' */
uint16_T SysFltRctn_stLostRP_Reason;
                       /* Simulink.Signal object 'SysFltRctn_stLostRP_Reason' */
uint8_T CfgParam_DiagTraceMemory[12];
                         /* Simulink.Signal object 'CfgParam_DiagTraceMemory' */
uint8_T CfgParam_HWSupId[2];     /* Simulink.Signal object 'CfgParam_HWSupId' */
uint8_T CfgParam_HWVersion[2];    /* Simulink.Signal object 'CfgParam_HWVersion'
                                   * Hardware version

                                     Hardware Major version constant in ASCII
                                     Hardware minor version constant in Decimal

                                   */
uint8_T CfgParam_MBCECUIDHWPartNum[10];
                       /* Simulink.Signal object 'CfgParam_MBCECUIDHWPartNum' */
uint8_T CfgParam_MtrSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_MtrSerialNum'
                                * Motor Serial Number in Decimal
                                */
uint8_T CfgParam_RoofPartNum[9];/* Simulink.Signal object 'CfgParam_RoofPartNum'
                                 * Roof Part Number in ASCII
                                 */
uint8_T CfgParam_RoofSerialNum[6];
                              /* Simulink.Signal object 'CfgParam_RoofSerialNum'
                               * Roof serial number Version
                               */
uint8_T CfgParam_SCUPartNum[9];  /* Simulink.Signal object 'CfgParam_SCUPartNum'
                                  * SCU Part Number in ASCII
                                  */
uint8_T CfgParam_SCUSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_SCUSerialNum'
                                * SCU Serial Number in Decimal
                                */
uint8_T CfgParam_SWSupId[2];     /* Simulink.Signal object 'CfgParam_SWSupId' */
uint8_T CfgParam_SupportedConfigMech;
                     /* Simulink.Signal object 'CfgParam_SupportedConfigMech' */
uint8_T PIDDID_IOCtrl_uMtrDtyCyc;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_uMtrDtyCyc' */
uint8_T PIDDID_SoftStartPoint1Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Perc' */
uint8_T PIDDID_SoftStartPoint2Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint2Perc' */
uint8_T PIDDID_SoftStopPoint1Perc;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Perc' */
uint8_T SysFltRctn_stDTCCounter_Byte1[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte1' */
uint8_T SysFltRctn_stDTCCounter_Byte2[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte2' */
uint8_T SysFltRctn_stDTCCounter_Byte3[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte3' */
uint8_T SysFltRctn_stDTCStatus[3];
                           /* Simulink.Signal object 'SysFltRctn_stDTCStatus' */
uint8_T SysFltRctn_stLast10DTCs[10];
                          /* Simulink.Signal object 'SysFltRctn_stLast10DTCs' */
boolean_T PIDDID_HallOutputDisabled;
                           /* Simulink.Signal object 'PIDDID_HallOutputDisabled'
                            * Diagnostic Routine Hall output Enabled/Disabled
                              0: Enabled
                              1: Disabled

                            */
boolean_T PIDDID_IOCtrl_HallSenOneIpSt;
                     /* Simulink.Signal object 'PIDDID_IOCtrl_HallSenOneIpSt' */
boolean_T PIDDID_IOCtrl_HallSenTwoIpSt;
                     /* Simulink.Signal object 'PIDDID_IOCtrl_HallSenTwoIpSt' */
boolean_T PIDDID_IOCtrl_INTNInpState;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_INTNInpState' */
boolean_T PIDDID_IOCtrl_isHallOut;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_isHallOut' */
boolean_T PIDDID_IOCtrl_isHallSupplyCtrl;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isHallSupplyCtrl' */
boolean_T PIDDID_IOCtrl_isMtrCCWAct;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCCWAct' */
boolean_T PIDDID_IOCtrl_isMtrCWAct;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCWAct' */
boolean_T PIDDID_IOCtrl_isMtrFdbkCtrl;
                      /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFdbkCtrl' */
boolean_T PIDDID_IOCtrl_isMtrFltrBlankA;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankA' */
boolean_T PIDDID_IOCtrl_isMtrFltrBlankB;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankB' */
boolean_T PIDDID_IOCtrl_isSwtchSupplyCtrl;
                  /* Simulink.Signal object 'PIDDID_IOCtrl_isSwtchSupplyCtrl' */
boolean_T PIDDID_IOCtrl_isVBATMeasEnable;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isVBATMeasEnable' */
boolean_T PIDDID_isAutoCycleActive;
                         /* Simulink.Signal object 'PIDDID_isAutoCycleActive' */
boolean_T PIDDID_isFactoryMode; /* Simulink.Signal object 'PIDDID_isFactoryMode'
                                 * Diagnostic Factory Mode
                                   1 - Enabled
                                   0 -Disabled

                                 */
boolean_T PIDDID_isSoftStartEna;
                            /* Simulink.Signal object 'PIDDID_isSoftStartEna' */
boolean_T PIDDID_isSoftStopEna;
                             /* Simulink.Signal object 'PIDDID_isSoftStopEna' */
boolean_T PosMon_isCurPosVldNvm;
                               /* Simulink.Signal object 'PosMon_isCurPosVldNvm'
                                * Current position validity NVM:
                                  0 invalid
                                  1: valid
                                */
boolean_T PosMon_isRelearn;      /* Simulink.Signal object 'PosMon_isRelearn' */
SysFltDiag_LastStopReason_t SysFltRctn_stLastStopReason;
                      /* Simulink.Signal object 'SysFltRctn_stLastStopReason' */
HallDeco_Dir_t HallDeco_enDMtrDir;/* Simulink.Signal object 'HallDeco_enDMtrDir'
                                   * Get Motor condition is moving or not in C code
                                   */
LearnAdap_nextRenorm_t LearnAdap_stNextRenorm;
                           /* Simulink.Signal object 'LearnAdap_stNextRenorm' */

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
boolean_T PIDDID_isCyclicRenormSuppress;
boolean_T PIDDID_isDummyRPRtnEna;
boolean_T PIDDID_isIntermediatePositionRtnEna;
boolean_T PIDDID_isOpenClosePositionRtnEna;
boolean_T PIDDID_isOverridePWMEnable;

/* Block signals (default storage) */
B_SCUMain_T SCUMain_B;

/* Block states (default storage) */
DW_SCUMain_T SCUMain_DW;

/* Real-time model */
static RT_MODEL_SCUMain_T SCUMain_M_;
RT_MODEL_SCUMain_T *const SCUMain_M = &SCUMain_M_;

/* Model step function */
void PIDDID_IoCtrl(uint16_T rtu_ioDID, uint8_T rtu_CtrlParam, uint8_T ioLen,
                   uint8_T rtuy_srcBuf[22], uint8_T *rty_retVal)
{
  int32_T i;
  uint32_T rtb_UsgHist_MotorOperTime;
  int16_T rtb_ADCMon_TAmbTemp;
  int16_T rtb_ADCMon_TMOSFETTemp;
  int16_T rtb_AmbTempMon_TAmbTemp;
  int16_T rtb_MOSFETTempMon_TMosTemp;
  int16_T rtb_MtrMdl_TEstMtrTemp;
  int16_T rtb_UsgHist_MaxAmbTemp;
  int16_T rtb_UsgHist_MinAmbTemp;
  uint16_T rtb_ADCMon_u12VBattInst;
  uint16_T rtb_ADCMon_uHallPwrInst;
  uint16_T rtb_ADCMon_uM1AVolt_mv;
  uint16_T rtb_ADCMon_uM1BVolt_mv;
  uint16_T rtb_BlockDet_hStallPos;
  uint16_T rtb_HallDeco_rMtrSpd;
  uint16_T rtb_PosMon_hCurPos;
  uint16_T rtb_SysFltRctn_stsLearnFailReason;
  uint16_T rtb_SysFltRctn_stsSysFaultReaction;
  uint16_T rtb_SysFltRctn_stsUnlearnReason;
  uint16_T rtb_UsgHist_CycRenormCount;
  uint16_T rtb_UsgHist_MaxBattVtg;
  uint16_T rtb_UsgHist_MinBattVtg;
  uint16_T rtb_UsgHist_ReversalCount;
  uint16_T rtb_UsgHist_ReversalPos;
  uint16_T rtb_UsgHist_ReversalThres;
  uint16_T rtb_UsgHist_SlideCycCount;
  uint16_T rtb_UsgHist_nCycle;
  uint16_T rtb_VoltMon_u12VBatt;
  BlockDet_Stall_dir rtb_BlockDet_stStallDir;
  CtrlLog_RelearnMode_t rtb_CtrlLog_stRelearnMode;
  HallDeco_Dir_t rtb_HallDeco_DMtrDir;
  LearnAdap_Mode_t rtb_LearnAdap_stMode;
  MtrCtrl_MtrDir_t rtb_IoHwAb_SetMtrDir;
  PnlOp_ATS_t rtb_PnlOp_stATS;
  StateMach_Mode_t rtb_StateMach_stSysMode;
  SysFltDiag_LastStopReason_t rtb_SysFltRctn_stsLasStpReason;
  ThermProt_MosfetTempClass_t rtb_ThermProt_stMosfetTempClass;
  ThermProt_MtrTempClass_t rtb_ThermProt_stMtrTempClass;
  VoltMon_VoltClass_t rtb_VoltMon_stVoltClass;
  uint8_T rtb_SysFltRctn_stLast10DTCs[10];
  uint8_T rtb_SysFltRctn_DTCStatus[3];
  uint8_T rtb_DTC_HALL_Hall1;
  uint8_T rtb_ExtDev_u8GetPWMDutyCycleVal;
  uint8_T rtb_UsgHist_LeanrFailCount;
  uint8_T rtb_UsgHist_LearnSuccessCount;
  uint8_T rtb_UsgHist_ReprogrammingCount;
  uint8_T rtb_UsgHist_StallCount;
  uint8_T rtb_UsgHist_ThermProtCount;
  boolean_T rtb_MtrCtrl_FeedbackStatus[2];
  boolean_T rtb_AmbTempMon_isAmbTempVld;
  boolean_T rtb_DIOCtrl_isHallOutPinStatus;
  boolean_T rtb_DIOCtrl_isHallSens1PinStatus;
  boolean_T rtb_DIOCtrl_isHallSens2PinStatus;
  boolean_T rtb_DIOCtrl_isINTPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankAPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankBPinStatus;
  boolean_T rtb_DIOCtrl_isVBATMeasEnbPinStatus;
  boolean_T rtb_ExtDev_isHallSuppHS2Status;
  boolean_T rtb_ExtDev_isMtrFebkCtrlHS1Status;
  boolean_T rtb_ExtDev_isSwtchSuppHS3Status;
  boolean_T rtb_LearnAdap_isLearnComplete;
  boolean_T rtb_LearnAdap_isLearningAllowed;
  boolean_T rtb_PnlOp_isMtrMoving;
  boolean_T rtb_PosMon_isCurPosVld;
  boolean_T rtb_ThermProt_isStartupRdy;
  boolean_T rtb_VoltMon_isFlctnDet;
  boolean_T rtb_isHallSupplyFault;
  CtrlLog_tenTargetDirection rtb_CtrlLog_stDirCommand;
  CtrlLog_tenTargetDirection rtb_UsgHist_ReversalDir;

  /* Outputs for Function Call SubSystem: '<Root>/PIDDID_IoCtrl' */
  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsSysFaultReaction = SCUMain_B.SysFltRctn_stsSysFaultReaction;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearningAllowed = SCUMain_B.LearnAdap_isLearningAllowed_n;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_BlockDet_hStallPos = SCUMain_B.BlockDet_hStallPos_f;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_BlockDet_stStallDir = SCUMain_B.BlockDet_stStallDir_i;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_TAmbTemp = SCUMain_B.AmbTempMon_TAmbTemp_a;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_isAmbTempVld = SCUMain_B.AmbTempMon_isAmbTempVld;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_PosMon_isCurPosVld = SCUMain_B.PosMon_isCurPosVld_m;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_PosMon_hCurPos = SCUMain_B.PosMon_hCurPos_h;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_VoltMon_stVoltClass = SCUMain_B.VoltMon_stVoltClass_p;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_VoltMon_isFlctnDet = SCUMain_B.VoltMon_isFlctnDet_o;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_VoltMon_u12VBatt = SCUMain_B.VoltMon_u12VBatt_a;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsUnlearnReason = SCUMain_B.SysFltRctn_stsUnlearnReason;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_MOSFETTempMon_TMosTemp = SCUMain_B.MOSFETTempMon_TMosTemp;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMtrTempClass = SCUMain_B.ThermProt_stMtrTempClass;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ThermProt_isStartupRdy = SCUMain_B.ThermProt_isStartupRdy;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMosfetTempClass = SCUMain_B.ThermProt_stMosfetTempClass;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_PnlOp_stATS = SCUMain_B.PnlOp_stATS_g;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_PnlOp_isMtrMoving = SCUMain_B.PnlOp_isMtrMoving_b;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_IoHwAb_SetMtrDir = SCUMain_B.IoHwAb_SetMtrDir;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_MtrCtrl_FeedbackStatus[0] = SCUMain_B.MtrCtrl_FeedbackStatus[0];
  rtb_MtrCtrl_FeedbackStatus[1] = SCUMain_B.MtrCtrl_FeedbackStatus[1];

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_CtrlLog_stDirCommand = SCUMain_B.CtrlLog_stDirCommand;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_CtrlLog_stRelearnMode = SCUMain_B.CtrlLog_stRelearnMode;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsLearnFailReason = SCUMain_B.SysFltRctn_stsLearnFailReason;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_nCycle = SCUMain_B.UsgHist_nCycle_a;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxAmbTemp = SCUMain_B.UsgHist_MaxAmbTemp_g;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinAmbTemp = SCUMain_B.UsgHist_MinAmbTemp_d;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxBattVtg = SCUMain_B.UsgHist_MaxBattVtg_e;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinBattVtg = SCUMain_B.UsgHist_MinBattVtg_c;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_MotorOperTime = SCUMain_B.UsgHist_MotorOperTime_c;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_CycRenormCount = SCUMain_B.UsgHist_CycRenormCount_a;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_LearnSuccessCount = SCUMain_B.UsgHist_LearnSuccessCount_c;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_LeanrFailCount = SCUMain_B.UsgHist_LeanrFailCount_d;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_StallCount = SCUMain_B.UsgHist_StallCount_i;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_DTCStatus[0] = SCUMain_B.SysFltRctn_DTCStatus[0];
  rtb_SysFltRctn_DTCStatus[1] = SCUMain_B.SysFltRctn_DTCStatus[1];
  rtb_SysFltRctn_DTCStatus[2] = SCUMain_B.SysFltRctn_DTCStatus[2];

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalCount = SCUMain_B.UsgHist_ReversalCount_p;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_SlideCycCount = SCUMain_B.UsgHist_SlideCycCount_o;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_ThermProtCount = SCUMain_B.UsgHist_ThermProtCount_k;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReprogrammingCount = SCUMain_B.UsgHist_ReprogrammingCount_l;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalPos = SCUMain_B.UsgHist_ReversalPos_o;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalDir = SCUMain_B.UsgHist_ReversalDir_j;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalThres = SCUMain_B.UsgHist_ReversalThres_d;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_HallDeco_DMtrDir = SCUMain_B.HallDeco_DMtrDir;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DTC_HALL_Hall1 = SCUMain_B.DTC_HALL_Hall1;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_HallDeco_rMtrSpd = SCUMain_B.HallDeco_rMtrSpd;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/SysFaultReac_Mdl'
   */
  rtb_SysFltRctn_stsLasStpReason = SCUMain_B.SysFltRctn_stsLasStpReason;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_isHallSupplyFault = SCUMain_B.isHallSupplyFault;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ADCMon_u12VBattInst = SCUMain_B.ADCMon_u12VBattInst;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ADCMon_TAmbTemp = SCUMain_B.ADCMon_TAmbTemp;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ADCMon_TMOSFETTemp = SCUMain_B.ADCMon_TMOSFETTemp;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ADCMon_uHallPwrInst = SCUMain_B.ADCMon_uHallPwrInst;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1AVolt_mv = SCUMain_B.ADCMon_uM1AVolt_mv;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1BVolt_mv = SCUMain_B.ADCMon_uM1BVolt_mv;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_StateMach_stSysMode = SCUMain_B.StateMach_stSysMode;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isVBATMeasEnbPinStatus = SCUMain_B.DIOCtrl_isVBATMeasEnbPinStatus;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallOutPinStatus = SCUMain_B.DIOCtrl_isHallOutPinStatus;
  for (i = 0; i < 10; i++) {
    /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
    rtb_SysFltRctn_stLast10DTCs[i] = SCUMain_B.SysFltRctn_stLast10DTCs_a[i];
  }

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankAPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankAPinStatus;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankBPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankBPinStatus;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens1PinStatus = SCUMain_B.DIOCtrl_isHallSens1PinStatus;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens2PinStatus = SCUMain_B.DIOCtrl_isHallSens2PinStatus;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isINTPinStatus = SCUMain_B.DIOCtrl_isINTPinStatus;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ExtDev_isMtrFebkCtrlHS1Status = SCUMain_B.ExtDev_isMtrFebkCtrlHS1Status;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ExtDev_isHallSuppHS2Status = SCUMain_B.ExtDev_isHallSuppHS2Status;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ExtDev_isSwtchSuppHS3Status = SCUMain_B.ExtDev_isSwtchSuppHS3Status;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_ExtDev_u8GetPWMDutyCycleVal = SCUMain_B.ExtDev_u8GetPWMDutyCycleVal;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_MtrMdl_TEstMtrTemp = SCUMain_B.MtrMdl_TEstMtrTemp_g;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_LearnAdap_stMode = SCUMain_B.LearnAdap_stMode;

  /* SignalConversion generated from: '<S2>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearnComplete = SCUMain_B.LearnAdap_isLearnComplete_a;

  /* ModelReference generated from: '<S2>/Model' incorporates:
   *  SignalConversion generated from: '<S2>/CtrlParam1'
   *  SignalConversion generated from: '<S2>/CtrlParam'
   *  SignalConversion generated from: '<S2>/ioDID'
   */
  PIDDID_IoCtrl_Func(&rtuy_srcBuf[0], &rtu_ioDID, &rtu_CtrlParam, &ioLen,
                     &rtb_VoltMon_stVoltClass, &SCUMain_B.Model_o1_d,
                     &SCUMain_B.Model_o2_n[0]);

  /* SignalConversion generated from: '<S2>/retVal' */
  *rty_retVal = SCUMain_B.Model_o1_d;

  /* SignalConversion generated from: '<S2>/srcBuf~' */
  for (i = 0; i < 22; i++) {
    rtuy_srcBuf[i] = SCUMain_B.Model_o2_n[i];
  }

  /* End of SignalConversion generated from: '<S2>/srcBuf~' */
  /* End of Outputs for SubSystem: '<Root>/PIDDID_IoCtrl' */
}

/* Model step function */
void PIDDID_ReadDID(uint16_T rtu_rdDID, uint8_T rty_srcBuf[252], uint8_T
                    *rty_retVal)
{
  int32_T i;
  uint32_T rtb_UsgHist_MotorOperTime;
  int16_T rtb_ADCMon_TAmbTemp;
  int16_T rtb_ADCMon_TMOSFETTemp;
  int16_T rtb_AmbTempMon_TAmbTemp;
  int16_T rtb_MOSFETTempMon_TMosTemp;
  int16_T rtb_MtrMdl_TEstMtrTemp;
  int16_T rtb_UsgHist_MaxAmbTemp;
  int16_T rtb_UsgHist_MinAmbTemp;
  uint16_T rtb_ADCMon_u12VBattInst;
  uint16_T rtb_ADCMon_uHallPwrInst;
  uint16_T rtb_ADCMon_uM1AVolt_mv;
  uint16_T rtb_ADCMon_uM1BVolt_mv;
  uint16_T rtb_BlockDet_hStallPos;
  uint16_T rtb_HallDeco_rMtrSpd;
  uint16_T rtb_PosMon_hCurPos;
  uint16_T rtb_SysFltRctn_stsLearnFailReason;
  uint16_T rtb_SysFltRctn_stsSysFaultReaction;
  uint16_T rtb_SysFltRctn_stsUnlearnReason;
  uint16_T rtb_UsgHist_CycRenormCount;
  uint16_T rtb_UsgHist_MaxBattVtg;
  uint16_T rtb_UsgHist_MinBattVtg;
  uint16_T rtb_UsgHist_ReversalCount;
  uint16_T rtb_UsgHist_ReversalPos;
  uint16_T rtb_UsgHist_ReversalThres;
  uint16_T rtb_UsgHist_SlideCycCount;
  uint16_T rtb_UsgHist_nCycle;
  uint16_T rtb_VoltMon_u12VBatt;
  BlockDet_Stall_dir rtb_BlockDet_stStallDir;
  CtrlLog_RelearnMode_t rtb_CtrlLog_stRelearnMode;
  HallDeco_Dir_t rtb_HallDeco_DMtrDir;
  LearnAdap_Mode_t rtb_LearnAdap_stMode;
  MtrCtrl_MtrDir_t rtb_IoHwAb_SetMtrDir;
  PnlOp_ATS_t rtb_PnlOp_stATS;
  StateMach_Mode_t rtb_StateMach_stSysMode;
  SysFltDiag_LastStopReason_t rtb_SysFltRctn_stsLasStpReason;
  ThermProt_MosfetTempClass_t rtb_ThermProt_stMosfetTempClass;
  ThermProt_MtrTempClass_t rtb_ThermProt_stMtrTempClass;
  VoltMon_VoltClass_t rtb_VoltMon_stVoltClass;
  uint8_T rtb_SysFltRctn_stLast10DTCs[10];
  uint8_T rtb_SysFltRctn_DTCStatus[3];
  uint8_T rtb_DTC_HALL_Hall1;
  uint8_T rtb_ExtDev_u8GetPWMDutyCycleVal;
  uint8_T rtb_UsgHist_LeanrFailCount;
  uint8_T rtb_UsgHist_LearnSuccessCount;
  uint8_T rtb_UsgHist_ReprogrammingCount;
  uint8_T rtb_UsgHist_StallCount;
  uint8_T rtb_UsgHist_ThermProtCount;
  boolean_T rtb_MtrCtrl_FeedbackStatus[2];
  boolean_T rtb_AmbTempMon_isAmbTempVld;
  boolean_T rtb_DIOCtrl_isHallOutPinStatus;
  boolean_T rtb_DIOCtrl_isHallSens1PinStatus;
  boolean_T rtb_DIOCtrl_isHallSens2PinStatus;
  boolean_T rtb_DIOCtrl_isINTPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankAPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankBPinStatus;
  boolean_T rtb_DIOCtrl_isVBATMeasEnbPinStatus;
  boolean_T rtb_ExtDev_isHallSuppHS2Status;
  boolean_T rtb_ExtDev_isMtrFebkCtrlHS1Status;
  boolean_T rtb_ExtDev_isSwtchSuppHS3Status;
  boolean_T rtb_LearnAdap_isLearnComplete;
  boolean_T rtb_LearnAdap_isLearningAllowed;
  boolean_T rtb_PnlOp_isMtrMoving;
  boolean_T rtb_PosMon_isCurPosVld;
  boolean_T rtb_ThermProt_isStartupRdy;
  boolean_T rtb_VoltMon_isFlctnDet;
  boolean_T rtb_isHallSupplyFault;
  CtrlLog_tenTargetDirection rtb_CtrlLog_stDirCommand;
  CtrlLog_tenTargetDirection rtb_UsgHist_ReversalDir;

  /* Outputs for Function Call SubSystem: '<Root>/PIDDID_ReadDID' */
  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsSysFaultReaction = SCUMain_B.SysFltRctn_stsSysFaultReaction;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearningAllowed = SCUMain_B.LearnAdap_isLearningAllowed_n;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_BlockDet_hStallPos = SCUMain_B.BlockDet_hStallPos_f;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_BlockDet_stStallDir = SCUMain_B.BlockDet_stStallDir_i;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_TAmbTemp = SCUMain_B.AmbTempMon_TAmbTemp_a;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_isAmbTempVld = SCUMain_B.AmbTempMon_isAmbTempVld;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_PosMon_isCurPosVld = SCUMain_B.PosMon_isCurPosVld_m;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_PosMon_hCurPos = SCUMain_B.PosMon_hCurPos_h;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_VoltMon_stVoltClass = SCUMain_B.VoltMon_stVoltClass_p;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_VoltMon_isFlctnDet = SCUMain_B.VoltMon_isFlctnDet_o;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_VoltMon_u12VBatt = SCUMain_B.VoltMon_u12VBatt_a;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsUnlearnReason = SCUMain_B.SysFltRctn_stsUnlearnReason;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_MOSFETTempMon_TMosTemp = SCUMain_B.MOSFETTempMon_TMosTemp;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMtrTempClass = SCUMain_B.ThermProt_stMtrTempClass;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ThermProt_isStartupRdy = SCUMain_B.ThermProt_isStartupRdy;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMosfetTempClass = SCUMain_B.ThermProt_stMosfetTempClass;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_PnlOp_stATS = SCUMain_B.PnlOp_stATS_g;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_PnlOp_isMtrMoving = SCUMain_B.PnlOp_isMtrMoving_b;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_IoHwAb_SetMtrDir = SCUMain_B.IoHwAb_SetMtrDir;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_MtrCtrl_FeedbackStatus[0] = SCUMain_B.MtrCtrl_FeedbackStatus[0];
  rtb_MtrCtrl_FeedbackStatus[1] = SCUMain_B.MtrCtrl_FeedbackStatus[1];

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_CtrlLog_stDirCommand = SCUMain_B.CtrlLog_stDirCommand;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_CtrlLog_stRelearnMode = SCUMain_B.CtrlLog_stRelearnMode;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsLearnFailReason = SCUMain_B.SysFltRctn_stsLearnFailReason;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_nCycle = SCUMain_B.UsgHist_nCycle_a;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxAmbTemp = SCUMain_B.UsgHist_MaxAmbTemp_g;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinAmbTemp = SCUMain_B.UsgHist_MinAmbTemp_d;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxBattVtg = SCUMain_B.UsgHist_MaxBattVtg_e;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinBattVtg = SCUMain_B.UsgHist_MinBattVtg_c;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_MotorOperTime = SCUMain_B.UsgHist_MotorOperTime_c;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_CycRenormCount = SCUMain_B.UsgHist_CycRenormCount_a;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_LearnSuccessCount = SCUMain_B.UsgHist_LearnSuccessCount_c;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_LeanrFailCount = SCUMain_B.UsgHist_LeanrFailCount_d;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_StallCount = SCUMain_B.UsgHist_StallCount_i;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_DTCStatus[0] = SCUMain_B.SysFltRctn_DTCStatus[0];
  rtb_SysFltRctn_DTCStatus[1] = SCUMain_B.SysFltRctn_DTCStatus[1];
  rtb_SysFltRctn_DTCStatus[2] = SCUMain_B.SysFltRctn_DTCStatus[2];

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalCount = SCUMain_B.UsgHist_ReversalCount_p;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_SlideCycCount = SCUMain_B.UsgHist_SlideCycCount_o;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_ThermProtCount = SCUMain_B.UsgHist_ThermProtCount_k;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReprogrammingCount = SCUMain_B.UsgHist_ReprogrammingCount_l;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalPos = SCUMain_B.UsgHist_ReversalPos_o;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalDir = SCUMain_B.UsgHist_ReversalDir_j;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalThres = SCUMain_B.UsgHist_ReversalThres_d;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_HallDeco_DMtrDir = SCUMain_B.HallDeco_DMtrDir;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DTC_HALL_Hall1 = SCUMain_B.DTC_HALL_Hall1;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_HallDeco_rMtrSpd = SCUMain_B.HallDeco_rMtrSpd;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/SysFaultReac_Mdl'
   */
  rtb_SysFltRctn_stsLasStpReason = SCUMain_B.SysFltRctn_stsLasStpReason;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_isHallSupplyFault = SCUMain_B.isHallSupplyFault;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ADCMon_u12VBattInst = SCUMain_B.ADCMon_u12VBattInst;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ADCMon_TAmbTemp = SCUMain_B.ADCMon_TAmbTemp;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ADCMon_TMOSFETTemp = SCUMain_B.ADCMon_TMOSFETTemp;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ADCMon_uHallPwrInst = SCUMain_B.ADCMon_uHallPwrInst;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1AVolt_mv = SCUMain_B.ADCMon_uM1AVolt_mv;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1BVolt_mv = SCUMain_B.ADCMon_uM1BVolt_mv;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_StateMach_stSysMode = SCUMain_B.StateMach_stSysMode;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isVBATMeasEnbPinStatus = SCUMain_B.DIOCtrl_isVBATMeasEnbPinStatus;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallOutPinStatus = SCUMain_B.DIOCtrl_isHallOutPinStatus;
  for (i = 0; i < 10; i++) {
    /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
    rtb_SysFltRctn_stLast10DTCs[i] = SCUMain_B.SysFltRctn_stLast10DTCs_a[i];
  }

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankAPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankAPinStatus;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankBPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankBPinStatus;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens1PinStatus = SCUMain_B.DIOCtrl_isHallSens1PinStatus;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens2PinStatus = SCUMain_B.DIOCtrl_isHallSens2PinStatus;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isINTPinStatus = SCUMain_B.DIOCtrl_isINTPinStatus;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ExtDev_isMtrFebkCtrlHS1Status = SCUMain_B.ExtDev_isMtrFebkCtrlHS1Status;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ExtDev_isHallSuppHS2Status = SCUMain_B.ExtDev_isHallSuppHS2Status;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ExtDev_isSwtchSuppHS3Status = SCUMain_B.ExtDev_isSwtchSuppHS3Status;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_ExtDev_u8GetPWMDutyCycleVal = SCUMain_B.ExtDev_u8GetPWMDutyCycleVal;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_MtrMdl_TEstMtrTemp = SCUMain_B.MtrMdl_TEstMtrTemp_g;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_LearnAdap_stMode = SCUMain_B.LearnAdap_stMode;

  /* SignalConversion generated from: '<S3>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearnComplete = SCUMain_B.LearnAdap_isLearnComplete_a;

  /* ModelReference generated from: '<S3>/Model' incorporates:
   *  SignalConversion generated from: '<S3>/rdDID'
   */
  PIDDID_ReadDID_Func(&rtu_rdDID, &rtb_SysFltRctn_stsUnlearnReason,
                      &rtb_SysFltRctn_stsLearnFailReason,
                      &rtb_SysFltRctn_DTCStatus[0],
                      &rtb_SysFltRctn_stLast10DTCs[0],
                      &rtb_LearnAdap_isLearnComplete,
                      &rtb_LearnAdap_isLearningAllowed, &rtb_BlockDet_hStallPos,
                      &rtb_BlockDet_stStallDir, &rtb_PosMon_isCurPosVld,
                      &rtb_PosMon_hCurPos, &rtb_VoltMon_stVoltClass,
                      &rtb_VoltMon_isFlctnDet, &rtb_VoltMon_u12VBatt,
                      &rtb_ThermProt_stMtrTempClass,
                      &rtb_ThermProt_stMosfetTempClass,
                      &rtb_MtrCtrl_FeedbackStatus[0], &rtb_CtrlLog_stDirCommand,
                      &rtb_UsgHist_MaxAmbTemp, &rtb_UsgHist_MinAmbTemp,
                      &rtb_UsgHist_MaxBattVtg, &rtb_UsgHist_MinBattVtg,
                      &rtb_UsgHist_MotorOperTime, &rtb_UsgHist_CycRenormCount,
                      &rtb_UsgHist_LearnSuccessCount,
                      &rtb_UsgHist_LeanrFailCount, &rtb_UsgHist_StallCount,
                      &rtb_UsgHist_ReversalCount, &rtb_UsgHist_SlideCycCount,
                      &rtb_UsgHist_ThermProtCount,
                      &rtb_UsgHist_ReprogrammingCount, &rtb_UsgHist_ReversalPos,
                      &rtb_UsgHist_ReversalDir, &rtb_UsgHist_ReversalThres,
                      &rtb_HallDeco_rMtrSpd, &rtb_ADCMon_u12VBattInst,
                      &rtb_ADCMon_TAmbTemp, &rtb_ADCMon_TMOSFETTemp,
                      &rtb_ADCMon_uHallPwrInst, &rtb_ADCMon_uM1AVolt_mv,
                      &rtb_ADCMon_uM1BVolt_mv, &rtb_StateMach_stSysMode,
                      &rtb_DIOCtrl_isVBATMeasEnbPinStatus,
                      &rtb_DIOCtrl_isHallOutPinStatus,
                      &rtb_DIOCtrl_isMtrFltrBlankAPinStatus,
                      &rtb_DIOCtrl_isMtrFltrBlankBPinStatus,
                      &rtb_DIOCtrl_isHallSens1PinStatus,
                      &rtb_DIOCtrl_isHallSens2PinStatus,
                      &rtb_DIOCtrl_isINTPinStatus,
                      &rtb_ExtDev_isMtrFebkCtrlHS1Status,
                      &rtb_ExtDev_isHallSuppHS2Status,
                      &rtb_ExtDev_isSwtchSuppHS3Status,
                      &rtb_ExtDev_u8GetPWMDutyCycleVal, &SCUMain_B.Model_o1_f,
                      &SCUMain_B.srcBuf[0]);

  /* SignalConversion generated from: '<S3>/retVal' */
  *rty_retVal = SCUMain_B.Model_o1_f;

  /* SignalConversion generated from: '<S3>/srcBuf' */
  memcpy(&rty_srcBuf[0], &SCUMain_B.srcBuf[0], 252U * sizeof(uint8_T));

  /* End of Outputs for SubSystem: '<Root>/PIDDID_ReadDID' */
}

/* Model step function */
void PIDDID_RoutineCtrl(uint16_T rtu_rtnID, uint8_T rtu_rtnType, uint8_T
  rtu_rtnLen, const uint8_T rtu_rtnOption[10], uint8_T rty_TxData[10], uint8_T
  *rty_retVal)
{
  int32_T i;
  uint32_T rtb_UsgHist_MotorOperTime;
  int16_T rtb_ADCMon_TAmbTemp;
  int16_T rtb_ADCMon_TMOSFETTemp;
  int16_T rtb_AmbTempMon_TAmbTemp;
  int16_T rtb_MOSFETTempMon_TMosTemp;
  int16_T rtb_MtrMdl_TEstMtrTemp;
  int16_T rtb_UsgHist_MaxAmbTemp;
  int16_T rtb_UsgHist_MinAmbTemp;
  uint16_T rtb_ADCMon_u12VBattInst;
  uint16_T rtb_ADCMon_uHallPwrInst;
  uint16_T rtb_ADCMon_uM1AVolt_mv;
  uint16_T rtb_ADCMon_uM1BVolt_mv;
  uint16_T rtb_BlockDet_hStallPos;
  uint16_T rtb_HallDeco_rMtrSpd;
  uint16_T rtb_PosMon_hCurPos;
  uint16_T rtb_SysFltRctn_stsLearnFailReason;
  uint16_T rtb_SysFltRctn_stsSysFaultReaction;
  uint16_T rtb_SysFltRctn_stsUnlearnReason;
  uint16_T rtb_UsgHist_CycRenormCount;
  uint16_T rtb_UsgHist_MaxBattVtg;
  uint16_T rtb_UsgHist_MinBattVtg;
  uint16_T rtb_UsgHist_ReversalCount;
  uint16_T rtb_UsgHist_ReversalPos;
  uint16_T rtb_UsgHist_ReversalThres;
  uint16_T rtb_UsgHist_SlideCycCount;
  uint16_T rtb_UsgHist_nCycle;
  uint16_T rtb_VoltMon_u12VBatt;
  BlockDet_Stall_dir rtb_BlockDet_stStallDir;
  CtrlLog_RelearnMode_t rtb_CtrlLog_stRelearnMode;
  HallDeco_Dir_t rtb_HallDeco_DMtrDir;
  LearnAdap_Mode_t rtb_LearnAdap_stMode;
  MtrCtrl_MtrDir_t rtb_IoHwAb_SetMtrDir;
  PnlOp_ATS_t rtb_PnlOp_stATS;
  StateMach_Mode_t rtb_StateMach_stSysMode;
  SysFltDiag_LastStopReason_t rtb_SysFltRctn_stsLasStpReason;
  ThermProt_MosfetTempClass_t rtb_ThermProt_stMosfetTempClass;
  ThermProt_MtrTempClass_t rtb_ThermProt_stMtrTempClass;
  VoltMon_VoltClass_t rtb_VoltMon_stVoltClass;
  uint8_T rtb_SysFltRctn_stLast10DTCs[10];
  uint8_T rtb_SysFltRctn_DTCStatus[3];
  uint8_T rtb_DTC_HALL_Hall1;
  uint8_T rtb_ExtDev_u8GetPWMDutyCycleVal;
  uint8_T rtb_UsgHist_LeanrFailCount;
  uint8_T rtb_UsgHist_LearnSuccessCount;
  uint8_T rtb_UsgHist_ReprogrammingCount;
  uint8_T rtb_UsgHist_StallCount;
  uint8_T rtb_UsgHist_ThermProtCount;
  boolean_T rtb_MtrCtrl_FeedbackStatus[2];
  boolean_T rtb_AmbTempMon_isAmbTempVld;
  boolean_T rtb_DIOCtrl_isHallOutPinStatus;
  boolean_T rtb_DIOCtrl_isHallSens1PinStatus;
  boolean_T rtb_DIOCtrl_isHallSens2PinStatus;
  boolean_T rtb_DIOCtrl_isINTPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankAPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankBPinStatus;
  boolean_T rtb_DIOCtrl_isVBATMeasEnbPinStatus;
  boolean_T rtb_ExtDev_isHallSuppHS2Status;
  boolean_T rtb_ExtDev_isMtrFebkCtrlHS1Status;
  boolean_T rtb_ExtDev_isSwtchSuppHS3Status;
  boolean_T rtb_LearnAdap_isLearnComplete;
  boolean_T rtb_LearnAdap_isLearningAllowed;
  boolean_T rtb_PnlOp_isMtrMoving;
  boolean_T rtb_PosMon_isCurPosVld;
  boolean_T rtb_ThermProt_isStartupRdy;
  boolean_T rtb_VoltMon_isFlctnDet;
  boolean_T rtb_isHallSupplyFault;
  CtrlLog_tenTargetDirection rtb_CtrlLog_stDirCommand;
  CtrlLog_tenTargetDirection rtb_UsgHist_ReversalDir;

  /* Outputs for Function Call SubSystem: '<Root>/PIDDID_RoutineCtrl' */
  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsSysFaultReaction = SCUMain_B.SysFltRctn_stsSysFaultReaction;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearningAllowed = SCUMain_B.LearnAdap_isLearningAllowed_n;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_BlockDet_hStallPos = SCUMain_B.BlockDet_hStallPos_f;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_BlockDet_stStallDir = SCUMain_B.BlockDet_stStallDir_i;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_TAmbTemp = SCUMain_B.AmbTempMon_TAmbTemp_a;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_isAmbTempVld = SCUMain_B.AmbTempMon_isAmbTempVld;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_PosMon_isCurPosVld = SCUMain_B.PosMon_isCurPosVld_m;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_PosMon_hCurPos = SCUMain_B.PosMon_hCurPos_h;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_VoltMon_stVoltClass = SCUMain_B.VoltMon_stVoltClass_p;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_VoltMon_isFlctnDet = SCUMain_B.VoltMon_isFlctnDet_o;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_VoltMon_u12VBatt = SCUMain_B.VoltMon_u12VBatt_a;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsUnlearnReason = SCUMain_B.SysFltRctn_stsUnlearnReason;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_MOSFETTempMon_TMosTemp = SCUMain_B.MOSFETTempMon_TMosTemp;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMtrTempClass = SCUMain_B.ThermProt_stMtrTempClass;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ThermProt_isStartupRdy = SCUMain_B.ThermProt_isStartupRdy;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMosfetTempClass = SCUMain_B.ThermProt_stMosfetTempClass;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_PnlOp_stATS = SCUMain_B.PnlOp_stATS_g;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_PnlOp_isMtrMoving = SCUMain_B.PnlOp_isMtrMoving_b;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_IoHwAb_SetMtrDir = SCUMain_B.IoHwAb_SetMtrDir;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_MtrCtrl_FeedbackStatus[0] = SCUMain_B.MtrCtrl_FeedbackStatus[0];
  rtb_MtrCtrl_FeedbackStatus[1] = SCUMain_B.MtrCtrl_FeedbackStatus[1];

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_CtrlLog_stDirCommand = SCUMain_B.CtrlLog_stDirCommand;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_CtrlLog_stRelearnMode = SCUMain_B.CtrlLog_stRelearnMode;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsLearnFailReason = SCUMain_B.SysFltRctn_stsLearnFailReason;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_nCycle = SCUMain_B.UsgHist_nCycle_a;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxAmbTemp = SCUMain_B.UsgHist_MaxAmbTemp_g;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinAmbTemp = SCUMain_B.UsgHist_MinAmbTemp_d;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxBattVtg = SCUMain_B.UsgHist_MaxBattVtg_e;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinBattVtg = SCUMain_B.UsgHist_MinBattVtg_c;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_MotorOperTime = SCUMain_B.UsgHist_MotorOperTime_c;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_CycRenormCount = SCUMain_B.UsgHist_CycRenormCount_a;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_LearnSuccessCount = SCUMain_B.UsgHist_LearnSuccessCount_c;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_LeanrFailCount = SCUMain_B.UsgHist_LeanrFailCount_d;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_StallCount = SCUMain_B.UsgHist_StallCount_i;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_DTCStatus[0] = SCUMain_B.SysFltRctn_DTCStatus[0];
  rtb_SysFltRctn_DTCStatus[1] = SCUMain_B.SysFltRctn_DTCStatus[1];
  rtb_SysFltRctn_DTCStatus[2] = SCUMain_B.SysFltRctn_DTCStatus[2];

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalCount = SCUMain_B.UsgHist_ReversalCount_p;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_SlideCycCount = SCUMain_B.UsgHist_SlideCycCount_o;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_ThermProtCount = SCUMain_B.UsgHist_ThermProtCount_k;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReprogrammingCount = SCUMain_B.UsgHist_ReprogrammingCount_l;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalPos = SCUMain_B.UsgHist_ReversalPos_o;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalDir = SCUMain_B.UsgHist_ReversalDir_j;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalThres = SCUMain_B.UsgHist_ReversalThres_d;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_HallDeco_DMtrDir = SCUMain_B.HallDeco_DMtrDir;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DTC_HALL_Hall1 = SCUMain_B.DTC_HALL_Hall1;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_HallDeco_rMtrSpd = SCUMain_B.HallDeco_rMtrSpd;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/SysFaultReac_Mdl'
   */
  rtb_SysFltRctn_stsLasStpReason = SCUMain_B.SysFltRctn_stsLasStpReason;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_isHallSupplyFault = SCUMain_B.isHallSupplyFault;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ADCMon_u12VBattInst = SCUMain_B.ADCMon_u12VBattInst;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ADCMon_TAmbTemp = SCUMain_B.ADCMon_TAmbTemp;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ADCMon_TMOSFETTemp = SCUMain_B.ADCMon_TMOSFETTemp;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ADCMon_uHallPwrInst = SCUMain_B.ADCMon_uHallPwrInst;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1AVolt_mv = SCUMain_B.ADCMon_uM1AVolt_mv;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1BVolt_mv = SCUMain_B.ADCMon_uM1BVolt_mv;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_StateMach_stSysMode = SCUMain_B.StateMach_stSysMode;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isVBATMeasEnbPinStatus = SCUMain_B.DIOCtrl_isVBATMeasEnbPinStatus;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallOutPinStatus = SCUMain_B.DIOCtrl_isHallOutPinStatus;
  for (i = 0; i < 10; i++) {
    /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
    rtb_SysFltRctn_stLast10DTCs[i] = SCUMain_B.SysFltRctn_stLast10DTCs_a[i];
  }

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankAPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankAPinStatus;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankBPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankBPinStatus;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens1PinStatus = SCUMain_B.DIOCtrl_isHallSens1PinStatus;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens2PinStatus = SCUMain_B.DIOCtrl_isHallSens2PinStatus;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isINTPinStatus = SCUMain_B.DIOCtrl_isINTPinStatus;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ExtDev_isMtrFebkCtrlHS1Status = SCUMain_B.ExtDev_isMtrFebkCtrlHS1Status;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ExtDev_isHallSuppHS2Status = SCUMain_B.ExtDev_isHallSuppHS2Status;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ExtDev_isSwtchSuppHS3Status = SCUMain_B.ExtDev_isSwtchSuppHS3Status;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_ExtDev_u8GetPWMDutyCycleVal = SCUMain_B.ExtDev_u8GetPWMDutyCycleVal;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_MtrMdl_TEstMtrTemp = SCUMain_B.MtrMdl_TEstMtrTemp_g;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_LearnAdap_stMode = SCUMain_B.LearnAdap_stMode;

  /* SignalConversion generated from: '<S4>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearnComplete = SCUMain_B.LearnAdap_isLearnComplete_a;

  /* ModelReference generated from: '<S4>/Model' incorporates:
   *  SignalConversion generated from: '<S4>/rtnID'
   *  SignalConversion generated from: '<S4>/rtnLen'
   *  SignalConversion generated from: '<S4>/rtnOption'
   *  SignalConversion generated from: '<S4>/rtnType'
   */
  PIDDID_RoutineCtrl_Func(&rtu_rtnID, &rtu_rtnType, &rtu_rtnLen, &rtu_rtnOption
    [0], &rtb_LearnAdap_isLearningAllowed, &rtb_PosMon_isCurPosVld,
    &rtb_VoltMon_stVoltClass, &rtb_CtrlLog_stDirCommand, &rtb_isHallSupplyFault,
    &SCUMain_B.Model_o1, &SCUMain_B.Model_o2[0]);

  /* SignalConversion generated from: '<S4>/TxData~' */
  for (i = 0; i < 10; i++) {
    rty_TxData[i] = SCUMain_B.Model_o2[i];
  }

  /* End of SignalConversion generated from: '<S4>/TxData~' */

  /* SignalConversion generated from: '<S4>/retVal' */
  *rty_retVal = SCUMain_B.Model_o1;

  /* End of Outputs for SubSystem: '<Root>/PIDDID_RoutineCtrl' */
}

/* Model step function */
uint8_T PIDDID_WriteDID(uint16_T rtu_wrDID, const uint8_T rtu_srcBuf[252],
  uint8_T rtu_wrLen)
{
  uint8_T rty_retVal_0;
  int32_T i;
  uint32_T rtb_UsgHist_MotorOperTime;
  int16_T rtb_ADCMon_TAmbTemp;
  int16_T rtb_ADCMon_TMOSFETTemp;
  int16_T rtb_AmbTempMon_TAmbTemp;
  int16_T rtb_MOSFETTempMon_TMosTemp;
  int16_T rtb_MtrMdl_TEstMtrTemp;
  int16_T rtb_UsgHist_MaxAmbTemp;
  int16_T rtb_UsgHist_MinAmbTemp;
  uint16_T rtb_ADCMon_u12VBattInst;
  uint16_T rtb_ADCMon_uHallPwrInst;
  uint16_T rtb_ADCMon_uM1AVolt_mv;
  uint16_T rtb_ADCMon_uM1BVolt_mv;
  uint16_T rtb_BlockDet_hStallPos;
  uint16_T rtb_HallDeco_rMtrSpd;
  uint16_T rtb_PosMon_hCurPos;
  uint16_T rtb_SysFltRctn_stsLearnFailReason;
  uint16_T rtb_SysFltRctn_stsSysFaultReaction;
  uint16_T rtb_SysFltRctn_stsUnlearnReason;
  uint16_T rtb_UsgHist_CycRenormCount;
  uint16_T rtb_UsgHist_MaxBattVtg;
  uint16_T rtb_UsgHist_MinBattVtg;
  uint16_T rtb_UsgHist_ReversalCount;
  uint16_T rtb_UsgHist_ReversalPos;
  uint16_T rtb_UsgHist_ReversalThres;
  uint16_T rtb_UsgHist_SlideCycCount;
  uint16_T rtb_UsgHist_nCycle;
  uint16_T rtb_VoltMon_u12VBatt;
  BlockDet_Stall_dir rtb_BlockDet_stStallDir;
  CtrlLog_RelearnMode_t rtb_CtrlLog_stRelearnMode;
  HallDeco_Dir_t rtb_HallDeco_DMtrDir;
  LearnAdap_Mode_t rtb_LearnAdap_stMode;
  MtrCtrl_MtrDir_t rtb_IoHwAb_SetMtrDir;
  PnlOp_ATS_t rtb_PnlOp_stATS;
  StateMach_Mode_t rtb_StateMach_stSysMode;
  SysFltDiag_LastStopReason_t rtb_SysFltRctn_stsLasStpReason;
  ThermProt_MosfetTempClass_t rtb_ThermProt_stMosfetTempClass;
  ThermProt_MtrTempClass_t rtb_ThermProt_stMtrTempClass;
  VoltMon_VoltClass_t rtb_VoltMon_stVoltClass;
  uint8_T rtb_SysFltRctn_stLast10DTCs[10];
  uint8_T rtb_SysFltRctn_DTCStatus[3];
  uint8_T rtb_DTC_HALL_Hall1;
  uint8_T rtb_ExtDev_u8GetPWMDutyCycleVal;
  uint8_T rtb_UsgHist_LeanrFailCount;
  uint8_T rtb_UsgHist_LearnSuccessCount;
  uint8_T rtb_UsgHist_ReprogrammingCount;
  uint8_T rtb_UsgHist_StallCount;
  uint8_T rtb_UsgHist_ThermProtCount;
  boolean_T rtb_MtrCtrl_FeedbackStatus[2];
  boolean_T rtb_AmbTempMon_isAmbTempVld;
  boolean_T rtb_DIOCtrl_isHallOutPinStatus;
  boolean_T rtb_DIOCtrl_isHallSens1PinStatus;
  boolean_T rtb_DIOCtrl_isHallSens2PinStatus;
  boolean_T rtb_DIOCtrl_isINTPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankAPinStatus;
  boolean_T rtb_DIOCtrl_isMtrFltrBlankBPinStatus;
  boolean_T rtb_DIOCtrl_isVBATMeasEnbPinStatus;
  boolean_T rtb_ExtDev_isHallSuppHS2Status;
  boolean_T rtb_ExtDev_isMtrFebkCtrlHS1Status;
  boolean_T rtb_ExtDev_isSwtchSuppHS3Status;
  boolean_T rtb_LearnAdap_isLearnComplete;
  boolean_T rtb_LearnAdap_isLearningAllowed;
  boolean_T rtb_PnlOp_isMtrMoving;
  boolean_T rtb_PosMon_isCurPosVld;
  boolean_T rtb_ThermProt_isStartupRdy;
  boolean_T rtb_VoltMon_isFlctnDet;
  boolean_T rtb_isHallSupplyFault;
  CtrlLog_tenTargetDirection rtb_CtrlLog_stDirCommand;
  CtrlLog_tenTargetDirection rtb_UsgHist_ReversalDir;
  UNUSED_PARAMETER(rtu_wrLen);

  /* Outputs for Function Call SubSystem: '<Root>/PIDDID_WriteDID' */
  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsSysFaultReaction = SCUMain_B.SysFltRctn_stsSysFaultReaction;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearningAllowed = SCUMain_B.LearnAdap_isLearningAllowed_n;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_BlockDet_hStallPos = SCUMain_B.BlockDet_hStallPos_f;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_BlockDet_stStallDir = SCUMain_B.BlockDet_stStallDir_i;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_TAmbTemp = SCUMain_B.AmbTempMon_TAmbTemp_a;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_AmbTempMon_isAmbTempVld = SCUMain_B.AmbTempMon_isAmbTempVld;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_PosMon_isCurPosVld = SCUMain_B.PosMon_isCurPosVld_m;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_PosMon_hCurPos = SCUMain_B.PosMon_hCurPos_h;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_VoltMon_stVoltClass = SCUMain_B.VoltMon_stVoltClass_p;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_VoltMon_isFlctnDet = SCUMain_B.VoltMon_isFlctnDet_o;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_VoltMon_u12VBatt = SCUMain_B.VoltMon_u12VBatt_a;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsUnlearnReason = SCUMain_B.SysFltRctn_stsUnlearnReason;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_MOSFETTempMon_TMosTemp = SCUMain_B.MOSFETTempMon_TMosTemp;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMtrTempClass = SCUMain_B.ThermProt_stMtrTempClass;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ThermProt_isStartupRdy = SCUMain_B.ThermProt_isStartupRdy;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_ThermProt_stMosfetTempClass = SCUMain_B.ThermProt_stMosfetTempClass;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_PnlOp_stATS = SCUMain_B.PnlOp_stATS_g;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_PnlOp_isMtrMoving = SCUMain_B.PnlOp_isMtrMoving_b;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_IoHwAb_SetMtrDir = SCUMain_B.IoHwAb_SetMtrDir;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_MtrCtrl_FeedbackStatus[0] = SCUMain_B.MtrCtrl_FeedbackStatus[0];
  rtb_MtrCtrl_FeedbackStatus[1] = SCUMain_B.MtrCtrl_FeedbackStatus[1];

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_CtrlLog_stDirCommand = SCUMain_B.CtrlLog_stDirCommand;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_CtrlLog_stRelearnMode = SCUMain_B.CtrlLog_stRelearnMode;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_stsLearnFailReason = SCUMain_B.SysFltRctn_stsLearnFailReason;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_nCycle = SCUMain_B.UsgHist_nCycle_a;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxAmbTemp = SCUMain_B.UsgHist_MaxAmbTemp_g;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinAmbTemp = SCUMain_B.UsgHist_MinAmbTemp_d;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_MaxBattVtg = SCUMain_B.UsgHist_MaxBattVtg_e;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_MinBattVtg = SCUMain_B.UsgHist_MinBattVtg_c;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_MotorOperTime = SCUMain_B.UsgHist_MotorOperTime_c;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_CycRenormCount = SCUMain_B.UsgHist_CycRenormCount_a;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_LearnSuccessCount = SCUMain_B.UsgHist_LearnSuccessCount_c;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_LeanrFailCount = SCUMain_B.UsgHist_LeanrFailCount_d;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_StallCount = SCUMain_B.UsgHist_StallCount_i;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_SysFltRctn_DTCStatus[0] = SCUMain_B.SysFltRctn_DTCStatus[0];
  rtb_SysFltRctn_DTCStatus[1] = SCUMain_B.SysFltRctn_DTCStatus[1];
  rtb_SysFltRctn_DTCStatus[2] = SCUMain_B.SysFltRctn_DTCStatus[2];

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalCount = SCUMain_B.UsgHist_ReversalCount_p;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_SlideCycCount = SCUMain_B.UsgHist_SlideCycCount_o;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_ThermProtCount = SCUMain_B.UsgHist_ThermProtCount_k;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReprogrammingCount = SCUMain_B.UsgHist_ReprogrammingCount_l;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalPos = SCUMain_B.UsgHist_ReversalPos_o;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalDir = SCUMain_B.UsgHist_ReversalDir_j;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_UsgHist_ReversalThres = SCUMain_B.UsgHist_ReversalThres_d;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_HallDeco_DMtrDir = SCUMain_B.HallDeco_DMtrDir;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DTC_HALL_Hall1 = SCUMain_B.DTC_HALL_Hall1;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_HallDeco_rMtrSpd = SCUMain_B.HallDeco_rMtrSpd;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/SysFaultReac_Mdl'
   */
  rtb_SysFltRctn_stsLasStpReason = SCUMain_B.SysFltRctn_stsLasStpReason;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_isHallSupplyFault = SCUMain_B.isHallSupplyFault;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ADCMon_u12VBattInst = SCUMain_B.ADCMon_u12VBattInst;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ADCMon_TAmbTemp = SCUMain_B.ADCMon_TAmbTemp;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ADCMon_TMOSFETTemp = SCUMain_B.ADCMon_TMOSFETTemp;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ADCMon_uHallPwrInst = SCUMain_B.ADCMon_uHallPwrInst;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1AVolt_mv = SCUMain_B.ADCMon_uM1AVolt_mv;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ADCMon_uM1BVolt_mv = SCUMain_B.ADCMon_uM1BVolt_mv;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_StateMach_stSysMode = SCUMain_B.StateMach_stSysMode;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isVBATMeasEnbPinStatus = SCUMain_B.DIOCtrl_isVBATMeasEnbPinStatus;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallOutPinStatus = SCUMain_B.DIOCtrl_isHallOutPinStatus;
  for (i = 0; i < 10; i++) {
    /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
    rtb_SysFltRctn_stLast10DTCs[i] = SCUMain_B.SysFltRctn_stLast10DTCs_a[i];
  }

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankAPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankAPinStatus;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isMtrFltrBlankBPinStatus =
    SCUMain_B.DIOCtrl_isMtrFltrBlankBPinStatus;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens1PinStatus = SCUMain_B.DIOCtrl_isHallSens1PinStatus;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isHallSens2PinStatus = SCUMain_B.DIOCtrl_isHallSens2PinStatus;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_DIOCtrl_isINTPinStatus = SCUMain_B.DIOCtrl_isINTPinStatus;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ExtDev_isMtrFebkCtrlHS1Status = SCUMain_B.ExtDev_isMtrFebkCtrlHS1Status;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ExtDev_isHallSuppHS2Status = SCUMain_B.ExtDev_isHallSuppHS2Status;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ExtDev_isSwtchSuppHS3Status = SCUMain_B.ExtDev_isSwtchSuppHS3Status;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_ExtDev_u8GetPWMDutyCycleVal = SCUMain_B.ExtDev_u8GetPWMDutyCycleVal;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_MtrMdl_TEstMtrTemp = SCUMain_B.MtrMdl_TEstMtrTemp_g;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' incorporates:
   *  ModelReference generated from: '<S13>/PIDDID_Mdl'
   */
  rtb_LearnAdap_stMode = SCUMain_B.LearnAdap_stMode;

  /* SignalConversion generated from: '<S5>/SWC_DiagLyrBus' */
  rtb_LearnAdap_isLearnComplete = SCUMain_B.LearnAdap_isLearnComplete_a;

  /* ModelReference generated from: '<S5>/PIDDID_WriteDID_func' incorporates:
   *  SignalConversion generated from: '<S5>/srcBuf'
   *  SignalConversion generated from: '<S5>/wrDID'
   */
  PIDDID_WriteDID_Func(&rtu_srcBuf[0], &rtu_wrDID, &rtb_VoltMon_stVoltClass,
                       &rtb_CtrlLog_stDirCommand, &rtb_ADCMon_u12VBattInst,
                       &rtb_ADCMon_TAmbTemp, &rtb_ADCMon_TMOSFETTemp,
                       &SCUMain_B.PIDDID_WriteDID_func);

  /* SignalConversion generated from: '<S5>/retVal' */
  rty_retVal_0 = SCUMain_B.PIDDID_WriteDID_func;

  /* End of Outputs for SubSystem: '<Root>/PIDDID_WriteDID' */
  return rty_retVal_0;
}

/* Model step function */
void SCUMain_vTrigger(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/SCUMain_vTrigger' incorporates:
   *  SubSystem: '<Root>/SCU Software'
   */
  /* Outputs for Atomic SubSystem: '<S10>/TaskSchedular_Mdl' */
  SCUMain_TaskSchedular_Mdl();

  /* End of Outputs for SubSystem: '<S10>/TaskSchedular_Mdl' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SCUMain_vTrigger' */
}

/* Model step function */
void SCUMain_hallInterrupt(uint8_T rtu_ISR_u8ChannelId, uint16_T
  rtu_ISR_u16Counter, boolean_T rtu_ISR_isRising)
{
  /* Outputs for Function Call SubSystem: '<Root>/Simulink Function' */
  /* SignalConversion generated from: '<S7>/u1' */
  SCUMain_B.inputCaptureValue = rtu_ISR_u16Counter;

  /* ModelReference: '<S7>/Model' incorporates:
   *  SignalConversion generated from: '<S7>/u2'
   *  SignalConversion generated from: '<S7>/u'
   */
  HALLDeco_Mdl_ISR(&rtu_ISR_u8ChannelId, &SCUMain_B.inputCaptureValue,
                   &rtu_ISR_isRising, &SCUMain_B.HallCounts,
                   &SCUMain_B.Direction, &SCUMain_B.PulseTime,
                   rtCP_Model_ChannelIdSensor1);

  /* End of Outputs for SubSystem: '<Root>/Simulink Function' */
}

/* Model initialize function */
void SCUMain_initialize(void)
{
  /* Registration code */

  /* states (dwork) */

  /* custom states */
  PIDDID_stClearRtnResult = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stControlIntermediatePosRoutineResult =
    PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stDummyRPRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stNVMSyncRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stOpenClosePosRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stOverridePWMRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_AutoCyc = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_ClearRPRF = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_GotoTgtPos = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_IOTestRtn = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_LearnRtn = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_MtrCtrlrtn = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_NormMtrRtn = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_RoofCtrlrtn = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stRoutineResult_SuppressCyclicRenorm =
    PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stSoftStartRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_stSoftStopRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;
  PIDDID_WriteID = PIDDID_WriteID_t_NO_ID;

  /* Model Initialize function for ModelReference Block: '<S7>/Model' */
  HALLDeco_Mdl_ISR_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S2>/Model' */
  PIDDID_IoCtrl_Func_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S3>/Model' */
  PIDDID_ReadDID_Func_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S4>/Model' */
  PIDDID_RoutineCtrl_Func_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S5>/PIDDID_WriteDID_func' */
  PIDDID_WriteDID_Func_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S11>/VehCom_Mdl' */
  VehCom_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S12>/CfgParam_Mdl' */
  CfgParam_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S12>/RefField_Mdl' */
  RefField_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S12>/UsgHis_Mdl' */
  UsgHis_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S13>/PIDDID_Mdl' */
  PIDDID_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S13>/SysFaultReac_Mdl' */
  SysFaultReac_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S14>/APDet_Mdl' */
  APDet_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S14>/BlockDet_Mdl' */
  BlockDet_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S14>/LearnAdap_Mdl' */
  LearnAdap_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S14>/MtrMdl_Mdl' */
  MtrMdl_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S14>/RefForce_Mdl' */
  RefForce_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S14>/TheshForce_Mdl' */
  TheshForce_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S15>/ComLog_Mdl' */
  ComLog_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S15>/CtrlLog_Mdl' */
  CtrlLog_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S15>/MtrCtrl_Mdl' */
  MtrCtrl_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S15>/PnlOp_Mdl' */
  PnlOp_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S15>/ThermProt_Mdl' */
  ThermProt_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S16>/AmbTempMon_Mdl' */
  AmbTempMon_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S16>/MOSFETTempMon_Mdl' */
  MOSFETTempMon_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S16>/PosMon_Mdl' */
  PosMon_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S16>/SwitchLog_Mdl' */
  SwitchLog_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S16>/VoltMon_Mdl' */
  VoltMon_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S17>/StateMach_Mdl' */
  StateMach_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S9>/ADCMon_Mdl' */
  ADCMon_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S9>/DIOCtrl_Mdl' */
  DIOCtrl_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S9>/DiagComMan' */
  DiagComMan_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S9>/ExtDev_Mdl' */
  ExtDev_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S9>/HALLDeco_Mdl' */
  HALLDeco_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  /* Model Initialize function for ModelReference Block: '<S9>/PWMCtrl_Mdl' */
  PWMCtrl_Mdl_initialize(rtmGetErrorStatusPointer(SCUMain_M));

  {
    int32_T i;

    /* Start for DataStoreMemory generated from: '<S14>/BlockDet_Mdl' */
    BlockDet_tBlockedTime_P = 50U;

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    CfgParam_HWSupId[0] = 0U;

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    CfgParam_HWVersion[0] = 65U;

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    CfgParam_HWSupId[1] = 136U;

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    CfgParam_HWVersion[1] = 0U;

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    for (i = 0; i < 10; i++) {
      CfgParam_MBCECUIDHWPartNum[i] =
        rtCP_CfgParam_MBCECUIDHWPartNum_InitialValue[i];
    }

    /* End of Start for DataStoreMemory generated from: '<S3>/Model' */

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    for (i = 0; i < 9; i++) {
      CfgParam_RoofPartNum[i] = rtCP_CfgParam_RoofPartNum_InitialValue[i];
    }

    /* End of Start for DataStoreMemory generated from: '<S3>/Model' */

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    for (i = 0; i < 6; i++) {
      CfgParam_RoofSerialNum[i] = MAX_uint8_T;
    }

    /* End of Start for DataStoreMemory generated from: '<S3>/Model' */

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    for (i = 0; i < 9; i++) {
      CfgParam_SCUPartNum[i] = rtCP_CfgParam_SCUPartNum_InitialValue[i];
    }

    /* End of Start for DataStoreMemory generated from: '<S3>/Model' */

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    CfgParam_SWSupId[0] = 0U;
    CfgParam_SWSupId[1] = 136U;

    /* Start for DataStoreMemory generated from: '<S16>/VoltMon_Mdl' */
    DTC_isDTCEnbl = true;

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    ISOUDS_Sess = 1U;

    /* Start for DataStoreMemory generated from: '<S5>/PIDDID_WriteDID_func' */
    PIDDID_WriteID = PIDDID_WriteID_t_NO_ID;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_hDiagToPos = MAX_uint16_T;

    /* Start for DataStoreMemory generated from: '<S3>/Model' */
    PIDDID_isATSEnabled = true;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stClearRtnResult = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stControlIntermediatePosRoutineResult =
      PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stDummyRPRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stNVMSyncRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stOpenClosePosRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stOverridePWMRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_AutoCyc = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_ClearRPRF = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_GotoTgtPos = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_IOTestRtn = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_LearnRtn = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_MtrCtrlrtn = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_NormMtrRtn = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_RoofCtrlrtn = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stRoutineResult_SuppressCyclicRenorm =
      PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stSoftStartRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S4>/Model' */
    PIDDID_stSoftStopRoutineResult = PIDDID_RoutineStatus_t_Not_supported_C;

    /* Start for DataStoreMemory generated from: '<S13>/PIDDID_Mdl' */
    PIDDID_stSyncToNvm = SCU_STATUS_ERROR;

    /* Start for DataStoreMemory generated from: '<S16>/VoltMon_Mdl' */
    VoltMon_u12VBatt = 9000U;

    /* SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_IoCtrl' incorporates:
     *  SubSystem: '<Root>/PIDDID_IoCtrl'
     */
    /* SystemInitialize for ModelReference generated from: '<S2>/Model' */
    PIDDID_IoCtrl_Func_Init(&SCUMain_B.Model_o2_n[0]);

    /* End of SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_IoCtrl' */

    /* SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_ReadDID' incorporates:
     *  SubSystem: '<Root>/PIDDID_ReadDID'
     */
    /* SystemInitialize for ModelReference generated from: '<S3>/Model' */
    PIDDID_ReadDID_Func_Init(&SCUMain_B.srcBuf[0]);

    /* End of SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_ReadDID' */

    /* SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_RoutineCtrl' incorporates:
     *  SubSystem: '<Root>/PIDDID_RoutineCtrl'
     */
    /* SystemInitialize for ModelReference generated from: '<S4>/Model' */
    PIDDID_RoutineCtrl_Func_Init(&SCUMain_B.Model_o2[0]);

    /* End of SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_RoutineCtrl' */

    /* SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_WriteDID' incorporates:
     *  SubSystem: '<Root>/PIDDID_WriteDID'
     */
    /* SystemInitialize for ModelReference generated from: '<S5>/PIDDID_WriteDID_func' */
    PIDDID_WriteDID_Func_Init();

    /* End of SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/PIDDID_WriteDID' */

    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/SCUMain_vTrigger' incorporates:
     *  SubSystem: '<Root>/SCU Software'
     */
    /* SystemInitialize for Atomic SubSystem: '<S10>/TaskSchedular_Mdl' */
    SCUMain_TaskSchedular_Mdl_Init();

    /* End of SystemInitialize for SubSystem: '<S10>/TaskSchedular_Mdl' */
    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/SCUMain_vTrigger' */

    /* SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/Simulink Function' incorporates:
     *  SubSystem: '<Root>/Simulink Function'
     */
    /* SystemInitialize for ModelReference: '<S7>/Model' */
    HALLDeco_Mdl_ISR_Init();

    /* End of SystemInitialize for S-Function (sfun_private_function_caller) generated from: '<Root>/Simulink Function' */
  }
}

/* Model terminate function */
void SCUMain_terminate(void)
{
  /* (no terminate code required) */
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
