/*
 * File: UsgHist_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "UsgHist_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
boolean_T UsgHist_AmbTempVld;
UsgHist_Temp_t UsgHist_CaseTemp;
/* Stored Motor Casing Temperature and Temperature Validity (Glass and Rollo) */
UsgHist_Cycle_Counters_bus UsgHist_Cycle_Counter_stallCnt;
UsgHist_Extend_Event_bus UsgHist_Extend_Event;
UsgHist_History_loggers_bus UsgHist_History_loggers;
boolean_T UsgHist_MOSFETTempVld;
UsgHist_MinMax_historylog_bus UsgHist_MinMax_historylog;
UsgHist_Temp_t UsgHist_MtrTemp;
     /* Stored Motor Coil Temperature and Temperature Validity (Glass and Rollo)
      */
UsgHist_Reversal_Event_bus UsgHist_Reversal_Event;/* UsgHist reversal bus */
uint16_T UsgHist_nCycle;               /* Cycle counts Rollo
                                        */
uint8_T UsgHist_resetReason[14];       /* UsgHist Stored Reset Reason */
uint8_T UsgHist_stHallState;           /* Hall states

                                          Bit0(1/0): Glass Hall1 High or Low
                                          Bit1(1/0): Glass Hall2 High or Low
                                          Bit2(1/0): Rollo Hall1 High or Low
                                          Bit3(1/0): Rollo Hall1 High or Low
                                          Bit4~Bit7(1/0): Reserved

                                          Byte2 = error counter */
uint16_T UsgHist_unlearnReason;
                              /* UsgHist Stored Unlearn Reason (Glass and Rollo)
                                 Bit0(1/0): Unlearn due to panel position sensing ability lost (hall failure)
                                 Bit1(1/0): Unlearn due to repeated blocks in the same position at a panel
                                 Bit2(1/0): Unlearn due to reference field learning procedure started
                                 Bit3(1/0): Unlearn due to unlearned by diagnostic command
                                 Bit4(1/0): Unlearn due to CAL-file is invalid
                                 Bit5(1/0): Unlearn due to Position is invalid
                                 Bit6(1/0): Unlearn due to Reference Field is invalid
                                 Bit7(1/0): Unlearn due to ECU reset via diagnostics during movement
                                 Bit8(1/0): Unlearn due to double stall
                                 Bit9(1/0): Unlearn due to position out of range
                                 Bit10-15(1/0): Reserved */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
