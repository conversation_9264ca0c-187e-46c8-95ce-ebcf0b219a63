/*
 * File: PIDDID_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "PIDDID_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T PIDDID_MtrDutyCycle;
uint16_T PIDDID_SoftStartPoint2Time;
int16_T PIDDID_TAmbTempOffset;
int16_T PIDDID_TMOSFETTempOffset;
PIDDID_WrResp_t PIDDID_WrResp;         /* Write Response */
PIDDID_WriteID_t PIDDID_WriteID;       /* Write ID */
uint16_T PIDDID_hDiagToPos;     /* Diagnostic Goto Position (Glass and Rollo) */
boolean_T PIDDID_isATSEnabled;         /* Diagnostic ATS Enable and Disable */
boolean_T PIDDID_isClearHisRtnActive; /* Diagnostic clear History Routine active
                                         0: clear History Routine is active
                                         1: clear History Routine is inactive */
boolean_T PIDDID_isGoToTrgtActv;
        /* To indicate whether go to target position routine is active or not */
boolean_T PIDDID_isMtrCtrlRtnBusy;
boolean_T PIDDID_isOtherRtnBusy;       /* Diagnostic other Routine Busy status
                                          0: Not busy
                                          1: otherroutine in process
                                        */
boolean_T PIDDID_isOverridePWMRtnBusy;
boolean_T PIDDID_isReqUnlearn;
        /* Diagnostic request entering unlearned state command (Glass and Rollo)
           0: No request
           1: Request entering unlearned state */
boolean_T PIDDID_isRoofCtrlRtnBusy;
                                  /* Diagnostic Roof Control Routine Busy status
                                     0: Not busy
                                     1: Roof Control  routine in process
                                   */
boolean_T PIDDID_isRtnBusy;           /* Diagnostic Learning Routine Busy status
                                         0: Not busy
                                         1: Learning routine in process
                                       */
boolean_T PIDDID_isRtnToPosBusy; /* Diagnostic Goto Position Routine Busy status
                                    0: Not busy
                                    1: Goto Position routine in process
                                  */
boolean_T PIDDID_isSuppressCyclicRenormRtnBusy;
boolean_T PIDDID_isSyncToNVMEnable;
PIDDID_RoutineStatus_t PIDDID_stClearRtnResult;
PIDDID_RoutineStatus_t PIDDID_stControlIntermediatePosRoutineResult;
uint8_T PIDDID_stDiagHallPwrCmd;
                           /* Diagnostic Hall power control command (Rollo only)
                              Bit0(1/0): Hall power diagnostic command active or not
                              Bit1(1/0): Hall power ON or OFF
                              Bit2~Bit7(1/0): Reserved */
uint8_T PIDDID_stDiagRlyCmd;
                     /* Diagnostic motor relay control command (Glass and Rollo)
                        Bit0(1/0): Relay diagnostic command active or not
                        Bit1(1/0): Relay 1 ON or OFF
                        Bit2(1/0): Relay 2 ON or OFF
                        Bit3~Bit7: Reserved */
VehCom_Cmd_t PIDDID_stDiagRtnCmd; /* Diagnostic Switch Command (Glass and Rollo)
                                     None_C       ---->0: No Command
                                     ExpOpn_C   ---->1: Express Open Command
                                     ExpCls_C     ---->2: Express Close Command
                                     ManOpn_C  ---->3: Manual Open Command
                                     ManCls_C    ---->4: Manual Close Command
                                     ExpVent_C   ---->5: Express Vent Command
                                     ManVent_C  ---->6: Manual Vent Command
                                     Stop_C        ---->7: Stop Command
                                     ToPos_C      ---->8: To Position Command
                                     Learn_C      ---->9: Learning Command */
PIDDID_RoutineStatus_t PIDDID_stDummyRPRoutineResult;
uint8_T PIDDID_stIOTestCmd;
PIDDID_RoutineStatus_t PIDDID_stNVMSyncRoutineResult;
PIDDID_RoutineStatus_t PIDDID_stOpenClosePosRoutineResult;
PIDDID_RoutineStatus_t PIDDID_stOverridePWMRoutineResult;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_AutoCyc;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_ClearRPRF;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_GotoTgtPos;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_IOTestRtn;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_LearnRtn;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_MtrCtrlrtn;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_NormMtrRtn;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_RoofCtrlrtn;
PIDDID_RoutineStatus_t PIDDID_stRoutineResult_SuppressCyclicRenorm;
PIDDID_RoutineStatus_t PIDDID_stSoftStartRoutineResult;
PIDDID_RoutineStatus_t PIDDID_stSoftStopRoutineResult;
HwAbs_tenStatus PIDDID_stSyncToNvm;
int16_T PIDDID_u12VBattInstOffset;

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
