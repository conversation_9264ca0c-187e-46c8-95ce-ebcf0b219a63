/*
 * File: SCUMain_private.h
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_SCUMain_private_h_
#define RTW_HEADER_SCUMain_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Includes for objects with custom storage classes */
#include "ExtDev.h"
#include "lin.h"
#include "lin.h."
#include "LINMiddleware.h"
#include "nvmman.h"
#include "flash_driver.h"
#include "ICMon.h"
#include "PWMCtrl_Man.h"
#include "SCUInit.h"
#include "SysFltRctn_Man.h"
#include "XCPLIN_Man.h"
#include "pin_mux.h"
#include "ISOUDS.h"
#include "CfgParam_Mdl_Version.h"
#include "irs_std_types.h"

/*
 * Check that imported macros with storage class "ImportedDefine" are defined
 */
#ifndef CfgParam_SW_Build_Num_C
#error The value of parameter "CfgParam_SW_Build_Num_C" is not defined
#endif

#ifndef HALL_OUT_PIN
#error The value of parameter "HALL_OUT_PIN" is not defined
#endif

#ifndef MTR1_FLT_BANK_A_PIN
#error The value of parameter "MTR1_FLT_BANK_A_PIN" is not defined
#endif

#ifndef MTR1_FLT_BANK_B_PIN
#error The value of parameter "MTR1_FLT_BANK_B_PIN" is not defined
#endif

#ifndef VBAT_ADC_EN_PIN
#error The value of parameter "VBAT_ADC_EN_PIN" is not defined
#endif

#ifndef INVALID
#error The value of parameter "INVALID" is not defined
#endif

#ifndef VALID
#error The value of parameter "VALID" is not defined
#endif

#ifndef CfgParam_BootLoadMajVer_C
#error The value of parameter "CfgParam_BootLoadMajVer_C" is not defined
#endif

#ifndef CfgParam_BootLoadMinVer_C
#error The value of parameter "CfgParam_BootLoadMinVer_C" is not defined
#endif

#ifndef CfgParam_Cal_Ver_Major_C
#error The value of parameter "CfgParam_Cal_Ver_Major_C" is not defined
#endif

#ifndef CfgParam_SW_MajVer_C
#error The value of parameter "CfgParam_SW_MajVer_C" is not defined
#endif

#ifndef CfgParam_SW_MinVer_C
#error The value of parameter "CfgParam_SW_MinVer_C" is not defined
#endif

#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

extern const uint8_T rtCP_pooled_rpnYm2uaWZ1g;
extern const uint8_T rtCP_pooled_erv4FZRhLXPs[10];
extern const uint8_T rtCP_pooled_D2NvPMBA0cGq[9];

#define rtCP_Model_ChannelIdSensor1    rtCP_pooled_rpnYm2uaWZ1g  /* Computed Parameter: rtCP_Model_ChannelIdSensor1
                                                                  * Referenced by: '<S7>/Model'
                                                                  */
#define rtCP_CfgParam_MBCECUIDHWPartNum_InitialValue rtCP_pooled_erv4FZRhLXPs/* Expression: []
                                                                      * Referenced by:
                                                                      */
#define rtCP_CfgParam_RoofPartNum_InitialValue rtCP_pooled_D2NvPMBA0cGq/* Expression: []
                                                                      * Referenced by:
                                                                      */
#define rtCP_CfgParam_SCUPartNum_InitialValue rtCP_pooled_D2NvPMBA0cGq/* Expression: []
                                                                      * Referenced by:
                                                                      */
#endif                                 /* RTW_HEADER_SCUMain_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
