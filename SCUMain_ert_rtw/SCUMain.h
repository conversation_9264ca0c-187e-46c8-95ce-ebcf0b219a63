/*
 * File: SCUMain.h
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_SCUMain_h_
#define RTW_HEADER_SCUMain_h_
#ifndef SCUMain_COMMON_INCLUDES_
#define SCUMain_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* SCUMain_COMMON_INCLUDES_ */

#include "SCUMain_types.h"
#include "CtrlLogic_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "LearnAdp_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "UsgHist_ExpTypes.h"
#include "MtrMdl_ExpTypes.h"
#include "PIDDID_ExpTypes.h"
#include "irs_std_types.h"
#include "rt_defines.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "DTC_Exp.h"
#include "RoofSys.h"
#include "AmbTempMon_Exp.h"
#include "AntiPinch_Exp.h"
#include "BlockDet_Mdl_Exp.h"
#include "CfgParam_Mdl_Exp.h"
#include "IoSigIf_Exp.h"
#include "LearnAdap_Exp.h"
#include "MtrMdl_Exp.h"
#include "PIDDID_Exp.h"
#include "PosMon_Exp.h"
#include "RefField_Exp.h"
#include "ThermProt_Exp.h"
#include "UsgHist_Exp.h"
#include "VoltMon_Exp.h"
#include "SCUMain_hallInterrupt.h"
#include "PIDDID_WriteDID.h"
#include "PIDDID_RoutineCtrl.h"
#include "PIDDID_ReadDID.h"
#include "PIDDID_IoCtrl.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
#define rtmGetErrorStatus(rtm)         ((rtm)->errorStatus)
#endif

#ifndef rtmSetErrorStatus
#define rtmSetErrorStatus(rtm, val)    ((rtm)->errorStatus = (val))
#endif

#ifndef rtmGetErrorStatusPointer
#define rtmGetErrorStatusPointer(rtm)  ((const char_T **)(&((rtm)->errorStatus)))
#endif

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals (default storage) */
typedef struct {
  SWC_ObjDetLyrBus
    BusConversion_InsertedFor_CtrlLog_Mdl_at_inport_113_BusCreator1;
  MtrCtrlBus MtrCtrlBus_e;             /* '<S15>/MtrCtrl_Mdl' */
  CtrlLogBus CtrlLogBus_m;             /* '<S15>/CtrlLog_Mdl' */
  ThermProtBus ThermProtBus_b;         /* '<S15>/ThermProt_Mdl' */
  ComLogBus ComLogBus_j;               /* '<S15>/ComLog_Mdl' */
  uint32_T UsgHist_MotorOperTime;      /* '<S12>/UsgHis_Mdl' */
  uint32_T UsgHist_MotorOperTime_c;    /* '<S13>/PIDDID_Mdl' */
  stdReturn_t RefField_stRFSave;       /* '<S12>/RefField_Mdl' */
  CtrlLog_tenTargetDirection UsgHist_ReversalDir;/* '<S12>/UsgHis_Mdl' */
  CtrlLog_tenTargetDirection CtrlLog_stDirCommand;/* '<S13>/PIDDID_Mdl' */
  CtrlLog_tenTargetDirection UsgHist_ReversalDir_j;/* '<S13>/PIDDID_Mdl' */
  APDet_tenTargetDirection APDet_ATSRevDir;/* '<S14>/APDet_Mdl' */
  uint16_T inputCaptureValue;          /* '<S7>/u1' */
  uint16_T PulseTime;                  /* '<S7>/Model' */
  uint16_T VoltMon_u12VBatt_b;         /* '<S16>/VoltMon_Mdl' */
  uint16_T UsgHist_nCycle_e;           /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_unlearnReason_b;    /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_MaxBattVtg;         /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_MinBattVtg;         /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_CycRenormCount;     /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_ReversalCount;      /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_SlideCycCount;      /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_ReversalPos;        /* '<S12>/UsgHis_Mdl' */
  uint16_T UsgHist_ReversalThres;      /* '<S12>/UsgHis_Mdl' */
  uint16_T ThresForce_FatsThreshold;   /* '<S14>/TheshForce_Mdl' */
  uint16_T SysFltRctn_stsSysFaultReaction;/* '<S13>/SysFaultReac_Mdl' */
  uint16_T SysFltRctn_stsUnlearnReason;/* '<S13>/SysFaultReac_Mdl' */
  uint16_T SysFltRctn_stsLearnFailReason;/* '<S13>/SysFaultReac_Mdl' */
  uint16_T PnlOp_hTrgtPosPtcd;         /* '<S15>/PnlOp_Mdl' */
  uint16_T PosMon_hCurPos;             /* '<S16>/PosMon_Mdl' */
  uint16_T hHardStopCls;               /* '<S16>/PosMon_Mdl' */
  uint16_T hSoftStopCls;               /* '<S16>/PosMon_Mdl' */
  uint16_T hNearClsSld;                /* '<S16>/PosMon_Mdl' */
  uint16_T hComfortMidStop;            /* '<S16>/PosMon_Mdl' */
  uint16_T hSoftStopOpn;               /* '<S16>/PosMon_Mdl' */
  uint16_T hHardStopOpn;               /* '<S16>/PosMon_Mdl' */
  uint16_T BlockDet_hStallPos_f;       /* '<S13>/PIDDID_Mdl' */
  uint16_T PosMon_hCurPos_h;           /* '<S13>/PIDDID_Mdl' */
  uint16_T VoltMon_u12VBatt_a;         /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_nCycle_a;           /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_MaxBattVtg_e;       /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_MinBattVtg_c;       /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_CycRenormCount_a;   /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_ReversalCount_p;    /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_SlideCycCount_o;    /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_ReversalPos_o;      /* '<S13>/PIDDID_Mdl' */
  uint16_T UsgHist_ReversalThres_d;    /* '<S13>/PIDDID_Mdl' */
  uint16_T HallDeco_rMtrSpd;           /* '<S13>/PIDDID_Mdl' */
  uint16_T ADCMon_u12VBattInst;        /* '<S13>/PIDDID_Mdl' */
  uint16_T ADCMon_uHallPwrInst;        /* '<S13>/PIDDID_Mdl' */
  uint16_T ADCMon_uM1AVolt_mv;         /* '<S13>/PIDDID_Mdl' */
  uint16_T ADCMon_uM1BVolt_mv;         /* '<S13>/PIDDID_Mdl' */
  uint16_T LearnAdap_newHardStop;      /* '<S14>/LearnAdap_Mdl' */
  uint16_T LearnAdap_newSoftStop_l;    /* '<S14>/LearnAdap_Mdl' */
  uint16_T HallDeco_rMtrSpd_c;         /* '<S9>/HALLDeco_Mdl' */
  uint16_T BlockDet_hStallPos_h;       /* '<S14>/BlockDet_Mdl' */
  uint16_T APDet_ATSRevThreshold;      /* '<S14>/APDet_Mdl' */
  uint16_T APDet_ATSRevPosition;       /* '<S14>/APDet_Mdl' */
  uint16_T ADCMon_u12VBattInst_c;      /* '<S9>/ADCMon_Mdl' */
  uint16_T ADCMon_uSwchInst[2];        /* '<S9>/ADCMon_Mdl' */
  uint16_T ADCMon_uHallPwrInst_h;      /* '<S9>/ADCMon_Mdl' */
  uint16_T ADCMon_uM1AVolt_mv_d;       /* '<S9>/ADCMon_Mdl' */
  uint16_T ADCMon_uM1BVolt_mv_n;       /* '<S9>/ADCMon_Mdl' */
  uint16_T RefField_currByteOfRF;      /* '<S12>/RefField_Mdl' */
  uint16_T PosMon_relativePinionSize;  /* '<S16>/PosMon_Mdl' */
  int16_T PnlOp_pMtrSpdCmd;            /* '<S15>/PnlOp_Mdl' */
  int16_T PWMCtrl_SetMtrDutyCycle;     /* '<S9>/PWMCtrl_Mdl' */
  int16_T UsgHist_TEstMtrTemp;         /* '<S12>/UsgHis_Mdl' */
  int16_T UsgHist_TEstCaseTemp;        /* '<S12>/UsgHis_Mdl' */
  int16_T UsgHist_MaxAmbTemp;          /* '<S12>/UsgHis_Mdl' */
  int16_T UsgHist_MinAmbTemp;          /* '<S12>/UsgHis_Mdl' */
  int16_T RefForce_FreferenceForce;    /* '<S14>/RefForce_Mdl' */
  int16_T PosMon_hCurPosSigned;        /* '<S16>/PosMon_Mdl' */
  int16_T PosMon_stPosSave;            /* '<S16>/PosMon_Mdl' */
  int16_T MtrMdl_TEstMtrTemp_g;        /* '<S13>/PIDDID_Mdl' */
  int16_T AmbTempMon_TAmbTemp_a;       /* '<S13>/PIDDID_Mdl' */
  int16_T MOSFETTempMon_TMosTemp;      /* '<S13>/PIDDID_Mdl' */
  int16_T UsgHist_MaxAmbTemp_g;        /* '<S13>/PIDDID_Mdl' */
  int16_T UsgHist_MinAmbTemp_d;        /* '<S13>/PIDDID_Mdl' */
  int16_T ADCMon_TAmbTemp;             /* '<S13>/PIDDID_Mdl' */
  int16_T ADCMon_TMOSFETTemp;          /* '<S13>/PIDDID_Mdl' */
  int16_T MOSFETTempMon_TMosTemp_k;    /* '<S16>/MOSFETTempMon_Mdl' */
  int16_T AmbTempMon_TAmbTemp_n;       /* '<S16>/AmbTempMon_Mdl' */
  int16_T FcalcForce;                  /* '<S14>/MtrMdl_Mdl' */
  int16_T McalcTorque;                 /* '<S14>/MtrMdl_Mdl' */
  int16_T MtrMdl_TEstMtrTemp_gx;       /* '<S14>/MtrMdl_Mdl' */
  int16_T MtrMdl_TEstCaseTemp_e;       /* '<S14>/MtrMdl_Mdl' */
  int16_T MtrMdl_MbrakeTorque;         /* '<S14>/MtrMdl_Mdl' */
  int16_T APDet_FDifference;           /* '<S14>/APDet_Mdl' */
  int16_T APDet_FTracking;             /* '<S14>/APDet_Mdl' */
  int16_T ADCMon_TAmbTemp_a;           /* '<S9>/ADCMon_Mdl' */
  int16_T ADCMon_TMOSFETTemp_l;        /* '<S9>/ADCMon_Mdl' */
  uint8_T HallCounts;                  /* '<S7>/Model' */
  uint8_T PIDDID_WriteDID_func;        /* '<S5>/PIDDID_WriteDID_func' */
  uint8_T Model_o1;                    /* '<S4>/Model' */
  uint8_T Model_o2[10];                /* '<S4>/Model' */
  uint8_T Model_o1_f;                  /* '<S3>/Model' */
  uint8_T srcBuf[252];                 /* '<S3>/Model' */
  uint8_T Model_o1_d;                  /* '<S2>/Model' */
  uint8_T Model_o2_n[22];              /* '<S2>/Model' */
  uint8_T DTC_VOLT_VBatt;              /* '<S16>/VoltMon_Mdl' */
  uint8_T DTC_VOLT_Pullup;             /* '<S16>/VoltMon_Mdl' */
  uint8_T VehCom_hToPos;               /* '<S11>/VehCom_Mdl' */
  uint8_T VehCom_vVehSpd;              /* '<S11>/VehCom_Mdl' */
  uint8_T VehCom_percRelPosn;          /* '<S11>/VehCom_Mdl' */
  uint8_T UsgHist_LearnSuccessCount;   /* '<S12>/UsgHis_Mdl' */
  uint8_T UsgHist_LeanrFailCount;      /* '<S12>/UsgHis_Mdl' */
  uint8_T UsgHist_StallCount;          /* '<S12>/UsgHis_Mdl' */
  uint8_T UsgHist_ThermProtCount;      /* '<S12>/UsgHis_Mdl' */
  uint8_T UsgHist_ReprogrammingCount;  /* '<S12>/UsgHis_Mdl' */
  uint8_T SysFltRctn_DTCStatus[3];     /* '<S13>/SysFaultReac_Mdl' */
  uint8_T SysFltRctn_stLast10DTCs_a[10];/* '<S13>/SysFaultReac_Mdl' */
  uint8_T RefField_currRFArea;         /* '<S12>/RefField_Mdl' */
  uint8_T UsgHist_LearnSuccessCount_c; /* '<S13>/PIDDID_Mdl' */
  uint8_T UsgHist_LeanrFailCount_d;    /* '<S13>/PIDDID_Mdl' */
  uint8_T UsgHist_StallCount_i;        /* '<S13>/PIDDID_Mdl' */
  uint8_T UsgHist_ThermProtCount_k;    /* '<S13>/PIDDID_Mdl' */
  uint8_T UsgHist_ReprogrammingCount_l;/* '<S13>/PIDDID_Mdl' */
  uint8_T DTC_HALL_Hall1;              /* '<S13>/PIDDID_Mdl' */
  uint8_T ExtDev_u8GetPWMDutyCycleVal; /* '<S13>/PIDDID_Mdl' */
  uint8_T MOSFETTempMon_stMosTempSensFlt;/* '<S16>/MOSFETTempMon_Mdl' */
  uint8_T LearnAdap_stInt;             /* '<S14>/LearnAdap_Mdl' */
  uint8_T HallDeco_uiHallCountsCyclic; /* '<S9>/HALLDeco_Mdl' */
  uint8_T DTC_HALL_Hall1_i;            /* '<S9>/HALLDeco_Mdl' */
  uint8_T DTC_HallMtrOptDir;           /* '<S9>/HALLDeco_Mdl' */
  uint8_T CfgParam_stCALFileFlt;       /* '<S12>/CfgParam_Mdl' */
  uint8_T BlockDet_stStallFlt;         /* '<S14>/BlockDet_Mdl' */
  uint8_T AmbTempMon_stAmbTempSensFlt; /* '<S16>/AmbTempMon_Mdl' */
  uint8_T UsgHist_ATSRevReason[18];    /* '<S14>/APDet_Mdl' */
  uint8_T ExtDev_u8GetPWMDutyCycleVal_a;/* '<S9>/ExtDev_Mdl' */
  boolean_T Direction;                 /* '<S7>/Model' */
  boolean_T VoltMon_is12VBattVld_f;    /* '<S16>/VoltMon_Mdl' */
  boolean_T VoltMon_isVoltDrop;        /* '<S16>/VoltMon_Mdl' */
  boolean_T VoltMon_isFlctnDet;        /* '<S16>/VoltMon_Mdl' */
  boolean_T VoltMon_isStartUpRdy;      /* '<S16>/VoltMon_Mdl' */
  boolean_T VoltMon_isPwrLost;         /* '<S16>/VoltMon_Mdl' */
  boolean_T VoltMon_isUnderVtgFlgSet;  /* '<S16>/VoltMon_Mdl' */
  boolean_T VoltMon_isOverVtgFlgSet;   /* '<S16>/VoltMon_Mdl' */
  boolean_T VehCom_isVehSpdVld;        /* '<S11>/VehCom_Mdl' */
  boolean_T VehCom_bSleepReady;        /* '<S11>/VehCom_Mdl' */
  boolean_T UsgHist_isShdnRdy;         /* '<S12>/UsgHis_Mdl' */
  boolean_T UsgHist_TEstMtrTempVld;    /* '<S12>/UsgHis_Mdl' */
  boolean_T UsgHistl_TEstCaseTempVld;  /* '<S12>/UsgHis_Mdl' */
  boolean_T UsgHist_AmbTempVld_g;      /* '<S12>/UsgHis_Mdl' */
  boolean_T UsgHist_MOSFETTempVld_g;   /* '<S12>/UsgHis_Mdl' */
  boolean_T UsgHist_isStartupRdy;      /* '<S12>/UsgHis_Mdl' */
  boolean_T PnlOp_isMtrMoving;         /* '<S15>/PnlOp_Mdl' */
  boolean_T RefForce_isCalcRefForceVld;/* '<S14>/RefForce_Mdl' */
  boolean_T RefForce_isPosOutsideRefField;/* '<S14>/RefForce_Mdl' */
  boolean_T RefField_isStartupRdy;     /* '<S12>/RefField_Mdl' */
  boolean_T RefField_isShdnRdy;        /* '<S12>/RefField_Mdl' */
  boolean_T RefField_isRFTblVldNvm;    /* '<S12>/RefField_Mdl' */
  boolean_T RefField_isRFTblVld;       /* '<S12>/RefField_Mdl' */
  boolean_T RefField_isRFTblVldNvmBak; /* '<S12>/RefField_Mdl' */
  boolean_T RefField_isRFTblVldNvmSync;/* '<S12>/RefField_Mdl' */
  boolean_T RefField_isInsideRF;       /* '<S12>/RefField_Mdl' */
  boolean_T PosMon_isCurPosVld;        /* '<S16>/PosMon_Mdl' */
  boolean_T PosMon_isCurPosVldNvm_e;   /* '<S16>/PosMon_Mdl' */
  boolean_T PosMon_isRelearn_m;        /* '<S16>/PosMon_Mdl' */
  boolean_T PosMon_isShdnRdy;          /* '<S16>/PosMon_Mdl' */
  boolean_T PosMon_isOutOfRange;       /* '<S16>/PosMon_Mdl' */
  boolean_T PosMon_isStartupRdy;       /* '<S16>/PosMon_Mdl' */
  boolean_T LearnAdap_isLearnComplete_a;/* '<S13>/PIDDID_Mdl' */
  boolean_T LearnAdap_isLearningAllowed_n;/* '<S13>/PIDDID_Mdl' */
  boolean_T AmbTempMon_isAmbTempVld;   /* '<S13>/PIDDID_Mdl' */
  boolean_T PosMon_isCurPosVld_m;      /* '<S13>/PIDDID_Mdl' */
  boolean_T VoltMon_isFlctnDet_o;      /* '<S13>/PIDDID_Mdl' */
  boolean_T ThermProt_isStartupRdy;    /* '<S13>/PIDDID_Mdl' */
  boolean_T PnlOp_isMtrMoving_b;       /* '<S13>/PIDDID_Mdl' */
  boolean_T MtrCtrl_FeedbackStatus[2]; /* '<S13>/PIDDID_Mdl' */
  boolean_T isHallSupplyFault;         /* '<S13>/PIDDID_Mdl' */
  boolean_T DIOCtrl_isVBATMeasEnbPinStatus;/* '<S13>/PIDDID_Mdl' */
  boolean_T DIOCtrl_isHallOutPinStatus;/* '<S13>/PIDDID_Mdl' */
  boolean_T DIOCtrl_isMtrFltrBlankAPinStatus;/* '<S13>/PIDDID_Mdl' */
  boolean_T DIOCtrl_isMtrFltrBlankBPinStatus;/* '<S13>/PIDDID_Mdl' */
  boolean_T DIOCtrl_isHallSens1PinStatus;/* '<S13>/PIDDID_Mdl' */
  boolean_T DIOCtrl_isHallSens2PinStatus;/* '<S13>/PIDDID_Mdl' */
  boolean_T DIOCtrl_isINTPinStatus;    /* '<S13>/PIDDID_Mdl' */
  boolean_T ExtDev_isMtrFebkCtrlHS1Status;/* '<S13>/PIDDID_Mdl' */
  boolean_T ExtDev_isHallSuppHS2Status;/* '<S13>/PIDDID_Mdl' */
  boolean_T ExtDev_isSwtchSuppHS3Status;/* '<S13>/PIDDID_Mdl' */
  boolean_T MOSFETTempMon_isMosTempVld;/* '<S16>/MOSFETTempMon_Mdl' */
  boolean_T LearnAdap_isLearnComplete_i;/* '<S14>/LearnAdap_Mdl' */
  boolean_T LearnAdap_isLearningAllowed_e;/* '<S14>/LearnAdap_Mdl' */
  boolean_T LearnAdap_isInterrupted;   /* '<S14>/LearnAdap_Mdl' */
  boolean_T HallDeco_isMtrSpdVld;      /* '<S9>/HALLDeco_Mdl' */
  boolean_T isHallSupplyFaultDebd;     /* '<S9>/HALLDeco_Mdl' */
  boolean_T isHallSupplyFault_h;       /* '<S9>/HALLDeco_Mdl' */
  boolean_T Dummy;                     /* '<S9>/DiagComMan' */
  boolean_T CfgParam_isCALFileVld;     /* '<S12>/CfgParam_Mdl' */
  boolean_T CfgParam_isStartupRdy;     /* '<S12>/CfgParam_Mdl' */
  boolean_T CfgParam_isPosTblVld;      /* '<S12>/CfgParam_Mdl' */
  boolean_T AmbTempMon_isAmbTempVld_h; /* '<S16>/AmbTempMon_Mdl' */
  boolean_T MtrMdl_isMotorInStartUp;   /* '<S14>/MtrMdl_Mdl' */
  boolean_T MtrMdl_isStartupRdy;       /* '<S14>/MtrMdl_Mdl' */
  boolean_T MtrMdl_isCalcForTorqVld;   /* '<S14>/MtrMdl_Mdl' */
  boolean_T MtrMdl_isEstMtrTempVld_o;  /* '<S14>/MtrMdl_Mdl' */
  boolean_T APDet_isAtsDetected;       /* '<S14>/APDet_Mdl' */
  boolean_T APDet_isInputsAtsValid;    /* '<S14>/APDet_Mdl' */
  boolean_T APDet_isAtsEnabled;        /* '<S14>/APDet_Mdl' */
  boolean_T DIOCtrl_PINReadStatus;     /* '<S9>/DIOCtrl_Mdl' */
  boolean_T DIOCtrl_isVBATMeasEnbPinStatus_d;/* '<S9>/DIOCtrl_Mdl' */
  boolean_T DIOCtrl_isHallOutPinStatus_m;/* '<S9>/DIOCtrl_Mdl' */
  boolean_T DIOCtrl_isMtrFltrBlankAPinStatus_c;/* '<S9>/DIOCtrl_Mdl' */
  boolean_T DIOCtrl_isMtrFltrBlankBPinStatus_c;/* '<S9>/DIOCtrl_Mdl' */
  boolean_T DIOCtrl_isHallSens1PinStatus_p;/* '<S9>/DIOCtrl_Mdl' */
  boolean_T DIOCtrl_isHallSens2PinStatus_c;/* '<S9>/DIOCtrl_Mdl' */
  boolean_T DIOCtrl_isINTPinStatus_c;  /* '<S9>/DIOCtrl_Mdl' */
  boolean_T ExtDev_isMtrFebkCtrlHS1Status_e;/* '<S9>/ExtDev_Mdl' */
  boolean_T ExtDev_isHallSuppHS2Status_l;/* '<S9>/ExtDev_Mdl' */
  boolean_T ExtDev_isSwtchSuppHS3Status_h;/* '<S9>/ExtDev_Mdl' */
  VoltMon_VoltClass_t VoltMon_stVoltClass;/* '<S16>/VoltMon_Mdl' */
  VoltMon_VoltClass_t VoltMon_stVoltClass_p;/* '<S13>/PIDDID_Mdl' */
  VehCom_Cmd_t VehCom_stVehCmd;        /* '<S11>/VehCom_Mdl' */
  ThermProt_MtrTempClass_t ThermProt_stMtrTempClass;/* '<S13>/PIDDID_Mdl' */
  ThermProt_MosfetTempClass_t ThermProt_stMosfetTempClass;/* '<S13>/PIDDID_Mdl' */
  SysFltDiag_LastStopReason_t SysFltRctn_stsLasStpReason;/* '<S13>/SysFaultReac_Mdl' */
  StateMach_Mode_t StateMach_stSysMode;/* '<S13>/PIDDID_Mdl' */
  StateMach_Mode_t StateMach_stSysMode_b;/* '<S17>/StateMach_Mdl' */
  PosMon_PnlPosArea_t PosMon_panelCurPosArea;/* '<S16>/PosMon_Mdl' */
  PosMon_Area_t PosMon_curPanelArea;   /* '<S16>/PosMon_Mdl' */
  PnlOp_Rev_t PnlOp_stReversal;        /* '<S15>/PnlOp_Mdl' */
  PnlOp_MtrCmd_t PnlOp_stMtrCtrlCmd;   /* '<S15>/PnlOp_Mdl' */
  PnlOp_Mode_t PnlOp_stNormalMov;      /* '<S15>/PnlOp_Mdl' */
  PnlOp_ATS_t PnlOp_stATS;             /* '<S15>/PnlOp_Mdl' */
  PnlOp_ATS_t PnlOp_stATS_g;           /* '<S13>/PIDDID_Mdl' */
  PWMCtrl_MtrDir_t PWMCtrl_SetMtrDir;  /* '<S9>/PWMCtrl_Mdl' */
  MtrCtrl_MtrDir_t IoHwAb_SetMtrDir;   /* '<S13>/PIDDID_Mdl' */
  LearnAdap_Req_t LearnAdap_stRefFieldReq;/* '<S14>/LearnAdap_Mdl' */
  LearnAdap_Req_t LearnAdap_stPosReq;  /* '<S14>/LearnAdap_Mdl' */
  LearnAdap_Mode_t LearnAdap_stMode;   /* '<S13>/PIDDID_Mdl' */
  LearnAdap_Mode_t LearnAdap_stMode_l; /* '<S14>/LearnAdap_Mdl' */
  HallDeco_Dir_t HallDeco_DMtrDir;     /* '<S13>/PIDDID_Mdl' */
  HallDeco_Dir_t HallDeco_DMtrDir_d;   /* '<S9>/HALLDeco_Mdl' */
  CtrlLog_RelearnMode_t CtrlLog_stRelearnMode;/* '<S13>/PIDDID_Mdl' */
  CfgParam_TestMode_t CfgParam_stTestMode;/* '<S12>/CfgParam_Mdl' */
  BlockDet_Stall_t BlockDet_stStallType;/* '<S14>/BlockDet_Mdl' */
  BlockDet_Stall_dir BlockDet_stStallDir_i;/* '<S13>/PIDDID_Mdl' */
  BlockDet_Stall_dir BlockDet_stStallDir_a;/* '<S14>/BlockDet_Mdl' */
  BlockDet_FltType_t BlockDet_stFltType;/* '<S14>/BlockDet_Mdl' */
} B_SCUMain_T;

/* Block states (default storage) for system '<Root>' */
typedef struct {
  uint16_T Output_DSTATE;              /* '<S20>/Output' */
} DW_SCUMain_T;

/* Real-time Model Data Structure */
struct tag_RTM_SCUMain_T {
  const char_T *errorStatus;
};

/* Block signals (default storage) */
extern B_SCUMain_T SCUMain_B;

/* Block states (default storage) */
extern DW_SCUMain_T SCUMain_DW;

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern CfgParam_ActiveDiagInfoBus_t CfgParam_ActiveDiagInfo;
                          /* Simulink.Signal object 'CfgParam_ActiveDiagInfo' */
extern CfgParam_DDSPackageRelBus_t CfgParam_DDSPackageRel;
                           /* Simulink.Signal object 'CfgParam_DDSPackageRel' */
extern CfgParam_HWVerInfoBus_t CfgParam_HWVerInfo;
                               /* Simulink.Signal object 'CfgParam_HWVerInfo' */
extern CfgParam_SWVerInfoBus_t CfgParam_SWVerInfo;
                               /* Simulink.Signal object 'CfgParam_SWVerInfo' */
extern PIDDID_PCB_SensorBus PIDDID_PCB_Sensor;
                                /* Simulink.Signal object 'PIDDID_PCB_Sensor' */
extern uint32_T HallDeco_sumFilterdPulseTicks;
                    /* Simulink.Signal object 'HallDeco_sumFilterdPulseTicks' */
extern stdReturn_t NVMAccessState; /* Simulink.Signal object 'NVMAccessState' */
extern int16_T LearnAdap_TLastNormTemp;
                          /* Simulink.Signal object 'LearnAdap_TLastNormTemp' */
extern int16_T PIDDID_IOCtrl_TAmbTemp;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TAmbTemp' */
extern int16_T PIDDID_IOCtrl_TMosfetTemp;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_TMosfetTemp' */
extern uint16_T BlockDet_tBlockedTime_P;
                          /* Simulink.Signal object 'BlockDet_tBlockedTime_P' */
extern uint16_T LearnAdap_renormCycleCounter;
                     /* Simulink.Signal object 'LearnAdap_renormCycleCounter' */
extern uint16_T MtrMdl_pwmVoltage;
                                /* Simulink.Signal object 'MtrMdl_pwmVoltage' */
extern uint16_T PIDDID_ControlIntermediatePos;
                    /* Simulink.Signal object 'PIDDID_ControlIntermediatePos' */
extern uint16_T PIDDID_IOCtrl_TBattVtg;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TBattVtg' */
extern uint16_T PIDDID_IOCtrl_THallSuppVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_THallSuppVtg' */
extern uint16_T PIDDID_IOCtrl_TMtrCurrA;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrCurrA' */
extern uint16_T PIDDID_IOCtrl_TMtrCurrB;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrCurrB' */
extern uint16_T PIDDID_IOCtrl_TMtrFdbkAVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkAVtg' */
extern uint16_T PIDDID_IOCtrl_TMtrFdbkBVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkBVtg' */
extern uint16_T PIDDID_IOCtrl_TSwtchIpA;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TSwtchIpA' */
extern uint16_T PIDDID_IOCtrl_TSwtchIpB;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TSwtchIpB' */
extern uint16_T PIDDID_IOcontrolMaskRecord;
                       /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecord' */
extern uint16_T PIDDID_IOcontrolMaskRecordDigiOut;
                /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordDigiOut' */
extern uint16_T PIDDID_IOcontrolMaskRecordSysICOut;
               /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordSysICOut' */
extern uint16_T PIDDID_SoftClosePos;
                              /* Simulink.Signal object 'PIDDID_SoftClosePos' */
extern uint16_T PIDDID_SoftOpenPos;
                               /* Simulink.Signal object 'PIDDID_SoftOpenPos' */
extern uint16_T PIDDID_SoftStartPoint1Time;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Time' */
extern uint16_T PIDDID_SoftStopPoint1Time;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Time' */
extern uint16_T PIDDID_uAutoCyclePauseTime[2];
                       /* Simulink.Signal object 'PIDDID_uAutoCyclePauseTime' */
extern uint16_T SysFltRctn_stLearnFail_Reason;
                    /* Simulink.Signal object 'SysFltRctn_stLearnFail_Reason' */
extern uint16_T SysFltRctn_stLostRP_Reason;
                       /* Simulink.Signal object 'SysFltRctn_stLostRP_Reason' */
extern uint8_T CfgParam_DiagTraceMemory[12];
                         /* Simulink.Signal object 'CfgParam_DiagTraceMemory' */
extern uint8_T CfgParam_HWSupId[2];
                                 /* Simulink.Signal object 'CfgParam_HWSupId' */
extern uint8_T CfgParam_HWVersion[2];
                                  /* Simulink.Signal object 'CfgParam_HWVersion'
                                   * Hardware version

                                     Hardware Major version constant in ASCII
                                     Hardware minor version constant in Decimal

                                   */
extern uint8_T CfgParam_MBCECUIDHWPartNum[10];
                       /* Simulink.Signal object 'CfgParam_MBCECUIDHWPartNum' */
extern uint8_T CfgParam_MtrSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_MtrSerialNum'
                                * Motor Serial Number in Decimal
                                */
extern uint8_T CfgParam_RoofPartNum[9];
                                /* Simulink.Signal object 'CfgParam_RoofPartNum'
                                 * Roof Part Number in ASCII
                                 */
extern uint8_T CfgParam_RoofSerialNum[6];
                              /* Simulink.Signal object 'CfgParam_RoofSerialNum'
                               * Roof serial number Version
                               */
extern uint8_T CfgParam_SCUPartNum[9];
                                 /* Simulink.Signal object 'CfgParam_SCUPartNum'
                                  * SCU Part Number in ASCII
                                  */
extern uint8_T CfgParam_SCUSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_SCUSerialNum'
                                * SCU Serial Number in Decimal
                                */
extern uint8_T CfgParam_SWSupId[2];
                                 /* Simulink.Signal object 'CfgParam_SWSupId' */
extern uint8_T CfgParam_SupportedConfigMech;
                     /* Simulink.Signal object 'CfgParam_SupportedConfigMech' */
extern uint8_T PIDDID_IOCtrl_uMtrDtyCyc;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_uMtrDtyCyc' */
extern uint8_T PIDDID_SoftStartPoint1Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Perc' */
extern uint8_T PIDDID_SoftStartPoint2Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint2Perc' */
extern uint8_T PIDDID_SoftStopPoint1Perc;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Perc' */
extern uint8_T SysFltRctn_stDTCCounter_Byte1[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte1' */
extern uint8_T SysFltRctn_stDTCCounter_Byte2[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte2' */
extern uint8_T SysFltRctn_stDTCCounter_Byte3[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte3' */
extern uint8_T SysFltRctn_stDTCStatus[3];
                           /* Simulink.Signal object 'SysFltRctn_stDTCStatus' */
extern uint8_T SysFltRctn_stLast10DTCs[10];
                          /* Simulink.Signal object 'SysFltRctn_stLast10DTCs' */
extern boolean_T PIDDID_HallOutputDisabled;
                           /* Simulink.Signal object 'PIDDID_HallOutputDisabled'
                            * Diagnostic Routine Hall output Enabled/Disabled
                              0: Enabled
                              1: Disabled

                            */
extern boolean_T PIDDID_IOCtrl_HallSenOneIpSt;
                     /* Simulink.Signal object 'PIDDID_IOCtrl_HallSenOneIpSt' */
extern boolean_T PIDDID_IOCtrl_HallSenTwoIpSt;
                     /* Simulink.Signal object 'PIDDID_IOCtrl_HallSenTwoIpSt' */
extern boolean_T PIDDID_IOCtrl_INTNInpState;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_INTNInpState' */
extern boolean_T PIDDID_IOCtrl_isHallOut;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_isHallOut' */
extern boolean_T PIDDID_IOCtrl_isHallSupplyCtrl;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isHallSupplyCtrl' */
extern boolean_T PIDDID_IOCtrl_isMtrCCWAct;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCCWAct' */
extern boolean_T PIDDID_IOCtrl_isMtrCWAct;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCWAct' */
extern boolean_T PIDDID_IOCtrl_isMtrFdbkCtrl;
                      /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFdbkCtrl' */
extern boolean_T PIDDID_IOCtrl_isMtrFltrBlankA;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankA' */
extern boolean_T PIDDID_IOCtrl_isMtrFltrBlankB;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankB' */
extern boolean_T PIDDID_IOCtrl_isSwtchSupplyCtrl;
                  /* Simulink.Signal object 'PIDDID_IOCtrl_isSwtchSupplyCtrl' */
extern boolean_T PIDDID_IOCtrl_isVBATMeasEnable;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isVBATMeasEnable' */
extern boolean_T PIDDID_isAutoCycleActive;
                         /* Simulink.Signal object 'PIDDID_isAutoCycleActive' */
extern boolean_T PIDDID_isFactoryMode;
                                /* Simulink.Signal object 'PIDDID_isFactoryMode'
                                 * Diagnostic Factory Mode
                                   1 - Enabled
                                   0 -Disabled

                                 */
extern boolean_T PIDDID_isSoftStartEna;
                            /* Simulink.Signal object 'PIDDID_isSoftStartEna' */
extern boolean_T PIDDID_isSoftStopEna;
                             /* Simulink.Signal object 'PIDDID_isSoftStopEna' */
extern boolean_T PosMon_isCurPosVldNvm;
                               /* Simulink.Signal object 'PosMon_isCurPosVldNvm'
                                * Current position validity NVM:
                                  0 invalid
                                  1: valid
                                */
extern boolean_T PosMon_isRelearn;
                                 /* Simulink.Signal object 'PosMon_isRelearn' */
extern SysFltDiag_LastStopReason_t SysFltRctn_stLastStopReason;
                      /* Simulink.Signal object 'SysFltRctn_stLastStopReason' */
extern HallDeco_Dir_t HallDeco_enDMtrDir;
                                  /* Simulink.Signal object 'HallDeco_enDMtrDir'
                                   * Get Motor condition is moving or not in C code
                                   */
extern LearnAdap_nextRenorm_t LearnAdap_stNextRenorm;
                           /* Simulink.Signal object 'LearnAdap_stNextRenorm' */

/* Model entry point functions */
extern void SCUMain_initialize(void);
extern void SCUMain_terminate(void);

/* Exported entry point function */
extern void SCUMain_vTrigger(void);

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern boolean_T PIDDID_isCyclicRenormSuppress;
extern boolean_T PIDDID_isDummyRPRtnEna;
extern boolean_T PIDDID_isIntermediatePositionRtnEna;
extern boolean_T PIDDID_isOpenClosePositionRtnEna;
extern boolean_T PIDDID_isOverridePWMEnable;

/* Real-time Model object */
extern RT_MODEL_SCUMain_T *const SCUMain_M;

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S20>/Data Type Propagation' : Unused code path elimination
 * Block '<S23>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/FixPt Data Type Duplicate1' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'SCUMain'
 * '<S1>'   : 'SCUMain/Model Info'
 * '<S2>'   : 'SCUMain/PIDDID_IoCtrl'
 * '<S3>'   : 'SCUMain/PIDDID_ReadDID'
 * '<S4>'   : 'SCUMain/PIDDID_RoutineCtrl'
 * '<S5>'   : 'SCUMain/PIDDID_WriteDID'
 * '<S6>'   : 'SCUMain/SCU Software'
 * '<S7>'   : 'SCUMain/Simulink Function'
 * '<S8>'   : 'SCUMain/SCU Software/Application Software'
 * '<S9>'   : 'SCUMain/SCU Software/Hardware Abstraction'
 * '<S10>'  : 'SCUMain/SCU Software/Task Scheduler'
 * '<S11>'  : 'SCUMain/SCU Software/Application Software/Communication'
 * '<S12>'  : 'SCUMain/SCU Software/Application Software/Data Handling'
 * '<S13>'  : 'SCUMain/SCU Software/Application Software/Diagnostics'
 * '<S14>'  : 'SCUMain/SCU Software/Application Software/Object Detection'
 * '<S15>'  : 'SCUMain/SCU Software/Application Software/Operation Logic'
 * '<S16>'  : 'SCUMain/SCU Software/Application Software/Signal Monitoring'
 * '<S17>'  : 'SCUMain/SCU Software/Application Software/State Machine'
 * '<S18>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl'
 * '<S19>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl/TaskSchedular'
 * '<S20>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl/TaskSchedular/Counter Limited'
 * '<S21>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl/TaskSchedular/TaskScheduler'
 * '<S22>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl/TaskSchedular/determinTaskGroupActive'
 * '<S23>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl/TaskSchedular/Counter Limited/Increment Real World'
 * '<S24>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl/TaskSchedular/Counter Limited/Wrap To Zero'
 * '<S25>'  : 'SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl/TaskSchedular/TaskScheduler/TaskScheduling'
 */
#endif                                 /* RTW_HEADER_SCUMain_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
