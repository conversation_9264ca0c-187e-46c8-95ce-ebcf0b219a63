<svg xmlns="http://www.w3.org/2000/svg" width ='20' height ='20' xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16">
    <defs>
        <style>
            .cls-1{fill:none;}
            .cls-2{fill:#b7312c;}
            .cls-3{fill:#fff;}
            .cls-4{fill:#b7312c;}
        </style>
        <radialGradient id="radial-gradient" cx="4.18" cy="4.3" r="9.17" gradientTransform="matrix(1, 0, 0, -1, 0, 14)" gradientUnits="userSpaceOnUse">
            <stop offset="0.38" stop-color="#fff"/>
            <stop offset="0.79" stop-color="#ccc"/>
        </radialGradient>
        <linearGradient id="linear-gradient" x1="6.5" y1="2" x2="6.5" y2="13" gradientTransform="matrix(1, 0, 0, -1, 0, 14)" gradientUnits="userSpaceOnUse">
            <stop offset="0.04" stop-color="#720004"/>
            <stop offset="0.5" stop-color="#e11b22"/>
            <stop offset="1" stop-color="#e84943"/>
        </linearGradient>
    </defs>
    <title>alert_stop_14</title>
    <g id="Layer_2" data-name="Layer 2">
        <g id="Layer_1-2" data-name="Layer 1">
        <rect class="cls-1" width="14" height="14"/>
        <path class="cls-2" d="M6.5,1.69A4.81,4.81,0,1,0,11.31,6.5,4.82,4.82,0,0,0,6.5,1.69Z"/>
        <path class="cls-3" d="M6.5,1.69A4.81,4.81,0,1,0,11.31,6.5,4.82,4.82,0,0,0,6.5,1.69Z"/>
        <path class="cls-4" d="M12,6.5A5.5,5.5,0,1,0,6.5,12,5.5,5.5,0,0,0,12,6.5ZM2.91,3.87,9.1,10.12A4.45,4.45,0,0,1,2,6.5,4.38,4.38,0,0,1,2.91,3.87ZM11,6.5a4.46,4.46,0,0,1-.82,2.56L4,2.82A4.46,4.46,0,0,1,11,6.5Z"/>
        </g>
    </g>
</svg>