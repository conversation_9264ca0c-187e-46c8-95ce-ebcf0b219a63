if (!dojoConfig) { 
	var dojoConfig={}; 
}
if (!dojoConfig.packages) { 
	dojoConfig.packages = []; 
}
dojoConfig.packages.push({'name': 'ActionHandlers', 'location': '/derived/ui/mw-diagnostic-widgets/ActionHandlers'});
dojoConfig.packages.push({'name': 'AxesView', 'location': '/toolbox/shared/sdi/web/AxesView/js'});
dojoConfig.packages.push({'name': 'BootstrapJS', 'location': '/derived/ui/BootstrapJS'});
dojoConfig.packages.push({'name': 'CodeMirror', 'location': '/derived/ui/CodeMirror'});
dojoConfig.packages.push({'name': 'D3js', 'location': '/derived/ui/D3js'});
dojoConfig.packages.push({'name': 'DV', 'location': '/derived/ui/mw-diagnostic-widgets/DV'});
dojoConfig.packages.push({'name': 'DiagnosticObj', 'location': '/derived/ui/mw-diagnostic-widgets/DiagnosticObj'});
dojoConfig.packages.push({'name': 'InstallServiceHandler', 'location': '/install/installservicehandler/web/installservicehandler'});
dojoConfig.packages.push({'name': 'MW', 'location': '/ui/webwidgets/src/js/MW'});
dojoConfig.packages.push({'name': 'SDI2', 'location': '/toolbox/shared/sdi/web/MainView/SDI2'});
dojoConfig.packages.push({'name': 'STIXfonts', 'location': '/derived/3p/ui/STIXfonts'});
dojoConfig.packages.push({'name': 'TableView', 'location': '/toolbox/shared/sdi/web/TableView/js'});
dojoConfig.packages.push({'name': 'Vuejs', 'location': '/derived/ui/Vuejs'});
dojoConfig.packages.push({'name': 'addons-ui', 'location': '/toolbox/matlab/addons/addons-ui'});
dojoConfig.packages.push({'name': 'addressbar-ui', 'location': '/toolbox/matlab/addressbar/addressbar-ui'});
dojoConfig.packages.push({'name': 'aerocomponents', 'location': '/toolbox/aero/aeroshared/web/aerocomponents'});
dojoConfig.packages.push({'name': 'aerocomponents_plugin_uifigure', 'location': '/toolbox/aero/aeroshared/web/plugin/uifigure'});
dojoConfig.packages.push({'name': 'analyzer', 'location': '/toolbox/matlab/system/editor/analyzer/analyzer'});
dojoConfig.packages.push({'name': 'analyzerrpt-ui', 'location': '/toolbox/matlab/codeanalysis/analyzerrpt/web/analyzerrpt/analyzerrpt-ui'});
dojoConfig.packages.push({'name': 'app', 'location': '/toolbox/matlab/login/web/app'});
dojoConfig.packages.push({'name': 'app-ui', 'location': '/toolbox/mdom/app/app-ui'});
dojoConfig.packages.push({'name': 'appcontainer', 'location': '/toolbox/matlab/appcontainer/web/appcontainer_ui'});
dojoConfig.packages.push({'name': 'appcontainer_dojo', 'location': '/toolbox/matlab/appcontainer_dojo/appcontainer'});
dojoConfig.packages.push({'name': 'appdesigner', 'location': '/toolbox/matlab/appdesigner/web/application'});
dojoConfig.packages.push({'name': 'appdesigner_build', 'location': '/toolbox/matlab/appdesigner/appdesigner/build'});
dojoConfig.packages.push({'name': 'appdesigner_comparison', 'location': '/toolbox/matlab/appdesigner/comparisons/mldesktop_js/application'});
dojoConfig.packages.push({'name': 'appdesigner_interface', 'location': '/toolbox/matlab/appdesigner/web/interface'});
dojoConfig.packages.push({'name': 'appdesigner_startscreen', 'location': '/toolbox/matlab/appdesigner/startscreen/web'});
dojoConfig.packages.push({'name': 'browse_for_folder_button', 'location': '/toolbox/matlab/addressbar_plugins/browse_for_folder_button/browse_for_folder_button'});
dojoConfig.packages.push({'name': 'cajsservice', 'location': '/toolbox/matlab/codeanalysis/jsapi/cajsservice'});
dojoConfig.packages.push({'name': 'ccr', 'location': '/toolbox/matlab/codeanalysis/reports/view/ccr'});
dojoConfig.packages.push({'name': 'cd_up_one_dir_button', 'location': '/toolbox/matlab/addressbar_plugins/cd_up_one_dir_button/cd_up_one_dir_button'});
dojoConfig.packages.push({'name': 'classbrowser-ui', 'location': '/toolbox/classdiagram/classbrowser/classbrowser-ui'});
dojoConfig.packages.push({'name': 'clone', 'location': '/toolbox/shared/cmlink/view/clone_web/clone-lib'});
dojoConfig.packages.push({'name': 'cmlink-action-ui', 'location': '/toolbox/shared/cmlink/view/action_web/cmlink-action-ui/cmlink-action-ui'});
dojoConfig.packages.push({'name': 'cmlink-core-ui', 'location': '/toolbox/shared/cmlink/view/core_web/cmlink-core-ui'});
dojoConfig.packages.push({'name': 'cmlink-credentials-lib', 'location': '/toolbox/shared/cmlink/view/credentials_web/cmlink-credentials-lib'});
dojoConfig.packages.push({'name': 'cmlink-git-lib', 'location': '/toolbox/shared/cmlink/view/git_web/cmlink-git-lib'});
dojoConfig.packages.push({'name': 'cmlink-jni-lib', 'location': '/toolbox/shared/cmlink/view/jni_web/cmlink-jni-lib'});
dojoConfig.packages.push({'name': 'cmlink-util-ui', 'location': '/toolbox/shared/cmlink/view/util_web/cmlink-util-ui'});
dojoConfig.packages.push({'name': 'coderapp-common', 'location': '/toolbox/coder/coderapp/common/web/coderapp-common'});
dojoConfig.packages.push({'name': 'coderapp-form', 'location': '/toolbox/coder/coderapp/form/web/coderapp-form'});
dojoConfig.packages.push({'name': 'coderapp-resources', 'location': '/toolbox/coder/coderapp/common/web/coderapp-resources'});
dojoConfig.packages.push({'name': 'coderapp_jsbuild', 'location': '/toolbox/coder/coderapp/buildtools/jsbuild'});
dojoConfig.packages.push({'name': 'coderapp_screener_ui', 'location': '/toolbox/coder/coderapp/screener/web/coderapp_screener_ui'});
dojoConfig.packages.push({'name': 'codingui', 'location': '/toolbox/matlab/rtc_addons/shared/codingui/js'});
dojoConfig.packages.push({'name': 'commandwindowservices', 'location': '/toolbox/matlab/commandwindowservices'});
dojoConfig.packages.push({'name': 'common_features', 'location': '/toolbox/matlab/editor/common/features/js'});
dojoConfig.packages.push({'name': 'comparisoncore', 'location': '/toolbox/matlab/comparisontools/comparisoncore/comparisoncore'});
dojoConfig.packages.push({'name': 'comparisons_text_corewidgets', 'location': '/toolbox/comparisons/text/view/corewidgets/comparisons_text_corewidgets'});
dojoConfig.packages.push({'name': 'comparisons_text_diff', 'location': '/toolbox/comparisons/text/apps/diff/comparisons_text_diff'});
dojoConfig.packages.push({'name': 'comparisons_text_merge', 'location': '/toolbox/comparisons/text/view/merge/comparisons_text_merge'});
dojoConfig.packages.push({'name': 'comparisons_text_widgets', 'location': '/toolbox/comparisons/text/widgets/comparisons_text_widgets'});
dojoConfig.packages.push({'name': 'comparisons_toolstrip', 'location': '/toolbox/comparisons/view/toolstrip/comparisons_toolstrip'});
dojoConfig.packages.push({'name': 'comparisons_tree_diff', 'location': '/toolbox/comparisons/tree/view/apps/diff/comparisons_tree_diff'});
dojoConfig.packages.push({'name': 'comparisons_tree_merge', 'location': '/toolbox/comparisons/tree/view/apps/merge/comparisons_tree_merge'});
dojoConfig.packages.push({'name': 'comparisons_tree_widgets', 'location': '/toolbox/comparisons/tree/view/widgets/comparisons_tree_widgets'});
dojoConfig.packages.push({'name': 'comparisons_view_widgets', 'location': '/toolbox/comparisons/view/widgets/comparisons_view_widgets'});
dojoConfig.packages.push({'name': 'componentframework', 'location': '/toolbox/matlab/uitools/componentframeworkjs'});
dojoConfig.packages.push({'name': 'compositeDVWidget', 'location': '/derived/ui/mw-diagnostic-widgets/compositeDVWidget'});
dojoConfig.packages.push({'name': 'computils-shared', 'location': '/toolbox/shared/computils/shared_web'});
dojoConfig.packages.push({'name': 'computils-ui', 'location': '/toolbox/shared/computils/view_web/computils-ui'});
dojoConfig.packages.push({'name': 'configmanager', 'location': '/toolbox/matlab/configmanager/js'});
dojoConfig.packages.push({'name': 'csview', 'location': '/toolbox/shared/configset_view/web/csview'});
dojoConfig.packages.push({'name': 'currentfolderbrowser-ui', 'location': '/toolbox/matlab/currentfolderbrowser/currentfolderbrowser-ui'});
dojoConfig.packages.push({'name': 'custom-elements', 'location': '/derived/ui/custom-elements'});
dojoConfig.packages.push({'name': 'd3flamegraph', 'location': '/derived/3p/ui/d3flamegraph'});
dojoConfig.packages.push({'name': 'd3tip', 'location': '/derived/ui/d3tip'});
dojoConfig.packages.push({'name': 'dagre', 'location': '/derived/3p/ui/dagre'});
dojoConfig.packages.push({'name': 'datacreation-ui', 'location': '/toolbox/shared/datacreation/datacreation-ui'});
dojoConfig.packages.push({'name': 'datatools-cfb-preview', 'location': '/toolbox/matlab/datatools/matlab_integration/cfb/js/datatools_cfb_preview/datatools_cfb_preview'});
dojoConfig.packages.push({'name': 'datatoolsservices', 'location': '/toolbox/matlab/datatools/datatoolsservices/js/datatoolsservices/src'});
dojoConfig.packages.push({'name': 'dependency-app', 'location': '/toolbox/matlab/dependency/app/web/dependency-app'});
dojoConfig.packages.push({'name': 'dependency-refactoring', 'location': '/toolbox/matlab/dependency/refactoring/web/dependency-refactoring'});
dojoConfig.packages.push({'name': 'dependency-widget-progress', 'location': '/toolbox/matlab/dependency/widget/progress_web/dependency-widget-progress'});
dojoConfig.packages.push({'name': 'desktop_importtool', 'location': '/toolbox/matlab/datatools/desktop_importtool/desktop_importtool'});
dojoConfig.packages.push({'name': 'desktop_variableeditor', 'location': '/toolbox/matlab/datatools/desktop_variableeditor/js'});
dojoConfig.packages.push({'name': 'desktop_workspacebrowser', 'location': '/toolbox/matlab/datatools/desktop_workspacebrowser/js'});
dojoConfig.packages.push({'name': 'dgrid', 'location': '/derived/3p/ui/dgrid'});
dojoConfig.packages.push({'name': 'diffmerge', 'location': '/toolbox/matlab/comparisontools/diffmerge/diffmerge'});
dojoConfig.packages.push({'name': 'dig-ui', 'location': '/toolbox/dig_web/dig/ui'});
dojoConfig.packages.push({'name': 'dijit', 'location': '/derived/3p/ui/dijit'});
dojoConfig.packages.push({'name': 'dojo', 'location': '/derived/3p/ui/dojo'});
dojoConfig.packages.push({'name': 'dojox', 'location': '/derived/3p/ui/dojox'});
dojoConfig.packages.push({'name': 'editor-ui', 'location': '/toolbox/classdiagram/editor/editor-ui'});
dojoConfig.packages.push({'name': 'editor_application_interface', 'location': '/toolbox/matlab/editor/application_interface/js'});
dojoConfig.packages.push({'name': 'element_core', 'location': '/ui/core/element_core/js'});
dojoConfig.packages.push({'name': 'embedded-styles', 'location': '/derived/ui/embedded-styles'});
dojoConfig.packages.push({'name': 'equation_renderer-ui', 'location': '/toolbox/shared/mlreportgen/widgets/equation/js/equation_renderer-ui'});
dojoConfig.packages.push({'name': 'equationrenderer', 'location': '/ui/equationrenderer/equationrenderer-ui'});
dojoConfig.packages.push({'name': 'equationrenderercore', 'location': '/toolbox/matlab/equation_renderer_core/js'});
dojoConfig.packages.push({'name': 'equations', 'location': '/toolbox/matlab/rich_text_component/src/js/equations'});
dojoConfig.packages.push({'name': 'examplemanager', 'location': '/toolbox/matlab/helptools/examplemanager/examplemanager'});
dojoConfig.packages.push({'name': 'export-ui', 'location': '/toolbox/diagram/editor/web/export/export-ui'});
dojoConfig.packages.push({'name': 'file_chooser_service_js', 'location': '/toolbox/matlab/file_chooser/service/file_chooser_service_js'});
dojoConfig.packages.push({'name': 'filebrowser_utils-ui', 'location': '/toolbox/matlab/filebrowser_utils/filebrowser_utils-ui'});
dojoConfig.packages.push({'name': 'filechooser-ui', 'location': '/toolbox/matlab/filechooser/filechooser-ui'});
dojoConfig.packages.push({'name': 'filesystem-datamodel-base', 'location': '/derived/toolbox/matlab/filesystem/datamodel/base'});
dojoConfig.packages.push({'name': 'filesystem-datamodel-sourcecontrol', 'location': '/derived/toolbox/matlab/filesystem/datamodel/sourcecontrol'});
dojoConfig.packages.push({'name': 'filesystem-events', 'location': '/toolbox/matlab/filesystem/events/js'});
dojoConfig.packages.push({'name': 'filesystem-js-utils', 'location': '/toolbox/matlab/filesystem/js_utils/js'});
dojoConfig.packages.push({'name': 'filesystem_services', 'location': '/toolbox/matlab/filesystem/services/client'});
dojoConfig.packages.push({'name': 'findandreplace-ui', 'location': '/ui/find_and_replace/findandreplace-ui'});
dojoConfig.packages.push({'name': 'findfiles-ui', 'location': '/toolbox/matlab/findfiles/js/findfiles-ui'});
dojoConfig.packages.push({'name': 'folder_plugin', 'location': '/toolbox/matlab/filesystem/default_filetype_plugins/folder_plugin'});
dojoConfig.packages.push({'name': 'fsui-config', 'location': '/toolbox/matlab/filesystem/fsui_config/js'});
dojoConfig.packages.push({'name': 'gbtclient', 'location': '/toolbox/matlab/uitools/uifigureappjs/js'});
dojoConfig.packages.push({'name': 'gbtcomponents', 'location': '/toolbox/matlab/uitools/componentsjs'});
dojoConfig.packages.push({'name': 'gbtcomponents_plugin_uifigure', 'location': '/toolbox/matlab/uitools/componentspluginjs'});
dojoConfig.packages.push({'name': 'gbtdivfigure', 'location': '/toolbox/matlab/uitools/divfigurejs/js'});
dojoConfig.packages.push({'name': 'gbtfigure_uicontainer', 'location': '/toolbox/matlab/uitools/figureuicontainerjs/js'});
dojoConfig.packages.push({'name': 'gbtshared', 'location': '/toolbox/matlab/uitools/sharedjs'});
dojoConfig.packages.push({'name': 'graphics', 'location': '/toolbox/matlab/graphics/web/web/scene/js/MW/graphics'});
dojoConfig.packages.push({'name': 'graphlib', 'location': '/derived/ui/graphlib'});
dojoConfig.packages.push({'name': 'helpbrowser', 'location': '/ui/help/helpbrowser/helpbrowser'});
dojoConfig.packages.push({'name': 'helpwin-ui', 'location': '/ui/help/helpwin/helpwin-ui'});
dojoConfig.packages.push({'name': 'hmishared', 'location': '/toolbox/shared/appdes/web/js/MW/hmishared'});
dojoConfig.packages.push({'name': 'htmltext', 'location': '/ui/lightweightbrowser/htmltext/htmltext'});
dojoConfig.packages.push({'name': 'htmlviewer-ui', 'location': '/toolbox/matlab/htmlviewer/web/htmlviewer/htmlviewer-ui'});
dojoConfig.packages.push({'name': 'images', 'location': '/ui/webwidgets/src/images'});
dojoConfig.packages.push({'name': 'importtool_client', 'location': '/toolbox/matlab/datatools/importtool/js/client/importtool_client'});
dojoConfig.packages.push({'name': 'importtool_peer', 'location': '/toolbox/matlab/datatools/importtool/js/peer/importtool_peer'});
dojoConfig.packages.push({'name': 'inspector_client', 'location': '/toolbox/matlab/datatools/inspector/js/client/inspector_client'});
dojoConfig.packages.push({'name': 'inspector_peer', 'location': '/toolbox/matlab/datatools/inspector/js/peer/inspector_peer'});
dojoConfig.packages.push({'name': 'inspector_server', 'location': '/toolbox/matlab/datatools/inspector/matlab'});
dojoConfig.packages.push({'name': 'install_core_services', 'location': '/install/core_services/web'});
dojoConfig.packages.push({'name': 'installer_login', 'location': '/install/components/installer_login/web/installer_login'});
dojoConfig.packages.push({'name': 'installjscommon', 'location': '/install/components/installjscommon/web/installjscommon'});
dojoConfig.packages.push({'name': 'kineticjs', 'location': '/derived/3p/ui/kineticjs'});
dojoConfig.packages.push({'name': 'l10n', 'location': '/ui/webwidgets/src/l10n'});
dojoConfig.packages.push({'name': 'lit-element', 'location': '/derived/3p/ui/lit-element'});
dojoConfig.packages.push({'name': 'lit-html', 'location': '/derived/3p/ui/lit-html'});
dojoConfig.packages.push({'name': 'livecode_features', 'location': '/toolbox/matlab/editor/livecode/features/js'});
dojoConfig.packages.push({'name': 'livecode_saveload', 'location': '/toolbox/matlab/livecode/saveload'});
dojoConfig.packages.push({'name': 'livecontrol', 'location': '/toolbox/matlab/rtc_addons/livecontrol/js'});
dojoConfig.packages.push({'name': 'liveeditor', 'location': '/toolbox/matlab/editor/application/js'});
dojoConfig.packages.push({'name': 'livetask', 'location': '/toolbox/matlab/rtc_addons/livetask/js'});
dojoConfig.packages.push({'name': 'lodash', 'location': '/derived/3p/ui/lodash'});
dojoConfig.packages.push({'name': 'matlab_codecov_js-ui', 'location': '/toolbox/matlab/testframework/unittest/codecov/matlab_codecov_js-ui'});
dojoConfig.packages.push({'name': 'matlab_favorite_commands', 'location': '/toolbox/matlab/matlab_favorite_commands/web/matlab_favorite_commands'});
dojoConfig.packages.push({'name': 'matlab_login', 'location': '/toolbox/matlab/matlab_login/web/matlab_login'});
dojoConfig.packages.push({'name': 'matlab_login_framework', 'location': '/toolbox/matlab/matlab_login_framework/web/matlab_login_framework'});
dojoConfig.packages.push({'name': 'matlab_sourcecontrol_javascript-ui', 'location': '/toolbox/matlab/matlab_sourcecontrol_javascript/matlab_sourcecontrol_javascript-ui'});
dojoConfig.packages.push({'name': 'matlab_toolbox_indentcode', 'location': '/toolbox/matlab/indentcode/web/matlab_toolbox_indentcode'});
dojoConfig.packages.push({'name': 'matlab_unit', 'location': '/toolbox/matlab/editor_addons/matlab_unit/js'});
dojoConfig.packages.push({'name': 'matlabdrive_js', 'location': '/toolbox/matlab/storage/mldrivejsplugins/matlabdrive_js/matlabdrive_js'});
dojoConfig.packages.push({'name': 'matlabeditor', 'location': '/toolbox/shared/dastudio/web/matlabeditor/js'});
dojoConfig.packages.push({'name': 'maven_component_ui', 'location': '/derived/ui/maven_component_ui'});
dojoConfig.packages.push({'name': 'mdom', 'location': '/toolbox/mdom/web/mdom'});
dojoConfig.packages.push({'name': 'mf0', 'location': '/derived/toolbox/modeling/mf0/web'});
dojoConfig.packages.push({'name': 'mf0_sync', 'location': '/derived/toolbox/modeling/mf0_sync/web'});
dojoConfig.packages.push({'name': 'mlapp_actions', 'location': '/toolbox/matlab/appdesigner/matlab_integration/cfb/mlapp_actions/mlapp_actions'});
dojoConfig.packages.push({'name': 'mlapp_preview', 'location': '/toolbox/matlab/appdesigner/matlab_integration/cfb/mlapp_preview/mlapp_preview'});
dojoConfig.packages.push({'name': 'mlappcomparison', 'location': '/toolbox/matlab/appdesigner/comparison/web/application'});
dojoConfig.packages.push({'name': 'mlc', 'location': '/toolbox/coder/coder/web/mlc'});
dojoConfig.packages.push({'name': 'mlc-lib', 'location': '/toolbox/coder/coder/web/mlc-lib'});
dojoConfig.packages.push({'name': 'mlc-resources', 'location': '/toolbox/coder/coder/web/mlc-resources'});
dojoConfig.packages.push({'name': 'mldatx_file_label_filter_plugin', 'location': '/toolbox/shared/mldatx/file_label_filter_plugin/js/mldatx_file_label_filter_plugin'});
dojoConfig.packages.push({'name': 'mldatx_file_preview_plugin', 'location': '/toolbox/shared/mldatx/file_preview_plugin/js/mldatx_file_preview_plugin'});
dojoConfig.packages.push({'name': 'mldo-embedded-client', 'location': '/derived/ui/mldo-embedded-client'});
dojoConfig.packages.push({'name': 'mldosharing-js', 'location': '/toolbox/matlab/mldosharing/js/mldosharing-js'});
dojoConfig.packages.push({'name': 'mldriveecjsservices', 'location': '/toolbox/matlab/storage/mldriveecservices/client'});
dojoConfig.packages.push({'name': 'mm_tree_node_ui', 'location': '/src/mm_tree_node/web/mm_tree_node_ui'});
dojoConfig.packages.push({'name': 'mw-actiondataservice', 'location': '/derived/ui/mw-actiondataservice'});
dojoConfig.packages.push({'name': 'mw-binarystream', 'location': '/derived/ui/mw-binarystream'});
dojoConfig.packages.push({'name': 'mw-browser-utils', 'location': '/derived/ui/mw-browser-utils'});
dojoConfig.packages.push({'name': 'mw-clipboard', 'location': '/derived/ui/mw-clipboard'});
dojoConfig.packages.push({'name': 'mw-colorpicker', 'location': '/derived/ui/mw-colorpicker'});
dojoConfig.packages.push({'name': 'mw-data-model', 'location': '/derived/ui/mw-data-model'});
dojoConfig.packages.push({'name': 'mw-datepicker', 'location': '/derived/ui/mw-datepicker'});
dojoConfig.packages.push({'name': 'mw-ddux', 'location': '/derived/ui/mw-ddux'});
dojoConfig.packages.push({'name': 'mw-deferred-utils', 'location': '/derived/ui/mw-deferred-utils'});
dojoConfig.packages.push({'name': 'mw-deprecation-utils', 'location': '/derived/ui/mw-deprecation-utils'});
dojoConfig.packages.push({'name': 'mw-dialog-utils', 'location': '/derived/ui/mw-dialog-utils'});
dojoConfig.packages.push({'name': 'mw-dialogs', 'location': '/derived/ui/mw-dialogs'});
dojoConfig.packages.push({'name': 'mw-diff', 'location': '/toolbox/shared/comparisons/web/mw-diff/mw-diff'});
dojoConfig.packages.push({'name': 'mw-diff3', 'location': '/toolbox/comparisons/view/web3/mw-diff3/mw-diff3'});
dojoConfig.packages.push({'name': 'mw-dnd', 'location': '/derived/ui/mw-dnd'});
dojoConfig.packages.push({'name': 'mw-dom-snapshot-utils', 'location': '/derived/ui/mw-dom-snapshot-utils'});
dojoConfig.packages.push({'name': 'mw-dom-utils', 'location': '/derived/ui/mw-dom-utils'});
dojoConfig.packages.push({'name': 'mw-draganddrop', 'location': '/derived/ui/mw-draganddrop'});
dojoConfig.packages.push({'name': 'mw-event-utils', 'location': '/derived/ui/mw-event-utils'});
dojoConfig.packages.push({'name': 'mw-facade', 'location': '/derived/ui/mw-facade'});
dojoConfig.packages.push({'name': 'mw-fctrl', 'location': '/ui/fctrl/fctrl_js-lib'});
dojoConfig.packages.push({'name': 'mw-filebrowser', 'location': '/derived/ui/mw-filebrowser'});
dojoConfig.packages.push({'name': 'mw-filename-utils', 'location': '/derived/ui/mw-filename-utils'});
dojoConfig.packages.push({'name': 'mw-form', 'location': '/derived/ui/mw-form'});
dojoConfig.packages.push({'name': 'mw-gesture', 'location': '/derived/ui/mw-gesture'});
dojoConfig.packages.push({'name': 'mw-help-csh', 'location': '/toolbox/matlab/helptools_js/csh/mw-help-csh'});
dojoConfig.packages.push({'name': 'mw-help-csh-base', 'location': '/toolbox/matlab/helptools_js/csh_base/mw-help-csh-base'});
dojoConfig.packages.push({'name': 'mw-help-link-click-handling', 'location': '/toolbox/matlab/helptools_js/link_click_handling_docservice/mw-help-link-click-handling'});
dojoConfig.packages.push({'name': 'mw-help-matlab-colon', 'location': '/toolbox/matlab/helptools_js/matlab_colon_docservice/mw-help-matlab-colon'});
dojoConfig.packages.push({'name': 'mw-help-staticcontent', 'location': '/toolbox/matlab/helptools_js/help_static_content/mw-help-staticcontent'});
dojoConfig.packages.push({'name': 'mw-html-utils', 'location': '/derived/ui/mw-html-utils'});
dojoConfig.packages.push({'name': 'mw-icons', 'location': '/derived/ui/mw-icons'});
dojoConfig.packages.push({'name': 'mw-icons-store', 'location': '/ui/icons'});
dojoConfig.packages.push({'name': 'mw-keybindings', 'location': '/derived/ui/mw-keybindings'});
dojoConfig.packages.push({'name': 'mw-log', 'location': '/derived/ui/mw-log'});
dojoConfig.packages.push({'name': 'mw-login', 'location': '/derived/ui/mw-login'});
dojoConfig.packages.push({'name': 'mw-mda', 'location': '/derived/toolbox/mw_mda'});
dojoConfig.packages.push({'name': 'mw-messageservice', 'location': '/derived/ui/mw-messageservice'});
dojoConfig.packages.push({'name': 'mw-module-loader', 'location': '/derived/ui/mw-module-loader'});
dojoConfig.packages.push({'name': 'mw-mvm', 'location': '/toolbox/mvm_javascript/mw-mvm/mw-mvm-lib'});
dojoConfig.packages.push({'name': 'mw-navigationbar', 'location': '/derived/ui/mw-navigationbar'});
dojoConfig.packages.push({'name': 'mw-notifications', 'location': '/derived/ui/mw-notifications'});
dojoConfig.packages.push({'name': 'mw-overlay-utils', 'location': '/derived/ui/mw-overlay-utils'});
dojoConfig.packages.push({'name': 'mw-peermodel', 'location': '/derived/ui/mw-peermodel'});
dojoConfig.packages.push({'name': 'mw-perf', 'location': '/derived/ui/mw-perf'});
dojoConfig.packages.push({'name': 'mw-popout', 'location': '/derived/ui/mw-popout'});
dojoConfig.packages.push({'name': 'mw-postmessagechannel', 'location': '/derived/ui/mw-postmessagechannel'});
dojoConfig.packages.push({'name': 'mw-progress-indicator', 'location': '/derived/ui/mw-progress-indicator'});
dojoConfig.packages.push({'name': 'mw-promise', 'location': '/derived/ui/mw-promise'});
dojoConfig.packages.push({'name': 'mw-remote', 'location': '/derived/ui/mw-remote'});
dojoConfig.packages.push({'name': 'mw-scrollbar-slider', 'location': '/derived/ui/mw-scrollbar-slider'});
dojoConfig.packages.push({'name': 'mw-slider', 'location': '/derived/ui/mw-slider'});
dojoConfig.packages.push({'name': 'mw-statemachine', 'location': '/derived/ui/mw-statemachine'});
dojoConfig.packages.push({'name': 'mw-staticcontent', 'location': '/toolbox/matlab/connector2/staticcontent/web/mw-staticcontent'});
dojoConfig.packages.push({'name': 'mw-store-utils', 'location': '/derived/ui/mw-store-utils'});
dojoConfig.packages.push({'name': 'mw-string-utils', 'location': '/derived/ui/mw-string-utils'});
dojoConfig.packages.push({'name': 'mw-style-utils', 'location': '/derived/ui/mw-style-utils'});
dojoConfig.packages.push({'name': 'mw-table', 'location': '/derived/ui/mw-table'});
dojoConfig.packages.push({'name': 'mw-tabular-data-model', 'location': '/derived/ui/mw-tabular-data-model'});
dojoConfig.packages.push({'name': 'mw-tooltip', 'location': '/derived/ui/mw-tooltip'});
dojoConfig.packages.push({'name': 'mw-tree', 'location': '/derived/ui/mw-tree'});
dojoConfig.packages.push({'name': 'mw-tree-data-model', 'location': '/derived/ui/mw-tree-data-model'});
dojoConfig.packages.push({'name': 'mw-treetable-data-model', 'location': '/derived/ui/mw-treetable-data-model'});
dojoConfig.packages.push({'name': 'mw-tristate-checkbox', 'location': '/derived/ui/mw-tristate-checkbox'});
dojoConfig.packages.push({'name': 'mw-ui-themes', 'location': '/derived/ui/mw-ui-themes'});
dojoConfig.packages.push({'name': 'mw-utils', 'location': '/derived/ui/mw-utils'});
dojoConfig.packages.push({'name': 'mw-webwindow', 'location': '/toolbox/matlab/connector2/webwindow/web/mw-webwindow'});
dojoConfig.packages.push({'name': 'mw-webwindow-dialogs', 'location': '/derived/ui/mw-webwindow-dialogs'});
dojoConfig.packages.push({'name': 'mw-widget-api', 'location': '/derived/ui/mw-widget-api'});
dojoConfig.packages.push({'name': 'navigation_services', 'location': '/toolbox/matlab/editor/navigation/services/client'});
dojoConfig.packages.push({'name': 'object-hash', 'location': '/derived/3p/ui/object-hash'});
dojoConfig.packages.push({'name': 'online_license_management', 'location': '/src/olm/web/online_license_management'});
dojoConfig.packages.push({'name': 'pathdataservice-js', 'location': '/toolbox/matlab/pathdataservice/pathdataservice-js'});
dojoConfig.packages.push({'name': 'pdfjs', 'location': '/derived/3p/ui/pdfjs'});
dojoConfig.packages.push({'name': 'performance', 'location': '/derived/toolbox/performance/web'});
dojoConfig.packages.push({'name': 'physmod_simscape_language_editor', 'location': '/toolbox/physmod/simscape/language_editor/js/src'});
dojoConfig.packages.push({'name': 'plaincode_features', 'location': '/toolbox/matlab/editor/plaincode/features/js'});
dojoConfig.packages.push({'name': 'plaincode_saveload', 'location': '/toolbox/matlab/rtc_addons/rtcsaveload/plaincode'});
dojoConfig.packages.push({'name': 'popper', 'location': '/derived/3p/ui/popper'});
dojoConfig.packages.push({'name': 'prefdir', 'location': '/toolbox/matlab/prefdir_service/src/js/prefdir'});
dojoConfig.packages.push({'name': 'preprocessing', 'location': '/toolbox/matlab/datatools/preprocessing/js'});
dojoConfig.packages.push({'name': 'printexportapp-ui', 'location': '/toolbox/matlab/uitools/uidialogs/printexportappjs/printexportapp-ui'});
dojoConfig.packages.push({'name': 'profileviewer', 'location': '/toolbox/matlab/profileviewer/profileviewer'});
dojoConfig.packages.push({'name': 'project-action-ui', 'location': '/toolbox/matlab/project/views/action_web/project-action-ui/project-action-ui'});
dojoConfig.packages.push({'name': 'project-cfbextension-ui', 'location': '/toolbox/matlab/project/cfbfileinfoplugin/web-extension/project-cfbextension-ui'});
dojoConfig.packages.push({'name': 'project-cfbicon-ui', 'location': '/toolbox/matlab/project/cfbfileinfoplugin/web-icon/project-cfbicon-ui'});
dojoConfig.packages.push({'name': 'project-cfbpreview-ui', 'location': '/toolbox/matlab/project/cfbfileinfoplugin/web-preview/project-cfbpreview-ui'});
dojoConfig.packages.push({'name': 'project-core-ui', 'location': '/toolbox/matlab/project/views/core_web/project-core-ui'});
dojoConfig.packages.push({'name': 'project-creation', 'location': '/toolbox/matlab/project/views/creation_web/project-creation'});
dojoConfig.packages.push({'name': 'project-file-ui', 'location': '/toolbox/matlab/project/views/file_web/project-file-ui'});
dojoConfig.packages.push({'name': 'project-fromfile-ui', 'location': '/toolbox/matlab/project/views/fromfile_web/project-fromfile-ui'});
dojoConfig.packages.push({'name': 'project-labels-ui', 'location': '/toolbox/matlab/project/views/labels_web/project-labels-ui'});
dojoConfig.packages.push({'name': 'project-references-ui', 'location': '/toolbox/matlab/project/views/references_web/project-references-ui'});
dojoConfig.packages.push({'name': 'project-sharing-profiles', 'location': '/toolbox/matlab/project/sharing/profilesview_web/project-sharing-profiles'});
dojoConfig.packages.push({'name': 'project-sharing-toarchive', 'location': '/toolbox/matlab/project/sharing/toarchive_web'});
dojoConfig.packages.push({'name': 'project-sharing-validation', 'location': '/toolbox/matlab/project/sharing/validationview_web'});
dojoConfig.packages.push({'name': 'project-sharing-widgets', 'location': '/toolbox/matlab/project/sharing/widgets_web'});
dojoConfig.packages.push({'name': 'project-util-ui', 'location': '/toolbox/matlab/project/views/util_web/project-util-ui'});
dojoConfig.packages.push({'name': 'prop-types', 'location': '/derived/ui/prop-types'});
dojoConfig.packages.push({'name': 'put-selector', 'location': '/derived/3p/ui/put-selector'});
dojoConfig.packages.push({'name': 'react', 'location': '/derived/3p/ui/react'});
dojoConfig.packages.push({'name': 'reflectionserviceAPI', 'location': '/toolbox/matlab/reflectionserviceAPI'});
dojoConfig.packages.push({'name': 'regenerator-runtime', 'location': '/derived/3p/ui/regenerator-runtime'});
dojoConfig.packages.push({'name': 'registration_framework_js', 'location': '/toolbox/matlab/registration_framework/reg_fw_js'});
dojoConfig.packages.push({'name': 'remoteproxy', 'location': '/remote/proxy'});
dojoConfig.packages.push({'name': 'rendererseditors', 'location': '/toolbox/matlab/datatools/rendererseditors/js/rendererseditors'});
dojoConfig.packages.push({'name': 'report', 'location': '/toolbox/coder/simulinkcoder_app/report/web/report'});
dojoConfig.packages.push({'name': 'reportviewer', 'location': '/toolbox/coder/coder/web/reportviewer/reportviewer'});
dojoConfig.packages.push({'name': 'richcontent_preview_plugin', 'location': '/toolbox/matlab/richcontent_preview/js/richcontent_preview_plugin'});
dojoConfig.packages.push({'name': 'rtc', 'location': '/toolbox/matlab/rich_text_component/src/js/rtc'});
dojoConfig.packages.push({'name': 'rtc_clike_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_clike_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_java_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_java_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_javascript_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_js_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_mlike_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_mlike_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_plugin_uifigure', 'location': '/toolbox/matlab/rich_text_component/rtc_figure_integration/web/plugin/uifigure'});
dojoConfig.packages.push({'name': 'rtc_python_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_python_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_tlc_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_tlc_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_verilog_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_verilog_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_vhdl_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_vhdl_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtc_xml_language_support', 'location': '/toolbox/matlab/rtc_addons/rtclanguagesupport/rtc_xml_language_support/js/src'});
dojoConfig.packages.push({'name': 'rtcaddon_simulink_requirements', 'location': '/toolbox/matlab/rtc_addons/vnv/req/js'});
dojoConfig.packages.push({'name': 'rtcintegration', 'location': '/toolbox/matlab/rich_text_component/src/js/rtcintegration'});
dojoConfig.packages.push({'name': 'settings', 'location': '/toolbox/matlab/settings_service/src/js/settings'});
dojoConfig.packages.push({'name': 'sf_file_icon_plugin', 'location': '/toolbox/stateflow/sf_file_preview_plugin/js-icon/sf_file_icon_plugin'});
dojoConfig.packages.push({'name': 'sf_file_preview_plugin', 'location': '/toolbox/stateflow/sf_file_preview_plugin/js-preview/sf_file_preview_plugin'});
dojoConfig.packages.push({'name': 'sf_file_type_plugin', 'location': '/toolbox/stateflow/sf_file_preview_plugin/js-type/sf_file_type_plugin'});
dojoConfig.packages.push({'name': 'share_folder_preview', 'location': '/toolbox/matlab/storage/mldrivejsplugins/share_folder_preview/js'});
dojoConfig.packages.push({'name': 'sharing_actions', 'location': '/toolbox/matlab/storage/mldrivejsplugins/sharing_actions/sharing_actions'});
dojoConfig.packages.push({'name': 'sidebar', 'location': '/ui/layout/sidebar/js'});
dojoConfig.packages.push({'name': 'sidepanel-ui', 'location': '/toolbox/matlab/plottools/sidepanel/sidepanel-ui'});
dojoConfig.packages.push({'name': 'slcoderRpt', 'location': '/toolbox/coder/simulinkcoder_app/slcoderRpt/src/slcoderRpt_js/slcoderRpt'});
dojoConfig.packages.push({'name': 'sourcecontrol-ui', 'location': '/toolbox/matlab/sourcecontrol/sourcecontrol-ui'});
dojoConfig.packages.push({'name': 'spinner', 'location': '/derived/3p/ui/spinner'});
dojoConfig.packages.push({'name': 'supportsoftwareclient', 'location': '/install/components/supportsoftwareclient/web/supportsoftwareclient'});
dojoConfig.packages.push({'name': 'supportsoftwareinstaller', 'location': '/install/components/supportsoftwareinstaller/web'});
dojoConfig.packages.push({'name': 'svg-intersections', 'location': '/derived/3p/ui/svg-intersections'});
dojoConfig.packages.push({'name': 'system_object', 'location': '/toolbox/matlab/editor_addons/system_object/js'});
dojoConfig.packages.push({'name': 'toolstrip', 'location': '/toolbox/matlab/toolstrip/web'});
dojoConfig.packages.push({'name': 'tripwire_button', 'location': '/toolbox/matlab/storage/mldrivejsplugins/tripwire_button/tripwire_button'});
dojoConfig.packages.push({'name': 'uicomponents_appdesigner_plugin', 'location': '/toolbox/matlab/uicomponents/web/plugin/appdesigner'});
dojoConfig.packages.push({'name': 'uicomponents_plugin_uifigure', 'location': '/toolbox/matlab/uicomponents/web/plugin/uifigure'});
dojoConfig.packages.push({'name': 'uifigure_interface', 'location': '/toolbox/matlab/uitools/uifigureinterfacejs'});
dojoConfig.packages.push({'name': 'uisetcolor', 'location': '/toolbox/matlab/uitools/uidialogs/uisetcolorappjs/uisetcolor'});
dojoConfig.packages.push({'name': 'uitest', 'location': '/toolbox/matlab/testframework/uiautomation_web/uitest'});
dojoConfig.packages.push({'name': 'userexecutionservice', 'location': '/toolbox/matlab/userexecutionservice'});
dojoConfig.packages.push({'name': 'uuid', 'location': '/derived/3p/ui/uuid'});
dojoConfig.packages.push({'name': 'variableeditor', 'location': '/toolbox/matlab/codetools/uicomponents/web/datatools/variableeditor/js'});
dojoConfig.packages.push({'name': 'variableeditor_client', 'location': '/toolbox/matlab/datatools/variableeditor/js/client/variableeditor_client'});
dojoConfig.packages.push({'name': 'variableeditor_peer', 'location': '/toolbox/matlab/datatools/variableeditor/js/peer/variableeditor_peer'});
dojoConfig.packages.push({'name': 'viewmarks', 'location': '/toolbox/shared/dastudio/web/viewmarks/viewmarks'});
dojoConfig.packages.push({'name': 'viewmarksmanager', 'location': '/toolbox/shared/dastudio/web/viewmarksmanager/viewmarksmanager'});
dojoConfig.packages.push({'name': 'viewmodel', 'location': '/toolbox/shared/viewmodel/web/viewmodel'});
dojoConfig.packages.push({'name': 'viewmodel_zml', 'location': '/toolbox/shared/viewmodel/zml/json'});
dojoConfig.packages.push({'name': 'visualcomponents', 'location': '/toolbox/matlab/uicomponents/web/components'});
dojoConfig.packages.push({'name': 'vor', 'location': '/derived/toolbox/modeling/vor/web'});
dojoConfig.packages.push({'name': 'webfontloaderjs', 'location': '/derived/3p/ui/webfontloaderjs'});
dojoConfig.packages.push({'name': 'webscopes', 'location': '/toolbox/shared/spcuilib/jswebscopes/js'});
dojoConfig.packages.push({'name': 'webwidgets_css', 'location': '/ui/webwidgets/src/css'});
dojoConfig.packages.push({'name': 'widgets', 'location': '/toolbox/matlab/datatools/widgets/js/widgets'});
dojoConfig.packages.push({'name': 'wigl', 'location': '/derived/toolbox/diagram/editor/web/transpiled'});
dojoConfig.packages.push({'name': 'workspacebrowser', 'location': '/toolbox/matlab/codetools/uicomponents/web/datatools/workspacebrowser/js'});
dojoConfig.packages.push({'name': 'xstyle', 'location': '/derived/3p/ui/xstyle'});
dojoConfig.packages.push({'name': 'css-matlabeditor', 'location': '/toolbox/shared/dastudio/web/matlabeditor/css-matlabeditor'});

dojoConfig.supportedLocales = ['ja-jp','ko-kr','zh-cn','en-us'];
dojoConfig.localeList = 'ja-jp,ko-kr,zh-cn,en-us';
dojoConfig.map = {"*":{"mw-menu":"mw-form","mw-mixins":"mw-form/mixins","mw-mixins-tests":"mw-form/mixins-tests"}};
