<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="stylesheet" type="text/css" href="rtwreport.css" /><title>
Coder Assumptions for 'SCUMain'
</title>

</head>
<body onload="try {if (top) {if (top.rtwPageOnLoad) top.rtwPageOnLoad('rtwIdCoderAssumptionsPage'); else local_onload();}} catch(err) {};">
<h1>
Coder Assumptions for 'SCUMain'
</h1>
<div>
<p>
List of assumptions that you can check and expected results for selected target environment. For more information see, <a href="javascript: void(0)" onclick="postParentWindowMessage({message:'legacyMCall', expr:'helpview(fullfile(docroot,\'toolbox\',\'ecoder\',\'helptargets.map\'),\'verif_of_code_generation_assumptions\')'})" >Verification of Code Generation Assumptions</a>.
</p>
</div>
<div>

</div>
<h3 name="sec_C_Language_Configuration_for_NXP->Cortex-M0/M0+_Target_Hardware" id="sec_target_hardware">
C Language Configuration for NXP->Cortex-M0/M0+ Target Hardware
</h3>
<table width="100%" border="0">
<tr>
<td align="left" valign="top">
<p>
<table class="AltRow" cellspacing="0">
<tr class="even">
<td align="left" valign="top">
BitPerChar
</td>
<td align="left" valign="top">
8
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
BitPerShort
</td>
<td align="left" valign="top">
16
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
BitPerInt
</td>
<td align="left" valign="top">
32
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
BitPerLong
</td>
<td align="left" valign="top">
32
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
BitPerPointer
</td>
<td align="left" valign="top">
32
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
BitPerSizeT
</td>
<td align="left" valign="top">
32
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
BitPerPtrDiffT
</td>
<td align="left" valign="top">
32
</td>

</tr>

</table>
<br /><table class="AltRow" cellspacing="0">
<tr class="even">
<td align="left" valign="top">
Endianness
</td>
<td align="left" valign="top">
LittleEndian
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Shift right for signed integer is arithmetic shift.
</td>
<td align="left" valign="top">
True
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
Signed integer division rounds to
</td>
<td align="left" valign="top">
Zero
</td>

</tr>

</table>

</p>

</td>

</tr>

</table>
<h3 name="sec_C_Language_Standard" id="sec_lang_standard">
C Language Standard
</h3>
<table width="100%" border="0">
<tr>
<td align="left" valign="top">
<p>
Zero initialization code has been optimized for model 'SCUMain'.
</p>
<p>
<table class="AltRow" cellspacing="0">
<tr class="even">
<td align="left" valign="top">
Initial value of a global integer variable is zero.
</td>
<td align="left" valign="top">
True
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Initial value of each element of dynamically allocated int array is zero.
</td>
<td align="left" valign="top">
True
</td>

</tr>

</table>

</p>
<p>
If assumption is not correct, remove optimization by using <a href="javascript: void(0)" onclick="postParentWindowMessage({message:'legacyMCall', expr:'rtw.report.CoderAssumptions.showMemZeroInitParams(\'SCUMain\')'})" >Configuration Parameters > Code Generation > Optimization</a> settings. Alternatively, configure target environment to make assumption hold.
</p>

</td>

</tr>

</table>

<script>function postParentWindowMessage(message) {window.parent.postMessage(message, "*");}</script>
</body>

</html>
