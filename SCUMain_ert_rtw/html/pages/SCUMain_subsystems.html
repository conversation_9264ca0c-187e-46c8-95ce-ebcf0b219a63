<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="stylesheet" type="text/css" href="rtwreport.css" /><script language="JavaScript" type="text/javascript" src="rtwshrink.js"></script><title>
Non-virtual subsystems in SCUMain
</title>

</head>
<body onload="try {if (top) {if (top.rtwPageOnLoad) top.rtwPageOnLoad('rtwIdSubsystem'); else local_onload();}} catch(err) {};">
<h1>
Non-virtual subsystems in SCUMain
</h1>
<div>

</div>
<div>

</div>
<h3 name="sec_Code_Mappings" id="sec_code_mapping">
1. Code Mappings <span title="Click to shrink or expand section" style="cursor:pointer;font-weight:normal;" id="rtwIdSubsystem_table_001_control" onclick ="if (rtwTableShrink) rtwTableShrink(window.document, this, 'rtwIdSubsystem_table_001', false)"><span class="shrink-button">[<u>hide</u>]</span></span>
</h3>
<table width="100%" name="rtwIdSubsystem_table_001" id="rtwIdSubsystem_table_001" border="0">
<tr>
<td align="left" valign="top">
<p>
This table: <br /><ul>
<li>
provides a mapping from non-virtual subsystems in model to functions or reusable functions in generated code. The table
</li>
<li>
notes exceptions that caused nonvirtual subsystems to not reuse code even though they were assigned a function packaging setting ('Function packaging' entry on the Subsystem Block Dialog) of 'Auto' or 'Reusable function'.
</li>

</ul>

</p>

</td>

</tr>
<tr>
<td align="left" valign="top">
<table class="AltRow FirstColumn" cellspacing="0">
<tr class="heading">
<th align="left" valign="top">
<b>
Subsystem
</b>

</th>
<th align="left" valign="top">
<b>
Reuse Setting
</b>

</th>
<th align="left" valign="top">
<b>
Reuse Outcome
</b>

</th>
<th align="left" valign="top">
<b>
Outcome Diagnostic
</b>

</th>

</tr>
<tr class="even">
<td align="left" valign="top">
&lt;S18&gt;
</td>
<td align="left" valign="top">
Function
</td>
<td align="left" valign="top">
<a href="javascript: void(0)" onclick="postParentWindowMessage({message:'jumpToCode',location:'SCUMain_TaskSchedular_Mdl'})" class="reportToCode" fcnName="SCUMain_TaskSchedular_Mdl">Function(S18)</a>
</td>
<td align="left" valign="top">
<FONT COLOR="green">normal</FONT>
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
&lt;S2&gt;
</td>
<td align="left" valign="top">
Function
</td>
<td align="left" valign="top">
<a href="javascript: void(0)" onclick="postParentWindowMessage({message:'jumpToCode',location:'PIDDID_IoCtrl'})" class="reportToCode" fcnName="PIDDID_IoCtrl">Function(S2)</a>
</td>
<td align="left" valign="top">
<FONT COLOR="green">normal</FONT>
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
&lt;S3&gt;
</td>
<td align="left" valign="top">
Function
</td>
<td align="left" valign="top">
<a href="javascript: void(0)" onclick="postParentWindowMessage({message:'jumpToCode',location:'PIDDID_ReadDID'})" class="reportToCode" fcnName="PIDDID_ReadDID">Function(S3)</a>
</td>
<td align="left" valign="top">
<FONT COLOR="green">normal</FONT>
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
&lt;S4&gt;
</td>
<td align="left" valign="top">
Function
</td>
<td align="left" valign="top">
<a href="javascript: void(0)" onclick="postParentWindowMessage({message:'jumpToCode',location:'PIDDID_RoutineCtrl'})" class="reportToCode" fcnName="PIDDID_RoutineCtrl">Function(S4)</a>
</td>
<td align="left" valign="top">
<FONT COLOR="green">normal</FONT>
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
&lt;S5&gt;
</td>
<td align="left" valign="top">
Function
</td>
<td align="left" valign="top">
<a href="javascript: void(0)" onclick="postParentWindowMessage({message:'jumpToCode',location:'PIDDID_WriteDID'})" class="reportToCode" fcnName="PIDDID_WriteDID">Function(S5)</a>
</td>
<td align="left" valign="top">
<FONT COLOR="green">normal</FONT>
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
&lt;S7&gt;
</td>
<td align="left" valign="top">
Function
</td>
<td align="left" valign="top">
<a href="javascript: void(0)" onclick="postParentWindowMessage({message:'jumpToCode',location:'SCUMain_hallInterrupt'})" class="reportToCode" fcnName="SCUMain_hallInterrupt">Function(S7)</a>
</td>
<td align="left" valign="top">
<FONT COLOR="green">normal</FONT>
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
&lt;S25&gt;
</td>
<td align="left" valign="top">
Auto
</td>
<td align="left" valign="top">
Inline
</td>
<td align="left" valign="top">
<FONT COLOR="green">normal</FONT>
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
&lt;S6&gt;
</td>
<td align="left" valign="top">
Auto
</td>
<td align="left" valign="top">
Inline
</td>
<td align="left" valign="top">
<A HREF="#S6blker"TARGET="rtwreport_document_frame"><FONT COLOR="red">[exceptions]</FONT></A>
</td>

</tr>

</table>

</td>

</tr>

</table>
<h3 name="sec_Code_Reuse_Exceptions" id="sec_reuse_exception">
2. Code Reuse Exceptions <span title="Click to shrink or expand section" style="cursor:pointer;font-weight:normal;" id="rtwIdSubsystem_table_002_control" onclick ="if (rtwTableShrink) rtwTableShrink(window.document, this, 'rtwIdSubsystem_table_002', false)"><span class="shrink-button">[<u>hide</u>]</span></span>
</h3>
<table width="100%" name="rtwIdSubsystem_table_002" id="rtwIdSubsystem_table_002" border="0">
<tr>
<td align="left" valign="top">
<p>
This section provides details on each exception that caused a nonvirtual subsystem with a function packaging setting of<br /><ul>
<li>
'Auto' to become an inlined code segment,
</li>
<li>
'Auto' to become a non-reusable function without arguments, or
</li>
<li>
'Reusable function' to become a non-reusable function without arguments.
</li>

</ul>
<b>Note:</b>This section does not report graphically identical nonvirtual subsystems marked as 'Auto' that were not reused due to differences in their functional properties (such as dimensions, data types, work vectors, parameters, and so on).  You can identify reasons for nonreuse in such cases by inspecting the differences in the functional attributes of the subsystems in the model or in the inlined generated code.
</p>

</td>

</tr>
<tr>
<td align="left" valign="top">
<A NAME="S6blker">Contents of &lt;S6&gt; not reusable because: </B><BR />

<ul>

<li>The following block was marked as 'Function'. [SCUMain/SCU Software/Task Scheduler/TaskSchedular_Mdl]</li>
</ul>


</td>

</tr>

</table>

</body>

</html>
