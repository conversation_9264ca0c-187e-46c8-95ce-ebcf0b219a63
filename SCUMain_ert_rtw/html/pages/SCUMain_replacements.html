<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="stylesheet" type="text/css" href="rtwreport.css" /><title>
Code replacements in SCUMain
</title>

</head>
<body onload="try {if (top) {if (top.rtwPageOnLoad) top.rtwPageOnLoad('rtwIdCodeReplacements'); else local_onload();}} catch(err) {};">
<h1>
Code replacements in SCUMain
</h1>
<div>

</div>
<img src="hilite_warning.png" />Code replacements report not generated. Select <a href="javascript: void(0)" onclick="postParentWindowMessage({message:'legacyMCall', expr:'configset.highlightParameter(\'SCUMain\', \'GenerateCodeReplacementReport\')'})" >'Summarize which blocks triggered code replacements'</a>.
<script>function postParentWindowMessage(message) {window.parent.postMessage(message, "*");}</script>
</body>

</html>
