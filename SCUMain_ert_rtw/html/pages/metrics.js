function CodeMetrics() {
	 this.metricsArray = {};
	 this.metricsArray.var = new Array();
	 this.metricsArray.fcn = new Array();
	 this.metricsArray.var["TaskSch_Man.c:ST_u8_1msCntr"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code\\TaskSch_Man.c",
	size: 1};
	 this.metricsArray.fcn["CRC16_CCITT"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\Common\\Code\\irs_lib.c",
	stack: 9,
	stackTotal: 9};
	 this.metricsArray.fcn["DataCopy"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\Common\\Code\\irs_lib.c",
	stack: 10,
	stackTotal: 10};
	 this.metricsArray.fcn["DataCopySelect"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\Common\\Code\\irs_lib.c",
	stack: 3,
	stackTotal: 3};
	 this.metricsArray.fcn["DataSet"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\Common\\Code\\irs_lib.c",
	stack: 2,
	stackTotal: 2};
	 this.metricsArray.fcn["DataSwap"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\Common\\Code\\irs_lib.c",
	stack: 3,
	stackTotal: 3};
	 this.metricsArray.fcn["TaskSch_monitorTaskTimeEnd"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code\\TaskSch_Man.c",
	stack: 0,
	stackTotal: 0};
	 this.metricsArray.fcn["TaskSch_monitorTaskTimeStart"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code\\TaskSch_Man.c",
	stack: 0,
	stackTotal: 0};
	 this.metricsArray.fcn["TaskSch_u8Get1msCntr"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code\\TaskSch_Man.c",
	stack: 0,
	stackTotal: 0};
	 this.metricsArray.fcn["TaskSch_vDec1msCntr"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code\\TaskSch_Man.c",
	stack: 0,
	stackTotal: 0};
	 this.metricsArray.fcn["TaskSch_vInc1msCntr"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code\\TaskSch_Man.c",
	stack: 0,
	stackTotal: 0};
	 this.metricsArray.fcn["TaskSch_vResetMaxValues"] = {file: "C:\\Users\\<USER>\\workspace\\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\\MBDevStrm\\SwApplication\\Application\\TaskSchedulerLayer\\TaskSch\\Code\\TaskSch_Man.c",
	stack: 0,
	stackTotal: 0};
	 this.getMetrics = function(token) { 
		 var data;
		 data = this.metricsArray.var[token];
		 if (!data) {
			 data = this.metricsArray.fcn[token];
			 if (data) data.type = "fcn";
		 } else { 
			 data.type = "var";
		 }
	 return data; }; 
	 this.codeMetricsSummary = '<a href="javascript:void(0)" onclick="return postParentWindowMessage({message:\'gotoReportPage\', pageName:\'SCUMain_metrics\'});">Global Memory: 1(bytes) Maximum Stack: 10(bytes)</a>';
	}
CodeMetrics.instance = new CodeMetrics();
