<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="stylesheet" type="text/css" href="rtwreport.css" /><script language="JavaScript" type="text/javascript" src="rtwcodemetricsreport_utils.js"></script>
<script language="JavaScript" id="metrics.js" type="text/javascript" src="metrics.js"></script>
<title>
Code Generation Report for 'SCUMain'
</title>

</head>
<body onload="try {if (top) {if (top.rtwPageOnLoad) top.rtwPageOnLoad('rtwIdSummaryPage'); else local_onload();}} catch(err) {};">
<h1>
Code Generation Report for 'SCUMain'
</h1>
<div>

</div>
<div>

</div>
<h3 name="sec_Model_Information" id="sec_model_info">
Model Information
</h3>
<table width="100%" border="0">
<tr>
<td align="left" valign="top">
<p>
<table class="AltRow" cellspacing="0">
<tr class="even">
<td align="left" valign="top">
Author
</td>
<td align="left" valign="top">
kortam
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Last Modified By
</td>
<td align="left" valign="top">
allz
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
Model Version
</td>
<td align="left" valign="top">
1.387
</td>

</tr>

</table>
<br /><a onclick="return postParentWindowMessage({message:'legacyMCall', expr:'coder.internal.viewCodeConfigsetFromReport(\'file:///C:/Users/<USER>/workspace/MB_ASSY_SCU_SW_DevStrm_allz_Workspace/slprj/ert/SCUMain/tmwinternal/binfo.mat?SCUMain\')'});" id="linkToCS_V2" href="javascript:void(0)">
Configuration settings at time of code generation
</a>

</p>

</td>

</tr>

</table>
<h3 name="sec_Code_Information" id="sec_code_info">
Code Information
</h3>
<table width="100%" border="0">
<tr>
<td align="left" valign="top">
<p>
<table class="AltRow" cellspacing="0">
<tr class="even">
<td align="left" valign="top">
System Target File
</td>
<td align="left" valign="top">
ert.tlc
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Hardware Device Type
</td>
<td align="left" valign="top">
NXP->Cortex-M0/M0+
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
Simulink Coder Version
</td>
<td align="left" valign="top">
9.8 (R2022b) 13-May-2022
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Timestamp of Generated Source Code
</td>
<td align="left" valign="top">
Wed Jun  4 11:44:35 2025
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
<span id="sourceLocationTitle">Location of Generated Source Code</span>
</td>
<td align="left" valign="top">
<span id="sourceLocation">C:\Users\<USER>\workspace\MB_ASSY_SCU_SW_DevStrm_allz_Workspace\SCUMain_ert_rtw</span>
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Type of Build
</td>
<td align="left" valign="top">
Exported Top Model
</td>

</tr>
<tr class="even">
<td align="left" valign="top">
<span id="metricsLocationTitle"> Memory Information </span>
</td>
<td align="left" valign="top">
<span id="metricsLocation"><script>document.write("Code Metrics not yet available"); getCodeMetricsByPolling();</script></span>
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Objectives Specified
</td>
<td align="left" valign="top">
<b>
<font Color="orange">
Unspecified
</font>

</b>

</td>

</tr>

</table>

</p>

</td>

</tr>

</table>
<h3 name="sec_Additional_Information" id="sec_additional_info">
Additional Information
</h3>
<table width="100%" border="0">
<tr>
<td align="left" valign="top">
<table class="AltRow" cellspacing="0">
<tr class="even">
<td align="left" valign="top">
Code Generation Advisor
</td>
<td align="left" valign="top">
Not run
</td>

</tr>
<tr class="odd">
<td align="left" valign="top">
Subsystem Report
</td>
<td align="left" valign="top">
<a href="SCUMain_subsystems.html">Code Reuse Exceptions</a>
</td>

</tr>

</table>

</td>

</tr>

</table>

<script>function postParentWindowMessage(message) {window.parent.postMessage(message, "*");}</script>
</body>

</html>
