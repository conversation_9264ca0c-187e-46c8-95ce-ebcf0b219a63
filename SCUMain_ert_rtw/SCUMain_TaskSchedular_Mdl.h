/*
 * File: SCUMain_TaskSchedular_Mdl.h
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_SCUMain_TaskSchedular_Mdl_h_
#define RTW_HEADER_SCUMain_TaskSchedular_Mdl_h_
#ifndef SCUMain_COMMON_INCLUDES_
#define SCUMain_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* SCUMain_COMMON_INCLUDES_ */

extern void SCUMain_TaskSchedular_Mdl_Init(void);
extern void SCUMain_TaskSchedular_Mdl(void);

#endif                             /* RTW_HEADER_SCUMain_TaskSchedular_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
