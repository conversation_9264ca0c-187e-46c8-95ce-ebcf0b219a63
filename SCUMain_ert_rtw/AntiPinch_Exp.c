/*
 * File: AntiPinch_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "AntiPinch_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T AntiPinch_FDifference;         /* Force difference (Rollo)
                                        */
int16_T AntiPinch_FTracking;           /* Tracking force (Rollo)
                                        */
boolean_T AntiPinch_isAtsDetected;
                     /* Boolean to indicate if a pinch is detected at the moment
                      */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
