/*
 * File: BlockDet_Mdl.h
 *
 * Code generated for Simulink model 'BlockDet_Mdl'.
 *
 * Model version                  : 1.187
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:29:26 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_BlockDet_Mdl_h_
#define RTW_HEADER_BlockDet_Mdl_h_
#ifndef BlockDet_Mdl_COMMON_INCLUDES_
#define BlockDet_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* BlockDet_Mdl_COMMON_INCLUDES_ */

#include "BlockDet_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "BlockDet_Mdl_types.h"
#include "LearnAdp_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "RoofSys_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "BlockDet_Mdl_Exp.h"
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'BlockDet_Mdl' */
#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  int32_T DetectionDifference;         /* '<S4>/Subtract1' */
  uint8_T SFunction1;                  /* '<S36>/S-Function1' */
  uint8_T SFunction;                   /* '<S35>/S-Function' */
  uint8_T Saturation;                  /* '<S16>/Saturation' */
  boolean_T isBlockDisabled;           /* '<S4>/Chart' */
  boolean_T LogicalOperator;           /* '<S10>/Logical Operator' */
  boolean_T isDoubleStallFault;        /* '<S10>/DetectDoubleStall' */
} B_BlockDet_Mdl_c_T;

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'BlockDet_Mdl' */
#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T UnitDelay3_DSTATE;          /* '<S4>/Unit Delay3' */
  CtrlLog_tenTargetDirection UnitDelay1_DSTATE;/* '<S4>/Unit Delay1' */
  CtrlLog_tenTargetDirection stMtrCtrlCmd_prev;/* '<S4>/Chart' */
  CtrlLog_tenTargetDirection stMtrCtrlCmd_start;/* '<S4>/Chart' */
  uint16_T DelayInput1_DSTATE;         /* '<S33>/Delay Input1' */
  uint16_T UnitDelay_DSTATE;           /* '<S4>/Unit Delay' */
  uint16_T UnitDelay2_DSTATE;          /* '<S4>/Unit Delay2' */
  uint16_T UnitDelay_DSTATE_a[2];      /* '<S16>/Unit Delay' */
  uint8_T DelayInput1_DSTATE_h;        /* '<S29>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_c;        /* '<S11>/Delay Input1' */
  uint8_T UnitDelay_DSTATE_g;          /* '<S3>/Unit Delay' */
  uint8_T UnitDelay1_DSTATE_n;         /* '<S10>/Unit Delay1' */
  boolean_T DelayInput1_DSTATE_j;      /* '<S13>/Delay Input1' */
  BlockDet_Stall_t DelayInput1_DSTATE_n;/* '<S30>/Delay Input1' */
  BlockDet_Stall_dir DelayInput1_DSTATE_jw;/* '<S32>/Delay Input1' */
  uint8_T is_c1_BlockDet_Mdl;          /* '<S4>/Chart' */
  uint8_T is_active_c1_BlockDet_Mdl;   /* '<S4>/Chart' */
  uint8_T temporalCounter_i1;          /* '<S4>/Chart' */
  uint8_T is_c2_BlockDet_Mdl;          /* '<S10>/DetectDoubleStall' */
  uint8_T is_active_c2_BlockDet_Mdl;   /* '<S10>/DetectDoubleStall' */
  PnlOp_Rev_t stReversal_prev;         /* '<S10>/DetectDoubleStall' */
  PnlOp_Rev_t stReversal_start;        /* '<S10>/DetectDoubleStall' */
} DW_BlockDet_Mdl_f_T;

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'BlockDet_Mdl' */
#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState writeStallPosToNVM_Trig_ZCE;/* '<S6>/writeStallPosToNVM' */
  ZCSigState writeStallDirToNVM_Trig_ZCE;/* '<S6>/writeStallDirToNVM' */
  ZCSigState StallTypeCheck_Trig_ZCE;  /* '<S10>/StallTypeCheck' */
} ZCE_BlockDet_Mdl_T;

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_BlockDet_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_BlockDet_Mdl_T rtm;
} MdlrefDW_BlockDet_Mdl_T;

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint32_T HallDeco_sumFilterdPulseTicks;
                    /* Simulink.Signal object 'HallDeco_sumFilterdPulseTicks' */
extern uint16_T BlockDet_tBlockedTime_P;
                          /* Simulink.Signal object 'BlockDet_tBlockedTime_P' */
extern void BlockDet_Mdl_Init(void);
extern void BlockDet_Mdl(const uint16_T *rtu_LearnAdapBus_LearnAdap_newSoftStop,
  const boolean_T *rtu_LearnAdapBus_LearnAdap_isLearnComplete, const PnlOp_Rev_t
  *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_stReversal, const CtrlLog_RelearnMode_t *
  rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const VehCom_Cmd_t
  *rtu_SWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, const SWC_HwAbsLyrBus
  *rtu_SWC_HwAbsLyrBus, BlockDet_Stall_t *rty_BlockDetBus_BlockDet_stStallType,
  uint8_T *rty_BlockDetBus_BlockDet_stStallFlt, BlockDet_FltType_t
  *rty_BlockDetBus_BlockDet_stFltType, uint16_T
  *rty_BlockDetBus_BlockDet_hStallPos, BlockDet_Stall_dir
  *rty_BlockDetBus_BlockDet_stStallDir);

/* Model reference registration function */
extern void BlockDet_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

extern void BlockDet_Mdl_ConsecutiveMotorblocksDetection_Init(boolean_T
  *rty_isDoubleStallDetected);
extern void BlockDet_Mdl_ConsecutiveMotorblocksDetection(boolean_T rtu_isBlocked,
  boolean_T rtu_isLearningPos, const uint16_T *rtu_PosMon_hCurPos, uint8_T
  rtu_hMovPosErr, uint8_T rtu_nCounterLimit, BlockDet_Stall_t
  rtu_BlockDet_stStallType, const PnlOp_Rev_t *rtu_stReversal, boolean_T
  rtu_newPulsesDetected, uint8_T rtu_resetCounter, boolean_T
  *rty_isDoubleStallDetected);
extern void BlockDet_Mdl_BlockFault_Determination_Init(void);
extern void BlockDet_Mdl_BlockFault_Determination(BlockDet_Stall_t
  rtu_stStallType, boolean_T rtu_newPulsesDetected, const uint8_T
  *rtu_uiHallCountsCyclic, const CtrlLog_RelearnMode_t *rtu_stRelearnMode,
  uint8_T rtu_hMovPosErr, const uint16_T *rtu_hCurPos, const PnlOp_Rev_t
  *rtu_stReversal, uint8_T rtu_RUN_PASS, uint8_T rtu_RUN_FAIL, uint8_T
  rtu_nCounterLimit, BlockDet_FltType_t rtu_SamePosStallFault_C,
  BlockDet_FltType_t rtu_DoubleStallFault_C, BlockDet_FltType_t rtu_NoFault_C,
  uint8_T rtu_resetCounter, uint8_T *rty_stStallFlt, BlockDet_FltType_t
  *rty_stFltType);
extern void BlockDet_Mdl_Block_Det(const CtrlLog_tenTargetDirection
  *rtu_stMtrCtrlCmd, const uint8_T *rtu_uiHallCountsCyclic, BlockDet_Stall_t
  rtu_DecStall_C, uint16_T rtu_tBlockedTime_P, BlockDet_Stall_t rtu_NoStall_C,
  BlockDet_Stall_t rtu_IncStall_C, const uint16_T *rtu_hCurPos, uint32_T
  rtu_rPusleTimeSum, const uint16_T *rtu_newSoftStop, const boolean_T
  *rtu_isLearnComplete, const VehCom_Cmd_t *rtu_ComLog_stActvMovCmd, const
  CtrlLog_RelearnMode_t *rtu_stRelearnMode, uint16_T rtu_posSoftStallThreshold,
  const uint16_T rtu_thresholdValueDetection[2], uint16_T
  rtu_hMaxPosSoftStopClose, BlockDet_Stall_dir *rty_stStallDir, uint16_T
  *rty_hStallPos, BlockDet_Stall_t *rty_stStallType, boolean_T
  *rty_newPulsesDetected);

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_BlockDet_Mdl_T BlockDet_Mdl_MdlrefDW;

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef BlockDet_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_BlockDet_Mdl_c_T BlockDet_Mdl_B;

/* Block states (default storage) */
extern DW_BlockDet_Mdl_f_T BlockDet_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_BlockDet_Mdl_T BlockDet_Mdl_PrevZCX;

#endif                                 /*BlockDet_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'BlockDet_Mdl'
 * '<S1>'   : 'BlockDet_Mdl/Model Info'
 * '<S2>'   : 'BlockDet_Mdl/blockDetection'
 * '<S3>'   : 'BlockDet_Mdl/blockDetection/BlockFault_Determination'
 * '<S4>'   : 'BlockDet_Mdl/blockDetection/Block_Det'
 * '<S5>'   : 'BlockDet_Mdl/blockDetection/DocBlock'
 * '<S6>'   : 'BlockDet_Mdl/blockDetection/NVMAccess'
 * '<S7>'   : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/Compare To Constant1'
 * '<S8>'   : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/Compare To Constant2'
 * '<S9>'   : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/Compare To Constant3'
 * '<S10>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/ConsecutiveMotorblocksDetection'
 * '<S11>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/Detect Change'
 * '<S12>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/DocBlock'
 * '<S13>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/ConsecutiveMotorblocksDetection/Detect Rise Positive2'
 * '<S14>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/ConsecutiveMotorblocksDetection/DetectDoubleStall'
 * '<S15>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/ConsecutiveMotorblocksDetection/DocBlock'
 * '<S16>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/ConsecutiveMotorblocksDetection/StallTypeCheck'
 * '<S17>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/ConsecutiveMotorblocksDetection/Detect Rise Positive2/Positive'
 * '<S18>'  : 'BlockDet_Mdl/blockDetection/BlockFault_Determination/ConsecutiveMotorblocksDetection/StallTypeCheck/DocBlock'
 * '<S19>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Chart'
 * '<S20>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant'
 * '<S21>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant1'
 * '<S22>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant2'
 * '<S23>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant3'
 * '<S24>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant4'
 * '<S25>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant5'
 * '<S26>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant6'
 * '<S27>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant7'
 * '<S28>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Compare To Constant8'
 * '<S29>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Detect Change'
 * '<S30>'  : 'BlockDet_Mdl/blockDetection/Block_Det/Detect Change1'
 * '<S31>'  : 'BlockDet_Mdl/blockDetection/Block_Det/DocBlock'
 * '<S32>'  : 'BlockDet_Mdl/blockDetection/NVMAccess/Detect Change'
 * '<S33>'  : 'BlockDet_Mdl/blockDetection/NVMAccess/Detect Change1'
 * '<S34>'  : 'BlockDet_Mdl/blockDetection/NVMAccess/DocBlock'
 * '<S35>'  : 'BlockDet_Mdl/blockDetection/NVMAccess/writeStallDirToNVM'
 * '<S36>'  : 'BlockDet_Mdl/blockDetection/NVMAccess/writeStallPosToNVM'
 * '<S37>'  : 'BlockDet_Mdl/blockDetection/NVMAccess/writeStallDirToNVM/DocBlock'
 * '<S38>'  : 'BlockDet_Mdl/blockDetection/NVMAccess/writeStallPosToNVM/DocBlock'
 */
#endif                                 /* RTW_HEADER_BlockDet_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
