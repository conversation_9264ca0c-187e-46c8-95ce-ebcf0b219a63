/*
 * File: CfgParam_Mdl.h
 *
 * Code generated for Simulink model 'CfgParam_Mdl'.
 *
 * Model version                  : 1.238
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:29:52 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_CfgParam_Mdl_h_
#define RTW_HEADER_CfgParam_Mdl_h_
#ifndef CfgParam_Mdl_COMMON_INCLUDES_
#define CfgParam_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* CfgParam_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "CfgParam_Mdl_types.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'CfgParam_Mdl' */
#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T stCALFileFltOut;             /* '<S28>/Switch' */
  uint8_T isShdnRdy;                   /* '<S23>/S-Function' */
  uint8_T Status;                      /* '<S18>/S-Function' */
  boolean_T SFunction;                 /* '<S28>/S-Function' */
  boolean_T SFunction1;                /* '<S28>/S-Function1' */
  boolean_T isCALFileVldOut;           /* '<S28>/Switch2' */
  boolean_T isWriteCALToNVM;           /* '<S28>/updateCAL' */
} B_CfgParam_Mdl_c_T;

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'CfgParam_Mdl' */
#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  boolean_T DelayInput1_DSTATE;        /* '<S21>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_l;      /* '<S16>/Delay Input1' */
} DW_CfgParam_Mdl_f_T;

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'CfgParam_Mdl' */
#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState StartupCheck_Trig_ZCE;    /* '<S5>/StartupCheck' */
} ZCE_CfgParam_Mdl_T;

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_CfgParam_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_CfgParam_Mdl_T rtm;
} MdlrefDW_CfgParam_Mdl_T;

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern CfgParam_ActiveDiagInfoBus_t CfgParam_ActiveDiagInfo;
                          /* Simulink.Signal object 'CfgParam_ActiveDiagInfo' */
extern CfgParam_DDSPackageRelBus_t CfgParam_DDSPackageRel;
                           /* Simulink.Signal object 'CfgParam_DDSPackageRel' */
extern CfgParam_HWVerInfoBus_t CfgParam_HWVerInfo;
                               /* Simulink.Signal object 'CfgParam_HWVerInfo' */
extern CfgParam_SWVerInfoBus_t CfgParam_SWVerInfo;
                               /* Simulink.Signal object 'CfgParam_SWVerInfo' */
extern uint8_T CfgParam_DiagTraceMemory[12];
                         /* Simulink.Signal object 'CfgParam_DiagTraceMemory' */
extern uint8_T CfgParam_HWSupId[2];
                                 /* Simulink.Signal object 'CfgParam_HWSupId' */
extern uint8_T CfgParam_HWVersion[2];
                                  /* Simulink.Signal object 'CfgParam_HWVersion'
                                   * Hardware version

                                     Hardware Major version constant in ASCII
                                     Hardware minor version constant in Decimal

                                   */
extern uint8_T CfgParam_MBCECUIDHWPartNum[10];
                       /* Simulink.Signal object 'CfgParam_MBCECUIDHWPartNum' */
extern uint8_T CfgParam_MtrSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_MtrSerialNum'
                                * Motor Serial Number in Decimal
                                */
extern uint8_T CfgParam_RoofPartNum[9];
                                /* Simulink.Signal object 'CfgParam_RoofPartNum'
                                 * Roof Part Number in ASCII
                                 */
extern uint8_T CfgParam_RoofSerialNum[6];
                              /* Simulink.Signal object 'CfgParam_RoofSerialNum'
                               * Roof serial number Version
                               */
extern uint8_T CfgParam_SCUPartNum[9];
                                 /* Simulink.Signal object 'CfgParam_SCUPartNum'
                                  * SCU Part Number in ASCII
                                  */
extern uint8_T CfgParam_SCUSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_SCUSerialNum'
                                * SCU Serial Number in Decimal
                                */
extern uint8_T CfgParam_SWSupId[2];
                                 /* Simulink.Signal object 'CfgParam_SWSupId' */
extern uint8_T CfgParam_SupportedConfigMech;
                     /* Simulink.Signal object 'CfgParam_SupportedConfigMech' */
extern void CfgParam_Mdl_Init(void);
extern void CfgParam_Mdl(const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, uint8_T
  *rty_CfgParamBus_CfgParam_stCALFileFlt, boolean_T
  *rty_CfgParamBus_CfgParam_isCALFileVld, boolean_T
  *rty_CfgParamBus_CfgParam_isStartupRdy, boolean_T
  *rty_CfgParamBus_CfgParam_isPosTblVld, CfgParam_TestMode_t
  *rty_CfgParamBus_CfgParam_stTestMode);

/* Model reference registration function */
extern void CfgParam_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

extern void CfgParam_Mdl_NVMWrite(boolean_T rtu_Enable, uint32_T rtu_BlockId,
  uint8_T *rty_Status);

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_CfgParam_Mdl_T CfgParam_Mdl_MdlrefDW;

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef CfgParam_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_CfgParam_Mdl_c_T CfgParam_Mdl_B;

/* Block states (default storage) */
extern DW_CfgParam_Mdl_f_T CfgParam_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_CfgParam_Mdl_T CfgParam_Mdl_PrevZCX;

#endif                                 /*CfgParam_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CfgParam_Mdl'
 * '<S1>'   : 'CfgParam_Mdl/CfgParam'
 * '<S2>'   : 'CfgParam_Mdl/Model Info'
 * '<S3>'   : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW'
 * '<S4>'   : 'CfgParam_Mdl/CfgParam/DocBlock'
 * '<S5>'   : 'CfgParam_Mdl/CfgParam/StartupCheck'
 * '<S6>'   : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess'
 * '<S7>'   : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/DocBlock'
 * '<S8>'   : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/DocBlock'
 * '<S9>'   : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL'
 * '<S10>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite'
 * '<S11>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/Compare To Zero'
 * '<S12>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/DocBlock'
 * '<S13>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_ExtData'
 * '<S14>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_data'
 * '<S15>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/DocBlock'
 * '<S16>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_ExtData/Detect Rise Positive'
 * '<S17>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_ExtData/DocBlock'
 * '<S18>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_ExtData/NVMWrite'
 * '<S19>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_ExtData/Detect Rise Positive/Positive'
 * '<S20>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_ExtData/NVMWrite/DocBlock'
 * '<S21>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_data/Detect Rise Positive'
 * '<S22>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_data/DocBlock'
 * '<S23>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_data/NVMWrite'
 * '<S24>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_data/Detect Rise Positive/Positive'
 * '<S25>'  : 'CfgParam_Mdl/CfgParam/CfgParam_NVMRW/CfgParam_NVMAccess/WriteCALAndExtCAL/CalEraseWrite/Cal_data/NVMWrite/DocBlock'
 * '<S26>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/Compare To Constant'
 * '<S27>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/DocBlock'
 * '<S28>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/StartupCheck'
 * '<S29>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/StartupCheck/ChecksumCalc'
 * '<S30>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/StartupCheck/Compare To Constant2'
 * '<S31>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/StartupCheck/DocBlock'
 * '<S32>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/StartupCheck/DocBlock1'
 * '<S33>'  : 'CfgParam_Mdl/CfgParam/StartupCheck/StartupCheck/updateCAL'
 */
#endif                                 /* RTW_HEADER_CfgParam_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
