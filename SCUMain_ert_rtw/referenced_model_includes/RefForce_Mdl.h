/*
 * File: RefForce_Mdl.h
 *
 * Code generated for Simulink model '<PERSON>f<PERSON><PERSON><PERSON>_Mdl'.
 *
 * Model version                  : 1.41
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:39:18 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_RefForce_Mdl_h_
#define RTW_HEADER_RefForce_Mdl_h_
#ifndef RefForce_Mdl_COMMON_INCLUDES_
#define RefForce_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* RefForce_Mdl_COMMON_INCLUDES_ */

#include "CtrlLogic_ExpTypes.h"
#include "RefForce_Mdl_types.h"
#include "RoofSys_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"
#include "RefField_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'RefForce_Mdl' */
#ifndef RefForce_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  int16_T referenceForce;              /* '<S5>/CalculateReferenceForce' */
} B_RefForce_Mdl_c_T;

#endif                                 /*RefForce_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'RefForce_Mdl' */
#ifndef RefForce_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  CtrlLog_tenTargetDirection prevDirection;/* '<S5>/CalculateReferenceForce' */
  int16_T internalReferenceForce;      /* '<S5>/CalculateReferenceForce' */
  uint8_T is_c3_RefForce_Mdl;          /* '<S5>/CalculateReferenceForce' */
  uint8_T prevByte;                    /* '<S5>/CalculateReferenceForce' */
  uint8_T maxByte;                     /* '<S5>/CalculateReferenceForce' */
  uint8_T is_active_c3_RefForce_Mdl;   /* '<S5>/CalculateReferenceForce' */
  uint8_T currentRefFieldArea_prev;    /* '<S5>/CalculateReferenceForce' */
  uint8_T currentRefFieldArea_start;   /* '<S5>/CalculateReferenceForce' */
} DW_RefForce_Mdl_f_T;

#endif                                 /*RefForce_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef RefForce_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_RefForce_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*RefForce_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef RefForce_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_RefForce_Mdl_T rtm;
} MdlrefDW_RefForce_Mdl_T;

#endif                                 /*RefForce_Mdl_MDLREF_HIDE_CHILD_*/

extern void RefForce_Mdl(const boolean_T
  *rtu_SWC_DataHndlLyrBus_RefFieldBus_RefField_isInsideRF, const uint16_T
  *rtu_SWC_DataHndlLyrBus_RefFieldBus_RefField_currByteOfRF, const uint8_T
  *rtu_SWC_DataHndlLyrBus_RefFieldBus_RefField_currRFArea, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const boolean_T
  *rtu_LearnAdapBus_LearnAdap_isLearnComplete, int16_T
  *rty_RefForceBus_RefForce_FreferenceForce, boolean_T
  *rty_RefForceBus_RefForce_isCalcRefForceVld, boolean_T
  *rty_RefForceBus_RefForce_isPosOutsideRefField);

/* Model reference registration function */
extern void RefForce_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef RefForce_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_RefForce_Mdl_T RefForce_Mdl_MdlrefDW;

#endif                                 /*RefForce_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef RefForce_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_RefForce_Mdl_c_T RefForce_Mdl_B;

/* Block states (default storage) */
extern DW_RefForce_Mdl_f_T RefForce_Mdl_DW;

#endif                                 /*RefForce_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S12>/DTProp1' : Unused code path elimination
 * Block '<S12>/DTProp2' : Unused code path elimination
 * Block '<S13>/DTProp1' : Unused code path elimination
 * Block '<S13>/DTProp2' : Unused code path elimination
 * Block '<S14>/Data Type Duplicate' : Unused code path elimination
 * Block '<S14>/Data Type Propagation' : Unused code path elimination
 * Block '<S12>/Modify Scaling Only' : Eliminate redundant data type conversion
 * Block '<S13>/Modify Scaling Only' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'RefForce_Mdl'
 * '<S1>'   : 'RefForce_Mdl/RoofModel'
 * '<S2>'   : 'RefForce_Mdl/RoofModel/DetermineEnableCalculations'
 * '<S3>'   : 'RefForce_Mdl/RoofModel/DetermineRefFieldAvailability'
 * '<S4>'   : 'RefForce_Mdl/RoofModel/DocBlock'
 * '<S5>'   : 'RefForce_Mdl/RoofModel/SelectByteFromReferenceField'
 * '<S6>'   : 'RefForce_Mdl/RoofModel/DetermineEnableCalculations/Compare To Constant'
 * '<S7>'   : 'RefForce_Mdl/RoofModel/DetermineEnableCalculations/Compare To Constant1'
 * '<S8>'   : 'RefForce_Mdl/RoofModel/DetermineEnableCalculations/DocBlock'
 * '<S9>'   : 'RefForce_Mdl/RoofModel/DetermineRefFieldAvailability/DocBlock'
 * '<S10>'  : 'RefForce_Mdl/RoofModel/SelectByteFromReferenceField/CalculateReferenceForce'
 * '<S11>'  : 'RefForce_Mdl/RoofModel/SelectByteFromReferenceField/DocBlock'
 * '<S12>'  : 'RefForce_Mdl/RoofModel/SelectByteFromReferenceField/Extract Bits'
 * '<S13>'  : 'RefForce_Mdl/RoofModel/SelectByteFromReferenceField/Extract Bits1'
 * '<S14>'  : 'RefForce_Mdl/RoofModel/SelectByteFromReferenceField/Saturation Dynamic'
 */
#endif                                 /* RTW_HEADER_RefForce_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
