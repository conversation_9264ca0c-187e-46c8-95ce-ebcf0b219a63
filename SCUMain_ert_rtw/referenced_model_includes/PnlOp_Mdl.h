/*
 * File: PnlOp_Mdl.h
 *
 * Code generated for Simulink model 'PnlOp_Mdl'.
 *
 * Model version                  : 1.121
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:37:55 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PnlOp_Mdl_h_
#define RTW_HEADER_PnlOp_Mdl_h_
#ifndef PnlOp_Mdl_COMMON_INCLUDES_
#define PnlOp_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* PnlOp_Mdl_COMMON_INCLUDES_ */

#include "CtrlLogic_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "PnlOp_Mdl_types.h"
#include "LearnAdp_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "StateMach_ExpTypes.h"
#include "HallDeco_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'PnlOp_Mdl' */
#ifndef PnlOp_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  int32_T speedPID_PError;             /* '<S13>/Product2' */
  int32_T speedPID_IError;             /* '<S19>/Switch1' */
  uint16_T MtrSpdCmd;                  /* '<S4>/speedZoneCalculation' */
  int16_T MtrSpdCmd_f;                 /* '<S6>/PWMofBeforeDirChange' */
  int16_T PIDDIDInitValue;             /* '<S13>/Divide' */
  int16_T speedPIDCommand;             /* '<S13>/Data Type Conversion4' */
  int16_T MtrSpeedCommand;             /* '<S10>/Switch2' */
  int16_T speedError;                  /* '<S13>/Sum' */
} B_PnlOp_Mdl_c_T;

#endif                                 /*PnlOp_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'PnlOp_Mdl' */
#ifndef PnlOp_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  CtrlLog_tenTargetDirection MtrCmd_prev;/* '<S6>/PWMofBeforeDirChange' */
  CtrlLog_tenTargetDirection MtrCmd_start;/* '<S6>/PWMofBeforeDirChange' */
  CtrlLog_tenTargetDirection MtrCmd_prev_h;/* '<S4>/speedZoneCalculation' */
  CtrlLog_tenTargetDirection MtrCmd_start_m;/* '<S4>/speedZoneCalculation' */
  uint8_T is_c4_PnlOp_Mdl;             /* '<S6>/PWMofBeforeDirChange' */
  uint8_T is_MotorMoving;              /* '<S6>/PWMofBeforeDirChange' */
  uint8_T is_RampUpPWM;                /* '<S6>/PWMofBeforeDirChange' */
  uint8_T is_RampDownPWM;              /* '<S6>/PWMofBeforeDirChange' */
  uint8_T counter;                     /* '<S6>/PWMofBeforeDirChange' */
  uint8_T startMtrSpdCmd;              /* '<S6>/PWMofBeforeDirChange' */
  uint8_T is_active_c4_PnlOp_Mdl;      /* '<S6>/PWMofBeforeDirChange' */
  uint8_T is_c1_PnlOp_Mdl;             /* '<S4>/speedZoneCalculation' */
  uint8_T is_MotorMoving_i;            /* '<S4>/speedZoneCalculation' */
  uint8_T is_active_c1_PnlOp_Mdl;      /* '<S4>/speedZoneCalculation' */
} DW_PnlOp_Mdl_f_T;

#endif                                 /*PnlOp_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PnlOp_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_PnlOp_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*PnlOp_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PnlOp_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_PnlOp_Mdl_T rtm;
} MdlrefDW_PnlOp_Mdl_T;

#endif                                 /*PnlOp_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T PIDDID_SoftStartPoint1Time;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Time' */
extern uint16_T PIDDID_SoftStopPoint1Time;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Time' */
extern uint8_T PIDDID_SoftStartPoint1Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Perc' */
extern uint8_T PIDDID_SoftStartPoint2Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint2Perc' */
extern uint8_T PIDDID_SoftStopPoint1Perc;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Perc' */
extern boolean_T PIDDID_isFactoryMode;
                                /* Simulink.Signal object 'PIDDID_isFactoryMode'
                                 * Diagnostic Factory Mode
                                   1 - Enabled
                                   0 -Disabled

                                 */
extern boolean_T PIDDID_isSoftStartEna;
                            /* Simulink.Signal object 'PIDDID_isSoftStartEna' */
extern boolean_T PIDDID_isSoftStopEna;
                             /* Simulink.Signal object 'PIDDID_isSoftStopEna' */
extern void PnlOp_Mdl(const boolean_T
                      *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete,
                      const uint16_T
                      *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const
                      int16_T
                      *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned,
                      const uint16_T
                      *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn,
                      const CtrlLog_MovType_t *rtu_CtrlLogBus_CtrlLog_stMovType,
                      const int16_T *rtu_CtrlLogBus_CtrlLog_hReactTgtPos, const
                      CtrlLog_RelearnMode_t
                      *rtu_CtrlLogBus_CtrlLog_stRelearnMode, const
                      CtrlLog_tenTargetDirection
                      *rtu_CtrlLogBus_CtrlLog_stDirCommand, const uint16_T
                      *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd,
                      PnlOp_MtrCmd_t *rty_PnlOpBus_PnlOp_stMtrCtrlCmd, boolean_T
                      *rty_PnlOpBus_PnlOp_isMtrMoving, PnlOp_Mode_t
                      *rty_PnlOpBus_PnlOp_stNormalMov, PnlOp_Rev_t
                      *rty_PnlOpBus_PnlOp_stReversal, PnlOp_ATS_t
                      *rty_PnlOpBus_PnlOp_stATS, int16_T
                      *rty_PnlOpBus_PnlOp_pMtrSpdCmd, uint16_T
                      *rty_PnlOpBus_PnlOp_hTrgtPosPtcd);

/* Model reference registration function */
extern void PnlOp_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef PnlOp_Mdl_MDLREF_HIDE_CHILD_

extern void PnlOp_Mdl_rampCalc(uint16_T rtu_PWMValIn, uint16_T rtu_Factor,
  uint16_T rtu_Step, boolean_T rtu_increase, uint16_T *rty_PWMvalOut);
extern void PnlOp_Mdl_calcRamp(uint16_T rtu_curTime, uint8_T rtu_goal, uint8_T
  rtu_start, uint16_T rtu_duration, uint8_T *rty_percentage);
extern void PnlOp_Mdl_MtrSpeedCmd_Calc(const CtrlLog_tenTargetDirection
  *rtu_stDirCommand, const uint16_T *rtu_u12VBatt, uint16_T
  rtu_MtrSpeedZone1Open, uint16_T rtu_MtrSpeedZone1Close, uint16_T
  rtu_MtrSpeedZone2Open, uint16_T rtu_MtrSpeedZone2Close, uint8_T
  rtu_MtrSpeedZoneRampOpen, uint8_T rtu_MtrSpeedZoneRampClose, uint16_T
  rtu_MtrSpeedZonePositionOpen, uint16_T rtu_MtrSpeedZonePositionClose,
  boolean_T rtu_PIDDID_isFactoryMode, uint16_T
  rtu_BlockDet_posSoftStallThreshold, uint16_T rtu_CloseLoopSpeedPID_P, uint16_T
  rtu_CloseLoopSpeedPID_I, const int16_T *rtu_PosMon_hCurPosSigned, const
  CtrlLog_MovType_t *rtu_stMovType, const uint16_T *rtu_HallDeco_rMtrSpd,
  int16_T rtu_MtrSpdCmd, const uint16_T *rtu_hSoftStopOpn, const
  CtrlLog_RelearnMode_t *rtu_CtrlLog_stRelearnMode, const boolean_T
  *rtu_LearnAdap_isLearnComplete, int16_T *rty_pMtrSpdCmd);

#endif                                 /*PnlOp_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PnlOp_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_PnlOp_Mdl_T PnlOp_Mdl_MdlrefDW;

#endif                                 /*PnlOp_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PnlOp_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_PnlOp_Mdl_c_T PnlOp_Mdl_B;

/* Block states (default storage) */
extern DW_PnlOp_Mdl_f_T PnlOp_Mdl_DW;

#endif                                 /*PnlOp_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S10>/Data Type Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PnlOp_Mdl'
 * '<S1>'   : 'PnlOp_Mdl/Model Info'
 * '<S2>'   : 'PnlOp_Mdl/PnlOp_Mdl'
 * '<S3>'   : 'PnlOp_Mdl/PnlOp_Mdl/DocBlock'
 * '<S4>'   : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc'
 * '<S5>'   : 'PnlOp_Mdl/PnlOp_Mdl/PWMOverride'
 * '<S6>'   : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange'
 * '<S7>'   : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/DocBlock1'
 * '<S8>'   : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/setParametersForCurrentDirection'
 * '<S9>'   : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/speedZoneCalculation'
 * '<S10>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation'
 * '<S11>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/setParametersForCurrentDirection/Compare To Constant2'
 * '<S12>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/speedZoneCalculation/rampCalc'
 * '<S13>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/ClosedLoopPIDController'
 * '<S14>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/Compare To Constant'
 * '<S15>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/Compare To Constant3'
 * '<S16>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/Compare To Constant4'
 * '<S17>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/ClosedLoopPIDController/Compare To Constant'
 * '<S18>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/ClosedLoopPIDController/Compare To Constant1'
 * '<S19>'  : 'PnlOp_Mdl/PnlOp_Mdl/MtrSpeedCmd_Calc/voltageCompensation/ClosedLoopPIDController/integrator'
 * '<S20>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Cal_Soft_startandstop_param'
 * '<S21>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Compare To Constant'
 * '<S22>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/DocBlock1'
 * '<S23>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/PWMofBeforeDirChange'
 * '<S24>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Subsystem'
 * '<S25>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/PWMofBeforeDirChange/calcRamp'
 * '<S26>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Subsystem/Compare To Constant'
 * '<S27>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Subsystem/Compare To Constant1'
 * '<S28>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Subsystem/Compare To Constant2'
 * '<S29>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Subsystem/Compare To Zero'
 * '<S30>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Subsystem/Compare To Zero1'
 * '<S31>'  : 'PnlOp_Mdl/PnlOp_Mdl/PWMofBeforeDirChange/Subsystem/Compare To Zero2'
 */
#endif                                 /* RTW_HEADER_PnlOp_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
