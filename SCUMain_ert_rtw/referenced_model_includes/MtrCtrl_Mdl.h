/*
 * File: MtrCtrl_Mdl.h
 *
 * Code generated for Simulink model 'MtrCtrl_Mdl'.
 *
 * Model version                  : 1.236
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:34:06 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_MtrCtrl_Mdl_h_
#define RTW_HEADER_MtrCtrl_Mdl_h_
#ifndef MtrCtrl_Mdl_COMMON_INCLUDES_
#define MtrCtrl_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* MtrCtrl_Mdl_COMMON_INCLUDES_ */

#include "RoofSys_CommDefines.h"
#include "CtrlLogic_ExpTypes.h"
#include "MtrCtrl_Mdl_types.h"
#include "HallDeco_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofSys_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "DTC_Exp.h"
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block states (default storage) for system '<S33>/MovingAvgFilter' */
#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T TappedDelay_X[3];           /* '<S36>/Tapped Delay' */
} DW_CoreSubsys_MtrCtrl_Mdl_T;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/* Block signals for model 'MtrCtrl_Mdl' */
#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  int16_T PwmVal;                      /* '<S6>/PWMofBeforeDirChange' */
  MtrCtrl_MtrDir_t MtrOutDir;          /* '<S6>/PWMofBeforeDirChange' */
} B_MtrCtrl_Mdl_c_T;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'MtrCtrl_Mdl' */
#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  CtrlLog_tenTargetDirection MtrCmd_prev;/* '<S6>/PWMofBeforeDirChange' */
  CtrlLog_tenTargetDirection MtrCmd_start;/* '<S6>/PWMofBeforeDirChange' */
  uint8_T UnitDelay1_DSTATE;           /* '<S11>/Unit Delay1' */
  uint8_T UnitDelay_DSTATE;            /* '<S27>/UnitDelay' */
  uint8_T UnitDelay_DSTATE_b;          /* '<S18>/UnitDelay' */
  boolean_T DelayInput1_DSTATE;        /* '<S20>/Delay Input1' */
  MtrCtrl_MtrPwrd_t DelayInput1_DSTATE_g;/* '<S30>/Delay Input1' */
  MtrCtrl_MtrPwrd_t UnitDelay2_DSTATE; /* '<S13>/Unit Delay2' */
  uint8_T is_c3_MtrCtrl_Mdl;           /* '<S6>/PWMofBeforeDirChange' */
  uint8_T is_active_c3_MtrCtrl_Mdl;    /* '<S6>/PWMofBeforeDirChange' */
  DW_CoreSubsys_MtrCtrl_Mdl_T CoreSubsys[2];/* '<S33>/MovingAvgFilter' */
} DW_MtrCtrl_Mdl_f_T;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for system '<S33>/MovingAvgFilter' */
#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const uint8_T Width;                 /* '<S36>/Width' */
} ConstB_CoreSubsys_MtrCtrl_Mdl_T;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'MtrCtrl_Mdl' */
#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ConstB_CoreSubsys_MtrCtrl_Mdl_T CoreSubsys;/* '<S33>/MovingAvgFilter' */
} ConstB_MtrCtrl_Mdl_h_T;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_MtrCtrl_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_MtrCtrl_Mdl_T rtm;
} MdlrefDW_MtrCtrl_Mdl_T;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

extern void MtrCtrl_Mdl_Init(void);
extern void MtrCtrl_Mdl(const uint16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1AVolt_mv, const uint16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1BVolt_mv, const int16_T
  *rtu_PnlOpBus_PnlOp_pMtrSpdCmd, const CtrlLog_tenTargetDirection
  *rtu_CtrlLogBus_CtrlLog_stDirCommand, MtrCtrlBus *rty_MtrCtrlBus);

/* Model reference registration function */
extern void MtrCtrl_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

extern void MtrCtrl_Mdl_MotorControl_Init(MtrCtrl_MtrDir_t *rty_IoHwAb_SetMtrDir,
  int16_T *rty_MtrCtrl_pMtrPwmCmd);
extern void MtrCtrl_Mdl_MotorControl(const CtrlLog_tenTargetDirection
  *rtu_PnlOp_stMtrCtrlCmd, const int16_T *rtu_PnlOp_pMtrSpdCmd, boolean_T
  rtu_Config_typeMtrDir_P, MtrCtrl_MtrDir_t *rty_IoHwAb_SetMtrDir, int16_T
  *rty_MtrCtrl_pMtrPwmCmd);
extern void MtrCtrl_Mdl_debounceErrorActive100ms(boolean_T rtu_MotorfaultActive,
  boolean_T *rty_isDebounceFinished);
extern void MtrCtrl_Mdl_MotorDirectionFaultDetection_Init(void);
extern void MtrCtrl_Mdl_MotorDirectionFaultDetection(MtrCtrl_MtrDir_t
  rtu_motorDirection, const boolean_T rtu_Feedback[2], uint8_T *rty_DTC_MTR_Dir,
  MtrCtrl_MtrPwrd_t *rty_MtrPwrdState);
extern void MtrCtrl_Mdl_MotorTerminalValue(const uint16_T
  *rtu_ADCMon_uM1AVolt_mV, const uint16_T *rtu_ADCMon_uM1BVolt_mV, boolean_T
  rty_MotorTerminal[2]);

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_MtrCtrl_Mdl_T MtrCtrl_Mdl_MdlrefDW;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrCtrl_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_MtrCtrl_Mdl_c_T MtrCtrl_Mdl_B;

/* Block states (default storage) */
extern DW_MtrCtrl_Mdl_f_T MtrCtrl_Mdl_DW;

#endif                                 /*MtrCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<Root>/Constant1' : Unused code path elimination
 * Block '<S4>/Compare' : Unused code path elimination
 * Block '<S4>/Constant' : Unused code path elimination
 * Block '<S19>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S19>/FixPt Data Type Propagation' : Unused code path elimination
 * Block '<S29>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S29>/FixPt Data Type Propagation' : Unused code path elimination
 * Block '<S19>/FixPt Gateway In' : Eliminate redundant data type conversion
 * Block '<S29>/FixPt Gateway In' : Eliminate redundant data type conversion
 * Block '<S13>/Logical Operator9' : Eliminated due to no operation
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'MtrCtrl_Mdl'
 * '<S1>'   : 'MtrCtrl_Mdl/DocBlock'
 * '<S2>'   : 'MtrCtrl_Mdl/Model Info'
 * '<S3>'   : 'MtrCtrl_Mdl/MotorControl'
 * '<S4>'   : 'MtrCtrl_Mdl/MotorControl/Compare To Constant'
 * '<S5>'   : 'MtrCtrl_Mdl/MotorControl/DocBlock'
 * '<S6>'   : 'MtrCtrl_Mdl/MotorControl/MotorControl'
 * '<S7>'   : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection'
 * '<S8>'   : 'MtrCtrl_Mdl/MotorControl/MotorTerminalValue'
 * '<S9>'   : 'MtrCtrl_Mdl/MotorControl/MotorControl/DocBlock'
 * '<S10>'  : 'MtrCtrl_Mdl/MotorControl/MotorControl/PWMofBeforeDirChange'
 * '<S11>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC'
 * '<S12>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirectionValidity'
 * '<S13>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrPwrdState'
 * '<S14>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/DocBlock'
 * '<S15>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC/Compare To Constant1'
 * '<S16>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC/Compare To Constant2'
 * '<S17>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC/DocBlock'
 * '<S18>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC/debounceErrorActive100ms'
 * '<S19>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC/debounceErrorActive100ms/AddOneTick'
 * '<S20>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC/debounceErrorActive100ms/Detect Change'
 * '<S21>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirDTC/debounceErrorActive100ms/DocBlock'
 * '<S22>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirectionValidity/Compare To Constant'
 * '<S23>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirectionValidity/Compare To Constant1'
 * '<S24>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirectionValidity/Compare To Constant2'
 * '<S25>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrDirectionValidity/DocBlock'
 * '<S26>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrPwrdState/Compare To Constant3'
 * '<S27>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrPwrdState/DebounceMtrDepwrd500ms'
 * '<S28>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrPwrdState/DocBlock'
 * '<S29>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrPwrdState/DebounceMtrDepwrd500ms/AddOneTick'
 * '<S30>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrPwrdState/DebounceMtrDepwrd500ms/Detect Change'
 * '<S31>'  : 'MtrCtrl_Mdl/MotorControl/MotorDirectionFaultDetection/CalculateMtrPwrdState/DebounceMtrDepwrd500ms/DocBlock'
 * '<S32>'  : 'MtrCtrl_Mdl/MotorControl/MotorTerminalValue/DocBlock'
 * '<S33>'  : 'MtrCtrl_Mdl/MotorControl/MotorTerminalValue/MotorTerminalValue'
 * '<S34>'  : 'MtrCtrl_Mdl/MotorControl/MotorTerminalValue/MotorTerminalValue/Compare To Constant'
 * '<S35>'  : 'MtrCtrl_Mdl/MotorControl/MotorTerminalValue/MotorTerminalValue/DocBlock'
 * '<S36>'  : 'MtrCtrl_Mdl/MotorControl/MotorTerminalValue/MotorTerminalValue/MovingAvgFilter'
 * '<S37>'  : 'MtrCtrl_Mdl/MotorControl/MotorTerminalValue/MotorTerminalValue/MovingAvgFilter/DocBlock'
 */
#endif                                 /* RTW_HEADER_MtrCtrl_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
