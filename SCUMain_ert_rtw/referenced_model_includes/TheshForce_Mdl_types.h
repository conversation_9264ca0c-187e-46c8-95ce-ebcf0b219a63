/*
 * File: TheshForce_Mdl_types.h
 *
 * Code generated for Simulink model 'TheshForce_Mdl'.
 *
 * Model version                  : 1.94
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:41:39 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TheshForce_Mdl_types_h_
#define RTW_HEADER_TheshForce_Mdl_types_h_
#include "CtrlLogic_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "VehComBus.h"
#include "SWC_CommLyrBus.h"
#include "ThermProt_ExpTypes.h"
#include "rtwtypes.h"
#include "RoofSys_CommDefines.h"
#include "RoofOper_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "MtrMdl_ExpTypes.h"
#include "CfgParam_TThermThreshBus_t.h"
#include "CfgParam_TThermThreshBus_MosfetLevels.h"
#ifndef DEFINED_TYPEDEF_FOR_ThermProtBus_
#define DEFINED_TYPEDEF_FOR_ThermProtBus_

/* Bus object for Thermal Protection signals */
typedef struct {
  /* Motor Temperature Class Status(Rollo Motor) */
  ThermProt_MtrTempClass_t ThermProt_stMtrTempClass;

  /* Motor Temperature Standup ready(Rollo Motor) */
  boolean_T ThermProt_isStartupRdy;

  /* Mosfet Temperature Class Status(Rollo Motor) */
  ThermProt_MosfetTempClass_t ThermProt_stMosfetTempClass;
  boolean_T ThermProt_isSleepAllowed;
} ThermProtBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_MtrCtrlBus_
#define DEFINED_TYPEDEF_FOR_MtrCtrlBus_

/* Bus object for Motor Control signals */
typedef struct {
  /* Motor Direction Control signal (M1/M2)
     RoofSys_MtrDir_t.IDLE_C: Idle
     RoofSys_MtrDir_t.CW_C: CW
     RoofSys_MtrDir_t.CCW_C: CCW
     RoofSys_MtrDir_t.SECURE_C: Secure */
  MtrCtrl_MtrDir_t IoHwAb_SetMtrDir;

  /* Motor Direction DTC Status (Glass and Rollo)
     0: Not Run
     1: Run and Pass
     2: Run and Fail */
  uint8_T DTC_MTR_Dir;

  /* Motor Duty Cycle Control signal (M1/M2)
     Applied percentage value (0-100% = 0 - 100) */
  int16_T IoHwAb_SetMtrDutyCycle;
  uint16_T MtrCtrl_rMtrSpdTarget;
  int16_T MtrCtrl_rClsLpFactor;
  int16_T MtrCtrl_rOpnLpFactor;

  /* MotorSpeed Error */
  int32_T MtrCtrl_rMtrSpdError;

  /* Motor debounced powered state */
  MtrCtrl_MtrPwrd_t MtrCtrl_MtrPwrdState;
  boolean_T MtrCtrl_FeedbackStatus[2];
} MtrCtrlBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_PnlOpBus_
#define DEFINED_TYPEDEF_FOR_PnlOpBus_

/* Bus object for Roof Operation signals */
typedef struct {
  /* Motor Control Command from Sunroof Operation (Glass Motor Command and Rollo Motor Command)
     RoofOper_stMtrNoCmd_C      ----> 0: No Motor Command
     RoofOper_stMtrIncCmd_C      ---->1: Increasing
     RoofOper_stMtDecCmd_C      ----> 2: Decreasing */
  PnlOp_MtrCmd_t PnlOp_stMtrCtrlCmd;

  /* Sunroof Moving Status including reversal moving (Glass and Rollo)
     0: Motor Not Moving
     1: Motor Moving */
  boolean_T PnlOp_isMtrMoving;

  /* Sunroof Normal Moving Status (Glass and Rollo) */
  PnlOp_Mode_t PnlOp_stNormalMov;

  /* Sunroof Anti Pinch Reversal Status (glass and rollo) */
  PnlOp_Rev_t PnlOp_stReversal;

  /* ATS states from Sunroof Operation (Glass ATS state and Rollo ATS state)
     '0: Deactivated'
     '1: Available'
     '2: Active'
     '3: Override Stage 1'
     '4: Override Stage 2'
     '5: Disabled' */
  PnlOp_ATS_t PnlOp_stATS;

  /* Motor speed control command (Glass and Rollo) */
  int16_T PnlOp_pMtrSpdCmd;

  /* Motor target position */
  uint16_T PnlOp_hTrgtPosPtcd;
} PnlOpBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SysFltDiag_LastStopReason_t_
#define DEFINED_TYPEDEF_FOR_SysFltDiag_LastStopReason_t_

typedef uint8_T SysFltDiag_LastStopReason_t;

/* enum SysFltDiag_LastStopReason_t */
#define NoFault_C                      ((SysFltDiag_LastStopReason_t)0U) /* Default value */
#define Debugger_C                     ((SysFltDiag_LastStopReason_t)1U)
#define DiagReq_C                      ((SysFltDiag_LastStopReason_t)2U)
#define PanicStp_C                     ((SysFltDiag_LastStopReason_t)3U)
#define ThermPrtMtr_C                  ((SysFltDiag_LastStopReason_t)4U)
#define ThermPrtMosfet_C               ((SysFltDiag_LastStopReason_t)5U)
#define RelaxMechComplt_C              ((SysFltDiag_LastStopReason_t)6U)
#define AtsRevComplt_C                 ((SysFltDiag_LastStopReason_t)7U)
#define MovTimeout_C                   ((SysFltDiag_LastStopReason_t)8U)
#define ClassEOV_C                     ((SysFltDiag_LastStopReason_t)16U)
#define ClassEUV_C                     ((SysFltDiag_LastStopReason_t)17U)
#define BattVltNonPlsbl_C              ((SysFltDiag_LastStopReason_t)18U)
#define AmbTmpNonPlsbl_C               ((SysFltDiag_LastStopReason_t)19U)
#define MosfetTempNonPlsbl_C           ((SysFltDiag_LastStopReason_t)20U)
#define SysIC_CommFlt_C                ((SysFltDiag_LastStopReason_t)21U)
#define LINCommFlt_C                   ((SysFltDiag_LastStopReason_t)22U)
#define SysICFailSafe_C                ((SysFltDiag_LastStopReason_t)23U)
#define ChargePumpFlt_C                ((SysFltDiag_LastStopReason_t)24U)
#define HSSuppFlt_C                    ((SysFltDiag_LastStopReason_t)25U)
#define VsIntFlt_C                     ((SysFltDiag_LastStopReason_t)26U)
#define VsSuppFlt_C                    ((SysFltDiag_LastStopReason_t)27U)
#define VCC1Flt_C                      ((SysFltDiag_LastStopReason_t)28U)
#define RPInvalidFlt_C                 ((SysFltDiag_LastStopReason_t)29U)
#define HallFlt_C                      ((SysFltDiag_LastStopReason_t)30U)
#define SysICThermShdFlt_C             ((SysFltDiag_LastStopReason_t)31U)
#define MtrCtrlFlt_C                   ((SysFltDiag_LastStopReason_t)35U)
#define HallSuppFlt_C                  ((SysFltDiag_LastStopReason_t)37U)
#define UnderVotlage_C                 ((SysFltDiag_LastStopReason_t)48U)
#define OverVoltage_C                  ((SysFltDiag_LastStopReason_t)49U)
#define StallDurAtsRev_RlxMech_C       ((SysFltDiag_LastStopReason_t)50U)
#define OutsideEnvCond_C               ((SysFltDiag_LastStopReason_t)51U)
#define TargetPosRchd_C                ((SysFltDiag_LastStopReason_t)52U)
#define PrevReason_C                   (MAX_uint8_T)
#endif

#ifndef DEFINED_TYPEDEF_FOR_CtrlLogBus_
#define DEFINED_TYPEDEF_FOR_CtrlLogBus_

/* Bus object for Control Logic signals */
typedef struct {
  /* Reaction Command (Glass and Rollo) */
  CtrlLog_MovType_t CtrlLog_stMovType;
  int16_T CtrlLog_hReactTgtPos;

  /* Control Logic Relearn Mode */
  CtrlLog_RelearnMode_t CtrlLog_stRelearnMode;

  /* CtrlLogic learning interruption reasons
     Bit0(1/0): Learning request becomes inactive or not
     Bit1(1/0): Block detected or not
     Bit2(1/0): Maximum learning time is passed or not
     Bit3(1/0): Position lost or not
     Bit4(1/0): Is Emergency Mode Active or not
     Bit5(1/0): Is Auth Disabled & Not in Factory Mode & Learn by Switch or not
     Bit6(1/0): Is Power State not ON & Not in Factory Mode & Learn by Switch or not
     Bit7(1/0): Is Fault Active or not */
  SysFltDiag_LastStopReason_t CtrlLog_stLastStopReason;

  /* MovIdle Status
     When false: CtrlLogic is in the progress of executing single- or multi-step movements.
     When true: CtrlLogic has completed all movement steps/initial value */
  boolean_T CtrlLog_isMovIdle;

  /* PnlMovIdle Status [glass,rollo]
     When false: CtrlLogic is in the process of executing a panel movement.
     When true: CtrlLogic is not executing a panel movement. */
  boolean_T CtrlLog_isPnlMovIdle;
  CtrlLog_tenTargetDirection CtrlLog_stDirCommand;
} CtrlLogBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ComLogBus_
#define DEFINED_TYPEDEF_FOR_ComLogBus_

/* Bus object for Command Logic signals */
typedef struct {
  /* Active movement command (Glass and Rollo) */
  VehCom_Cmd_t ComLog_stActvMovCmd;
} ComLogBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SWC_OperLogLyrBus_
#define DEFINED_TYPEDEF_FOR_SWC_OperLogLyrBus_

typedef struct {
  ThermProtBus ThermProtBus;
  MtrCtrlBus MtrCtrlBus;
  PnlOpBus PnlOpBus;
  CtrlLogBus CtrlLogBus;
  ComLogBus ComLogBus;
} SWC_OperLogLyrBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_VoltMonBus_
#define DEFINED_TYPEDEF_FOR_VoltMonBus_

/* Bus object for Voltage Monitoring Signals */
typedef struct {
  /* 12v Battery voltage */
  uint16_T VoltMon_u12VBatt;

  /* 12v Battery voltage Validity Status
     0: Invalid
     1: Valid */
  boolean_T VoltMon_is12VBattVld;

  /* Battery voltage class status */
  VoltMon_VoltClass_t VoltMon_stVoltClass;

  /* Battery voltage drop status
     0: No voltage drop detected
     1: Voltage drop detected */
  boolean_T VoltMon_isVoltDrop;

  /* Battery voltage fluctuation status
     0: No voltage fluctuation detected
     1: Voltage fluctuation detected */
  boolean_T VoltMon_isFlctnDet;

  /* Config Startup Ready Status
     0: Not ready
     1: Ready */
  boolean_T VoltMon_isStartUpRdy;

  /* VBatt Voltage DTC Status
     0: Not Run
     1: Run and Pass
     2: Run and Fail */
  uint8_T DTC_VOLT_VBatt;

  /* VPullup Voltage DTC Status
     0: Not Run
     1: Run and Pass
     2: Run and Fail */
  uint8_T DTC_VOLT_Pullup;

  /* Battery voltage lost detection */
  boolean_T VoltMon_isPwrLost;

  /* VoltMon_isUnderVtgDTCFlgSet: if 1 = Set DTC ; 0 = clear DTC */
  boolean_T VoltMon_isUnderVtgFlgSet;

  /* VoltMon_isOverVtgDTCFlgSet: if 1 = Set DTC ; 0 = clear DTC */
  boolean_T VoltMon_isOverVtgFlgSet;
} VoltMonBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_PosMonBus_
#define DEFINED_TYPEDEF_FOR_PosMonBus_

/* Bus object for Position Monitoring signals */
typedef struct {
  boolean_T PosMon_isCurPosVld;
  boolean_T PosMon_isCurPosVldNvm;
  int16_T PosMon_hCurPosSigned;
  boolean_T PosMon_isRelearn;

  /* Position saving status (Glass and Rollo)
     stdReturn_t:

     '0: OK'
     '1: Not OK'
     '2: Pending' */
  int16_T PosMon_stPosSave;

  /* Current Position (Glass and Rollo) */
  uint16_T PosMon_hCurPos;

  /* Current position area (Glass and Rollo) */
  PosMon_Area_t PosMon_curPanelArea;

  /* Current position area (Glass and Rollo) */
  PosMon_PnlPosArea_t PosMon_panelCurPosArea;

  /* 0: Not ready
     1: Ready */
  boolean_T PosMon_isShdnRdy;

  /* Position out of range (Glass and Rollo)
     0: Invalid
     1: Valid */
  boolean_T PosMon_isOutOfRange;

  /* Position Monitoring Startup Ready Status
     0: Not ready
     1: Ready */
  boolean_T PosMon_isStartupRdy;

  /* Diameter of the helix pulley in relation to the full close diameter */
  uint16_T PosMon_relativePinionSize;

  /* Position table which includes the correct open positions */
  CfgParam_hPosBus_t PosMon_hPosTbl;
} PosMonBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_MOSFETTempBus_
#define DEFINED_TYPEDEF_FOR_MOSFETTempBus_

/* Bus object for Temperature Monitoring signals */
typedef struct {
  /* MOSFET Temperature */
  int16_T MOSFETTempMon_TMosTemp;

  /* MOSFET Temperature Validity Status
     0: Invalid
     1: Valid */
  boolean_T MOSFETTempMon_isMosTempVld;

  /* MOSFET Temperature Sensor Fault Status
     0: Not Run
     1: Run and Pass
     2: Run and Fail */
  uint8_T MOSFETTempMon_stMosTempSensFlt;
} MOSFETTempBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AmbTempMonBus_
#define DEFINED_TYPEDEF_FOR_AmbTempMonBus_

/* Bus object for Temperature Monitoring signals */
typedef struct {
  /* Ambient Temperature */
  int16_T AmbTempMon_TAmbTemp;

  /* Ambient Temperature Validity Status
     0: Invalid
     1: Valid */
  boolean_T AmbTempMon_isAmbTempVld;

  /* Ambient Temperature Sensor Fault Status
     0: Not Run
     1: Run and Pass
     2: Run and Fail */
  uint8_T AmbTempMon_stAmbTempSensFlt;
} AmbTempMonBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SWC_SigMonLyrBus_
#define DEFINED_TYPEDEF_FOR_SWC_SigMonLyrBus_

typedef struct {
  VoltMonBus VoltMonBus;
  PosMonBus PosMonBus;
  MOSFETTempBus MOSFETTempBus;
  AmbTempMonBus AmbTempMonBus;
} SWC_SigMonLyrBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_MtrMdlBus_
#define DEFINED_TYPEDEF_FOR_MtrMdlBus_

/* Bus object for Motor Model  signals */
typedef struct {
  MtrMdl_CalcForTorqBus_t MtrMdl_stCalcForTorq;

  /* Estimated Motor Temperature (Rollo) */
  int16_T MtrMdl_TEstMtrTemp;

  /* Estimated Case Temperature (Rollo) */
  int16_T MtrMdl_TEstCaseTemp;
  boolean_T MtrMdl_isMotorInStartUp;
  boolean_T MtrMdl_isStartupRdy;

  /* Calculated Force and Torque Validity (Rollo)
     0: Invalid
     1: Valid */
  boolean_T MtrMdl_isCalcForTorqVld;

  /* Estimated Motor Temperature Validity (Rollo)
     0: Invalid
     1: Valid */
  boolean_T MtrMdl_isEstMtrTempVld;
  int16_T MtrMdl_MbrakeTorque;
} MtrMdlBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ThresForceBus_
#define DEFINED_TYPEDEF_FOR_ThresForceBus_

typedef struct {
  /* ATS threshold force */
  uint16_T ThresForce_FatsThreshold;
} ThresForceBus;

#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_TheshForce_Mdl_T RT_MODEL_TheshForce_Mdl_T;

#endif                                 /* RTW_HEADER_TheshForce_Mdl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
