/*
 * File: ThermProt_Mdl.h
 *
 * Code generated for Simulink model 'ThermProt_Mdl'.
 *
 * Model version                  : 1.360
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:41:19 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ThermProt_Mdl_h_
#define RTW_HEADER_ThermProt_Mdl_h_
#ifndef ThermProt_Mdl_COMMON_INCLUDES_
#define ThermProt_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* ThermProt_Mdl_COMMON_INCLUDES_ */

#include "ThermProt_ExpTypes.h"
#include "CfgParam_TThermThreshBus_MosfetLevels.h"
#include "StateMach_ExpTypes.h"
#include "CfgParam_TThermThreshBus_t.h"
#include "ThermProt_Mdl_types.h"
#include "LearnAdp_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"
#include "ThermProt_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'ThermProt_Mdl' */
#ifndef ThermProt_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  boolean_T ThermProt_isStartupRdy;   /* '<S6>/ThermalProtectionStateMachine' */
} B_ThermProt_Mdl_c_T;

#endif                                 /*ThermProt_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'ThermProt_Mdl' */
#ifndef ThermProt_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T is_c1_ThermProt_Mdl;        /* '<S6>/ThermalProtectionStateMachine' */
  uint8_T is_active_c1_ThermProt_Mdl; /* '<S6>/ThermalProtectionStateMachine' */
  uint8_T is_c3_ThermProt_Mdl;  /* '<S5>/ThermalProtectionStateMachineMosfet' */
  uint8_T is_active_c3_ThermProt_Mdl;
                                /* '<S5>/ThermalProtectionStateMachineMosfet' */
} DW_ThermProt_Mdl_f_T;

#endif                                 /*ThermProt_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ThermProt_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_ThermProt_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*ThermProt_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ThermProt_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_ThermProt_Mdl_T rtm;
} MdlrefDW_ThermProt_Mdl_T;

#endif                                 /*ThermProt_Mdl_MDLREF_HIDE_CHILD_*/

extern void ThermProt_Mdl_Init(ThermProtBus *rty_ThermProtBus);
extern void ThermProt_Mdl(const int16_T
  *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_TEstMtrTemp, const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, const int16_T
  *rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp, const int16_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, ThermProtBus
  *rty_ThermProtBus);

/* Model reference registration function */
extern void ThermProt_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef ThermProt_Mdl_MDLREF_HIDE_CHILD_

extern void ThermProt_Mdl_MtrTempClassOverride(ThermProt_MtrTempClass_t
  rtu_stFltTypeIn, boolean_T rtu_isOverride, ThermProt_MtrTempClass_t
  rtu_stMtrTempClass, ThermProt_MtrTempClass_t *rty_ThermProt_stMtrTempClass);
extern void ThermProt_Mdl_ThermProtMosfetTempClass_Init
  (ThermProt_MosfetTempClass_t *rty_ThermProt_stMosfetTempClass);
extern void ThermProt_Mdl_ThermProtMosfetTempClass(const int16_T
  *rtu_s16MosfetTemp, const CfgParam_TThermThreshBus_MosfetLevels
  *rtu_stThresholds_Param_Mosfet, const StateMach_Mode_t *rtu_stSysMode,
  ThermProt_MosfetTempClass_t *rty_ThermProt_stMosfetTempClass);
extern void ThermProt_Mdl_TherprotmotorTempcClass_Init(ThermProt_MtrTempClass_t *
  rty_stMtrTempClass, boolean_T *rty_ThermProt_isStartupRdy);
extern void ThermProt_Mdl_TherprotmotorTempcClass(const int16_T
  *rtu_s16TEstMtrTemp, const int16_T *rtu_s16AmbTemp, const
  CfgParam_TThermThreshBus_t *rtu_stThresholds_Param, const StateMach_Mode_t
  *rtu_stSysMode, ThermProt_MtrTempClass_t *rty_stMtrTempClass, boolean_T
  *rty_ThermProt_isStartupRdy);

#endif                                 /*ThermProt_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ThermProt_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_ThermProt_Mdl_T ThermProt_Mdl_MdlrefDW;

#endif                                 /*ThermProt_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ThermProt_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_ThermProt_Mdl_c_T ThermProt_Mdl_B;

/* Block states (default storage) */
extern DW_ThermProt_Mdl_f_T ThermProt_Mdl_DW;

#endif                                 /*ThermProt_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S4>/Data Type Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'ThermProt_Mdl'
 * '<S1>'   : 'ThermProt_Mdl/Model Info'
 * '<S2>'   : 'ThermProt_Mdl/ThermProt_Mdl'
 * '<S3>'   : 'ThermProt_Mdl/ThermProt_Mdl/DocBlock'
 * '<S4>'   : 'ThermProt_Mdl/ThermProt_Mdl/MtrTempClassOverride'
 * '<S5>'   : 'ThermProt_Mdl/ThermProt_Mdl/ThermProtMosfetTempClass'
 * '<S6>'   : 'ThermProt_Mdl/ThermProt_Mdl/TherprotmotorTempcClass'
 * '<S7>'   : 'ThermProt_Mdl/ThermProt_Mdl/MtrTempClassOverride/DocBlock'
 * '<S8>'   : 'ThermProt_Mdl/ThermProt_Mdl/ThermProtMosfetTempClass/Compare To Constant'
 * '<S9>'   : 'ThermProt_Mdl/ThermProt_Mdl/ThermProtMosfetTempClass/DocBlock'
 * '<S10>'  : 'ThermProt_Mdl/ThermProt_Mdl/ThermProtMosfetTempClass/ThermalProtectionStateMachineMosfet'
 * '<S11>'  : 'ThermProt_Mdl/ThermProt_Mdl/TherprotmotorTempcClass/Compare To Constant'
 * '<S12>'  : 'ThermProt_Mdl/ThermProt_Mdl/TherprotmotorTempcClass/DocBlock'
 * '<S13>'  : 'ThermProt_Mdl/ThermProt_Mdl/TherprotmotorTempcClass/ThermalProtectionStateMachine'
 */
#endif                                 /* RTW_HEADER_ThermProt_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
