/*
 * File: StateMach_Mdl.h
 *
 * Code generated for Simulink model 'StateMach_Mdl'.
 *
 * Model version                  : 1.190
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:39:38 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_StateMach_Mdl_h_
#define RTW_HEADER_StateMach_Mdl_h_
#ifndef StateMach_Mdl_COMMON_INCLUDES_
#define StateMach_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* StateMach_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "StateMach_Mdl_types.h"
#include "irs_std_types.h"
#include "RoofSys_CommDefines.h"
#include "PosMon_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "zero_crossing_types.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'StateMach_Mdl' */
#ifndef StateMach_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T SFunction1;                  /* '<S10>/S-Function1' */
  boolean_T SFunction1_c;              /* '<S8>/S-Function1' */
  HwAbs_tenStatus stsGoToShtdwnReady;  /* '<S7>/S-Function1' */
  HwAbs_tenStatus stsFastShtdwnDone;   /* '<S6>/S-Function1' */
} B_StateMach_Mdl_c_T;

#endif                                 /*StateMach_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'StateMach_Mdl' */
#ifndef StateMach_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T UnitDelay_DSTATE;            /* '<S28>/Unit Delay' */
  boolean_T Delay2_DSTATE;             /* '<S2>/Delay2' */
  HwAbs_tenStatus Delay_DSTATE;        /* '<S2>/Delay' */
  HwAbs_tenStatus Delay1_DSTATE;       /* '<S2>/Delay1' */
  uint8_T is_c1_StateMach_Mdl;         /* '<S12>/SCUStateArbitration' */
  uint8_T is_active_c1_StateMach_Mdl;  /* '<S12>/SCUStateArbitration' */
  uint8_T temporalCounter_i1;          /* '<S12>/SCUStateArbitration' */
} DW_StateMach_Mdl_f_T;

#endif                                 /*StateMach_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'StateMach_Mdl' */
#ifndef StateMach_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState NVMWriteForShutdown_Trig_ZCE;/* '<S2>/NVMWriteForShutdown' */
  ZCSigState NVMWriteForFastShutdown_Trig_ZCE;/* '<S2>/NVMWriteForFastShutdown' */
} ZCE_StateMach_Mdl_T;

#endif                                 /*StateMach_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef StateMach_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_StateMach_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*StateMach_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef StateMach_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_StateMach_Mdl_T rtm;
} MdlrefDW_StateMach_Mdl_T;

#endif                                 /*StateMach_Mdl_MDLREF_HIDE_CHILD_*/

extern void StateMach_Mdl_Init(void);
extern void StateMach_Mdl(const HallDeco_Dir_t
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_DMtrDir, const uint8_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic, const uint8_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1, const boolean_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isStartUpRdy, const boolean_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isPwrLost, const boolean_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isStartupRdy, const boolean_T
  *rtu_SWC_DataHndlLyrBus_UsgHistBus_UsgHist_isStartupRdy, const boolean_T
  *rtu_SWC_DataHndlLyrBus_RefFieldBus_RefField_isStartupRdy, const boolean_T
  *rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy, const boolean_T
  *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isStartupRdy, const boolean_T
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_isStartupRdy, const
  CtrlLog_MovType_t *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovType, const
  uint16_T *rtu_SWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsSysFaultReaction,
  const boolean_T *rtu_SWC_CommLyrBus_VehComBus_VehCom_bSleepReady,
  StateMach_Mode_t *rty_StateMachBus_StateMach_stSysMode);

/* Model reference registration function */
extern void StateMach_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef StateMach_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_StateMach_Mdl_T StateMach_Mdl_MdlrefDW;

#endif                                 /*StateMach_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef StateMach_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_StateMach_Mdl_c_T StateMach_Mdl_B;

/* Block states (default storage) */
extern DW_StateMach_Mdl_f_T StateMach_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_StateMach_Mdl_T StateMach_Mdl_PrevZCX;

#endif                                 /*StateMach_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'StateMach_Mdl'
 * '<S1>'   : 'StateMach_Mdl/Model Info'
 * '<S2>'   : 'StateMach_Mdl/StateMach_Mdl'
 * '<S3>'   : 'StateMach_Mdl/StateMach_Mdl/DocBlock'
 * '<S4>'   : 'StateMach_Mdl/StateMach_Mdl/NVMSleepWriteTriggerArbitrationFastShutdown'
 * '<S5>'   : 'StateMach_Mdl/StateMach_Mdl/NVMSleepWriteTriggerArbitrationShutdown'
 * '<S6>'   : 'StateMach_Mdl/StateMach_Mdl/NVMWriteForFastShutdown'
 * '<S7>'   : 'StateMach_Mdl/StateMach_Mdl/NVMWriteForShutdown'
 * '<S8>'   : 'StateMach_Mdl/StateMach_Mdl/SBCShutdown'
 * '<S9>'   : 'StateMach_Mdl/StateMach_Mdl/SBCShutdownTriggerArbitration'
 * '<S10>'  : 'StateMach_Mdl/StateMach_Mdl/SBCSystemReset'
 * '<S11>'  : 'StateMach_Mdl/StateMach_Mdl/SBCSystemResetTrigger'
 * '<S12>'  : 'StateMach_Mdl/StateMach_Mdl/SCUStateArbitration'
 * '<S13>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination'
 * '<S14>'  : 'StateMach_Mdl/StateMach_Mdl/NVMSleepWriteTriggerArbitrationFastShutdown/DocBlock'
 * '<S15>'  : 'StateMach_Mdl/StateMach_Mdl/NVMSleepWriteTriggerArbitrationShutdown/DocBlock'
 * '<S16>'  : 'StateMach_Mdl/StateMach_Mdl/NVMWriteForFastShutdown/DocBlock'
 * '<S17>'  : 'StateMach_Mdl/StateMach_Mdl/NVMWriteForShutdown/DocBlock'
 * '<S18>'  : 'StateMach_Mdl/StateMach_Mdl/SBCShutdown/DocBlock'
 * '<S19>'  : 'StateMach_Mdl/StateMach_Mdl/SBCShutdownTriggerArbitration/DocBlock'
 * '<S20>'  : 'StateMach_Mdl/StateMach_Mdl/SBCSystemReset/DocBlock'
 * '<S21>'  : 'StateMach_Mdl/StateMach_Mdl/SBCSystemResetTrigger/DocBlock'
 * '<S22>'  : 'StateMach_Mdl/StateMach_Mdl/SCUStateArbitration/DocBlock'
 * '<S23>'  : 'StateMach_Mdl/StateMach_Mdl/SCUStateArbitration/SCUStateArbitration'
 * '<S24>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/AbruptShutdownCalc'
 * '<S25>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/DocBlock'
 * '<S26>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/FstShtdwnDoneConditionCalc'
 * '<S27>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/GoToShutdownReadyConditionCalc'
 * '<S28>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/GoToStandbyConditionCalc'
 * '<S29>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/InitDoneConditionCalc'
 * '<S30>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/StartupDoneConditionCalc'
 * '<S31>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/AbruptShutdownCalc/DocBlock'
 * '<S32>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/FstShtdwnDoneConditionCalc/DocBlock'
 * '<S33>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/GoToShutdownReadyConditionCalc/DocBlock'
 * '<S34>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/GoToStandbyConditionCalc/Compare To Zero'
 * '<S35>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/GoToStandbyConditionCalc/DocBlock'
 * '<S36>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/InitDoneConditionCalc/DocBlock'
 * '<S37>'  : 'StateMach_Mdl/StateMach_Mdl/StateMachineTransitionConditionsDetermination/StartupDoneConditionCalc/DocBlock'
 */
#endif                                 /* RTW_HEADER_StateMach_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
