/*
 * File: VoltMon_Mdl.h
 *
 * Code generated for Simulink model 'VoltMon_Mdl'.
 *
 * Model version                  : 1.244
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:42:51 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_VoltMon_Mdl_h_
#define RTW_HEADER_VoltMon_Mdl_h_
#ifndef VoltMon_Mdl_COMMON_INCLUDES_
#define VoltMon_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* VoltMon_Mdl_COMMON_INCLUDES_ */

#include "CfgParam_Mdl_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "StateMach_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "VoltMon_Mdl_types.h"
#include "Lib_DTCDet_4mPQXRNG.h"
#include "ThermProt_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "DTC_Exp.h"
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "VoltMon_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'VoltMon_Mdl' */
#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T TmpSignalConversionAtStartupCheckInport1;
  uint16_T TmpSignalConversionAtRollingAverageFilterInport2;
  uint16_T Switch2;                    /* '<S5>/Switch2' */
  uint8_T DTC_VOLT_VBatt;              /* '<S6>/StartUpRdy' */
  uint8_T DTC_VOLT_Pullup;             /* '<S6>/StartUpRdy' */
  boolean_T VoltMon_is12VBattVld_e;    /* '<S6>/StartUpRdy' */
  VoltMon_VoltClass_t stVoltMonClass; /* '<S7>/VoltageMonitoringStateMachine' */
} B_VoltMon_Mdl_c_T;

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'VoltMon_Mdl' */
#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T temporalCounter_i1;         /* '<S36>/debounce200ms' */
  uint32_T temporalCounter_i1_g;       /* '<S36>/debounce2000ms' */
  uint32_T temporalCounter_i1_f;       /* '<S35>/debounce1000ms' */
  uint32_T temporalCounter_i1_j;       /* '<S35>/debounce10000ms' */
  uint32_T temporalCounter_i1_m;      /* '<S7>/VoltageMonitoringStateMachine' */
  uint16_T TappedDelay_X[3];           /* '<S6>/Tapped Delay' */
  uint16_T TappedDelay_X_g[4];         /* '<S5>/Tapped Delay' */
  uint16_T TappedDelay_X_n[19];        /* '<S12>/Tapped Delay' */
  uint16_T Delay1_DSTATE[20];          /* '<S11>/Delay1' */
  uint16_T TappedDelay_X_i[20];        /* '<S13>/Tapped Delay' */
  boolean_T UnitDelay_DSTATE;          /* '<S35>/Unit Delay' */
  boolean_T UnitDelay_DSTATE_p;        /* '<S36>/Unit Delay' */
  boolean_T Delay3_DSTATE[20];         /* '<S11>/Delay3' */
  VoltMon_VoltClass_t Delay_DSTATE;    /* '<S7>/Delay' */
  uint8_T is_c1_VoltMon_Mdl;           /* '<S36>/debounce200ms' */
  uint8_T is_active_c1_VoltMon_Mdl;    /* '<S36>/debounce200ms' */
  uint8_T is_c4_VoltMon_Mdl;           /* '<S36>/debounce2000ms' */
  uint8_T is_active_c4_VoltMon_Mdl;    /* '<S36>/debounce2000ms' */
  uint8_T is_c6_VoltMon_Mdl;           /* '<S35>/debounce1000ms' */
  uint8_T is_active_c6_VoltMon_Mdl;    /* '<S35>/debounce1000ms' */
  uint8_T is_c5_VoltMon_Mdl;           /* '<S35>/debounce10000ms' */
  uint8_T is_active_c5_VoltMon_Mdl;    /* '<S35>/debounce10000ms' */
  uint8_T is_c2_VoltMon_Mdl;          /* '<S7>/VoltageMonitoringStateMachine' */
  uint8_T is_active_c2_VoltMon_Mdl;   /* '<S7>/VoltageMonitoringStateMachine' */
  uint8_T is_c3_VoltMon_Mdl;           /* '<S6>/StartUpRdy' */
  uint8_T is_active_c3_VoltMon_Mdl;    /* '<S6>/StartUpRdy' */
  DW_Lib_DTCDet_4mPQXRNG_T Lib_DTCDet_f[1];/* '<S13>/Lib_DTCDet' */
  DW_Lib_DTCDet_4mPQXRNG_T Lib_DTCDet[1];/* '<S12>/Lib_DTCDet' */
} DW_VoltMon_Mdl_f_T;

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'VoltMon_Mdl' */
#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const uint8_T Width;                 /* '<S5>/Width' */
} ConstB_VoltMon_Mdl_h_T;

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_VoltMon_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_VoltMon_Mdl_T rtm;
} MdlrefDW_VoltMon_Mdl_T;

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

extern void VoltMon_Mdl_Init(void);
extern void VoltMon_Mdl(const MtrCtrl_MtrDir_t
  *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir, const boolean_T
  *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isMotorInStartUp, const SWC_HwAbsLyrBus
  *rtu_SWC_HwAbsLyrBus, const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, uint16_T
  *rty_VoltMonBus_VoltMon_u12VBatt, boolean_T
  *rty_VoltMonBus_VoltMon_is12VBattVld, VoltMon_VoltClass_t
  *rty_VoltMonBus_VoltMon_stVoltClass, boolean_T
  *rty_VoltMonBus_VoltMon_isVoltDrop, boolean_T
  *rty_VoltMonBus_VoltMon_isFlctnDet, boolean_T
  *rty_VoltMonBus_VoltMon_isStartUpRdy, uint8_T *rty_VoltMonBus_DTC_VOLT_VBatt,
  uint8_T *rty_VoltMonBus_DTC_VOLT_Pullup, boolean_T
  *rty_VoltMonBus_VoltMon_isPwrLost, boolean_T
  *rty_VoltMonBus_VoltMon_isUnderVtgFlgSet, boolean_T
  *rty_VoltMonBus_VoltMon_isOverVtgFlgSet);

/* Model reference registration function */
extern void VoltMon_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

extern void VoltMon_Mdl_PowerFailureDetect_Init(void);
extern void VoltMon_Mdl_PowerFailureDetect(boolean_T rtu_VoltMon_is12VBattVld,
  uint16_T rtu_VoltMon_u12VBatt, const CfgParam_CALBus_t *rtu_CfgParam_CAL,
  const boolean_T *rtu_isMotorStartUp, boolean_T rtu_DTC_isDTCEnbl, boolean_T
  rtu_DTC_isFltClr, const MtrCtrl_MtrDir_t *rtu_SetMtrDir, boolean_T
  *rty_VoltMon_isVoltDrop, boolean_T *rty_VoltMon_isFlctnDet, boolean_T
  *rty_VoltMon_isPwrLost);
extern void VoltMon_Mdl_RollingAverageFilter(uint8_T rtu_DTC_VOLT_VBatt,
  uint16_T rtu_u12VBattInst, uint16_T *rty_VoltMon_u12VBatt);
extern void VoltMon_Mdl_StartupCheck_Init(boolean_T *rty_VoltMon_isStartUpRdy,
  uint8_T *rty_DTC_VOLT_VBatt, uint8_T *rty_DTC_VOLT_Pullup, boolean_T
  *rty_VoltMon_is12VBattVld);
extern void VoltMon_Mdl_StartupCheck(uint16_T rtu_u12VBattInst, const
  StateMach_Mode_t *rtu_stSysMode, uint16_T rtu_uSwchPwrInst, boolean_T
  *rty_VoltMon_isStartUpRdy, uint8_T *rty_DTC_VOLT_VBatt, uint8_T
  *rty_DTC_VOLT_Pullup, boolean_T *rty_VoltMon_is12VBattVld);
extern void VoltMon_Mdl_VoltageClassMonitor(uint8_T rtu_DTC_VOLT_VBatt,
  boolean_T rtu_VoltMon_is12VBattVld, uint16_T rtu_VoltMon_u12VBatt, const
  CfgParam_CALBus_t *rtu_CfgParam_CAL, const StateMach_Mode_t *rtu_stSysMode,
  VoltMon_VoltClass_t *rty_VoltMon_stVoltClass);
extern void VoltMon_Mdl_VoltageFltFlagsDetect(uint16_T rtu_VoltMon_u12VBatt,
  boolean_T *rty_VoltMon_isUnderVtgFlgSet, boolean_T
  *rty_VoltMon_isOverVtgFlgSet);

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_VoltMon_Mdl_T VoltMon_Mdl_MdlrefDW;

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VoltMon_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_VoltMon_Mdl_c_T VoltMon_Mdl_B;

/* Block states (default storage) */
extern DW_VoltMon_Mdl_f_T VoltMon_Mdl_DW;

#endif                                 /*VoltMon_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S9>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S11>/Logical Operator1' : Eliminated due to no operation
 * Block '<S12>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S13>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S20>/Add1' : Unused code path elimination
 * Block '<S20>/Constant3' : Unused code path elimination
 * Block '<S24>/Add1' : Unused code path elimination
 * Block '<S24>/Constant3' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'VoltMon_Mdl'
 * '<S1>'   : 'VoltMon_Mdl/Model Info'
 * '<S2>'   : 'VoltMon_Mdl/VoltageMonitoring'
 * '<S3>'   : 'VoltMon_Mdl/VoltageMonitoring/DocBlock'
 * '<S4>'   : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect'
 * '<S5>'   : 'VoltMon_Mdl/VoltageMonitoring/RollingAverageFilter'
 * '<S6>'   : 'VoltMon_Mdl/VoltageMonitoring/StartupCheck'
 * '<S7>'   : 'VoltMon_Mdl/VoltageMonitoring/VoltageClassMonitor'
 * '<S8>'   : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect'
 * '<S9>'   : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/ PowerLostDetection'
 * '<S10>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/DocBlock'
 * '<S11>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/SignalValidation'
 * '<S12>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageDropDetection'
 * '<S13>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageFluctuationDetection'
 * '<S14>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/ PowerLostDetection/Compare To Constant1'
 * '<S15>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/ PowerLostDetection/Compare To Constant2'
 * '<S16>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/ PowerLostDetection/DocBlock'
 * '<S17>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/SignalValidation/Compare To Constant4'
 * '<S18>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/SignalValidation/DocBlock'
 * '<S19>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageDropDetection/DocBlock'
 * '<S20>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageDropDetection/Lib_DTCDet'
 * '<S21>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageFluctuationDetection/Compare To Constant'
 * '<S22>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageFluctuationDetection/Compare To Constant1'
 * '<S23>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageFluctuationDetection/DocBlock'
 * '<S24>'  : 'VoltMon_Mdl/VoltageMonitoring/PowerFailureDetect/VoltageFluctuationDetection/Lib_DTCDet'
 * '<S25>'  : 'VoltMon_Mdl/VoltageMonitoring/RollingAverageFilter/Compare To Constant'
 * '<S26>'  : 'VoltMon_Mdl/VoltageMonitoring/RollingAverageFilter/DocBlock'
 * '<S27>'  : 'VoltMon_Mdl/VoltageMonitoring/StartupCheck/Compare To Constant2'
 * '<S28>'  : 'VoltMon_Mdl/VoltageMonitoring/StartupCheck/DocBlock'
 * '<S29>'  : 'VoltMon_Mdl/VoltageMonitoring/StartupCheck/StartUpRdy'
 * '<S30>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageClassMonitor/Compare To Constant'
 * '<S31>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageClassMonitor/Compare To Constant2'
 * '<S32>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageClassMonitor/DocBlock'
 * '<S33>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageClassMonitor/VoltageMonitoringStateMachine'
 * '<S34>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/DocBlock'
 * '<S35>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/OvervtgVtgFlgDetrmn'
 * '<S36>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/UnderVtgFlgDetrmn'
 * '<S37>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/OvervtgVtgFlgDetrmn/DocBlock'
 * '<S38>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/OvervtgVtgFlgDetrmn/debounce10000ms'
 * '<S39>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/OvervtgVtgFlgDetrmn/debounce1000ms'
 * '<S40>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/UnderVtgFlgDetrmn/DocBlock'
 * '<S41>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/UnderVtgFlgDetrmn/debounce2000ms'
 * '<S42>'  : 'VoltMon_Mdl/VoltageMonitoring/VoltageFltFlagsDetect/UnderVtgFlgDetrmn/debounce200ms'
 */
#endif                                 /* RTW_HEADER_VoltMon_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
