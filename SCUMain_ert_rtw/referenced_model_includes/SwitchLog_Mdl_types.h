/*
 * File: SwitchLog_Mdl_types.h
 *
 * Code generated for Simulink model 'SwitchLog_Mdl'.
 *
 * Model version                  : 1.14
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:39:54 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_SwitchLog_Mdl_types_h_
#define RTW_HEADER_SwitchLog_Mdl_types_h_

/* Forward declaration for rtModel */
typedef struct tag_RTM_SwitchLog_Mdl_T RT_MODEL_SwitchLog_Mdl_T;

#endif                                 /* RTW_HEADER_SwitchLog_Mdl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
