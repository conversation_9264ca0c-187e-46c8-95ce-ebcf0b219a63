/*
 * File: LearnAdap_Mdl.h
 *
 * Code generated for Simulink model 'LearnAdap_Mdl'.
 *
 * Model version                  : 1.152
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:33:05 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_LearnAdap_Mdl_h_
#define RTW_HEADER_LearnAdap_Mdl_h_
#ifndef LearnAdap_Mdl_COMMON_INCLUDES_
#define LearnAdap_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* LearnAdap_Mdl_COMMON_INCLUDES_ */

#include "ThermProt_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "LearnAdap_Mdl_types.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "RoofSys_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"
#include "LearnAdap_Exp.h"
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'LearnAdap_Mdl' */
#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  CfgParam_CALBus_t CfgParam_CAL_g;    /* '<Root>/Data Store Read1' */
  uint16_T Subtract;                   /* '<S41>/Subtract' */
  uint16_T LearnAdap_newHardStop;      /* '<S41>/LearningStates' */
  uint8_T Status;                      /* '<S38>/S-Function' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new;
                               /* '<S27>/S_Func_NVMMan_enRequestNVMWrite_new' */
  boolean_T LearnAdap_isInterrupted;   /* '<S41>/LearningStates' */
  LearnAdap_nextRenorm_t DataStoreRead3;/* '<Root>/Data Store Read3' */
  LearnAdap_Req_t LearnAdap_stPosReq;  /* '<S41>/LearningStates' */
  LearnAdap_Mode_t LearnAdap_stMode;   /* '<S41>/LearningStates' */
} B_LearnAdap_Mdl_c_T;

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'LearnAdap_Mdl' */
#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T DelayInput1_DSTATE;         /* '<S16>/Delay Input1' */
  uint16_T prevNewHardStop;            /* '<S41>/LearningStates' */
  uint16_T prevNewSoftStop;            /* '<S41>/LearningStates' */
  boolean_T DelayInput1_DSTATE_n;      /* '<S17>/Delay Input1' */
  uint8_T is_c2_LearnAdap_Mdl;         /* '<S41>/LearningStates' */
  uint8_T is_learned;                  /* '<S41>/LearningStates' */
  uint8_T is_Renorming;                /* '<S41>/LearningStates' */
  uint8_T is_learningPosition;         /* '<S41>/LearningStates' */
  uint8_T is_active_c2_LearnAdap_Mdl;  /* '<S41>/LearningStates' */
} DW_LearnAdap_Mdl_f_T;

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'LearnAdap_Mdl' */
#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState incrementCycleCounter1_Trig_ZCE;/* '<S6>/incrementCycleCounter1' */
  ZCSigState incrementCycleCounter_Trig_ZCE;/* '<S6>/incrementCycleCounter' */
} ZCE_LearnAdap_Mdl_T;

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'LearnAdap_Mdl' */
#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const LearnAdap_Req_t LearnAdap_stRefFieldReq;/* '<Root>/Data Type Conversion4' */
} ConstB_LearnAdap_Mdl_h_T;

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_LearnAdap_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_LearnAdap_Mdl_T rtm;
} MdlrefDW_LearnAdap_Mdl_T;

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern int16_T LearnAdap_TLastNormTemp;
                          /* Simulink.Signal object 'LearnAdap_TLastNormTemp' */
extern uint16_T LearnAdap_renormCycleCounter;
                     /* Simulink.Signal object 'LearnAdap_renormCycleCounter' */
extern boolean_T PIDDID_isFactoryMode;
                                /* Simulink.Signal object 'PIDDID_isFactoryMode'
                                 * Diagnostic Factory Mode
                                   1 - Enabled
                                   0 -Disabled

                                 */
extern LearnAdap_nextRenorm_t LearnAdap_stNextRenorm;
                           /* Simulink.Signal object 'LearnAdap_stNextRenorm' */
extern void LearnAdap_Mdl_Init(void);
extern void LearnAdap_Mdl(const ThermProt_MtrTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass, const
  ThermProt_MosfetTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass, const
  VehCom_Cmd_t *rtu_SWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd, const
  VoltMon_VoltClass_t *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass,
  const boolean_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet, const
  int16_T *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn, const int16_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const boolean_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld, const
  StateMach_Mode_t *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode,
  const BlockDet_Stall_t *rtu_BlockDetBus_BlockDet_stStallType, const uint16_T
  *rtu_SWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsSysFaultReaction,
  LearnAdap_Mode_t *rty_LearnAdapBus_LearnAdap_stMode, LearnAdap_Req_t
  *rty_LearnAdapBus_LearnAdap_stRefFieldReq, LearnAdap_Req_t
  *rty_LearnAdapBus_LearnAdap_stPosReq, uint8_T
  *rty_LearnAdapBus_LearnAdap_stInt, uint16_T
  *rty_LearnAdapBus_LearnAdap_newHardStop, uint16_T
  *rty_LearnAdapBus_LearnAdap_newSoftStop, boolean_T
  *rty_LearnAdapBus_LearnAdap_isLearnComplete, boolean_T
  *rty_LearnAdapBus_LearnAdap_isLearningAllowed, boolean_T
  *rty_LearnAdapBus_LearnAdap_isInterrupted);

/* Model reference registration function */
extern void LearnAdap_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

extern void LearnAdap_Mdl_NVMWrite(uint32_T rtu_BlockId, uint8_T *rty_Status);
extern void LearnAdap_Mdl_setLastNormingTemperature(const int16_T
  *rtu_AmbTempMon_TAmbTemp, int16_T rtu_LearnAdap_TLastNormTemp, const boolean_T
  *rtu_AmbTempMon_isAmbTempVld);

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern boolean_T PIDDID_isCyclicRenormSuppress;
extern boolean_T PIDDID_isDummyRPRtnEna;

#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_LearnAdap_Mdl_T LearnAdap_Mdl_MdlrefDW;

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef LearnAdap_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_LearnAdap_Mdl_c_T LearnAdap_Mdl_B;

/* Block states (default storage) */
extern DW_LearnAdap_Mdl_f_T LearnAdap_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_LearnAdap_Mdl_T LearnAdap_Mdl_PrevZCX;

#endif                                 /*LearnAdap_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S23>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/FixPt Data Type Propagation' : Unused code path elimination
 * Block '<S36>/Compare' : Unused code path elimination
 * Block '<S36>/Constant' : Unused code path elimination
 * Block '<S8>/Constant' : Unused code path elimination
 * Block '<S8>/Constant2' : Unused code path elimination
 * Block '<S8>/Switch1' : Unused code path elimination
 * Block '<Root>/Data Type Conversion6' : Eliminate redundant data type conversion
 * Block '<S23>/FixPt Gateway In' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'LearnAdap_Mdl'
 * '<S1>'   : 'LearnAdap_Mdl/Compare To Constant'
 * '<S2>'   : 'LearnAdap_Mdl/Model Info'
 * '<S3>'   : 'LearnAdap_Mdl/RelearnAdaption'
 * '<S4>'   : 'LearnAdap_Mdl/RelearnAdaption/DetermineCyclicRenormRequired'
 * '<S5>'   : 'LearnAdap_Mdl/RelearnAdaption/DocBlock'
 * '<S6>'   : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter'
 * '<S7>'   : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed'
 * '<S8>'   : 'LearnAdap_Mdl/RelearnAdaption/NVM_Access'
 * '<S9>'   : 'LearnAdap_Mdl/RelearnAdaption/Relearn'
 * '<S10>'  : 'LearnAdap_Mdl/RelearnAdaption/clearCyclicCounter'
 * '<S11>'  : 'LearnAdap_Mdl/RelearnAdaption/setLastNormingTemperature'
 * '<S12>'  : 'LearnAdap_Mdl/RelearnAdaption/DetermineCyclicRenormRequired/Compare To Constant'
 * '<S13>'  : 'LearnAdap_Mdl/RelearnAdaption/DetermineCyclicRenormRequired/Compare To Constant1'
 * '<S14>'  : 'LearnAdap_Mdl/RelearnAdaption/DetermineCyclicRenormRequired/DocBlock'
 * '<S15>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/Compare To Constant'
 * '<S16>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/Detect Change'
 * '<S17>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/Detect Increase'
 * '<S18>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/DocBlock'
 * '<S19>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter'
 * '<S20>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter1'
 * '<S21>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter/Compare To Constant'
 * '<S22>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter/DocBlock'
 * '<S23>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter/Increment Stored Integer'
 * '<S24>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter1/DocBlock'
 * '<S25>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter1/WriteCycRenormCounterVld'
 * '<S26>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter1/WriteCycRenormCounterVld/DocBlock'
 * '<S27>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter1/WriteCycRenormCounterVld/NVMWrite'
 * '<S28>'  : 'LearnAdap_Mdl/RelearnAdaption/IncreaseNormingCycleCounter/incrementCycleCounter1/WriteCycRenormCounterVld/NVMWrite/DocBlock'
 * '<S29>'  : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed/Compare To Constant'
 * '<S30>'  : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed/Compare To Constant1'
 * '<S31>'  : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed/Compare To Constant2'
 * '<S32>'  : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed/Compare To Constant3'
 * '<S33>'  : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed/Compare To Constant4'
 * '<S34>'  : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed/Compare To Zero'
 * '<S35>'  : 'LearnAdap_Mdl/RelearnAdaption/LearningAllowed/DocBlock'
 * '<S36>'  : 'LearnAdap_Mdl/RelearnAdaption/NVM_Access/CompareToConstant4'
 * '<S37>'  : 'LearnAdap_Mdl/RelearnAdaption/NVM_Access/DocBlock'
 * '<S38>'  : 'LearnAdap_Mdl/RelearnAdaption/NVM_Access/NVMWrite'
 * '<S39>'  : 'LearnAdap_Mdl/RelearnAdaption/NVM_Access/NVMWrite/DocBlock'
 * '<S40>'  : 'LearnAdap_Mdl/RelearnAdaption/Relearn/DocBlock'
 * '<S41>'  : 'LearnAdap_Mdl/RelearnAdaption/Relearn/StateMachine'
 * '<S42>'  : 'LearnAdap_Mdl/RelearnAdaption/Relearn/StateMachine/DocBlock1'
 * '<S43>'  : 'LearnAdap_Mdl/RelearnAdaption/Relearn/StateMachine/LearningStates'
 * '<S44>'  : 'LearnAdap_Mdl/RelearnAdaption/clearCyclicCounter/DocBlock'
 * '<S45>'  : 'LearnAdap_Mdl/RelearnAdaption/clearCyclicCounter/clearCyclicCounter'
 * '<S46>'  : 'LearnAdap_Mdl/RelearnAdaption/clearCyclicCounter/clearCyclicCounter/DocBlock'
 * '<S47>'  : 'LearnAdap_Mdl/RelearnAdaption/setLastNormingTemperature/DocBlock'
 * '<S48>'  : 'LearnAdap_Mdl/RelearnAdaption/setLastNormingTemperature/setLastNormingTemperature'
 * '<S49>'  : 'LearnAdap_Mdl/RelearnAdaption/setLastNormingTemperature/setLastNormingTemperature/DocBlock'
 */
#endif                                 /* RTW_HEADER_LearnAdap_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
