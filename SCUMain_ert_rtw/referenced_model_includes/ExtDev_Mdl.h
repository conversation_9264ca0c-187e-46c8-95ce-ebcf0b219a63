/*
 * File: ExtDev_Mdl.h
 *
 * Code generated for Simulink model 'ExtDev_Mdl'.
 *
 * Model version                  : 1.59
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:31:42 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ExtDev_Mdl_h_
#define RTW_HEADER_ExtDev_Mdl_h_
#ifndef ExtDev_Mdl_COMMON_INCLUDES_
#define ExtDev_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* ExtDev_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "ExtDev_Mdl_types.h"
#include "zero_crossing_types.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block states (default storage) for model 'ExtDev_Mdl' */
#ifndef ExtDev_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T DelayInput1_DSTATE;         /* '<S9>/Delay Input1' */
  boolean_T isMtrFebkCtrlHS1Set;       /* '<S4>/Data Store Memory' */
  boolean_T isHallSuppCtrlHS2Set;      /* '<S4>/Data Store Memory1' */
  boolean_T isSwtchSuppCtrlHS3Set;     /* '<S4>/Data Store Memory2' */
} DW_ExtDev_Mdl_f_T;

#endif                                 /*ExtDev_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'ExtDev_Mdl' */
#ifndef ExtDev_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState StoreInitiStateHS_Trig_ZCE;/* '<S4>/StoreInitiStateHS' */
} ZCE_ExtDev_Mdl_T;

#endif                                 /*ExtDev_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ExtDev_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_ExtDev_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*ExtDev_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ExtDev_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_ExtDev_Mdl_T rtm;
} MdlrefDW_ExtDev_Mdl_T;

#endif                                 /*ExtDev_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T PIDDID_IOcontrolMaskRecordSysICOut;
               /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordSysICOut' */
extern boolean_T PIDDID_IOCtrl_isHallSupplyCtrl;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isHallSupplyCtrl' */
extern boolean_T PIDDID_IOCtrl_isMtrFdbkCtrl;
                      /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFdbkCtrl' */
extern boolean_T PIDDID_IOCtrl_isSwtchSupplyCtrl;
                  /* Simulink.Signal object 'PIDDID_IOCtrl_isSwtchSupplyCtrl' */
extern void ExtDev_Mdl(const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, boolean_T
  *rty_ExtDevBus_ExtDev_isMtrFebkCtrlHS1Status, boolean_T
  *rty_ExtDevBus_ExtDev_isHallSuppHS2Status, boolean_T
  *rty_ExtDevBus_ExtDev_isSwtchSuppHS3Status, uint8_T
  *rty_ExtDevBus_ExtDev_u8GetPWMDutyCycleVal);

/* Model reference registration function */
extern void ExtDev_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef ExtDev_Mdl_MDLREF_HIDE_CHILD_

extern void ExtDev_Mdl_ExtDevReadStatus(boolean_T
  *rty_ExtDev_isMtrFebkCtrlHS1Status, boolean_T *rty_ExtDev_isHallSuppHS2Status,
  boolean_T *rty_ExtDev_isSwtchSuppHS3Status, uint8_T
  *rty_ExtDev_u8GetPWMDutyCycleVal);
extern void ExtDev_Mdl_SYSIC_PIDDID_IOCtrl(const StateMach_Mode_t
  *rtu_StateMach_stSysMode);

#endif                                 /*ExtDev_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ExtDev_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_ExtDev_Mdl_T ExtDev_Mdl_MdlrefDW;

#endif                                 /*ExtDev_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ExtDev_Mdl_MDLREF_HIDE_CHILD_

/* Block states (default storage) */
extern DW_ExtDev_Mdl_f_T ExtDev_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_ExtDev_Mdl_T ExtDev_Mdl_PrevZCX;

#endif                                 /*ExtDev_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'ExtDev_Mdl'
 * '<S1>'   : 'ExtDev_Mdl/ExtDev_Mdl'
 * '<S2>'   : 'ExtDev_Mdl/Model Info'
 * '<S3>'   : 'ExtDev_Mdl/ExtDev_Mdl/ExtDevReadStatus'
 * '<S4>'   : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl'
 * '<S5>'   : 'ExtDev_Mdl/ExtDev_Mdl/ExtDevReadStatus/Compare To Constant'
 * '<S6>'   : 'ExtDev_Mdl/ExtDev_Mdl/ExtDevReadStatus/Compare To Constant1'
 * '<S7>'   : 'ExtDev_Mdl/ExtDev_Mdl/ExtDevReadStatus/Compare To Constant2'
 * '<S8>'   : 'ExtDev_Mdl/ExtDev_Mdl/ExtDevReadStatus/DocBlock1'
 * '<S9>'   : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl/Detect Change'
 * '<S10>'  : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl/DocBlock1'
 * '<S11>'  : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl/SYSIC_IOCtrl_Set'
 * '<S12>'  : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl/StoreInitiStateHS'
 * '<S13>'  : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl/StoreInitiStateHS/Compare To Constant'
 * '<S14>'  : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl/StoreInitiStateHS/Compare To Constant1'
 * '<S15>'  : 'ExtDev_Mdl/ExtDev_Mdl/SYSIC_PIDDID_IOCtrl/StoreInitiStateHS/Compare To Constant2'
 */
#endif                                 /* RTW_HEADER_ExtDev_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
