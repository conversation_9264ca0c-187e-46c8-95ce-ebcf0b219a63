/*
 * File: MOSFETTempMon_Mdl.h
 *
 * Code generated for Simulink model 'MOSFETTempMon_Mdl'.
 *
 * Model version                  : 1.68
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:33:26 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_MOSFETTempMon_Mdl_h_
#define RTW_HEADER_MOSFETTempMon_Mdl_h_
#ifndef MOSFETTempMon_Mdl_COMMON_INCLUDES_
#define MOSFETTempMon_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* MOSFETTempMon_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "MOSFETTempMon_Mdl_types.h"
#include "CmdLogic_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "PosMon_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "HallDeco_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'MOSFETTempMon_Mdl' */
#ifndef MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  boolean_T LogicalOperator;           /* '<S14>/Logical Operator' */
  boolean_T MOSFETTempMon_TMosTemp;    /* '<S13>/Logical Operator' */
} B_MOSFETTempMon_Mdl_c_T;

#endif                                 /*MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'MOSFETTempMon_Mdl' */
#ifndef MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  boolean_T UnitDelay_DSTATE;          /* '<S11>/Unit Delay' */
} DW_MOSFETTempMon_Mdl_f_T;

#endif                                 /*MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_MOSFETTempMon_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_MOSFETTempMon_Mdl_T rtm;
} MdlrefDW_MOSFETTempMon_Mdl_T;

#endif                                 /*MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_*/

extern void MOSFETTempMon_Mdl_Init(void);
extern void MOSFETTempMon_Mdl(const int16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TMOSFETTemp, const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, int16_T
  *rty_MOSFETTempBus_MOSFETTempMon_TMosTemp, boolean_T
  *rty_MOSFETTempBus_MOSFETTempMon_isMosTempVld, uint8_T
  *rty_MOSFETTempBus_MOSFETTempMon_stMosTempSensFlt);

/* Model reference registration function */
extern void MOSFETTempMon_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_

extern void MOSFETTempMon_Mdl_determineMOSFETTemp(const int16_T
  *rtu_s16MOSFETTemp, int16_T *rty_MOSFETTempMon_TMosTemp);

#endif                                 /*MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_MOSFETTempMon_Mdl_T MOSFETTempMon_Mdl_MdlrefDW;

#endif                                 /*MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_MOSFETTempMon_Mdl_c_T MOSFETTempMon_Mdl_B;

/* Block states (default storage) */
extern DW_MOSFETTempMon_Mdl_f_T MOSFETTempMon_Mdl_DW;

#endif                                 /*MOSFETTempMon_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S11>/Cast To Boolean' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'MOSFETTempMon_Mdl'
 * '<S1>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring'
 * '<S2>'   : 'MOSFETTempMon_Mdl/Model Info'
 * '<S3>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/DocBlock1'
 * '<S4>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/convertMOSFETTempCAL'
 * '<S5>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTemp'
 * '<S6>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity'
 * '<S7>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/convertMOSFETTempCAL/DocBlock'
 * '<S8>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTemp/DocBlock'
 * '<S9>'   : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/DocBlock'
 * '<S10>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity'
 * '<S11>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity/Arbitrate different validity'
 * '<S12>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity/DocBlock'
 * '<S13>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity/MOSFETTemp Validity startup'
 * '<S14>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity/MOSTFETTemp range Validity '
 * '<S15>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity/Arbitrate different validity/DocBlock'
 * '<S16>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity/MOSFETTemp Validity startup/DocBlock'
 * '<S17>'  : 'MOSFETTempMon_Mdl/MOSFETTemperatureMonitoring/determineMOSFETTempValidity/determineMOSFETTempValidity/MOSTFETTemp range Validity /DocBlock'
 */
#endif                                 /* RTW_HEADER_MOSFETTempMon_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
