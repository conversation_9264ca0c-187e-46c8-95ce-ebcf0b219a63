/*
 * File: CtrlLog_Mdl.h
 *
 * Code generated for Simulink model 'CtrlLog_Mdl'.
 *
 * Model version                  : 1.173
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:30:54 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_CtrlLog_Mdl_h_
#define RTW_HEADER_CtrlLog_Mdl_h_
#ifndef CtrlLog_Mdl_COMMON_INCLUDES_
#define CtrlLog_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* CtrlLog_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "CtrlLog_Mdl_types.h"
#include "CtrlLogic_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "PosMon_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'CtrlLog_Mdl' */
#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  CtrlLog_tenTargetDirection targetDirection;/* '<S6>/Merge' */
  CtrlLog_tenTargetDirection direction;/* '<S7>/setDirectionCommand' */
  uint16_T Switch1;                    /* '<S78>/Switch1' */
  int16_T targetPosition;              /* '<S6>/Merge' */
  boolean_T reversalFinished;          /* '<S7>/setDirectionCommand' */
  boolean_T LogicalOperator_o;         /* '<S22>/Logical Operator' */
  SysFltDiag_LastStopReason_t lastStopReason;/* '<S7>/setDirectionCommand' */
  CtrlLog_RelearnMode_t relearnMode;   /* '<S7>/setDirectionCommand' */
  CtrlLog_MovType_t movementType;      /* '<S7>/setDirectionCommand' */
} B_CtrlLog_Mdl_c_T;

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'CtrlLog_Mdl' */
#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T UnitDelay1_DSTATE;          /* '<S58>/Unit Delay1' */
  CtrlLog_tenTargetDirection UnitDelay_DSTATE;/* '<S1>/Unit Delay' */
  uint32_T temporalCounter_i1;         /* '<S7>/setDirectionCommand' */
  int16_T UnitDelay_DSTATE_m;          /* '<S58>/Unit Delay' */
  uint16_T DelayInput1_DSTATE;         /* '<S72>/Delay Input1' */
  uint16_T DelayInput1_DSTATE_f;       /* '<S73>/Delay Input1' */
  uint16_T DelayInput1_DSTATE_e;       /* '<S74>/Delay Input1' */
  uint16_T DelayInput1_DSTATE_c;       /* '<S75>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_d;        /* '<S23>/Delay Input1' */
  boolean_T UnitDelay2_DSTATE;         /* '<S1>/Unit Delay2' */
  boolean_T DelayInput1_DSTATE_eg;     /* '<S36>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_b;      /* '<S20>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_e0;     /* '<S16>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_g;      /* '<S17>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_h;      /* '<S18>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_eu;     /* '<S19>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_p;      /* '<S65>/Delay Input1' */
  CtrlLog_MovType_t UnitDelay1_DSTATE_o;/* '<S1>/Unit Delay1' */
  uint8_T is_c7_CtrlLog_Mdl;           /* '<S7>/setDirectionCommand' */
  uint8_T is_learning;                 /* '<S7>/setDirectionCommand' */
  uint8_T is_active_c7_CtrlLog_Mdl;    /* '<S7>/setDirectionCommand' */
  boolean_T Memory_PreviousInput;      /* '<S80>/Memory' */
  boolean_T Memory_PreviousInput_p;    /* '<S67>/Memory' */
  boolean_T isTimeOut;                 /* '<S7>/setDirectionCommand' */
  boolean_T isManual_prev;             /* '<S7>/setDirectionCommand' */
  boolean_T isManual_start;            /* '<S7>/setDirectionCommand' */
  boolean_T isAuto_prev;               /* '<S7>/setDirectionCommand' */
  boolean_T isAuto_start;              /* '<S7>/setDirectionCommand' */
  boolean_T isAutoStepWiseMovement_prev;/* '<S7>/setDirectionCommand' */
  boolean_T isAutoStepWiseMovement_start;/* '<S7>/setDirectionCommand' */
  boolean_T isObjectDetected_prev;     /* '<S7>/setDirectionCommand' */
  boolean_T isObjectDetected_start;    /* '<S7>/setDirectionCommand' */
  VehCom_Cmd_t movCmd_prev;            /* '<S7>/setDirectionCommand' */
  VehCom_Cmd_t movCmd_start;           /* '<S7>/setDirectionCommand' */
} DW_CtrlLog_Mdl_f_T;

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'CtrlLog_Mdl' */
#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState stepWiseTarget_Trig_ZCE;  /* '<S8>/stepWiseTarget' */
  ZCSigState TriggeredSubsystem_Trig_ZCE;/* '<S11>/Triggered Subsystem' */
} ZCE_CtrlLog_Mdl_T;

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'CtrlLog_Mdl' */
#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const boolean_T CtrlLog_isMovIdle;   /* '<S3>/Data Type Conversion5' */
  const boolean_T CtrlLog_isPnlMovIdle;/* '<S3>/Data Type Conversion6' */
} ConstB_CtrlLog_Mdl_h_T;

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_CtrlLog_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_CtrlLog_Mdl_T rtm;
} MdlrefDW_CtrlLog_Mdl_T;

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

extern void CtrlLog_Mdl_Init(void);
extern void CtrlLog_Mdl(const boolean_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const int16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hComfortMidStop, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn, const
  StateMach_Mode_t *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode,
  const uint8_T *rtu_SWC_CommLyrBus_VehComBus_VehCom_hToPos, const uint16_T
  *rtu_SWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsSysFaultReaction, const
  ComLogBus *rtu_ComLogBus, const SWC_ObjDetLyrBus *rtu_SWC_ObjDetLyrBus, const
  uint8_T *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1, const boolean_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFaultDebd, CtrlLogBus
  *rty_CtrlLogBus);

/* Model reference registration function */
extern void CtrlLog_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_CtrlLog_Mdl_T CtrlLog_Mdl_MdlrefDW;

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef CtrlLog_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_CtrlLog_Mdl_c_T CtrlLog_Mdl_B;

/* Block states (default storage) */
extern DW_CtrlLog_Mdl_f_T CtrlLog_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_CtrlLog_Mdl_T CtrlLog_Mdl_PrevZCX;

#endif                                 /*CtrlLog_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S45>/Data Type Propagation' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CtrlLog_Mdl'
 * '<S1>'   : 'CtrlLog_Mdl/ControlLogic'
 * '<S2>'   : 'CtrlLog_Mdl/Model Info'
 * '<S3>'   : 'CtrlLog_Mdl/stub1'
 * '<S4>'   : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger'
 * '<S5>'   : 'CtrlLog_Mdl/ControlLogic/DocBlock'
 * '<S6>'   : 'CtrlLog_Mdl/ControlLogic/MovementType'
 * '<S7>'   : 'CtrlLog_Mdl/ControlLogic/PositionDetect'
 * '<S8>'   : 'CtrlLog_Mdl/ControlLogic/Subsystem'
 * '<S9>'   : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement'
 * '<S10>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/DocBlock'
 * '<S11>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger'
 * '<S12>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop'
 * '<S13>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/objectDetected'
 * '<S14>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Compare To Constant'
 * '<S15>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Compare To Constant1'
 * '<S16>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect Change'
 * '<S17>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect Increase'
 * '<S18>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect Increase1'
 * '<S19>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect Increase2'
 * '<S20>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Detect Increase4'
 * '<S21>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/DocBlock'
 * '<S22>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Triggered Subsystem'
 * '<S23>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/MovementTypeTrigger/Triggered Subsystem/Detect Change'
 * '<S24>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant'
 * '<S25>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant1'
 * '<S26>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant2'
 * '<S27>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant3'
 * '<S28>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant4'
 * '<S29>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant5'
 * '<S30>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant6'
 * '<S31>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant7'
 * '<S32>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant8'
 * '<S33>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/Compare To Constant9'
 * '<S34>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/determineManualAutoStop/DocBlock'
 * '<S35>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/objectDetected/Compare To Constant'
 * '<S36>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/objectDetected/Detect Increase'
 * '<S37>'  : 'CtrlLog_Mdl/ControlLogic/DetectMovementTrigger/objectDetected/DocBlock'
 * '<S38>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/AutomaticMovement'
 * '<S39>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/DocBlock'
 * '<S40>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement'
 * '<S41>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Stop'
 * '<S42>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/reversal'
 * '<S43>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/AutomaticMovement/Compare To Constant'
 * '<S44>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/AutomaticMovement/DocBlock'
 * '<S45>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/AutomaticMovement/Saturation Dynamic'
 * '<S46>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/Compare To Constant'
 * '<S47>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/Compare To Constant1'
 * '<S48>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/Compare To Constant2'
 * '<S49>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/Compare To Constant3'
 * '<S50>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/Compare To Constant4'
 * '<S51>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/DocBlock'
 * '<S52>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/Truth Table'
 * '<S53>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Manual Movement/Truth Table/DocBlock'
 * '<S54>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/Stop/DocBlock'
 * '<S55>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/reversal/Compare To Constant3'
 * '<S56>'  : 'CtrlLog_Mdl/ControlLogic/MovementType/reversal/DocBlock'
 * '<S57>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/DocBlock'
 * '<S58>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached'
 * '<S59>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed'
 * '<S60>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/setDirectionCommand'
 * '<S61>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Compare To Constant'
 * '<S62>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Compare To Constant2'
 * '<S63>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Compare To Constant3'
 * '<S64>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Compare To Zero'
 * '<S65>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/Detect Increase'
 * '<S66>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/DocBlock'
 * '<S67>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/determinePositionReached/S-R Flip-Flop'
 * '<S68>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Compare To Constant1'
 * '<S69>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Compare To Constant2'
 * '<S70>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Compare To Zero'
 * '<S71>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Compare To Zero1'
 * '<S72>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect Increase'
 * '<S73>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect Increase1'
 * '<S74>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect Increase2'
 * '<S75>'  : 'CtrlLog_Mdl/ControlLogic/PositionDetect/movementAllowed/Detect Increase3'
 * '<S76>'  : 'CtrlLog_Mdl/ControlLogic/Subsystem/DocBlock'
 * '<S77>'  : 'CtrlLog_Mdl/ControlLogic/Subsystem/determineAutoStepwiseMovementActive'
 * '<S78>'  : 'CtrlLog_Mdl/ControlLogic/Subsystem/stepWiseTarget'
 * '<S79>'  : 'CtrlLog_Mdl/ControlLogic/Subsystem/determineAutoStepwiseMovementActive/Compare To Constant3'
 * '<S80>'  : 'CtrlLog_Mdl/ControlLogic/Subsystem/determineAutoStepwiseMovementActive/S-R Flip-Flop'
 * '<S81>'  : 'CtrlLog_Mdl/ControlLogic/Subsystem/stepWiseTarget/Compare To Constant2'
 * '<S82>'  : 'CtrlLog_Mdl/ControlLogic/Subsystem/stepWiseTarget/DocBlock'
 * '<S83>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Constant'
 * '<S84>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Constant1'
 * '<S85>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Constant2'
 * '<S86>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Constant3'
 * '<S87>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Constant4'
 * '<S88>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Constant5'
 * '<S89>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Constant6'
 * '<S90>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/Compare To Zero'
 * '<S91>'  : 'CtrlLog_Mdl/ControlLogic/determineIsRenormMovement/DocBlock'
 */
#endif                                 /* RTW_HEADER_CtrlLog_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
