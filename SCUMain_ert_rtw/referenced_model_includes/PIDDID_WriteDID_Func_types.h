/*
 * File: PIDDID_WriteDID_Func_types.h
 *
 * Code generated for Simulink model 'PIDDID_WriteDID_Func'.
 *
 * Model version                  : 7.489
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:36:43 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PIDDID_WriteDID_Func_types_h_
#define RTW_HEADER_PIDDID_WriteDID_Func_types_h_
#include "PIDDID_ExpTypes.h"
#include "rtwtypes.h"
#include "PIDDID_MtrMdlBus.h"
#include "LearnAdp_ExpTypes.h"
#include "PIDDID_LearnAdapBus.h"
#include "BlockDet_ExpTypes.h"
#include "PIDDID_BlockDetBus.h"
#include "PIDDID_AmbTempMonBus.h"
#include "PIDDID_PosMonBus.h"
#include "VoltMon_ExpTypes.h"
#include "PIDDID_VoltMonBus.h"
#include "PIDDID_MOSFETTempBus.h"
#include "ThermProt_ExpTypes.h"
#include "PIDDID_ThermProtBus.h"
#include "RoofOper_ExpTypes.h"
#include "PIDDID_PnlOpBus.h"
#include "RoofSys_CommDefines.h"
#include "PIDDID_MtrCtrlBus.h"
#include "CtrlLogic_ExpTypes.h"
#include "PIDDID_CtrlLogBus.h"
#include "PIDDID_UsgHistBus.h"
#include "HallDeco_ExpTypes.h"
#include "PIDDID_HallDecoBus.h"
#include "PIDDID_ADCMonBus.h"
#include "StateMach_ExpTypes.h"
#include "PIDDID_StateMachBus.h"
#include "PIDDID_DIOCtrlBus.h"
#include "PIDDID_ExtDevBus.h"
#include "PIDDIDBus.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "UsgHist_ExpTypes.h"
#include "crc_driver.h"
#include "CfgParam_TThermThreshBus_t.h"
#include "CfgParam_TThermThreshBus_MosfetLevels.h"
#ifndef DEFINED_TYPEDEF_FOR_SysFltDiag_LastStopReason_t_
#define DEFINED_TYPEDEF_FOR_SysFltDiag_LastStopReason_t_

typedef uint8_T SysFltDiag_LastStopReason_t;

/* enum SysFltDiag_LastStopReason_t */
#define NoFault_C                      ((SysFltDiag_LastStopReason_t)0U) /* Default value */
#define Debugger_C                     ((SysFltDiag_LastStopReason_t)1U)
#define DiagReq_C                      ((SysFltDiag_LastStopReason_t)2U)
#define PanicStp_C                     ((SysFltDiag_LastStopReason_t)3U)
#define ThermPrtMtr_C                  ((SysFltDiag_LastStopReason_t)4U)
#define ThermPrtMosfet_C               ((SysFltDiag_LastStopReason_t)5U)
#define RelaxMechComplt_C              ((SysFltDiag_LastStopReason_t)6U)
#define AtsRevComplt_C                 ((SysFltDiag_LastStopReason_t)7U)
#define MovTimeout_C                   ((SysFltDiag_LastStopReason_t)8U)
#define ClassEOV_C                     ((SysFltDiag_LastStopReason_t)16U)
#define ClassEUV_C                     ((SysFltDiag_LastStopReason_t)17U)
#define BattVltNonPlsbl_C              ((SysFltDiag_LastStopReason_t)18U)
#define AmbTmpNonPlsbl_C               ((SysFltDiag_LastStopReason_t)19U)
#define MosfetTempNonPlsbl_C           ((SysFltDiag_LastStopReason_t)20U)
#define SysIC_CommFlt_C                ((SysFltDiag_LastStopReason_t)21U)
#define LINCommFlt_C                   ((SysFltDiag_LastStopReason_t)22U)
#define SysICFailSafe_C                ((SysFltDiag_LastStopReason_t)23U)
#define ChargePumpFlt_C                ((SysFltDiag_LastStopReason_t)24U)
#define HSSuppFlt_C                    ((SysFltDiag_LastStopReason_t)25U)
#define VsIntFlt_C                     ((SysFltDiag_LastStopReason_t)26U)
#define VsSuppFlt_C                    ((SysFltDiag_LastStopReason_t)27U)
#define VCC1Flt_C                      ((SysFltDiag_LastStopReason_t)28U)
#define RPInvalidFlt_C                 ((SysFltDiag_LastStopReason_t)29U)
#define HallFlt_C                      ((SysFltDiag_LastStopReason_t)30U)
#define SysICThermShdFlt_C             ((SysFltDiag_LastStopReason_t)31U)
#define MtrCtrlFlt_C                   ((SysFltDiag_LastStopReason_t)35U)
#define HallSuppFlt_C                  ((SysFltDiag_LastStopReason_t)37U)
#define UnderVotlage_C                 ((SysFltDiag_LastStopReason_t)48U)
#define OverVoltage_C                  ((SysFltDiag_LastStopReason_t)49U)
#define StallDurAtsRev_RlxMech_C       ((SysFltDiag_LastStopReason_t)50U)
#define OutsideEnvCond_C               ((SysFltDiag_LastStopReason_t)51U)
#define TargetPosRchd_C                ((SysFltDiag_LastStopReason_t)52U)
#define PrevReason_C                   (MAX_uint8_T)
#endif

#ifndef DEFINED_TYPEDEF_FOR_SysFaultReacBus_
#define DEFINED_TYPEDEF_FOR_SysFaultReacBus_

/* Fault reaciton bus */
typedef struct {
  /* Sys reaction bit */
  uint16_T SysFltRctn_stsSysFaultReaction;

  /* Unlearn reason */
  uint16_T SysFltRctn_stsUnlearnReason;

  /* Learn Fail reason */
  uint16_T SysFltRctn_stsLearnFailReason;
  uint8_T SysFltRctn_DTCStatus[3];
  SysFltDiag_LastStopReason_t SysFltRctn_stsLasStpReason;
  uint8_T SysFltRctn_stLast10DTCs[10];
} SysFaultReacBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SWC_DiagLyrBus_
#define DEFINED_TYPEDEF_FOR_SWC_DiagLyrBus_

/* Bus object for VehComm signals */
typedef struct {
  SysFaultReacBus SysFaultReacBus;

  /* Authorization Status (Enabled Command) from Vehicle communication
     0: Disable
     1: Enable */
  PIDDIDBus PIDDIDBus;
} SWC_DiagLyrBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_PIDDID_PCB_SensorBus_
#define DEFINED_TYPEDEF_FOR_PIDDID_PCB_SensorBus_

typedef struct {
  int16_T PIDDID_TAmbTempOffset;
  int16_T PIDDID_TMOSFETTempOffset;
  int16_T PIDDID_u12VBattInstOffset;
} PIDDID_PCB_SensorBus;

#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_PIDDID_WriteDID_Func_T RT_MODEL_PIDDID_WriteDID_Func_T;

#endif                            /* RTW_HEADER_PIDDID_WriteDID_Func_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
