/*
 * File: HALLDeco_Mdl_ISR.h
 *
 * Code generated for Simulink model 'HALLDeco_Mdl_ISR'.
 *
 * Model version                  : 7.47
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:32:27 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_HALLDeco_Mdl_ISR_h_
#define RTW_HEADER_HALLDeco_Mdl_ISR_h_
#ifndef HALLDeco_Mdl_ISR_COMMON_INCLUDES_
#define HALLDeco_Mdl_ISR_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* HALLDeco_Mdl_ISR_COMMON_INCLUDES_ */

#include "HALLDeco_Mdl_ISR_types.h"
#include "zero_crossing_types.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'HALLDeco_Mdl_ISR' */
#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T filteredPulseTicks;         /* '<S8>/Switch1' */
  uint16_T filteredPulseTicks_f;       /* '<S8>/Divide1' */
  boolean_T TmpSignalConversionAtTriggerSpeedCalculationInport1;/* '<S7>/CompareToConstant1' */
  boolean_T isMotorDirectionCW;        /* '<S5>/Switch' */
} B_HALLDeco_Mdl_ISR_c_T;

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'HALLDeco_Mdl_ISR' */
#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T UnitDelay_DSTATE;           /* '<S8>/UnitDelay' */
  uint16_T TappedDelay2_X[11];         /* '<S8>/TappedDelay2' */
  uint8_T HallCountDelay_DSTATE;       /* '<S4>/HallCountDelay' */
  boolean_T LastPulseMemory_DSTATE[2]; /* '<S5>/LastPulseMemory' */
} DW_HALLDeco_Mdl_ISR_f_T;

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'HALLDeco_Mdl_ISR' */
#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState Capture_Diff_Trig_ZCE;    /* '<S3>/Capture_Diff ' */
} ZCE_HALLDeco_Mdl_ISR_T;

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'HALLDeco_Mdl_ISR' */
#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

typedef struct {
  const uint8_T Width1;                /* '<S8>/Width1' */
} ConstB_HALLDeco_Mdl_ISR_h_T;

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_HALLDeco_Mdl_ISR_T {
  const char_T **errorStatus;
};

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_HALLDeco_Mdl_ISR_T rtm;
} MdlrefDW_HALLDeco_Mdl_ISR_T;

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint32_T HallDeco_sumFilterdPulseTicks;
                    /* Simulink.Signal object 'HallDeco_sumFilterdPulseTicks' */
extern void HALLDeco_Mdl_ISR_Init(void);
extern void HALLDeco_Mdl_ISR(const uint8_T *rtu_channelId, const uint16_T
  *rtu_inputCaptureValue, const boolean_T *rtu_isChannelRising, uint8_T
  *rty_HallCounts, boolean_T *rty_Direction, uint16_T *rty_PulseTime, uint8_T
  rtp_ChannelIdSensor1);

/* Model reference registration function */
extern void HALLDeco_Mdl_ISR_initialize(const char_T **rt_errorStatus);

#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

extern void HALLDeco_Mdl_ISR_Capture_Diff_Init(void);
extern void HALLDeco_Mdl_ISR_Capture_Diff(void);
extern void HALLDeco_Mdl_ISR_CountPulse(void);
extern void HALLDeco_Mdl_ISR_Determine_Dir(const uint8_T *rtu_ISR_u8ChannelId,
  const boolean_T *rtu_ISR_isRising, boolean_T *rty_isMotorDirectionCW);
extern void HALLDeco_Mdl_ISR_Spd_Snsr_Detection(const uint8_T *rtu_channelId,
  boolean_T *rty_TriggerSpeedCalculation);

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

extern MdlrefDW_HALLDeco_Mdl_ISR_T HALLDeco_Mdl_ISR_MdlrefDW;

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_HALLDeco_Mdl_ISR_c_T HALLDeco_Mdl_ISR_B;

/* Block states (default storage) */
extern DW_HALLDeco_Mdl_ISR_f_T HALLDeco_Mdl_ISR_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_HALLDeco_Mdl_ISR_T HALLDeco_Mdl_ISR_PrevZCX;

#endif                                 /*HALLDeco_Mdl_ISR_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'HALLDeco_Mdl_ISR'
 * '<S1>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR'
 * '<S2>'   : 'HALLDeco_Mdl_ISR/ModelInfo'
 * '<S3>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Capture_Diff'
 * '<S4>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/CountPulse'
 * '<S5>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Determine_Dir'
 * '<S6>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/DocBlock'
 * '<S7>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Spd_Snsr_Detection'
 * '<S8>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Capture_Diff/Capture_Diff '
 * '<S9>'   : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Capture_Diff/Capture_Diff /DocBlock'
 * '<S10>'  : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/CountPulse/DocBlock'
 * '<S11>'  : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Determine_Dir/CompareToConstant1'
 * '<S12>'  : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Determine_Dir/DocBlock'
 * '<S13>'  : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Spd_Snsr_Detection/CompareToConstant1'
 * '<S14>'  : 'HALLDeco_Mdl_ISR/HALLDeco_ISR/Spd_Snsr_Detection/DocBlock'
 */
#endif                                 /* RTW_HEADER_HALLDeco_Mdl_ISR_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
