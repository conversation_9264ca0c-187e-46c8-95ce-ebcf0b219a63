/*
 * File: ExtDev_Mdl_types.h
 *
 * Code generated for Simulink model 'ExtDev_Mdl'.
 *
 * Model version                  : 1.59
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:31:42 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ExtDev_Mdl_types_h_
#define RTW_HEADER_ExtDev_Mdl_types_h_
#include "StateMach_ExpTypes.h"
#include "rtwtypes.h"
#ifndef DEFINED_TYPEDEF_FOR_StateMachBus_
#define DEFINED_TYPEDEF_FOR_StateMachBus_

/* Bus object for State Machine signals
   already Present. */
typedef struct {
  /* Sunroof System Mode */
  StateMach_Mode_t StateMach_stSysMode;
} StateMachBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SWC_StateMachLyrBus_
#define DEFINED_TYPEDEF_FOR_SWC_StateMachLyrBus_

typedef struct {
  StateMachBus StateMachBus;
} SWC_StateMachLyrBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ExtDevBus_
#define DEFINED_TYPEDEF_FOR_ExtDevBus_

typedef struct {
  boolean_T ExtDev_isMtrFebkCtrlHS1Status;
  boolean_T ExtDev_isHallSuppHS2Status;
  boolean_T ExtDev_isSwtchSuppHS3Status;
  uint8_T ExtDev_u8GetPWMDutyCycleVal;
} ExtDevBus;

#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_ExtDev_Mdl_T RT_MODEL_ExtDev_Mdl_T;

#endif                                 /* RTW_HEADER_ExtDev_Mdl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
