/*
 * File: DIOCtrl_Mdl_private.h
 *
 * Code generated for Simulink model 'DIOCtrl_Mdl'.
 *
 * Model version                  : 1.199
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:31:19 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_DIOCtrl_Mdl_private_h_
#define RTW_HEADER_DIOCtrl_Mdl_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "DIOCtrl_Mdl_types.h"
#include "StateMach_ExpTypes.h"
#include "DIOCtrl_Mdl.h"

/* Includes for objects with custom storage classes */
#include "pin_mux.h"

/*
 * Check that imported macros with storage class "ImportedDefine" are defined
 */
#ifndef HALL_OUT_PIN
#error The value of parameter "HALL_OUT_PIN" is not defined
#endif

#ifndef MTR1_FLT_BANK_A_PIN
#error The value of parameter "MTR1_FLT_BANK_A_PIN" is not defined
#endif

#ifndef MTR1_FLT_BANK_B_PIN
#error The value of parameter "MTR1_FLT_BANK_B_PIN" is not defined
#endif

#ifndef VBAT_ADC_EN_PIN
#error The value of parameter "VBAT_ADC_EN_PIN" is not defined
#endif

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
#define rtmGetErrorStatus(rtm)         (*((rtm)->errorStatus))
#endif

#ifndef rtmSetErrorStatus
#define rtmSetErrorStatus(rtm, val)    (*((rtm)->errorStatus) = (val))
#endif

#ifndef rtmGetErrorStatusPointer
#define rtmGetErrorStatusPointer(rtm)  (rtm)->errorStatus
#endif

#ifndef rtmSetErrorStatusPointer
#define rtmSetErrorStatusPointer(rtm, val) ((rtm)->errorStatus = (val))
#endif

#include "DioCtrl_Man.h"
#endif                                 /* RTW_HEADER_DIOCtrl_Mdl_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
