/*
 * File: PIDDID_WriteDID_Func.h
 *
 * Code generated for Simulink model 'PIDDID_WriteDID_Func'.
 *
 * Model version                  : 7.489
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:36:43 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PIDDID_WriteDID_Func_h_
#define RTW_HEADER_PIDDID_WriteDID_Func_h_
#ifndef PIDDID_WriteDID_Func_COMMON_INCLUDES_
#define PIDDID_WriteDID_Func_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                               /* PIDDID_WriteDID_Func_COMMON_INCLUDES_ */

#include "VoltMon_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "PIDDID_WriteDID_Func_types.h"
#include "ThermProt_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "LearnAdp_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "UsgHist_ExpTypes.h"
#include "PIDDID_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"
#include "PIDDID_Exp.h"
#include "UsgHist_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'PIDDID_WriteDID_Func' */
#ifndef PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T DataTypeConversion1;        /* '<Root>/Data Type Conversion1' */
  uint16_T UsgHist_SlideCycCount;      /* '<Root>/WriteDID' */
  uint8_T isShdnRdy;                   /* '<S11>/S-Function' */
  uint8_T HWYear;                      /* '<Root>/WriteDID' */
  uint8_T HWWeek;                      /* '<Root>/WriteDID' */
  uint8_T HWPatch;                     /* '<Root>/WriteDID' */
} B_PIDDID_WriteDID_Func_c_T;

#endif                               /*PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'PIDDID_WriteDID_Func' */
#ifndef PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T Unit_Delay_DSTATE;           /* '<Root>/Unit_Delay' */
  boolean_T DelayInput1_DSTATE;        /* '<S9>/Delay Input1' */
} DW_PIDDID_WriteDID_Func_f_T;

#endif                               /*PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_PIDDID_WriteDID_Func_T {
  const char_T **errorStatus;
};

#endif                               /*PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_PIDDID_WriteDID_Func_T rtm;
} MdlrefDW_PIDDID_WriteDID_Func_T;

#endif                               /*PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern CfgParam_HWVerInfoBus_t CfgParam_HWVerInfo;
                               /* Simulink.Signal object 'CfgParam_HWVerInfo' */
extern PIDDID_PCB_SensorBus PIDDID_PCB_Sensor;
                                /* Simulink.Signal object 'PIDDID_PCB_Sensor' */
extern uint16_T LearnAdap_renormCycleCounter;
                     /* Simulink.Signal object 'LearnAdap_renormCycleCounter' */
extern uint8_T CfgParam_DiagTraceMemory[12];
                         /* Simulink.Signal object 'CfgParam_DiagTraceMemory' */
extern uint8_T CfgParam_HWVersion[2];
                                  /* Simulink.Signal object 'CfgParam_HWVersion'
                                   * Hardware version

                                     Hardware Major version constant in ASCII
                                     Hardware minor version constant in Decimal

                                   */
extern uint8_T CfgParam_MBCECUIDHWPartNum[10];
                       /* Simulink.Signal object 'CfgParam_MBCECUIDHWPartNum' */
extern uint8_T CfgParam_MtrSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_MtrSerialNum'
                                * Motor Serial Number in Decimal
                                */
extern uint8_T CfgParam_RoofPartNum[9];
                                /* Simulink.Signal object 'CfgParam_RoofPartNum'
                                 * Roof Part Number in ASCII
                                 */
extern uint8_T CfgParam_RoofSerialNum[6];
                              /* Simulink.Signal object 'CfgParam_RoofSerialNum'
                               * Roof serial number Version
                               */
extern uint8_T CfgParam_SCUPartNum[9];
                                 /* Simulink.Signal object 'CfgParam_SCUPartNum'
                                  * SCU Part Number in ASCII
                                  */
extern uint8_T CfgParam_SCUSerialNum[6];
                               /* Simulink.Signal object 'CfgParam_SCUSerialNum'
                                * SCU Serial Number in Decimal
                                */
extern boolean_T PIDDID_isFactoryMode;
                                /* Simulink.Signal object 'PIDDID_isFactoryMode'
                                 * Diagnostic Factory Mode
                                   1 - Enabled
                                   0 -Disabled

                                 */
extern SysFltDiag_LastStopReason_t SysFltRctn_stLastStopReason;
                      /* Simulink.Signal object 'SysFltRctn_stLastStopReason' */
extern void PIDDID_WriteDID_Func_Init(void);
extern void PIDDID_WriteDID_Func(const uint8_T rtu_srcBuf[252], const uint16_T
  *rtu_wrDID, const VoltMon_VoltClass_t
  *rtu_SWC_DiagLyrBus1_PIDDIDBus_VoltMon_VoltMon_stVoltClass, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_DiagLyrBus1_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand, const uint16_T
  *rtu_SWC_DiagLyrBus1_PIDDIDBus_ADCMon_ADCMon_u12VBattInst, const int16_T
  *rtu_SWC_DiagLyrBus1_PIDDIDBus_ADCMon_ADCMon_TAmbTemp, const int16_T
  *rtu_SWC_DiagLyrBus1_PIDDIDBus_ADCMon_ADCMon_TMOSFETTemp, uint8_T *rty_retVal);

/* Model reference registration function */
extern void PIDDID_WriteDID_Func_initialize(const char_T **rt_errorStatus);

#ifndef PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_

extern MdlrefDW_PIDDID_WriteDID_Func_T PIDDID_WriteDID_Func_MdlrefDW;

#endif                               /*PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_PIDDID_WriteDID_Func_c_T PIDDID_WriteDID_Func_B;

/* Block states (default storage) */
extern DW_PIDDID_WriteDID_Func_f_T PIDDID_WriteDID_Func_DW;

#endif                               /*PIDDID_WriteDID_Func_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PIDDID_WriteDID_Func'
 * '<S1>'   : 'PIDDID_WriteDID_Func/Compare To Constant'
 * '<S2>'   : 'PIDDID_WriteDID_Func/DocBlock'
 * '<S3>'   : 'PIDDID_WriteDID_Func/ModelInfo'
 * '<S4>'   : 'PIDDID_WriteDID_Func/RunAdvise'
 * '<S5>'   : 'PIDDID_WriteDID_Func/Update_WrResp'
 * '<S6>'   : 'PIDDID_WriteDID_Func/WriteDID'
 * '<S7>'   : 'PIDDID_WriteDID_Func/Write_EEPData'
 * '<S8>'   : 'PIDDID_WriteDID_Func/Update_WrResp/DocBlock'
 * '<S9>'   : 'PIDDID_WriteDID_Func/Write_EEPData/Detect Rise Positive'
 * '<S10>'  : 'PIDDID_WriteDID_Func/Write_EEPData/DocBlock'
 * '<S11>'  : 'PIDDID_WriteDID_Func/Write_EEPData/NVMWrite'
 * '<S12>'  : 'PIDDID_WriteDID_Func/Write_EEPData/Detect Rise Positive/Positive'
 * '<S13>'  : 'PIDDID_WriteDID_Func/Write_EEPData/NVMWrite/DocBlock'
 */
#endif                                 /* RTW_HEADER_PIDDID_WriteDID_Func_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
