/*
 * File: DiagComMan_types.h
 *
 * Code generated for Simulink model 'DiagComMan'.
 *
 * Model version                  : 1.25
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:31:29 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_DiagComMan_types_h_
#define RTW_HEADER_DiagComMan_types_h_
#include "rtwtypes.h"
#ifndef DEFINED_TYPEDEF_FOR_DiagComManBus_
#define DEFINED_TYPEDEF_FOR_DiagComManBus_

typedef struct {
  boolean_T Dummy;
} DiagComManBus;

#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_DiagComMan_T RT_MODEL_DiagComMan_T;

#endif                                 /* RTW_HEADER_DiagComMan_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
