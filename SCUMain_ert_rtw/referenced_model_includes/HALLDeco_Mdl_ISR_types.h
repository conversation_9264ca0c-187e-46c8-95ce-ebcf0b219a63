/*
 * File: HALLDeco_Mdl_ISR_types.h
 *
 * Code generated for Simulink model 'HALLDeco_Mdl_ISR'.
 *
 * Model version                  : 7.47
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:32:27 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_HALLDeco_Mdl_ISR_types_h_
#define RTW_HEADER_HALLDeco_Mdl_ISR_types_h_

/* Forward declaration for rtModel */
typedef struct tag_RTM_HALLDeco_Mdl_ISR_T RT_MODEL_HALLDeco_Mdl_ISR_T;

#endif                                /* RTW_HEADER_HALLDeco_Mdl_ISR_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
