/*
 * File: HALLDeco_Mdl_ISR_private.h
 *
 * Code generated for Simulink model 'HALLDeco_Mdl_ISR'.
 *
 * Model version                  : 7.47
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:32:27 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_HALLDeco_Mdl_ISR_private_h_
#define RTW_HEADER_HALLDeco_Mdl_ISR_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "HALLDeco_Mdl_ISR.h"
#include "HALLDeco_Mdl_ISR_types.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
#define rtmGetErrorStatus(rtm)         (*((rtm)->errorStatus))
#endif

#ifndef rtmSetErrorStatus
#define rtmSetErrorStatus(rtm, val)    (*((rtm)->errorStatus) = (val))
#endif

#ifndef rtmGetErrorStatusPointer
#define rtmGetErrorStatusPointer(rtm)  (rtm)->errorStatus
#endif

#ifndef rtmSetErrorStatusPointer
#define rtmSetErrorStatusPointer(rtm, val) ((rtm)->errorStatus = (val))
#endif

/* Invariant block signals (default storage) */
extern const ConstB_HALLDeco_Mdl_ISR_h_T HALLDeco_Mdl_ISR_ConstB;

#endif                              /* RTW_HEADER_HALLDeco_Mdl_ISR_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
