/*
 * File: PIDDID_RoutineCtrl_Func.h
 *
 * Code generated for Simulink model 'PIDDID_RoutineCtrl_Func'.
 *
 * Model version                  : 7.249
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:36:22 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PIDDID_RoutineCtrl_Func_h_
#define RTW_HEADER_PIDDID_RoutineCtrl_Func_h_
#ifndef PIDDID_RoutineCtrl_Func_COMMON_INCLUDES_
#define PIDDID_RoutineCtrl_Func_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                            /* PIDDID_RoutineCtrl_Func_COMMON_INCLUDES_ */

#include "VoltMon_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "PIDDID_RoutineCtrl_Func_types.h"
#include "RoofSys_CommDefines.h"
#include "PIDDID_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"
#include "LearnAdap_Exp.h"
#include "PIDDID_Exp.h"
#include "ThermProt_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'PIDDID_RoutineCtrl_Func' */
#ifndef PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T hAutoCycle_PauseTimeFCls;   /* '<Root>/RoutineCtrl' */
  uint16_T hAutoCycle_PauseTimeFOpn;   /* '<Root>/RoutineCtrl' */
  uint8_T SFunction;                   /* '<S1>/S-Function' */
  uint8_T SFunction2;                  /* '<S1>/S-Function2' */
  uint8_T SFunction3;                  /* '<S1>/S-Function3' */
  uint8_T SFunction1;                  /* '<S1>/S-Function1' */
  uint8_T SFunction4;                  /* '<S1>/S-Function4' */
  boolean_T PIDDID_isHallOutputEnabled;/* '<Root>/RoutineCtrl' */
} B_PIDDID_RoutineCtrl_Func_c_T;

#endif                            /*PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'PIDDID_RoutineCtrl_Func' */
#ifndef PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T result;                      /* '<Root>/RoutineCtrl' */
  uint8_T ATSresult;                   /* '<Root>/RoutineCtrl' */
  uint8_T Hallresult;                  /* '<Root>/RoutineCtrl' */
} DW_PIDDID_RoutineCtrl_Func_f_T;

#endif                            /*PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_PIDDID_RoutineCtrl_Func_T {
  const char_T **errorStatus;
};

#endif                            /*PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_PIDDID_RoutineCtrl_Func_T rtm;
} MdlrefDW_PIDDID_RoutineCtrl_Func_T;

#endif                            /*PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T PIDDID_ControlIntermediatePos;
                    /* Simulink.Signal object 'PIDDID_ControlIntermediatePos' */
extern uint16_T PIDDID_SoftClosePos;
                              /* Simulink.Signal object 'PIDDID_SoftClosePos' */
extern uint16_T PIDDID_SoftOpenPos;
                               /* Simulink.Signal object 'PIDDID_SoftOpenPos' */
extern uint16_T PIDDID_SoftStartPoint1Time;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Time' */
extern uint16_T PIDDID_SoftStopPoint1Time;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Time' */
extern uint16_T PIDDID_uAutoCyclePauseTime[2];
                       /* Simulink.Signal object 'PIDDID_uAutoCyclePauseTime' */
extern uint8_T PIDDID_SoftStartPoint1Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint1Perc' */
extern uint8_T PIDDID_SoftStartPoint2Perc;
                       /* Simulink.Signal object 'PIDDID_SoftStartPoint2Perc' */
extern uint8_T PIDDID_SoftStopPoint1Perc;
                        /* Simulink.Signal object 'PIDDID_SoftStopPoint1Perc' */
extern uint8_T SysFltRctn_stDTCCounter_Byte1[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte1' */
extern uint8_T SysFltRctn_stDTCCounter_Byte2[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte2' */
extern uint8_T SysFltRctn_stDTCCounter_Byte3[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte3' */
extern uint8_T SysFltRctn_stDTCStatus[3];
                           /* Simulink.Signal object 'SysFltRctn_stDTCStatus' */
extern uint8_T SysFltRctn_stLast10DTCs[10];
                          /* Simulink.Signal object 'SysFltRctn_stLast10DTCs' */
extern boolean_T PIDDID_HallOutputDisabled;
                           /* Simulink.Signal object 'PIDDID_HallOutputDisabled'
                            * Diagnostic Routine Hall output Enabled/Disabled
                              0: Enabled
                              1: Disabled

                            */
extern boolean_T PIDDID_isAutoCycleActive;
                         /* Simulink.Signal object 'PIDDID_isAutoCycleActive' */
extern boolean_T PIDDID_isSoftStartEna;
                            /* Simulink.Signal object 'PIDDID_isSoftStartEna' */
extern boolean_T PIDDID_isSoftStopEna;
                             /* Simulink.Signal object 'PIDDID_isSoftStopEna' */
extern void PIDDID_RoutineCtrl_Func_Init(uint8_T rty_TxData[10]);
extern void PIDDID_RoutineCtrl_Func(const uint16_T *rtu_rtnId, const uint8_T
  *rtu_rtnType, const uint8_T *rtu_rtnLen, const uint8_T rtu_rtnOption[10],
  const boolean_T
  *rtu_SWC_DiagLyrBus_PIDDIDBus_LearnAdap_LearnAdap_isLearningAllowed, const
  boolean_T *rtu_SWC_DiagLyrBus_PIDDIDBus_PosMon_PosMon_isCurPosVld, const
  VoltMon_VoltClass_t *rtu_SWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_stVoltClass,
  const CtrlLog_tenTargetDirection
  *rtu_SWC_DiagLyrBus_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand, const boolean_T
  *rtu_SWC_DiagLyrBus_PIDDIDBus_HallDeco_isHallSupplyFault, uint8_T *rty_retVal,
  uint8_T rty_TxData[10]);

/* Model reference registration function */
extern void PIDDID_RoutineCtrl_Func_initialize(const char_T **rt_errorStatus);

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern boolean_T PIDDID_isCyclicRenormSuppress;
extern boolean_T PIDDID_isDummyRPRtnEna;
extern boolean_T PIDDID_isIntermediatePositionRtnEna;
extern boolean_T PIDDID_isOpenClosePositionRtnEna;
extern boolean_T PIDDID_isOverridePWMEnable;

#ifndef PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_

extern MdlrefDW_PIDDID_RoutineCtrl_Func_T PIDDID_RoutineCtrl_Func_MdlrefDW;

#endif                            /*PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_PIDDID_RoutineCtrl_Func_c_T PIDDID_RoutineCtrl_Func_B;

/* Block states (default storage) */
extern DW_PIDDID_RoutineCtrl_Func_f_T PIDDID_RoutineCtrl_Func_DW;

#endif                            /*PIDDID_RoutineCtrl_Func_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PIDDID_RoutineCtrl_Func'
 * '<S1>'   : 'PIDDID_RoutineCtrl_Func/Clear DTC'
 * '<S2>'   : 'PIDDID_RoutineCtrl_Func/DocBlock'
 * '<S3>'   : 'PIDDID_RoutineCtrl_Func/Model Info'
 * '<S4>'   : 'PIDDID_RoutineCtrl_Func/RoutineCtrl'
 */
#endif                               /* RTW_HEADER_PIDDID_RoutineCtrl_Func_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
