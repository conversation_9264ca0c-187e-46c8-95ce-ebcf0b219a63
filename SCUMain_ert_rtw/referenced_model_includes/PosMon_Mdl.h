/*
 * File: PosMon_Mdl.h
 *
 * Code generated for Simulink model 'PosMon_Mdl'.
 *
 * Model version                  : 1.404
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:38:33 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PosMon_Mdl_h_
#define RTW_HEADER_PosMon_Mdl_h_
#ifndef PosMon_Mdl_COMMON_INCLUDES_
#define PosMon_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* PosMon_Mdl_COMMON_INCLUDES_ */

#include "VoltMon_ExpTypes.h"
#include "PosMon_Mdl_types.h"
#include "NvM_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "BlockDet_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "HallDeco_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "PosMon_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'PosMon_Mdl' */
#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  POSMON_STATEModeType POSMON_STATE;   /* '<S4>/PositionState' */
  int16_T RP_beforeReset;              /* '<S4>/PositionState' */
  uint8_T SFunction;                   /* '<S25>/S-Function' */
  boolean_T isNVMPosVldOut;            /* '<S4>/PositionState' */
  boolean_T isCurPosVld;               /* '<S4>/PositionState' */
  boolean_T PosMon_isRelearnOut;       /* '<S4>/PositionState' */
  NVMMgmt_ReqCmdType NvmAccess;        /* '<S4>/PositionState' */
} B_PosMon_Mdl_c_T;

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'PosMon_Mdl' */
#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  stdReturn_t Previous_NVM_State_DSTATE;/* '<S2>/Previous_NVM_State' */
  uint32_T temporalCounter_i1;         /* '<S4>/PositionState' */
  uint8_T UD_DSTATE;                   /* '<S18>/UD' */
  boolean_T UnitDelay_DSTATE;          /* '<S7>/Unit Delay' */
  uint8_T is_c3_PosMon_Mdl;            /* '<S4>/PositionState' */
  uint8_T is_NORMAL_OPERATION;         /* '<S4>/PositionState' */
  uint8_T is_active_c3_PosMon_Mdl;     /* '<S4>/PositionState' */
  LearnAdap_Req_t stPosReq_prev;       /* '<S4>/PositionState' */
  LearnAdap_Req_t stPosReq_start;      /* '<S4>/PositionState' */
} DW_PosMon_Mdl_f_T;

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'PosMon_Mdl' */
#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const PosMon_PnlPosArea_t
    TmpBufferAtTmpGroundAtPosMon_panelCurPosAreaInport1Outport1;
  const PosMon_Area_t TmpBufferAtTmpGroundAtPosMon_curPanelAreaInport1Outport1;
} ConstB_PosMon_Mdl_h_T;

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_PosMon_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_PosMon_Mdl_T rtm;
} MdlrefDW_PosMon_Mdl_T;

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern stdReturn_t NVMAccessState; /* Simulink.Signal object 'NVMAccessState' */
extern uint16_T PIDDID_ControlIntermediatePos;
                    /* Simulink.Signal object 'PIDDID_ControlIntermediatePos' */
extern uint16_T PIDDID_SoftClosePos;
                              /* Simulink.Signal object 'PIDDID_SoftClosePos' */
extern uint16_T PIDDID_SoftOpenPos;
                               /* Simulink.Signal object 'PIDDID_SoftOpenPos' */
extern boolean_T PosMon_isCurPosVldNvm;
                               /* Simulink.Signal object 'PosMon_isCurPosVldNvm'
                                * Current position validity NVM:
                                  0 invalid
                                  1: valid
                                */
extern boolean_T PosMon_isRelearn;
                                 /* Simulink.Signal object 'PosMon_isRelearn' */
extern void PosMon_Mdl_Init(void);
extern void PosMon_Mdl(const LearnAdap_Mode_t
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode, const LearnAdap_Req_t
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq, const uint16_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop, const uint16_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newSoftStop, const boolean_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete, const uint8_T
  *rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_stCALFileFlt, const boolean_T
  *rtu_SWC_DataHndlLyrBus_CfgParamBus_CfgParam_isStartupRdy, const
  VoltMon_VoltClass_t *rtu_VoltMonBus_VoltMon_stVoltClass, const uint8_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_uiHallCountsCyclic, boolean_T
  *rty_PosMonBus_PosMon_isCurPosVld, boolean_T
  *rty_PosMonBus_PosMon_isCurPosVldNvm, int16_T
  *rty_PosMonBus_PosMon_hCurPosSigned, boolean_T *rty_PosMonBus_PosMon_isRelearn,
  int16_T *rty_PosMonBus_PosMon_stPosSave, uint16_T
  *rty_PosMonBus_PosMon_hCurPos, PosMon_Area_t
  *rty_PosMonBus_PosMon_curPanelArea, PosMon_PnlPosArea_t
  *rty_PosMonBus_PosMon_panelCurPosArea, boolean_T
  *rty_PosMonBus_PosMon_isShdnRdy, boolean_T *rty_PosMonBus_PosMon_isOutOfRange,
  boolean_T *rty_PosMonBus_PosMon_isStartupRdy, uint16_T
  *rty_PosMonBus_PosMon_relativePinionSize, uint16_T
  *rty_PosMonBus_PosMon_hPosTbl_hHardStopCls, uint16_T
  *rty_PosMonBus_PosMon_hPosTbl_hSoftStopCls, uint16_T
  *rty_PosMonBus_PosMon_hPosTbl_hNearClsSld, uint16_T
  *rty_PosMonBus_PosMon_hPosTbl_hComfortMidStop, uint16_T
  *rty_PosMonBus_PosMon_hPosTbl_hSoftStopOpn, uint16_T
  *rty_PosMonBus_PosMon_hPosTbl_hHardStopOpn);

/* Model reference registration function */
extern void PosMon_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

extern void PosMon_Mdl_CalculatePosition(const uint8_T *rtu_HallCountsCyclic,
  boolean_T rtu_typeHallDir_P, int8_T rtu_U8One_C, int8_T rtu_Int8MinusOne_C,
  const VoltMon_VoltClass_t *rtu_VoltMon_stVoltClass, int8_T rtu_ZeroU8_C,
  int16_T rtu_hCurPos_IN, boolean_T *rty_isMotorMoving, int16_T *rty_localCurPos);
extern void PosMon_Mdl_DetermineAndWriteState_Init(POSMON_STATEModeType
  *rty_PanelState, NVMMgmt_ReqCmdType *rty_NvmAccessState);
extern void PosMon_Mdl_DetermineAndWriteState(uint16_T rtu_tSmpPosMon_SC,
  boolean_T rtu_PosMon_isCurPosVld, boolean_T rtu_HallSigFlt, boolean_T
  rtu_HallSigSc, boolean_T rtu_DiagDenormReq, const uint8_T *rtu_stCALFileFlt,
  boolean_T rtu_isRelearnIn, boolean_T rtu_isMotorMoving, int16_T
  rtu_localCurPos, const LearnAdap_Req_t *rtu_stPosReq, stdReturn_t
  rtu_PosMonNVMstate, boolean_T *rty_isCurPosVld, boolean_T *rty_isNVMPosVldOut,
  int16_T *rty_stPosSave, boolean_T *rty_isRelearnOut, POSMON_STATEModeType
  *rty_PanelState, int16_T *rty_RP_beforeReset, NVMMgmt_ReqCmdType
  *rty_NvmAccessState);
extern void PosMon_Mdl_PositionOutOfRange(int16_T rtu_localCurPos, const
  CfgParam_hPosBus_t *rtu_hPosTabel, boolean_T rtu_isCurPosVld, const
  LearnAdap_Mode_t *rtu_LearnAdap_stMode, const boolean_T *rtu_isStartupRdy_In,
  uint8_T rtu_hOutOfRangeOffset_C, const boolean_T *rtu_isLearnComplete,
  boolean_T *rty_isOutOfRange, boolean_T *rty_isStartupRdy);
extern void PosMon_Mdl_ShutdownReadyDetermination(POSMON_STATEModeType
  rtu_PanelState, boolean_T *rty_isShdnRdy);
extern void PosMon_Mdl_UpdatePosition(POSMON_STATEModeType rtu_PanelState,
  int16_T rtu_RP_beforeReset, uint16_T rtu_hPosTbl_hHardStopOpn, const
  LearnAdap_Req_t *rtu_stPosReq, int16_T rtu_localCurPosIn, int16_T
  *rty_hCurPosSigned, uint16_T *rty_hCurPosUnsigned);

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern boolean_T PIDDID_isIntermediatePositionRtnEna;
extern boolean_T PIDDID_isOpenClosePositionRtnEna;

#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_PosMon_Mdl_T PosMon_Mdl_MdlrefDW;

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PosMon_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_PosMon_Mdl_c_T PosMon_Mdl_B;

/* Block states (default storage) */
extern DW_PosMon_Mdl_f_T PosMon_Mdl_DW;

#endif                                 /*PosMon_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S2>/Constant6' : Unused code path elimination
 * Block '<S2>/Constant7' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PosMon_Mdl'
 * '<S1>'   : 'PosMon_Mdl/ModelInfo'
 * '<S2>'   : 'PosMon_Mdl/PosMon'
 * '<S3>'   : 'PosMon_Mdl/PosMon/CalculatePosition'
 * '<S4>'   : 'PosMon_Mdl/PosMon/DetermineAndWriteState'
 * '<S5>'   : 'PosMon_Mdl/PosMon/DocBlock'
 * '<S6>'   : 'PosMon_Mdl/PosMon/NVM_Access'
 * '<S7>'   : 'PosMon_Mdl/PosMon/PositionOutOfRange'
 * '<S8>'   : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination'
 * '<S9>'   : 'PosMon_Mdl/PosMon/UpdatePosition'
 * '<S10>'  : 'PosMon_Mdl/PosMon/determinePositionTable'
 * '<S11>'  : 'PosMon_Mdl/PosMon/determineRelativePinionSize'
 * '<S12>'  : 'PosMon_Mdl/PosMon/overwritePositionPIDDID'
 * '<S13>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant'
 * '<S14>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant1'
 * '<S15>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant2'
 * '<S16>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Compare To Constant3'
 * '<S17>'  : 'PosMon_Mdl/PosMon/CalculatePosition/CompareToConstant'
 * '<S18>'  : 'PosMon_Mdl/PosMon/CalculatePosition/Difference'
 * '<S19>'  : 'PosMon_Mdl/PosMon/CalculatePosition/DocBlock'
 * '<S20>'  : 'PosMon_Mdl/PosMon/DetermineAndWriteState/DocBlock'
 * '<S21>'  : 'PosMon_Mdl/PosMon/DetermineAndWriteState/PositionState'
 * '<S22>'  : 'PosMon_Mdl/PosMon/NVM_Access/Compare To Constant'
 * '<S23>'  : 'PosMon_Mdl/PosMon/NVM_Access/CompareToConstant4'
 * '<S24>'  : 'PosMon_Mdl/PosMon/NVM_Access/DocBlock1'
 * '<S25>'  : 'PosMon_Mdl/PosMon/NVM_Access/Subsystem'
 * '<S26>'  : 'PosMon_Mdl/PosMon/PositionOutOfRange/Compare To Constant'
 * '<S27>'  : 'PosMon_Mdl/PosMon/PositionOutOfRange/Compare To Constant1'
 * '<S28>'  : 'PosMon_Mdl/PosMon/PositionOutOfRange/DocBlock'
 * '<S29>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/Compare To Constant'
 * '<S30>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/Compare To Constant1'
 * '<S31>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/Compare To Constant2'
 * '<S32>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/DocBlock'
 * '<S33>'  : 'PosMon_Mdl/PosMon/ShutdownReadyDetermination/DocBlock1'
 * '<S34>'  : 'PosMon_Mdl/PosMon/UpdatePosition/Compare To Constant'
 * '<S35>'  : 'PosMon_Mdl/PosMon/UpdatePosition/DocBlock'
 * '<S36>'  : 'PosMon_Mdl/PosMon/determinePositionTable/Compare To Zero2'
 * '<S37>'  : 'PosMon_Mdl/PosMon/determinePositionTable/DocBlock1'
 * '<S38>'  : 'PosMon_Mdl/PosMon/determineRelativePinionSize/DocBlock1'
 * '<S39>'  : 'PosMon_Mdl/PosMon/overwritePositionPIDDID/DocBlock1'
 */
#endif                                 /* RTW_HEADER_PosMon_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
