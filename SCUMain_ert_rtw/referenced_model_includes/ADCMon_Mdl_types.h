/*
 * File: ADCMon_Mdl_types.h
 *
 * Code generated for Simulink model 'ADCMon_Mdl'.
 *
 * Model version                  : 1.183
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:28:08 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ADCMon_Mdl_types_h_
#define RTW_HEADER_ADCMon_Mdl_types_h_
#include "rtwtypes.h"
#ifndef DEFINED_TYPEDEF_FOR_ADCMonBus_
#define DEFINED_TYPEDEF_FOR_ADCMonBus_

typedef struct {
  uint16_T ADCMon_u12VBattInst;
  uint16_T ADCMon_uSwchInst[2];
  int16_T ADCMon_TAmbTemp;
  int16_T ADCMon_TMOSFETTemp;
  uint16_T ADCMon_uHallPwrInst;
  uint16_T ADCMon_uM1AVolt_mv;
  uint16_T ADCMon_uM1BVolt_mv;
} ADCMonBus;

#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_ADCMon_Mdl_T RT_MODEL_ADCMon_Mdl_T;

#endif                                 /* RTW_HEADER_ADCMon_Mdl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
