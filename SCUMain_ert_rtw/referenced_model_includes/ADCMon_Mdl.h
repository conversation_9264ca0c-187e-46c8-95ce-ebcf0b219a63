/*
 * File: ADCMon_Mdl.h
 *
 * Code generated for Simulink model 'ADCMon_Mdl'.
 *
 * Model version                  : 1.183
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:28:08 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ADCMon_Mdl_h_
#define RTW_HEADER_ADCMon_Mdl_h_
#ifndef ADCMon_Mdl_COMMON_INCLUDES_
#define ADCMon_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* ADCMon_Mdl_COMMON_INCLUDES_ */

#include "ADCMon_Mdl_types.h"

/* Includes for objects with custom storage classes */
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'ADCMon_Mdl' */
#ifndef ADCMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T ADCMon_rawAMB_TEMP_SE11;    /* '<S5>/S-Function6' */
  uint16_T ADCMon_rawMOSF_TEMP_SE12;   /* '<S5>/S-Function4' */
  uint16_T ADCMon_rawVBAT_SE0;         /* '<S5>/S-Function' */
  uint16_T ADCMon_rawSWIF_A_SE8;       /* '<S5>/S-Function1' */
  uint16_T ADCMon_rawSWIF_B_SE9;       /* '<S5>/S-Function2' */
  uint16_T ADCMon_rawMOT_VTG_1_SE2;    /* '<S5>/S-Function3' */
  uint16_T ADCMon_rawMOT_VTG_2_SE3;    /* '<S5>/S-Function5' */
  uint16_T ADCMon_rawMOT_CURR_1_SE4;   /* '<S5>/S-Function7' */
  uint16_T ADCMon_rawMOT_CURR_2_SE5;   /* '<S5>/S-Function8' */
  uint16_T ADCMon_rawMTR_CTRL_VTG_SE1; /* '<S5>/S-Function10' */
  uint16_T ADCMon_rawSWSUPP_LEV_SE7;   /* '<S5>/S-Function11' */
  uint16_T ConversiontoInputvoltage[2];/* '<S12>/Conversion to Input voltage' */
} B_ADCMon_Mdl_c_T;

#endif                                 /*ADCMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ADCMon_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_ADCMon_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*ADCMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ADCMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_ADCMon_Mdl_T rtm;
} MdlrefDW_ADCMon_Mdl_T;

#endif                                 /*ADCMon_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern int16_T PIDDID_IOCtrl_TAmbTemp;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TAmbTemp' */
extern int16_T PIDDID_IOCtrl_TMosfetTemp;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_TMosfetTemp' */
extern uint16_T PIDDID_IOCtrl_TBattVtg;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TBattVtg' */
extern uint16_T PIDDID_IOCtrl_THallSuppVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_THallSuppVtg' */
extern uint16_T PIDDID_IOCtrl_TMtrFdbkAVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkAVtg' */
extern uint16_T PIDDID_IOCtrl_TMtrFdbkBVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkBVtg' */
extern uint16_T PIDDID_IOcontrolMaskRecord;
                       /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecord' */
extern void ADCMon_Mdl(uint16_T *rty_ADCMonBus_ADCMon_u12VBattInst, uint16_T
  rty_ADCMonBus_ADCMon_uSwchInst[2], int16_T *rty_ADCMonBus_ADCMon_TAmbTemp,
  int16_T *rty_ADCMonBus_ADCMon_TMOSFETTemp, uint16_T
  *rty_ADCMonBus_ADCMon_uHallPwrInst, uint16_T *rty_ADCMonBus_ADCMon_uM1AVolt_mv,
  uint16_T *rty_ADCMonBus_ADCMon_uM1BVolt_mv);

/* Model reference registration function */
extern void ADCMon_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef ADCMon_Mdl_MDLREF_HIDE_CHILD_

extern void ADCMon_Mdl_ADCCount_to_degC_for_AmbTemp(uint16_T
  rtu_ADCMon_rawAMB_TEMP_SE11, int16_T rtu_PIDDID_TAmbTempOffset, int16_T
  *rty_ADCMon_TAmbTemp);
extern void ADCMon_Mdl_ADCCount_to_degC_for_MOSFETTemp(uint16_T
  rtu_ADCMon_rawMOSF_TEMP_SE12, int16_T rtu_PIDDID_TMOSFETTempOffset, int16_T
  *rty_ADCMon_TMOSFETTemp);
extern void ADCMon_Mdl_ADCCount_to_mV(const uint16_T rtu_ADCMon_rawValue[4],
  uint16_T rtu_isADCDivConst_C, uint16_T rtu_isADCMulConst_C, uint16_T
  rty_ConvertedVolt[4]);
extern void ADCMon_Mdl_ConvertMtrTerminalVoltage(void);

#endif                                 /*ADCMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ADCMon_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_ADCMon_Mdl_T ADCMon_Mdl_MdlrefDW;

#endif                                 /*ADCMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ADCMon_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_ADCMon_Mdl_c_T ADCMon_Mdl_B;

#endif                                 /*ADCMon_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'ADCMon_Mdl'
 * '<S1>'   : 'ADCMon_Mdl/ADCMon'
 * '<S2>'   : 'ADCMon_Mdl/Model Info'
 * '<S3>'   : 'ADCMon_Mdl/ADCMon/ADCCount_to_degC_for_AmbTemp'
 * '<S4>'   : 'ADCMon_Mdl/ADCMon/ADCCount_to_degC_for_MOSFETTemp'
 * '<S5>'   : 'ADCMon_Mdl/ADCMon/ADCMon_GetRawADCValue'
 * '<S6>'   : 'ADCMon_Mdl/ADCMon/ADC_Counts_to_mV'
 * '<S7>'   : 'ADCMon_Mdl/ADCMon/DocBlock'
 * '<S8>'   : 'ADCMon_Mdl/ADCMon/ADCCount_to_degC_for_AmbTemp/DocBlock'
 * '<S9>'   : 'ADCMon_Mdl/ADCMon/ADCCount_to_degC_for_MOSFETTemp/DocBlock'
 * '<S10>'  : 'ADCMon_Mdl/ADCMon/ADCMon_GetRawADCValue/DocBlock'
 * '<S11>'  : 'ADCMon_Mdl/ADCMon/ADC_Counts_to_mV/ADCCount_to_mV'
 * '<S12>'  : 'ADCMon_Mdl/ADCMon/ADC_Counts_to_mV/ConvertMtrTerminalVoltage'
 * '<S13>'  : 'ADCMon_Mdl/ADCMon/ADC_Counts_to_mV/DocBlock'
 * '<S14>'  : 'ADCMon_Mdl/ADCMon/ADC_Counts_to_mV/ADCCount_to_mV/DocBlock'
 * '<S15>'  : 'ADCMon_Mdl/ADCMon/ADC_Counts_to_mV/ConvertMtrTerminalVoltage/DocBlock'
 */
#endif                                 /* RTW_HEADER_ADCMon_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
