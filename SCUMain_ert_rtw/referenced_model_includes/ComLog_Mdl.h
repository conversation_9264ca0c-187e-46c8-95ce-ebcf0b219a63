/*
 * File: ComLog_Mdl.h
 *
 * Code generated for Simulink model 'ComLog_Mdl'.
 *
 * Model version                  : 1.80
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:30:12 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_ComLog_Mdl_h_
#define RTW_HEADER_ComLog_Mdl_h_
#ifndef ComLog_Mdl_COMMON_INCLUDES_
#define ComLog_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* ComLog_Mdl_COMMON_INCLUDES_ */

#include "CmdLogic_ExpTypes.h"
#include "ComLog_Mdl_types.h"
#include "PosMon_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "ThermProt_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "BlockDet_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"
#ifndef ComLog_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_ComLog_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*ComLog_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef ComLog_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_ComLog_Mdl_T rtm;
} MdlrefDW_ComLog_Mdl_T;

#endif                                 /*ComLog_Mdl_MDLREF_HIDE_CHILD_*/

extern void ComLog_Mdl(const boolean_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const VehCom_Cmd_t
  *rtu_SWC_CommLyrBus_VehComBus_VehCom_stVehCmd, const SWC_ObjDetLyrBus
  *rtu_SWC_ObjDetLyrBus, ComLogBus *rty_ComLogBus);

/* Model reference registration function */
extern void ComLog_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef ComLog_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_ComLog_Mdl_T ComLog_Mdl_MdlrefDW;

#endif                                 /*ComLog_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<Root>/Constant1' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'ComLog_Mdl'
 * '<S1>'   : 'ComLog_Mdl/CommandLogic'
 * '<S2>'   : 'ComLog_Mdl/CommandLogic/DocBlock1'
 * '<S3>'   : 'ComLog_Mdl/CommandLogic/LINCommandArbitration'
 * '<S4>'   : 'ComLog_Mdl/CommandLogic/RP_RF_validityDetermination'
 * '<S5>'   : 'ComLog_Mdl/CommandLogic/LINCommandArbitration/DocBlock'
 * '<S6>'   : 'ComLog_Mdl/CommandLogic/LINCommandArbitration/TT_LINCommandArbitration'
 * '<S7>'   : 'ComLog_Mdl/CommandLogic/RP_RF_validityDetermination/DocBlock'
 */
#endif                                 /* RTW_HEADER_ComLog_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
