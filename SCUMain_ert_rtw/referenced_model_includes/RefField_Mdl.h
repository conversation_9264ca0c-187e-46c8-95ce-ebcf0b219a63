/*
 * File: RefField_Mdl.h
 *
 * Code generated for Simulink model 'RefField_Mdl'.
 *
 * Model version                  : 1.368
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:38:57 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_RefField_Mdl_h_
#define RTW_HEADER_RefField_Mdl_h_
#ifndef RefField_Mdl_COMMON_INCLUDES_
#define RefField_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* RefField_Mdl_COMMON_INCLUDES_ */

#include "RefField_Mdl_types.h"
#include "StateMach_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "BlockDet_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "RefField_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for system '<S1>/ReferenceFieldLogic' */
#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  stdReturn_t RFDet_stRefFieldSave;    /* '<S5>/referenceFieldState' */
  uint16_T isPositionInCurrentArea;    /* '<S39>/Add1' */
  uint8_T currentArea;                 /* '<S34>/DetermineCurrentZoneAndByte' */
  boolean_T RFDet_isShdnRdy;           /* '<S5>/referenceFieldState' */
  boolean_T RFDet_isRefFieldTblVldNvmOut;/* '<S5>/referenceFieldState' */
  boolean_T RFDet_isRefFieldTblVld;    /* '<S5>/referenceFieldState' */
  boolean_T RFDet_isRefFieldTblVldNvmBakOut;/* '<S5>/referenceFieldState' */
  boolean_T RFDet_isRefFieldTblVldNvmSyncOut;/* '<S5>/referenceFieldState' */
  RFDet_NVMReq_t stNVMReq;             /* '<S5>/referenceFieldState' */
  RFDet_DataSwap_t swapBufferRefField; /* '<S5>/referenceFieldState' */
} B_CoreSubsys_RefField_Mdl_T;

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

/* Block signals for model 'RefField_Mdl' */
#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T DataTypeConversion;         /* '<S11>/Data Type Conversion' */
  uint32_T DataTypeConversion5;        /* '<S11>/Data Type Conversion5' */
  uint32_T DataTypeConversion6;        /* '<S11>/Data Type Conversion6' */
  uint32_T DataTypeConversion8;        /* '<S11>/Data Type Conversion8' */
  uint32_T DataTypeConversion9;        /* '<S11>/Data Type Conversion9' */
  uint8_T Status;                      /* '<S26>/S-Function' */
  uint8_T Status_d;                    /* '<S25>/S-Function' */
  uint8_T Status_n;                    /* '<S24>/S-Function' */
  uint8_T Status_o;                    /* '<S23>/S-Function' */
  uint8_T Status_h;                    /* '<S22>/S-Function' */
  B_CoreSubsys_RefField_Mdl_T CoreSubsys[1];/* '<S1>/ReferenceFieldLogic' */
} B_RefField_Mdl_c_T;

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'RefField_Mdl' */
#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  stdReturn_t UnitDelay_DSTATE;        /* '<S1>/Unit Delay' */
  boolean_T DelayInput1_DSTATE;        /* '<S7>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_k;      /* '<S8>/Delay Input1' */
} DW_RefField_Mdl_f_T;

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_RefField_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_RefField_Mdl_T rtm;
} MdlrefDW_RefField_Mdl_T;

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

extern void RefField_Mdl(const PnlOp_MtrCmd_t
  *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_stMtrCtrlCmd, const boolean_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, boolean_T
  *rty_RefFieldBus_RefField_isStartupRdy, boolean_T
  *rty_RefFieldBus_RefField_isShdnRdy, stdReturn_t
  *rty_RefFieldBus_RefField_stRFSave, boolean_T
  *rty_RefFieldBus_RefField_isRFTblVldNvm, boolean_T
  *rty_RefFieldBus_RefField_isRFTblVld, boolean_T
  *rty_RefFieldBus_RefField_isRFTblVldNvmBak, boolean_T
  *rty_RefFieldBus_RefField_isRFTblVldNvmSync, boolean_T
  *rty_RefFieldBus_RefField_isInsideRF, uint16_T
  *rty_RefFieldBus_RefField_currByteOfRF, uint8_T
  *rty_RefFieldBus_RefField_currRFArea);

/* Model reference registration function */
extern void RefField_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

extern void RefField_Mdl_NVM_RF(uint8_T rtu_Enable, uint32_T rtu_BlockId,
  uint8_T *rty_Status);
extern void RefField_Mdl_NVMRW(stdReturn_t *rty_stNVMAccess);

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_RefField_Mdl_T RefField_Mdl_MdlrefDW;

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef RefField_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_RefField_Mdl_c_T RefField_Mdl_B;

/* Block states (default storage) */
extern DW_RefField_Mdl_f_T RefField_Mdl_DW;

#endif                                 /*RefField_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'RefField_Mdl'
 * '<S1>'   : 'RefField_Mdl/RefFieldDet'
 * '<S2>'   : 'RefField_Mdl/RefFieldDet/DocBlock1'
 * '<S3>'   : 'RefField_Mdl/RefFieldDet/IsRFValidityChange'
 * '<S4>'   : 'RefField_Mdl/RefFieldDet/NVMRW'
 * '<S5>'   : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic'
 * '<S6>'   : 'RefField_Mdl/RefFieldDet/IsRFValidityChange/Compare To Constant3'
 * '<S7>'   : 'RefField_Mdl/RefFieldDet/IsRFValidityChange/Detect Increase1'
 * '<S8>'   : 'RefField_Mdl/RefFieldDet/IsRFValidityChange/Detect Increase2'
 * '<S9>'   : 'RefField_Mdl/RefFieldDet/IsRFValidityChange/DocBlock1'
 * '<S10>'  : 'RefField_Mdl/RefFieldDet/NVMRW/DocBlock'
 * '<S11>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess'
 * '<S12>'  : 'RefField_Mdl/RefFieldDet/NVMRW/SetReq'
 * '<S13>'  : 'RefField_Mdl/RefFieldDet/NVMRW/SwapDataBuffers'
 * '<S14>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/Compare To Constant1'
 * '<S15>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/Compare To Constant2'
 * '<S16>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/Compare To Constant3'
 * '<S17>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/Compare To Constant4'
 * '<S18>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/Compare To Constant5'
 * '<S19>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/CompareToConstant1'
 * '<S20>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/CompareToConstant4'
 * '<S21>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/DocBlock'
 * '<S22>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF'
 * '<S23>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_BAK'
 * '<S24>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_VLD'
 * '<S25>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_VLD_BAK'
 * '<S26>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_VLD_SYNC'
 * '<S27>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF/DocBlock'
 * '<S28>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_BAK/DocBlock'
 * '<S29>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_VLD/DocBlock'
 * '<S30>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_VLD_BAK/DocBlock'
 * '<S31>'  : 'RefField_Mdl/RefFieldDet/NVMRW/NVMAccess/NVM_RF_VLD_SYNC/DocBlock'
 * '<S32>'  : 'RefField_Mdl/RefFieldDet/NVMRW/SetReq/DocBlock'
 * '<S33>'  : 'RefField_Mdl/RefFieldDet/NVMRW/SwapDataBuffers/DocBlock'
 * '<S34>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField'
 * '<S35>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DocBlock1'
 * '<S36>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/referenceFieldState'
 * '<S37>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/CheckForCurrentDirection'
 * '<S38>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/CheckForCurrentPositionInArea'
 * '<S39>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/DetermineCurrentByte'
 * '<S40>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/DetermineCurrentZoneAndByte'
 * '<S41>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/DetermineDirectionAreas'
 * '<S42>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/DocBlock1'
 * '<S43>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/CheckForCurrentDirection/DocBlock1'
 * '<S44>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/CheckForCurrentPositionInArea/DocBlock1'
 * '<S45>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/DetermineCurrentByte/DocBlock1'
 * '<S46>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/DetermineDirectionAreas/CompareToZero'
 * '<S47>'  : 'RefField_Mdl/RefFieldDet/ReferenceFieldLogic/DetermineCurrentByteReferenceField/DetermineDirectionAreas/DocBlock1'
 */
#endif                                 /* RTW_HEADER_RefField_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
