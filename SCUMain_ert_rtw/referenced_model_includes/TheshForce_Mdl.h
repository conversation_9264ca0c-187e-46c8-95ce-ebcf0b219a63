/*
 * File: TheshForce_Mdl.h
 *
 * Code generated for Simulink model 'TheshForce_Mdl'.
 *
 * Model version                  : 1.94
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:41:39 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_TheshForce_Mdl_h_
#define RTW_HEADER_TheshForce_Mdl_h_
#ifndef TheshForce_Mdl_COMMON_INCLUDES_
#define TheshForce_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TheshForce_Mdl_COMMON_INCLUDES_ */

#include "CtrlLogic_ExpTypes.h"
#include "TheshForce_Mdl_types.h"
#include "CmdLogic_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "PosMon_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'TheshForce_Mdl' */
#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  int16_T thresholdClassic;     /* '<S18>/CalculateThresholdOfAllValidBlocks' */
  uint8_T RoughRoadATSThreshold;       /* '<S7>/SpikeDetection' */
  boolean_T motorStartUpThreshold;     /* '<S5>/MotorStartUpThreshold' */
} B_TheshForce_Mdl_c_T;

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'TheshForce_Mdl' */
#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  int16_T oldMtrForce;                 /* '<S7>/SpikeDetection' */
  uint8_T is_c3_TheshForce_Mdl;        /* '<S5>/MotorStartUpThreshold' */
  uint8_T is_active_c3_TheshForce_Mdl; /* '<S5>/MotorStartUpThreshold' */
  boolean_T isRoughRoadActive;         /* '<S7>/SpikeDetection' */
} DW_TheshForce_Mdl_f_T;

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'TheshForce_Mdl' */
#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const uint8_T Width;                 /* '<S8>/Width' */
} ConstB_TheshForce_Mdl_h_T;

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_TheshForce_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_TheshForce_Mdl_T rtm;
} MdlrefDW_TheshForce_Mdl_T;

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

extern void TheshForce_Mdl(const boolean_T
  *rtu_SWC_CommLyrBus_VehComBus_VehCom_isVehSpdVld, const uint8_T
  *rtu_SWC_CommLyrBus_VehComBus_VehCom_vVehSpd, const CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const uint16_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const boolean_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld, const boolean_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const int16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned, const int16_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const boolean_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld, const int16_T
  *rtu_MtrMdlBus_MtrMdl_stCalcForTorq_FcalcForce, const boolean_T
  *rtu_MtrMdlBus_MtrMdl_isMotorInStartUp, uint16_T
  *rty_ThresForceBus_ThresForce_FatsThreshold);

/* Model reference registration function */
extern void TheshForce_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

extern void TheshForce_Mdl_calculateinterpolateThreshold(int8_T rtu_threshold1,
  int8_T rtu_threshold2, int16_T rtu_dataPoint1, int16_T rtu_dataPoint2, int16_T
  rtu_currentDataPoint, int16_T *rty_blockThreshold);

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_TheshForce_Mdl_T TheshForce_Mdl_MdlrefDW;

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef TheshForce_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_TheshForce_Mdl_c_T TheshForce_Mdl_B;

/* Block states (default storage) */
extern DW_TheshForce_Mdl_f_T TheshForce_Mdl_DW;

#endif                                 /*TheshForce_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<Root>/Constant1' : Unused code path elimination
 * Block '<Root>/Constant' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TheshForce_Mdl'
 * '<S1>'   : 'TheshForce_Mdl/Model Info'
 * '<S2>'   : 'TheshForce_Mdl/PanelThreshold'
 * '<S3>'   : 'TheshForce_Mdl/PanelThreshold/BaselineThreshold'
 * '<S4>'   : 'TheshForce_Mdl/PanelThreshold/DocBlock'
 * '<S5>'   : 'TheshForce_Mdl/PanelThreshold/MotorStartUpThreshold'
 * '<S6>'   : 'TheshForce_Mdl/PanelThreshold/RudimentaryATSThreshold'
 * '<S7>'   : 'TheshForce_Mdl/PanelThreshold/SpikeDetection'
 * '<S8>'   : 'TheshForce_Mdl/PanelThreshold/ThresholdAdjustmentATSThreshold'
 * '<S9>'   : 'TheshForce_Mdl/PanelThreshold/VehicleSpeedATSThreshold'
 * '<S10>'  : 'TheshForce_Mdl/PanelThreshold/BaselineThreshold/CompareToConstant'
 * '<S11>'  : 'TheshForce_Mdl/PanelThreshold/BaselineThreshold/DocBlock'
 * '<S12>'  : 'TheshForce_Mdl/PanelThreshold/MotorStartUpThreshold/DocBlock'
 * '<S13>'  : 'TheshForce_Mdl/PanelThreshold/MotorStartUpThreshold/MotorStartUpThreshold'
 * '<S14>'  : 'TheshForce_Mdl/PanelThreshold/RudimentaryATSThreshold/CompareToConstant'
 * '<S15>'  : 'TheshForce_Mdl/PanelThreshold/RudimentaryATSThreshold/DocBlock'
 * '<S16>'  : 'TheshForce_Mdl/PanelThreshold/SpikeDetection/DocBlock'
 * '<S17>'  : 'TheshForce_Mdl/PanelThreshold/SpikeDetection/SpikeDetection'
 * '<S18>'  : 'TheshForce_Mdl/PanelThreshold/ThresholdAdjustmentATSThreshold/AddThresholdFromEachValidxBlock'
 * '<S19>'  : 'TheshForce_Mdl/PanelThreshold/ThresholdAdjustmentATSThreshold/DocBlock'
 * '<S20>'  : 'TheshForce_Mdl/PanelThreshold/ThresholdAdjustmentATSThreshold/AddThresholdFromEachValidxBlock/CalculateThresholdOfAllValidBlocks'
 * '<S21>'  : 'TheshForce_Mdl/PanelThreshold/ThresholdAdjustmentATSThreshold/AddThresholdFromEachValidxBlock/DocBlock'
 * '<S22>'  : 'TheshForce_Mdl/PanelThreshold/ThresholdAdjustmentATSThreshold/AddThresholdFromEachValidxBlock/CalculateThresholdOfAllValidBlocks/calculate.interpolateThreshold'
 * '<S23>'  : 'TheshForce_Mdl/PanelThreshold/VehicleSpeedATSThreshold/DocBlock'
 */
#endif                                 /* RTW_HEADER_TheshForce_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
