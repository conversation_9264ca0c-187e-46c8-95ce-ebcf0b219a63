/*
 * File: DIOCtrl_Mdl.h
 *
 * Code generated for Simulink model 'DIOCtrl_Mdl'.
 *
 * Model version                  : 1.199
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:31:19 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_DIOCtrl_Mdl_h_
#define RTW_HEADER_DIOCtrl_Mdl_h_
#ifndef DIOCtrl_Mdl_COMMON_INCLUDES_
#define DIOCtrl_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* DIOCtrl_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "DIOCtrl_Mdl_types.h"
#include "zero_crossing_types.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'DIOCtrl_Mdl' */
#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  boolean_T DIOCtrl_PINReadStatus;     /* '<S11>/DIOCtrl_bReadPin' */
  boolean_T DIOCtrl_PINReadStatus_o;   /* '<S11>/DIOCtrl_bReadPin1' */
  boolean_T DIOCtrl_PINReadStatus_f;   /* '<S11>/DIOCtrl_bReadPin2' */
  boolean_T DIOCtrl_PINReadStatus_l;   /* '<S11>/DIOCtrl_bReadPin3' */
  boolean_T ADCMon_uHallPwrInst;       /* '<S10>/Switch3' */
  boolean_T ADCMon_uHallPwrInst_m;     /* '<S10>/Switch4' */
  boolean_T ADCMon_uHallPwrInst_p;     /* '<S10>/Switch5' */
  boolean_T ADCMon_uHallPwrInst_d;     /* '<S10>/Switch6' */
} B_DIOCtrl_Mdl_c_T;

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'DIOCtrl_Mdl' */
#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T DelayInput1_DSTATE;         /* '<S9>/Delay Input1' */
  boolean_T isVBATMeasEnableInitial;   /* '<S6>/Data Store Memory' */
  boolean_T isHallOutInitial;          /* '<S6>/Data Store Memory1' */
  boolean_T isMtrFltrBlankAInitial;    /* '<S6>/Data Store Memory2' */
  boolean_T isMtrFltrBlankBInitial;    /* '<S6>/Data Store Memory3' */
} DW_DIOCtrl_Mdl_f_T;

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'DIOCtrl_Mdl' */
#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState TriggeredSubsystem_Trig_ZCE;/* '<S6>/Triggered Subsystem' */
} ZCE_DIOCtrl_Mdl_T;

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_DIOCtrl_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_DIOCtrl_Mdl_T rtm;
} MdlrefDW_DIOCtrl_Mdl_T;

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T PIDDID_IOcontrolMaskRecordDigiOut;
                /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordDigiOut' */
extern boolean_T PIDDID_IOCtrl_isHallOut;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_isHallOut' */
extern boolean_T PIDDID_IOCtrl_isMtrFltrBlankA;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankA' */
extern boolean_T PIDDID_IOCtrl_isMtrFltrBlankB;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankB' */
extern boolean_T PIDDID_IOCtrl_isVBATMeasEnable;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isVBATMeasEnable' */
extern void DIOCtrl_Mdl(const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_PINReadStatus, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_isVBATMeasEnbPinStatus, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_isHallOutPinStatus, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_isMtrFltrBlankAPinStatus, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_isMtrFltrBlankBPinStatus, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_isHallSens1PinStatus, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_isHallSens2PinStatus, boolean_T
  *rty_DIOCtrlBus_DIOCtrl_isINTPinStatus);

/* Model reference registration function */
extern void DIOCtrl_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

extern void DIOCtrl_Mdl_DIOCtrl_PinReadStatus(boolean_T
  *rty_DIOCtrl_isVBATMeasEnbPinStatus, boolean_T *rty_DIOCtrl_isHallOutPinStatus,
  boolean_T *rty_DIOCtrl_isMtrFltrBlankAPinStatus, boolean_T
  *rty_DIOCtrl_isMtrFltrBlankBPinStatus, boolean_T
  *rty_DIOCtrl_isHallSens1PinStatus, boolean_T *rty_DIOCtrl_isHallSens2PinStatus,
  boolean_T *rty_DIOCtrl_isINTPinStatus);
extern void DIOCtrl_Mdl_PIDID_IOCtrl(const StateMach_Mode_t
  *rtu_StateMach_stSysMode);

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_DIOCtrl_Mdl_T DIOCtrl_Mdl_MdlrefDW;

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef DIOCtrl_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_DIOCtrl_Mdl_c_T DIOCtrl_Mdl_B;

/* Block states (default storage) */
extern DW_DIOCtrl_Mdl_f_T DIOCtrl_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_DIOCtrl_Mdl_T DIOCtrl_Mdl_PrevZCX;

#endif                                 /*DIOCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'DIOCtrl_Mdl'
 * '<S1>'   : 'DIOCtrl_Mdl/DIOCtrl'
 * '<S2>'   : 'DIOCtrl_Mdl/Model Info'
 * '<S3>'   : 'DIOCtrl_Mdl/DIOCtrl/DIOCtrl'
 * '<S4>'   : 'DIOCtrl_Mdl/DIOCtrl/DIOCtrl_PinReadStatus'
 * '<S5>'   : 'DIOCtrl_Mdl/DIOCtrl/DocBlock'
 * '<S6>'   : 'DIOCtrl_Mdl/DIOCtrl/PIDID_IOCtrl'
 * '<S7>'   : 'DIOCtrl_Mdl/DIOCtrl/DIOCtrl_PinReadStatus/DocBlock'
 * '<S8>'   : 'DIOCtrl_Mdl/DIOCtrl/PIDID_IOCtrl/Compare To Constant'
 * '<S9>'   : 'DIOCtrl_Mdl/DIOCtrl/PIDID_IOCtrl/Detect Change'
 * '<S10>'  : 'DIOCtrl_Mdl/DIOCtrl/PIDID_IOCtrl/Subsystem'
 * '<S11>'  : 'DIOCtrl_Mdl/DIOCtrl/PIDID_IOCtrl/Triggered Subsystem'
 */
#endif                                 /* RTW_HEADER_DIOCtrl_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
