/*
 * File: PWMCtrl_Mdl.h
 *
 * Code generated for Simulink model 'PWMCtrl_Mdl'.
 *
 * Model version                  : 1.91
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:37:16 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PWMCtrl_Mdl_h_
#define RTW_HEADER_PWMCtrl_Mdl_h_
#ifndef PWMCtrl_Mdl_COMMON_INCLUDES_
#define PWMCtrl_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* PWMCtrl_Mdl_COMMON_INCLUDES_ */

#include "RoofSys_CommDefines.h"
#include "StateMach_ExpTypes.h"
#include "PWMCtrl_Mdl_types.h"
#include "ThermProt_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'PWMCtrl_Mdl' */
#ifndef PWMCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T MultiportSwitch1;            /* '<S3>/Multiport Switch1' */
  uint8_T SFunction;                   /* '<S4>/S-Function' */
  uint8_T DataTypeConversion;          /* '<S10>/Data Type Conversion' */
  uint8_T SFunction_j;                 /* '<S10>/S-Function' */
  uint8_T DataTypeConversion_n;        /* '<S8>/Data Type Conversion' */
  uint8_T DataTypeConversion1;         /* '<S8>/Data Type Conversion1' */
  uint8_T SFunction_k;                 /* '<S8>/S-Function' */
} B_PWMCtrl_Mdl_c_T;

#endif                                 /*PWMCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PWMCtrl_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_PWMCtrl_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*PWMCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PWMCtrl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_PWMCtrl_Mdl_T rtm;
} MdlrefDW_PWMCtrl_Mdl_T;

#endif                                 /*PWMCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T PIDDID_IOcontrolMaskRecordSysICOut;
               /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordSysICOut' */
extern uint8_T PIDDID_IOCtrl_uMtrDtyCyc;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_uMtrDtyCyc' */
extern boolean_T PIDDID_IOCtrl_isMtrCCWAct;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCCWAct' */
extern boolean_T PIDDID_IOCtrl_isMtrCWAct;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCWAct' */
extern void PWMCtrl_Mdl_Init(void);
extern void PWMCtrl_Mdl(const MtrCtrl_MtrDir_t
  *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir, const int16_T
  *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDutyCycle, const boolean_T
  rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_FeedbackStatus[2], const
  StateMach_Mode_t *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode,
  PWMCtrl_MtrDir_t *rty_PWMCtrlBus_PWMCtrl_SetMtrDir, int16_T
  *rty_PWMCtrlBus_PWMCtrl_SetMtrDutyCycle);

/* Model reference registration function */
extern void PWMCtrl_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef PWMCtrl_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_PWMCtrl_Mdl_T PWMCtrl_Mdl_MdlrefDW;

#endif                                 /*PWMCtrl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PWMCtrl_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_PWMCtrl_Mdl_c_T PWMCtrl_Mdl_B;

#endif                                 /*PWMCtrl_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S3>/Data Type Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PWMCtrl_Mdl'
 * '<S1>'   : 'PWMCtrl_Mdl/DocBlock1'
 * '<S2>'   : 'PWMCtrl_Mdl/Model Info'
 * '<S3>'   : 'PWMCtrl_Mdl/MotorCtrlCmd'
 * '<S4>'   : 'PWMCtrl_Mdl/PWMCtrl'
 * '<S5>'   : 'PWMCtrl_Mdl/Subsystem'
 * '<S6>'   : 'PWMCtrl_Mdl/MotorCtrlCmd/DocBlock'
 * '<S7>'   : 'PWMCtrl_Mdl/MotorCtrlCmd/DutyCycleConversion'
 * '<S8>'   : 'PWMCtrl_Mdl/MotorCtrlCmd/Init'
 * '<S9>'   : 'PWMCtrl_Mdl/MotorCtrlCmd/PWMCtrl_SYSIC_IOCtrl'
 * '<S10>'  : 'PWMCtrl_Mdl/MotorCtrlCmd/PWMMotorIdle'
 * '<S11>'  : 'PWMCtrl_Mdl/MotorCtrlCmd/DutyCycleConversion/DocBlock'
 * '<S12>'  : 'PWMCtrl_Mdl/MotorCtrlCmd/Init/DocBlock'
 * '<S13>'  : 'PWMCtrl_Mdl/MotorCtrlCmd/PWMCtrl_SYSIC_IOCtrl/Subsystem'
 * '<S14>'  : 'PWMCtrl_Mdl/MotorCtrlCmd/PWMMotorIdle/DocBlock'
 */
#endif                                 /* RTW_HEADER_PWMCtrl_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
