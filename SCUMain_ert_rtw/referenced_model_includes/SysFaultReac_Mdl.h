/*
 * File: SysFaultReac_Mdl.h
 *
 * Code generated for Simulink model 'SysFaultReac_Mdl'.
 *
 * Model version                  : 1.527
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:40:48 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_SysFaultReac_Mdl_h_
#define RTW_HEADER_SysFaultReac_Mdl_h_
#ifndef SysFaultReac_Mdl_COMMON_INCLUDES_
#define SysFaultReac_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* SysFaultReac_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "SysFaultReac_Mdl_types.h"
#include "ThermProt_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "BlockDet_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "DTC_Exp.h"
#include "CfgParam_Mdl_Exp.h"
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for system '<S52>/For Each Subsystem' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T Add;                         /* '<S65>/Add' */
} B_CoreSubsys_SysFaultReac_Mdl_cau_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for system '<S52>/For Each Subsystem' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState TriggeredSubsystem_Trig_ZCE_mx;/* '<S63>/Triggered Subsystem' */
} ZCE_CoreSubsys_SysFaultReac_Mdl_fej_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Block signals for system '<S52>/For Each Subsystem' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  B_CoreSubsys_SysFaultReac_Mdl_cau_T CoreSubsys;/* '<S52>/For Each Subsystem' */
} B_ForEachSubsystem_SysFaultReac_Mdl_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for system '<S52>/For Each Subsystem' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCE_CoreSubsys_SysFaultReac_Mdl_fej_T CoreSubsys;/* '<S52>/For Each Subsystem' */
} ZCE_ForEachSubsystem_SysFaultReac_Mdl_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Block signals for system '<S114>/For Each Subsystem' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T Add;                         /* '<S125>/Add' */
} B_CoreSubsys_SysFaultReac_Mdl_caua_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for system '<S114>/For Each Subsystem' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState TriggeredSubsystem_Trig_ZCE_m;/* '<S123>/Triggered Subsystem' */
} ZCE_CoreSubsys_SysFaultReac_Mdl_fejc_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Block signals for model 'SysFaultReac_Mdl' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T BitwiseOR;                  /* '<S21>/Bitwise OR' */
  uint16_T Switch4;                    /* '<S233>/Switch4' */
  uint16_T BitwiseOR_m;                /* '<S190>/Bitwise OR' */
  uint16_T Switch;                     /* '<S6>/Switch' */
  uint8_T VectorConcatenate[3];        /* '<S5>/Vector Concatenate' */
  uint8_T SFunction;                   /* '<S236>/S-Function' */
  uint8_T SFunction_f;                 /* '<S215>/S-Function' */
  uint8_T SFunction_d;                 /* '<S191>/S-Function' */
  uint8_T SFunction_n;                 /* '<S171>/S-Function' */
  uint8_T Last10Dtc[10];               /* '<S42>/Last10Dtc' */
  uint8_T SFunction_k;                 /* '<S42>/S-Function' */
  uint8_T SFunction_o;                 /* '<S36>/S-Function' */
  uint8_T SFunction_i;                 /* '<S35>/S-Function' */
  uint8_T SFunction_b;                 /* '<S34>/S-Function' */
  uint8_T SFunction_j;                 /* '<S33>/S-Function' */
  boolean_T SFunction_h;               /* '<S3>/S-Function' */
  boolean_T SFunction1;                /* '<S3>/S-Function1' */
  boolean_T SFunction2;                /* '<S3>/S-Function2' */
  boolean_T SFunction8;                /* '<S3>/S-Function8' */
  boolean_T SFunction3;                /* '<S3>/S-Function3' */
  boolean_T SFunction4;                /* '<S3>/S-Function4' */
  boolean_T SFunction5;                /* '<S3>/S-Function5' */
  boolean_T SFunction6;                /* '<S3>/S-Function6' */
  boolean_T SFunction7;                /* '<S3>/S-Function7' */
  boolean_T SFunction_g;               /* '<S13>/S-Function' */
  boolean_T SFunction3_f;              /* '<S4>/S-Function3' */
  SysFltDiag_LastStopReason_t stLastStopReason;/* '<S215>/stLastStopReason' */
  SysFltDiag_LastStopReason_t Switch13;/* '<S7>/Switch13' */
  SysFltDiag_LastStopReason_t Switch6; /* '<S6>/Switch6' */
  B_CoreSubsys_SysFaultReac_Mdl_caua_T CoreSubsys_pna[8];/* '<S114>/For Each Subsystem' */
  B_ForEachSubsystem_SysFaultReac_Mdl_T ForEachSubsystem_i[8];/* '<S83>/For Each Subsystem' */
  B_ForEachSubsystem_SysFaultReac_Mdl_T ForEachSubsystem[8];/* '<S52>/For Each Subsystem' */
} B_SysFaultReac_Mdl_c_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'SysFaultReac_Mdl' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T UnitDelay_DSTATE;           /* '<S14>/Unit Delay' */
  uint8_T DelayInput1_DSTATE[3];       /* '<S28>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_h[10];    /* '<S41>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_p[8];     /* '<S31>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_m[8];     /* '<S29>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_p0[8];    /* '<S30>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_g;      /* '<S183>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_gz;     /* '<S213>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_h1;     /* '<S212>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_b;      /* '<S211>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_e;      /* '<S227>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_l;      /* '<S231>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_n;      /* '<S188>/Delay Input1' */
  SysFltDiag_LastStopReason_t UnitDelay_DSTATE_k;/* '<S216>/Unit Delay' */
} DW_SysFaultReac_Mdl_f_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'SysFaultReac_Mdl' */
#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState captureUnlearnReason_Trig_ZCE;/* '<S25>/captureUnlearnReason' */
  ZCSigState EnabledSubsystem_Trig_ZCE;/* '<S24>/Enabled Subsystem' */
  ZCSigState TriggeredSubsystem_Trig_ZCE;/* '<S19>/Triggered Subsystem' */
  ZCSigState TriggeredSubsystem2_Trig_ZCE;/* '<S13>/Triggered Subsystem2' */
  ZCSigState TriggeredSubsystem7_Trig_ZCE;/* '<S124>/Triggered Subsystem7' */
  ZCSigState TriggeredSubsystem6_Trig_ZCE;/* '<S124>/Triggered Subsystem6' */
  ZCSigState TriggeredSubsystem5_Trig_ZCE;/* '<S124>/Triggered Subsystem5' */
  ZCSigState TriggeredSubsystem4_Trig_ZCE;/* '<S124>/Triggered Subsystem4' */
  ZCSigState TriggeredSubsystem3_Trig_ZCE;/* '<S124>/Triggered Subsystem3' */
  ZCSigState TriggeredSubsystem2_Trig_ZCE_b;/* '<S124>/Triggered Subsystem2' */
  ZCSigState TriggeredSubsystem1_Trig_ZCE;/* '<S124>/Triggered Subsystem1' */
  ZCSigState TriggeredSubsystem_Trig_ZCE_p;/* '<S124>/Triggered Subsystem' */
  ZCE_CoreSubsys_SysFaultReac_Mdl_fejc_T CoreSubsys_pna[8];/* '<S114>/For Each Subsystem' */
  ZCSigState TriggeredSubsystem7_Trig_ZCE_a;/* '<S95>/Triggered Subsystem7' */
  ZCSigState TriggeredSubsystem6_Trig_ZCE_l;/* '<S95>/Triggered Subsystem6' */
  ZCSigState TriggeredSubsystem5_Trig_ZCE_d;/* '<S95>/Triggered Subsystem5' */
  ZCSigState TriggeredSubsystem4_Trig_ZCE_o;/* '<S95>/Triggered Subsystem4' */
  ZCSigState TriggeredSubsystem3_Trig_ZCE_l;/* '<S95>/Triggered Subsystem3' */
  ZCSigState TriggeredSubsystem2_Trig_ZCE_p;/* '<S95>/Triggered Subsystem2' */
  ZCSigState TriggeredSubsystem1_Trig_ZCE_o;/* '<S95>/Triggered Subsystem1' */
  ZCSigState TriggeredSubsystem_Trig_ZCE_l;/* '<S95>/Triggered Subsystem' */
  ZCE_ForEachSubsystem_SysFaultReac_Mdl_T ForEachSubsystem_i[8];/* '<S83>/For Each Subsystem' */
  ZCSigState TriggeredSubsystem7_Trig_ZCE_d;/* '<S64>/Triggered Subsystem7' */
  ZCSigState TriggeredSubsystem6_Trig_ZCE_h;/* '<S64>/Triggered Subsystem6' */
  ZCSigState TriggeredSubsystem5_Trig_ZCE_a;/* '<S64>/Triggered Subsystem5' */
  ZCSigState TriggeredSubsystem4_Trig_ZCE_c;/* '<S64>/Triggered Subsystem4' */
  ZCSigState TriggeredSubsystem3_Trig_ZCE_g;/* '<S64>/Triggered Subsystem3' */
  ZCSigState TriggeredSubsystem2_Trig_ZCE_n;/* '<S64>/Triggered Subsystem2' */
  ZCSigState TriggeredSubsystem1_Trig_ZCE_g;/* '<S64>/Triggered Subsystem1' */
  ZCSigState TriggeredSubsystem_Trig_ZCE_d;/* '<S64>/Triggered Subsystem' */
  ZCE_ForEachSubsystem_SysFaultReac_Mdl_T ForEachSubsystem[8];/* '<S52>/For Each Subsystem' */
  ZCSigState TriggeredSubsystem_Trig_ZCE_j;/* '<S37>/Triggered Subsystem' */
  ZCSigState LogDTCStatus_Trig_ZCE;    /* '<S5>/LogDTCStatus' */
  ZCSigState LogDTCByte3Counter_Trig_ZCE;/* '<S5>/LogDTCByte3Counter' */
  ZCSigState LogDTCByte2Counter_Trig_ZCE;/* '<S5>/LogDTCByte2Counter' */
  ZCSigState LogDTCByte1Counter_Trig_ZCE;/* '<S5>/LogDTCByte1Counter' */
} ZCE_SysFaultReac_Mdl_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_SysFaultReac_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_SysFaultReac_Mdl_T rtm;
} MdlrefDW_SysFaultReac_Mdl_T;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T SysFltRctn_stLearnFail_Reason;
                    /* Simulink.Signal object 'SysFltRctn_stLearnFail_Reason' */
extern uint16_T SysFltRctn_stLostRP_Reason;
                       /* Simulink.Signal object 'SysFltRctn_stLostRP_Reason' */
extern uint8_T SysFltRctn_stDTCCounter_Byte1[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte1' */
extern uint8_T SysFltRctn_stDTCCounter_Byte2[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte2' */
extern uint8_T SysFltRctn_stDTCCounter_Byte3[8];
                    /* Simulink.Signal object 'SysFltRctn_stDTCCounter_Byte3' */
extern uint8_T SysFltRctn_stDTCStatus[3];
                           /* Simulink.Signal object 'SysFltRctn_stDTCStatus' */
extern uint8_T SysFltRctn_stLast10DTCs[10];
                          /* Simulink.Signal object 'SysFltRctn_stLast10DTCs' */
extern SysFltDiag_LastStopReason_t SysFltRctn_stLastStopReason;
                      /* Simulink.Signal object 'SysFltRctn_stLastStopReason' */
extern void SysFaultReac_Mdl_Init(void);
extern void SysFaultReac_Mdl(const uint8_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1, const uint8_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HallMtrOptDir, const boolean_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFaultDebd, const boolean_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault, const
  ThermProt_MtrTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass, const
  ThermProt_MosfetTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass, const
  boolean_T *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_isSleepAllowed, const
  MtrCtrl_MtrDir_t *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir, const
  uint8_T *rtu_SWC_OperLogLyrBus_MtrCtrlBus_DTC_MTR_Dir, const MtrCtrl_MtrPwrd_t
  *rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_MtrPwrdState, const
  CtrlLog_RelearnMode_t *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode,
  const SysFltDiag_LastStopReason_t
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stLastStopReason, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const VehCom_Cmd_t
  *rtu_SWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd, const boolean_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete, const boolean_T *
  rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isInterrupted, const
  BlockDet_FltType_t *rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_stFltType, const
  uint16_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const boolean_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld, const
  VoltMon_VoltClass_t *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass,
  const boolean_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet, const
  boolean_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isUnderVtgFlgSet, const
  boolean_T *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isOverVtgFlgSet, const
  boolean_T *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const boolean_T *
  rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isOutOfRange, const boolean_T
  *rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld, const int16_T *
  rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const boolean_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld, const boolean_T
  *rtu_SWC_DataHndlLyr_CfgParamBus_CfgParam_isCALFileVld, const StateMach_Mode_t
  *rtu_SWC_StateMachLyr_StateMachBus_StateMach_stSysMode, const boolean_T
  *rtu_SWC_CommLyrBus_VehComBus_VehCom_bSleepReady, uint16_T
  *rty_SysFaultReacBus_SysFltRctn_stsSysFaultReaction, uint16_T
  *rty_SysFaultReacBus_SysFltRctn_stsUnlearnReason, uint16_T
  *rty_SysFaultReacBus_SysFltRctn_stsLearnFailReason, uint8_T
  rty_SysFaultReacBus_SysFltRctn_DTCStatus[3], SysFltDiag_LastStopReason_t
  *rty_SysFaultReacBus_SysFltRctn_stsLasStpReason, uint8_T
  rty_SysFaultReacBus_SysFltRctn_stLast10DTCs[10]);

/* Model reference registration function */
extern void SysFaultReac_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

extern void SysFaultReac_Mdl_BitShift(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_BitShift1(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_BitShift2(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_BitShift3(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_BitShift4(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_BitShift5(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_BitShift6(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_BitShift7(uint8_T rtu_u, uint8_T *rty_y);
extern void SysFaultReac_Mdl_ForEachSubsystem_Init(int32_T NumIters,
  ZCE_ForEachSubsystem_SysFaultReac_Mdl_T localZCE[8]);
extern void SysFaultReac_Mdl_ForEachSubsystem(int32_T NumIters, const boolean_T
  rtu_CurrentBit[8], const uint8_T rtu_Counter[8], uint8_T rty_DTCCounterOut[8],
  B_ForEachSubsystem_SysFaultReac_Mdl_T localB[8],
  ZCE_ForEachSubsystem_SysFaultReac_Mdl_T localZCE[8]);
extern void SysFaultReac_Mdl_CalDataFault(uint32_T rtu_idx, uint16_T
  *rty_stReactionCalFlt, uint16_T *rty_calUnlearnReason);
extern void SysFaultReac_Mdl_DTCCounterAndLast10DTCByte1_Init(void);
extern void SysFaultReac_Mdl_DTCCounterAndLast10DTCByte1(uint8_T
  rtu_DTCByte1_Bits, uint8_T rtu_DTCByte1_Bits_n, uint8_T rtu_DTCByte1_Bits_m,
  uint8_T rtu_DTCByte1_Bits_e, uint8_T rtu_DTCByte1_Bits_c, uint8_T
  rtu_DTCByte1_Bits_k, uint8_T rtu_DTCByte1_Bits_j, uint8_T rtu_DTCByte1_Bits_i,
  const uint8_T rtu_DTCCounterIn[8], uint8_T rty_stDTC_Status_Byte1[8]);
extern void SysFaultReac_Mdl_DTC_Status_Byte1_Init(void);
extern void SysFaultReac_Mdl_DTC_Status_Byte1(const uint8_T
  rtu_stDTCCounter_Byte1_In[8], const boolean_T *rtu_VoltMon_isOverVtg, const
  boolean_T *rtu_VoltMon_isUnderVtg, const boolean_T *rtu_stMosfetTempVld, const
  boolean_T *rtu_stBattVltVld, const boolean_T *rtu_stAmbTempVld, boolean_T
  rtu_isLINCommFail, boolean_T rtu_isSBCSPICRCFault, boolean_T
  rtu_isSysICFailFlt, const StateMach_Mode_t *rtu_stSystemState, boolean_T
  rtu_isVCC1SupFlt, uint8_T *rty_stDTC_Status_Byte1, uint8_T
  rty_stDTCCounter_Byte1_Out[8]);
extern void SysFaultReac_Mdl_DTCCounterAndLast10DTCByte2_Init(void);
extern void SysFaultReac_Mdl_DTCCounterAndLast10DTCByte2(uint8_T
  rtu_DTCByte2_Bits, uint8_T rtu_DTCByte2_Bits_j, uint8_T rtu_DTCByte2_Bits_g,
  uint8_T rtu_DTCByte2_Bits_m, uint8_T rtu_DTCByte2_Bits_f, uint8_T
  rtu_DTCByte2_Bits_gn, uint8_T rtu_DTCByte2_Bits_p, uint8_T rtu_DTCByte2_Bits_l,
  const uint8_T rtu_DTCCounterIn[8], uint8_T rty_stDTC_Status_Byte2[8]);
extern void SysFaultReac_Mdl_DTC_Status_Byte2_Init(void);
extern void SysFaultReac_Mdl_DTC_Status_Byte2(const uint8_T
  rtu_stDTCCounter_Byte2_In[8], boolean_T rtu_isVCC1Fault, boolean_T
  rtu_isThermFlt, boolean_T rtu_isChrgPmpFlt, boolean_T rtu_isHSSupFlt,
  boolean_T rtu_isVSINTFlt, boolean_T rtu_isVSSupFlt, const uint8_T
  *rtu_isHallDtc, const boolean_T *rtu_isRPValid, uint8_T
  *rty_stDTC_Status_Byte2, uint8_T rty_stDTCCounter_Byte2_Out[8]);
extern void SysFaultReac_Mdl_DTCCounterAndLast10DTCByte3_Init(void);
extern void SysFaultReac_Mdl_DTCCounterAndLast10DTCByte3(uint8_T
  rtu_DTCByte3_Bits, uint8_T rtu_DTCByte3_Bits_j, uint8_T rtu_DTCByte3_Bits_b,
  uint8_T rtu_DTCByte3_Bits_p, uint8_T rtu_DTCByte3_Bits_f, uint8_T
  rtu_DTCByte3_Bits_a, uint8_T rtu_DTCByte3_Bits_bh, const uint8_T
  rtu_DTCCounterIn[8], uint8_T rty_stDTC_Status_Byte3[8]);
extern void SysFaultReac_Mdl_SysDiag_Learn_Fail_Reason(uint16_T
  rtu_stLearnFailReason_PosLost, uint16_T rtu_stLearnFailReason_Voltage,
  uint16_T rtu_stLearnFailReason_PowerMode, uint16_T
  rtu_stLearnFailReason_Thermal, uint16_T rtu_stLearnFailReason_Block, const
  boolean_T *rtu_LearnAdap_isInterrupted, const VehCom_Cmd_t *rtu_stActvMovCmd,
  uint16_T *rty_SysFltRctn_stsLearnFailReason);
extern void SysFaultReac_Mdl_determineUnlearnedReason(uint16_T
  rtu_SysFltRctn_stsSysFaultReaction, boolean_T rtu_PIDDID_isReqUnlearn, const
  CtrlLog_RelearnMode_t *rtu_CtrlLogic_stRelearnMode, uint16_T
  rtu_hallUnlearnReason, uint16_T rtu_blockUnlearnReason, uint16_T
  rtu_calUnlearnReason, uint16_T rtu_posLostUnlearnReason, uint16_T rtu_isNvmErr,
  const boolean_T *rtu_isLearnComplete, boolean_T rtu_EEPEraseErr, boolean_T
  rtu_EEPCmpErr, uint16_T *rty_unlearnedReason);
extern void SysFaultReac_Mdl_SysFaultRct_Init(void);
extern void SysFaultReac_Mdl_SysFaultRct(const CfgParam_CALBus_t
  *rtu_CfgParam_CAL, uint16_T rtu_SBC_FaultStatus, const boolean_T
  *rtu_VehCom_bSleepReady, const uint8_T *rtu_DTC_HALL_Hall1, const uint8_T
  *rtu_DTC_MtrControl, const boolean_T *rtu_isHallSupplyFaultDebounced, const
  boolean_T *rtu_isHallSuppFlt, const SysFltDiag_LastStopReason_t
  *rtu_stCtrlLogicLastStpReason, const CtrlLog_tenTargetDirection *rtu_stDirCmd,
  const ThermProt_MtrTempClass_t *rtu_stMtrTempClass, const
  ThermProt_MosfetTempClass_t *rtu_stMosfetMtrTempClass, const boolean_T
  *rtu_isSleepAllowed, const CtrlLog_RelearnMode_t *rtu_CtrlLogic_stRelearnMode,
  const uint8_T *rtu_DTC_MTR_Dir, const MtrCtrl_MtrPwrd_t
  *rtu_MtrCtrl_MtrPwrdState, const MtrCtrl_MtrDir_t *rtu_stSetMtrDir, const
  VehCom_Cmd_t *rtu_stActvMovCmd, const BlockDet_FltType_t
  *rtu_BlockDet_stFltType, const boolean_T *rtu_isLearnComplete, const boolean_T
  *rtu_LearnAdap_isInterrupted, const boolean_T *rtu_VoltMon_isFlctnDet, const
  VoltMon_VoltClass_t *rtu_VoltMon_stVoltClass, const boolean_T
  *rtu_stAmbTempVld, const boolean_T *rtu_stMosfetTempVld, const uint16_T
  *rtu_VoltMon_u12VBatt, const boolean_T *rtu_stBattVltVld, const boolean_T
  *rtu_PosMon_isCurPosVld, const boolean_T *rtu_PosMon_isOutOfRange, const
  int16_T *rtu_AmbTempMon_TAmbTemp, const boolean_T *rtu_VoltMon_isUnderVtg,
  const boolean_T *rtu_VoltMon_isOverVtg, const boolean_T *rtu_isCALFileVld,
  boolean_T rtu_PIDDID_isReqUnlearn, const StateMach_Mode_t
  *rtu_StateMach_stSysMode, uint32_T rtu_NVM_CALEXT_IDX, uint32_T
  rtu_NVM_POSMON_IDX, uint32_T rtu_NVM_LEARNADP_IDX, uint32_T rtu_NVM_RF_VLD_IDX,
  uint32_T rtu_NVM_RFTbl_IDX, uint32_T rtu_NVM_RF_VLD_BKUP_IDX, uint32_T
  rtu_NVM_RF_BKUP_IDX, uint32_T rtu_NVM_RF_VLDSYNC_BKUP_IDX, uint32_T
  rtu_NVM_CAL_DATA_IDX, boolean_T rtu_bEEP_EraseErr, boolean_T rtu_bEEP_CmpErr,
  const uint8_T rtu_stDTCCounter_Byte1[8], const uint8_T rtu_stDTCCounter_Byte2
  [8], const uint8_T rtu_stDTCCounter_Byte3[8], uint16_T
  *rty_SysFltRctn_stsUnlearnReason, uint16_T *rty_SysFltRctn_stsLearnFailReason,
  SysFltDiag_LastStopReason_t *rty_SysFltRctn_stsLasStpReason, uint8_T
  rty_SysFltRctn_stLast10DTCs[10]);

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_SysFaultReac_Mdl_T SysFaultReac_Mdl_MdlrefDW;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef SysFaultReac_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_SysFaultReac_Mdl_c_T SysFaultReac_Mdl_B;

/* Block states (default storage) */
extern DW_SysFaultReac_Mdl_f_T SysFaultReac_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_SysFaultReac_Mdl_T SysFaultReac_Mdl_PrevZCX;

#endif                                 /*SysFaultReac_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S16>/Bitwise Operator' : Unused code path elimination
 * Block '<S181>/Compare' : Unused code path elimination
 * Block '<S181>/Constant' : Unused code path elimination
 * Block '<S16>/Constant1' : Unused code path elimination
 * Block '<S16>/Constant3' : Unused code path elimination
 * Block '<S16>/Constant4' : Unused code path elimination
 * Block '<S16>/Switch2' : Unused code path elimination
 * Block '<S16>/Switch3' : Unused code path elimination
 * Block '<S16>/Switch5' : Unused code path elimination
 * Block '<S16>/bitMaskRFInvalid' : Unused code path elimination
 * Block '<S16>/bitMaskRPInvalid2' : Unused code path elimination
 * Block '<S16>/unlearnedDueToPosOutOfRange' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'SysFaultReac_Mdl'
 * '<S1>'   : 'SysFaultReac_Mdl/Model Info'
 * '<S2>'   : 'SysFaultReac_Mdl/SysFaultRct'
 * '<S3>'   : 'SysFaultReac_Mdl/SysFaultRct/AggreateNvmCrcFaults'
 * '<S4>'   : 'SysFaultReac_Mdl/SysFaultRct/CalDataFault'
 * '<S5>'   : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler'
 * '<S6>'   : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1'
 * '<S7>'   : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2'
 * '<S8>'   : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3'
 * '<S9>'   : 'SysFaultReac_Mdl/SysFaultRct/DocBlock'
 * '<S10>'  : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Failure_Reaction_Determination'
 * '<S11>'  : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination'
 * '<S12>'  : 'SysFaultReac_Mdl/SysFaultRct/Hall_Fault'
 * '<S13>'  : 'SysFaultReac_Mdl/SysFaultRct/InitialteDataStores'
 * '<S14>'  : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection'
 * '<S15>'  : 'SysFaultReac_Mdl/SysFaultRct/MotorStall'
 * '<S16>'  : 'SysFaultReac_Mdl/SysFaultRct/PositionInvalid'
 * '<S17>'  : 'SysFaultReac_Mdl/SysFaultRct/StopMtrWhenAfterRun_OFF'
 * '<S18>'  : 'SysFaultReac_Mdl/SysFaultRct/Subsystem'
 * '<S19>'  : 'SysFaultReac_Mdl/SysFaultRct/SysDiag_Learn_Fail_Reason'
 * '<S20>'  : 'SysFaultReac_Mdl/SysFaultRct/SystemStateFault'
 * '<S21>'  : 'SysFaultReac_Mdl/SysFaultRct/System_Failure_reaction_Determination'
 * '<S22>'  : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass'
 * '<S23>'  : 'SysFaultReac_Mdl/SysFaultRct/ThermalFault'
 * '<S24>'  : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason'
 * '<S25>'  : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason'
 * '<S26>'  : 'SysFaultReac_Mdl/SysFaultRct/AggreateNvmCrcFaults/DocBlock'
 * '<S27>'  : 'SysFaultReac_Mdl/SysFaultRct/CalDataFault/DocBlock'
 * '<S28>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/Detect Change'
 * '<S29>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/Detect Change1'
 * '<S30>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/Detect Change2'
 * '<S31>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/Detect Change3'
 * '<S32>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/DocBlock'
 * '<S33>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogDTCByte1Counter'
 * '<S34>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogDTCByte2Counter'
 * '<S35>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogDTCByte3Counter'
 * '<S36>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogDTCStatus'
 * '<S37>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogLast10DTCNumber'
 * '<S38>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogDTCByte1Counter/TakeHighestCtrValue'
 * '<S39>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogDTCByte2Counter/TakeHighestCtrValue'
 * '<S40>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogDTCByte3Counter/TakeHighestCtrValue'
 * '<S41>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogLast10DTCNumber/Detect Change'
 * '<S42>'  : 'SysFaultReac_Mdl/SysFaultRct/DTCHandler/LogLast10DTCNumber/Triggered Subsystem'
 * '<S43>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift'
 * '<S44>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift1'
 * '<S45>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift2'
 * '<S46>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift3'
 * '<S47>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift4'
 * '<S48>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift5'
 * '<S49>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift6'
 * '<S50>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift7'
 * '<S51>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Compare To Constant'
 * '<S52>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1'
 * '<S53>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DocBlock'
 * '<S54>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift/bit_shift'
 * '<S55>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift1/bit_shift'
 * '<S56>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift2/bit_shift'
 * '<S57>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift3/bit_shift'
 * '<S58>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift4/bit_shift'
 * '<S59>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift5/bit_shift'
 * '<S60>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift6/bit_shift'
 * '<S61>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/Bit Shift7/bit_shift'
 * '<S62>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/Compare To Constant'
 * '<S63>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/For Each Subsystem'
 * '<S64>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs'
 * '<S65>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/For Each Subsystem/Triggered Subsystem'
 * '<S66>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem'
 * '<S67>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem1'
 * '<S68>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem2'
 * '<S69>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem3'
 * '<S70>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem4'
 * '<S71>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem5'
 * '<S72>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem6'
 * '<S73>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte1/DTCCounterAndLast10DTCByte1/UpdateLast10DTCs/Triggered Subsystem7'
 * '<S74>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift'
 * '<S75>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift1'
 * '<S76>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift2'
 * '<S77>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift3'
 * '<S78>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift4'
 * '<S79>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift5'
 * '<S80>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift6'
 * '<S81>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift7'
 * '<S82>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Compare To Constant'
 * '<S83>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2'
 * '<S84>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DocBlock'
 * '<S85>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift/bit_shift'
 * '<S86>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift1/bit_shift'
 * '<S87>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift2/bit_shift'
 * '<S88>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift3/bit_shift'
 * '<S89>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift4/bit_shift'
 * '<S90>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift5/bit_shift'
 * '<S91>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift6/bit_shift'
 * '<S92>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/Bit Shift7/bit_shift'
 * '<S93>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/Compare To Constant'
 * '<S94>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/For Each Subsystem'
 * '<S95>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs'
 * '<S96>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/For Each Subsystem/Triggered Subsystem'
 * '<S97>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem'
 * '<S98>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem1'
 * '<S99>'  : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem2'
 * '<S100>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem3'
 * '<S101>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem4'
 * '<S102>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem5'
 * '<S103>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem6'
 * '<S104>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte2/DTCCounterAndLast10DTCByte2/UpdateLast10DTCs/Triggered Subsystem7'
 * '<S105>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift1'
 * '<S106>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift2'
 * '<S107>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift3'
 * '<S108>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift4'
 * '<S109>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift7'
 * '<S110>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift8'
 * '<S111>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Compare To Constant'
 * '<S112>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Compare To Constant1'
 * '<S113>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Compare To Constant2'
 * '<S114>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3'
 * '<S115>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DocBlock'
 * '<S116>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift1/bit_shift'
 * '<S117>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift2/bit_shift'
 * '<S118>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift3/bit_shift'
 * '<S119>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift4/bit_shift'
 * '<S120>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift7/bit_shift'
 * '<S121>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/Bit Shift8/bit_shift'
 * '<S122>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/Compare To Constant'
 * '<S123>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/For Each Subsystem'
 * '<S124>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs'
 * '<S125>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/For Each Subsystem/Triggered Subsystem'
 * '<S126>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem'
 * '<S127>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem1'
 * '<S128>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem2'
 * '<S129>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem3'
 * '<S130>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem4'
 * '<S131>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem5'
 * '<S132>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem6'
 * '<S133>' : 'SysFaultReac_Mdl/SysFaultRct/DTC_Status_Byte3/DTCCounterAndLast10DTCByte3/UpdateLast10DTCs/Triggered Subsystem7'
 * '<S134>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Failure_Reaction_Determination/Compare To Constant'
 * '<S135>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Failure_Reaction_Determination/Compare To Constant1'
 * '<S136>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Failure_Reaction_Determination/DocBlock'
 * '<S137>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Failure_Reaction_Determination/Subsystem'
 * '<S138>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift1'
 * '<S139>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift10'
 * '<S140>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift11'
 * '<S141>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift12'
 * '<S142>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift13'
 * '<S143>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift2'
 * '<S144>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift3'
 * '<S145>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift4'
 * '<S146>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift5'
 * '<S147>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift6'
 * '<S148>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift7'
 * '<S149>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift8'
 * '<S150>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift9'
 * '<S151>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/DocBlock'
 * '<S152>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift1/bit_shift'
 * '<S153>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift10/bit_shift'
 * '<S154>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift11/bit_shift'
 * '<S155>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift12/bit_shift'
 * '<S156>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift13/bit_shift'
 * '<S157>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift2/bit_shift'
 * '<S158>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift3/bit_shift'
 * '<S159>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift4/bit_shift'
 * '<S160>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift5/bit_shift'
 * '<S161>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift6/bit_shift'
 * '<S162>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift7/bit_shift'
 * '<S163>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift8/bit_shift'
 * '<S164>' : 'SysFaultReac_Mdl/SysFaultRct/ExtDev_Fault_Determination/Bit Shift9/bit_shift'
 * '<S165>' : 'SysFaultReac_Mdl/SysFaultRct/Hall_Fault/Compare To Constant'
 * '<S166>' : 'SysFaultReac_Mdl/SysFaultRct/Hall_Fault/Compare To Constant1'
 * '<S167>' : 'SysFaultReac_Mdl/SysFaultRct/Hall_Fault/Compare To Constant2'
 * '<S168>' : 'SysFaultReac_Mdl/SysFaultRct/Hall_Fault/Compare To Constant3'
 * '<S169>' : 'SysFaultReac_Mdl/SysFaultRct/Hall_Fault/DocBlock'
 * '<S170>' : 'SysFaultReac_Mdl/SysFaultRct/InitialteDataStores/DocBlock'
 * '<S171>' : 'SysFaultReac_Mdl/SysFaultRct/InitialteDataStores/Triggered Subsystem2'
 * '<S172>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/Compare To Constant'
 * '<S173>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/Compare To Constant1'
 * '<S174>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/Compare To Constant2'
 * '<S175>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/Compare To Constant3'
 * '<S176>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/Compare To Constant4'
 * '<S177>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/Compare To Constant5'
 * '<S178>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/Compare To Constant6'
 * '<S179>' : 'SysFaultReac_Mdl/SysFaultRct/MotorDirection/DocBlock'
 * '<S180>' : 'SysFaultReac_Mdl/SysFaultRct/MotorStall/DocBlock'
 * '<S181>' : 'SysFaultReac_Mdl/SysFaultRct/PositionInvalid/Compare To Constant'
 * '<S182>' : 'SysFaultReac_Mdl/SysFaultRct/PositionInvalid/Compare To Constant1'
 * '<S183>' : 'SysFaultReac_Mdl/SysFaultRct/PositionInvalid/Detect Increase'
 * '<S184>' : 'SysFaultReac_Mdl/SysFaultRct/PositionInvalid/DocBlock'
 * '<S185>' : 'SysFaultReac_Mdl/SysFaultRct/Subsystem/DocBlock'
 * '<S186>' : 'SysFaultReac_Mdl/SysFaultRct/SysDiag_Learn_Fail_Reason/Compare To Constant'
 * '<S187>' : 'SysFaultReac_Mdl/SysFaultRct/SysDiag_Learn_Fail_Reason/Compare To Zero1'
 * '<S188>' : 'SysFaultReac_Mdl/SysFaultRct/SysDiag_Learn_Fail_Reason/Detect Decrease'
 * '<S189>' : 'SysFaultReac_Mdl/SysFaultRct/SysDiag_Learn_Fail_Reason/DocBlock'
 * '<S190>' : 'SysFaultReac_Mdl/SysFaultRct/SysDiag_Learn_Fail_Reason/Triggered Subsystem'
 * '<S191>' : 'SysFaultReac_Mdl/SysFaultRct/SysDiag_Learn_Fail_Reason/Triggered Subsystem/Subsystem'
 * '<S192>' : 'SysFaultReac_Mdl/SysFaultRct/SystemStateFault/Compare To Constant'
 * '<S193>' : 'SysFaultReac_Mdl/SysFaultRct/SystemStateFault/Compare To Constant1'
 * '<S194>' : 'SysFaultReac_Mdl/SysFaultRct/SystemStateFault/DocBlock'
 * '<S195>' : 'SysFaultReac_Mdl/SysFaultRct/System_Failure_reaction_Determination/DocBlock'
 * '<S196>' : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass/Compare To Constant'
 * '<S197>' : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass/Compare To Constant1'
 * '<S198>' : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass/Compare To Constant2'
 * '<S199>' : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass/Compare To Constant3'
 * '<S200>' : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass/DocBlock'
 * '<S201>' : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass/Roof Error Determination (LV and HV)'
 * '<S202>' : 'SysFaultReac_Mdl/SysFaultRct/TempVoltClass/Roof Error Determination (LV and HV)/DocBlock'
 * '<S203>' : 'SysFaultReac_Mdl/SysFaultRct/ThermalFault/Compare To Constant2'
 * '<S204>' : 'SysFaultReac_Mdl/SysFaultRct/ThermalFault/Compare To Constant3'
 * '<S205>' : 'SysFaultReac_Mdl/SysFaultRct/ThermalFault/DocBlock'
 * '<S206>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Compare To Constant1'
 * '<S207>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Compare To Constant3'
 * '<S208>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Compare To Zero'
 * '<S209>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Compare To Zero1'
 * '<S210>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Compare To Zero2'
 * '<S211>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Detect Increase'
 * '<S212>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Detect Increase1'
 * '<S213>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Detect Increase2'
 * '<S214>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/DocBlock'
 * '<S215>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/Enabled Subsystem'
 * '<S216>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason'
 * '<S217>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant'
 * '<S218>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant1'
 * '<S219>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant2'
 * '<S220>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant3'
 * '<S221>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant4'
 * '<S222>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant5'
 * '<S223>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant6'
 * '<S224>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant7'
 * '<S225>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant8'
 * '<S226>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Compare To Constant9'
 * '<S227>' : 'SysFaultReac_Mdl/SysFaultRct/UpdateLastStopReason/GetLastestStopReason/Detect Increase'
 * '<S228>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/Compare To Constant'
 * '<S229>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/Compare To Zero'
 * '<S230>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/Compare To Zero1'
 * '<S231>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/Detect Decrease'
 * '<S232>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/DocBlock'
 * '<S233>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/captureUnlearnReason'
 * '<S234>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/captureUnlearnReason/Compare To Zero'
 * '<S235>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/captureUnlearnReason/DocBlock'
 * '<S236>' : 'SysFaultReac_Mdl/SysFaultRct/determineUnlearnedReason/captureUnlearnReason/Subsystem'
 */
#endif                                 /* RTW_HEADER_SysFaultReac_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
