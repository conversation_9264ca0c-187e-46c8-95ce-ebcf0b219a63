/*
 * File: APDet_Mdl.h
 *
 * Code generated for Simulink model 'APDet_Mdl'.
 *
 * Model version                  : 1.318
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:28:38 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_APDet_Mdl_h_
#define RTW_HEADER_APDet_Mdl_h_
#ifndef APDet_Mdl_COMMON_INCLUDES_
#define APDet_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* APDet_Mdl_COMMON_INCLUDES_ */

#include "CtrlLogic_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "APDet_Mdl_types.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "MtrMdl_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "RoofOper_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "AntiPinch_Exp.h"
#include "CfgParam_Mdl_Exp.h"
#include "MtrMdl_Exp.h"
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'APDet_Mdl' */
#ifndef APDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  APDet_tenTargetDirection APDet_ATSRevDir;/* '<S10>/Data Type Conversion8' */
  uint16_T APDet_ATSRevThreshold;      /* '<S10>/Data Type Conversion6' */
  uint16_T APDet_ATSRevPosition;       /* '<S10>/Data Type Conversion7' */
  int16_T trackingForce;               /* '<S27>/tracking' */
  uint8_T DataTypeConversion;          /* '<S25>/Data Type Conversion' */
  uint8_T DataTypeConversion3[18];     /* '<S12>/Data Type Conversion3' */
  int8_T Saturation;                   /* '<S31>/Saturation' */
} B_APDet_Mdl_c_T;

#endif                                 /*APDet_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'APDet_Mdl' */
#ifndef APDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  int16_T UnitDelay_DSTATE;            /* '<S7>/Unit Delay' */
  int16_T UnitDelay_DSTATE_k;          /* '<S31>/Unit Delay' */
  int16_T UnitDelay_DSTATE_i;          /* '<S42>/Unit Delay' */
  uint16_T UnitDelay_DSTATE_iz;        /* '<S25>/Unit Delay' */
  int8_T TappedDelay_X[14];            /* '<S31>/Tapped Delay' */
  uint8_T TappedDelay_X_l[14];         /* '<S25>/Tapped Delay' */
  boolean_T DelayInput1_DSTATE;        /* '<S43>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_k;      /* '<S44>/Delay Input1' */
} DW_APDet_Mdl_f_T;

#endif                                 /*APDet_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'APDet_Mdl' */
#ifndef APDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState captureATSRevReason_Trig_ZCE;/* '<S3>/captureATSRevReason' */
} ZCE_APDet_Mdl_T;

#endif                                 /*APDet_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef APDet_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_APDet_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*APDet_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef APDet_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_APDet_Mdl_T rtm;
} MdlrefDW_APDet_Mdl_T;

#endif                                 /*APDet_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern boolean_T PIDDID_isFactoryMode;
                                /* Simulink.Signal object 'PIDDID_isFactoryMode'
                                 * Diagnostic Factory Mode
                                   1 - Enabled
                                   0 -Disabled

                                 */
extern void APDet_Mdl_Init(void);
extern void APDet_Mdl(const int16_T *rtu_MtrMdlBus_MtrMdl_TEstMtrTemp, const
                      boolean_T *rtu_MtrMdlBus_MtrMdl_isMotorInStartUp, const
                      boolean_T *rtu_MtrMdlBus_MtrMdl_isCalcForTorqVld, const
                      uint16_T *rtu_SWC_DataHndlLyrBus_UsgHistBus_UsgHist_nCycle,
                      const CtrlLog_tenTargetDirection
                      *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand,
                      const uint16_T
                      *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const
                      uint16_T *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos,
                      const uint16_T
                      *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_relativePinionSize,
                      const int16_T
                      *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp,
                      const int16_T *rtu_RefForceBus_RefForce_FreferenceForce,
                      const boolean_T
                      *rtu_RefForceBus_RefForce_isCalcRefForceVld, const
                      boolean_T *rtu_RefForceBus_RefForce_isPosOutsideRefField,
                      const uint16_T *rtu_ThresForceBus_ThresForce_FatsThreshold,
                      const boolean_T
                      *rtu_LearnAdapBus_LearnAdap_isLearnComplete, boolean_T
                      *rty_APDetBus_APDet_isAtsDetected, int16_T
                      *rty_APDetBus_APDet_FDifference, boolean_T
                      *rty_APDetBus_APDet_isInputsAtsValid, int16_T
                      *rty_APDetBus_APDet_FTracking, uint8_T
                      rty_APDetBus_UsgHist_ATSRevReason[18], uint16_T
                      *rty_APDetBus_APDet_ATSRevThreshold, uint16_T
                      *rty_APDetBus_APDet_ATSRevPosition,
                      APDet_tenTargetDirection *rty_APDetBus_APDet_ATSRevDir,
                      boolean_T *rty_APDetBus_APDet_isAtsEnabled);

/* Model reference registration function */
extern void APDet_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef APDet_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_APDet_Mdl_T APDet_Mdl_MdlrefDW;

#endif                                 /*APDet_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef APDet_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_APDet_Mdl_c_T APDet_Mdl_B;

/* Block states (default storage) */
extern DW_APDet_Mdl_f_T APDet_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_APDet_Mdl_T APDet_Mdl_PrevZCX;

#endif                                 /*APDet_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'APDet_Mdl'
 * '<S1>'   : 'APDet_Mdl/AntiPinch'
 * '<S2>'   : 'APDet_Mdl/Model Info'
 * '<S3>'   : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration'
 * '<S4>'   : 'APDet_Mdl/AntiPinch/DocBlock'
 * '<S5>'   : 'APDet_Mdl/AntiPinch/checkAtsInputs'
 * '<S6>'   : 'APDet_Mdl/AntiPinch/detectTrap'
 * '<S7>'   : 'APDet_Mdl/AntiPinch/determineTracking'
 * '<S8>'   : 'APDet_Mdl/AntiPinch/equalTheForce'
 * '<S9>'   : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration/DocBlock1'
 * '<S10>'  : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration/captureATSRevReason'
 * '<S11>'  : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration/captureATSRevReason/DocBlock'
 * '<S12>'  : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration/captureATSRevReason/RevDataConversion'
 * '<S13>'  : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration/captureATSRevReason/RevDataConversion/Bit Shift'
 * '<S14>'  : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration/captureATSRevReason/RevDataConversion/DocBlock'
 * '<S15>'  : 'APDet_Mdl/AntiPinch/ATSRevReasonArbitration/captureATSRevReason/RevDataConversion/Bit Shift/bit_shift'
 * '<S16>'  : 'APDet_Mdl/AntiPinch/checkAtsInputs/DocBlock'
 * '<S17>'  : 'APDet_Mdl/AntiPinch/detectTrap/Calc_of_isPinch'
 * '<S18>'  : 'APDet_Mdl/AntiPinch/detectTrap/DocBlock'
 * '<S19>'  : 'APDet_Mdl/AntiPinch/detectTrap/Calc_of_isPinch/Compare To Constant'
 * '<S20>'  : 'APDet_Mdl/AntiPinch/detectTrap/Calc_of_isPinch/Compare To Constant1'
 * '<S21>'  : 'APDet_Mdl/AntiPinch/detectTrap/Calc_of_isPinch/DocBlock'
 * '<S22>'  : 'APDet_Mdl/AntiPinch/determineTracking/DocBlock'
 * '<S23>'  : 'APDet_Mdl/AntiPinch/determineTracking/Subsystem'
 * '<S24>'  : 'APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive'
 * '<S25>'  : 'APDet_Mdl/AntiPinch/determineTracking/distanceTraveled'
 * '<S26>'  : 'APDet_Mdl/AntiPinch/determineTracking/thermalParameterAdaption'
 * '<S27>'  : 'APDet_Mdl/AntiPinch/determineTracking/tracking'
 * '<S28>'  : 'APDet_Mdl/AntiPinch/determineTracking/Subsystem/DocBlock'
 * '<S29>'  : 'APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive/DocBlock'
 * '<S30>'  : 'APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive/determineIfTrackingActive'
 * '<S31>'  : 'APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive/determineInclineInRange'
 * '<S32>'  : 'APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive/determineIfTrackingActive/DocBlock'
 * '<S33>'  : 'APDet_Mdl/AntiPinch/determineTracking/determineTrackingActive/determineInclineInRange/DocBlock'
 * '<S34>'  : 'APDet_Mdl/AntiPinch/determineTracking/distanceTraveled/DocBlock'
 * '<S35>'  : 'APDet_Mdl/AntiPinch/determineTracking/thermalParameterAdaption/Compare To Zero'
 * '<S36>'  : 'APDet_Mdl/AntiPinch/determineTracking/thermalParameterAdaption/DocBlock'
 * '<S37>'  : 'APDet_Mdl/AntiPinch/determineTracking/tracking/DocBlock'
 * '<S38>'  : 'APDet_Mdl/AntiPinch/determineTracking/tracking/tracking'
 * '<S39>'  : 'APDet_Mdl/AntiPinch/determineTracking/tracking/tracking/DocBlock'
 * '<S40>'  : 'APDet_Mdl/AntiPinch/equalTheForce/DocBlock'
 * '<S41>'  : 'APDet_Mdl/AntiPinch/equalTheForce/determineForceEqualizing'
 * '<S42>'  : 'APDet_Mdl/AntiPinch/equalTheForce/getSignalsFromBus'
 * '<S43>'  : 'APDet_Mdl/AntiPinch/equalTheForce/determineForceEqualizing/Detect Change'
 * '<S44>'  : 'APDet_Mdl/AntiPinch/equalTheForce/determineForceEqualizing/Detect Decrease1'
 * '<S45>'  : 'APDet_Mdl/AntiPinch/equalTheForce/determineForceEqualizing/DocBlock'
 * '<S46>'  : 'APDet_Mdl/AntiPinch/equalTheForce/getSignalsFromBus/DocBlock'
 */
#endif                                 /* RTW_HEADER_APDet_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
