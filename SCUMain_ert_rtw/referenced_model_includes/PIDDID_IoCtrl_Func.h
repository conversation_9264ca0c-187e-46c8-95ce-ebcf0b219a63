/*
 * File: PIDDID_IoCtrl_Func.h
 *
 * Code generated for Simulink model 'PIDDID_IoCtrl_Func'.
 *
 * Model version                  : 11.82
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:35:06 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PIDDID_IoCtrl_Func_h_
#define RTW_HEADER_PIDDID_IoCtrl_Func_h_
#ifndef PIDDID_IoCtrl_Func_COMMON_INCLUDES_
#define PIDDID_IoCtrl_Func_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* PIDDID_IoCtrl_Func_COMMON_INCLUDES_ */

#include "VoltMon_ExpTypes.h"
#include "PIDDID_IoCtrl_Func_types.h"
#include "BlockDet_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "ThermProt_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "StateMach_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "PIDDID_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"
#ifndef PIDDID_IoCtrl_Func_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_PIDDID_IoCtrl_Func_T {
  const char_T **errorStatus;
};

#endif                                 /*PIDDID_IoCtrl_Func_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_IoCtrl_Func_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_PIDDID_IoCtrl_Func_T rtm;
} MdlrefDW_PIDDID_IoCtrl_Func_T;

#endif                                 /*PIDDID_IoCtrl_Func_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern int16_T PIDDID_IOCtrl_TAmbTemp;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TAmbTemp' */
extern int16_T PIDDID_IOCtrl_TMosfetTemp;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_TMosfetTemp' */
extern uint16_T PIDDID_IOCtrl_TBattVtg;
                           /* Simulink.Signal object 'PIDDID_IOCtrl_TBattVtg' */
extern uint16_T PIDDID_IOCtrl_THallSuppVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_THallSuppVtg' */
extern uint16_T PIDDID_IOCtrl_TMtrCurrA;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrCurrA' */
extern uint16_T PIDDID_IOCtrl_TMtrCurrB;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrCurrB' */
extern uint16_T PIDDID_IOCtrl_TMtrFdbkAVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkAVtg' */
extern uint16_T PIDDID_IOCtrl_TMtrFdbkBVtg;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_TMtrFdbkBVtg' */
extern uint16_T PIDDID_IOCtrl_TSwtchIpA;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TSwtchIpA' */
extern uint16_T PIDDID_IOCtrl_TSwtchIpB;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_TSwtchIpB' */
extern uint16_T PIDDID_IOcontrolMaskRecord;
                       /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecord' */
extern uint16_T PIDDID_IOcontrolMaskRecordDigiOut;
                /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordDigiOut' */
extern uint16_T PIDDID_IOcontrolMaskRecordSysICOut;
               /* Simulink.Signal object 'PIDDID_IOcontrolMaskRecordSysICOut' */
extern uint8_T PIDDID_IOCtrl_uMtrDtyCyc;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_uMtrDtyCyc' */
extern boolean_T PIDDID_IOCtrl_HallSenOneIpSt;
                     /* Simulink.Signal object 'PIDDID_IOCtrl_HallSenOneIpSt' */
extern boolean_T PIDDID_IOCtrl_HallSenTwoIpSt;
                     /* Simulink.Signal object 'PIDDID_IOCtrl_HallSenTwoIpSt' */
extern boolean_T PIDDID_IOCtrl_INTNInpState;
                       /* Simulink.Signal object 'PIDDID_IOCtrl_INTNInpState' */
extern boolean_T PIDDID_IOCtrl_isHallOut;
                          /* Simulink.Signal object 'PIDDID_IOCtrl_isHallOut' */
extern boolean_T PIDDID_IOCtrl_isHallSupplyCtrl;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isHallSupplyCtrl' */
extern boolean_T PIDDID_IOCtrl_isMtrCCWAct;
                        /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCCWAct' */
extern boolean_T PIDDID_IOCtrl_isMtrCWAct;
                         /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrCWAct' */
extern boolean_T PIDDID_IOCtrl_isMtrFdbkCtrl;
                      /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFdbkCtrl' */
extern boolean_T PIDDID_IOCtrl_isMtrFltrBlankA;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankA' */
extern boolean_T PIDDID_IOCtrl_isMtrFltrBlankB;
                    /* Simulink.Signal object 'PIDDID_IOCtrl_isMtrFltrBlankB' */
extern boolean_T PIDDID_IOCtrl_isSwtchSupplyCtrl;
                  /* Simulink.Signal object 'PIDDID_IOCtrl_isSwtchSupplyCtrl' */
extern boolean_T PIDDID_IOCtrl_isVBATMeasEnable;
                   /* Simulink.Signal object 'PIDDID_IOCtrl_isVBATMeasEnable' */
extern void PIDDID_IoCtrl_Func_Init(uint8_T rty_srcBufOut[22]);
extern void PIDDID_IoCtrl_Func(const uint8_T rtu_srcBufIn[22], const uint16_T
  *rtu_ioDID, const uint8_T *rtu_CtrlParam, const uint8_T *rtu_ioLen, const
  VoltMon_VoltClass_t *rtu_SWC_DiagLyrBus_PIDDIDBus_VoltMon_VoltMon_stVoltClass,
  uint8_T *rty_retVal, uint8_T rty_srcBufOut[22]);

/* Model reference registration function */
extern void PIDDID_IoCtrl_Func_initialize(const char_T **rt_errorStatus);

#ifndef PIDDID_IoCtrl_Func_MDLREF_HIDE_CHILD_

extern MdlrefDW_PIDDID_IoCtrl_Func_T PIDDID_IoCtrl_Func_MdlrefDW;

#endif                                 /*PIDDID_IoCtrl_Func_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PIDDID_IoCtrl_Func'
 * '<S1>'   : 'PIDDID_IoCtrl_Func/DocBlock'
 * '<S2>'   : 'PIDDID_IoCtrl_Func/IoCtrl'
 * '<S3>'   : 'PIDDID_IoCtrl_Func/Model Info'
 */
#endif                                 /* RTW_HEADER_PIDDID_IoCtrl_Func_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
