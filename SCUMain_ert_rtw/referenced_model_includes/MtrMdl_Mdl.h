/*
 * File: MtrMdl_Mdl.h
 *
 * Code generated for Simulink model 'MtrMdl_Mdl'.
 *
 * Model version                  : 1.192
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:34:44 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_MtrMdl_Mdl_h_
#define RTW_HEADER_MtrMdl_Mdl_h_
#ifndef MtrMdl_Mdl_COMMON_INCLUDES_
#define MtrMdl_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* MtrMdl_Mdl_COMMON_INCLUDES_ */

#include "RoofSys_CommDefines.h"
#include "CtrlLogic_ExpTypes.h"
#include "MtrMdl_Mdl_types.h"
#include "RoofOper_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "UsgHist_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"
#include "MtrMdl_Exp.h"
#include "UsgHist_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'MtrMdl_Mdl' */
#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T pinionRadius;               /* '<S40>/Product' */
  int32_T Divide;                      /* '<S38>/Divide' */
  uint16_T Add;                        /* '<S12>/Add' */
  uint16_T sampleTime;                 /* '<S14>/thermalCalculationTrigger' */
  uint16_T Add_h;                      /* '<S13>/Add' */
  uint16_T Add_l;                      /* '<S10>/Add' */
  uint16_T Add_k;                      /* '<S11>/Add' */
  int16_T motorForce16B;               /* '<S40>/Data Type Conversion1' */
  int16_T Switch;                      /* '<S41>/Switch' */
  int16_T Product2;                    /* '<S39>/Product2' */
  int16_T Saturation2;                 /* '<S7>/Saturation2' */
  int16_T Subtract;                    /* '<S40>/Subtract' */
  int16_T motorForce32B;               /* '<S40>/Divide' */
  int16_T motorForce16B_g;             /* '<S40>/Data Type Conversion' */
  int16_T MtrMdl_MbrakeTorque;         /* '<S40>/Product4' */
  int16_T calculateCaseTemperature_o1; /* '<S29>/calculateCaseTemperature' */
  int16_T calculateCaseTemperature_o1_e;/* '<S28>/calculateCaseTemperature' */
  boolean_T MtrMdl_isMotorInStartUp;   /* '<S48>/determineMotorStartUp' */
  boolean_T MtrMdl_isStartupRdy;       /* '<S14>/thermalCalculationTrigger' */
} B_MtrMdl_Mdl_c_T;

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'MtrMdl_Mdl' */
#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  CtrlLog_tenTargetDirection curDir_prev;/* '<S48>/determineMotorStartUp' */
  CtrlLog_tenTargetDirection curDir_start;/* '<S48>/determineMotorStartUp' */
  int32_T TappedDelay1_DWORK1[19];     /* '<S38>/Tapped Delay1' */
  int16_T DelayInput1_DSTATE;          /* '<S24>/Delay Input1' */
  int16_T DelayInput1_DSTATE_c;        /* '<S25>/Delay Input1' */
  uint16_T UnitDelay_1_DSTATE;         /* '<S2>/Unit Delay' */
  uint16_T UnitDelay_DSTATE;           /* '<S5>/UnitDelay' */
  uint16_T UnitDelay_2_DSTATE;         /* '<S2>/Unit Delay' */
  uint16_T UnitDelay_3_DSTATE;         /* '<S2>/Unit Delay' */
  uint16_T UnitDelay1_DSTATE;          /* '<S5>/UnitDelay1' */
  uint16_T UnitDelay2_DSTATE;          /* '<S5>/UnitDelay2' */
  uint16_T UnitDelay_4_DSTATE;         /* '<S2>/Unit Delay' */
  uint16_T UnitDelay3_DSTATE;          /* '<S5>/UnitDelay3' */
  int16_T UnitDelay_DSTATE_b;          /* '<S28>/UnitDelay' */
  int16_T UnitDelay_DSTATE_e;          /* '<S29>/UnitDelay' */
  uint16_T startUpDistance;            /* '<S48>/determineMotorStartUp' */
  uint16_T startPos;                   /* '<S48>/determineMotorStartUp' */
  uint8_T is_c5_MtrMdl_Mdl;            /* '<S48>/determineMotorStartUp' */
  uint8_T is_active_c5_MtrMdl_Mdl;     /* '<S48>/determineMotorStartUp' */
  uint8_T is_c3_MtrMdl_Mdl;            /* '<S14>/thermalCalculationTrigger' */
  uint8_T cnt;                         /* '<S14>/thermalCalculationTrigger' */
  uint8_T is_active_c3_MtrMdl_Mdl;     /* '<S14>/thermalCalculationTrigger' */
  uint8_T temporalCounter_i1;          /* '<S14>/thermalCalculationTrigger' */
  PnlOp_MtrCmd_t prevDir;              /* '<S48>/determineMotorStartUp' */
  boolean_T gearboxCalculation_MODE;   /* '<S6>/gearboxCalculation' */
  boolean_T forceCalculation_MODE;     /* '<S6>/forceCalculation' */
} DW_MtrMdl_Mdl_f_T;

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'MtrMdl_Mdl' */
#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const uint8_T Width1;                /* '<S38>/Width1' */
} ConstB_MtrMdl_Mdl_h_T;

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_MtrMdl_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_MtrMdl_Mdl_T rtm;
} MdlrefDW_MtrMdl_Mdl_T;

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T MtrMdl_pwmVoltage;
                                /* Simulink.Signal object 'MtrMdl_pwmVoltage' */
extern void MtrMdl_Mdl_Init(void);
extern void MtrMdl_Mdl_Disable(void);
extern void MtrMdl_Mdl(const MtrCtrl_MtrDir_t
  *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir, const int16_T
  *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDutyCycle, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const uint16_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const boolean_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_relativePinionSize, const int16_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const boolean_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld, const uint16_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd, const boolean_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_isMtrSpdVld, int16_T
  *rty_MtrMdlBus_MtrMdl_stCalcForTorq_FcalcForce, int16_T
  *rty_MtrMdlBus_MtrMdl_stCalcForTorq_McalcTorque, int16_T
  *rty_MtrMdlBus_MtrMdl_TEstMtrTemp, int16_T *rty_MtrMdlBus_MtrMdl_TEstCaseTemp,
  boolean_T *rty_MtrMdlBus_MtrMdl_isMotorInStartUp, boolean_T
  *rty_MtrMdlBus_MtrMdl_isStartupRdy, boolean_T
  *rty_MtrMdlBus_MtrMdl_isCalcForTorqVld, boolean_T
  *rty_MtrMdlBus_MtrMdl_isEstMtrTempVld, int16_T
  *rty_MtrMdlBus_MtrMdl_MbrakeTorque);

/* Model reference registration function */
extern void MtrMdl_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

extern void MtrMdl_Mdl_calculateBackEmfConstant_Init(uint16_T
  *rty_backEmfConstant);
extern void MtrMdl_Mdl_calculateBackEmfConstant(uint16_T rtu_backEmfConstantT0,
  uint16_T rtu_tempCoeffBackEmfConst, int16_T rtu_temperature, uint16_T
  *rty_backEmfConstant);
extern void MtrMdl_Mdl_thermalCalculation_Init(void);
extern void MtrMdl_Mdl_thermalCalculation(void);

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_MtrMdl_Mdl_T MtrMdl_Mdl_MdlrefDW;

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef MtrMdl_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_MtrMdl_Mdl_c_T MtrMdl_Mdl_B;

/* Block states (default storage) */
extern DW_MtrMdl_Mdl_f_T MtrMdl_Mdl_DW;

#endif                                 /*MtrMdl_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S7>/RelationalOperator1' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'MtrMdl_Mdl'
 * '<S1>'   : 'MtrMdl_Mdl/Model Info'
 * '<S2>'   : 'MtrMdl_Mdl/MotorModel'
 * '<S3>'   : 'MtrMdl_Mdl/RunAdvise'
 * '<S4>'   : 'MtrMdl_Mdl/MotorModel/DocBlock'
 * '<S5>'   : 'MtrMdl_Mdl/MotorModel/calcMotorTemp'
 * '<S6>'   : 'MtrMdl_Mdl/MotorModel/calcMotorTorque'
 * '<S7>'   : 'MtrMdl_Mdl/MotorModel/checkDataVld'
 * '<S8>'   : 'MtrMdl_Mdl/MotorModel/determineMotorStartUp'
 * '<S9>'   : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/DocBlock'
 * '<S10>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateBackEmfConstant'
 * '<S11>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateGearboxEfficiency'
 * '<S12>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateResistanceMotorCoil'
 * '<S13>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateTorqueConstant'
 * '<S14>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/counterThermalCalculations'
 * '<S15>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/setOutputMotorTemperature'
 * '<S16>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation'
 * '<S17>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateBackEmfConstant/DocBlock'
 * '<S18>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateGearboxEfficiency/DocBlock'
 * '<S19>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateResistanceMotorCoil/DocBlock'
 * '<S20>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/calculateTorqueConstant/DocBlock'
 * '<S21>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/counterThermalCalculations/DocBlock'
 * '<S22>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/counterThermalCalculations/thermalCalculationTrigger'
 * '<S23>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/counterThermalCalculations/thermalCalculationTrigger/DocBlock'
 * '<S24>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/setOutputMotorTemperature/Detect Change'
 * '<S25>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/setOutputMotorTemperature/Detect Change1'
 * '<S26>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/setOutputMotorTemperature/DocBlock'
 * '<S27>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/DocBlock'
 * '<S28>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateCaseTemperature'
 * '<S29>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateCoilTemperature'
 * '<S30>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateTemperature'
 * '<S31>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateCaseTemperature/DocBlock'
 * '<S32>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateCoilTemperature/DocBlock'
 * '<S33>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateTemperature/DocBlock'
 * '<S34>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateTemperature/calculateTemp'
 * '<S35>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTemp/thermalCalculation/calculateTemperature/calculateTemp/DocBlock'
 * '<S36>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/Compare To Constant'
 * '<S37>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/DocBlock'
 * '<S38>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/determineEffectiveMotorVoltage'
 * '<S39>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/forceCalculation'
 * '<S40>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/gearboxCalculation'
 * '<S41>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/powerLossCorrection'
 * '<S42>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/determineEffectiveMotorVoltage/DocBlock'
 * '<S43>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/forceCalculation/DocBlock'
 * '<S44>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/gearboxCalculation/DocBlock'
 * '<S45>'  : 'MtrMdl_Mdl/MotorModel/calcMotorTorque/powerLossCorrection/DocBlock'
 * '<S46>'  : 'MtrMdl_Mdl/MotorModel/checkDataVld/DocBlock'
 * '<S47>'  : 'MtrMdl_Mdl/MotorModel/determineMotorStartUp/DocBlock'
 * '<S48>'  : 'MtrMdl_Mdl/MotorModel/determineMotorStartUp/determineMotorStartUp'
 * '<S49>'  : 'MtrMdl_Mdl/MotorModel/determineMotorStartUp/determineMotorStartUp/DocBlock'
 * '<S50>'  : 'MtrMdl_Mdl/MotorModel/determineMotorStartUp/determineMotorStartUp/determineMotorStartUp'
 * '<S51>'  : 'MtrMdl_Mdl/MotorModel/determineMotorStartUp/determineMotorStartUp/determineMotorStartUp/DocBlock'
 */
#endif                                 /* RTW_HEADER_MtrMdl_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
