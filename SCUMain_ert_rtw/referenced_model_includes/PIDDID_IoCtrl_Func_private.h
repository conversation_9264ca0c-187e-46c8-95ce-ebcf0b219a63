/*
 * File: PIDDID_IoCtrl_Func_private.h
 *
 * Code generated for Simulink model 'PIDDID_IoCtrl_Func'.
 *
 * Model version                  : 11.82
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:35:06 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PIDDID_IoCtrl_Func_private_h_
#define RTW_HEADER_PIDDID_IoCtrl_Func_private_h_
#include "rtwtypes.h"
#include "PIDDID_IoCtrl_Func_types.h"

/* Macros for accessing real-time model data structure */
#ifndef rtmGetErrorStatus
#define rtmGetErrorStatus(rtm)         (*((rtm)->errorStatus))
#endif

#ifndef rtmSetErrorStatus
#define rtmSetErrorStatus(rtm, val)    (*((rtm)->errorStatus) = (val))
#endif

#ifndef rtmGetErrorStatusPointer
#define rtmGetErrorStatusPointer(rtm)  (rtm)->errorStatus
#endif

#ifndef rtmSetErrorStatusPointer
#define rtmSetErrorStatusPointer(rtm, val) ((rtm)->errorStatus = (val))
#endif
#endif                            /* RTW_HEADER_PIDDID_IoCtrl_Func_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
