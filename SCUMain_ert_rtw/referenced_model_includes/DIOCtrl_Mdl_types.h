/*
 * File: DIOCtrl_Mdl_types.h
 *
 * Code generated for Simulink model 'DIOCtrl_Mdl'.
 *
 * Model version                  : 1.199
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:31:19 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_DIOCtrl_Mdl_types_h_
#define RTW_HEADER_DIOCtrl_Mdl_types_h_
#include "StateMach_ExpTypes.h"
#include "rtwtypes.h"
#ifndef DEFINED_TYPEDEF_FOR_StateMachBus_
#define DEFINED_TYPEDEF_FOR_StateMachBus_

/* Bus object for State Machine signals
   already Present. */
typedef struct {
  /* Sunroof System Mode */
  StateMach_Mode_t StateMach_stSysMode;
} StateMachBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SWC_StateMachLyrBus_
#define DEFINED_TYPEDEF_FOR_SWC_StateMachLyrBus_

typedef struct {
  StateMachBus StateMachBus;
} SWC_StateMachLyrBus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_DIOCtrlBus_
#define DEFINED_TYPEDEF_FOR_DIOCtrlBus_

typedef struct {
  boolean_T DIOCtrl_PINReadStatus;
  boolean_T DIOCtrl_isVBATMeasEnbPinStatus;
  boolean_T DIOCtrl_isHallOutPinStatus;
  boolean_T DIOCtrl_isMtrFltrBlankAPinStatus;
  boolean_T DIOCtrl_isMtrFltrBlankBPinStatus;
  boolean_T DIOCtrl_isHallSens1PinStatus;
  boolean_T DIOCtrl_isHallSens2PinStatus;
  boolean_T DIOCtrl_isINTPinStatus;
} DIOCtrlBus;

#endif

/* Forward declaration for rtModel */
typedef struct tag_RTM_DIOCtrl_Mdl_T RT_MODEL_DIOCtrl_Mdl_T;

#endif                                 /* RTW_HEADER_DIOCtrl_Mdl_types_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
