/*
 * File: AmbTempMon_Mdl.h
 *
 * Code generated for Simulink model 'AmbTempMon_Mdl'.
 *
 * Model version                  : 1.183
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:29:03 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_AmbTempMon_Mdl_h_
#define RTW_HEADER_AmbTempMon_Mdl_h_
#ifndef AmbTempMon_Mdl_COMMON_INCLUDES_
#define AmbTempMon_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* AmbTempMon_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "AmbTempMon_Mdl_types.h"
#include "PosMon_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "HallDeco_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "AmbTempMon_Exp.h"
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'AmbTempMon_Mdl' */
#ifndef AmbTempMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T SFunction;                   /* '<S9>/S-Function' */
  boolean_T AmbTempMon_TAmbTemp_g;     /* '<S15>/Logical Operator' */
  boolean_T AmbTempMon_TAmbTemp_e;     /* '<S14>/Logical Operator' */
} B_AmbTempMon_Mdl_c_T;

#endif                                 /*AmbTempMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'AmbTempMon_Mdl' */
#ifndef AmbTempMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  boolean_T UnitDelay_DSTATE;          /* '<S16>/Unit Delay' */
} DW_AmbTempMon_Mdl_f_T;

#endif                                 /*AmbTempMon_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'AmbTempMon_Mdl' */
#ifndef AmbTempMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState TriggeredSubsystem_Trig_ZCE;/* '<S4>/Triggered Subsystem' */
} ZCE_AmbTempMon_Mdl_T;

#endif                                 /*AmbTempMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef AmbTempMon_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_AmbTempMon_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*AmbTempMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef AmbTempMon_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_AmbTempMon_Mdl_T rtm;
} MdlrefDW_AmbTempMon_Mdl_T;

#endif                                 /*AmbTempMon_Mdl_MDLREF_HIDE_CHILD_*/

extern void AmbTempMon_Mdl_Init(void);
extern void AmbTempMon_Mdl(const boolean_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete, const int16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TAmbTemp, const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, int16_T
  *rty_AmbTempMonBus_AmbTempMon_TAmbTemp, boolean_T
  *rty_AmbTempMonBus_AmbTempMon_isAmbTempVld, uint8_T
  *rty_AmbTempMonBus_AmbTempMon_stAmbTempSensFlt);

/* Model reference registration function */
extern void AmbTempMon_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef AmbTempMon_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_AmbTempMon_Mdl_T AmbTempMon_Mdl_MdlrefDW;

#endif                                 /*AmbTempMon_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef AmbTempMon_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_AmbTempMon_Mdl_c_T AmbTempMon_Mdl_B;

/* Block states (default storage) */
extern DW_AmbTempMon_Mdl_f_T AmbTempMon_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_AmbTempMon_Mdl_T AmbTempMon_Mdl_PrevZCX;

#endif                                 /*AmbTempMon_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S7>/Compare' : Unused code path elimination
 * Block '<S7>/Constant' : Unused code path elimination
 * Block '<S4>/Constant' : Unused code path elimination
 * Block '<S4>/Constant2' : Unused code path elimination
 * Block '<S4>/Switch1' : Unused code path elimination
 * Block '<S16>/Cast To Boolean' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'AmbTempMon_Mdl'
 * '<S1>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring'
 * '<S2>'   : 'AmbTempMon_Mdl/Model Info'
 * '<S3>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/DocBlock1'
 * '<S4>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/NVM_Access'
 * '<S5>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/convertAmbTempCAL'
 * '<S6>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity'
 * '<S7>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/NVM_Access/CompareToConstant4'
 * '<S8>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/NVM_Access/DocBlock'
 * '<S9>'   : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/NVM_Access/Triggered Subsystem'
 * '<S10>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/convertAmbTempCAL/DocBlock'
 * '<S11>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/convertAmbTempCAL/DocBlock1'
 * '<S12>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/DocBlock'
 * '<S13>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity'
 * '<S14>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity/AmbTemp Validity startup'
 * '<S15>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity/AmbTemp range Validity '
 * '<S16>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity/Arbitrate different validity'
 * '<S17>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity/DocBlock'
 * '<S18>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity/AmbTemp Validity startup/DocBlock'
 * '<S19>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity/AmbTemp range Validity /DocBlock'
 * '<S20>'  : 'AmbTempMon_Mdl/AmbTemperatureMonitoring/determineAmbTempValidity/determineAmbTempValidity/Arbitrate different validity/DocBlock'
 */
#endif                                 /* RTW_HEADER_AmbTempMon_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
