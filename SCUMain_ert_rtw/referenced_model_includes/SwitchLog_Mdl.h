/*
 * File: SwitchLog_Mdl.h
 *
 * Code generated for Simulink model 'SwitchLog_Mdl'.
 *
 * Model version                  : 1.14
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:39:54 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_SwitchLog_Mdl_h_
#define RTW_HEADER_SwitchLog_Mdl_h_
#ifndef SwitchLog_Mdl_COMMON_INCLUDES_
#define SwitchLog_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* SwitchLog_Mdl_COMMON_INCLUDES_ */

#include "SwitchLog_Mdl_types.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"
#ifndef SwitchLog_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_SwitchLog_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*SwitchLog_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef SwitchLog_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_SwitchLog_Mdl_T rtm;
} MdlrefDW_SwitchLog_Mdl_T;

#endif                                 /*SwitchLog_Mdl_MDLREF_HIDE_CHILD_*/

extern void SwitchLog_Mdl(void);

/* Model reference registration function */
extern void SwitchLog_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef SwitchLog_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_SwitchLog_Mdl_T SwitchLog_Mdl_MdlrefDW;

#endif                                 /*SwitchLog_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'SwitchLog_Mdl'
 */
#endif                                 /* RTW_HEADER_SwitchLog_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
