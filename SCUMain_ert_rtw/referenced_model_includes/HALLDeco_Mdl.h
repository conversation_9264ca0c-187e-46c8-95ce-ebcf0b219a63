/*
 * File: HALLDeco_Mdl.h
 *
 * Code generated for Simulink model 'HALLDeco_Mdl'.
 *
 * Model version                  : 7.195
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:32:11 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_HALLDeco_Mdl_h_
#define RTW_HEADER_HALLDeco_Mdl_h_
#ifndef HALLDeco_Mdl_COMMON_INCLUDES_
#define HALLDeco_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* HALLDeco_Mdl_COMMON_INCLUDES_ */

#include "HallDeco_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "CtrlLogic_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "HALLDeco_Mdl_types.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'HALLDeco_Mdl' */
#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  HalErrorModeType hallFbErrorMode;    /* '<S18>/HallFailureDetectChart' */
  HalErrorModeType hallErrorMode;      /* '<S17>/HallFailureDetectChart' */
  boolean_T AND;                       /* '<S31>/AND' */
} B_HALLDeco_Mdl_c_T;

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'HALLDeco_Mdl' */
#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  CtrlLog_tenTargetDirection stMtrDirCommand_prev;/* '<S18>/HallFailureDetectChart' */
  CtrlLog_tenTargetDirection stMtrDirCommand_start;/* '<S18>/HallFailureDetectChart' */
  uint16_T UnitDelay1_DSTATE;          /* '<S5>/UnitDelay1' */
  uint16_T UnitDelay_DSTATE;           /* '<S5>/Unit Delay' */
  int16_T CurPos_prev;                 /* '<S18>/HallFailureDetectChart' */
  int16_T CurPos_start;                /* '<S18>/HallFailureDetectChart' */
  uint16_T hall_cnt;                   /* '<S18>/HallFailureDetectChart' */
  uint8_T UD_DSTATE;                   /* '<S34>/UD' */
  uint8_T UnitDelay_DSTATE_o;          /* '<S26>/UnitDelay' */
  uint8_T DelayInput1_DSTATE;          /* '<S13>/Delay Input1' */
  uint8_T UnitDelay_DSTATE_e;          /* '<S3>/UnitDelay' */
  uint8_T DelayInput1_DSTATE_l;        /* '<S8>/Delay Input1' */
  boolean_T UnitDelay_DSTATE_a;        /* '<S23>/Unit Delay' */
  boolean_T UnitDelay1_DSTATE_b;       /* '<S19>/Unit Delay1' */
  boolean_T DelayInput1_DSTATE_m;      /* '<S28>/Delay Input1' */
  boolean_T UnitDelay2_DSTATE;         /* '<S5>/UnitDelay2' */
  VehCom_Cmd_t DelayInput1_DSTATE_d;   /* '<S30>/Delay Input1' */
  uint8_T is_RunAndPass;               /* '<S18>/HallFailureDetectChart' */
  uint8_T fault;                       /* '<S18>/HallFailureDetectChart' */
  uint8_T is_active_c1_HALLDeco_Mdl;   /* '<S18>/HallFailureDetectChart' */
  uint8_T is_RunAndFail;               /* '<S17>/HallFailureDetectChart' */
  uint8_T is_RunAndPass_n;             /* '<S17>/HallFailureDetectChart' */
  uint8_T maturationCnt;               /* '<S17>/HallFailureDetectChart' */
  uint8_T deMaturationCnt;             /* '<S17>/HallFailureDetectChart' */
  uint8_T startCounts;                 /* '<S17>/HallFailureDetectChart' */
  uint8_T is_active_c3_HALLDeco_Mdl;   /* '<S17>/HallFailureDetectChart' */
  HallDeco_Dir_t startDir;             /* '<S17>/HallFailureDetectChart' */
} DW_HALLDeco_Mdl_f_T;

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'HALLDeco_Mdl' */
#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState TriggeredSubsystem_Trig_ZCE;/* '<S21>/Triggered Subsystem' */
} ZCE_HALLDeco_Mdl_T;

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_HALLDeco_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_HALLDeco_Mdl_T rtm;
} MdlrefDW_HALLDeco_Mdl_T;

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern HallDeco_Dir_t HallDeco_enDMtrDir;
                                  /* Simulink.Signal object 'HallDeco_enDMtrDir'
                                   * Get Motor condition is moving or not in C code
                                   */
extern void HALLDeco_Mdl_Init(void);
extern void HALLDeco_Mdl(const uint8_T *rtu_HallCounts, const boolean_T
  *rtu_Direction, const uint16_T *rtu_PulseTime, const MtrCtrl_MtrDir_t
  *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const VehCom_Cmd_t
  *rtu_SWC_OperLogLyrBus_ComLogBus_ComLog_stActvMovCmd, const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, const uint16_T
  *rtu_SWC_ADCMonBus_ADCMon_uHallPwrInst, HallDeco_Dir_t
  *rty_HallDecoBus_HallDeco_DMtrDir, uint8_T
  *rty_HallDecoBus_HallDeco_uiHallCountsCyclic, uint16_T
  *rty_HallDecoBus_HallDeco_rMtrSpd, boolean_T
  *rty_HallDecoBus_HallDeco_isMtrSpdVld, uint8_T *rty_HallDecoBus_DTC_HALL_Hall1,
  uint8_T *rty_HallDecoBus_DTC_HallMtrOptDir, boolean_T
  *rty_HallDecoBus_isHallSupplyFaultDebd, boolean_T
  *rty_HallDecoBus_isHallSupplyFault);

/* Model reference registration function */
extern void HALLDeco_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

extern void HALLDeco_Mdl_Dir_Timeout_Init(void);
extern void HALLDeco_Mdl_Dir_Timeout(const uint8_T *rtu_uiHallCounts, const
  boolean_T *rtu_Direction, HallDeco_Dir_t rtu_CCW_C, HallDeco_Dir_t rtu_CW_C,
  uint8_T rtu_U8Zero1_C, uint8_T rtu_Notmovtimeoutval_C, HallDeco_Dir_t
  rtu_DirNone_C, HallDeco_Dir_t *rty_directionCyclic);
extern void HALLDeco_Mdl_HallDeco_Spd_Init(void);
extern void HALLDeco_Mdl_HallDeco_Spd(const uint8_T *rtu_HallCounts, const
  uint16_T *rtu_filteredPulseTicks, uint32_T rtu_InputCaptureTimer_C, uint16_T
  rtu_tSmpHallDeco_SC, uint16_T rtu_U16Zero_C, uint16_T rtu_OverflowValue_C,
  uint16_T rtu_rad4poles_C, uint16_T *rty_HallDeco_rMtrSpd, boolean_T
  *rty_isSpeedValid);
extern void HALLDeco_Mdl_debounce200ms(boolean_T rtu_MtrDir, boolean_T
  *rty_isDebounceFinished);

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_HALLDeco_Mdl_T HALLDeco_Mdl_MdlrefDW;

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef HALLDeco_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_HALLDeco_Mdl_c_T HALLDeco_Mdl_B;

/* Block states (default storage) */
extern DW_HALLDeco_Mdl_f_T HALLDeco_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_HALLDeco_Mdl_T HALLDeco_Mdl_PrevZCX;

#endif                                 /*HALLDeco_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S7>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S7>/FixPt Data Type Propagation' : Unused code path elimination
 * Block '<S27>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/FixPt Data Type Propagation' : Unused code path elimination
 * Block '<S7>/FixPt Gateway In' : Eliminate redundant data type conversion
 * Block '<S27>/FixPt Gateway In' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'HALLDeco_Mdl'
 * '<S1>'   : 'HALLDeco_Mdl/HallDeco'
 * '<S2>'   : 'HALLDeco_Mdl/Model Info'
 * '<S3>'   : 'HALLDeco_Mdl/HallDeco/Dir_Timeout'
 * '<S4>'   : 'HALLDeco_Mdl/HallDeco/DocBlock'
 * '<S5>'   : 'HALLDeco_Mdl/HallDeco/HallDeco_Spd'
 * '<S6>'   : 'HALLDeco_Mdl/HallDeco/HallFailureDetection'
 * '<S7>'   : 'HALLDeco_Mdl/HallDeco/Dir_Timeout/AddOnePulse'
 * '<S8>'   : 'HALLDeco_Mdl/HallDeco/Dir_Timeout/Detect Change'
 * '<S9>'   : 'HALLDeco_Mdl/HallDeco/Dir_Timeout/DocBlock'
 * '<S10>'  : 'HALLDeco_Mdl/HallDeco/HallDeco_Spd/Compare To Constant'
 * '<S11>'  : 'HALLDeco_Mdl/HallDeco/HallDeco_Spd/CompareToConstant'
 * '<S12>'  : 'HALLDeco_Mdl/HallDeco/HallDeco_Spd/CompareToConstant1'
 * '<S13>'  : 'HALLDeco_Mdl/HallDeco/HallDeco_Spd/DetectChange'
 * '<S14>'  : 'HALLDeco_Mdl/HallDeco/HallDeco_Spd/DocBlock'
 * '<S15>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault'
 * '<S16>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DocBlock'
 * '<S17>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/FailureForEachPanel'
 * '<S18>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/HallMtrDirectionFbFault'
 * '<S19>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC'
 * '<S20>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/Compare To Constant1'
 * '<S21>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/HallSupplyHandlingFault'
 * '<S22>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/Compare To Constant'
 * '<S23>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/DebounceMtrMovingCondition'
 * '<S24>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/DebounceMtrMovingCondition/Compare To Constant1'
 * '<S25>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/DebounceMtrMovingCondition/Compare To Constant2'
 * '<S26>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/DebounceMtrMovingCondition/debounce200ms'
 * '<S27>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/DebounceMtrMovingCondition/debounce200ms/AddOneTick'
 * '<S28>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/DebounceMtrMovingCondition/debounce200ms/Detect Change'
 * '<S29>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/CalculateMtrDirDTC/DebounceMtrMovingCondition/debounce200ms/DocBlock'
 * '<S30>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/HallSupplyHandlingFault/Detect Change'
 * '<S31>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/HallSupplyHandlingFault/Triggered Subsystem'
 * '<S32>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/DetectHallSupplyFault/HallSupplyHandlingFault/Triggered Subsystem/Compare To Constant'
 * '<S33>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/FailureForEachPanel/HallFailureDetectChart'
 * '<S34>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/HallMtrDirectionFbFault/Difference'
 * '<S35>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/HallMtrDirectionFbFault/HallFailureDetectChart'
 * '<S36>'  : 'HALLDeco_Mdl/HallDeco/HallFailureDetection/HallMtrDirectionFbFault/IncOrDecCurPosition'
 */
#endif                                 /* RTW_HEADER_HALLDeco_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
