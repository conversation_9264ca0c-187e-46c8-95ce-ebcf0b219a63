/*
 * File: PIDDID_Mdl.h
 *
 * Code generated for Simulink model 'PIDDID_Mdl'.
 *
 * Model version                  : 1.414
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:35:29 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_PIDDID_Mdl_h_
#define RTW_HEADER_PIDDID_Mdl_h_
#ifndef PIDDID_Mdl_COMMON_INCLUDES_
#define PIDDID_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* PIDDID_Mdl_COMMON_INCLUDES_ */

#include "StateMach_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "RoofOper_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "PIDDID_Mdl_types.h"
#include "irs_std_types.h"
#include "PosMon_ExpTypes.h"
#include "PIDDID_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "UsgHist_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "BlockDet_Mdl_Exp.h"
#include "CfgParam_Mdl_Exp.h"
#include "PIDDID_Exp.h"
#include "UsgHist_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'PIDDID_Mdl' */
#ifndef PIDDID_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  boolean_T Equal;                     /* '<S15>/Equal' */
  boolean_T isReqUnlearnOut;           /* '<S2>/ClearRF_Routine' */
  HwAbs_tenStatus stsGoToShtdwnReady;  /* '<S25>/S-Function1' */
  HwAbs_tenStatus SFunction1;          /* '<S15>/S-Function1' */
} B_PIDDID_Mdl_c_T;

#endif                                 /*PIDDID_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'PIDDID_Mdl' */
#ifndef PIDDID_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T temporalCounter_i1;         /* '<S2>/Roof_Control_Routine' */
  uint32_T temporalCounter_i1_a;       /* '<S2>/IO_TEST_ROUTINE' */
  uint32_T temporalCounter_i1_j;       /* '<S2>/AutoCycle' */
  CtrlLog_tenTargetDirection CtrlLog_stDirCommand_prev;/* '<S2>/Run_Motor_Continuous_Routine' */
  CtrlLog_tenTargetDirection CtrlLog_stDirCommand_start;/* '<S2>/Run_Motor_Continuous_Routine' */
  boolean_T UnitDelay_DSTATE;          /* '<S16>/Unit Delay' */
  boolean_T Delay1_DSTATE;             /* '<S2>/Delay1' */
  uint8_T is_c11_PIDDID_Mdl;           /* '<S2>/Sync_To_NVM' */
  uint8_T is_active_c11_PIDDID_Mdl;    /* '<S2>/Sync_To_NVM' */
  uint8_T is_c13_PIDDID_Mdl;          /* '<S2>/Suppress_CyclicRenorm_Routine' */
  uint8_T is_active_c13_PIDDID_Mdl;   /* '<S2>/Suppress_CyclicRenorm_Routine' */
  uint8_T is_c10_PIDDID_Mdl;           /* '<S2>/SoftStop_Control' */
  uint8_T is_active_c10_PIDDID_Mdl;    /* '<S2>/SoftStop_Control' */
  uint8_T is_c9_PIDDID_Mdl;            /* '<S2>/SoftStart_Control_Routine' */
  uint8_T is_active_c9_PIDDID_Mdl;     /* '<S2>/SoftStart_Control_Routine' */
  uint8_T is_c12_PIDDID_Mdl;           /* '<S2>/Run_Motor_Continuous_Routine' */
  uint8_T is_active_c12_PIDDID_Mdl;    /* '<S2>/Run_Motor_Continuous_Routine' */
  uint8_T is_c7_PIDDID_Mdl;            /* '<S2>/Roof_Control_Routine' */
  uint8_T is_active_c7_PIDDID_Mdl;     /* '<S2>/Roof_Control_Routine' */
  uint8_T is_c14_PIDDID_Mdl;           /* '<S2>/OverridePWM_Routine' */
  uint8_T is_active_c14_PIDDID_Mdl;    /* '<S2>/OverridePWM_Routine' */
  uint8_T is_c4_PIDDID_Mdl;            /* '<S2>/Learning_Routine' */
  uint8_T is_active_c4_PIDDID_Mdl;     /* '<S2>/Learning_Routine' */
  uint8_T is_c8_PIDDID_Mdl;            /* '<S2>/IO_TEST_ROUTINE' */
  uint8_T is_RequestRun;               /* '<S2>/IO_TEST_ROUTINE' */
  uint8_T is_active_c8_PIDDID_Mdl;     /* '<S2>/IO_TEST_ROUTINE' */
  uint8_T is_c18_PIDDID_Mdl;           /* '<S2>/HallOutput_Routine' */
  uint8_T is_active_c18_PIDDID_Mdl;    /* '<S2>/HallOutput_Routine' */
  uint8_T is_c6_PIDDID_Mdl;            /* '<S2>/Goto_Target_Pos' */
  uint8_T is_active_c6_PIDDID_Mdl;     /* '<S2>/Goto_Target_Pos' */
  uint8_T is_c17_PIDDID_Mdl;           /* '<S2>/Dummy_RP_Routine' */
  uint8_T is_active_c17_PIDDID_Mdl;    /* '<S2>/Dummy_RP_Routine' */
  uint8_T temporalCounter_i1_n;        /* '<S2>/Dummy_RP_Routine' */
  uint8_T is_c15_PIDDID_Mdl;    /* '<S2>/Control_Open_Close_Position_Routine' */
  uint8_T is_active_c15_PIDDID_Mdl;
                                /* '<S2>/Control_Open_Close_Position_Routine' */
  uint8_T is_c16_PIDDID_Mdl;  /* '<S2>/Control_Intermediate_Position_Routine' */
  uint8_T is_active_c16_PIDDID_Mdl;
                              /* '<S2>/Control_Intermediate_Position_Routine' */
  uint8_T is_c3_PIDDID_Mdl;            /* '<S2>/Clear_History_Logs_Routine' */
  uint8_T is_active_c3_PIDDID_Mdl;     /* '<S2>/Clear_History_Logs_Routine' */
  uint8_T is_c1_PIDDID_Mdl;            /* '<S2>/ClearRF_Routine' */
  uint8_T is_active_c1_PIDDID_Mdl;     /* '<S2>/ClearRF_Routine' */
  uint8_T is_c5_PIDDID_Mdl;            /* '<S2>/AutoCycle' */
  uint8_T is_AutoCycle;                /* '<S2>/AutoCycle' */
  uint8_T is_active_c5_PIDDID_Mdl;     /* '<S2>/AutoCycle' */
  boolean_T PIDDID_isDummyRPRtnEna_prev;/* '<S2>/Dummy_RP_Routine' */
  boolean_T PIDDID_isDummyRPRtnEna_start;/* '<S2>/Dummy_RP_Routine' */
  VehCom_Cmd_t VehCom_stVehCmd_prev;   /* '<S2>/Learning_Routine' */
  VehCom_Cmd_t VehCom_stVehCmd_start;  /* '<S2>/Learning_Routine' */
  boolean_T NVMClear_MODE;             /* '<S2>/NVMClear' */
} DW_PIDDID_Mdl_f_T;

#endif                                 /*PIDDID_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'PIDDID_Mdl' */
#ifndef PIDDID_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState NVMWriteForShutdown_Trig_ZCE;/* '<S16>/NVMWriteForShutdown' */
} ZCE_PIDDID_Mdl_T;

#endif                                 /*PIDDID_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_PIDDID_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*PIDDID_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_PIDDID_Mdl_T rtm;
} MdlrefDW_PIDDID_Mdl_T;

#endif                                 /*PIDDID_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T PIDDID_uAutoCyclePauseTime[2];
                       /* Simulink.Signal object 'PIDDID_uAutoCyclePauseTime' */
extern uint16_T SysFltRctn_stLearnFail_Reason;
                    /* Simulink.Signal object 'SysFltRctn_stLearnFail_Reason' */
extern uint16_T SysFltRctn_stLostRP_Reason;
                       /* Simulink.Signal object 'SysFltRctn_stLostRP_Reason' */
extern boolean_T PIDDID_HallOutputDisabled;
                           /* Simulink.Signal object 'PIDDID_HallOutputDisabled'
                            * Diagnostic Routine Hall output Enabled/Disabled
                              0: Enabled
                              1: Disabled

                            */
extern boolean_T PIDDID_isAutoCycleActive;
                         /* Simulink.Signal object 'PIDDID_isAutoCycleActive' */
extern boolean_T PIDDID_isSoftStartEna;
                            /* Simulink.Signal object 'PIDDID_isSoftStartEna' */
extern boolean_T PIDDID_isSoftStopEna;
                             /* Simulink.Signal object 'PIDDID_isSoftStopEna' */
extern SysFltDiag_LastStopReason_t SysFltRctn_stLastStopReason;
                      /* Simulink.Signal object 'SysFltRctn_stLastStopReason' */
extern void PIDDID_Mdl_Init(void);
extern void PIDDID_Mdl_Disable(void);
extern void PIDDID_Mdl(const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, const
  ThermProt_MtrTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass, const boolean_T *
  rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_isStartupRdy, const
  ThermProt_MosfetTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass, const
  MtrCtrl_MtrDir_t *rtu_SWC_OperLogLyrBus_MtrCtrlBus_IoHwAb_SetMtrDir, const
  uint8_T *rtu_SWC_OperLogLyrBus_MtrCtrlBus_DTC_MTR_Dir, const boolean_T
  rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_FeedbackStatus[2], const boolean_T
  *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_isMtrMoving, const PnlOp_ATS_t
  *rtu_SWC_OperLogLyrBus_PnlOpBus_PnlOp_stATS, const CtrlLog_RelearnMode_t
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stRelearnMode, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const boolean_T
  *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isVBATMeasEnbPinStatus, const
  boolean_T *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallOutPinStatus, const
  boolean_T *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankAPinStatus,
  const boolean_T
  *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isMtrFltrBlankBPinStatus, const
  boolean_T *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens1PinStatus, const
  boolean_T *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isHallSens2PinStatus, const
  boolean_T *rtu_SWC_HwAbsLyrBus_DIOCtrlBus_DIOCtrl_isINTPinStatus, const
  uint16_T *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_u12VBattInst, const int16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TAmbTemp, const int16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_TMOSFETTemp, const uint16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uHallPwrInst, const uint16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1AVolt_mv, const uint16_T
  *rtu_SWC_HwAbsLyrBus_ADCMonBus_ADCMon_uM1BVolt_mv, const boolean_T
  *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isMtrFebkCtrlHS1Status, const boolean_T *
  rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isHallSuppHS2Status, const boolean_T
  *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_isSwtchSuppHS3Status, const uint8_T
  *rtu_SWC_HwAbsLyrBus_ExtDevBus_ExtDev_u8GetPWMDutyCycleVal, const
  HallDeco_Dir_t *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_DMtrDir, const
  uint16_T *rtu_SWC_HwAbsLyrBus_HallDecoBus_HallDeco_rMtrSpd, const uint8_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HALL_Hall1, const uint8_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_DTC_HallMtrOptDir, const boolean_T
  *rtu_SWC_HwAbsLyrBus_HallDecoBus_isHallSupplyFault, const LearnAdap_Mode_t
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stMode, const LearnAdap_Req_t
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_stPosReq, const uint16_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newHardStop, const boolean_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete, const boolean_T *
  rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearningAllowed, const uint16_T *
  rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_hStallPos, const BlockDet_Stall_dir *
  rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallDir, const int16_T
  *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_TEstMtrTemp, const uint16_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const VoltMon_VoltClass_t
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_stVoltClass, const boolean_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_isFlctnDet, const boolean_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn, const int16_T
  *rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_TMosTemp, const int16_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const boolean_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld, const uint16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_nCycle, const int16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MaxAmbTemp, const int16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MinAmbTemp, const uint16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MaxBattVtg, const uint16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MinBattVtg, const uint32_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_MotorOperTime, const uint16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_CycRenormCount, const uint8_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_LearnSuccessCount, const uint8_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_LeanrFailCount, const uint8_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_StallCount, const uint16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalCount, const uint16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_SlideCycCount, const uint8_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ThermProtCount, const uint8_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReprogrammingCount, const uint16_T
  *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalPos, const
  CtrlLog_tenTargetDirection *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalDir,
  const uint16_T *rtu_SWC_DataHndlLyr_UsgHistBus_UsgHist_ReversalThres, const
  VehCom_Cmd_t *rtu_SWC_CommLyrBus_VehComBus_VehCom_stVehCmd, const uint8_T
  *rtu_SWC_CommLyrBus_VehComBus_VehCom_percRelPosn, int16_T
  *rty_PIDDIDBus_MtrMdlBus_MtrMdl_TEstMtrTemp, LearnAdap_Mode_t
  *rty_PIDDIDBus_LearnAdap_LearnAdap_stMode, boolean_T
  *rty_PIDDIDBus_LearnAdap_LearnAdap_isLearnComplete, boolean_T
  *rty_PIDDIDBus_LearnAdap_LearnAdap_isLearningAllowed, uint16_T
  *rty_PIDDIDBus_BlockDet_BlockDet_hStallPos, BlockDet_Stall_dir
  *rty_PIDDIDBus_BlockDet_BlockDet_stStallDir, int16_T
  *rty_PIDDIDBus_AmbTemp_AmbTempMon_TAmbTemp, boolean_T
  *rty_PIDDIDBus_AmbTemp_AmbTempMon_isAmbTempVld, boolean_T
  *rty_PIDDIDBus_PosMon_PosMon_isCurPosVld, uint16_T
  *rty_PIDDIDBus_PosMon_PosMon_hCurPos, VoltMon_VoltClass_t
  *rty_PIDDIDBus_VoltMon_VoltMon_stVoltClass, boolean_T
  *rty_PIDDIDBus_VoltMon_VoltMon_isFlctnDet, uint16_T
  *rty_PIDDIDBus_VoltMon_VoltMon_u12VBatt, int16_T
  *rty_PIDDIDBus_MOSFETTempBus_MOSFETTempMon_TMosTemp, ThermProt_MtrTempClass_t *
  rty_PIDDIDBus_ThermProt_ThermProt_stMtrTempClass, boolean_T
  *rty_PIDDIDBus_ThermProt_ThermProt_isStartupRdy, ThermProt_MosfetTempClass_t
  *rty_PIDDIDBus_ThermProt_ThermProt_stMosfetTempClass, PnlOp_ATS_t
  *rty_PIDDIDBus_PnlOp_PnlOp_stATS, boolean_T
  *rty_PIDDIDBus_PnlOp_PnlOp_isMtrMoving, MtrCtrl_MtrDir_t
  *rty_PIDDIDBus_MtrCtrl_IoHwAb_SetMtrDir, boolean_T
  rty_PIDDIDBus_MtrCtrl_MtrCtrl_FeedbackStatus[2], CtrlLog_tenTargetDirection
  *rty_PIDDIDBus_CtrlLog_CtrlLog_stDirCommand, CtrlLog_RelearnMode_t
  *rty_PIDDIDBus_CtrlLog_CtrlLog_stRelearnMode, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_nCycle, int16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_MaxAmbTemp, int16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_MinAmbTemp, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_MaxBattVtg, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_MinBattVtg, uint32_T
  *rty_PIDDIDBus_UsgHist_UsgHist_MotorOperTime, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_CycRenormCount, uint8_T
  *rty_PIDDIDBus_UsgHist_UsgHist_LearnSuccessCount, uint8_T
  *rty_PIDDIDBus_UsgHist_UsgHist_LeanrFailCount, uint8_T
  *rty_PIDDIDBus_UsgHist_UsgHist_StallCount, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalCount, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_SlideCycCount, uint8_T
  *rty_PIDDIDBus_UsgHist_UsgHist_ThermProtCount, uint8_T
  *rty_PIDDIDBus_UsgHist_UsgHist_ReprogrammingCount, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalPos, CtrlLog_tenTargetDirection
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalDir, uint16_T
  *rty_PIDDIDBus_UsgHist_UsgHist_ReversalThres, HallDeco_Dir_t
  *rty_PIDDIDBus_HallDeco_HallDeco_DMtrDir, uint8_T
  *rty_PIDDIDBus_HallDeco_DTC_HALL_Hall1, uint16_T
  *rty_PIDDIDBus_HallDeco_HallDeco_rMtrSpd, boolean_T
  *rty_PIDDIDBus_HallDeco_isHallSupplyFault, uint16_T
  *rty_PIDDIDBus_ADCMon_ADCMon_u12VBattInst, int16_T
  *rty_PIDDIDBus_ADCMon_ADCMon_TAmbTemp, int16_T
  *rty_PIDDIDBus_ADCMon_ADCMon_TMOSFETTemp, uint16_T
  *rty_PIDDIDBus_ADCMon_ADCMon_uHallPwrInst, uint16_T
  *rty_PIDDIDBus_ADCMon_ADCMon_uM1AVolt_mv, uint16_T
  *rty_PIDDIDBus_ADCMon_ADCMon_uM1BVolt_mv, StateMach_Mode_t
  *rty_PIDDIDBus_StateMach_StateMach_stSysMode, boolean_T
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isVBATMeasEnbPinStatus, boolean_T
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallOutPinStatus, boolean_T
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankAPinStatus, boolean_T
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isMtrFltrBlankBPinStatus, boolean_T
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens1PinStatus, boolean_T
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isHallSens2PinStatus, boolean_T
  *rty_PIDDIDBus_DIOCtrl_DIOCtrl_isINTPinStatus, boolean_T
  *rty_PIDDIDBus_ExtDev_ExtDev_isMtrFebkCtrlHS1Status, boolean_T
  *rty_PIDDIDBus_ExtDev_ExtDev_isHallSuppHS2Status, boolean_T
  *rty_PIDDIDBus_ExtDev_ExtDev_isSwtchSuppHS3Status, uint8_T
  *rty_PIDDIDBus_ExtDev_ExtDev_u8GetPWMDutyCycleVal);

/* Model reference registration function */
extern void PIDDID_Mdl_initialize(const char_T **rt_errorStatus);

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern boolean_T PIDDID_isCyclicRenormSuppress;
extern boolean_T PIDDID_isDummyRPRtnEna;
extern boolean_T PIDDID_isIntermediatePositionRtnEna;
extern boolean_T PIDDID_isOpenClosePositionRtnEna;
extern boolean_T PIDDID_isOverridePWMEnable;

#ifndef PIDDID_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_PIDDID_Mdl_T PIDDID_Mdl_MdlrefDW;

#endif                                 /*PIDDID_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef PIDDID_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_PIDDID_Mdl_c_T PIDDID_Mdl_B;

/* Block states (default storage) */
extern DW_PIDDID_Mdl_f_T PIDDID_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_PIDDID_Mdl_T PIDDID_Mdl_PrevZCX;

#endif                                 /*PIDDID_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PIDDID_Mdl'
 * '<S1>'   : 'PIDDID_Mdl/Model Info'
 * '<S2>'   : 'PIDDID_Mdl/PIDandDID'
 * '<S3>'   : 'PIDDID_Mdl/Prepare_PIDDID_Bus'
 * '<S4>'   : 'PIDDID_Mdl/PIDandDID/AutoCycle'
 * '<S5>'   : 'PIDDID_Mdl/PIDandDID/ClearRF_Routine'
 * '<S6>'   : 'PIDDID_Mdl/PIDandDID/Clear_History_Logs_Routine'
 * '<S7>'   : 'PIDDID_Mdl/PIDandDID/Control_Intermediate_Position_Routine'
 * '<S8>'   : 'PIDDID_Mdl/PIDandDID/Control_Open_Close_Position_Routine'
 * '<S9>'   : 'PIDDID_Mdl/PIDandDID/DocBlock'
 * '<S10>'  : 'PIDDID_Mdl/PIDandDID/Dummy_RP_Routine'
 * '<S11>'  : 'PIDDID_Mdl/PIDandDID/Goto_Target_Pos'
 * '<S12>'  : 'PIDDID_Mdl/PIDandDID/HallOutput_Routine'
 * '<S13>'  : 'PIDDID_Mdl/PIDandDID/IO_TEST_ROUTINE'
 * '<S14>'  : 'PIDDID_Mdl/PIDandDID/Learning_Routine'
 * '<S15>'  : 'PIDDID_Mdl/PIDandDID/NVMClear'
 * '<S16>'  : 'PIDDID_Mdl/PIDandDID/NVMWrite'
 * '<S17>'  : 'PIDDID_Mdl/PIDandDID/OverridePWM_Routine'
 * '<S18>'  : 'PIDDID_Mdl/PIDandDID/Roof_Control_Routine'
 * '<S19>'  : 'PIDDID_Mdl/PIDandDID/Run_Motor_Continuous_Routine'
 * '<S20>'  : 'PIDDID_Mdl/PIDandDID/SoftStart_Control_Routine'
 * '<S21>'  : 'PIDDID_Mdl/PIDandDID/SoftStop_Control'
 * '<S22>'  : 'PIDDID_Mdl/PIDandDID/Suppress_CyclicRenorm_Routine'
 * '<S23>'  : 'PIDDID_Mdl/PIDandDID/Sync_To_NVM'
 * '<S24>'  : 'PIDDID_Mdl/PIDandDID/NVMWrite/DocBlock'
 * '<S25>'  : 'PIDDID_Mdl/PIDandDID/NVMWrite/NVMWriteForShutdown'
 * '<S26>'  : 'PIDDID_Mdl/PIDandDID/NVMWrite/NVMWriteForShutdown/DocBlock'
 * '<S27>'  : 'PIDDID_Mdl/Prepare_PIDDID_Bus/DocBlock'
 */
#endif                                 /* RTW_HEADER_PIDDID_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
