/*
 * File: VehCom_Mdl.h
 *
 * Code generated for Simulink model 'VehCom_Mdl'.
 *
 * Model version                  : 1.865
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:42:29 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_VehCom_Mdl_h_
#define RTW_HEADER_VehCom_Mdl_h_
#ifndef VehCom_Mdl_COMMON_INCLUDES_
#define VehCom_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* VehCom_Mdl_COMMON_INCLUDES_ */

#include "CtrlLogic_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "VehCom_Mdl_types.h"
#include "PosMon_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "RoofSys_CommDefines.h"
#include "RoofOper_ExpTypes.h"
#include "BlockDet_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "VoltMon_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"

/* Includes for objects with custom storage classes */
#include "RoofSys.h"
#include "CfgParam_Mdl_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block signals for model 'VehCom_Mdl' */
#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T DataTypeConversion;          /* '<S44>/Data Type Conversion' */
  boolean_T VehCom_bSleepReady;        /* '<S45>/Logical Operator' */
  VehCom_Cmd_t VehCom_stVehCmd;        /* '<S5>/VehCmd determination' */
} B_VehCom_Mdl_c_T;

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'VehCom_Mdl' */
#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint16_T UnitDelay_DSTATE;           /* '<S49>/UnitDelay' */
  uint8_T ReqManMov_prev;              /* '<S5>/VehCmd determination' */
  uint8_T ReqManMov_start;             /* '<S5>/VehCmd determination' */
  uint8_T Mov2Pos_prev;                /* '<S5>/VehCmd determination' */
  uint8_T Mov2Pos_start;               /* '<S5>/VehCmd determination' */
  boolean_T doneDoubleBufferReInit;    /* '<S5>/VehCmd determination' */
} DW_VehCom_Mdl_f_T;

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

/* Invariant block signals for model 'VehCom_Mdl' */
#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  const int16_T Add6[15];              /* '<S14>/Add6' */
  const uint8_T VehCom_vVehSpd;
  const boolean_T VehCom_isVehSpdVld;
} ConstB_VehCom_Mdl_h_T;

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_VehCom_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_VehCom_Mdl_T rtm;
} MdlrefDW_VehCom_Mdl_T;

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint8_T SysFltRctn_stDTCStatus[3];
                           /* Simulink.Signal object 'SysFltRctn_stDTCStatus' */
extern void VehCom_Mdl_Init(void);
extern void VehCom_Mdl(const StateMach_Mode_t
  *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode, const
  ThermProt_MtrTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass, const
  CtrlLog_MovType_t *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovType, const
  CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const uint16_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_newSoftStop, const boolean_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete, const boolean_T *
  rtu_SWC_ObjDetLyrBus_APDetBus_APDet_isAtsEnabled, const uint16_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const boolean_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_is12VBattVld, const boolean_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_isCurPosVld, const int16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPosSigned, VehCom_Cmd_t
  *rty_VehComBus_VehCom_stVehCmd, uint8_T *rty_VehComBus_VehCom_hToPos,
  boolean_T *rty_VehComBus_VehCom_isVehSpdVld, uint8_T
  *rty_VehComBus_VehCom_vVehSpd, boolean_T *rty_VehComBus_VehCom_bSleepReady,
  uint8_T *rty_VehComBus_VehCom_percRelPosn);

/* Model reference registration function */
extern void VehCom_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

extern void VehCom_Mdl_PublishedFrames(const CtrlLog_tenTargetDirection
  *rtu_stDirCommand, const CtrlLog_MovType_t *rtu_stMovementType, const
  StateMach_Mode_t *rtu_stSysMode, const boolean_T *rtu_isCurPosVld, const
  ThermProt_MtrTempClass_t *rtu_stMtrTempClass, const uint16_T *rtu_u12VoltBatt,
  const boolean_T *rtu_is12VBattVld, uint8_T rtu_uMinVoltClassB, uint8_T
  rtu_uMaxVoltClassB, uint16_T rtu_hSoftStopClose, uint8_T rtu_hMovPosErr, const
  uint16_T *rtu_LearnAdap_newSoftStopOpen, const int16_T *rtu_hCurPosSigned,
  const boolean_T *rtu_LearnAdap_isLearnComplete, uint8_T rtu_DTCStatus_Byte1,
  uint8_T rtu_DTCStatus_Byte2, uint8_T rtu_DTCStatus_Byte3, uint16_T
  rtu_SBC_FaultStatus, const boolean_T *rtu_APDet_isAtsDetected, uint8_T
  *rty_VehCom_stECU, uint8_T *rty_VehCom_stRoofMotion, uint8_T
  *rty_VehCom_stRoof, uint8_T *rty_VehCom_verRoofIfc, boolean_T
  *rty_VehCom_bRoofErrHiTemp, boolean_T *rty_VehCom_bRoofErrNoSync, boolean_T
  *rty_VehCom_bRoofErrLV, boolean_T *rty_VehCom_bRoofErrHV, uint8_T
  *rty_VehCom_percRelPosn, boolean_T *rty_VehCom_bRoofErrNoATS, uint8_T
  *rty_VehCom_stRoofWakeUp, boolean_T *rty_VehCom_stECUDefect);
extern void VehCom_Mdl_debounceLinActive(boolean_T rtu_MotorfaultActive,
  boolean_T *rty_isDebounceFinished);
extern void VehCom_Mdl_SubscribedFrames_Init(VehCom_Cmd_t *rty_VehCom_stVehCmd);
extern void VehCom_Mdl_SubscribedFrames(uint8_T rtu_Mov2Posn, uint8_T
  rtu_ReqManMovement, uint8_T rtu_StopMovment, uint8_T
  rtu_LINMiddleware_AfterRun, boolean_T rtu_LINMiddleware_bGetLINBusInActivity,
  boolean_T rtu_XCPLIN_bGetStatus, boolean_T
  rtu_LINMiddleware_bGetLINBusSleepState, VehCom_Cmd_t *rty_VehCom_stVehCmd);

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_VehCom_Mdl_T VehCom_Mdl_MdlrefDW;

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef VehCom_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_VehCom_Mdl_c_T VehCom_Mdl_B;

/* Block states (default storage) */
extern DW_VehCom_Mdl_f_T VehCom_Mdl_DW;

#endif                                 /*VehCom_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S50>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S50>/FixPt Data Type Propagation' : Unused code path elimination
 * Block '<S50>/FixPt Gateway In' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'VehCom_Mdl'
 * '<S1>'   : 'VehCom_Mdl/Model Info'
 * '<S2>'   : 'VehCom_Mdl/VehCom_Mdl'
 * '<S3>'   : 'VehCom_Mdl/VehCom_Mdl/DocBlock'
 * '<S4>'   : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames'
 * '<S5>'   : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames'
 * '<S6>'   : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/DocBlock'
 * '<S7>'   : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof ECU Motion state'
 * '<S8>'   : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof ECU state value determination'
 * '<S9>'   : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Error - Learned status'
 * '<S10>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Error Determination (ATS)'
 * '<S11>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Error Determination (LV and HV)'
 * '<S12>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof High temperature determination'
 * '<S13>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Interface version determination'
 * '<S14>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof State and Relative Position determination'
 * '<S15>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Wake up state determination'
 * '<S16>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect'
 * '<S17>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof ECU Motion state/Compare To Constant'
 * '<S18>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof ECU Motion state/DocBlock'
 * '<S19>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof ECU state value determination/Compare To Constant'
 * '<S20>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof ECU state value determination/DocBlock'
 * '<S21>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Error - Learned status/Compare To Constant'
 * '<S22>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Error - Learned status/DocBlock'
 * '<S23>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Error Determination (ATS)/DocBlock'
 * '<S24>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Error Determination (LV and HV)/DocBlock'
 * '<S25>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof High temperature determination/DocBlock'
 * '<S26>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Interface version determination/Compare To Constant'
 * '<S27>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Interface version determination/DocBlock'
 * '<S28>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof State and Relative Position determination/DocBlock'
 * '<S29>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Wake up state determination/Compare To Constant'
 * '<S30>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/Roof Wake up state determination/DocBlock'
 * '<S31>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant'
 * '<S32>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant1'
 * '<S33>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant10'
 * '<S34>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant2'
 * '<S35>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant3'
 * '<S36>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant4'
 * '<S37>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant5'
 * '<S38>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant6'
 * '<S39>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant7'
 * '<S40>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant8'
 * '<S41>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/Compare To Constant9'
 * '<S42>'  : 'VehCom_Mdl/VehCom_Mdl/PublishedFrames/RoofErr_ECUDefect/DocBlock'
 * '<S43>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/DocBlock'
 * '<S44>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/Move to position value determination'
 * '<S45>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/SCU sleep ready determination'
 * '<S46>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/VehCmd determination'
 * '<S47>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/Move to position value determination/DocBlock'
 * '<S48>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/SCU sleep ready determination/DocBlock'
 * '<S49>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/SCU sleep ready determination/debounceLinActive'
 * '<S50>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/SCU sleep ready determination/debounceLinActive/AddOneTick'
 * '<S51>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/SCU sleep ready determination/debounceLinActive/Compare To Zero'
 * '<S52>'  : 'VehCom_Mdl/VehCom_Mdl/SubscribedFrames/SCU sleep ready determination/debounceLinActive/DocBlock'
 */
#endif                                 /* RTW_HEADER_VehCom_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
