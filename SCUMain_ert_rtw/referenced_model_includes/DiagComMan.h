/*
 * File: DiagComMan.h
 *
 * Code generated for Simulink model 'DiagComMan'.
 *
 * Model version                  : 1.25
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:31:29 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_DiagComMan_h_
#define RTW_HEADER_DiagComMan_h_
#ifndef DiagComMan_COMMON_INCLUDES_
#define DiagComMan_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* DiagComMan_COMMON_INCLUDES_ */

#include "DiagComMan_types.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"
#ifndef DiagComMan_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_DiagComMan_T {
  const char_T **errorStatus;
};

#endif                                 /*DiagComMan_MDLREF_HIDE_CHILD_*/

#ifndef DiagComMan_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_DiagComMan_T rtm;
} MdlrefDW_DiagComMan_T;

#endif                                 /*DiagComMan_MDLREF_HIDE_CHILD_*/

extern void DiagComMan_Init(void);
extern void DiagComMan(boolean_T *rty_DiagComManBus_Dummy);

/* Model reference registration function */
extern void DiagComMan_initialize(const char_T **rt_errorStatus);

#ifndef DiagComMan_MDLREF_HIDE_CHILD_

extern MdlrefDW_DiagComMan_T DiagComMan_MdlrefDW;

#endif                                 /*DiagComMan_MDLREF_HIDE_CHILD_*/

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'DiagComMan'
 * '<S1>'   : 'DiagComMan/Initialize Function'
 */
#endif                                 /* RTW_HEADER_DiagComMan_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
