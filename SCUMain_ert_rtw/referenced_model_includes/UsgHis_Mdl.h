/*
 * File: UsgHis_Mdl.h
 *
 * Code generated for Simulink model 'UsgHis_Mdl'.
 *
 * Model version                  : 1.442
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:42:03 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_UsgHis_Mdl_h_
#define RTW_HEADER_UsgHis_Mdl_h_
#ifndef UsgHis_Mdl_COMMON_INCLUDES_
#define UsgHis_Mdl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* UsgHis_Mdl_COMMON_INCLUDES_ */

#include "RoofSys_CommDefines.h"
#include "BlockDet_ExpTypes.h"
#include "RoofSys_ExpTypes.h"
#include "StateMach_ExpTypes.h"
#include "ThermProt_ExpTypes.h"
#include "CtrlLogic_ExpTypes.h"
#include "UsgHis_Mdl_types.h"
#include "VoltMon_ExpTypes.h"
#include "LearnAdp_ExpTypes.h"
#include "PosMon_ExpTypes.h"
#include "UsgHist_ExpTypes.h"
#include "RoofOper_ExpTypes.h"
#include "CmdLogic_ExpTypes.h"
#include "APDet_ExpTypes.h"
#include "HallDeco_ExpTypes.h"
#include "CfgParam_Mdl_ExpTypes.h"
#include "zero_crossing_types.h"

/* Includes for objects with custom storage classes */
#include "CfgParam_Mdl_Exp.h"
#include "IoSigIf_Exp.h"
#include "MtrMdl_Exp.h"
#include "UsgHist_Exp.h"

/* user code (top of header file) */
#include "Stdint.h"
#include "S32K118.h"
#include "pins_driver.h"
#include "TaskSch_Man.h"
#include "pins_port_hw_access.h"
#include "irs_lib.h"
#include "irs_std_types.h"

/* Block states (default storage) for system '<S185>/Lib_CycleCounter' */
#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint8_T is_c2_UsgHis_Mdl;            /* '<S185>/Lib_CycleCounter' */
  uint8_T is_active_c2_UsgHis_Mdl;     /* '<S185>/Lib_CycleCounter' */
} DW_Lib_CycleCounter_UsgHis_Mdl_T;

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

/* Block signals for model 'UsgHis_Mdl' */
#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T EEP_USGHIST_UNLEARN_REASON; /* '<S198>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MINBATTVTG;     /* '<S181>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MINBATTVTG_n;   /* '<S170>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MAXBATTVTG;     /* '<S162>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MINAMBTEMP;     /* '<S153>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MAXAMBTEMP;     /* '<S145>/Data Type Conversion' */
  uint32_T UsgHist_MotorOperTime;      /* '<S112>/Switch3' */
  uint32_T EEP_USGHIST_MINBATTVTG_h;   /* '<S133>/Data Type Conversion' */
  uint32_T UsgHist_MotorOperTime_o;    /* '<S117>/Switch3' */
  uint32_T UsgHist_MotorOperTime_c;    /* '<S121>/Switch' */
  uint32_T EEP_USGHIST_MINBATTVTG_l;   /* '<S122>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MINBATTVTG_nl;  /* '<S108>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MINBATTVTG_o;   /* '<S93>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MINBATTVTG_p;   /* '<S85>/Data Type Conversion' */
  uint32_T EEP_USGHIST_MINBATTVTG_a;   /* '<S73>/Data Type Conversion' */
  uint32_T DataTypeConversion;         /* '<S64>/Data Type Conversion' */
  uint32_T EEP_USGHIST_REVERSALEVENT;  /* '<S54>/Data Type Conversion' */
  uint32_T EEP_USGHIST_REVERSALEVENT_m;/* '<S44>/Data Type Conversion' */
  uint32_T EEP_USGHIST_REVERSALEVENT_a;/* '<S34>/Data Type Conversion' */
  uint32_T EEP_USGHIST_CYCLECNT;       /* '<S14>/Data Type Conversion' */
  CtrlLog_tenTargetDirection stDirCommand;/* '<S32>/stDirCommand' */
  uint16_T nCycle;                     /* '<S185>/Lib_CycleCounter' */
  uint16_T UsgHist_MinBattVtg;         /* '<S166>/Data Type Conversion' */
  uint16_T UsgHist_MaxBattVtg;         /* '<S160>/Data Type Conversion' */
  uint16_T UsgHist_ReversalCount;      /* '<S114>/Switch3' */
  uint16_T UsgHist_ReversalCount_d;    /* '<S130>/Switch' */
  uint16_T UsgHist_CycRenormCount;     /* '<S60>/Data Type Conversion' */
  uint16_T UsgHist_SlideCycCount;      /* '<S59>/Switch4' */
  uint16_T Add;                        /* '<S71>/Add' */
  uint16_T UsgHist_RevthrNvmRead;      /* '<S52>/UsgHist_RevthrNvmRead' */
  uint16_T UsgHist_RevNvmRead;         /* '<S42>/UsgHist_RevNvmRead' */
  int16_T UsgHist_MinAmbTemp;          /* '<S151>/Data Type Conversion' */
  int16_T UsgHist_MaxAmbTemp;          /* '<S143>/Data Type Conversion' */
  uint8_T isShdnRdy;          /* '<S208>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T UsgHist_StallCount;          /* '<S24>/Switch3' */
  uint8_T UsgHist_StallCount_l;        /* '<S179>/Add' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new;
                              /* '<S183>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_l;
                              /* '<S173>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_i;
                              /* '<S164>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_p;
                              /* '<S155>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_e;
                              /* '<S147>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T Cast;                        /* '<S113>/Cast' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_b;
                              /* '<S135>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_lt;
                              /* '<S124>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T UsgHist_LeanrFailCount;      /* '<S77>/Switch3' */
  uint8_T UsgHist_LearnSuccessCount;   /* '<S78>/Switch3' */
  uint8_T UsgHist_ThermProtCount;      /* '<S79>/Switch3' */
  uint8_T UsgHist_ThermProtCount_g;    /* '<S106>/Add' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_a;
                              /* '<S110>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T Add_c;                       /* '<S91>/Add' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_c;
                               /* '<S95>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T Switch;                      /* '<S82>/Switch' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_n;
                               /* '<S87>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_h;
                               /* '<S75>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_f;
                               /* '<S66>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_lh;
                               /* '<S56>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_hp;
                               /* '<S46>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T S_Func_NVMMan_enRequestNVMWrite_new_o;
                               /* '<S36>/S_Func_NVMMan_enRequestNVMWrite_new' */
  uint8_T isShdnRdy_p;         /* '<S16>/S_Func_NVMMan_enRequestNVMWrite_new' */
} B_UsgHis_Mdl_c_T;

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

/* Block states (default storage) for model 'UsgHis_Mdl' */
#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  uint32_T UnitDelay_DSTATE;           /* '<S117>/Unit Delay' */
  uint32_T DelayInput1_DSTATE;         /* '<S119>/Delay Input1' */
  uint16_T DelayInput1_DSTATE_e;       /* '<S201>/Delay Input1' */
  uint16_T DelayInput1_DSTATE_g;       /* '<S62>/Delay Input1' */
  uint16_T DelayInput1_DSTATE_j;       /* '<S69>/Delay Input1' */
  uint16_T DelayInput1_DSTATE_gg;      /* '<S10>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_eo;       /* '<S177>/Delay Input1' */
  uint8_T DelayInput1_DSTATE_c;        /* '<S128>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_k;      /* '<S191>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_m;      /* '<S30>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_d;      /* '<S40>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_h;      /* '<S50>/Delay Input1' */
  boolean_T DelayInput1_DSTATE_gd;     /* '<S104>/Delay Input1' */
  BlockDet_Stall_t UnitDelay_DSTATE_o; /* '<S24>/Unit Delay' */
  DW_Lib_CycleCounter_UsgHis_Mdl_T sf_Lib_CycleCounter;/* '<S185>/Lib_CycleCounter' */
} DW_UsgHis_Mdl_f_T;

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

/* Zero-crossing (trigger) state for model 'UsgHis_Mdl' */
#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  ZCSigState StallCounter_Trig_ZCE;    /* '<S24>/StallCounter' */
  ZCSigState ReversalCounter_Trig_ZCE; /* '<S114>/ReversalCounter' */
  ZCSigState ThermalProtCounter_Trig_ZCE;/* '<S79>/ThermalProtCounter' */
  ZCSigState LeanrSuccessCounter_Trig_ZCE;/* '<S78>/LeanrSuccessCounter' */
  ZCSigState LeanrFailCounter_Trig_ZCE;/* '<S77>/LeanrFailCounter' */
  ZCSigState WriteCycRenormCounterVld_Trig_ZCE;/* '<S60>/WriteCycRenormCounterVld' */
  ZCSigState ReversalThreshold_Trig_ZCE;/* '<S27>/ReversalThreshold' */
  ZCSigState ReversalPosition_Trig_ZCE;/* '<S26>/ReversalPosition' */
  ZCSigState ReversalDirection_Trig_ZCE;/* '<S25>/ReversalDirection' */
  ZCSigState NVMWrite_Trig_ZCE;        /* '<S14>/NVMWrite' */
} ZCE_UsgHis_Mdl_T;

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

/* Real-time Model Data Structure */
struct tag_RTM_UsgHis_Mdl_T {
  const char_T **errorStatus;
};

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

typedef struct {
  RT_MODEL_UsgHis_Mdl_T rtm;
} MdlrefDW_UsgHis_Mdl_T;

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

/*
 * Exported States
 *
 * Note: Exported states are block states with an exported global
 * storage class designation.  Code generation will declare the memory for these
 * states and exports their symbols.
 *
 */
extern uint16_T LearnAdap_renormCycleCounter;
                     /* Simulink.Signal object 'LearnAdap_renormCycleCounter' */
extern boolean_T PIDDID_isFactoryMode;
                                /* Simulink.Signal object 'PIDDID_isFactoryMode'
                                 * Diagnostic Factory Mode
                                   1 - Enabled
                                   0 -Disabled

                                 */
extern void UsgHis_Mdl_Init(void);
extern void UsgHis_Mdl(const uint16_T
  *rtu_SWC_SigMonLyrBus_VoltMonBus_VoltMon_u12VBatt, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hCurPos, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopCls, const uint16_T
  *rtu_SWC_SigMonLyrBus_PosMonBus_PosMon_hPosTbl_hSoftStopOpn, const boolean_T
  *rtu_SWC_SigMonLyrBus_MOSFETTempBus_MOSFETTempMon_isMosTempVld, const int16_T *
  rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_TAmbTemp, const boolean_T
  *rtu_SWC_SigMonLyrBus_AmbTempMonBus_AmbTempMon_isAmbTempVld, const
  StateMach_Mode_t *rtu_SWC_StateMachLyrBus_StateMachBus_StateMach_stSysMode,
  const uint16_T *rtu_SWC_DiagLyrBus_SysFaultReacBus_SysFltRctn_stsUnlearnReason,
  const ThermProt_MtrTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMtrTempClass, const
  ThermProt_MosfetTempClass_t
  *rtu_SWC_OperLogLyrBus_ThermProtBus_ThermProt_stMosfetTempClass, const
  MtrCtrl_MtrPwrd_t *rtu_SWC_OperLogLyrBus_MtrCtrlBus_MtrCtrl_MtrPwrdState,
  const CtrlLog_MovType_t *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stMovType,
  const CtrlLog_tenTargetDirection
  *rtu_SWC_OperLogLyrBus_CtrlLogBus_CtrlLog_stDirCommand, const boolean_T
  *rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isLearnComplete, const boolean_T *
  rtu_SWC_ObjDetLyrBus_LearnAdapBus_LearnAdap_isInterrupted, const
  BlockDet_Stall_t *rtu_SWC_ObjDetLyrBus_BlockDetBus_BlockDet_stStallType, const
  uint16_T *rtu_SWC_ObjDetLyrBus_ThresForceBus_ThresForce_FatsThreshold, const
  boolean_T *rtu_SWC_ObjDetLyrBus_MtrMdlBus_MtrMdl_isStartupRdy, uint16_T
  *rty_UsgHistBus_UsgHist_nCycle, boolean_T *rty_UsgHistBus_UsgHist_isShdnRdy,
  uint16_T *rty_UsgHistBus_UsgHist_unlearnReason, int16_T
  *rty_UsgHistBus_UsgHist_TEstMtrTemp, int16_T
  *rty_UsgHistBus_UsgHist_TEstCaseTemp, boolean_T
  *rty_UsgHistBus_UsgHist_TEstMtrTempVld, boolean_T
  *rty_UsgHistBus_UsgHistl_TEstCaseTempVld, boolean_T
  *rty_UsgHistBus_UsgHist_AmbTempVld, boolean_T
  *rty_UsgHistBus_UsgHist_MOSFETTempVld, int16_T
  *rty_UsgHistBus_UsgHist_MaxAmbTemp, int16_T *rty_UsgHistBus_UsgHist_MinAmbTemp,
  uint16_T *rty_UsgHistBus_UsgHist_MaxBattVtg, uint16_T
  *rty_UsgHistBus_UsgHist_MinBattVtg, uint32_T
  *rty_UsgHistBus_UsgHist_MotorOperTime, uint16_T
  *rty_UsgHistBus_UsgHist_CycRenormCount, uint8_T
  *rty_UsgHistBus_UsgHist_LearnSuccessCount, uint8_T
  *rty_UsgHistBus_UsgHist_LeanrFailCount, uint8_T
  *rty_UsgHistBus_UsgHist_StallCount, uint16_T
  *rty_UsgHistBus_UsgHist_ReversalCount, uint16_T
  *rty_UsgHistBus_UsgHist_SlideCycCount, uint8_T
  *rty_UsgHistBus_UsgHist_ThermProtCount, uint8_T
  *rty_UsgHistBus_UsgHist_ReprogrammingCount, uint16_T
  *rty_UsgHistBus_UsgHist_ReversalPos, CtrlLog_tenTargetDirection
  *rty_UsgHistBus_UsgHist_ReversalDir, uint16_T
  *rty_UsgHistBus_UsgHist_ReversalThres, boolean_T
  *rty_UsgHistBus_UsgHist_isStartupRdy);

/* Model reference registration function */
extern void UsgHis_Mdl_initialize(const char_T **rt_errorStatus);

#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

extern void UsgHis_Mdl_Lib_CycleCounter_Init(uint16_T *rty_nCycle);
extern void UsgHis_Mdl_Lib_CycleCounter(const uint16_T *rtu_CurPos, uint8_T
  rtu_offSet, const uint16_T *rtu_Open, const uint16_T *rtu_Close, uint16_T
  rtu_nCycleIn, uint16_T *rty_nCycle, DW_Lib_CycleCounter_UsgHis_Mdl_T *localDW);
extern void UsgHis_Mdl_CheckManufacturingConditions(uint16_T rtu_nCycleIn,
  uint16_T rtu_nCycleCntEEP, uint16_T *rty_UsgHist_nCycle);
extern void UsgHis_Mdl_ProduceDIDSignals(boolean_T rtu_StateMach_isFactMode,
  const int16_T *rtu_AmbTempMon_TAmbTemp, const uint16_T *rtu_VoltMon_u12VBatt,
  const MtrCtrl_MtrPwrd_t *rtu_MtrCtrl_MtrPwrdState, const boolean_T
  *rtu_LearnAdap_isLearnComplete, const BlockDet_Stall_t
  *rtu_BlockDet_stStallType, uint8_T rtu_UsgHist_StallCountNvmRead, uint16_T
  rtu_UsgHist_SlideCycCountNvmRead, uint8_T rtu_UsgHist_LearnSuccessCountNvmRead,
  uint8_T rtu_UsgHist_LearnFailCountNvmRead, uint8_T
  rtu_UsgHist_ThermProtCountNvmRead, int16_T rtu_UsgHist_MinAmbTempNvmRead,
  int16_T rtu_UsgHist_MaxAmbTempNvmRead, uint16_T rtu_UsgHist_MinBattVtgNvmRead,
  uint16_T rtu_UsgHist_MaxBattVtgNvmRead, const boolean_T
  *rtu_LearnAdap_isInterrupted, uint16_T rtu_nCycOut, uint32_T
  rtu_UsgHist_MotorOperTime_In, uint16_T rtu_UsgHist_ReversalCountNvmRead,
  uint8_T rtu_UsgHist_ReprogrammingCount_In, const CtrlLog_MovType_t
  *rtu_stMovementType, const StateMach_Mode_t *rtu_StateMach_stSysMode, const
  ThermProt_MtrTempClass_t *rtu_stMtrTempClass, const
  ThermProt_MosfetTempClass_t *rtu_stMosfetTempClass, const uint16_T
  *rtu_hCurPos, const CtrlLog_tenTargetDirection *rtu_stDirCommand, const
  uint16_T *rtu_ThresForce_ForceThreshold, uint16_T rtu_UsgHist_ReversalThres_In,
  CtrlLog_tenTargetDirection rtu_UsgHist_ReversalDir_In, uint16_T
  rtu_UsgHist_ReversalPos_In, uint16_T rtu_LearnAdap_renormCycleCounter, int16_T
  *rty_UsgHist_MaxAmbTemp, int16_T *rty_UsgHist_MinAmbTemp, uint16_T
  *rty_UsgHist_MaxBattVtg, uint16_T *rty_UsgHist_MinBattVtg, uint32_T
  *rty_UsgHist_MotorOperTime, uint16_T *rty_UsgHist_CycRenormCount, uint8_T
  *rty_UsgHist_LearnSuccessCount, uint8_T *rty_UsgHist_LeanrFailCount, uint8_T
  *rty_UsgHist_StallCount, uint16_T *rty_UsgHist_ReversalCount, uint16_T
  *rty_UsgHist_SlideCycCount, uint8_T *rty_UsgHist_ThermProtCount, uint8_T
  *rty_UsgHist_ReprogrammingCount, uint16_T *rty_UsgHist_ReversalPos,
  CtrlLog_tenTargetDirection *rty_UsgHist_ReversalDir, uint16_T
  *rty_UsgHist_ReversalThres);
extern void UsgHis_Mdl_TravelCycleCounter_Init(uint16_T *rty_nCycleOut);
extern void UsgHis_Mdl_TravelCycleCounter(const boolean_T *rtu_isLearnComplete,
  uint8_T rtu_hMovPosErr, const uint16_T *rtu_hCurPos, boolean_T
  rtu_StateMach_isFactMode, uint16_T rtu_nCycleIn, const uint16_T
  *rtu_hSoftStopOpn, const uint16_T *rtu_hSoftStopCls, uint16_T *rty_nCycleOut);
extern void UsgHis_Mdl_UpdateNVM(const StateMach_Mode_t *rtu_StateMach_stSysMode,
  const uint16_T *rtu_SysFltRctn_stsUnlearnReason, uint16_T
  rtu_UsgHist_unlearnReasonIn, int16_T rtu_MtrMdl_TEstMtrTemp, int16_T
  rtu_MtrMdl_TEstCaseTemp, boolean_T rtu_MtrMdl_TEstMtrTempVld, const boolean_T *
  rtu_AmbTempMon_isAmbTempVld, const boolean_T *rtu_MOSFETTempMon_isAmbTempVld,
  const boolean_T *rtu_MtrMdl_isStartupRdy, boolean_T *rty_UsgHist_isShdnRdy,
  uint16_T *rty_UsgHist_unlearnReason, int16_T *rty_UsgHist_TEstMtrTemp, int16_T
  *rty_UsgHist_TEstCaseTemp, boolean_T *rty_UsgHist_TEstMtrTempVld, boolean_T
  *rty_UsgHistl_TEstCaseTempVld, boolean_T *rty_UsgHist_AmbTempVld, boolean_T
  *rty_UsgHist_MOSFETTempVld);

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

extern MdlrefDW_UsgHis_Mdl_T UsgHis_Mdl_MdlrefDW;

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

#ifndef UsgHis_Mdl_MDLREF_HIDE_CHILD_

/* Block signals (default storage) */
extern B_UsgHis_Mdl_c_T UsgHis_Mdl_B;

/* Block states (default storage) */
extern DW_UsgHis_Mdl_f_T UsgHis_Mdl_DW;

/* Previous zero-crossings (trigger) states */
extern ZCE_UsgHis_Mdl_T UsgHis_Mdl_PrevZCX;

#endif                                 /*UsgHis_Mdl_MDLREF_HIDE_CHILD_*/

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<Root>/Constant3' : Unused code path elimination
 * Block '<S8>/Compare' : Unused code path elimination
 * Block '<S8>/Constant' : Unused code path elimination
 * Block '<S9>/Compare' : Unused code path elimination
 * Block '<S9>/Constant' : Unused code path elimination
 * Block '<S3>/Constant4' : Unused code path elimination
 * Block '<S11>/Delay Input1' : Unused code path elimination
 * Block '<S11>/FixPt Relational Operator' : Unused code path elimination
 * Block '<S13>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/FixPt Data Type Propagation' : Unused code path elimination
 * Block '<S3>/LogicalOperator' : Unused code path elimination
 * Block '<S3>/LogicalOperator1' : Unused code path elimination
 * Block '<S3>/RelationalOperator' : Unused code path elimination
 * Block '<S3>/Switch' : Unused code path elimination
 * Block '<S192>/Delay Input1' : Unused code path elimination
 * Block '<S192>/FixPt Relational Operator' : Unused code path elimination
 * Block '<S7>/Logical Operator3' : Unused code path elimination
 * Block '<S13>/FixPt Gateway In' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion2' : Eliminate redundant data type conversion
 * Block '<S195>/Logical Operator1' : Eliminated due to no operation
 * Block '<S195>/Logical Operator3' : Eliminated due to no operation
 * Block '<S196>/Data Type Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'UsgHis_Mdl'
 * '<S1>'   : 'UsgHis_Mdl/Model Info'
 * '<S2>'   : 'UsgHis_Mdl/UsageHistory'
 * '<S3>'   : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions'
 * '<S4>'   : 'UsgHis_Mdl/UsageHistory/DocBlock'
 * '<S5>'   : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals'
 * '<S6>'   : 'UsgHis_Mdl/UsageHistory/TravelCycleCounter'
 * '<S7>'   : 'UsgHis_Mdl/UsageHistory/UpdateNVM'
 * '<S8>'   : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/CheckIfBelowThreshold'
 * '<S9>'   : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/CheckIfEquealorAboveExitThreshold'
 * '<S10>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/Detect Change'
 * '<S11>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/DetectLeavingManufacturing'
 * '<S12>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/DocBlock'
 * '<S13>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/Increment Stored Integer'
 * '<S14>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/WriteToNvm4'
 * '<S15>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/WriteToNvm4/DocBlock'
 * '<S16>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/WriteToNvm4/NVMWrite'
 * '<S17>'  : 'UsgHis_Mdl/UsageHistory/CheckManufacturingConditions/WriteToNvm4/NVMWrite/DocBlock'
 * '<S18>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event'
 * '<S19>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters'
 * '<S20>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/DocBlock'
 * '<S21>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event'
 * '<S22>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers'
 * '<S23>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog'
 * '<S24>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter'
 * '<S25>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection'
 * '<S26>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition'
 * '<S27>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres'
 * '<S28>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/Compare To Constant'
 * '<S29>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/CompareToConstant3'
 * '<S30>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/Detect Increase'
 * '<S31>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/DocBlock'
 * '<S32>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/ReversalDirection'
 * '<S33>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/ReversalDirection/DocBlock'
 * '<S34>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/ReversalDirection/WriteREVERSALEVENT'
 * '<S35>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/ReversalDirection/WriteREVERSALEVENT/DocBlock'
 * '<S36>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/ReversalDirection/WriteREVERSALEVENT/NVMWrite'
 * '<S37>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalDirection/ReversalDirection/WriteREVERSALEVENT/NVMWrite/DocBlock'
 * '<S38>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/Compare To Constant'
 * '<S39>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/CompareToConstant3'
 * '<S40>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/Detect Increase'
 * '<S41>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/DocBlock'
 * '<S42>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/ReversalPosition'
 * '<S43>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/ReversalPosition/DocBlock'
 * '<S44>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/ReversalPosition/WriteREVERSALEVENT'
 * '<S45>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/ReversalPosition/WriteREVERSALEVENT/DocBlock'
 * '<S46>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/ReversalPosition/WriteREVERSALEVENT/NVMWrite'
 * '<S47>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalPosition/ReversalPosition/WriteREVERSALEVENT/NVMWrite/DocBlock'
 * '<S48>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/Compare To Constant'
 * '<S49>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/CompareToConstant3'
 * '<S50>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/Detect Increase'
 * '<S51>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/DocBlock'
 * '<S52>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/ReversalThreshold'
 * '<S53>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/ReversalThreshold/DocBlock'
 * '<S54>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/ReversalThreshold/WriteREVERSALEVENT'
 * '<S55>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/ReversalThreshold/WriteREVERSALEVENT/DocBlock'
 * '<S56>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/ReversalThreshold/WriteREVERSALEVENT/NVMWrite'
 * '<S57>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/ATS_Reversal_Event/ReversalThres/ReversalThreshold/WriteREVERSALEVENT/NVMWrite/DocBlock'
 * '<S58>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter'
 * '<S59>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter'
 * '<S60>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/CycRenormCounter'
 * '<S61>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/DocBlock'
 * '<S62>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/CycRenormCounter/Detect Change'
 * '<S63>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/CycRenormCounter/DocBlock'
 * '<S64>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/CycRenormCounter/WriteCycRenormCounterVld'
 * '<S65>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/CycRenormCounter/WriteCycRenormCounterVld/DocBlock'
 * '<S66>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/CycRenormCounter/WriteCycRenormCounterVld/NVMWrite'
 * '<S67>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/CycRenormCounter/CycRenormCounter/WriteCycRenormCounterVld/NVMWrite/DocBlock'
 * '<S68>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/CompareToConstant3'
 * '<S69>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/Detect Increase'
 * '<S70>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/DocBlock'
 * '<S71>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/SlideCycCounter'
 * '<S72>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/SlideCycCounter/DocBlock'
 * '<S73>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/SlideCycCounter/WriteSlideCycCounterVld'
 * '<S74>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/SlideCycCounter/WriteSlideCycCounterVld/DocBlock'
 * '<S75>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/SlideCycCounter/WriteSlideCycCounterVld/NVMWrite'
 * '<S76>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Cycle_counters/SlideCycCounter/SlideCycCounter/WriteSlideCycCounterVld/NVMWrite/DocBlock'
 * '<S77>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter'
 * '<S78>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter'
 * '<S79>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter'
 * '<S80>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/CompareToConstant2'
 * '<S81>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/DocBlock'
 * '<S82>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/LeanrFailCounter'
 * '<S83>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/LeanrFailCounter/Compare To Constant'
 * '<S84>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/LeanrFailCounter/DocBlock'
 * '<S85>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/LeanrFailCounter/WriteLeanrFailCounterVld'
 * '<S86>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/LeanrFailCounter/WriteLeanrFailCounterVld/DocBlock'
 * '<S87>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/LeanrFailCounter/WriteLeanrFailCounterVld/NVMWrite'
 * '<S88>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnFailCounter/LeanrFailCounter/WriteLeanrFailCounterVld/NVMWrite/DocBlock'
 * '<S89>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/CompareToConstant1'
 * '<S90>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/DocBlock'
 * '<S91>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/LeanrSuccessCounter'
 * '<S92>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/LeanrSuccessCounter/DocBlock'
 * '<S93>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/LeanrSuccessCounter/WriteLearnSuccessCounterVld'
 * '<S94>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/LeanrSuccessCounter/WriteLearnSuccessCounterVld/DocBlock'
 * '<S95>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/LeanrSuccessCounter/WriteLearnSuccessCounterVld/NVMWrite'
 * '<S96>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/LearnSuccessCounter/LeanrSuccessCounter/WriteLearnSuccessCounterVld/NVMWrite/DocBlock'
 * '<S97>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/Compare To Constant'
 * '<S98>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/Compare To Constant1'
 * '<S99>'  : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/Compare To Constant2'
 * '<S100>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/Compare To Constant3'
 * '<S101>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/Compare To Constant4'
 * '<S102>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/Compare To Constant5'
 * '<S103>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/CompareToConstant2'
 * '<S104>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/Detect Increase'
 * '<S105>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/DocBlock'
 * '<S106>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/ThermalProtCounter'
 * '<S107>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/ThermalProtCounter/DocBlock'
 * '<S108>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/ThermalProtCounter/WriteThermalProtCounterVld'
 * '<S109>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/ThermalProtCounter/WriteThermalProtCounterVld/DocBlock'
 * '<S110>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/ThermalProtCounter/WriteThermalProtCounterVld/NVMWrite'
 * '<S111>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/Extend_Event/ThermalProtCounter/ThermalProtCounter/WriteThermalProtCounterVld/NVMWrite/DocBlock'
 * '<S112>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime'
 * '<S113>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReprogrammingCount'
 * '<S114>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter'
 * '<S115>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/CompareToConstant4'
 * '<S116>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/DocBlock'
 * '<S117>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime'
 * '<S118>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/CompareToConstant1'
 * '<S119>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/Detect Increase'
 * '<S120>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/DocBlock'
 * '<S121>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/MotorOperatingTime_sec'
 * '<S122>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/MotorOperatingTime_sec/WriteMotorOpVld'
 * '<S123>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/MotorOperatingTime_sec/WriteMotorOpVld/DocBlock'
 * '<S124>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/MotorOperatingTime_sec/WriteMotorOpVld/NVMWrite'
 * '<S125>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/MotorOperTime/MotorOperTime/MotorOperatingTime_sec/WriteMotorOpVld/NVMWrite/DocBlock'
 * '<S126>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReprogrammingCount/DocBlock'
 * '<S127>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/CompareToConstant1'
 * '<S128>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/Detect Change'
 * '<S129>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/DocBlock'
 * '<S130>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/ReversalCounter'
 * '<S131>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/ReversalCounter/Compare To Constant'
 * '<S132>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/ReversalCounter/DocBlock'
 * '<S133>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/ReversalCounter/WriteRevCounterVld'
 * '<S134>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/ReversalCounter/WriteRevCounterVld/DocBlock'
 * '<S135>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/ReversalCounter/WriteRevCounterVld/NVMWrite'
 * '<S136>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/History_Loggers/ReversalCounter/ReversalCounter/WriteRevCounterVld/NVMWrite/DocBlock'
 * '<S137>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet'
 * '<S138>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet'
 * '<S139>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet'
 * '<S140>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet'
 * '<S141>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/CompareToConstant1'
 * '<S142>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/DocBlock'
 * '<S143>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/WriteAmbMaxTemp'
 * '<S144>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/WriteAmbMaxTemp/DocBlock'
 * '<S145>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/WriteAmbMaxTemp/WriteAmbMaxTempVld'
 * '<S146>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/WriteAmbMaxTemp/WriteAmbMaxTempVld/DocBlock'
 * '<S147>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/WriteAmbMaxTemp/WriteAmbMaxTempVld/NVMWrite'
 * '<S148>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMaxTempDet/WriteAmbMaxTemp/WriteAmbMaxTempVld/NVMWrite/DocBlock'
 * '<S149>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/CompareToConstant1'
 * '<S150>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/DocBlock'
 * '<S151>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/WriteAmbMinTemp'
 * '<S152>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/WriteAmbMinTemp/DocBlock'
 * '<S153>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/WriteAmbMinTemp/WriteAmbMinTempVld'
 * '<S154>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/WriteAmbMinTemp/WriteAmbMinTempVld/DocBlock'
 * '<S155>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/WriteAmbMinTemp/WriteAmbMinTempVld/NVMWrite'
 * '<S156>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/AmbMinTempDet/WriteAmbMinTemp/WriteAmbMinTempVld/NVMWrite/DocBlock'
 * '<S157>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/Compare To Constant'
 * '<S158>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/CompareToConstant1'
 * '<S159>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/DocBlock'
 * '<S160>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/WriteBattVtg'
 * '<S161>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/WriteBattVtg/DocBlock'
 * '<S162>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/WriteBattVtg/WriteBattVtgVld'
 * '<S163>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/WriteBattVtg/WriteBattVtgVld/DocBlock'
 * '<S164>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/WriteBattVtg/WriteBattVtgVld/NVMWrite'
 * '<S165>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMaxDet/WriteBattVtg/WriteBattVtgVld/NVMWrite/DocBlock'
 * '<S166>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/BattVtgMinDet'
 * '<S167>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/Compare To Constant'
 * '<S168>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/CompareToConstant1'
 * '<S169>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/DocBlock'
 * '<S170>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/BattVtgMinDet/BattVtgMinDetVld'
 * '<S171>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/BattVtgMinDet/DocBlock'
 * '<S172>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/BattVtgMinDet/BattVtgMinDetVld/DocBlock'
 * '<S173>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/BattVtgMinDet/BattVtgMinDetVld/NVMWrite'
 * '<S174>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/MinMax_HistoryLog/BattVtgMinDet/BattVtgMinDet/BattVtgMinDetVld/NVMWrite/DocBlock'
 * '<S175>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/CompareToConstant2'
 * '<S176>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/CompareToConstant3'
 * '<S177>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/Detect Change'
 * '<S178>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/DocBlock'
 * '<S179>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/StallCounter'
 * '<S180>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/StallCounter/DocBlock'
 * '<S181>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/StallCounter/WriteStallCounterVld'
 * '<S182>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/StallCounter/WriteStallCounterVld/DocBlock'
 * '<S183>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/StallCounter/WriteStallCounterVld/NVMWrite'
 * '<S184>' : 'UsgHis_Mdl/UsageHistory/ProduceDIDSignals/StallCounter/StallCounter/WriteStallCounterVld/NVMWrite/DocBlock'
 * '<S185>' : 'UsgHis_Mdl/UsageHistory/TravelCycleCounter/CycleCounterRollo'
 * '<S186>' : 'UsgHis_Mdl/UsageHistory/TravelCycleCounter/DocBlock'
 * '<S187>' : 'UsgHis_Mdl/UsageHistory/TravelCycleCounter/CycleCounterRollo/DocBlock'
 * '<S188>' : 'UsgHis_Mdl/UsageHistory/TravelCycleCounter/CycleCounterRollo/Lib_CycleCounter'
 * '<S189>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/CompareToConstant2'
 * '<S190>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/CompareToConstant3'
 * '<S191>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/Detect Decrease'
 * '<S192>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/DetectIncrease'
 * '<S193>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/DocBlock'
 * '<S194>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/InvalidateUsagHistVariables'
 * '<S195>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UnlearnReason'
 * '<S196>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UpdateResetReason'
 * '<S197>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UpdateUsagHistVariables'
 * '<S198>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/WriteToNvm6'
 * '<S199>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/InvalidateUsagHistVariables/DocBlock'
 * '<S200>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UnlearnReason/Compare To Constant'
 * '<S201>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UnlearnReason/Detect Change'
 * '<S202>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UnlearnReason/DocBlock'
 * '<S203>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UpdateResetReason/DocBlock'
 * '<S204>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UpdateUsagHistVariables/DocBlock'
 * '<S205>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UpdateUsagHistVariables/MergeToOneByte'
 * '<S206>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/UpdateUsagHistVariables/MergeToOneByte/DocBlock'
 * '<S207>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/WriteToNvm6/DocBlock'
 * '<S208>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/WriteToNvm6/NVMWrite'
 * '<S209>' : 'UsgHis_Mdl/UsageHistory/UpdateNVM/WriteToNvm6/NVMWrite/DocBlock'
 */
#endif                                 /* RTW_HEADER_UsgHis_Mdl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
