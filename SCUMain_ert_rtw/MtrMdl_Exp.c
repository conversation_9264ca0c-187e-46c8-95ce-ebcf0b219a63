/*
 * File: MtrMdl_Exp.c
 *
 * Code generated for Simulink model 'SCUMain'.
 *
 * Model version                  : 1.387
 * Simulink Coder version         : 9.8 (R2022b) 13-May-2022
 * C/C++ source code generated on : Wed Jun  4 11:44:35 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: NXP->Cortex-M0/M0+
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "MtrMdl_Exp.h"
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "SCUMain_types.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
int16_T MtrMdl_TEstCaseTemp;  /* Estimated Case Temperature (Glass and Rollo) */
int16_T MtrMdl_TEstMtrTemp;  /* Estimated Motor Temperature (Glass and Rollo) */
boolean_T MtrMdl_isEstCaseTempVld;
                        /* Estimated Case Temperature Validity (Glass and Rollo)
                           0: Invalid
                           1: Valid
                         */
boolean_T MtrMdl_isEstMtrTempVld;
                       /* Estimated Motor Temperature Validity (Glass and Rollo)
                          0: Invalid
                          1: Valid
                        */
MtrMdl_CalcForTorqBus_t MtrMdl_stCalcForTorq;/* Calculated Force and Torque (Rollo)
                                                Initial value: [struct('FcalcForce',uint16(65535),'McalcTorque',uint16(65535)) struct('FcalcForce',uint16(65535),'McalcTorque',uint16(65535))]
                                              */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
